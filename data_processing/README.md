# Jupyter Notebook 中的数据处理
在 `program_txt_to_database.ipynb` 中进行原始数据处理，包括：
- 从JSON格式txt文件提取原始数据
- 缺失字段补充（如学费信息提取）
- 学校名称规范化和QS排名匹配
- 地区名称英文转中文
- 新增自定义列（申请学位类型、绩点要求、年开销预估值、留服认证）
- 数据清理和质量检查

在 `case_txt_to_database.ipynb` 中进行原始数据处理，包括：


# CSV到PostgreSQL通用导入工具

这是一个通用的CSV数据导入PostgreSQL数据库工具，支持多种表类型，并且可以轻松扩展以支持新的数据库表。

## 功能特性

- 🔄 **通用设计**: 一个脚本处理多种表类型
- 🎯 **精确操作**: 只删除和重建指定的表，不影响其他表
- 📊 **智能映射**: 支持CSV列名到数据库字段的自动映射
- 🚀 **批量处理**: 分批导入大量数据，性能优异
- 🔧 **易于扩展**: 简单配置即可支持新的表类型
- 💾 **向量支持**: 内置embedding字段支持向量嵌入

## 安装依赖

```bash
pip install pandas sqlalchemy sqlalchemy-utils psycopg2-binary
```

## 使用方法

### 1. 查看支持的表类型

```bash
python csv_to_postgres.py --list-tables
```

### 2. 导入专业数据

```bash
# 使用默认CSV文件
python csv_to_postgres.py --table programs --db-password="your_password"

# 指定CSV文件
python csv_to_postgres.py --table programs --csv "专业数据库.csv" --db-password="your_password"
```

### 3. 导入案例数据

```bash
# 使用默认CSV文件
python csv_to_postgres.py --table cases --db-password="your_password"

# 指定CSV文件
python csv_to_postgres.py --table cases --csv "案例数据库.csv" --db-password="your_password"
```

### 4. 完整参数示例

```bash
python csv_to_postgres.py \
  --table programs \
  --csv "专业数据库.csv" \
  --db-user postgres \
  --db-password "123456789" \
  --db-host localhost \
  --db-port 5432 \
  --db-name tunshuedu_ai_selection_db
```

## 当前支持的表类型

| 表类型 | 描述 | 默认CSV文件 | 数据库表名 |
|--------|------|-------------|------------|
| `programs` | 专业数据表 | `专业数据库.csv` | `ai_selection_programs` |
| `cases` | 案例数据表 | `案例数据库.csv` | `ai_selection_cases` |

## 扩展新表类型

要添加新的表类型，需要进行以下步骤：

### 1. 定义数据库模型

在 `csv_to_postgres.py` 中添加新的SQLAlchemy模型类：

```python
class NewTableModel(Base):
    """新表模型类"""
    __tablename__ = "new_table_name"
    
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    field1 = Column(String(100), nullable=True, comment="字段1")
    field2 = Column(Text, nullable=True, comment="字段2")
    # ... 其他字段
    embedding = Column(JSONB, nullable=True, comment="向量嵌入")  # 如需要
```

### 2. 添加表配置

在 `TABLE_CONFIGS` 字典中添加新的配置：

```python
TABLE_CONFIGS = {
    # ... 现有配置
    'new_table': {
        'model': NewTableModel,
        'default_csv': 'new_data.csv',
        'column_mapping': {
            'CSV列名1': 'field1',
            'CSV列名2': 'field2',
            # ... 更多映射
        },
        'dtype_mapping': {
            # 如果需要特殊数据类型处理
            'special_field': 'Int64'
        },
        'description': '新表描述'
    }
}
```

### 3. 使用新表类型

```bash
python csv_to_postgres.py --table new_table --csv "new_data.csv" --db-password="your_password"
```

## 配置说明

### `TABLE_CONFIGS` 结构

每个表类型的配置包含以下字段：

- **`model`**: SQLAlchemy模型类
- **`default_csv`**: 默认CSV文件名
- **`column_mapping`**: CSV列名到数据库字段的映射字典
- **`dtype_mapping`**: pandas读取CSV时的数据类型映射
- **`description`**: 表的描述信息

### 列名映射规则

- 如果CSV文件的列名与数据库字段名完全一致，`column_mapping` 可以设为空字典 `{}`
- 如果列名不同，需要提供完整的映射关系
- 映射格式：`'CSV中的列名': '数据库字段名'`

### 数据类型映射

对于包含空值的整数字段，建议使用 `'Int64'` 类型：

```python
'dtype_mapping': {
    'id_field': 'Int64',  # 允许NaN值的整数字段
}
```

## 技术特性

### 安全特性

- **精确删除**: 只删除指定的表，不影响数据库中的其他表
- **事务处理**: 导入过程中如果出错会自动回滚
- **检查存在**: 删除表前会检查表是否存在

### 性能优化

- **批量插入**: 使用SQLAlchemy的 `bulk_insert_mappings` 进行批量插入
- **分批处理**: 每次处理1000条记录，避免内存占用过大
- **连接池**: 复用数据库连接，提高效率

### 错误处理

- **文件检查**: 导入前检查CSV文件是否存在
- **参数验证**: 验证表类型和必需参数
- **详细日志**: 提供详细的进度和错误信息

## 常见问题

### Q: 如何处理CSV文件编码问题？

A: 确保CSV文件使用UTF-8编码。如果遇到编码问题，可以先用文本编辑器转换编码。

### Q: 如果表结构变更了怎么办？

A: 脚本会自动删除旧表并重新创建，因此可以安全地处理表结构变更。

### Q: 可以增量导入数据吗？

A: 当前版本是完全替换模式。如需增量导入，需要修改 `create_table` 函数，不删除现有表。

### Q: 如何备份现有数据？

A: 在运行脚本前，建议使用 `pg_dump` 备份数据库：

```bash
pg_dump -h localhost -U postgres -d tunshuedu_ai_selection_db > backup.sql
```

## 版本历史

- **v1.0**: 初始版本，支持专业和案例数据导入
- **v1.1**: 添加embedding字段支持
- **v1.2**: 重构为通用工具，支持易扩展架构

## 贡献指南

1. 确保新的表类型配置正确
2. 添加必要的测试数据
3. 更新本README文档
4. 提交前测试导入功能 