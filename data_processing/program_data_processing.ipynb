{"cells": [{"cell_type": "code", "execution_count": 1, "id": "84c9c4a1", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.metrics.pairwise import cosine_similarity"]}, {"cell_type": "markdown", "id": "f0e745f4", "metadata": {}, "source": ["### 1 数据读取与基本检查"]}, {"cell_type": "markdown", "id": "0f5f5d03", "metadata": {}, "source": ["#### 1.1 数据读取"]}, {"cell_type": "code", "execution_count": 26, "id": "3ce7a718", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原数据总数：11012条\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>专业中文名</th>\n", "      <th>专业英文名</th>\n", "      <th>专业大类</th>\n", "      <th>专业方向</th>\n", "      <th>所在学院</th>\n", "      <th>入学时间</th>\n", "      <th>项目时长</th>\n", "      <th>项目官网</th>\n", "      <th>...</th>\n", "      <th>课程设置</th>\n", "      <th>专业代码</th>\n", "      <th>项目学费</th>\n", "      <th>学校英文名(QS26)</th>\n", "      <th>学校排名(QS26)</th>\n", "      <th>学校所在地区</th>\n", "      <th>申请学位类型</th>\n", "      <th>绩点要求</th>\n", "      <th>年开销预估值</th>\n", "      <th>留服认证</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10052</th>\n", "      <td>新加坡科技设计大学</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>科技与设计（可持续产品设计）理学硕士</td>\n", "      <td>MSc Technology and Design (Sustainable Product...</td>\n", "      <td>社科</td>\n", "      <td>其他社科</td>\n", "      <td>NaN</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.sutd.edu.sg/programme-listing/mtd-...</td>\n", "      <td>...</td>\n", "      <td>设计创新InnovationbyDesign数字化制造DigitalManufacturin...</td>\n", "      <td>74891</td>\n", "      <td>54500新币/年</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>519</td>\n", "      <td>新加坡</td>\n", "      <td>硕士</td>\n", "      <td>60.0</td>\n", "      <td>15-25万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1166</th>\n", "      <td>南安普顿大学</td>\n", "      <td>University of Southampton</td>\n", "      <td>化学理学硕士</td>\n", "      <td>MSc Chemistry</td>\n", "      <td>理科</td>\n", "      <td>化学</td>\n", "      <td>化学学院</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.southampton.ac.uk/courses/chemistr...</td>\n", "      <td>...</td>\n", "      <td>化学硕士论文科学写作与表达技巧Scientificwritingandpresentatio...</td>\n", "      <td>51204</td>\n", "      <td>31500英镑/年</td>\n", "      <td>University of Southampton</td>\n", "      <td>87</td>\n", "      <td>英国</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>20-30万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6612</th>\n", "      <td>约克大学（英国）</td>\n", "      <td>University of York</td>\n", "      <td>可持续商业：领导、创新与管理理学硕士</td>\n", "      <td>MSc Sustainable Business: Leadership, Innovati...</td>\n", "      <td>商科</td>\n", "      <td>创业与创新</td>\n", "      <td>环境与地理学院</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.york.ac.uk/study/postgraduate-taug...</td>\n", "      <td>...</td>\n", "      <td>全部(12)核心课程(4)选修课程(8)可持续商业概论IntroductiontoSusta...</td>\n", "      <td>67046</td>\n", "      <td>25900英镑/年</td>\n", "      <td>University of York</td>\n", "      <td>169</td>\n", "      <td>英国</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>20-30万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1179</th>\n", "      <td>南安普顿大学</td>\n", "      <td>University of Southampton</td>\n", "      <td>声学与振动工程理学硕士</td>\n", "      <td>MSc Acoustical and Vibration Engineering</td>\n", "      <td>理科</td>\n", "      <td>物理</td>\n", "      <td>工程学院</td>\n", "      <td>9月</td>\n", "      <td>1年</td>\n", "      <td>https://www.southampton.ac.uk/courses/acoustic...</td>\n", "      <td>...</td>\n", "      <td>声学基础FundamentalsofAcoustics工程专业ProfessionalAsp...</td>\n", "      <td>51224</td>\n", "      <td>31500英镑/年</td>\n", "      <td>University of Southampton</td>\n", "      <td>87</td>\n", "      <td>英国</td>\n", "      <td>硕士</td>\n", "      <td>70.0</td>\n", "      <td>20-30万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9294</th>\n", "      <td>南卫理公会大学</td>\n", "      <td>Southern Methodist University</td>\n", "      <td>管理硕士</td>\n", "      <td>MS in Management</td>\n", "      <td>商科</td>\n", "      <td>管理</td>\n", "      <td>NaN</td>\n", "      <td>秋季</td>\n", "      <td>32学分</td>\n", "      <td>https://www.smu.edu/cox/Degrees-and-Programs/M...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>74015</td>\n", "      <td>42486美元/年</td>\n", "      <td>Southern Methodist University</td>\n", "      <td>1001-1200</td>\n", "      <td>美国</td>\n", "      <td>硕士</td>\n", "      <td>60.0</td>\n", "      <td>25-35万人民币</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["           学校中文名                                          学校英文名  \\\n", "10052  新加坡科技设计大学  Singapore University of Technology and Design   \n", "1166      南安普顿大学                      University of Southampton   \n", "6612    约克大学（英国）                             University of York   \n", "1179      南安普顿大学                      University of Southampton   \n", "9294     南卫理公会大学                  Southern Methodist University   \n", "\n", "                    专业中文名                                              专业英文名  \\\n", "10052  科技与设计（可持续产品设计）理学硕士  MSc Technology and Design (Sustainable Product...   \n", "1166               化学理学硕士                                      MSc Chemistry   \n", "6612   可持续商业：领导、创新与管理理学硕士  MSc Sustainable Business: Leadership, Innovati...   \n", "1179          声学与振动工程理学硕士           MSc Acoustical and Vibration Engineering   \n", "9294                 管理硕士                                   MS in Management   \n", "\n", "      专业大类   专业方向     所在学院 入学时间  项目时长  \\\n", "10052   社科   其他社科      NaN   9月    1年   \n", "1166    理科     化学     化学学院   9月    1年   \n", "6612    商科  创业与创新  环境与地理学院   9月    1年   \n", "1179    理科     物理     工程学院   9月    1年   \n", "9294    商科     管理      NaN   秋季  32学分   \n", "\n", "                                                    项目官网  ...  \\\n", "10052  https://www.sutd.edu.sg/programme-listing/mtd-...  ...   \n", "1166   https://www.southampton.ac.uk/courses/chemistr...  ...   \n", "6612   https://www.york.ac.uk/study/postgraduate-taug...  ...   \n", "1179   https://www.southampton.ac.uk/courses/acoustic...  ...   \n", "9294   https://www.smu.edu/cox/Degrees-and-Programs/M...  ...   \n", "\n", "                                                    课程设置   专业代码       项目学费  \\\n", "10052  设计创新InnovationbyDesign数字化制造DigitalManufacturin...  74891  54500新币/年   \n", "1166   化学硕士论文科学写作与表达技巧Scientificwritingandpresentatio...  51204  31500英镑/年   \n", "6612   全部(12)核心课程(4)选修课程(8)可持续商业概论IntroductiontoSusta...  67046  25900英镑/年   \n", "1179   声学基础FundamentalsofAcoustics工程专业ProfessionalAsp...  51224  31500英镑/年   \n", "9294                                                 NaN  74015  42486美元/年   \n", "\n", "                                         学校英文名(QS26) 学校排名(QS26)  学校所在地区  \\\n", "10052  Singapore University of Technology and Design        519     新加坡   \n", "1166                       University of Southampton         87      英国   \n", "6612                              University of York        169      英国   \n", "1179                       University of Southampton         87      英国   \n", "9294                   Southern Methodist University  1001-1200      美国   \n", "\n", "      申请学位类型  绩点要求     年开销预估值 留服认证  \n", "10052     硕士  60.0  15-25万人民币  NaN  \n", "1166      硕士  70.0  20-30万人民币  NaN  \n", "6612      硕士  70.0  20-30万人民币  NaN  \n", "1179      硕士  70.0  20-30万人民币  NaN  \n", "9294      硕士  60.0  25-35万人民币  NaN  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["old_program_df = pd.read_csv('项目数据/老项目数据库（noembedding）.csv')\n", "print(f'原数据总数：{len(old_program_df)}条')\n", "old_program_df.sample(n=5)"]}, {"cell_type": "code", "execution_count": 27, "id": "b00cdc2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["新数据总数：11447条\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>program_category</th>\n", "      <th>program_direction</th>\n", "      <th>faculty</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>...</th>\n", "      <th>interview_type</th>\n", "      <th>program_website</th>\n", "      <th>program_objectives</th>\n", "      <th>application_requirements</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>application_time</th>\n", "      <th>consultant_analysis</th>\n", "      <th>courses</th>\n", "      <th>interview_experience</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3838</th>\n", "      <td>50514</td>\n", "      <td>政治与传播理学硕士</td>\n", "      <td>MSc Politics and Communication</td>\n", "      <td>社科</td>\n", "      <td>媒体与传播</td>\n", "      <td>媒体与传播系</td>\n", "      <td>伦敦政治经济学院</td>\n", "      <td>The London School of Economics and Political S...</td>\n", "      <td>G5超级精英大学&lt;br&gt;金三角名校&lt;br&gt;</td>\n", "      <td>2026qs第56名&lt;br/&gt;2025times第1名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.lse.ac.uk/study-at-lse/graduate/ms...</td>\n", "      <td>伦敦政治经济学院政治与传播理学硕士项目旨在让学生深入了解政治和传播研究的交叉领域中的理论和应...</td>\n", "      <td>具有社会科学背景的2:1荣誉学位或同等学历，或具有其他领域的学位并且具备媒体与传播领域的专业...</td>\n", "      <td>NaN</td>\n", "      <td>雅思 | 总分要求: 7 | 小分要求: 听力: 6.5; 阅读: 7; 写作: 6.5; ...</td>\n", "      <td>25年秋季入学: 开放申请(2024-10-08) | 截止申请(-)&lt;br/&gt;</td>\n", "      <td>该项目旨在让学生对政治和传播研究的交叉领域的理论和应用知识有深入的了解。提供了追求媒体，政治...</td>\n", "      <td>课程描述：&lt;br/&gt;\\n学生将学习政治传播学、媒体与传播理论与概念、媒体与传播研究方法、民主...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6048</th>\n", "      <td>73250</td>\n", "      <td>能源政策与金融理学硕士</td>\n", "      <td>MSc Energy Policy and Finance</td>\n", "      <td>工科</td>\n", "      <td>能源</td>\n", "      <td>跨学科研究学院</td>\n", "      <td>圣安德鲁斯大学</td>\n", "      <td>University of St Andrews</td>\n", "      <td>苏格兰最古老的大学&lt;br&gt;</td>\n", "      <td>2026qs第113名&lt;br/&gt;2025times第2名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.st-andrews.ac.uk/subjects/interdis...</td>\n", "      <td>为我们所有人创造更美好能源未来的挑战既紧迫又复杂。圣安德鲁斯大学能源政策与金融理学硕士项目专...</td>\n", "      <td>具有2:1学位或同等学历，学科背景可包括但不限于：政治学、公共政策、社会学、地理学、管理学、...</td>\n", "      <td>该学校针对内地院校有专属的GPA分数要求，圣安德鲁斯大学会根据“C9院校“、“985院校”、...</td>\n", "      <td>雅思 | 总分要求: 7 | 小分要求: 听力: 6; 阅读: 6; 写作: 7; 口语: ...</td>\n", "      <td>25年秋季入学: 开放申请(2024-08-01) | 截止申请(2025-08-07)&lt;br/&gt;</td>\n", "      <td>NaN</td>\n", "      <td>课程描述：&lt;br/&gt;\\n&lt;br/&gt;\\n课程列表：&lt;br/&gt;\\n能源不平等与气候责任 | En...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3750</th>\n", "      <td>50466</td>\n", "      <td>社会人类学理学硕士</td>\n", "      <td>MSc Social Anthropology</td>\n", "      <td>社科</td>\n", "      <td>社会学与社工</td>\n", "      <td>人类学系</td>\n", "      <td>伦敦政治经济学院</td>\n", "      <td>The London School of Economics and Political S...</td>\n", "      <td>G5超级精英大学&lt;br&gt;金三角名校&lt;br&gt;</td>\n", "      <td>2026qs第56名&lt;br/&gt;2025times第1名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.lse.ac.uk/study-at-lse/graduate/ms...</td>\n", "      <td>伦敦政治经济学院社会人类学理学硕士项目提升学生的社会人类学基础知识，包括民族多样性和社会人类...</td>\n", "      <td>具有2:1荣誉学位或同等学历，不限专业背景，包括更广泛学位背景下的人类学（具备人类学的先前知...</td>\n", "      <td>NaN</td>\n", "      <td>雅思 | 总分要求: 7 | 小分要求: 听力: 6.5; 阅读: 7; 写作: 6.5; ...</td>\n", "      <td>25年秋季入学: 开放申请(2024-10-08) | 截止申请(-)&lt;br/&gt;</td>\n", "      <td>NaN</td>\n", "      <td>课程描述：&lt;br/&gt;\\n&lt;br/&gt;\\n课程列表：&lt;br/&gt;\\n人类学：理论与民族志 | An...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019</th>\n", "      <td>65879</td>\n", "      <td>可持续发展环境科学理学硕士</td>\n", "      <td>MSc Environmental Science for Sustainability</td>\n", "      <td>工科</td>\n", "      <td>环境工程</td>\n", "      <td>社会科学与公共政策学院</td>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>King's College London</td>\n", "      <td>罗素大学集团&lt;br&gt;金三角名校&lt;br&gt;</td>\n", "      <td>2026qs第31名&lt;br/&gt;2025times第24名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.kcl.ac.uk/study/postgraduate-taugh...</td>\n", "      <td>伦敦大学国王学院可持续发展环境科学理学硕士课程是一个以技能为重点的课程，提供高级水平的环境科...</td>\n", "      <td>具有2:1本科（荣誉）学士学位，优先考虑地理学、自然科学（如环境科学、物理学、化学和生物学）...</td>\n", "      <td>该学校针对内地院校有专属的GPA分数要求，伦敦大学国王学院会根据“211院校”、“双一流院校...</td>\n", "      <td>雅思 | 总分要求: 7 | 小分要求: 听力: 6.5; 阅读: 6.5; 写作: 6.5...</td>\n", "      <td>25年秋季入学: 开放申请(2024-10-16) | Round 1 截止(2025-03...</td>\n", "      <td>NaN</td>\n", "      <td>课程描述：&lt;br/&gt;\\n&lt;br/&gt;\\n课程列表：&lt;br/&gt;\\n论文 | Dissertati...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1317</th>\n", "      <td>71812</td>\n", "      <td>机械工程与应用力学工程理学硕士</td>\n", "      <td>M.S.E. in Mechanical Engineering and Applied M...</td>\n", "      <td>工科</td>\n", "      <td>机械工程</td>\n", "      <td>NaN</td>\n", "      <td>宾夕法尼亚大学</td>\n", "      <td>University of Pennsylvania</td>\n", "      <td>常春藤联盟&lt;br&gt;美国大学协会&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>2026qs第15名&lt;br/&gt;2025usnews第10名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://www.me.upenn.edu/masters/degrees-optio...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 100; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求: 7.5...</td>\n", "      <td>23年秋季入学: 开放申请(/) | 截止申请(2023-02-01)&lt;br/&gt;25年秋季入...</td>\n", "      <td>宾夕法尼亚大学机械工程与应用力学系开设了机械工程与应用力学硕士Master of Scien...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 23 columns</p>\n", "</div>"], "text/plain": ["      program_code  program_name_cn  \\\n", "3838         50514        政治与传播理学硕士   \n", "6048         73250      能源政策与金融理学硕士   \n", "3750         50466        社会人类学理学硕士   \n", "2019         65879    可持续发展环境科学理学硕士   \n", "1317         71812  机械工程与应用力学工程理学硕士   \n", "\n", "                                        program_name_en program_category  \\\n", "3838                     MSc Politics and Communication               社科   \n", "6048                      MSc Energy Policy and Finance               工科   \n", "3750                            MSc Social Anthropology               社科   \n", "2019       MSc Environmental Science for Sustainability               工科   \n", "1317  M.S.E. in Mechanical Engineering and Applied M...               工科   \n", "\n", "     program_direction      faculty school_name_cn  \\\n", "3838             媒体与传播       媒体与传播系       伦敦政治经济学院   \n", "6048                能源      跨学科研究学院        圣安德鲁斯大学   \n", "3750            社会学与社工         人类学系       伦敦政治经济学院   \n", "2019              环境工程  社会科学与公共政策学院       伦敦大学国王学院   \n", "1317              机械工程          NaN        宾夕法尼亚大学   \n", "\n", "                                         school_name_en  \\\n", "3838  The London School of Economics and Political S...   \n", "6048                           University of St Andrews   \n", "3750  The London School of Economics and Political S...   \n", "2019                              King's College London   \n", "1317                         University of Pennsylvania   \n", "\n", "                        school_labels                        school_ranks  \\\n", "3838            G5超级精英大学<br>金三角名校<br>    2026qs第56名<br/>2025times第1名<br/>   \n", "6048                    苏格兰最古老的大学<br>   2026qs第113名<br/>2025times第2名<br/>   \n", "3750            G5超级精英大学<br>金三角名校<br>    2026qs第56名<br/>2025times第1名<br/>   \n", "2019              罗素大学集团<br>金三角名校<br>   2026qs第31名<br/>2025times第24名<br/>   \n", "1317  常春藤联盟<br>美国大学协会<br>全球大学校长论坛<br>  2026qs第15名<br/>2025usnews第10名<br/>   \n", "\n", "      ... interview_type                                    program_website  \\\n", "3838  ...            Na<PERSON>  https://www.lse.ac.uk/study-at-lse/graduate/ms...   \n", "6048  ...            NaN  https://www.st-andrews.ac.uk/subjects/interdis...   \n", "3750  ...            NaN  https://www.lse.ac.uk/study-at-lse/graduate/ms...   \n", "2019  ...            NaN  https://www.kcl.ac.uk/study/postgraduate-taugh...   \n", "1317  ...            <PERSON><PERSON>  https://www.me.upenn.edu/masters/degrees-optio...   \n", "\n", "                                     program_objectives  \\\n", "3838  伦敦政治经济学院政治与传播理学硕士项目旨在让学生深入了解政治和传播研究的交叉领域中的理论和应...   \n", "6048  为我们所有人创造更美好能源未来的挑战既紧迫又复杂。圣安德鲁斯大学能源政策与金融理学硕士项目专...   \n", "3750  伦敦政治经济学院社会人类学理学硕士项目提升学生的社会人类学基础知识，包括民族多样性和社会人类...   \n", "2019  伦敦大学国王学院可持续发展环境科学理学硕士课程是一个以技能为重点的课程，提供高级水平的环境科...   \n", "1317                                                NaN   \n", "\n", "                               application_requirements  \\\n", "3838  具有社会科学背景的2:1荣誉学位或同等学历，或具有其他领域的学位并且具备媒体与传播领域的专业...   \n", "6048  具有2:1学位或同等学历，学科背景可包括但不限于：政治学、公共政策、社会学、地理学、管理学、...   \n", "3750  具有2:1荣誉学位或同等学历，不限专业背景，包括更广泛学位背景下的人类学（具备人类学的先前知...   \n", "2019  具有2:1本科（荣誉）学士学位，优先考虑地理学、自然科学（如环境科学、物理学、化学和生物学）...   \n", "1317                                                NaN   \n", "\n", "                                       gpa_requirements  \\\n", "3838                                                NaN   \n", "6048  该学校针对内地院校有专属的GPA分数要求，圣安德鲁斯大学会根据“C9院校“、“985院校”、...   \n", "3750                                                NaN   \n", "2019  该学校针对内地院校有专属的GPA分数要求，伦敦大学国王学院会根据“211院校”、“双一流院校...   \n", "1317                                                NaN   \n", "\n", "                                  language_requirements  \\\n", "3838  雅思 | 总分要求: 7 | 小分要求: 听力: 6.5; 阅读: 7; 写作: 6.5; ...   \n", "6048  雅思 | 总分要求: 7 | 小分要求: 听力: 6; 阅读: 6; 写作: 7; 口语: ...   \n", "3750  雅思 | 总分要求: 7 | 小分要求: 听力: 6.5; 阅读: 7; 写作: 6.5; ...   \n", "2019  雅思 | 总分要求: 7 | 小分要求: 听力: 6.5; 阅读: 6.5; 写作: 6.5...   \n", "1317  托福 | 总分要求: 100; 小分要求: 无要求; <br/>雅思 | 总分要求: 7.5...   \n", "\n", "                                       application_time  \\\n", "3838           25年秋季入学: 开放申请(2024-10-08) | 截止申请(-)<br/>   \n", "6048  25年秋季入学: 开放申请(2024-08-01) | 截止申请(2025-08-07)<br/>   \n", "3750           25年秋季入学: 开放申请(2024-10-08) | 截止申请(-)<br/>   \n", "2019  25年秋季入学: 开放申请(2024-10-16) | Round 1 截止(2025-03...   \n", "1317  23年秋季入学: 开放申请(/) | 截止申请(2023-02-01)<br/>25年秋季入...   \n", "\n", "                                    consultant_analysis  \\\n", "3838  该项目旨在让学生对政治和传播研究的交叉领域的理论和应用知识有深入的了解。提供了追求媒体，政治...   \n", "6048                                                NaN   \n", "3750                                                NaN   \n", "2019                                                NaN   \n", "1317  宾夕法尼亚大学机械工程与应用力学系开设了机械工程与应用力学硕士Master of Scien...   \n", "\n", "                                                courses interview_experience  \n", "3838  课程描述：<br/>\\n学生将学习政治传播学、媒体与传播理论与概念、媒体与传播研究方法、民主...                  NaN  \n", "6048  课程描述：<br/>\\n<br/>\\n课程列表：<br/>\\n能源不平等与气候责任 | En...                  NaN  \n", "3750  课程描述：<br/>\\n<br/>\\n课程列表：<br/>\\n人类学：理论与民族志 | An...                  NaN  \n", "2019  课程描述：<br/>\\n<br/>\\n课程列表：<br/>\\n论文 | Dissertati...                  NaN  \n", "1317                                                NaN                  NaN  \n", "\n", "[5 rows x 23 columns]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df = pd.read_csv('项目数据/项目数据库（原始爬取版）.csv')\n", "print(f'新数据总数：{len(program_df)}条')\n", "program_df.sample(n=5)"]}, {"cell_type": "markdown", "id": "27f6ce6f", "metadata": {}, "source": ["#### 1.2 空值检查"]}, {"cell_type": "code", "execution_count": 5, "id": "661d953b", "metadata": {}, "outputs": [{"data": {"text/plain": ["program_code                    0\n", "program_name_cn                 0\n", "program_name_en                 1\n", "program_category                0\n", "program_direction               0\n", "faculty                      2016\n", "school_name_cn                  0\n", "school_name_en                  0\n", "school_labels                  98\n", "school_ranks                  340\n", "enrollment_time                51\n", "program_duration               54\n", "program_tuition                71\n", "interview_type              10719\n", "program_website                12\n", "program_objectives           2141\n", "application_requirements      475\n", "gpa_requirements             5934\n", "language_requirements         149\n", "application_time              688\n", "consultant_analysis          8805\n", "courses                      2380\n", "interview_experience        11151\n", "dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 空值检查\n", "program_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 6, "id": "a6039efa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>program_category</th>\n", "      <th>program_direction</th>\n", "      <th>faculty</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>...</th>\n", "      <th>interview_type</th>\n", "      <th>program_website</th>\n", "      <th>program_objectives</th>\n", "      <th>application_requirements</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>application_time</th>\n", "      <th>consultant_analysis</th>\n", "      <th>courses</th>\n", "      <th>interview_experience</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1385</th>\n", "      <td>73599</td>\n", "      <td>应用物理理学硕士</td>\n", "      <td>NaN</td>\n", "      <td>理科</td>\n", "      <td>物理</td>\n", "      <td>NaN</td>\n", "      <td>康奈尔大学</td>\n", "      <td>Cornell University</td>\n", "      <td>常春藤联盟&lt;br&gt;美国大学协会&lt;br&gt;国际大学气候联盟&lt;br&gt;</td>\n", "      <td>2026qs第16名&lt;br/&gt;2025usnews第11名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://gradschool.cornell.edu/academics/field...</td>\n", "      <td>NaN</td>\n", "      <td>申请人应具有物理学或其他物理科学或工程领域的本科准备，focus是数学和现代物理学。</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 无要求 | 小分要求: 小分要求: L15,W20,R20,S22; ...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 23 columns</p>\n", "</div>"], "text/plain": ["      program_code program_name_cn program_name_en program_category  \\\n", "1385         73599        应用物理理学硕士             NaN               理科   \n", "\n", "     program_direction faculty school_name_cn      school_name_en  \\\n", "1385                物理     NaN          康奈尔大学  Cornell University   \n", "\n", "                        school_labels                        school_ranks  \\\n", "1385  常春藤联盟<br>美国大学协会<br>国际大学气候联盟<br>  2026qs第16名<br/>2025usnews第11名<br/>   \n", "\n", "      ... interview_type                                    program_website  \\\n", "1385  ...            NaN  https://gradschool.cornell.edu/academics/field...   \n", "\n", "     program_objectives                    application_requirements  \\\n", "1385                NaN  申请人应具有物理学或其他物理科学或工程领域的本科准备，focus是数学和现代物理学。   \n", "\n", "     gpa_requirements                              language_requirements  \\\n", "1385              NaN  托福 | 总分要求: 无要求 | 小分要求: 小分要求: L15,W20,R20,S22; ...   \n", "\n", "     application_time consultant_analysis courses interview_experience  \n", "1385              NaN                 NaN     NaN                  NaN  \n", "\n", "[1 rows x 23 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 对于某列只有极少数空值的情况进行检查和人工补充\n", "program_df[program_df['program_name_en'].isna()]"]}, {"cell_type": "code", "execution_count": 7, "id": "1ce1b252", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>program_category</th>\n", "      <th>program_direction</th>\n", "      <th>faculty</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>...</th>\n", "      <th>interview_type</th>\n", "      <th>program_website</th>\n", "      <th>program_objectives</th>\n", "      <th>application_requirements</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>application_time</th>\n", "      <th>consultant_analysis</th>\n", "      <th>courses</th>\n", "      <th>interview_experience</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "<p>0 rows × 23 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [program_code, program_name_cn, program_name_en, program_category, program_direction, faculty, school_name_cn, school_name_en, school_labels, school_ranks, enrollment_time, program_duration, program_tuition, interview_type, program_website, program_objectives, application_requirements, gpa_requirements, language_requirements, application_time, consultant_analysis, courses, interview_experience]\n", "Index: []\n", "\n", "[0 rows x 23 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df.loc[1385, 'program_name_en'] = 'MS in Applied Physics'\n", "program_df[program_df['program_name_en'].isna()]"]}, {"cell_type": "markdown", "id": "98411743", "metadata": {}, "source": ["#### 1.3 重复值检查"]}, {"cell_type": "code", "execution_count": 8, "id": "e30355cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n"]}], "source": ["# 重复值检查\n", "print(program_df.duplicated().sum())\n"]}, {"cell_type": "code", "execution_count": 9, "id": "b867c7b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["共有44组学校中文名和项目中文名都相同的项目，注意用项目英文名区分\n"]}], "source": ["# 学校中文名和项目中文名重复的项..\n", "\n", "duplicated_rows = len(program_df[program_df.duplicated(subset=['school_name_cn', 'program_name_cn'], keep=False)]) - len(program_df[program_df.duplicated(subset=['school_name_cn', 'program_name_cn'])])\n", "print(f'共有{duplicated_rows}组学校中文名和项目中文名都相同的项目，注意用项目英文名区分') \n", "# program_df[program_df.duplicated(subset=['school_name_cn', 'program_name_cn'], keep=False)]  # 共111条"]}, {"cell_type": "markdown", "id": "ab7688a9", "metadata": {}, "source": ["> 检查后发现部分是因为爬取时中文名称没爬全，比如nus “计算机工程理学硕士 - 数字化与通信技术方向” 只爬取了 “计算机工程理学硕士”...; 明尼苏达大学 “数据科学理学硕士 - 化学工程与材料科学” 只爬取了 “数据科学理学硕士”等等。后续在数据库中更改吧，通过写接口直接允许管理员从系统界面修改。"]}, {"cell_type": "code", "execution_count": 10, "id": "27d8ca32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>program_category</th>\n", "      <th>program_direction</th>\n", "      <th>faculty</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>...</th>\n", "      <th>interview_type</th>\n", "      <th>program_website</th>\n", "      <th>program_objectives</th>\n", "      <th>application_requirements</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>application_time</th>\n", "      <th>consultant_analysis</th>\n", "      <th>courses</th>\n", "      <th>interview_experience</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4774</th>\n", "      <td>71764</td>\n", "      <td>生物工程理学硕士</td>\n", "      <td>MS in Bioengineering</td>\n", "      <td>工科</td>\n", "      <td>生物工程</td>\n", "      <td>NaN</td>\n", "      <td>华盛顿大学</td>\n", "      <td>University of Washington</td>\n", "      <td>公立常春藤&lt;br&gt;帕克十二联盟&lt;br&gt;环太平洋大学联盟&lt;br&gt;国际大学气候联盟&lt;br&gt;</td>\n", "      <td>2026qs第81名&lt;br/&gt;2025usnews第46名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://bioe.uw.edu/academic-programs/masters/ms/</td>\n", "      <td>NaN</td>\n", "      <td>建议有以下先修课：代数、线性代数、三角学、常微分方程、信号分析、概率论与统计学、编程、电气工...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 92; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求: 7; 小...</td>\n", "      <td>23年秋季入学: 开放申请(/) | 截止申请(2023-12-01)&lt;br/&gt;</td>\n", "      <td>华盛顿大学生物工程系研究生阶段设有以下学位项目，分别是：生物工程理学硕士Master of ...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4775</th>\n", "      <td>73895</td>\n", "      <td>生物工程理学硕士</td>\n", "      <td>MS in Bioengineering</td>\n", "      <td>工科</td>\n", "      <td>生物工程</td>\n", "      <td>NaN</td>\n", "      <td>华盛顿大学</td>\n", "      <td>University of Washington</td>\n", "      <td>公立常春藤&lt;br&gt;帕克十二联盟&lt;br&gt;环太平洋大学联盟&lt;br&gt;国际大学气候联盟&lt;br&gt;</td>\n", "      <td>2026qs第81名&lt;br/&gt;2025usnews第46名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://bioe.uw.edu/academic-programs/masters/ms/</td>\n", "      <td>NaN</td>\n", "      <td>GPA3.0；具有强大的研究技能（有至少一年干湿实验室经验者优先）；生物工程、化学工程、化学...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 80; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求: 6.5;...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11341</th>\n", "      <td>72069</td>\n", "      <td>统计学理学硕士</td>\n", "      <td>MS in Statistics</td>\n", "      <td>理科</td>\n", "      <td>数学</td>\n", "      <td>NaN</td>\n", "      <td>康涅狄格大学</td>\n", "      <td>University of Connecticut</td>\n", "      <td>公立常春藤&lt;br&gt;美国竞技联盟&lt;br&gt;Universitas 21&lt;br&gt;</td>\n", "      <td>2025usnews第70名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://statistics.uconn.edu/graduate/masters-...</td>\n", "      <td>NaN</td>\n", "      <td>1.先修课：微积分（3学期,包括1学期多元微积分）,线性代数（1学期）,统计（2学期）\\n2...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 79; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求: 6.5;...</td>\n", "      <td>25年秋季入学: 开放申请(/) | 截止申请(2025-04-01)&lt;br/&gt;</td>\n", "      <td>康涅狄格大学统计学系开设了统计学MS项目，硕士课程强调应用统计学，要求学生在具体的应用领域至...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11342</th>\n", "      <td>73025</td>\n", "      <td>统计学理学硕士</td>\n", "      <td>MS in Statistics</td>\n", "      <td>理科</td>\n", "      <td>数学</td>\n", "      <td>NaN</td>\n", "      <td>康涅狄格大学</td>\n", "      <td>University of Connecticut</td>\n", "      <td>公立常春藤&lt;br&gt;美国竞技联盟&lt;br&gt;Universitas 21&lt;br&gt;</td>\n", "      <td>2025usnews第70名&lt;br/&gt;</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>https://statistics.uconn.edu/graduate/masters-...</td>\n", "      <td>NaN</td>\n", "      <td>整个本科累计平均绩点为3.0;本科最后两年的平均绩点至少为3.0;或在整个本科最后一年表现优...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: 79 | 小分要求: 小分要求: 22; &lt;br/&gt;雅思 | 总分要求...</td>\n", "      <td>25年秋季入学: 开放申请(/) | 截止申请(2025-04-01)&lt;br/&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4 rows × 23 columns</p>\n", "</div>"], "text/plain": ["       program_code program_name_cn       program_name_en program_category  \\\n", "4774          71764        生物工程理学硕士  MS in Bioengineering               工科   \n", "4775          73895        生物工程理学硕士  MS in Bioengineering               工科   \n", "11341         72069         统计学理学硕士      MS in Statistics               理科   \n", "11342         73025         统计学理学硕士      MS in Statistics               理科   \n", "\n", "      program_direction faculty school_name_cn             school_name_en  \\\n", "4774               生物工程     NaN          华盛顿大学   University of Washington   \n", "4775               生物工程     NaN          华盛顿大学   University of Washington   \n", "11341                数学     NaN         康涅狄格大学  University of Connecticut   \n", "11342                数学     NaN         康涅狄格大学  University of Connecticut   \n", "\n", "                                     school_labels  \\\n", "4774   公立常春藤<br>帕克十二联盟<br>环太平洋大学联盟<br>国际大学气候联盟<br>   \n", "4775   公立常春藤<br>帕克十二联盟<br>环太平洋大学联盟<br>国际大学气候联盟<br>   \n", "11341        公立常春藤<br>美国竞技联盟<br>Universitas 21<br>   \n", "11342        公立常春藤<br>美国竞技联盟<br>Universitas 21<br>   \n", "\n", "                             school_ranks  ... interview_type  \\\n", "4774   2026qs第81名<br/>2025usnews第46名<br/>  ...            NaN   \n", "4775   2026qs第81名<br/>2025usnews第46名<br/>  ...            NaN   \n", "11341                 2025usnews第70名<br/>  ...            NaN   \n", "11342                 2025usnews第70名<br/>  ...            NaN   \n", "\n", "                                         program_website program_objectives  \\\n", "4774   https://bioe.uw.edu/academic-programs/masters/ms/                NaN   \n", "4775   https://bioe.uw.edu/academic-programs/masters/ms/                NaN   \n", "11341  https://statistics.uconn.edu/graduate/masters-...                NaN   \n", "11342  https://statistics.uconn.edu/graduate/masters-...                NaN   \n", "\n", "                                application_requirements gpa_requirements  \\\n", "4774   建议有以下先修课：代数、线性代数、三角学、常微分方程、信号分析、概率论与统计学、编程、电气工...              NaN   \n", "4775   GPA3.0；具有强大的研究技能（有至少一年干湿实验室经验者优先）；生物工程、化学工程、化学...              NaN   \n", "11341  1.先修课：微积分（3学期,包括1学期多元微积分）,线性代数（1学期）,统计（2学期）\\n2...              NaN   \n", "11342  整个本科累计平均绩点为3.0;本科最后两年的平均绩点至少为3.0;或在整个本科最后一年表现优...              NaN   \n", "\n", "                                   language_requirements  \\\n", "4774   托福 | 总分要求: 92; 小分要求: 无要求; <br/>雅思 | 总分要求: 7; 小...   \n", "4775   托福 | 总分要求: 80; 小分要求: 无要求; <br/>雅思 | 总分要求: 6.5;...   \n", "11341  托福 | 总分要求: 79; 小分要求: 无要求; <br/>雅思 | 总分要求: 6.5;...   \n", "11342  托福 | 总分要求: 79 | 小分要求: 小分要求: 22; <br/>雅思 | 总分要求...   \n", "\n", "                               application_time  \\\n", "4774   23年秋季入学: 开放申请(/) | 截止申请(2023-12-01)<br/>   \n", "4775                                        NaN   \n", "11341  25年秋季入学: 开放申请(/) | 截止申请(2025-04-01)<br/>   \n", "11342  25年秋季入学: 开放申请(/) | 截止申请(2025-04-01)<br/>   \n", "\n", "                                     consultant_analysis courses  \\\n", "4774   华盛顿大学生物工程系研究生阶段设有以下学位项目，分别是：生物工程理学硕士Master of ...     NaN   \n", "4775                                                 NaN     NaN   \n", "11341  康涅狄格大学统计学系开设了统计学MS项目，硕士课程强调应用统计学，要求学生在具体的应用领域至...     NaN   \n", "11342                                                NaN     NaN   \n", "\n", "      interview_experience  \n", "4774                   NaN  \n", "4775                   NaN  \n", "11341                  NaN  \n", "11342                  NaN  \n", "\n", "[4 rows x 23 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 学校中文名，项目中文名和项目英文名都重复的项...\n", "# 下面4个check后发现应该就是单纯的录入重复，但很多字段值不同\n", "program_df[program_df.duplicated(subset=['school_name_cn', 'program_name_cn', 'program_name_en'], keep=False)]"]}, {"cell_type": "markdown", "id": "050aa7d1", "metadata": {}, "source": ["#### 1.4 中英文名一致性检查"]}, {"cell_type": "code", "execution_count": 11, "id": "9d9bd359", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["180\n", "180\n"]}], "source": ["print(len(program_df['school_name_cn'].unique()))\n", "print(len(program_df['school_name_en'].unique()))"]}, {"cell_type": "code", "execution_count": 12, "id": "a580b76e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中文名对应多个英文名的情况:\n", "\n", "英文名对应多个中文名的情况:\n"]}], "source": ["# 检查中文名对应多个英文名的情况\n", "one_to_many_zh = program_df.groupby('school_name_cn')['school_name_en'].nunique()\n", "many_zh_names = one_to_many_zh[one_to_many_zh > 1].index.tolist()\n", "\n", "# 检查英文名对应多个中文名的情况\n", "one_to_many_en = program_df.groupby('school_name_en')['school_name_cn'].nunique()\n", "many_en_names = one_to_many_en[one_to_many_en > 1].index.tolist()\n", "\n", "print(\"中文名对应多个英文名的情况:\")\n", "for zh_name in many_zh_names:\n", "    print(f\"{zh_name}: {', '.join(program_df[program_df['school_name_cn'] == zh_name]['school_name_en'].unique())}\")\n", "\n", "print(\"\\n英文名对应多个中文名的情况:\")\n", "for en_name in many_en_names:\n", "    print(f\"{en_name}: {', '.join(program_df[program_df['school_name_en'] == en_name]['school_name_cn'].unique())}\")"]}, {"cell_type": "markdown", "id": "eab99775", "metadata": {}, "source": ["### 2 数据补充"]}, {"cell_type": "markdown", "id": "1d8c0124", "metadata": {}, "source": ["#### 2.1 QS排名数据补充"]}, {"cell_type": "markdown", "id": "3b10abd9", "metadata": {}, "source": ["> **院校名称对齐**：项目数据中的院校英文名（school_name_en）和原始qs排名中院校英文名不完全相同，直接join项目表和原始排名表会导致很多学校查找不到排名。可通过字符串模糊匹配等方法对齐英文名，得到program_schools_qs2026.csv，该csv的中/英文名能够与项目数据program_df中的中/英文完全匹配，再join两表。"]}, {"cell_type": "code", "execution_count": 13, "id": "9ed5fdf9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>qs2026</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>175</th>\n", "      <td>香港树仁大学</td>\n", "      <td>Hong Kong Shue Yan University</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>马来西亚莫纳什大学</td>\n", "      <td>Monash University Malaysia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>本特利大学</td>\n", "      <td>Bentley University</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>Technical University of Munich Asia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>香港演艺学院</td>\n", "      <td>The Hong Kong Academy for Performing Arts</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>180 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                             school_name_en qs2026\n", "0           麻省理工学院      Massachusetts Institute of Technology      1\n", "1           帝国理工学院                    Imperial College London      2\n", "2            斯坦福大学                        Stanford University      3\n", "3             牛津大学                       University of Oxford      4\n", "4             哈佛大学                         Harvard University      5\n", "..             ...                                        ...    ...\n", "175         香港树仁大学              Hong Kong Shue Yan University    NaN\n", "176      马来西亚莫纳什大学                 Monash University Malaysia    NaN\n", "177          本特利大学                         Bentley University    NaN\n", "178    慕尼黑工业大学亚洲分校        Technical University of Munich Asia    NaN\n", "179         香港演艺学院  The Hong Kong Academy for Performing Arts    NaN\n", "\n", "[180 rows x 3 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["program_schools_qs2026_df = pd.read_csv('院校数据/program_schools_qs2026.csv')\n", "program_schools_qs2026_df"]}, {"cell_type": "code", "execution_count": 14, "id": "160326eb", "metadata": {}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 确认项目数据中涉及的院校名都能在qs排名数据中找到\n", "set(program_df['school_name_cn'].unique()) - set(program_schools_qs2026_df['school_name_cn'].unique())"]}, {"cell_type": "code", "execution_count": 15, "id": "0af50840", "metadata": {}, "outputs": [], "source": ["# 使用merge函数进行左连接，并指定on参数为'school_name_cn'\n", "program_df = pd.merge(program_df, program_schools_qs2026_df[['school_name_cn', 'qs2026']], \n", "                     on='school_name_cn', how='left')\n", "\n", "# 将'qs2026'列重命名为'school_qs_rank'\n", "program_df.rename(columns={'qs2026': 'school_qs_rank'}, inplace=True)"]}, {"cell_type": "markdown", "id": "148dc253", "metadata": {}, "source": ["#### 2.2 学校地区数据补充"]}, {"cell_type": "code", "execution_count": 16, "id": "6784f36f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'南安普顿大学马来西亚分校',\n", " '圣何塞州立大学',\n", " '慕尼黑工业大学亚洲分校',\n", " '香港演艺学院',\n", " '马来亚大学',\n", " '马来西亚博特拉大学',\n", " '马来西亚国立大学',\n", " '马来西亚理工大学',\n", " '马来西亚理科大学',\n", " '马来西亚莫纳什大学'}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# 新项目数据中相比原项目数据中新增的院校\n", "set(program_df['school_name_cn'].unique()) - set(old_program_df['学校中文名'].unique())"]}, {"cell_type": "code", "execution_count": 17, "id": "4544b59d", "metadata": {}, "outputs": [{"data": {"text/plain": ["school_region\n", "英国      6643\n", "美国      2014\n", "澳大利亚    1305\n", "中国香港     702\n", "马来西亚     393\n", "新加坡      246\n", "中国澳门     144\n", "Name: count, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 手动添加这些学校的地区\n", "new_schools_region_dic = {\n", "    '南安普顿大学马来西亚分校': '马来西亚',\n", "    '圣何塞州立大学': '美国',\n", "    '慕尼黑工业大学亚洲分校': '新加坡',\n", "    '香港演艺学院': '香港',\n", "    '马来亚大学': '马来西亚',\n", "    '马来西亚博特拉大学': '马来西亚',\n", "    '马来西亚国立大学': '马来西亚',\n", "    '马来西亚理工大学': '马来西亚',\n", "    '马来西亚理科大学': '马来西亚',\n", "    '马来西亚莫纳什大学': '马来西亚'\n", "}\n", "\n", "# 学校地点字典\n", "schools_region_dic = dict(zip(old_program_df['学校中文名'], old_program_df['学校所在地区']))\n", "schools_region_dic.update(new_schools_region_dic)\n", "\n", "# 学校所在地区赋值\n", "program_df['school_region'] = program_df['school_name_cn'].map(schools_region_dic)\n", "\n", "# 名称规范\n", "program_df['school_region'] = program_df['school_region'].replace({'香港': '中国香港', '澳门': '中国澳门'})\n", "\n", "program_df['school_region'].value_counts()"]}, {"cell_type": "markdown", "id": "3d419677", "metadata": {}, "source": ["#### 2.3 其他数据补充"]}, {"cell_type": "code", "execution_count": 36, "id": "d082380d", "metadata": {}, "outputs": [{"data": {"text/plain": ["other_cost\n", "20-30万人民币    6643\n", "25-35万人民币    2014\n", "15-25万人民币    1551\n", "10-20万人民币     846\n", "5-10万人民币      393\n", "Name: count, dtype: int64"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# 添加 生活费 列\n", "other_cost_dic = {\n", "    '英国': '20-30万人民币',\n", "    '美国': '25-35万人民币', \n", "    '澳大利亚': '15-25万人民币',\n", "    '中国香港': '10-20万人民币',\n", "    '马来西亚': '5-10万人民币',\n", "    '新加坡': '15-25万人民币',\n", "    '中国澳门': '10-20万人民币'\n", "}\n", "\n", "program_df['other_cost'] = program_df['school_region'].map(other_cost_dic)\n", "\n", "program_df['other_cost'].value_counts()"]}, {"cell_type": "code", "execution_count": 37, "id": "f1ee5162", "metadata": {}, "outputs": [], "source": ["# 添加 留服认证，embedding，时间戳 列，并设置为空值\n", "program_df['degree'] = '硕士'\n", "program_df['degree_evaluation'] = np.nan\n", "\n", "# CSV表中不添加这几个列的空值，待数据库中自动生成，不如入库时数据类型会出错（pd.read_csv 会自动将空值转换为 NaN float类型...）\n", "# program_df['embedding'] = None # 不能用 np.nan，这个是浮点型，后续数据库模型中设置为JSONB类型\n", "# program_df['create_at'] = pd.NaT\n", "# program_df['update_at'] = pd.NaT"]}, {"cell_type": "markdown", "id": "ef332afb", "metadata": {}, "source": ["#### 2.4 整理与导出"]}, {"cell_type": "code", "execution_count": 38, "id": "f3441689", "metadata": {}, "outputs": [{"data": {"text/plain": ["school_name_cn                  0\n", "school_name_en                  0\n", "school_region                   0\n", "school_labels                  98\n", "school_ranks                  340\n", "school_qs_rank                190\n", "program_code                    0\n", "program_website                12\n", "program_name_cn                 0\n", "program_name_en                 0\n", "program_category                0\n", "program_direction               0\n", "faculty                      2016\n", "enrollment_time                51\n", "program_duration               54\n", "program_tuition                71\n", "application_time              688\n", "application_requirements      475\n", "gpa_requirements             5934\n", "language_requirements         149\n", "interview_type              10719\n", "interview_experience        11151\n", "program_objectives           2141\n", "courses                      2380\n", "consultant_analysis          8805\n", "other_cost                      0\n", "degree                          0\n", "degree_evaluation           11447\n", "dtype: int64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["new_columns = ['school_name_cn', 'school_name_en', 'school_region', 'school_labels', 'school_ranks', 'school_qs_rank',\n", "'program_code', 'program_website', 'program_name_cn', 'program_name_en', 'program_category', 'program_direction', 'faculty', 'enrollment_time', 'program_duration', 'program_tuition', \n", "'application_time', 'application_requirements', 'gpa_requirements', 'language_requirements', 'interview_type', 'interview_experience',\n", "'program_objectives', 'courses', 'consultant_analysis',\n", "'other_cost', 'degree', 'degree_evaluation']\n", "\n", "# ['embedding', 'create_at', 'update_at']\n", "\n", "# 按照指定的顺序重新排列列\n", "program_df = program_df[new_columns]\n", "\n", "program_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 40, "id": "536ed529", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'school_name_cn': 13,\n", " 'school_name_en': 67,\n", " 'school_region': 4,\n", " 'school_labels': 107,\n", " 'school_ranks': 36,\n", " 'school_qs_rank': 9,\n", " 'program_website': 250,\n", " 'program_name_cn': 44,\n", " 'program_name_en': 154,\n", " 'program_category': 2,\n", " 'program_direction': 7,\n", " 'faculty': 22,\n", " 'enrollment_time': 11,\n", " 'program_duration': 12,\n", " 'program_tuition': 47,\n", " 'application_time': 350,\n", " 'application_requirements': 1529,\n", " 'gpa_requirements': 91,\n", " 'language_requirements': 297,\n", " 'interview_type': 21,\n", " 'interview_experience': 752,\n", " 'program_objectives': 1008,\n", " 'courses': 18249,\n", " 'consultant_analysis': 2646,\n", " 'other_cost': 9,\n", " 'degree': 2}"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["# 检查字符串长度, 用于后续数据库字段长度设置\n", "def longest_string_length(df):\n", "    max_lengths = {}\n", "    for column in df.select_dtypes(exclude=[int, float]).columns:\n", "        if df[column].dtype == object or df[column].dtype.name.startswith('str'):\n", "            max_lengths[column] = int(df[column].astype(str).apply(len).max())\n", "    return max_lengths\n", "\n", "length_check_result = longest_string_length(program_df)\n", "\n", "length_check_result"]}, {"cell_type": "code", "execution_count": null, "id": "3182ba97", "metadata": {}, "outputs": [], "source": ["# program_df.to_csv('项目数据库.csv', index=False, encoding='utf-8-sig')\n", "# program_df = pd.read_csv('项目数据库.csv')\n", "# program_df.isnull().sum()"]}, {"cell_type": "markdown", "id": "908119d5", "metadata": {}, "source": ["### 3 数据清洗规范"]}, {"cell_type": "code", "execution_count": 2, "id": "041576af", "metadata": {}, "outputs": [], "source": ["program_df = pd.read_csv('项目数据/项目数据库（数据补充版）.csv')"]}, {"cell_type": "markdown", "id": "e3b068b5", "metadata": {}, "source": ["#### 项目时长表述规范"]}, {"cell_type": "code", "execution_count": 3, "id": "38746e42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1年 : 7272\n", "2年 : 1499\n", "1.5年 : 507\n", "3年 : 63\n", "4年 : 34\n", "2.5年 : 16\n", "3.5年 : 3\n", "1.5-2年 : 2\n", "年 : 1\n", "1-2年 : 1\n"]}], "source": ["# 英联邦学校项目时长统计\n", "# 过滤出 school_region 不等于 '美国' 的行\n", "filtered_df = program_df[program_df['school_region'] != '美国']\n", "\n", "# 获取 filtered_df 中 program_duration 列的 unique 值及其计数\n", "filtered_value_counts = filtered_df['program_duration'].value_counts()\n", "\n", "# 打印每个值及其数量\n", "for value, count in filtered_value_counts.items():\n", "    print(f\"{value} : {count}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "23614bd5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_region</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>school_qs_rank</th>\n", "      <th>program_code</th>\n", "      <th>program_website</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>...</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>interview_type</th>\n", "      <th>interview_experience</th>\n", "      <th>program_objectives</th>\n", "      <th>courses</th>\n", "      <th>consultant_analysis</th>\n", "      <th>other_cost</th>\n", "      <th>degree</th>\n", "      <th>degree_evaluation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5851</th>\n", "      <td>伦敦大学玛丽皇后学院</td>\n", "      <td>Queen Mary University of London</td>\n", "      <td>英国</td>\n", "      <td>伦敦大学联盟&lt;br&gt;罗素大学集团&lt;br&gt;科学与工程南联盟&lt;br&gt;</td>\n", "      <td>2026qs第110名&lt;br/&gt;2025times第39名&lt;br/&gt;</td>\n", "      <td>110</td>\n", "      <td>53151</td>\n", "      <td>http://www.qmul.ac.uk/postgraduate/taught/cour...</td>\n", "      <td>电信与无线系统管理理学硕士</td>\n", "      <td>MSc Telecommunication and Wireless Systems Man...</td>\n", "      <td>...</td>\n", "      <td>该学校针对内地院校有专属的GPA分数要求，伦敦大学玛丽皇后学院单独设置院校名单，对内地院校划...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>互联网正以惊人的速度发展，以意想不到的方式将人与人联系起来。在伦敦大学玛丽皇后学院电信与无线...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>20-30万人民币</td>\n", "      <td>硕士</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 28 columns</p>\n", "</div>"], "text/plain": ["     school_name_cn                   school_name_en school_region  \\\n", "5851     伦敦大学玛丽皇后学院  Queen Mary University of London            英国   \n", "\n", "                         school_labels                        school_ranks  \\\n", "5851  伦敦大学联盟<br>罗素大学集团<br>科学与工程南联盟<br>  2026qs第110名<br/>2025times第39名<br/>   \n", "\n", "     school_qs_rank  program_code  \\\n", "5851            110         53151   \n", "\n", "                                        program_website program_name_cn  \\\n", "5851  http://www.qmul.ac.uk/postgraduate/taught/cour...   电信与无线系统管理理学硕士   \n", "\n", "                                        program_name_en  ...  \\\n", "5851  MSc Telecommunication and Wireless Systems Man...  ...   \n", "\n", "                                       gpa_requirements language_requirements  \\\n", "5851  该学校针对内地院校有专属的GPA分数要求，伦敦大学玛丽皇后学院单独设置院校名单，对内地院校划...                   NaN   \n", "\n", "     interview_type interview_experience  \\\n", "5851            NaN                  NaN   \n", "\n", "                                     program_objectives courses  \\\n", "5851  互联网正以惊人的速度发展，以意想不到的方式将人与人联系起来。在伦敦大学玛丽皇后学院电信与无线...     NaN   \n", "\n", "     consultant_analysis other_cost degree degree_evaluation  \n", "5851                 NaN  20-30万人民币     硕士               NaN  \n", "\n", "[1 rows x 28 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df[program_df['program_duration'] == '年']"]}, {"cell_type": "code", "execution_count": 5, "id": "1902db34", "metadata": {}, "outputs": [], "source": ["program_df.loc[5851, 'program_duration'] = '1年'"]}, {"cell_type": "code", "execution_count": 6, "id": "58f4c034", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1年 : 7513\n", "2年 : 1784\n", "1.5年 : 523\n", "30学分 : 360\n", "两年 : 87\n", "36学分 : 75\n", "一年 : 68\n", "3年 : 68\n", "32学分 : 55\n", "1-2年 : 52\n", "10个月 : 36\n", "4年 : 35\n", "9个月 : 35\n", "3学期 : 34\n", "1.5-2年 : 30\n", "33学分 : 22\n", "2学期 : 21\n", "15个月 : 17\n", "12个月 : 17\n", "11个月 : 17\n", "2.5年 : 16\n", "31学分 : 16\n", "18个月 : 15\n", "24学分 : 15\n", "4学期 : 14\n", "48学分 : 14\n", "45学分 : 14\n", "42学分 : 14\n", "12-18个月 : 13\n", "3-4学期 : 12\n", "16个月 : 12\n", "21个月 : 11\n", "28学分 : 11\n", "3个学期 : 11\n", "40学分 : 10\n", "2-3年 : 8\n", "1-1.5年 : 7\n", "60学分 : 7\n", "18-24个月 : 6\n", "10门课 : 6\n", "三个学期 : 6\n", "12-20个月 : 6\n", "12或16个月 : 6\n", "12门课 : 5\n", "44学分 : 5\n", "5 quarters : 5\n", "约30学分 : 5\n", "30-33学分 : 5\n", "至少33学分 : 5\n", "9门课 : 4\n", "38学分 : 4\n", "2-3学期 : 4\n", "3学期，36学分 : 4\n", "12-24个月 : 4\n", "13个月 : 4\n", "17个月 : 4\n", "12-16个月 : 4\n", "30-36学分 : 4\n", "34学分 : 3\n", "10-18个月 : 3\n", "8门课 : 3\n", "8个月 : 3\n", "20个月 : 3\n", "5学期 : 3\n", "3.5年 : 3\n", "12-15个月 : 3\n", "30学分，1-2年 : 3\n", "未标明 : 3\n", "39学分 : 3\n", "24-30学分 : 3\n", "10或18个月 : 3\n", "32-40学分 : 2\n", "33-36学分 : 2\n", "55学分 : 2\n", "16个月，36学分 : 2\n", "27-34学分 : 2\n", "35学分 : 2\n", "11或17个月 : 2\n", "32-36学分 : 2\n", "1年3个月 : 2\n", "8-16个月 : 2\n", "37学分 : 2\n", "90学分 : 2\n", "12或18个月 : 2\n", "12学分 : 2\n", "20学分 : 2\n", "97 units : 2\n", "两个学期 : 2\n", "26学分 : 2\n", "三或四个学期 : 2\n", "3 学期 : 2\n", "12节课 : 2\n", "2学期（30学分） : 2\n", "29学分 : 2\n", "16-24个月 : 2\n", "1年，36学分 : 2\n", "18学分 : 2\n", "32学分时 : 2\n", "1或1.5年 : 2\n", "18-24个月，36学分 : 2\n", "30-32学分 : 2\n", "9个月，35学分 : 1\n", "144units : 1\n", "3学期-2年 : 1\n", "96学分 : 1\n", "16-21个月 : 1\n", "22 个月 : 1\n", "48学分，9-18个月 : 1\n", "120学分 : 1\n", "二十个月 : 1\n", "4-6quarters : 1\n", "24-27学分 : 1\n", "9-12节课 : 1\n", "11学分 : 1\n", "9-15个月 : 1\n", "12课 : 1\n", "十个月 : 1\n", "12/16个月 : 1\n", "12门课程 : 1\n", "8-10门课 : 1\n", "11门课 : 1\n", "11节课 : 1\n", "33 学分 : 1\n", "16学分 : 1\n", "2-3个学期 : 1\n", "12 周 : 1\n", "32 学分 : 1\n", "1年，3学期 : 1\n", "15-18个月 : 1\n", "2-5年 : 1\n", "45units : 1\n", "138Units : 1\n", "4-5学期 : 1\n", "12-21个月 : 1\n", "1年-2年 : 1\n", "80学分 : 1\n", "66学分 : 1\n", "9-12个月 : 1\n", "72units : 1\n", "72学分 : 1\n", "18个月-2年 : 1\n", "36/40/65学分 : 1\n", "30学时 : 1\n", "24-32学分 : 1\n", "62学分 : 1\n", "2-4学期 : 1\n", "3-6quarters : 1\n", "98学分 : 1\n", "64学分 : 1\n", "1–1.5年 : 1\n", "3-4个学期 : 1\n", "44或48学分 : 1\n", "6个学期 : 1\n", "10个月-2年 : 1\n", "8或9门课 : 1\n", "1-3年 : 1\n", "3-5 semester : 1\n", "9-11个月 : 1\n", "10-24个月 : 1\n", "1年，52学分 : 1\n", "12个月，50学分 : 1\n", "3-4 quarters : 1\n", "十门课 : 1\n", "1年，30学分 : 1\n", "31-42学分 : 1\n", "34-44学分 : 1\n", "24个月 : 1\n", "30个学分 : 1\n", "36学分，2年 : 1\n", "32学分，2年 : 1\n", "36学分，10个月 : 1\n", "3个学期，33学分 : 1\n", "9或12个月 : 1\n", "12个月，30学分 : 1\n", "14个月 : 1\n", "37-39学分 : 1\n", "5年 : 1\n", "50学分 : 1\n", "12–16个月 : 1\n", "54学分 : 1\n", "40学时 : 1\n", "2-3学期，30学分 : 1\n", "25学分 : 1\n", "68学分 : 1\n", "1年，2学期 : 1\n", "49学分 : 1\n", "9个月-1年 : 1\n", "33-43.5学分 : 1\n", "31-36学分 : 1\n", "10-12 个月 : 1\n", "27/30学分 : 1\n", "1-2.5年 : 1\n", "36-40学分 : 1\n", "30-37学分 : 1\n", "34.5学分 : 1\n", "10或17个月 : 1\n", "42-45学分 : 1\n", "至少30学分 : 1\n", "15-21个月 : 1\n", "9个月-2年 : 1\n", "27-42学分 : 1\n", "11个月，35.5学分 : 1\n", "18-21个月 : 1\n", "3quarters : 1\n", "5-6 quarters : 1\n", "27学分 : 1\n", "7个月 : 1\n", "2-2.5年 : 1\n", "24-30个月 : 1\n", "48学分时 : 1\n", "10个月-1年4个月 : 1\n", "30学分，12-24个月 : 1\n", "34学分，2年 : 1\n", "12或24个月 : 1\n", "11-18个月 : 1\n", "10个月，37学分 : 1\n", "1-1年3个月 : 1\n", "1年，45学分 : 1\n", "9-17月 : 1\n", "48 学分 : 1\n", "30学分3学期1.5年 : 1\n", "2学期或3学期 : 1\n", "21-30学分 : 1\n", "30学分或39学分 : 1\n", "1-3学期/2.5年 : 1\n", "36学时 : 1\n", "17-22个月 : 1\n", "24-36个月 : 1\n", "59学分 : 1\n", "43学分 : 1\n"]}], "source": ["# 获取 unique 值及其计数\n", "value_counts = program_df['program_duration'].value_counts()\n", "\n", "# 打印每个值及其数量\n", "for value, count in value_counts.items():\n", "    print(f\"{value} : {count}\")"]}, {"cell_type": "code", "execution_count": null, "id": "5157d115", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1年 : 7581\n", "2年 : 1871\n", "1.5年 : 523\n", "30学分 : 360\n", "36学分 : 75\n", "3年 : 68\n", "32学分 : 55\n", "1-2年 : 52\n", "10个月 : 37\n", "9个月 : 35\n", "4年 : 35\n", "3学期 : 34\n", "1.5-2年 : 30\n", "33学分 : 22\n", "2学期 : 21\n", "12个月 : 17\n", "15个月 : 17\n", "11个月 : 17\n", "3个学期 : 17\n", "31学分 : 16\n", "2.5年 : 16\n", "24学分 : 15\n", "45学分 : 15\n", "18个月 : 15\n", "48学分 : 14\n", "4学期 : 14\n", "42学分 : 14\n", "12-18个月 : 13\n", "3-4学期 : 12\n", "16个月 : 12\n", "21个月 : 11\n", "28学分 : 11\n", "40学分 : 10\n", "2-3年 : 8\n", "60学分 : 7\n", "10门课 : 7\n", "1-1.5年 : 7\n", "12或16个月 : 6\n", "18-24个月 : 6\n", "12-20个月 : 6\n", "至少33学分 : 5\n", "30-33学分 : 5\n", "44学分 : 5\n", "5 quarters : 5\n", "12门课 : 5\n", "约30学分 : 5\n", "9门课 : 4\n", "38学分 : 4\n", "30-36学分 : 4\n", "17个月 : 4\n", "12-16个月 : 4\n", "12-24个月 : 4\n", "13个月 : 4\n", "20个月 : 4\n", "2-3学期 : 4\n", "3学期，36学分 : 4\n", "34学分 : 3\n", "10-18个月 : 3\n", "12-15个月 : 3\n", "3.5年 : 3\n", "8个月 : 3\n", "5学期 : 3\n", "30学分，1-2年 : 3\n", "未标明 : 3\n", "10或18个月 : 3\n", "8门课 : 3\n", "39学分 : 3\n", "24-30学分 : 3\n", "32-40学分 : 2\n", "55学分 : 2\n", "8-16个月 : 2\n", "1年3个月 : 2\n", "33-36学分 : 2\n", "37学分 : 2\n", "16个月，36学分 : 2\n", "27-34学分 : 2\n", "35学分 : 2\n", "32-36学分 : 2\n", "97 学分 : 2\n", "20学分 : 2\n", "3 学期 : 2\n", "2个学期 : 2\n", "12节课 : 2\n", "2学期（30学分） : 2\n", "26学分 : 2\n", "3或4个学期 : 2\n", "12学分 : 2\n", "90学分 : 2\n", "12或18个月 : 2\n", "72学分 : 2\n", "16-24个月 : 2\n", "29学分 : 2\n", "1年，36学分 : 2\n", "11或17个月 : 2\n", "30-32学分 : 2\n", "18学分 : 2\n", "18-24个月，36学分 : 2\n", "1或1.5年 : 2\n", "32学分时 : 2\n", "3学期-2年 : 1\n", "24-27学分 : 1\n", "33 学分 : 1\n", "9-12节课 : 1\n", "9-15个月 : 1\n", "12课 : 1\n", "12/16个月 : 1\n", "32 学分 : 1\n", "11学分 : 1\n", "4-6quarters : 1\n", "1年，3学期 : 1\n", "12 周 : 1\n", "2-3个学期 : 1\n", "16学分 : 1\n", "8-10门课 : 1\n", "11门课 : 1\n", "12门课程 : 1\n", "11节课 : 1\n", "4-5学期 : 1\n", "2-5年 : 1\n", "9个月，35学分 : 1\n", "66学分 : 1\n", "15-18个月 : 1\n", "1年-2年 : 1\n", "80学分 : 1\n", "138学分 : 1\n", "12-21个月 : 1\n", "9-12个月 : 1\n", "96学分 : 1\n", "120学分 : 1\n", "16-21个月 : 1\n", "48学分，9-18个月 : 1\n", "22 个月 : 1\n", "10个月-2年 : 1\n", "18个月-2年 : 1\n", "36/40/65学分 : 1\n", "30学时 : 1\n", "62学分 : 1\n", "2-4学期 : 1\n", "3-4 quarters : 1\n", "3-6quarters : 1\n", "98学分 : 1\n", "1–1.5年 : 1\n", "3-4个学期 : 1\n", "44或48学分 : 1\n", "6个学期 : 1\n", "1-3年 : 1\n", "3-5 学期 : 1\n", "8或9门课 : 1\n", "9-11个月 : 1\n", "24-32学分 : 1\n", "10-24个月 : 1\n", "64学分 : 1\n", "1年，52学分 : 1\n", "12个月，50学分 : 1\n", "31-42学分 : 1\n", "34-44学分 : 1\n", "1年，30学分 : 1\n", "144学分 : 1\n", "24个月 : 1\n", "30个学分 : 1\n", "36学分，2年 : 1\n", "32学分，2年 : 1\n", "36学分，10个月 : 1\n", "3个学期，33学分 : 1\n", "9或12个月 : 1\n", "12个月，30学分 : 1\n", "14个月 : 1\n", "37-39学分 : 1\n", "5年 : 1\n", "50学分 : 1\n", "12–16个月 : 1\n", "54学分 : 1\n", "40学时 : 1\n", "2-3学期，30学分 : 1\n", "25学分 : 1\n", "68学分 : 1\n", "1年，2学期 : 1\n", "49学分 : 1\n", "9个月-1年 : 1\n", "33-43.5学分 : 1\n", "31-36学分 : 1\n", "10-12 个月 : 1\n", "27/30学分 : 1\n", "1-2.5年 : 1\n", "36-40学分 : 1\n", "30-37学分 : 1\n", "34.5学分 : 1\n", "10或17个月 : 1\n", "42-45学分 : 1\n", "至少30学分 : 1\n", "15-21个月 : 1\n", "9个月-2年 : 1\n", "27-42学分 : 1\n", "11个月，35.5学分 : 1\n", "18-21个月 : 1\n", "3quarters : 1\n", "5-6 quarters : 1\n", "27学分 : 1\n", "7个月 : 1\n", "2-2.5年 : 1\n", "24-30个月 : 1\n", "48学分时 : 1\n", "10个月-1年4个月 : 1\n", "30学分，12-24个月 : 1\n", "34学分，2年 : 1\n", "12或24个月 : 1\n", "11-18个月 : 1\n", "10个月，37学分 : 1\n", "1-1年3个月 : 1\n", "1年，45学分 : 1\n", "9-17月 : 1\n", "48 学分 : 1\n", "30学分3学期1.5年 : 1\n", "2学期或3学期 : 1\n", "21-30学分 : 1\n", "30学分或39学分 : 1\n", "1-3学期/2.5年 : 1\n", "36学时 : 1\n", "17-22个月 : 1\n", "24-36个月 : 1\n", "59学分 : 1\n", "43学分 : 1\n"]}], "source": ["def clean_program_duration(text):\n", "    if pd.isna(text):\n", "        return text\n", "    \n", "    # 汉字数字到阿拉伯数字的映射\n", "    chinese_to_arabic = {\n", "        '零': 0, '一': 1, '二': 2, '两': 2, '三': 3, '四': 4, '五': 5,\n", "        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10, '十一': 11,\n", "        '十二': 12, '十三': 13, '十四': 14, '十五': 15, '十六': 16,\n", "        '十七': 17, '十八': 18, '十九': 19, '二十': 20\n", "    }\n", "\n", "    def convert_chinese_to_arabic(text):\n", "        for chinese_num, arabic_num in sorted(chinese_to_arabic.items(), key=lambda item: len(item[0]), reverse=True):\n", "            text = text.replace(chinese_num, str(arabic_num))\n", "        return text\n", "\n", "    def replace_units(text):\n", "        replacements = {\n", "            'Units': '学分',\n", "            'Unit': '学分',\n", "            'units': '学分',\n", "            'unit': '学分',\n", "            'semester': '学期',\n", "            'semesters': '学期'\n", "        }\n", "        for old, new in replacements.items():\n", "            text = text.replace(old, new)\n", "        return text\n", "\n", "    # 替换单位\n", "    text = replace_units(text)\n", "    # 转换汉字数字\n", "    text = convert_chinese_to_arabic(text)\n", "    return text\n", "\n", "program_df['program_duration'] = program_df['program_duration'].apply(clean_program_duration)\n", "\n", "# 获取 unique 值及其计数\n", "value_counts = program_df['program_duration'].value_counts()\n", "\n", "# 打印每个值及其数量\n", "for value, count in value_counts.items():\n", "    print(f\"{value} : {count}\")"]}, {"cell_type": "markdown", "id": "69505bbc", "metadata": {}, "source": ["#### 碎片文本清理"]}, {"cell_type": "code", "execution_count": 9, "id": "5e1ca29f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'school_name_cn': 0,\n", " 'school_name_en': 0,\n", " 'school_region': 0,\n", " 'school_labels': 0,\n", " 'school_ranks': 0,\n", " 'school_qs_rank': 0,\n", " 'program_website': 0,\n", " 'program_name_cn': 0,\n", " 'program_name_en': 0,\n", " 'program_category': 0,\n", " 'program_direction': 0,\n", " 'faculty': 0,\n", " 'enrollment_time': 0,\n", " 'program_duration': 0,\n", " 'program_tuition': 0,\n", " 'application_time': 0,\n", " 'application_requirements': 0,\n", " 'gpa_requirements': 0,\n", " 'language_requirements': 0,\n", " 'interview_type': 0,\n", " 'interview_experience': 0,\n", " 'program_objectives': 6219,\n", " 'courses': 0,\n", " 'consultant_analysis': 0,\n", " 'other_cost': 0,\n", " 'degree': 0}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 初始化一个字典来存储每列中包含“展开全部”的行数\n", "count_dict = {}\n", "\n", "# 遍历所有非数值类型的列\n", "for column in program_df.select_dtypes(include=['object']).columns:\n", "    # 统计包含“展开全部”的行数\n", "    count_dict[column] = program_df[program_df[column].str.contains('展开全部', na=False)].shape[0]\n", "    \n", "    # 移除“展开全部”\n", "    program_df[column] = program_df[column].str.replace('展开全部', '', regex=False)\n", "\n", "count_dict"]}, {"cell_type": "code", "execution_count": null, "id": "558b4d5d", "metadata": {}, "outputs": [], "source": ["# program_df.to_csv('项目数据/项目数据库（清洗规范版-无向量版）.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "id": "59949409", "metadata": {}, "source": ["### 4 向量嵌入"]}, {"cell_type": "markdown", "id": "3f67ac4a", "metadata": {}, "source": ["#### Embedding对比"]}, {"cell_type": "code", "execution_count": 2, "id": "6edd979a", "metadata": {}, "outputs": [], "source": ["bge_embedding_df = pd.read_csv('项目数据/program_data_bge_embedding.csv')\n", "qwen_embedding_df = pd.read_csv('项目数据/program_data_qwen_embedding.csv')"]}, {"cell_type": "code", "execution_count": 3, "id": "23c29e2c", "metadata": {}, "outputs": [], "source": ["# 函数：将字符串形式的嵌入向量转换为数值列表\n", "def str_to_list(embedding_str):\n", "    # 去除外层的双引号\n", "    embedding_str = embedding_str.strip('\"')\n", "    return list(map(float, embedding_str.strip('[]').split(', ')))\n", "\n", "# 提取嵌入向量并转换为数值列表\n", "bge_embedding_df['embedding'] = bge_embedding_df['embedding'].apply(str_to_list)\n", "qwen_embedding_df['embedding'] = qwen_embedding_df['embedding'].apply(str_to_list)"]}, {"cell_type": "code", "execution_count": null, "id": "5d80a176", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>program_code_pair</th>\n", "      <th>program_cosine_similarity</th>\n", "      <th>qwen_cosine_similarity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>64435 and 62541</td>\n", "      <td>0.858746</td>\n", "      <td>0.830806</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>64435 and 62542</td>\n", "      <td>0.874273</td>\n", "      <td>0.837418</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>62541 and 62542</td>\n", "      <td>0.955748</td>\n", "      <td>0.882522</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  program_code_pair  program_cosine_similarity  qwen_cosine_similarity\n", "0   64435 and 62541                   0.858746                0.830806\n", "1   64435 and 62542                   0.874273                0.837418\n", "2   62541 and 62542                   0.955748                0.882522"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取所有 program_code 组合\n", "program_codes = [64435, 62541, 62542] # 曼大MSc Data Science (Earth and Environmental Analytics) (Mathematics) (Social Analytics) 三个分支\n", "combinations = [(code1, code2) for i, code1 in enumerate(program_codes) for j, code2 in enumerate(program_codes) if i < j]\n", "\n", "# 计算相似度\n", "similarity_results = []\n", "\n", "for code1, code2 in combinations:\n", "    # 获取 program_embedding_df 中对应的 embedding 向量\n", "    prog_embed1 = bge_embedding_df.loc[bge_embedding_df['program_code'] == code1, 'embedding'].values[0]\n", "    prog_embed2 = bge_embedding_df.loc[bge_embedding_df['program_code'] == code2, 'embedding'].values[0]\n", "    \n", "    # 计算 program_embedding_df 中的余弦相似度\n", "    prog_cos_sim = cosine_similarity([prog_embed1], [prog_embed2])[0][0]\n", "    \n", "    # 获取 qwen_embedding_df 中对应的 embedding 向量\n", "    qwen_embed1 = qwen_embedding_df.loc[qwen_embedding_df['program_code'] == code1, 'embedding'].values[0]\n", "    qwen_embed2 = qwen_embedding_df.loc[qwen_embedding_df['program_code'] == code2, 'embedding'].values[0]\n", "    \n", "    # 计算 qwen_embedding_df 中的余弦相似度\n", "    qwen_cos_sim = cosine_similarity([qwen_embed1], [qwen_embed2])[0][0]\n", "    \n", "    similarity_results.append({\n", "        'program_code_pair': f'{code1} and {code2}',\n", "        'program_cosine_similarity': prog_cos_sim,\n", "        'qwen_cosine_similarity': qwen_cos_sim\n", "    })\n", "\n", "# 将结果转换为 DataFrame\n", "similarity_df = pd.DataFrame(similarity_results)\n", "similarity_df"]}, {"cell_type": "markdown", "id": "5be0f810", "metadata": {}, "source": ["#### ~~Embedding加入~~"]}, {"cell_type": "markdown", "id": "3c406a7b", "metadata": {}, "source": ["> TIPS: 现直接运行向量生成脚本后（在后端代码中），更新数据库中的向量嵌入，如果需要分享向量嵌入（包括给其他人使用和云数据库同步），不再使用如下csv过渡方法，直接从数据库导出id列和嵌入列后，使用sql脚本完成嵌入列更新即可...不需要再将原始项目数据库和嵌入向量合并后反复分发，只需要分发嵌入列即可。"]}, {"cell_type": "code", "execution_count": null, "id": "1019df24", "metadata": {"vscode": {"languageId": "sql"}}, "outputs": [], "source": ["COPY (\n", "  SELECT id, embedding\n", "  FROM ai_selection_programs\n", ") TO 'path_to\\programs_embedding_updates.csv' WITH (FORMAT CSV, HEADER);"]}, {"cell_type": "code", "execution_count": null, "id": "efa4f7b0", "metadata": {"vscode": {"languageId": "sql"}}, "outputs": [], "source": ["-- 清空主表原有的embedding列\n", "UPDATE ai_selection_programs\n", "SET embedding = NULL;\n", "\n", "-- 创建一个与要导入数据结构相同的临时表\n", "-- 临时表只在当前数据库会话中存在，关闭连接后会自动删除\n", "CREATE TEMPORARY TABLE temp_embedding_updates (\n", "    id INTEGER,\n", "    embedding JSONB\n", ");\n", "\n", "-- 将 CSV 文件的数据导入到临时表中\n", "-- 请将 '/path/to/your/embeddings_export.csv' 替换成您在目标电脑上存放该文件的实际路径\n", "COPY temp_embedding_updates FROM 'path_to\\programs_embedding_updates.csv' WITH (FORMAT CSV, HEADER);\n", "\n", "-- 使用临时表的数据来更新主表\n", "UPDATE ai_selection_programs\n", "SET embedding = temp.embedding\n", "FROM temp_embedding_updates AS temp\n", "WHERE ai_selection_programs.id = temp.id;"]}, {"cell_type": "code", "execution_count": 5, "id": "d7cddb1d", "metadata": {}, "outputs": [], "source": ["program_df = pd.read_csv('项目数据/项目数据库（清洗规范版-无向量版）.csv')\n", "program_embedding_df = pd.read_csv('项目数据/program_data_qwen_embedding.csv')"]}, {"cell_type": "code", "execution_count": 6, "id": "32561414", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_region</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>school_qs_rank</th>\n", "      <th>program_code</th>\n", "      <th>program_website</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>...</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>interview_type</th>\n", "      <th>interview_experience</th>\n", "      <th>program_objectives</th>\n", "      <th>courses</th>\n", "      <th>consultant_analysis</th>\n", "      <th>other_cost</th>\n", "      <th>degree</th>\n", "      <th>degree_evaluation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>美国</td>\n", "      <td>爱国者联盟&lt;br&gt;美国大学协会&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "      <td>1</td>\n", "      <td>65569</td>\n", "      <td>https://mitsloan.mit.edu/mfin</td>\n", "      <td>金融硕士</td>\n", "      <td>Master of Finance</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: Optional; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求...</td>\n", "      <td>机面（kira）/真人单面</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>和同样隶属于Sloan商学院的BA项目相比，MIT MFin的录取标准要友好太多了，而且是哈...</td>\n", "      <td>25-35万人民币</td>\n", "      <td>硕士</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>美国</td>\n", "      <td>爱国者联盟&lt;br&gt;美国大学协会&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "      <td>1</td>\n", "      <td>72540</td>\n", "      <td>https://cse.mit.edu/programs/sm/overview/</td>\n", "      <td>计算科学与工程硕士</td>\n", "      <td>MS in Computational Science and Engineering（暂停招生）</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>雅思 | 总分要求: 7; 小分要求: 无要求; &lt;br/&gt;GRE | 总分要求: 无要求;...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>25-35万人民币</td>\n", "      <td>硕士</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 28 columns</p>\n", "</div>"], "text/plain": ["  school_name_cn                         school_name_en school_region  \\\n", "0         麻省理工学院  Massachusetts Institute of Technology            美国   \n", "1         麻省理工学院  Massachusetts Institute of Technology            美国   \n", "\n", "                     school_labels                      school_ranks  \\\n", "0  爱国者联盟<br>美国大学协会<br>全球大学校长论坛<br>  2026qs第1名<br/>2025usnews第2名<br/>   \n", "1  爱国者联盟<br>美国大学协会<br>全球大学校长论坛<br>  2026qs第1名<br/>2025usnews第2名<br/>   \n", "\n", "  school_qs_rank  program_code                            program_website  \\\n", "0              1         65569              https://mitsloan.mit.edu/mfin   \n", "1              1         72540  https://cse.mit.edu/programs/sm/overview/   \n", "\n", "  program_name_cn                                    program_name_en  ...  \\\n", "0            金融硕士                                  Master of Finance  ...   \n", "1       计算科学与工程硕士  MS in Computational Science and Engineering（暂停招生）  ...   \n", "\n", "  gpa_requirements                              language_requirements  \\\n", "0              NaN  托福 | 总分要求: Optional; 小分要求: 无要求; <br/>雅思 | 总分要求...   \n", "1              NaN  雅思 | 总分要求: 7; 小分要求: 无要求; <br/>GRE | 总分要求: 无要求;...   \n", "\n", "  interview_type interview_experience program_objectives courses  \\\n", "0  机面（kira）/真人单面                  NaN                NaN     NaN   \n", "1            NaN                  NaN                NaN     NaN   \n", "\n", "                                 consultant_analysis other_cost degree  \\\n", "0  和同样隶属于Sloan商学院的BA项目相比，MIT MFin的录取标准要友好太多了，而且是哈...  25-35万人民币     硕士   \n", "1                                                NaN  25-35万人民币     硕士   \n", "\n", "  degree_evaluation  \n", "0               NaN  \n", "1               NaN  \n", "\n", "[2 rows x 28 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df.head(2)"]}, {"cell_type": "code", "execution_count": 7, "id": "85c878e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>program_code</th>\n", "      <th>school_name_en</th>\n", "      <th>program_name_en</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7934</td>\n", "      <td>73847</td>\n", "      <td>Ohio State University</td>\n", "      <td>Master of Business Administration</td>\n", "      <td>\"[-0.06436594, 0.012032372, -0.028689234, -0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>7935</td>\n", "      <td>72946</td>\n", "      <td>Ohio State University</td>\n", "      <td>Master of Public Health</td>\n", "      <td>\"[-0.07913416, 0.05467451, -0.**********, -0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8134</td>\n", "      <td>76388</td>\n", "      <td>University of Maryland, College Park</td>\n", "      <td>MA in Criminology and Criminal Justice</td>\n", "      <td>\"[-0.07049312, 0.049166292, -0.**********, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8136</td>\n", "      <td>71987</td>\n", "      <td>University of Maryland, College Park</td>\n", "      <td>MEng Civil and Environmental Engineering</td>\n", "      <td>\"[-0.066148385, 0.004040885, 0.032528225, 0.05...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8179</td>\n", "      <td>73719</td>\n", "      <td>University of Florida</td>\n", "      <td>Master of Public Health</td>\n", "      <td>\"[-0.033395633, 0.019248668, 0.**********, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11442</th>\n", "      <td>7436</td>\n", "      <td>65574</td>\n", "      <td>Washington University in St. Louis</td>\n", "      <td>MS in Finance</td>\n", "      <td>\"[-0.026039949, 0.023865215, 0.03155496, 0.032...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11443</th>\n", "      <td>7478</td>\n", "      <td>51119</td>\n", "      <td>University of York(United Kingdom)</td>\n", "      <td>MSc Advanced Computer Science</td>\n", "      <td>\"[0.049322627, -0.002808367, -0.010012755, -0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11444</th>\n", "      <td>7544</td>\n", "      <td>67023</td>\n", "      <td>University of York(United Kingdom)</td>\n", "      <td>MA Early Prehistory and Human Origins</td>\n", "      <td>\"[-0.04031615, 0.028706567, -0.02688684, -0.02...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11445</th>\n", "      <td>7684</td>\n", "      <td>67102</td>\n", "      <td>Cardiff University</td>\n", "      <td>MSc Cyber Security</td>\n", "      <td>\"[-0.018055933, -0.014083629, -0.051604565, 0....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11446</th>\n", "      <td>7834</td>\n", "      <td>71722</td>\n", "      <td>Emory University</td>\n", "      <td>MPH in Biostatistics</td>\n", "      <td>\"[-0.016253266, 0.024958309, 0.009218379, -0.0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>11447 rows × 5 columns</p>\n", "</div>"], "text/plain": ["         id  program_code                        school_name_en  \\\n", "0      7934         73847                 Ohio State University   \n", "1      7935         72946                 Ohio State University   \n", "2      8134         76388  University of Maryland, College Park   \n", "3      8136         71987  University of Maryland, College Park   \n", "4      8179         73719                 University of Florida   \n", "...     ...           ...                                   ...   \n", "11442  7436         65574    Washington University in St. Louis   \n", "11443  7478         51119    University of York(United Kingdom)   \n", "11444  7544         67023    University of York(United Kingdom)   \n", "11445  7684         67102                    Cardiff University   \n", "11446  7834         71722                      Emory University   \n", "\n", "                                program_name_en  \\\n", "0             Master of Business Administration   \n", "1                       Master of Public Health   \n", "2        MA in Criminology and Criminal Justice   \n", "3      MEng Civil and Environmental Engineering   \n", "4                       Master of Public Health   \n", "...                                         ...   \n", "11442                             MS in Finance   \n", "11443             MSc Advanced Computer Science   \n", "11444     MA Early Prehistory and Human Origins   \n", "11445                        MSc Cyber Security   \n", "11446                      MPH in Biostatistics   \n", "\n", "                                               embedding  \n", "0      \"[-0.06436594, 0.012032372, -0.028689234, -0.0...  \n", "1      \"[-0.07913416, 0.05467451, -0.**********, -0.0...  \n", "2      \"[-0.07049312, 0.049166292, -0.**********, 0.0...  \n", "3      \"[-0.066148385, 0.004040885, 0.032528225, 0.05...  \n", "4      \"[-0.033395633, 0.019248668, 0.**********, -0....  \n", "...                                                  ...  \n", "11442  \"[-0.026039949, 0.023865215, 0.03155496, 0.032...  \n", "11443  \"[0.049322627, -0.002808367, -0.010012755, -0....  \n", "11444  \"[-0.04031615, 0.028706567, -0.02688684, -0.02...  \n", "11445  \"[-0.018055933, -0.014083629, -0.051604565, 0....  \n", "11446  \"[-0.016253266, 0.024958309, 0.009218379, -0.0...  \n", "\n", "[11447 rows x 5 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["program_embedding_df"]}, {"cell_type": "code", "execution_count": 8, "id": "3c8c3982", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["program_code 列匹配正确\n"]}], "source": ["# 去掉 embedding 列中的双引号\n", "program_embedding_df['embedding'] = program_embedding_df['embedding'].str.strip('\"')\n", "\n", "# 调整 program_embedding_df 的 id 列以匹配 program_df 的索引\n", "program_embedding_df['index'] = program_embedding_df['id'] - 1\n", "\n", "# 设置 program_df 的索引列为 'index'\n", "program_df.index.name = 'index'\n", "\n", "# 合并两个 DataFrame\n", "merged_df = pd.merge(program_df.reset_index(), program_embedding_df[['index', 'embedding', 'program_code']], on='index', how='left')\n", "\n", "# 删除临时添加的 index 列\n", "merged_df.drop(columns=['index'], inplace=True)\n", "\n", "# 检查合并后的数据框中 program_code 是否一致\n", "if merged_df['program_code_x'].equals(merged_df['program_code_y']):\n", "    print(\"program_code 列匹配正确\")\n", "else:\n", "    print(\"program_code 列匹配不正确\")\n", "\n", "# 如果需要查看具体的不匹配项，可以使用以下代码\n", "# mismatched_rows = merged_df[merged_df['program_code_x'] != merged_df['program_code_y']]\n", "\n", "# 去掉 program_code_y 列，并将 program_code_x 列改回原名\n", "merged_df.drop(columns=['program_code_y'], inplace=True)\n", "merged_df.rename(columns={'program_code_x': 'program_code'}, inplace=True)\n", "\n", "# 最终的结果保存到新的 DataFrame 或覆盖原有的 program_df\n", "# program_df = merged_df.copy()"]}, {"cell_type": "code", "execution_count": 9, "id": "02ad7fed", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_region</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>school_qs_rank</th>\n", "      <th>program_code</th>\n", "      <th>program_website</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>...</th>\n", "      <th>language_requirements</th>\n", "      <th>interview_type</th>\n", "      <th>interview_experience</th>\n", "      <th>program_objectives</th>\n", "      <th>courses</th>\n", "      <th>consultant_analysis</th>\n", "      <th>other_cost</th>\n", "      <th>degree</th>\n", "      <th>degree_evaluation</th>\n", "      <th>embedding</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>美国</td>\n", "      <td>爱国者联盟&lt;br&gt;美国大学协会&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "      <td>1</td>\n", "      <td>65569</td>\n", "      <td>https://mitsloan.mit.edu/mfin</td>\n", "      <td>金融硕士</td>\n", "      <td>Master of Finance</td>\n", "      <td>...</td>\n", "      <td>托福 | 总分要求: Optional; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求...</td>\n", "      <td>机面（kira）/真人单面</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>和同样隶属于Sloan商学院的BA项目相比，MIT MFin的录取标准要友好太多了，而且是哈...</td>\n", "      <td>25-35万人民币</td>\n", "      <td>硕士</td>\n", "      <td>NaN</td>\n", "      <td>[-0.017185006, 0.0020031047, -0.031962663, 0.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>美国</td>\n", "      <td>爱国者联盟&lt;br&gt;美国大学协会&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "      <td>1</td>\n", "      <td>72540</td>\n", "      <td>https://cse.mit.edu/programs/sm/overview/</td>\n", "      <td>计算科学与工程硕士</td>\n", "      <td>MS in Computational Science and Engineering（暂停招生）</td>\n", "      <td>...</td>\n", "      <td>雅思 | 总分要求: 7; 小分要求: 无要求; &lt;br/&gt;GRE | 总分要求: 无要求;...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>25-35万人民币</td>\n", "      <td>硕士</td>\n", "      <td>NaN</td>\n", "      <td>[2.0070518e-05, 0.014064698, -0.031982936, 0.0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 29 columns</p>\n", "</div>"], "text/plain": ["  school_name_cn                         school_name_en school_region  \\\n", "0         麻省理工学院  Massachusetts Institute of Technology            美国   \n", "1         麻省理工学院  Massachusetts Institute of Technology            美国   \n", "\n", "                     school_labels                      school_ranks  \\\n", "0  爱国者联盟<br>美国大学协会<br>全球大学校长论坛<br>  2026qs第1名<br/>2025usnews第2名<br/>   \n", "1  爱国者联盟<br>美国大学协会<br>全球大学校长论坛<br>  2026qs第1名<br/>2025usnews第2名<br/>   \n", "\n", "  school_qs_rank  program_code                            program_website  \\\n", "0              1         65569              https://mitsloan.mit.edu/mfin   \n", "1              1         72540  https://cse.mit.edu/programs/sm/overview/   \n", "\n", "  program_name_cn                                    program_name_en  ...  \\\n", "0            金融硕士                                  Master of Finance  ...   \n", "1       计算科学与工程硕士  MS in Computational Science and Engineering（暂停招生）  ...   \n", "\n", "                               language_requirements interview_type  \\\n", "0  托福 | 总分要求: Optional; 小分要求: 无要求; <br/>雅思 | 总分要求...  机面（kira）/真人单面   \n", "1  雅思 | 总分要求: 7; 小分要求: 无要求; <br/>GRE | 总分要求: 无要求;...            NaN   \n", "\n", "  interview_experience program_objectives courses  \\\n", "0                  NaN                NaN     NaN   \n", "1                  NaN                NaN     NaN   \n", "\n", "                                 consultant_analysis other_cost degree  \\\n", "0  和同样隶属于Sloan商学院的BA项目相比，MIT MFin的录取标准要友好太多了，而且是哈...  25-35万人民币     硕士   \n", "1                                                NaN  25-35万人民币     硕士   \n", "\n", "  degree_evaluation                                          embedding  \n", "0               NaN  [-0.017185006, 0.0020031047, -0.031962663, 0.0...  \n", "1               NaN  [2.0070518e-05, 0.014064698, -0.031982936, 0.0...  \n", "\n", "[2 rows x 29 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "602319a8", "metadata": {}, "outputs": [], "source": ["# merged_df.to_csv(\"项目数据库.csv\", index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "id": "5abf02a9", "metadata": {}, "source": ["### 5. 数据分析"]}, {"cell_type": "code", "execution_count": 2, "id": "bed41c78", "metadata": {}, "outputs": [], "source": ["program_df = pd.read_csv('项目数据/项目数据库（清洗规范版-无向量版）.csv')"]}, {"cell_type": "code", "execution_count": 3, "id": "2c416e4a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_region</th>\n", "      <th>school_labels</th>\n", "      <th>school_ranks</th>\n", "      <th>school_qs_rank</th>\n", "      <th>program_code</th>\n", "      <th>program_website</th>\n", "      <th>program_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>...</th>\n", "      <th>gpa_requirements</th>\n", "      <th>language_requirements</th>\n", "      <th>interview_type</th>\n", "      <th>interview_experience</th>\n", "      <th>program_objectives</th>\n", "      <th>courses</th>\n", "      <th>consultant_analysis</th>\n", "      <th>other_cost</th>\n", "      <th>degree</th>\n", "      <th>degree_evaluation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>美国</td>\n", "      <td>爱国者联盟&lt;br&gt;美国大学协会&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "      <td>1</td>\n", "      <td>65569</td>\n", "      <td>https://mitsloan.mit.edu/mfin</td>\n", "      <td>金融硕士</td>\n", "      <td>Master of Finance</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>托福 | 总分要求: Optional; 小分要求: 无要求; &lt;br/&gt;雅思 | 总分要求...</td>\n", "      <td>机面（kira）/真人单面</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>和同样隶属于Sloan商学院的BA项目相比，MIT MFin的录取标准要友好太多了，而且是哈...</td>\n", "      <td>25-35万人民币</td>\n", "      <td>硕士</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>美国</td>\n", "      <td>爱国者联盟&lt;br&gt;美国大学协会&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "      <td>1</td>\n", "      <td>72540</td>\n", "      <td>https://cse.mit.edu/programs/sm/overview/</td>\n", "      <td>计算科学与工程硕士</td>\n", "      <td>MS in Computational Science and Engineering（暂停招生）</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>雅思 | 总分要求: 7; 小分要求: 无要求; &lt;br/&gt;GRE | 总分要求: 无要求;...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>25-35万人民币</td>\n", "      <td>硕士</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 28 columns</p>\n", "</div>"], "text/plain": ["  school_name_cn                         school_name_en school_region  \\\n", "0         麻省理工学院  Massachusetts Institute of Technology            美国   \n", "1         麻省理工学院  Massachusetts Institute of Technology            美国   \n", "\n", "                     school_labels                      school_ranks  \\\n", "0  爱国者联盟<br>美国大学协会<br>全球大学校长论坛<br>  2026qs第1名<br/>2025usnews第2名<br/>   \n", "1  爱国者联盟<br>美国大学协会<br>全球大学校长论坛<br>  2026qs第1名<br/>2025usnews第2名<br/>   \n", "\n", "  school_qs_rank  program_code                            program_website  \\\n", "0              1         65569              https://mitsloan.mit.edu/mfin   \n", "1              1         72540  https://cse.mit.edu/programs/sm/overview/   \n", "\n", "  program_name_cn                                    program_name_en  ...  \\\n", "0            金融硕士                                  Master of Finance  ...   \n", "1       计算科学与工程硕士  MS in Computational Science and Engineering（暂停招生）  ...   \n", "\n", "  gpa_requirements                              language_requirements  \\\n", "0              NaN  托福 | 总分要求: Optional; 小分要求: 无要求; <br/>雅思 | 总分要求...   \n", "1              NaN  雅思 | 总分要求: 7; 小分要求: 无要求; <br/>GRE | 总分要求: 无要求;...   \n", "\n", "  interview_type interview_experience program_objectives courses  \\\n", "0  机面（kira）/真人单面                  NaN                NaN     NaN   \n", "1            NaN                  NaN                NaN     NaN   \n", "\n", "                                 consultant_analysis other_cost degree  \\\n", "0  和同样隶属于Sloan商学院的BA项目相比，MIT MFin的录取标准要友好太多了，而且是哈...  25-35万人民币     硕士   \n", "1                                                NaN  25-35万人民币     硕士   \n", "\n", "  degree_evaluation  \n", "0               NaN  \n", "1               NaN  \n", "\n", "[2 rows x 28 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["program_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "id": "b70ca139", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据库中英国地区学校总数: 49\n", "数据库中英国地区项目总数: 6643\n"]}], "source": ["# 筛选出 school_region 为 \"英国\" 的项目\n", "uk_programs = program_df[program_df['school_region'] == '英国'] # 共6643个\n", "print(f'数据库中英国地区学校总数: {len(uk_programs['school_name_cn'].unique())}')\n", "print(f'数据库中英国地区项目总数: {len(uk_programs)}')\n", "# uk_programs.to_csv('uk_programs.csv', index=False, encoding='utf-8-sig ')"]}, {"cell_type": "markdown", "id": "c3638590", "metadata": {}, "source": ["#### 英国学校入学成绩等级要求统计"]}, {"cell_type": "code", "execution_count": null, "id": "135a49eb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>program_name_en</th>\n", "      <th>application_requirements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1948</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Computational Finance</td>\n", "      <td>具有high 2:1本科（荣誉）学士学位或同等学历，需要计算机科学或其他相关定量学科背景（如...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1949</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Financial Mathematics</td>\n", "      <td>具有high 2:1本科（荣誉）学士学位，需要数学或以数学为基础的学科背景\\n申请者需要证明...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1950</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Banking and Finance</td>\n", "      <td>具有high 2:1本科（荣誉）学士学位或同等学历，需要社会科学相关领域背景（如管理学、经济...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1951</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Finance (Asset Pricing)</td>\n", "      <td>具有high 2:1本科（荣誉）学士学位，需要社会科学相关领域背景（如管理学、经济学、金融学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1952</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Finance (Corporate Finance)</td>\n", "      <td>具有high 2:1本科（荣誉）学士学位，需要社会科学相关领域背景（如管理学、经济学、金融学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2100</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Mathematics</td>\n", "      <td>具有2:1本科（荣誉）学士学位，需要具备以数学为主要研究领域的背景，学校也欢迎具有2:2荣誉...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2101</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Microbiome in Health &amp; Disease</td>\n", "      <td>具有2:1本科（荣誉）学士学位，需要计算机科学、数学、生物科学、生物技术、工程学或化学或类似...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2102</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Physics</td>\n", "      <td>具有2:1本科（荣誉）学士学位，需要物理学背景，或者，与物理学相关的学位学科将根据具体情况被...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2103</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Statistics</td>\n", "      <td>具有high 2:1本科（荣誉）学士学位或同等学历，需要数学或以数学为基础的学科背景</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2104</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>MSc Theoretical Physics</td>\n", "      <td>具有2:1本科（荣誉）学士学位，需要物理学或数学背景，或者，与数学相关的学位学科将根据具体情...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>152 rows × 3 columns</p>\n", "</div>"], "text/plain": ["     school_name_cn                     program_name_en  \\\n", "1948       伦敦大学国王学院           MSc Computational Finance   \n", "1949       伦敦大学国王学院           MSc Financial Mathematics   \n", "1950       伦敦大学国王学院             MSc Banking and Finance   \n", "1951       伦敦大学国王学院         MSc Finance (Asset Pricing)   \n", "1952       伦敦大学国王学院     MSc Finance (Corporate Finance)   \n", "...             ...                                 ...   \n", "2100       伦敦大学国王学院                     MSc Mathematics   \n", "2101       伦敦大学国王学院  MSc Microbiome in Health & Disease   \n", "2102       伦敦大学国王学院                         MSc Physics   \n", "2103       伦敦大学国王学院                      MSc Statistics   \n", "2104       伦敦大学国王学院             MSc Theoretical Physics   \n", "\n", "                               application_requirements  \n", "1948  具有high 2:1本科（荣誉）学士学位或同等学历，需要计算机科学或其他相关定量学科背景（如...  \n", "1949  具有high 2:1本科（荣誉）学士学位，需要数学或以数学为基础的学科背景\\n申请者需要证明...  \n", "1950  具有high 2:1本科（荣誉）学士学位或同等学历，需要社会科学相关领域背景（如管理学、经济...  \n", "1951  具有high 2:1本科（荣誉）学士学位，需要社会科学相关领域背景（如管理学、经济学、金融学...  \n", "1952  具有high 2:1本科（荣誉）学士学位，需要社会科学相关领域背景（如管理学、经济学、金融学...  \n", "...                                                 ...  \n", "2100  具有2:1本科（荣誉）学士学位，需要具备以数学为主要研究领域的背景，学校也欢迎具有2:2荣誉...  \n", "2101  具有2:1本科（荣誉）学士学位，需要计算机科学、数学、生物科学、生物技术、工程学或化学或类似...  \n", "2102  具有2:1本科（荣誉）学士学位，需要物理学背景，或者，与物理学相关的学位学科将根据具体情况被...  \n", "2103         具有high 2:1本科（荣誉）学士学位或同等学历，需要数学或以数学为基础的学科背景  \n", "2104  具有2:1本科（荣誉）学士学位，需要物理学或数学背景，或者，与数学相关的学位学科将根据具体情...  \n", "\n", "[152 rows x 3 columns]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# 例子\n", "school_programs = uk_programs[uk_programs['school_name_cn'] == '伦敦大学国王学院']  \n", "# 统计不含 \"2:1\" 或 \"2：1\" 但含有 \"2:2\" 或 \"2：2\" 的项目数\n", "test_programs = school_programs[\n", "        (~school_programs['application_requirements'].str.contains('high[2:：]1', na=False)) &\n", "        (school_programs['application_requirements'].str.contains('2[:：]1', na=False))\n", "    ]\n", "\n", "test_programs[['school_name_cn', 'program_name_en', 'application_requirements']]\n", "\n", "## 如何筛选出不含 'high[2:：]1' 但含 '2[:：]1' 的..."]}, {"cell_type": "code", "execution_count": 36, "id": "f597678f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>ratio_2_1</th>\n", "      <th>ratio_2_2</th>\n", "      <th>count_missing</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>帝国理工学院</td>\n", "      <td>75.89</td>\n", "      <td>0.89</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>牛津大学</td>\n", "      <td>87.85</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>剑桥大学</td>\n", "      <td>5.98</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>伦敦大学学院</td>\n", "      <td>74.57</td>\n", "      <td>2.39</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>伦敦大学国王学院</td>\n", "      <td>96.82</td>\n", "      <td>1.91</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>爱丁堡大学</td>\n", "      <td>98.17</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>曼彻斯特大学</td>\n", "      <td>89.58</td>\n", "      <td>2.50</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>布里斯托大学</td>\n", "      <td>99.30</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>伦敦政治经济学院</td>\n", "      <td>95.92</td>\n", "      <td>0.68</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>华威大学</td>\n", "      <td>80.60</td>\n", "      <td>14.18</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>伯明翰大学</td>\n", "      <td>97.11</td>\n", "      <td>2.31</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>格拉斯哥大学</td>\n", "      <td>95.91</td>\n", "      <td>0.45</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>利兹大学</td>\n", "      <td>93.05</td>\n", "      <td>6.42</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>南安普顿大学</td>\n", "      <td>89.83</td>\n", "      <td>9.60</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>谢菲尔德大学</td>\n", "      <td>92.16</td>\n", "      <td>5.88</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>杜伦大学</td>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>诺丁汉大学</td>\n", "      <td>89.80</td>\n", "      <td>10.20</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>伦敦大学玛丽皇后学院</td>\n", "      <td>72.26</td>\n", "      <td>23.87</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>圣安德鲁斯大学</td>\n", "      <td>93.33</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>巴斯大学</td>\n", "      <td>58.65</td>\n", "      <td>25.96</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>纽卡斯尔大学（英国）</td>\n", "      <td>82.31</td>\n", "      <td>16.15</td>\n", "      <td>0.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>利物浦大学</td>\n", "      <td>5.23</td>\n", "      <td>92.81</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>埃克塞特大学</td>\n", "      <td>24.48</td>\n", "      <td>73.96</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>兰卡斯特大学</td>\n", "      <td>88.12</td>\n", "      <td>11.88</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>约克大学（英国）</td>\n", "      <td>2.13</td>\n", "      <td>97.34</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>卡迪夫大学</td>\n", "      <td>27.59</td>\n", "      <td>66.90</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>雷丁大学</td>\n", "      <td>41.18</td>\n", "      <td>31.76</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>贝尔法斯特女王大学</td>\n", "      <td>28.57</td>\n", "      <td>70.33</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>拉夫堡大学</td>\n", "      <td>29.91</td>\n", "      <td>69.23</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>思克莱德大学</td>\n", "      <td>21.05</td>\n", "      <td>18.42</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>萨里大学</td>\n", "      <td>8.43</td>\n", "      <td>91.57</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>阿伯丁大学</td>\n", "      <td>65.49</td>\n", "      <td>29.58</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>萨塞克斯大学</td>\n", "      <td>53.08</td>\n", "      <td>40.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>赫瑞瓦特大学</td>\n", "      <td>24.24</td>\n", "      <td>62.63</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>莱斯特大学</td>\n", "      <td>35.53</td>\n", "      <td>13.16</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>东安格利亚大学</td>\n", "      <td>53.57</td>\n", "      <td>46.43</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>阿斯顿大学</td>\n", "      <td>7.46</td>\n", "      <td>65.67</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>肯特大学</td>\n", "      <td>18.09</td>\n", "      <td>7.45</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>邓迪大学</td>\n", "      <td>15.15</td>\n", "      <td>80.30</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>埃塞克斯大学</td>\n", "      <td>27.89</td>\n", "      <td>68.95</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>伦敦大学皇家霍洛威学院</td>\n", "      <td>0.00</td>\n", "      <td>2.41</td>\n", "      <td>6.02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>伦敦大学城市学院</td>\n", "      <td>63.39</td>\n", "      <td>19.64</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>威斯敏斯特大学</td>\n", "      <td>24.27</td>\n", "      <td>75.73</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>考文垂大学</td>\n", "      <td>8.63</td>\n", "      <td>72.66</td>\n", "      <td>0.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>白金汉大学</td>\n", "      <td>5.88</td>\n", "      <td>11.76</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>伦敦大学亚非学院</td>\n", "      <td>0.00</td>\n", "      <td>21.35</td>\n", "      <td>5.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>伦敦商学院</td>\n", "      <td>40.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>克兰菲尔德大学</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>5.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>伦敦大学金史密斯学院</td>\n", "      <td>70.37</td>\n", "      <td>0.93</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   school_name_cn  ratio_2_1  ratio_2_2  count_missing\n", "0          帝国理工学院      75.89       0.89           0.00\n", "1            牛津大学      87.85       0.00           0.00\n", "2            剑桥大学       5.98       0.00           0.00\n", "3          伦敦大学学院      74.57       2.39           0.00\n", "4        伦敦大学国王学院      96.82       1.91           0.00\n", "5           爱丁堡大学      98.17       0.00           0.00\n", "6          曼彻斯特大学      89.58       2.50           0.00\n", "7          布里斯托大学      99.30       0.00           0.00\n", "8        伦敦政治经济学院      95.92       0.68           0.00\n", "9            华威大学      80.60      14.18           0.00\n", "10          伯明翰大学      97.11       2.31           0.00\n", "11         格拉斯哥大学      95.91       0.45           0.00\n", "12           利兹大学      93.05       6.42           0.00\n", "13         南安普顿大学      89.83       9.60           0.00\n", "14         谢菲尔德大学      92.16       5.88           0.00\n", "15           杜伦大学     100.00       0.00           0.00\n", "16          诺丁汉大学      89.80      10.20           0.00\n", "17     伦敦大学玛丽皇后学院      72.26      23.87           0.65\n", "18        圣安德鲁斯大学      93.33       0.00           0.00\n", "19           巴斯大学      58.65      25.96           0.00\n", "20     纽卡斯尔大学（英国）      82.31      16.15           0.77\n", "21          利物浦大学       5.23      92.81           0.00\n", "22         埃克塞特大学      24.48      73.96           0.00\n", "23         兰卡斯特大学      88.12      11.88           0.00\n", "24       约克大学（英国）       2.13      97.34           0.00\n", "25          卡迪夫大学      27.59      66.90           0.00\n", "26           雷丁大学      41.18      31.76           0.00\n", "27      贝尔法斯特女王大学      28.57      70.33           0.00\n", "28          拉夫堡大学      29.91      69.23           0.00\n", "29         思克莱德大学      21.05      18.42           0.00\n", "30           萨里大学       8.43      91.57           0.00\n", "31          阿伯丁大学      65.49      29.58           0.00\n", "32         萨塞克斯大学      53.08      40.00           0.00\n", "33         赫瑞瓦特大学      24.24      62.63           0.00\n", "34          莱斯特大学      35.53      13.16           0.00\n", "35        东安格利亚大学      53.57      46.43           0.00\n", "36          阿斯顿大学       7.46      65.67           0.00\n", "37           肯特大学      18.09       7.45           0.00\n", "38           邓迪大学      15.15      80.30           0.00\n", "39         埃塞克斯大学      27.89      68.95           0.00\n", "40    伦敦大学皇家霍洛威学院       0.00       2.41           6.02\n", "41       伦敦大学城市学院      63.39      19.64           0.00\n", "42        威斯敏斯特大学      24.27      75.73           0.00\n", "43          考文垂大学       8.63      72.66           0.72\n", "44          白金汉大学       5.88      11.76           0.00\n", "45       伦敦大学亚非学院       0.00      21.35           5.62\n", "46          伦敦商学院      40.00       0.00           0.00\n", "47        克兰菲尔德大学       0.00       0.00           5.56\n", "48     伦敦大学金史密斯学院      70.37       0.93           0.00"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# 初始化一个字典来存储结果\n", "results = {}\n", "\n", "# 遍历每个学校\n", "for school in uk_programs['school_name_cn'].unique():\n", "    # 获取该学校的所有项目\n", "    school_programs = uk_programs[uk_programs['school_name_cn'] == school]\n", "    \n", "    # 统计总项目数\n", "    total_projects = len(school_programs)\n", "    \n", "    # 统计含有 \"2:1\" 或 \"2：1\" 的项目数\n", "    count_2_1 = school_programs[school_programs['application_requirements'].str.contains('2[:：]1', na=False)].shape[0]\n", "    \n", "    # 统计不含 \"2:1\" 或 \"2：1\" 但含有 \"2:2\" 或 \"2：2\" 的项目数\n", "    count_2_2_only = school_programs[\n", "        (~school_programs['application_requirements'].str.contains('2[:：]1', na=False)) &\n", "        (school_programs['application_requirements'].str.contains('2[:：]2', na=False))\n", "    ].shape[0]\n", "    \n", "    # 统计 application_requirements 列值为空值的数量\n", "    count_missing = school_programs['application_requirements'].isna().sum()\n", "    \n", "    # 计算比例\n", "    ratio_2_1 = count_2_1 / total_projects if total_projects > 0 else 0\n", "    ratio_2_2 = count_2_2_only / total_projects if total_projects > 0 else 0\n", "    ratio_missing = count_missing / total_projects if total_projects > 0 else 0\n", "    \n", "    # 存储结果\n", "    results[school] = {'ratio_2_1': round(ratio_2_1*100, 2), 'ratio_2_2': round(ratio_2_2*100, 2), 'count_missing': round(ratio_missing*100, 2)}\n", "\n", "# 将结果转换为 DataFrame\n", "results_df = pd.DataFrame(results).T.reset_index()\n", "results_df.columns = ['school_name_cn', 'ratio_2_1', 'ratio_2_2', 'count_missing']\n", "\n", "results_df"]}, {"cell_type": "markdown", "id": "529b5117", "metadata": {}, "source": ["#### 学院统计(部分英国学校会按学院划分录取要求...)"]}, {"cell_type": "code", "execution_count": 6, "id": "6c112e89", "metadata": {}, "outputs": [], "source": ["durham_df = uk_programs[uk_programs['school_name_cn'] == '杜伦大学'] "]}, {"cell_type": "code", "execution_count": 13, "id": "2b9<PERSON><PERSON><PERSON>", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["商学院项目数: 21\n", "工程学院项目数: 6\n", "法学院项目数: 10\n", "其他学院项目数: 75\n"]}], "source": ["business_count = len(durham_df[durham_df['faculty'] == '商学院'])\n", "engineering_count = len(durham_df[durham_df['faculty'] == '工程学院'])\n", "law_count = len(durham_df[durham_df['faculty'] == '法学院'])\n", "rest_count = len(durham_df) - business_count - engineering_count - law_count\n", "print(f'商学院项目数: {business_count}')\n", "print(f'工程学院项目数: {engineering_count}')\n", "print(f'法学院项目数: {law_count}')\n", "print(f'其他学院项目数: {rest_count}')"]}], "metadata": {"kernelspec": {"display_name": "tunshu_data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}