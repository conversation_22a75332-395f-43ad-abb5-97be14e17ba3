{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import wordninja\n", "from thefuzz import fuzz, process\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from requests.adapters import HTTPAdapter\n", "import ssl\n", "\n", "from PIL import Image\n", "from io import BytesIO\n", "\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1 境内院校数据库基本制备"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 软科榜单处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取Excel文件\n", "file_path = '院校数据/2025年软科中国大学排名.xlsx'\n", "xls = pd.ExcelFile(file_path)\n", "\n", "# 初始化一个空的DataFrame用于存储最终结果\n", "ruanke_df = pd.DataFrame()\n", "\n", "# 定义需要处理的子表名列表\n", "sheet_names = ['主榜', '医药类', '财经类', '语言类', '政法类', '民族类', '体育类', '合作办学大学']\n", "\n", "for sheet_name in sheet_names:\n", "    # 检查工作表是否存在\n", "    if sheet_name not in xls.sheet_names:\n", "        print(f\"Sheet '{sheet_name}' 不存在于Excel文件中\")\n", "        continue\n", "    \n", "    # 读取每个子表的数据\n", "    df = pd.read_excel(xls, sheet_name)\n", "    \n", "    if sheet_name == '主榜':\n", "        # 提取主榜中的特定列\n", "        columns_to_keep = ['2025排名', '学校名称', '学校类型']\n", "    else:\n", "        # 提取其他子表中的特定列\n", "        columns_to_keep = ['主榜参考排名', '学校名称', '学校类型']\n", "    \n", "    # 检查是否存在所需的列\n", "    existing_columns = set(df.columns).intersection(columns_to_keep)\n", "    if not existing_columns:\n", "        print(f\"Sheet '{sheet_name}' 缺少所需列: {columns_to_keep}\")\n", "        continue\n", "    \n", "    # 提取并重命名列\n", "    sub_df = df[list(existing_columns)].copy()\n", "    sub_df.rename(columns={'2025排名': '排名', '主榜参考排名': '排名'}, inplace=True)\n", "    \n", "    # 将子表数据追加到最终的DataFrame中\n", "    ruanke_df = pd.concat([ruanke_df, sub_df], ignore_index=True)\n", "\n", "# 数据清洗处理\n", "ruanke_df.dropna(how='all', inplace=True)\n", "\n", "# 将'排名'列中所有值为'500+'的变为500\n", "ruanke_df['排名'] = ruanke_df['排名'].apply(lambda x: 500 if x == '500+' else x)\n", "\n", "# 将'排名'列转换为int类型\n", "ruanke_df['排名'] = ruanke_df['排名'].astype(int)\n", "\n", "# 按排名排序\n", "ruanke_df.sort_values(by='排名', inplace=True)\n", "\n", "# 重置index\n", "ruanke_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 软科表与教育部官方院校名单表合并"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校类型</th>\n", "      <th>排名</th>\n", "      <th>学校名称</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>530</th>\n", "      <td>医药</td>\n", "      <td>376</td>\n", "      <td>内蒙古科技大学包头医学院</td>\n", "    </tr>\n", "    <tr>\n", "      <th>634</th>\n", "      <td>综合</td>\n", "      <td>460</td>\n", "      <td>内蒙古科技大学包头师范学院</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    学校类型   排名           学校名称\n", "530   医药  376   内蒙古科技大学包头医学院\n", "634   综合  460  内蒙古科技大学包头师范学院"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["cn_school_df = pd.read_excel('./院校数据/全国本科院校名单202406.xlsx')\n", "ruanke_df[~ruanke_df['学校名称'].isin(cn_school_df['学校名称'])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>综合</td>\n", "      <td>4111010003</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>综合</td>\n", "      <td>4111010001</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>综合</td>\n", "      <td>4133010335</td>\n", "      <td>杭州市</td>\n", "      <td>教育部</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>综合</td>\n", "      <td>4131010248</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>复旦大学</td>\n", "      <td>综合</td>\n", "      <td>4131010246</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1305</th>\n", "      <td>黑龙江工程学院昆仑旅游学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013304</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1306</th>\n", "      <td>黑龙江财经学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013298</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1307</th>\n", "      <td>齐鲁医药学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>淄博市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1308</th>\n", "      <td>齐鲁理工学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>济南市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1309</th>\n", "      <td>齐齐哈尔工程学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>齐齐哈尔市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1310 rows × 7 columns</p>\n", "</div>"], "text/plain": ["               学校名称 学校类型       学校标识码    所在地     主管部门  软科排名   备注\n", "0              清华大学   综合  4111010003    北京市      教育部     1  NaN\n", "1              北京大学   综合  4111010001    北京市      教育部     2  NaN\n", "2              浙江大学   综合  4133010335    杭州市      教育部     3  NaN\n", "3            上海交通大学   综合  4131010248    上海市      教育部     4  NaN\n", "4              复旦大学   综合  4131010246    上海市      教育部     5  NaN\n", "...             ...  ...         ...    ...      ...   ...  ...\n", "1305  黑龙江工程学院昆仑旅游学院  NaN  4123013304   哈尔滨市  黑龙江省教育厅  <NA>   民办\n", "1306        黑龙江财经学院  NaN  4123013298   哈尔滨市  黑龙江省教育厅  <NA>   民办\n", "1307         齐鲁医药学院  NaN  **********    淄博市   山东省教育厅  <NA>   民办\n", "1308         齐鲁理工学院  NaN  **********    济南市   山东省教育厅  <NA>   民办\n", "1309       齐齐哈尔工程学院  NaN  **********  齐齐哈尔市  黑龙江省教育厅  <NA>   民办\n", "\n", "[1310 rows x 7 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 处理 cn_school_df\n", "# 将 '学校标识码' 列转换为 str 类型\n", "cn_school_df['学校标识码'] = cn_school_df['学校标识码'].astype(str)\n", "\n", "# 去掉 '办学层次' 列\n", "if '办学层次' in cn_school_df.columns:\n", "    cn_school_df.drop(columns=['办学层次'], inplace=True)\n", "\n", "# 将 '排名' 列重命名为 '软科排名'\n", "if '排名' in ruanke_df.columns:\n", "    ruanke_df.rename(columns={'排名': '软科排名'}, inplace=True)\n", "\n", "# 合并两个 DataFrame，以 outer 方式连接\n", "merged_cn_school_df = pd.merge(ruanke_df, cn_school_df, on='学校名称', how='outer')\n", "\n", "# 按 '软科排名' 排序，NaN 值排在最后\n", "merged_cn_school_df.sort_values(by='软科排名', ascending=True, na_position='last', inplace=True)\n", "merged_cn_school_df['软科排名'] = merged_cn_school_df['软科排名'].astype('Int64')\n", "\n", "# 重置 index\n", "merged_cn_school_df.reset_index(drop=True, inplace=True)\n", "\n", "# 重新排列列的顺序\n", "merged_cn_school_df = merged_cn_school_df[['学校名称', '学校类型', '学校标识码', '所在地', '主管部门', '软科排名', '备注']]\n", "\n", "merged_cn_school_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# merged_cn_school_df.to_csv(\"院校数据/境内院校数据库（初始无list版）.csv\", index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2 境内院校数据库更新"]}, {"cell_type": "markdown", "metadata": {}, "source": ["境内院校数据库更新的内容一般包括:\n", "1. 每年教育部官方院校名单的更新（比如下面的处理过程主要就是将2024.06的名单换成2025.06的最新名单，除了新增60所左右本科院校，还有部分院校更改了名称）\n", "2. 每年软科中国大学排名的更新（此处不涉及，但明年更新后需要更新该排名）\n", "3. 院校标签的更新（985/211不会再变化，主要是双一流和中外合办，双一流后续可具体到双一流专业...下面的处理过程主要是新增双一流和中外合办的标签）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> TIPS: `境内院校数据库（初始无list版）.csv`到`境内院校数据库（初版list）.csv` 的处理过程在后续`院校list及GPA要求数据处理`部分，但该学校层面的list已不再需要（再其他地方处理获得具体的项目层面的list了）。在此处理过程中才添加的985/211标签信息了，但这些信息应该在基本制备时就添加，即`境内院校数据库（初始无list版）.csv`中就需要含有..."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["home_school = pd.read_csv('./院校数据/境内院校数据库（初版list）.csv', dtype={'学校标识码': 'str', '软科排名': 'Int64'})\n", "cn_school_2025_df = pd.read_excel('./院校数据/全国本科院校名单202506.xls', dtype={'学校标识码': 'str'})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>排名</th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>261</th>\n", "      <td>200</td>\n", "      <td>新乡医学院</td>\n", "      <td>医药</td>\n", "    </tr>\n", "    <tr>\n", "      <th>378</th>\n", "      <td>276</td>\n", "      <td>桂林医学院</td>\n", "      <td>医药</td>\n", "    </tr>\n", "    <tr>\n", "      <th>389</th>\n", "      <td>283</td>\n", "      <td>常熟理工学院</td>\n", "      <td>理工</td>\n", "    </tr>\n", "    <tr>\n", "      <th>487</th>\n", "      <td>350</td>\n", "      <td>南昌工程学院</td>\n", "      <td>理工</td>\n", "    </tr>\n", "    <tr>\n", "      <th>529</th>\n", "      <td>376</td>\n", "      <td>西藏农牧学院</td>\n", "      <td>农业</td>\n", "    </tr>\n", "    <tr>\n", "      <th>530</th>\n", "      <td>376</td>\n", "      <td>内蒙古科技大学包头医学院</td>\n", "      <td>医药</td>\n", "    </tr>\n", "    <tr>\n", "      <th>608</th>\n", "      <td>438</td>\n", "      <td>吉林化工学院</td>\n", "      <td>理工</td>\n", "    </tr>\n", "    <tr>\n", "      <th>625</th>\n", "      <td>453</td>\n", "      <td>天水师范学院</td>\n", "      <td>师范</td>\n", "    </tr>\n", "    <tr>\n", "      <th>634</th>\n", "      <td>460</td>\n", "      <td>内蒙古科技大学包头师范学院</td>\n", "      <td>综合</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      排名           学校名称 学校类型\n", "261  200          新乡医学院   医药\n", "378  276          桂林医学院   医药\n", "389  283         常熟理工学院   理工\n", "487  350         南昌工程学院   理工\n", "529  376         西藏农牧学院   农业\n", "530  376   内蒙古科技大学包头医学院   医药\n", "608  438         吉林化工学院   理工\n", "625  453         天水师范学院   师范\n", "634  460  内蒙古科技大学包头师范学院   综合"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ruanke_df[~ruanke_df['学校名称'].isin(cn_school_2025_df['学校名称'])] # 为什么还多了几个学校...24->25改名了？？"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'内蒙古科技大学包头医学院',\n", " '内蒙古科技大学包头师范学院',\n", " '南昌工程学院',\n", " '吉林化工学院',\n", " '天水师范学院',\n", " '山西医科大学晋祠学院',\n", " '常熟理工学院',\n", " '新乡医学院',\n", " '新乡医学院三全学院',\n", " '桂林医学院',\n", " '湖南文理学院芙蓉学院',\n", " '湖南理工学院南湖学院',\n", " '绍兴文理学院元培学院',\n", " '西藏农牧学院'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["set(home_school['学校名称']) - set(cn_school_2025_df['学校名称'])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'乌鲁木齐职业大学',\n", " '九江科技职业大学',\n", " '兴安职业技术大学',\n", " '内蒙古建筑职业技术大学',\n", " '内蒙古警察学院',\n", " '北京科技职业大学',\n", " '吉林化工大学',\n", " '吉林铁道职业技术大学',\n", " '呼和浩特职业技术大学',\n", " '塔里木理工学院',\n", " '大湾区大学',\n", " '天水师范大学',\n", " '天津警察学院',\n", " '宁夏工商职业技术大学',\n", " '宁夏职业技术大学',\n", " '宁波东方理工大学',\n", " '宁波职业技术大学',\n", " '安徽第二医学院',\n", " '安徽职业技术大学',\n", " '山西医药学院',\n", " '山西文化旅游职业大学',\n", " '岳阳学院',\n", " '常德学院',\n", " '广州职业技术大学',\n", " '德宏师范学院',\n", " '成都航空职业技术大学',\n", " '扬州职业技术大学',\n", " '抚州医药学院',\n", " '拉萨师范学院',\n", " '新疆交通职业技术大学',\n", " '新疆工业学院',\n", " '新疆理工职业大学',\n", " '无锡职业技术大学',\n", " '晋中健康学院',\n", " '曲靖健康医学院',\n", " '杭州职业技术大学',\n", " '桂林医科大学',\n", " '桂林师范学院',\n", " '武威职业技术大学',\n", " '武汉职业技术大学',\n", " '江西水利电力大学',\n", " '河南医药大学',\n", " '海南洛桑旅游大学',\n", " '海南警察学院',\n", " '淄博职业技术大学',\n", " '深圳信息职业技术大学',\n", " '湖北三峡航空学院',\n", " '甘肃工业职业技术大学',\n", " '福建福耀科技大学',\n", " '绍兴理工学院',\n", " '芜湖职业技术大学',\n", " '苏州工学院',\n", " '苏州职业技术大学',\n", " '西安戏剧学院',\n", " '西藏农牧大学',\n", " '豫北医学院',\n", " '贵州轻工职业大学',\n", " '连云港师范学院',\n", " '郑州健康学院',\n", " '酒泉职业技术大学',\n", " '重庆工业职业技术大学',\n", " '铜仁职业技术大学',\n", " '长春职业技术大学',\n", " '长沙科技学院',\n", " '陕西农林职业技术大学',\n", " '陕西工业职业技术大学',\n", " '顺德职业技术大学',\n", " '黄河水利职业技术大学',\n", " '黎明职业大学'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["new2524 = set(cn_school_2025_df['学校名称']) - set(home_school['学校名称']) # 69所...\n", "new2524"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["False    69\n", "Name: count, dtype: int64"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.Series(list(new2524)).isin(ruanke_df['学校名称']).value_counts() # 新增院校没有拥有软科排名的"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'内蒙古科技大学包头医学院', '内蒙古科技大学包头师范学院'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["school_name_2425_mapping_dict = {\n", "    '南昌工程学院': '江西水利电力大学',\n", "    '吉林化工学院': '吉林化工大学',\n", "    '天水师范学院': '天水师范大学',\n", "    '山西医科大学晋祠学院': '晋中健康学院',\n", "    '常熟理工学院': '苏州工学院',\n", "    '新乡医学院': '河南医药大学',\n", "    '新乡医学院三全学院': '豫北医学院',\n", "    '桂林医学院': '桂林医科大学',\n", "    '湖南文理学院芙蓉学院': '常德学院',\n", "    '湖南理工学院南湖学院': '岳阳学院',\n", "    '绍兴文理学院元培学院': '绍兴理工学院',\n", "    '西藏农牧学院': '西藏农牧大学'\n", "}\n", "\n", "home_school['学校名称'] = home_school['学校名称'].map(school_name_2425_mapping_dict).fillna(home_school['学校名称'])\n", "set(home_school['学校名称']) - set(cn_school_2025_df['学校名称']) # 注意会多这两所...总数1365->1367"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "      <th>school_name_en</th>\n", "      <th>school_tier</th>\n", "      <th>gpa_requirements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>综合</td>\n", "      <td>4111010003</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>Tsinghua University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>综合</td>\n", "      <td>4111010001</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>Peking University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>综合</td>\n", "      <td>4133010335</td>\n", "      <td>杭州市</td>\n", "      <td>教育部</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>Zhejiang University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>综合</td>\n", "      <td>4131010248</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>复旦大学</td>\n", "      <td>综合</td>\n", "      <td>4131010246</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>Fudan University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1362</th>\n", "      <td>新疆工业学院</td>\n", "      <td>NaN</td>\n", "      <td>4165014897</td>\n", "      <td>和田地区</td>\n", "      <td>新疆生产建设兵团</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1363</th>\n", "      <td>乌鲁木齐职业大学</td>\n", "      <td>NaN</td>\n", "      <td>4165011565</td>\n", "      <td>乌鲁木齐市</td>\n", "      <td>新疆维吾尔自治区</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1364</th>\n", "      <td>新疆交通职业技术大学</td>\n", "      <td>NaN</td>\n", "      <td>4165013926</td>\n", "      <td>乌鲁木齐市</td>\n", "      <td>新疆维吾尔自治区</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1365</th>\n", "      <td>塔里木理工学院</td>\n", "      <td>NaN</td>\n", "      <td>4165014946</td>\n", "      <td>阿拉尔市</td>\n", "      <td>新疆生产建设兵团教育局</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1366</th>\n", "      <td>新疆理工职业大学</td>\n", "      <td>NaN</td>\n", "      <td>4165014947</td>\n", "      <td>喀什地区</td>\n", "      <td>新疆生产建设兵团</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1367 rows × 10 columns</p>\n", "</div>"], "text/plain": ["            学校名称 学校类型       学校标识码    所在地         主管部门  软科排名   备注  \\\n", "0           清华大学   综合  4111010003    北京市          教育部     1  NaN   \n", "1           北京大学   综合  4111010001    北京市          教育部     2  NaN   \n", "2           浙江大学   综合  4133010335    杭州市          教育部     3  NaN   \n", "3         上海交通大学   综合  4131010248    上海市          教育部     4  NaN   \n", "4           复旦大学   综合  4131010246    上海市          教育部     5  NaN   \n", "...          ...  ...         ...    ...          ...   ...  ...   \n", "1362      新疆工业学院  NaN  4165014897   和田地区     新疆生产建设兵团  <NA>  NaN   \n", "1363    乌鲁木齐职业大学  NaN  4165011565  乌鲁木齐市     新疆维吾尔自治区  <NA>  NaN   \n", "1364  新疆交通职业技术大学  NaN  4165013926  乌鲁木齐市     新疆维吾尔自治区  <NA>  NaN   \n", "1365     塔里木理工学院  NaN  4165014946   阿拉尔市  新疆生产建设兵团教育局  <NA>   民办   \n", "1366    新疆理工职业大学  NaN  4165014947   喀什地区     新疆生产建设兵团  <NA>  NaN   \n", "\n", "                     school_name_en school_tier  \\\n", "0               Tsinghua University         985   \n", "1                 Peking University         985   \n", "2               Zhejiang University         985   \n", "3     Shanghai Jiao Tong University         985   \n", "4                  Fudan University         985   \n", "...                             ...         ...   \n", "1362                            NaN         NaN   \n", "1363                            NaN         NaN   \n", "1364                            NaN         NaN   \n", "1365                            NaN         NaN   \n", "1366                            NaN         NaN   \n", "\n", "                                       gpa_requirements  \n", "0     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "1     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "2     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "3     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "4     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "...                                                 ...  \n", "1362                                                NaN  \n", "1363                                                NaN  \n", "1364                                                NaN  \n", "1365                                                NaN  \n", "1366                                                NaN  \n", "\n", "[1367 rows x 10 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 提取需要的列\n", "columns_to_keep = ['学校名称', '学校标识码', '主管部门', '所在地', '备注']\n", "cn_school_2025_filtered = cn_school_2025_df[columns_to_keep]\n", "\n", "# 找出不在 home_school 中的学校\n", "new_schools = cn_school_2025_filtered[~cn_school_2025_filtered['学校名称'].isin(home_school['学校名称'])]\n", "home_school_updated = pd.concat([home_school, new_schools], ignore_index=True)\n", "\n", "home_school_updated"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 985/211/双一流/中外合办标注 "]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 目前在后面英国院校list数据整理时进行的，但理应在此就提前完成。刚好还没标记单独的双一流院校，此处补上。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["double_first_school_list = [\n", "    \"中央美术学院\",\n", "    \"中央戏剧学院\",\n", "    \"上海音乐学院\",\n", "    \"中国美术学院\",\n", "    \"中国音乐学院\",\n", "    \"上海体育大学\",\n", "    \"北京协和医学院\",\n", "    \"南京医科大学\",\n", "    \"广州医科大学\",\n", "    \"南京中医药大学\",\n", "    \"上海中医药大学\",\n", "    \"广州中医药大学\",\n", "    \"天津中医药大学\",\n", "    \"成都中医药大学\",\n", "    \"南方科技大学\",\n", "    \"上海科技大学\",\n", "    \"南京邮电大学\",\n", "    \"南京信息工程大学\",\n", "    \"西南石油大学\",\n", "    \"成都理工大学\",\n", "    \"天津工业大学\",\n", "    \"宁波大学\",\n", "    \"河南大学\",\n", "    \"湘潭大学\",\n", "    \"山西大学\",\n", "    \"华南农业大学\",\n", "    \"南京林业大学\",\n", "    \"上海海洋大学\",\n", "    \"首都师范大学\",\n", "    \"外交学院\",\n", "    \"中国人民公安大学\",\n", "    \"中国科学院大学\"\n", "]\n", "\n", "set(double_first_school_list) - set(home_school_updated['学校名称']) # 确认所有学校名都在教育部名单中"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["school_tier\n", "211     74\n", "985     38\n", "双一流     32\n", "中外合办     6\n", "Name: count, dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["home_school_updated.loc[\n", "    home_school_updated['学校名称'].isin(double_first_school_list),\n", "    'school_tier'\n", "] = '双一流'\n", "\n", "home_school_updated.school_tier.value_counts() \n", "\n", "# 39所985，76所纯211，32所纯双一流，147所双一流院校\n", "# 不包含军校：国防科技大学、空军军医大学、海军军医大学"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "      <th>school_name_en</th>\n", "      <th>school_tier</th>\n", "      <th>gpa_requirements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>香港中文大学（深圳）</td>\n", "      <td>综合</td>\n", "      <td>4144016407</td>\n", "      <td>深圳市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>45</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>The Chinese University of Hong Kong, Shenzhen</td>\n", "      <td>中外合办</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>上海纽约大学</td>\n", "      <td>综合</td>\n", "      <td>4131016404</td>\n", "      <td>上海市</td>\n", "      <td>上海市教委</td>\n", "      <td>73</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>New York University Shanghai</td>\n", "      <td>中外合办</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>宁波诺丁汉大学</td>\n", "      <td>综合</td>\n", "      <td>4133016301</td>\n", "      <td>宁波市</td>\n", "      <td>浙江省教育厅</td>\n", "      <td>76</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>University of Nottingham Ningbo China</td>\n", "      <td>中外合办</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>昆山杜克大学</td>\n", "      <td>综合</td>\n", "      <td>4132016406</td>\n", "      <td>昆山市</td>\n", "      <td>江苏省教育厅</td>\n", "      <td>103</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>NaN</td>\n", "      <td>中外合办</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131</th>\n", "      <td>西交利物浦大学</td>\n", "      <td>综合</td>\n", "      <td>4132016403</td>\n", "      <td>苏州市</td>\n", "      <td>江苏省教育厅</td>\n", "      <td>103</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td><PERSON><PERSON><PERSON>-Liverpool University</td>\n", "      <td>中外合办</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>深圳北理莫斯科大学</td>\n", "      <td>综合</td>\n", "      <td>4144016409</td>\n", "      <td>深圳市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>106</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>NaN</td>\n", "      <td>中外合办</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>北京师范大学-香港浸会大学联合国际学院</td>\n", "      <td>综合</td>\n", "      <td>4144016401</td>\n", "      <td>珠海市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>124</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>Beijing Normal University-Hong Kong Baptist Un...</td>\n", "      <td>中外合办</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>温州肯恩大学</td>\n", "      <td>综合</td>\n", "      <td>4133016405</td>\n", "      <td>温州市</td>\n", "      <td>浙江省教育厅</td>\n", "      <td>151</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>Wenzhou-Kean University</td>\n", "      <td>中外合办</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>972</th>\n", "      <td>广东以色列理工学院</td>\n", "      <td>NaN</td>\n", "      <td>4144016410</td>\n", "      <td>汕头市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>NaN</td>\n", "      <td>中外合办</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1295</th>\n", "      <td>香港城市大学（东莞）</td>\n", "      <td>NaN</td>\n", "      <td>4144014851</td>\n", "      <td>东莞市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>NaN</td>\n", "      <td>中外合办</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1296</th>\n", "      <td>香港科技大学（广州）</td>\n", "      <td>NaN</td>\n", "      <td>4144016412</td>\n", "      <td>广州市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "      <td>NaN</td>\n", "      <td>中外合办</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     学校名称 学校类型       学校标识码  所在地    主管部门  软科排名  \\\n", "49             香港中文大学（深圳）   综合  4144016407  深圳市  广东省教育厅    45   \n", "85                 上海纽约大学   综合  4131016404  上海市   上海市教委    73   \n", "91                宁波诺丁汉大学   综合  4133016301  宁波市  浙江省教育厅    76   \n", "130                昆山杜克大学   综合  4132016406  昆山市  江苏省教育厅   103   \n", "131               西交利物浦大学   综合  4132016403  苏州市  江苏省教育厅   103   \n", "137             深圳北理莫斯科大学   综合  4144016409  深圳市  广东省教育厅   106   \n", "162   北京师范大学-香港浸会大学联合国际学院   综合  4144016401  珠海市  广东省教育厅   124   \n", "202                温州肯恩大学   综合  4133016405  温州市  浙江省教育厅   151   \n", "972             广东以色列理工学院  NaN  4144016410  汕头市  广东省教育厅  <NA>   \n", "1295           香港城市大学（东莞）  NaN  4144014851  东莞市  广东省教育厅  <NA>   \n", "1296           香港科技大学（广州）  NaN  4144016412  广州市  广东省教育厅  <NA>   \n", "\n", "                    备注                                     school_name_en  \\\n", "49    中外合作办学及内地与港澳合作办学      The Chinese University of Hong Kong, Shenzhen   \n", "85    中外合作办学及内地与港澳合作办学                       New York University Shanghai   \n", "91    中外合作办学及内地与港澳合作办学              University of Nottingham Ningbo China   \n", "130   中外合作办学及内地与港澳合作办学                                                NaN   \n", "131   中外合作办学及内地与港澳合作办学                Xi'an Jiaotong-Liverpool University   \n", "137   中外合作办学及内地与港澳合作办学                                                NaN   \n", "162   中外合作办学及内地与港澳合作办学  Beijing Normal University-Hong Kong Baptist Un...   \n", "202   中外合作办学及内地与港澳合作办学                            Wenzhou-Kean University   \n", "972   中外合作办学及内地与港澳合作办学                                                NaN   \n", "1295  中外合作办学及内地与港澳合作办学                                                NaN   \n", "1296  中外合作办学及内地与港澳合作办学                                                NaN   \n", "\n", "     school_tier                                   gpa_requirements  \n", "49          中外合办  {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...  \n", "85          中外合办  {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...  \n", "91          中外合办  {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...  \n", "130         中外合办                                                NaN  \n", "131         中外合办  {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...  \n", "137         中外合办                                                NaN  \n", "162         中外合办  {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...  \n", "202         中外合办  {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 88, '伦敦大学学院...  \n", "972         中外合办                                                NaN  \n", "1295        中外合办                                                NaN  \n", "1296        中外合办                                                NaN  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 筛选出“备注”列中不是空值的行\n", "non_nan_indices = home_school_updated['备注'].notna()\n", "\n", "# 在这些行中进一步筛选出包含“中外合作”的行，并更新“school_tier”列为“中外合办”\n", "indices_to_update = home_school_updated.loc[non_nan_indices & home_school_updated['备注'].str.contains('中外合作')].index\n", "home_school_updated.loc[indices_to_update, 'school_tier'] = '中外合办'\n", "home_school_updated.loc[indices_to_update]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["school_tier\n", "211     74\n", "985     38\n", "双一流     32\n", "中外合办    11\n", "Name: count, dtype: int64"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["home_school_updated.school_tier.value_counts() \n", "\n", "# 中外合办11所"]}, {"cell_type": "markdown", "metadata": {}, "source": ["教育部高校名单中不含军校，因此985少一所国防科大，211少两所军医大学"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# home_school_updated.drop(columns=['gpa_requirements']).to_csv('境内院校数据库.csv', index=False, encoding='utf-8-sig')\n", "# home_school_updated[['学校名称', 'school_name_en', '软科排名', 'school_tier']].to_csv('院校数据/国内院校标准对照表.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3 境外院校list数据处理时的校名匹配对齐流程示例"]}, {"cell_type": "markdown", "metadata": {}, "source": ["完全依照境内院校数据库衍生的`国内院校标准对照表.csv` 进行校名匹配对齐，故而在此处展示"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 1. 读取校名标准参考及映射字典"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>school_name_en</th>\n", "      <th>软科排名</th>\n", "      <th>school_tier</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>Tsinghua University</td>\n", "      <td>1</td>\n", "      <td>985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>Peking University</td>\n", "      <td>2</td>\n", "      <td>985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>Zhejiang University</td>\n", "      <td>3</td>\n", "      <td>985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>4</td>\n", "      <td>985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>复旦大学</td>\n", "      <td>Fudan University</td>\n", "      <td>5</td>\n", "      <td>985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1362</th>\n", "      <td>新疆工业学院</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1363</th>\n", "      <td>乌鲁木齐职业大学</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1364</th>\n", "      <td>新疆交通职业技术大学</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1365</th>\n", "      <td>塔里木理工学院</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1366</th>\n", "      <td>新疆理工职业大学</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1367 rows × 4 columns</p>\n", "</div>"], "text/plain": ["            学校名称                 school_name_en  软科排名 school_tier\n", "0           清华大学            Tsinghua University     1         985\n", "1           北京大学              Peking University     2         985\n", "2           浙江大学            Zhejiang University     3         985\n", "3         上海交通大学  Shanghai Jiao Tong University     4         985\n", "4           复旦大学               Fudan University     5         985\n", "...          ...                            ...   ...         ...\n", "1362      新疆工业学院                            NaN  <NA>         NaN\n", "1363    乌鲁木齐职业大学                            NaN  <NA>         NaN\n", "1364  新疆交通职业技术大学                            NaN  <NA>         NaN\n", "1365     塔里木理工学院                            NaN  <NA>         NaN\n", "1366    新疆理工职业大学                            NaN  <NA>         NaN\n", "\n", "[1367 rows x 4 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["school_reference_df = pd.read_csv('院校数据/院校List/国内院校标准对照表.csv', dtype={'软科排名': 'Int64'})\n", "\n", "# 读取 JSON 文件并将其转换为字典\n", "with open('院校数据/院校List/school_name_mapping.json', 'r', encoding='utf-8') as f:\n", "    school_name_mapping_dict = json.load(f)\n", "\n", "school_reference_df "]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 2. 先将相关list整理为一个可以直接由pandas处理的形式并读入, 并把学校名称列改名为标准列名（国内院校标准对照表中的中英文列名）"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>Band</th>\n", "      <th>Project 985</th>\n", "      <th>Project 211</th>\n", "      <th>格式标准化分数要求</th>\n", "      <th>原始分数要求</th>\n", "      <th>Double First-Class University/Specialist Institution*</th>\n", "      <th>Double First Subjects</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>中国人民解放军空军军医大学（第四军医大学）</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>安徽农业大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>安徽中医药大学</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>安徽医科大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>安徽师范大学</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>808</th>\n", "      <td>浙江外国语学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>809</th>\n", "      <td>中华女子学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>810</th>\n", "      <td>呼和浩特民族学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>811</th>\n", "      <td>湖南女子学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>812</th>\n", "      <td>青海民族大学</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>813 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                      学校名称 Band Project 985 Project 211  \\\n", "0    中国人民解放军空军军医大学（第四军医大学）    C          No         Yes   \n", "1                   安徽农业大学    D          No          No   \n", "2                  安徽中医药大学    E          No          No   \n", "3                   安徽医科大学    D          No          No   \n", "4                   安徽师范大学    C          No          No   \n", "..                     ...  ...         ...         ...   \n", "808                浙江外国语学院    E          No          No   \n", "809                 中华女子学院    E          No          No   \n", "810               呼和浩特民族学院    E          No          No   \n", "811                 湖南女子学院    E          No          No   \n", "812                 青海民族大学    E          No          No   \n", "\n", "                 格式标准化分数要求                 原始分数要求  \\\n", "0    Standard: minimum 77%  Standard: minimum 77%   \n", "1    Standard: minimum 82%  Standard: minimum 82%   \n", "2    Standard: minimum 85%  Standard: minimum 85%   \n", "3    Standard: minimum 82%  Standard: minimum 82%   \n", "4    Standard: minimum 77%  Standard: minimum 77%   \n", "..                     ...                    ...   \n", "808  Standard: minimum 85%  Standard: minimum 85%   \n", "809  Standard: minimum 85%  Standard: minimum 85%   \n", "810  Standard: minimum 85%  Standard: minimum 85%   \n", "811  Standard: minimum 85%  Standard: minimum 85%   \n", "812  Standard: minimum 85%  Standard: minimum 85%   \n", "\n", "    Double First-Class University/Specialist Institution*  \\\n", "0                                                   No      \n", "1                                                   No      \n", "2                                                   No      \n", "3                                                   No      \n", "4                                                   No      \n", "..                                                 ...      \n", "808                                                 No      \n", "809                                                 No      \n", "810                                                 No      \n", "811                                                 No      \n", "812                                                 No      \n", "\n", "    Double First Subjects  \n", "0                     NaN  \n", "1                     NaN  \n", "2                     NaN  \n", "3                     NaN  \n", "4                     NaN  \n", "..                    ...  \n", "808                   NaN  \n", "809                   NaN  \n", "810                   NaN  \n", "811                   NaN  \n", "812                   NaN  \n", "\n", "[813 rows x 8 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取Excel文件\n", "excel_file_path = '院校数据/院校List/圣安List初始整理版.xlsx'\n", "school_list_df = pd.read_excel(excel_file_path)\n", "\n", "# 将 Chinese name 列改名为 “学校名称”\n", "school_list_df.rename(columns={'Chinese name': '学校名称'}, inplace=True)\n", "school_list_df\n", "\n", "# 如果要使用英文名匹配则也要把英文名列改名...\n", "# school_list_df.rename(columns={'xxxx': 'school_name_en'}, inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 3. list去重：先总体去重，再对基准字段（比如学校中文名）核查情况并根据具体情况去重..."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>Band</th>\n", "      <th>Project 985</th>\n", "      <th>Project 211</th>\n", "      <th>格式标准化分数要求</th>\n", "      <th>原始分数要求</th>\n", "      <th>Double First-Class University/Specialist Institution*</th>\n", "      <th>Double First Subjects</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>河南农业大学</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>河南农业大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>辽宁工程技术大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>辽宁工程技术大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>269</th>\n", "      <td>山东航空学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>507</th>\n", "      <td>山东航空学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         学校名称 Band Project 985 Project 211              格式标准化分数要求  \\\n", "137    河南农业大学    C          No          No  Standard: minimum 77%   \n", "149    河南农业大学    D          No          No  Standard: minimum 82%   \n", "201  辽宁工程技术大学    D          No          No  Standard: minimum 82%   \n", "206  辽宁工程技术大学    D          No          No  Standard: minimum 82%   \n", "269    山东航空学院    E          No          No  Standard: minimum 85%   \n", "507    山东航空学院    E          No          No  Standard: minimum 85%   \n", "\n", "                    原始分数要求  \\\n", "137  Standard: minimum 77%   \n", "149  Standard: minimum 82%   \n", "201  Standard: minimum 82%   \n", "206  Standard: minimum 82%   \n", "269  Standard: minimum 85%   \n", "507  Standard: minimum 85%   \n", "\n", "    Double First-Class University/Specialist Institution*  \\\n", "137                                                 No      \n", "149                                                 No      \n", "201                                                NaN      \n", "206                                                 No      \n", "269                                                NaN      \n", "507                                                 No      \n", "\n", "    Double First Subjects  \n", "137                   NaN  \n", "149                   NaN  \n", "201                   NaN  \n", "206                   NaN  \n", "269                   NaN  \n", "507                   NaN  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["school_list_df.drop_duplicates(inplace=True)\n", "school_list_df[school_list_df.duplicated(subset=['学校名称'], keep=False)] # 河南农业大学确实出现了两次，无法判断...先直接去重了吧"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["学校名称\n", "北京警察学院                   1\n", "山东政法学院                   1\n", "中央司法警官学院                 1\n", "广东警官学院                   1\n", "重庆警察学院                   1\n", "                        ..\n", "安徽师范大学                   1\n", "安徽医科大学                   1\n", "安徽中医药大学                  1\n", "安徽农业大学                   1\n", "中国人民解放军空军军医大学（第四军医大学）    1\n", "Name: count, Length: 800, dtype: int64"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["school_list_df.drop_duplicates(subset=['学校名称'], inplace=True)\n", "school_list_df['学校名称'].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 4. 核查在list名单中，但没有在国内院校标准对照表中的学校"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15个院校名出现在大学List中，但没有出现在国内院校标准对照表中\n", "Found in mapping: {'吉林化工学院', '新乡医学院', '西藏农牧学院', '天水师范学院', '常熟理工学院', '桂林医学院'}\n", "Not found in mapping:\n"]}, {"data": {"text/plain": ["{'上海电机大学',\n", " '中国人民解放军国防科技大学（曾用名：国防科技大学）',\n", " '中国人民解放军海军军医大学（第二军医大学）',\n", " '中国人民解放军空军军医大学（第四军医大学）',\n", " '中国地质大学',\n", " '中国石油大学',\n", " '内蒙古科技大学包头师...',\n", " '北京航空航天大学北海学院（已于2013年停止招生）',\n", " '广西师范学院'}"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["missing_schools = set(school_list_df['学校名称']) - set(school_reference_df['学校名称'])\n", "print(f'{len(missing_schools)}个院校名出现在大学List中，但没有出现在国内院校标准对照表中')\n", "\n", "# fuzz_matched_results = {}\n", "# for name in missing_schools:\n", "#     best_match, _ = process.extractOne(name, set(school_reference_df['学校名称']))\n", "#     fuzz_matched_results[name] = best_match\n", "\n", "# fuzz_matched_results\n", "\n", "# 检测 missing_schools 中哪些在学校名称映射字典的键中\n", "found_in_mapping = missing_schools.intersection(school_name_mapping_dict.keys())\n", "not_found_in_mapping = missing_schools.difference(school_name_mapping_dict.keys())\n", "\n", "print(\"Found in mapping:\", found_in_mapping)\n", "# print(\"Not found in mapping:\", not_found_in_mapping)\n", "\n", "# 替换 school_list_df 中对应的 '学校名称' 列的值\n", "school_list_df['学校名称'] = school_list_df['学校名称'].apply(\n", "    lambda x: school_name_mapping_dict[x] if x in school_name_mapping_dict else x\n", ")\n", "\n", "print(f\"Not found in mapping:\")\n", "\n", "set(school_list_df['学校名称']) - set(school_reference_df['学校名称'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 下方字典已是在自动匹配基础上手动修改过的，但仍存在部分学校未匹配到\n", "school_name_mapping_dict = {\n", "    '湖州师范大学': '湖州师范学院',\n", "    '牡丹江医学院': '牡丹江医科大学',\n", "    '贵州财经大学商务学院': '贵州黔南经济学院',\n", "    '海南医学院': '海南医科大学',\n", "    '四川外国语大学成都学院': '成都外国语学院',\n", "    '桂林电子科技大学信息科技学院': '桂林信息科技学院',\n", "    '桂林理工大学博文管理学院': '南宁理工学院',\n", "    '江苏警官学校': '江苏警官学院',\n", "    '淮北师范大学信息学院': '淮北理工学院',\n", "    '西北师范大学知行学院': '兰州石化职业技术大学',\n", "    '赣南医学院': '赣南医科大学',\n", "    '中国矿业大学银川学院': '银川科技学院',\n", "    '福建工程学院': '福建理工大学',\n", "    '浙江海洋大学东海科学技术学院': '浙江药科职业大学',\n", "    '兰州财经大学长青学院': '兰州资源环境职业技术大学',\n", "    '北京工商大学嘉华学院': '北京金融科技学院',\n", "    '上海体育学院': '上海体育大学',\n", "    '香港中文大学': '香港中文大学（深圳）',\n", "    '合肥学院': '合肥大学',\n", "    '潍坊医学院': '山东第二医科大学',\n", "    '东莞理工学院城市学院': '东莞城市学院',\n", "    '广西中医药大学塞恩斯新医药学院': '广西中医药大学赛恩斯新医药学院',\n", "    '河南科技学院新科学院': '新乡工程学院',\n", "    '滨州学院': '山东航空学院',\n", "    '云南师范大学商学院': '昆明城市学院',\n", "    '重庆科技学院': '重庆科技大学',\n", "    '信阳师范学院': '信阳师范大学',\n", "    '江苏森林警察学院': '南京警察学院',\n", "    '山西师范大学现代文理学院': '山西电子科技学院',\n", "    '西南科技大学城市学院': '绵阳城市学院',\n", "    '河北中医学院': '河北中医药大学',\n", "    '阜阳师范大学信息工程学院': '阜阳理工学院',\n", "    '吉首大学张家界学院': '张家界学院',\n", "    '太原科技大学华科学院': '山西科技学院',\n", "    '铁道警察学院': '郑州警察学院',\n", "    '广西师范大学漓江学院': '桂林学院',\n", "    '贵州民族大学人文科技学院': '贵阳人文科技学院',\n", "    '宁夏师范学院': '宁夏师范大学',\n", "    '浙江科技学院': '浙江科技大学',\n", "    '长江大学工程技术学院': '荆州学院',\n", "    '济南大学泉城学院': '烟台科技学院',\n", "    '广西大学行健文理学院': '广西农业职业技术大学',\n", "    '北京电影学院现代创意媒体学院': '青岛电影学院',\n", "    '嘉兴学院': '嘉兴大学',\n", "    '云南大学滇池学院': '滇池学院',\n", "    '云南艺术学院文化学院': '昆明传媒学院',\n", "    '安徽师范大学皖江学院': '芜湖学院',\n", "    '蚌埠医学院': '蚌埠医科大学',\n", "    '佛山科学技术学院': '佛山大学',\n", "    '贵州师范大学求是学院': '贵阳康养职业大学',\n", "    '陕西国际贸易商贸学院': '陕西国际商贸学院',\n", "    '中国社会科学院': '中国社会科学院大学',\n", "    '广东工业大学华立学院': '广州华立学院',\n", "    '四川大学锦城学院': '成都锦城学院',\n", "    '贵州大学明德学院': '贵阳信息科技学院'\n", "}\n", "\n", "# school_name_2425_mapping_dict = {\n", "#     '南昌工程学院': '江西水利电力大学',\n", "#     '吉林化工学院': '吉林化工大学',\n", "#     '天水师范学院': '天水师范大学',\n", "#     '山西医科大学晋祠学院': '晋中健康学院',\n", "#     '常熟理工学院': '苏州工学院',\n", "#     '新乡医学院': '河南医药大学',\n", "#     '新乡医学院三全学院': '豫北医学院',\n", "#     '桂林医学院': '桂林医科大学',\n", "#     '湖南文理学院芙蓉学院': '常德学院',\n", "#     '湖南理工学院南湖学院': '岳阳学院',\n", "#     '绍兴文理学院元培学院': '绍兴理工学院',\n", "#     '西藏农牧学院': '西藏农牧大学',\n", "# }\n", "\n", "# with open('院校数据/院校List/school_name_mapping.json', 'w') as f:\n", "#     json.dump(school_name_mapping_dict, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ~~院校List及GPA要求数据处理~~ （不再使用，仅作参考）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> TIPS: 以下List数据处理都是基于一个预先初步整理好的“英国大学录取分数要求（初始版）.xlsx”进行的，是学校层面的gpa要求，后续具体到项目层面的gpa要求不再采用下面的处理。不过相关流程和处理代码可以参考，尤其是相关学校名的映射字典"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["home_school_df = pd.read_csv('院校数据/境内院校数据库（初始无list版）.csv', dtype={'学校标识码': 'str', '软科排名': 'Int64'})\n", "# aboard_school_df = pd.read_csv('境外院校数据库.csv', dtype={'school_usnews_rank': 'str'})\n", "\n", "uk_school_gpa_df = pd.read_excel('院校数据/英国大学录取分数要求（初始版）.xlsx')\n", "# 将这些列的数据类型转换为Int64\n", "all_columns = uk_school_gpa_df.columns\n", "columns_to_convert = [col for col in all_columns if col not in ['school_name_cn', 'school_name_en']]\n", "for col in columns_to_convert:\n", "    uk_school_gpa_df[col] = uk_school_gpa_df[col].astype('Int64')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 去重"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["去重前院校数量：1226\n", "去重后院校数量：1219\n", "中文学校名唯一值数量：1216\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>伦敦政治经济学院</th>\n", "      <th>华威大学</th>\n", "      <th>伯明翰大学</th>\n", "      <th>格拉斯哥大学</th>\n", "      <th>利兹大学</th>\n", "      <th>南安普顿大学</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>中国地质大学</td>\n", "      <td>China University of Geosciences (Wuhan)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>73</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>中国石油大学</td>\n", "      <td>China University of Petroleum (Beijing)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>73</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>中国矿业大学</td>\n", "      <td>China University of Mining and Technology (Xuz...</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>73</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>中国地质大学</td>\n", "      <td>China University of Geosciences (Beijing)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>73</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>中国石油大学</td>\n", "      <td>China University of Petroleum (Huadong) or (Ea...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>73</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>中国矿业大学</td>\n", "      <td>China University of Mining and Technology - Be...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>73</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   school_name_cn                                     school_name_en  牛津大学  \\\n", "54         中国地质大学            China University of Geosciences (Wuhan)    85   \n", "65         中国石油大学            China University of Petroleum (Beijing)    85   \n", "69         中国矿业大学  China University of Mining and Technology (Xuz...    85   \n", "74         中国地质大学          China University of Geosciences (Beijing)    85   \n", "80         中国石油大学  China University of Petroleum (Huadong) or (Ea...    90   \n", "83         中国矿业大学  China University of Mining and Technology - Be...    90   \n", "\n", "    剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  伦敦政治经济学院  华威大学  伯明翰大学  格拉斯哥大学  \\\n", "54    85      85        85     85      85        90    82     73      75   \n", "65    85      85        85     85      85        90    82     73      75   \n", "69    85      85        85     85      85        90    82     73      75   \n", "74    85      85        85     85      85        90    82     73      75   \n", "80    90      90        85     85      85        90    82     73      75   \n", "83    90      90        85     85      85        90    82     73      75   \n", "\n", "    利兹大学  南安普顿大学  谢菲尔德大学  杜伦大学  诺丁汉大学  巴斯大学  埃克塞特大学  贝尔法斯特女王大学  \n", "54    75      78      73    80     75    75      75         75  \n", "65    75      78      73    80     75    75      75         75  \n", "69    75      78      73    80     75    75      75         75  \n", "74    75      78      73    80     75    75      75         75  \n", "80    75      78      73    80     75    75      75         80  \n", "83    75      78      73    80     75    75      75         75  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["print(f'去重前院校数量：{len(uk_school_gpa_df)}')\n", "uk_school_gpa_df.drop_duplicates(inplace=True)\n", "print(f'去重后院校数量：{len(uk_school_gpa_df)}')\n", "print(f'中文学校名唯一值数量：{len(uk_school_gpa_df['school_name_cn'].unique())}') # 仍然大于去重后院校数量，需要进一步处理...\n", "\n", "# 检测school_name_cn字段重复的行\n", "uk_school_gpa_df[uk_school_gpa_df.duplicated(subset='school_name_cn', keep=False)]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["school_name_cn字段重复的行:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>伦敦政治经济学院</th>\n", "      <th>华威大学</th>\n", "      <th>伯明翰大学</th>\n", "      <th>格拉斯哥大学</th>\n", "      <th>利兹大学</th>\n", "      <th>南安普顿大学</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [school_name_cn, school_name_en, 牛津大学, 剑桥大学, 伦敦大学学院, 伦敦大学国王学院, 爱丁堡大学, 曼彻斯特大学, 伦敦政治经济学院, 华威大学, 伯明翰大学, 格拉斯哥大学, 利兹大学, 南安普顿大学, 谢菲尔德大学, 杜伦大学, 诺丁汉大学, 巴斯大学, 埃克塞特大学, 贝尔法斯特女王大学]\n", "Index: []"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定义映射字典\n", "mapping_dict = {'China University of Geosciences (Wuhan)':'中国地质大学（武汉）',\n", " 'China University of Geosciences (Beijing)': '中国地质大学（北京）',\n", " 'China University of Mining and Technology - Beijing':'中国矿业大学（北京）',\n", " 'China University of Mining and Technology (Xuzhou)':'中国矿业大学',\n", " 'China University of Petroleum (Huadong) or (East China)':'中国石油大学（华东）',\n", " 'China University of Petroleum (Beijing)':'中国石油大学（北京）'\n", " }\n", "\n", "# 更新school_name_cn字段\n", "uk_school_gpa_df['school_name_cn'] = uk_school_gpa_df['school_name_en'].map(mapping_dict).fillna(uk_school_gpa_df['school_name_cn'])\n", "\n", "# 检测school_name_cn字段重复的行\n", "duplicates_by_school_name = uk_school_gpa_df[uk_school_gpa_df.duplicated(subset='school_name_cn', keep=False)]\n", "\n", "# 列出所有school_name_cn字段重复的行\n", "print(\"school_name_cn字段重复的行:\")\n", "duplicates_by_school_name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 院校名匹配"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["63个院校名出现在GPA要求名单中，但没有出现在教育部全国普通高等学校名单中\n", "{'潍坊医学院', '信阳师范学院', '滨州学院', '东北大学秦皇岛分校', '北京工商大学嘉华学院', '云南艺术学院文化学院', '上海体育学院', '淮北师范大学信息学院', '广西师范大学漓江学院', '桂林理工大学博文管理学院', '南京师范大学师园学院', '中国矿业大学银川学院', '东莞理工学院城市学院', '浙江科技学院', '广西中医药大学塞恩斯新医药学院', '贵州师范大学求是学院', '河北中医学院', '西南科技大学城市学院', '赣南医学院', '贵州民族大学人文科技学院', '桂林电子科技大学信息科技学院', '阜阳师范大学信息工程学院', '云南大学滇池学院', '西北师范大学知行学院', '长江大学工程技术学院', '中国农业大学国际学院', '太原科技大学华科学院', '蚌埠医学院', '佛山科学技术学院', '重庆科技学院', '海南医学院', '广东工业大学华立学院', '贵州财经大学商务学院', '福建工程学院', '北京电影学院现代创意媒体学院', '中国科学院研究生院', '牡丹江医学院', '吉林大学珠海学院', '四川外国语大学成都学院', '宁夏师范学院', '嘉兴学院', '合肥学院', '苏州大学东吴商学院', '云南师范大学商学院', '江苏森林警察学院', '兰州财经大学长青学院', '四川大学锦城学院', '浙江海洋大学东海科学技术学院', '北京师范大学-香港浸会大学', '济南大学泉城学院', '中国社会科学院', '陕西国际贸易商贸学院', '河南科技学院新科学院', '山西师范大学现代文理学院', '安徽师范大学皖江学院', '香港中文大学', '湖州师范大学', '江苏警官学校', '贵州大学明德学院', '辽宁大学新华国际商学院', '吉首大学张家界学院', '铁道警察学院', '广西大学行健文理学院'}\n"]}], "source": ["missing_schools_from_gpa = set(uk_school_gpa_df['school_name_cn']) - set(home_school_df['学校名称'])\n", "print(f'{len(missing_schools_from_gpa)}个院校名出现在GPA要求名单中，但没有出现在教育部全国普通高等学校名单中')\n", "print(missing_schools_from_gpa)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'潍坊医学院': '潍坊学院',\n", " '信阳师范学院': '咸阳师范学院',\n", " '滨州学院': '滨州医学院',\n", " '东北大学秦皇岛分校': '东北大学',\n", " '北京工商大学嘉华学院': '北京工商大学',\n", " '云南艺术学院文化学院': '云南艺术学院',\n", " '上海体育学院': '上海体育大学',\n", " '淮北师范大学信息学院': '淮北师范大学',\n", " '广西师范大学漓江学院': '广西师范大学',\n", " '桂林理工大学博文管理学院': '桂林理工大学',\n", " '南京师范大学师园学院': '南宁师范大学师园学院',\n", " '中国矿业大学银川学院': '中国矿业大学',\n", " '东莞理工学院城市学院': '东莞理工学院',\n", " '浙江科技学院': '江西科技学院',\n", " '广西中医药大学塞恩斯新医药学院': '广西中医药大学赛恩斯新医药学院',\n", " '贵州师范大学求是学院': '贵州师范大学',\n", " '河北中医学院': '河北中医药大学',\n", " '西南科技大学城市学院': '西南科技大学',\n", " '赣南医学院': '南昌医学院',\n", " '贵州民族大学人文科技学院': '贵州民族大学',\n", " '桂林电子科技大学信息科技学院': '桂林电子科技大学',\n", " '阜阳师范大学信息工程学院': '阜阳师范大学',\n", " '云南大学滇池学院': '云南大学',\n", " '西北师范大学知行学院': '西北师范大学',\n", " '长江大学工程技术学院': '长江大学',\n", " '中国农业大学国际学院': '中国农业大学',\n", " '太原科技大学华科学院': '太原科技大学',\n", " '蚌埠医学院': '蚌埠学院',\n", " '佛山科学技术学院': '江西师范大学科学技术学院',\n", " '重庆科技学院': '重庆人文科技学院',\n", " '海南医学院': '南昌医学院',\n", " '广东工业大学华立学院': '广东工业大学',\n", " '贵州财经大学商务学院': '贵州财经大学',\n", " '福建工程学院': '福建商学院',\n", " '北京电影学院现代创意媒体学院': '北京电影学院',\n", " '中国科学院研究生院': '中国药科大学',\n", " '牡丹江医学院': '牡丹江师范学院',\n", " '吉林大学珠海学院': '吉林大学',\n", " '四川外国语大学成都学院': '四川外国语大学',\n", " '宁夏师范学院': '宁德师范学院',\n", " '嘉兴学院': '嘉兴南湖学院',\n", " '合肥学院': '合肥理工学院',\n", " '苏州大学东吴商学院': '苏州大学',\n", " '云南师范大学商学院': '云南师范大学',\n", " '江苏森林警察学院': '江苏警官学院',\n", " '兰州财经大学长青学院': '兰州财经大学',\n", " '四川大学锦城学院': '四川大学',\n", " '浙江海洋大学东海科学技术学院': '浙江海洋大学',\n", " '北京师范大学-香港浸会大学': '北京师范大学',\n", " '济南大学泉城学院': '济南大学',\n", " '中国社会科学院': '中国社会科学院大学',\n", " '陕西国际贸易商贸学院': '陕西国际商贸学院',\n", " '河南科技学院新科学院': '河南科技学院',\n", " '山西师范大学现代文理学院': '山西师范大学',\n", " '安徽师范大学皖江学院': '安徽师范大学',\n", " '香港中文大学': '香港中文大学（深圳）',\n", " '湖州师范大学': '湖州师范学院',\n", " '江苏警官学校': '江苏警官学院',\n", " '贵州大学明德学院': '贵州大学',\n", " '辽宁大学新华国际商学院': '辽宁大学',\n", " '吉首大学张家界学院': '张家界学院',\n", " '铁道警察学院': '重庆警察学院',\n", " '广西大学行健文理学院': '广西大学'}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 从教育部全国普通高等学校名单的学校名称中模糊匹配每个未找到的GPA名单中的学校名称\n", "fuzz_matched_results = {}\n", "for name in missing_schools_from_gpa:\n", "    best_match, _ = process.extractOne(name, set(home_school_df['学校名称']))\n", "    fuzz_matched_results[name] = best_match\n", "\n", "fuzz_matched_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 人工核对匹配结果并建立映射字典，注意很多独立院校被匹配到了原挂靠学校，但现在改制后很多改名了得去进一步check，比如 四川大学锦城学院 改成了 成都锦城学院。最后没有被匹配上的学校记录一下...\n", ">\n", "> 个别备注：同时有'北京师范大学-香港浸会大学' 和 '北京师范大学-香港浸会大学联合国际学院'...而且分数要求不一样...因此先都保留，不将'北京师范大学-香港浸会大学'匹配到'北京师范大学-香港浸会大学联合国际学院'。类似的还有'东北大学秦皇岛分校': '东北大学'，分校不是校区，录取要求不一样...因此gpa名单里两个都有，但教育部名单那里只有一个...后续看是否加在教育部名单里吧直接。然后'中国科学院研究生院'可以匹配到国科大，但是看了眼list里的分数明显不对，太高了...先不管吧以后再改\n", ">\n", "> 注意改名后也要再次检查school_name_cn字段是否有重复...不然比如上面两个学校改名后就会和本校重复...一个中文名对应两个list不行...。比如检查后就发现，'吉林大学珠海学院': '珠海科技学院'这个替换会导致重复，原本GPA清单中就有个珠海科技学院了...而且分数还不一致..只有先从dict里去掉了..."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 好像这些神奇的都是埃克塞特大学list里的...\n", "no_matching_schools = ['辽宁大学新华国际商学院', '苏州大学东吴商学院', '南京师范大学师园学院', '中国农业大学国际学院'] \n", "special_schools = ['北京师范大学-香港浸会大学', '东北大学秦皇岛分校', '中国科学院研究生院', '吉林大学珠海学院']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 下方字典已是在自动匹配基础上手动修改过的，但仍存在部分学校未匹配到\n", "school_name_mapping_dict = {\n", "    '湖州师范大学': '湖州师范学院',\n", "    '牡丹江医学院': '牡丹江医科大学',\n", "    '贵州财经大学商务学院': '贵州黔南经济学院',\n", "    '海南医学院': '海南医科大学',\n", "    '四川外国语大学成都学院': '成都外国语学院',\n", "    '桂林电子科技大学信息科技学院': '桂林信息科技学院',\n", "    '桂林理工大学博文管理学院': '南宁理工学院',\n", "    '江苏警官学校': '江苏警官学院',\n", "    '淮北师范大学信息学院': '淮北理工学院',\n", "    '西北师范大学知行学院': '兰州石化职业技术大学',\n", "    '赣南医学院': '赣南医科大学',\n", "    '中国矿业大学银川学院': '银川科技学院',\n", "    '福建工程学院': '福建理工大学',\n", "    '浙江海洋大学东海科学技术学院': '浙江药科职业大学',\n", "    '兰州财经大学长青学院': '兰州资源环境职业技术大学',\n", "    '北京工商大学嘉华学院': '北京金融科技学院',\n", "    '上海体育学院': '上海体育大学',\n", "    '香港中文大学': '香港中文大学（深圳）',\n", "    '合肥学院': '合肥大学',\n", "    '潍坊医学院': '山东第二医科大学',\n", "    '东莞理工学院城市学院': '东莞城市学院',\n", "    '广西中医药大学塞恩斯新医药学院': '广西中医药大学赛恩斯新医药学院',\n", "    '河南科技学院新科学院': '新乡工程学院',\n", "    '滨州学院': '山东航空学院',\n", "    '云南师范大学商学院': '昆明城市学院',\n", "    '重庆科技学院': '重庆科技大学',\n", "    '信阳师范学院': '信阳师范大学',\n", "    '江苏森林警察学院': '南京警察学院',\n", "    '山西师范大学现代文理学院': '山西电子科技学院',\n", "    '西南科技大学城市学院': '绵阳城市学院',\n", "    '河北中医学院': '河北中医药大学',\n", "    '阜阳师范大学信息工程学院': '阜阳理工学院',\n", "    '吉首大学张家界学院': '张家界学院',\n", "    '太原科技大学华科学院': '山西科技学院',\n", "    '铁道警察学院': '郑州警察学院',\n", "    '广西师范大学漓江学院': '桂林学院',\n", "    '贵州民族大学人文科技学院': '贵阳人文科技学院',\n", "    '宁夏师范学院': '宁夏师范大学',\n", "    '浙江科技学院': '浙江科技大学',\n", "    '长江大学工程技术学院': '荆州学院',\n", "    '济南大学泉城学院': '烟台科技学院',\n", "    '广西大学行健文理学院': '广西农业职业技术大学',\n", "    '北京电影学院现代创意媒体学院': '青岛电影学院',\n", "    '嘉兴学院': '嘉兴大学',\n", "    '云南大学滇池学院': '滇池学院',\n", "    '云南艺术学院文化学院': '昆明传媒学院',\n", "    '安徽师范大学皖江学院': '芜湖学院',\n", "    '蚌埠医学院': '蚌埠医科大学',\n", "    '佛山科学技术学院': '佛山大学',\n", "    '贵州师范大学求是学院': '贵阳康养职业大学',\n", "    '陕西国际贸易商贸学院': '陕西国际商贸学院',\n", "    '中国社会科学院': '中国社会科学院大学',\n", "    '广东工业大学华立学院': '广州华立学院',\n", "    '四川大学锦城学院': '成都锦城学院',\n", "    '贵州大学明德学院': '贵阳信息科技学院'\n", "}\n", "\n", "# school_name_2425_mapping_dict = {\n", "#     '南昌工程学院': '江西水利电力大学',\n", "#     '吉林化工学院': '吉林化工大学',\n", "#     '天水师范学院': '天水师范大学',\n", "#     '山西医科大学晋祠学院': '晋中健康学院',\n", "#     '常熟理工学院': '苏州工学院',\n", "#     '新乡医学院': '河南医药大学',\n", "#     '新乡医学院三全学院': '豫北医学院',\n", "#     '桂林医学院': '桂林医科大学',\n", "#     '湖南文理学院芙蓉学院': '常德学院',\n", "#     '湖南理工学院南湖学院': '岳阳学院',\n", "#     '绍兴文理学院元培学院': '绍兴理工学院',\n", "#     '西藏农牧学院': '西藏农牧大学',\n", "# }"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理院校数量：1219\n", "中文学校名唯一值数量：1219\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>伦敦政治经济学院</th>\n", "      <th>华威大学</th>\n", "      <th>伯明翰大学</th>\n", "      <th>格拉斯哥大学</th>\n", "      <th>利兹大学</th>\n", "      <th>南安普顿大学</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [school_name_cn, school_name_en, 牛津大学, 剑桥大学, 伦敦大学学院, 伦敦大学国王学院, 爱丁堡大学, 曼彻斯特大学, 伦敦政治经济学院, 华威大学, 伯明翰大学, 格拉斯哥大学, 利兹大学, 南安普顿大学, 谢菲尔德大学, 杜伦大学, 诺丁汉大学, 巴斯大学, 埃克塞特大学, 贝尔法斯特女王大学]\n", "Index: []"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df['school_name_cn'] = uk_school_gpa_df['school_name_cn'].replace(school_name_mapping_dict)\n", "print(f'处理院校数量：{len(uk_school_gpa_df)}')\n", "print(f'中文学校名唯一值数量：{len(uk_school_gpa_df['school_name_cn'].unique())}')\n", "uk_school_gpa_df[uk_school_gpa_df.duplicated(subset='school_name_cn', keep=False)]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'东北大学秦皇岛分校',\n", " '中国农业大学国际学院',\n", " '中国科学院研究生院',\n", " '北京师范大学-香港浸会大学',\n", " '南京师范大学师园学院',\n", " '吉林大学珠海学院',\n", " '苏州大学东吴商学院',\n", " '辽宁大学新华国际商学院'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["set(uk_school_gpa_df['school_name_cn']) - set(home_school_df['学校名称'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["362个院校名出现在案例数据中，但没有出现在教育部全国普通高等学校名单中\n", "{'印第安纳大学伯明顿分校', '广东海洋大学寸金学院', '贝勒大学', '布达佩斯考文纽斯大学', '澳大利亚国立大学', '东莞理工学院城市学院', '上海交通大学医学院', '西南科技大学城市学院', 'University of Central Florida', 'assumption university', '外交学院和北京第二外国语学院', '伊利诺伊大学香槟分校', '北京电影学院现代创意媒体学院', '亚利桑那州立大学', '中国矿业大学（徐州）', '南卫理公会大学', 'University of Manchester', '塞格德大学', '德国海德堡大学', '广州商学院+贝尔尤维大学', '哈尔滨工业大学（深圳）', '开姆尼茨工业大学（德国）', '西北大学（中国）', '昆士兰大学', '高丽大学', '加州大学圣塔芭芭拉分校', '东北财经大学和萨里大学', '哈尔滨工业大学（威海）', '威斯康星大学', '泰国博乐大学', '诺丁汉大学马来校区', '加州大学伯克利分校', '中原工学院信息商务学院', '逢甲大学', '马兰欧尼学院伦敦校区', '爱丁堡大学', '泰国博仁大学', '香港城市大学', '北京理工大学和北京大学', 'Dalhousie University', '西交利物浦', '考文垂大学', '麦克马斯特大学（McMaster University）', '墨尔本', '欧洲大学', '渥太华大学', '俄克拉荷马大学', '浙江科技学院', '隆德大学', '广东财经大学+西澳大学', 'Torontometropolitanuniversity', 'McMaster University', '加本Top3', '长安大学都柏林合办', '牛津大学', '外交学院+北京第二外国语学院', '莫斯科国立大学', 'University of Waterloo', '中山大学南方学院', '帝国理工学院', '中国农业大学+伊利诺伊大学香槟分校', '早稻田大学', '怀卡托大学', '诺丁汉大学', '马来西亚泰莱大学', '悉尼大学', '韩国汉阳大学', '华北电力大学（保定）', 'Lehigh University', '合肥工业大学（宣城校区）', '印第安纳大学凯利商学院', '台湾中原大学', '电子科技大学和格拉斯哥大学', '广东金融学院+西澳大学', '985高校', 'LaTrobe University', 'Lingnan University', '慕尼黑工业', '阿尔伯塔', '上海体育学院', '北京师范大学珠海分校+墨尔本大学', '华盛顿大学', '南安普顿大学', '大连理工大学（盘锦校区）', '东北师范大学+新泽西州立罗格斯大学', '河北工业大学+拉彭兰塔理工大学', '纽卡斯尔大学', '广东财经大学华商学院', '纽约州立大学石溪分校', '澳门理工学院', '香港科技大学', '台北商业大学', '埃默里大学', '西南大学（荣昌校区）', '犹太州立大学', '中南财经政法大学+罗德岛大学', '伦敦艺术学院', '都柏林大学', '英属哥伦比亚大学', '三一学院', '江南大学+莫纳什大学（3+1）', '庆熙大学', '香港中文大学', '维克森林大学', '卡尔顿大学', 'universityofwaterloo', '成均馆大学', '中国石油大学（北京）克拉玛依校区', '四川大学锦城学院', 'Simon Fraser University', '985院校', 'University of British Columbia', '惠顿学院', '俄亥俄州立大学+四川大学', '谢菲尔德大学', '北京工商大学嘉华学院', '布鲁克大学', '乐卓博大学', '北京工业大学北京-都柏林国际学院', '密歇根州立大学', '香港都会大学', '澳门大学', '西南财经大学和纽约城市大学', 'UKM', 'UNSW', '匹兹堡大学', '日本关西大学', '河北工业大学和拉彭兰塔理工大学', '伯克利音乐学院', '宾州州立大学', '北京工商大学嘉华学院和北京大学', '皇家墨尔本理工大学', '贵州师范大学求是学院', '墨尔本大学', '里士满大学', '东北大学秦皇岛分校', '悉尼科技大学', '华威大学', '上海理工大学和伯明翰大学', 'Western University', '滑铁卢大学', 'UIUC', '台湾东海大学', '伦敦大学皇家霍洛威学院', '兰州财经大学+美国北亚利桑那大学', '四川外国语大学重庆南方翻译学院', '铭传大学', '伦敦大学国王学院', '近畿大学', '都柏林圣三一大学', '中山大学珠海校区', '211院校', '加州大学圣地亚哥分校', '香港教育大学', '暨南大学+伯明翰大学', '马来西亚泰来', '加州大学欧文分校+帕萨迪纳城市学院', '加州大学欧文分校', '罗格斯大学', '伯明翰城市大学', '伦敦艺术大学', '萨凡纳艺术与设计学院', 'Toronto Metropolitan University', '华盛顿大学西雅图分校', '利物浦大学', '特拉华大学', '以色列理工学院', '柯伊学院', '康考迪亚大学', 'University of Delaware', '加州戴维斯大学', '纽约州立大学阿尔巴尼分校', '伯明翰大学', '韩国中央大学', '华南农业大学和都柏林大学', '福特汉姆大学', '汉阳大学', '美国犹他州立大学', '韦仕敦大学', '海南大学+亚利桑那州州立大学', '德克萨斯理工大学', '里昂商学院', '纽约州立大学宾汉姆顿分校', '弗莱堡大学', '伦敦大学学院', '加州州立大学', '马来西亚国立大学（UKM）', '加州大学圣克鲁兹分校', '华南农业+都柏林大学', '加州大学河滨分校', '广东金融学院+西阿拉巴马大学', '明尼苏达大学双城分校', '威斯康星大学麦迪逊分校', '香港理工大学', '天津大学仁爱学院', 'Dalhousie', '吉林大学莱姆顿学院', '科廷大学新加坡校区', '北京师范大学（珠海校区）', '香港恒生大学', '浙江大学城市学院', '南京信息工程大学滨江学院', '加利福尼亚大学圣塔芭芭拉分校', '西南财经大学+北京大学', 'UConn', '加州大学洛杉矶分校', '戴尔豪斯大学', 'University at Buffalo', '加拿大西安大略大学', '马来西亚博特拉大学', '香港岭南大学', '中国农业大学+科罗拉多大学丹佛分校', '广东金融学院和西阿拉巴马大学', 'The Pennsylvania State University', '格拉斯哥', '香港浸会大学', '澳门科技大学', '华盛顿州立大学', '南财+滑铁卢', '福尔曼大学', '犹他州立大学', 'University of Birmingham', '曼彻斯特大学', '安徽师范大学皖江学院', '上海旅游高等专科学校+上海师范大学', 'University of Macau', 'UCI', '中山大学新华学院', '纽卡斯尔', '中央大学', '弗吉尼亚大学', '马来西亚国立大学', '香港树仁大学', '东北师范大学人文学院', '博仁大学', '苏州大学文正学院', '安徽大学+格林威治大学(3+1），硕：贝尔法斯特女王大学（在读）', '北京交通大学威海校区+兰卡斯特大学', '中国传媒大学+纽约理工大学', '厦门大学马来西亚分校', '莫纳什大学', '云南大学旅游文化学院', '加拿大约克大学', '威顿学院', '伍伦贡大学', '杜伦大学', '南卡罗来纳大学', '罗切斯特大学', '埃塞克高等商学院', '纽约大学', '麦考瑞大学', '圣塔克拉拉大学', '佩奇大学', '不列颠哥伦比亚大学', '江南大学+莫纳什', '顺德职业技术学院', '新南威尔士大学', '爱荷华大学', '马里兰帕克', '北京交通大学（威海校区）', '美本Top40', '康普顿斯大学', '天津工业大学和下莱茵应用科学大学', '东北大学（中国）', 'SPJain全球管理学院', '恺撒里兹酒店管理学院', '西蒙弗雷泽大学', '文藻外语大学', '东北财经大学+萨里大学', '康涅狄格大学', '天津工业大学+下莱茵应用科学大学', '俄亥俄州立大学', '国立中山大学', '马萨诸塞大学波士顿分校', '山东大学（青岛）', '山东大学（威海）', '布达佩斯技术与经济大学', '麦克马斯特大学', '约克大学（加拿大）', '伦敦政治经济学院', '北京邮电大学（宏福校区）', '北京交通大学和罗切斯特理工学院', '香港公开大学', 'Baylor University', 'Emlyon Business School', '佩斯大学', '武汉工程大学+佛罗里达理工学院', '九州大学', '法国SKEMA商学院', '北京工业大学都柏林国际学院', '布里斯托大学', '上海理工大学-利兹大学', '卡拉马祖学院', '佩珀代因大学', '加拿大滑铁卢大学', '印第安纳大学布卢明顿分校', '中国农业大学和科罗拉多大学丹佛分校', '中国传媒大学南广学院', 'dalhousie', '香港大学', '中央财经大学+伊利诺伊大学厄巴纳-香槟分校', '马来西亚博特拉', '美本TOP40文理学院', 'Embry-Riddle Aeronautical University', '多伦多大学', 'University of Ottawa', 'SUNGKYUNKWAN UNIVERSITY', '四川外国语大学成都学院', '瓦萨学院', 'MercyCollege', '加拿大女王大学', '台湾政治大学', '吉林大学珠海学院', '重庆科技学院', '南澳大学', '南京财经大学+滑铁卢大学', '博洛尼亚大学', '雪城大学', '中国人民解放军国防科技大学', '慕尼黑工业大学', '河北工业大学+拉彭兰塔理工大学（1+3）', '多伦多', '奥克兰大学（新西兰）', '卡内基梅隆大学', '温州肯恩大学+肯恩大学', '211高校', '美利坚大学', '辅仁大学', '波士顿大学', '华北电力大学（北京）', '华南农业大学+莫纳什大学（2+2）', '中国人民大学（苏州）', '加州大学戴维斯分校', '哥伦比亚大学巴纳德学院', '江苏大学和加州州立大学', '赣南医学院', 'Erasmus University Rotterdam', '格拉斯哥大学', 'Monash University', '澳门城市大学', '特伦特大学', '圣安德鲁斯大学', 'The University of Sunderland', '圣安德鲁斯', '华南理工大学广州学院', '澳门本科', '暨南大学伯明翰大学联合学院', '日本明治大学', '里斯本大学学院', '阿尔伯塔大学', '中国地质大学（武汉）+滑铁卢大学', '美国东北大学', '燕山大学+科廷大学', '都灵理工大学', '索邦大学', '卡尔加里大学', '广东工业大学华立学院', '梅西大学', 'keukacollege', '华大政法大学', '河南大学+乔治梅森大学', '宾夕法尼亚州立大学'}\n"]}], "source": ["# 同理可check案例数据库中的本科学校名，是否在教育部全国普通高等学校名单中（其中大量都是海外院校的本科...）\n", "\n", "# case_df = pd.read_csv('案例数据库.csv', dtype={'offer_program_id': 'Int64',  'offer_program_code': 'Int64'})\n", "\n", "# missing_schools_from_case = set(case_df['undergraduate_school'].unique()) - set(home_school_df['学校名称'].unique())\n", "# print(f'{len(missing_schools_from_case)}个院校名出现在案例数据中，但没有出现在教育部全国普通高等学校名单中')\n", "# print(missing_schools_from_case)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### GPA要求名单数据检查、调整与补充"]}, {"cell_type": "markdown", "metadata": {}, "source": ["部分英国院校GPA要求信息补充..."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["['帝国理工学院',\n", " '布里斯托大学',\n", " '伦敦大学玛丽皇后学院',\n", " '圣安德鲁斯大学',\n", " '纽卡斯尔大学（英国）',\n", " '利物浦大学',\n", " '兰卡斯特大学',\n", " '约克大学（英国）',\n", " '卡迪夫大学',\n", " '雷丁大学']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df = pd.read_csv('境外院校数据库.csv', dtype={'school_usnews_rank': 'str'})\n", "uk_schools = aboard_school_df[aboard_school_df['school_region'] == '英国']['school_name_cn'].unique()\n", "[school for school in uk_schools if school not in uk_school_gpa_df.columns][:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 圣安德鲁斯大学\n", "  \n", "    注意圣安的原始List里还有很多其他有效信息，比如哪些是985/211/双一流, 因此先进行它的添加..."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Chinese name</th>\n", "      <th>Band</th>\n", "      <th>Project 985</th>\n", "      <th>Project 211</th>\n", "      <th>格式标准化分数要求</th>\n", "      <th>原始分数要求</th>\n", "      <th>Double First-Class University/Specialist Institution*</th>\n", "      <th>Double First Subjects</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>中国人民解放军空军军医大学（第四军医大学）</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>安徽农业大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>安徽中医药大学</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>安徽医科大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>安徽师范大学</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>808</th>\n", "      <td>浙江外国语学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>809</th>\n", "      <td>中华女子学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>810</th>\n", "      <td>呼和浩特民族学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>811</th>\n", "      <td>湖南女子学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>812</th>\n", "      <td>青海民族大学</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>813 rows × 8 columns</p>\n", "</div>"], "text/plain": ["              Chinese name Band Project 985 Project 211  \\\n", "0    中国人民解放军空军军医大学（第四军医大学）    C          No         Yes   \n", "1                   安徽农业大学    D          No          No   \n", "2                  安徽中医药大学    E          No          No   \n", "3                   安徽医科大学    D          No          No   \n", "4                   安徽师范大学    C          No          No   \n", "..                     ...  ...         ...         ...   \n", "808                浙江外国语学院    E          No          No   \n", "809                 中华女子学院    E          No          No   \n", "810               呼和浩特民族学院    E          No          No   \n", "811                 湖南女子学院    E          No          No   \n", "812                 青海民族大学    E          No          No   \n", "\n", "                 格式标准化分数要求                 原始分数要求  \\\n", "0    Standard: minimum 77%  Standard: minimum 77%   \n", "1    Standard: minimum 82%  Standard: minimum 82%   \n", "2    Standard: minimum 85%  Standard: minimum 85%   \n", "3    Standard: minimum 82%  Standard: minimum 82%   \n", "4    Standard: minimum 77%  Standard: minimum 77%   \n", "..                     ...                    ...   \n", "808  Standard: minimum 85%  Standard: minimum 85%   \n", "809  Standard: minimum 85%  Standard: minimum 85%   \n", "810  Standard: minimum 85%  Standard: minimum 85%   \n", "811  Standard: minimum 85%  Standard: minimum 85%   \n", "812  Standard: minimum 85%  Standard: minimum 85%   \n", "\n", "    Double First-Class University/Specialist Institution*  \\\n", "0                                                   No      \n", "1                                                   No      \n", "2                                                   No      \n", "3                                                   No      \n", "4                                                   No      \n", "..                                                 ...      \n", "808                                                 No      \n", "809                                                 No      \n", "810                                                 No      \n", "811                                                 No      \n", "812                                                 No      \n", "\n", "    Double First Subjects  \n", "0                     NaN  \n", "1                     NaN  \n", "2                     NaN  \n", "3                     NaN  \n", "4                     NaN  \n", "..                    ...  \n", "808                   NaN  \n", "809                   NaN  \n", "810                   NaN  \n", "811                   NaN  \n", "812                   NaN  \n", "\n", "[813 rows x 8 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取Excel文件\n", "excel_file_path = '院校数据/院校List/圣安List初始整理版.xlsx'\n", "saint_andrews_df = pd.read_excel(excel_file_path)\n", "saint_andrews_df"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["不在参考名单中的985高校: {'中国人民解放军国防科技大学（曾用名：国防科技大学）'}\n", "不在参考名单中的211高校: {'中国人民解放军海军军医大学（第二军医大学）', '中国人民解放军空军军医大学（第四军医大学）'}\n"]}], "source": ["# 提取985和211高校的名称\n", "tier1_universities = saint_andrews_df[saint_andrews_df['Project 985'] == 'Yes']['Chinese name'].tolist()\n", "tier2_universities = saint_andrews_df[(saint_andrews_df['Project 985'] == 'No') & (saint_andrews_df['Project 211'] == 'Yes')]['Chinese name'].tolist()\n", "\n", "# 读取参考名单JSON文件\n", "json_file_path = '院校数据/tiers.json'\n", "with open(json_file_path, 'r', encoding='utf-8') as file:\n", "    reference_tiers = json.load(file)\n", "\n", "reference_tier1 = set(reference_tiers.get('tier1', []))\n", "reference_tier2 = set(reference_tiers.get('tier2', []))\n", "\n", "# 检测哪些高校名称没在参考名单的\"tier1\"和\"tier2\"中出现\n", "missing_tier1 = set(tier1_universities) - reference_tier1\n", "missing_tier2 = set(tier2_universities) - reference_tier2\n", "\n", "print(\"不在参考名单中的985高校:\", missing_tier1)\n", "print(\"不在参考名单中的211高校:\", missing_tier2)\n", "\n", "# 对比结果说明tiers.json中的92学校名称还是很全面的，唯一不含的就是三所军校（圣安名单中给的曾用名所以没匹配上），但教育部普通高等学校名单中也没有这几个军校"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["school_tier\n", "211    74\n", "985    38\n", "Name: count, dtype: int64"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定义一个函数来确定 school_tier 的值\n", "def determine_school_tier(school_name):\n", "    if school_name in reference_tier1:\n", "        return '985'\n", "    elif school_name in reference_tier2:\n", "        return '211'\n", "    else:\n", "        return None  # 或者你可以选择其他默认值，比如 'Other'\n", "\n", "# 新增 school_tier 列\n", "uk_school_gpa_df['school_tier'] = uk_school_gpa_df['school_name_cn'].apply(determine_school_tier)\n", "uk_school_gpa_df['school_tier'].value_counts() # 39所985，77所纯211，这里是少了几所军校..."]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["空值情况:\n", "0\n", "分布情况:\n", "分数\n", "85    417\n", "82    192\n", "77    162\n", "75     33\n", "72      9\n", "Name: count, dtype: int64\n"]}], "source": ["# 使用正则表达式提取分数\n", "def extract_score(text):\n", "    match = re.search(r'minimum (\\d+)%', text)\n", "    if match:\n", "        return int(match.group(1))\n", "    else:\n", "        return None\n", "\n", "saint_andrews_df['分数'] = saint_andrews_df['格式标准化分数要求'].apply(extract_score)\n", "\n", "# 检查新列的空值和分布情况\n", "print(\"空值情况:\")\n", "print(saint_andrews_df['分数'].isnull().sum())\n", "print(\"分布情况:\")\n", "print(saint_andrews_df['分数'].value_counts())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["匹配：注意联表前先对联表基准字段去重...先总体去重，再对基准字段核查情况并单独去重..."]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["Chinese name\n", "辽宁工程技术大学    2\n", "河南农业大学      2\n", "山东航空学院      2\n", "安徽农业大学      1\n", "安徽中医药大学     1\n", "           ..\n", "浙江外国语学院     1\n", "中华女子学院      1\n", "呼和浩特民族学院    1\n", "湖南女子学院      1\n", "青海民族大学      1\n", "Name: count, Length: 800, dtype: int64"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["saint_andrews_df.drop_duplicates(inplace=True)\n", "saint_andrews_df['Chinese name'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Chinese name</th>\n", "      <th>Band</th>\n", "      <th>Project 985</th>\n", "      <th>Project 211</th>\n", "      <th>格式标准化分数要求</th>\n", "      <th>原始分数要求</th>\n", "      <th>Double First-Class University/Specialist Institution*</th>\n", "      <th>Double First Subjects</th>\n", "      <th>分数</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>河南农业大学</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>河南农业大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>辽宁工程技术大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>辽宁工程技术大学</td>\n", "      <td>D</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>Standard: minimum 82%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>269</th>\n", "      <td>山东航空学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>507</th>\n", "      <td>山东航空学院</td>\n", "      <td>E</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>Standard: minimum 85%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "      <td>85</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Chinese name Band Project 985 Project 211              格式标准化分数要求  \\\n", "137       河南农业大学    C          No          No  Standard: minimum 77%   \n", "149       河南农业大学    D          No          No  Standard: minimum 82%   \n", "201     辽宁工程技术大学    D          No          No  Standard: minimum 82%   \n", "206     辽宁工程技术大学    D          No          No  Standard: minimum 82%   \n", "269       山东航空学院    E          No          No  Standard: minimum 85%   \n", "507       山东航空学院    E          No          No  Standard: minimum 85%   \n", "\n", "                    原始分数要求  \\\n", "137  Standard: minimum 77%   \n", "149  Standard: minimum 82%   \n", "201  Standard: minimum 82%   \n", "206  Standard: minimum 82%   \n", "269  Standard: minimum 85%   \n", "507  Standard: minimum 85%   \n", "\n", "    Double First-Class University/Specialist Institution*  \\\n", "137                                                 No      \n", "149                                                 No      \n", "201                                                NaN      \n", "206                                                 No      \n", "269                                                NaN      \n", "507                                                 No      \n", "\n", "    Double First Subjects  分数  \n", "137                   NaN  77  \n", "149                   NaN  82  \n", "201                   NaN  82  \n", "206                   NaN  82  \n", "269                   NaN  85  \n", "507                   NaN  85  "]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["saint_andrews_df[saint_andrews_df.duplicated(subset=['Chinese name'], keep=False)] # 河南农业大学确实出现了两次，无法判断...先直接去重了吧"]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [{"data": {"text/plain": ["Chinese name\n", "北京警察学院                   1\n", "山东政法学院                   1\n", "中央司法警官学院                 1\n", "广东警官学院                   1\n", "重庆警察学院                   1\n", "                        ..\n", "安徽师范大学                   1\n", "安徽医科大学                   1\n", "安徽中医药大学                  1\n", "安徽农业大学                   1\n", "中国人民解放军空军军医大学（第四军医大学）    1\n", "Name: count, Length: 800, dtype: int64"]}, "execution_count": 128, "metadata": {}, "output_type": "execute_result"}], "source": ["saint_andrews_df.drop_duplicates(subset=['Chinese name'], inplace=True)\n", "saint_andrews_df['Chinese name'].value_counts()"]}, {"cell_type": "code", "execution_count": 129, "metadata": {}, "outputs": [], "source": ["# 将两个 DataFrame 基于 'school_name_cn' 和 'Chinese name' 进行合并\n", "uk_school_gpa_df = pd.merge(uk_school_gpa_df, saint_andrews_df[['Chinese name', '分数']], left_on='school_name_cn', right_on='Chinese name', how='left')\n", "\n", "# 重命名新列\n", "uk_school_gpa_df.rename(columns={'分数': '圣安德鲁斯大学'}, inplace=True)\n", "\n", "# 删除多余的列\n", "uk_school_gpa_df.drop(columns=['Chinese name'], inplace=True)\n", "\n", "uk_school_gpa_df['圣安德鲁斯大学'] = uk_school_gpa_df['圣安德鲁斯大学'].astype('Int64')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["还有一些没有匹配上的名字，逐个分析处理..."]}, {"cell_type": "code", "execution_count": 130, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20个院校名出现在圣安德鲁斯GPA要求名单中，但没有出现在总GPA要求名单中\n"]}, {"data": {"text/plain": ["{'厦门医学院': '厦门工学院',\n", " '深圳北理莫斯科大学': '深圳大学',\n", " '中国地质大学': '中国地质大学（武汉）',\n", " '西湖大学': '湖北大学',\n", " '昆山杜克大学': '长江大学',\n", " '中国人民解放军国防科技大学（曾用名：国防科技大学）': '武汉科技大学',\n", " '天津中德应用技术大学': '上海应用技术大学',\n", " '上海电机大学': '上海电力大学',\n", " '中国石油大学': '中国石油大学（北京）',\n", " '中国人民解放军空军军医大学（第四军医大学）': '中国人民大学',\n", " '河北水利电力学院': '华北水利水电大学',\n", " '中国青年政治学院': '长治学院',\n", " '中国科学院大学': '中国社会科学院大学',\n", " '广西职业师范学院': '广西民族师范学院',\n", " '广西师范学院': '广西民族师范学院',\n", " '中国人民解放军海军军医大学（第二军医大学）': '中国人民大学',\n", " '南京工业职业技术大学': '河北工业职业技术大学',\n", " '北京航空航天大学北海学院（已于2013年停止招生）': '北京航空航天大学',\n", " '滇西应用技术大学': '上海应用技术大学',\n", " '内蒙古科技大学包头师...': '内蒙古科技大学包头师范学院'}"]}, "execution_count": 130, "metadata": {}, "output_type": "execute_result"}], "source": ["missing_schools_from_sa = set(saint_<PERSON><PERSON><PERSON>_df['Chinese name']) - set(uk_school_gpa_df['school_name_cn'])\n", "print(f'{len(missing_schools_from_sa)}个院校名出现在圣安德鲁斯GPA要求名单中，但没有出现在总GPA要求名单中')\n", "\n", "fuzz_matched_results = {}\n", "for name in missing_schools_from_sa:\n", "    best_match, _ = process.extractOne(name, set(uk_school_gpa_df['school_name_cn']))\n", "    fuzz_matched_results[name] = best_match\n", "\n", "fuzz_matched_results\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对下述院校进行处理："]}, {"cell_type": "code", "execution_count": 131, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Chinese name</th>\n", "      <th>Band</th>\n", "      <th>Project 985</th>\n", "      <th>Project 211</th>\n", "      <th>格式标准化分数要求</th>\n", "      <th>原始分数要求</th>\n", "      <th>Double First-Class University/Specialist Institution*</th>\n", "      <th>Double First Subjects</th>\n", "      <th>分数</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>中国地质大学</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>Specialized in Geography and Marine Science</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>中国矿业大学</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>NaN</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>中国石油大学</td>\n", "      <td>C</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>Standard: minimum 77%</td>\n", "      <td>No</td>\n", "      <td>Subject strength in Geography</td>\n", "      <td>77</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Chinese name Band Project 985 Project 211              格式标准化分数要求  \\\n", "68       中国地质大学    C          No         Yes  Standard: minimum 77%   \n", "69       中国矿业大学    C          No         Yes  Standard: minimum 77%   \n", "70       中国石油大学    C          No         Yes  Standard: minimum 77%   \n", "\n", "                   原始分数要求  \\\n", "68  Standard: minimum 77%   \n", "69  Standard: minimum 77%   \n", "70  Standard: minimum 77%   \n", "\n", "   Double First-Class University/Specialist Institution*  \\\n", "68                                                 No      \n", "69                                                 No      \n", "70                                                 No      \n", "\n", "                          Double First Subjects  分数  \n", "68  Specialized in Geography and Marine Science  77  \n", "69                                          NaN  77  \n", "70                Subject strength in Geography  77  "]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["saint_andrews_df[\n", "    (saint_andrews_df['Chinese name'] == '中国地质大学') |\n", "    (saint_andrews_df['Chinese name'] == '中国矿业大学') |\n", "    (saint_and<PERSON>s_df['Chinese name'] == '中国石油大学')\n", "]"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>伦敦政治经济学院</th>\n", "      <th>华威大学</th>\n", "      <th>...</th>\n", "      <th>利兹大学</th>\n", "      <th>南安普顿大学</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "      <th>school_tier</th>\n", "      <th>圣安德鲁斯大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>中国地质大学（武汉）</td>\n", "      <td>China University of Geosciences (Wuhan)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>中国石油大学（北京）</td>\n", "      <td>China University of Petroleum (Beijing)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>中国矿业大学</td>\n", "      <td>China University of Mining and Technology (Xuz...</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>中国地质大学（北京）</td>\n", "      <td>China University of Geosciences (Beijing)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>中国石油大学（华东）</td>\n", "      <td>China University of Petroleum (Huadong) or (Ea...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>211</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>中国矿业大学（北京）</td>\n", "      <td>China University of Mining and Technology - Be...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>75</td>\n", "      <td>78</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>829</th>\n", "      <td>中国矿业大学徐海学院</td>\n", "      <td>Xuhai College, China University of Mining and ...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>90</td>\n", "      <td>93</td>\n", "      <td>...</td>\n", "      <td>80</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>None</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7 rows × 22 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                                     school_name_en  牛津大学  \\\n", "54      中国地质大学（武汉）            China University of Geosciences (Wuhan)    85   \n", "65      中国石油大学（北京）            China University of Petroleum (Beijing)    85   \n", "68          中国矿业大学  China University of Mining and Technology (Xuz...    85   \n", "72      中国地质大学（北京）          China University of Geosciences (Beijing)    85   \n", "77      中国石油大学（华东）  China University of Petroleum (Huadong) or (Ea...    90   \n", "79      中国矿业大学（北京）  China University of Mining and Technology - Be...    90   \n", "829     中国矿业大学徐海学院  Xuhai College, China University of Mining and ...    90   \n", "\n", "     剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  伦敦政治经济学院  华威大学  ...  利兹大学  南安普顿大学  \\\n", "54     85      85        85     85      85        90    82  ...    75      78   \n", "65     85      85        85     85      85        90    82  ...    75      78   \n", "68     85      85        85     85      85        90    82  ...    75      78   \n", "72     85      85        85     85      85        90    82  ...    75      78   \n", "77     90      90        85     85      85        90    82  ...    75      78   \n", "79     90      90        85     85      85        90    82  ...    75      78   \n", "829    90      90        88     87      88        90    93  ...    80      90   \n", "\n", "     谢菲尔德大学  杜伦大学  诺丁汉大学  巴斯大学  埃克塞特大学  贝尔法斯特女王大学  school_tier  圣安德鲁斯大学  \n", "54       73    80     75    75      75         75          211     <NA>  \n", "65       73    80     75    75      75         75          211     <NA>  \n", "68       73    80     75    75      75         75          211       77  \n", "72       73    80     75    75      75         75          211     <NA>  \n", "77       73    80     75    75      75         80          211     <NA>  \n", "79       73    80     75    75      75         75          211     <NA>  \n", "829      88  <NA>     83    80      80         80         None     <NA>  \n", "\n", "[7 rows x 22 columns]"]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定义目标学校名称列表\n", "target_schools = ['中国地质大学', '中国石油大学', '中国矿业大学']\n", "\n", "# 创建布尔掩码，检查 'school_name_cn' 是否包含目标学校名称之一\n", "mask = uk_school_gpa_df['school_name_cn'].str.contains('|'.join(target_schools))\n", "\n", "# 使用布尔掩码筛选行\n", "uk_school_gpa_df[mask]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uk_school_gpa_df.loc[mask, '圣安德鲁斯大学'] = 77\n", "# 单独处理徐海学院...\n", "uk_school_gpa_df.loc[829, '圣安德鲁斯大学'] = pd.NA"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [{"data": {"text/plain": ["圣安德鲁斯大学\n", "85    399\n", "82    186\n", "77    160\n", "75     31\n", "72      9\n", "Name: count, dtype: Int64"]}, "execution_count": 143, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df['圣安德鲁斯大学'].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 帝国理工学院\n", "    直接先按211 85+ 作为分数要求...同时注意中外合办也算在list中"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['香港中文大学（深圳）', '上海纽约大学', '宁波诺丁汉大学', '昆山杜克大学', '西交利物浦大学',\n", "       '深圳北理莫斯科大学', '北京师范大学-香港浸会大学联合国际学院', '温州肯恩大学', '广东以色列理工学院',\n", "       '香港城市大学（东莞）', '香港科技大学（广州）'], dtype=object)"]}, "execution_count": 155, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将 备注 列中的空值填充为空字符串\n", "home_school_df['备注'] = home_school_df['备注'].fillna('')\n", "\n", "# 筛选出 备注 列中含有 “中外合办” 字符串的行\n", "china_foreign_schools = home_school_df[home_school_df['备注'].str.contains('中外合作')]['学校名称'].unique()\n", "china_foreign_schools"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'广东以色列理工学院', '昆山杜克大学', '深圳北理莫斯科大学', '香港城市大学（东莞）', '香港科技大学（广州）'}"]}, "execution_count": 156, "metadata": {}, "output_type": "execute_result"}], "source": ["set(china_foreign_schools) - set(uk_school_gpa_df['school_name_cn'])"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [{"data": {"text/plain": ["school_tier\n", "211     74\n", "985     38\n", "中外合办     6\n", "Name: count, dtype: int64"]}, "execution_count": 160, "metadata": {}, "output_type": "execute_result"}], "source": ["# 更新 school_tier 列\n", "uk_school_gpa_df.loc[uk_school_gpa_df['school_name_cn'].isin(china_foreign_schools), 'school_tier'] = '中外合办'\n", "uk_school_gpa_df['school_tier'].value_counts()"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [], "source": ["# 添加新列 \"帝国理工学院\" 并根据条件赋值\n", "conditions = [\n", "    (uk_school_gpa_df['school_tier'] == '985'),\n", "    (uk_school_gpa_df['school_tier'] == '211'),\n", "    (uk_school_gpa_df['school_tier'] == '中外合办')\n", "]\n", "\n", "choices = [85, 85, 85]\n", "\n", "# 使用 np.select 根据条件赋值，并将默认值设置为 pd.NA\n", "uk_school_gpa_df['帝国理工学院'] = np.select(conditions, choices, default=np.nan)\n", "uk_school_gpa_df['帝国理工学院'] = uk_school_gpa_df['帝国理工学院'].astype('Int64')"]}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [{"data": {"text/plain": ["帝国理工学院\n", "<NA>    1101\n", "85       118\n", "Name: count, dtype: Int64"]}, "execution_count": 171, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df['帝国理工学院'].value_counts(dropna=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 布里斯托大学"]}, {"cell_type": "markdown", "metadata": {}, "source": ["    注意中外合办的分数...不是不在list中，比如西交利物浦。布大卡list比较严。"]}, {"cell_type": "code", "execution_count": 179, "metadata": {}, "outputs": [], "source": ["# 读取Excel文件\n", "file_path = '院校数据/院校List/布大List初始整理版.xlsx'\n", "bristol_df = pd.read_excel(file_path)\n", "\n", "# 提取中文大学名称\n", "def extract_chinese_name(full_name):\n", "    # 使用正则表达式找到第一个中文字符的位置\n", "    match = re.search(r'[\\u4e00-\\u9fff]', full_name)\n", "    if match:\n", "        # 返回匹配位置之后的所有字符作为中文名称\n", "        chinese_name = full_name[match.start():]\n", "        return chinese_name.strip()\n", "    else:\n", "        # 如果没有找到中文字符，返回空字符串或其他默认值\n", "        return ''\n", "\n", "# 应用函数到'University Name 大学名称'列\n", "bristol_df['Chinese University Name'] = bristol_df['University Name 大学名称'].apply(extract_chinese_name)\n"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [], "source": ["# 将两个 DataFrame 基于 'school_name_cn' 和 'Chinese University Name' 进行合并\n", "uk_school_gpa_df = pd.merge(uk_school_gpa_df, bristol_df[['Chinese University Name', '二等一']], left_on='school_name_cn', right_on='Chinese University Name', how='left')\n", "\n", "# 重命名新列\n", "uk_school_gpa_df.rename(columns={'二等一': '布里斯托大学'}, inplace=True)\n", "\n", "# 删除多余的列\n", "uk_school_gpa_df.drop(columns=['Chinese University Name'], inplace=True)\n", "\n", "\n", "uk_school_gpa_df['布里斯托大学'] = uk_school_gpa_df['布里斯托大学'].astype('Int64')"]}, {"cell_type": "code", "execution_count": 193, "metadata": {}, "outputs": [{"data": {"text/plain": ["布里斯托大学\n", "<NA>    956\n", "87      147\n", "82       56\n", "85       35\n", "80       16\n", "78        9\n", "Name: count, dtype: Int64"]}, "execution_count": 193, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df['布里斯托大学'].value_counts(dropna=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 依然会有一些在List中的学校，但没有在总名单中出现, 核验并处理这些学校..."]}, {"cell_type": "code", "execution_count": 194, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'上海体育学院',\n", " '中国人民解放军战略支援部队信息工程大学',\n", " '中国地质大学',\n", " '中国石油大学',\n", " '中国科学院大学',\n", " '中国青年政治学院',\n", " '国防科技大学',\n", " '海军军医大学',\n", " '第四军医大学',\n", " '西湖大学',\n", " '陆军军医大学'}"]}, "execution_count": 194, "metadata": {}, "output_type": "execute_result"}], "source": ["set(bristol_df['Chinese University Name']) - set(uk_school_gpa_df['school_name_cn'])"]}, {"cell_type": "code", "execution_count": 197, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>University Name 大学名称</th>\n", "      <th>Strong 2:1</th>\n", "      <th>二等一</th>\n", "      <th>Chinese University Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>China University of Geosciences (Beijing / Wuh...</td>\n", "      <td>85</td>\n", "      <td>82</td>\n", "      <td>中国地质大学</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>China University of Mining and Technology Beij...</td>\n", "      <td>88</td>\n", "      <td>85</td>\n", "      <td>中国矿业大学</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>China University of Petroleum (East China / Be...</td>\n", "      <td>88</td>\n", "      <td>85</td>\n", "      <td>中国石油大学</td>\n", "    </tr>\n", "    <tr>\n", "      <th>264</th>\n", "      <td>Shanghai University of Sport 上海体育学院</td>\n", "      <td>85</td>\n", "      <td>82</td>\n", "      <td>上海体育学院</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  University Name 大学名称  Strong 2:1  二等一  \\\n", "36   China University of Geosciences (Beijing / Wuh...          85   82   \n", "37   China University of Mining and Technology Beij...          88   85   \n", "38   China University of Petroleum (East China / Be...          88   85   \n", "264                Shanghai University of Sport 上海体育学院          85   82   \n", "\n", "    Chinese University Name  \n", "36                   中国地质大学  \n", "37                   中国矿业大学  \n", "38                   中国石油大学  \n", "264                  上海体育学院  "]}, "execution_count": 197, "metadata": {}, "output_type": "execute_result"}], "source": ["bristol_df[\n", "    (bristol_df['Chinese University Name'] == '中国地质大学') |\n", "    (bristol_df['Chinese University Name'] == '中国矿业大学') |\n", "    (bristol_df['Chinese University Name'] == '中国石油大学') |\n", "    (bristol_df['Chinese University Name'] == '上海体育学院')\n", "]"]}, {"cell_type": "code", "execution_count": 199, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>伦敦政治经济学院</th>\n", "      <th>华威大学</th>\n", "      <th>...</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "      <th>school_tier</th>\n", "      <th>圣安德鲁斯大学</th>\n", "      <th>帝国理工学院</th>\n", "      <th>布里斯托大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>中国地质大学（武汉）</td>\n", "      <td>China University of Geosciences (Wuhan)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>中国石油大学（北京）</td>\n", "      <td>China University of Petroleum (Beijing)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>中国矿业大学</td>\n", "      <td>China University of Mining and Technology (Xuz...</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>中国地质大学（北京）</td>\n", "      <td>China University of Geosciences (Beijing)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>中国石油大学（华东）</td>\n", "      <td>China University of Petroleum (Huadong) or (Ea...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>中国矿业大学（北京）</td>\n", "      <td>China University of Mining and Technology - Be...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>上海体育大学</td>\n", "      <td>Shanghai University of Sport</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>87</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>88</td>\n", "      <td>82</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>None</td>\n", "      <td>82</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>829</th>\n", "      <td>中国矿业大学徐海学院</td>\n", "      <td>Xuhai College, China University of Mining and ...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>90</td>\n", "      <td>93</td>\n", "      <td>...</td>\n", "      <td>88</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>None</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 24 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                                     school_name_en  牛津大学  \\\n", "54      中国地质大学（武汉）            China University of Geosciences (Wuhan)    85   \n", "65      中国石油大学（北京）            China University of Petroleum (Beijing)    85   \n", "68          中国矿业大学  China University of Mining and Technology (Xuz...    85   \n", "72      中国地质大学（北京）          China University of Geosciences (Beijing)    85   \n", "77      中国石油大学（华东）  China University of Petroleum (Huadong) or (Ea...    90   \n", "79      中国矿业大学（北京）  China University of Mining and Technology - Be...    90   \n", "208         上海体育大学                       Shanghai University of Sport    90   \n", "829     中国矿业大学徐海学院  Xuhai College, China University of Mining and ...    90   \n", "\n", "     剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  伦敦政治经济学院  华威大学  ...  谢菲尔德大学  杜伦大学  \\\n", "54     85      85        85     85      85        90    82  ...      73    80   \n", "65     85      85        85     85      85        90    82  ...      73    80   \n", "68     85      85        85     85      85        90    82  ...      73    80   \n", "72     85      85        85     85      85        90    82  ...      73    80   \n", "77     90      90        85     85      85        90    82  ...      73    80   \n", "79     90      90        85     85      85        90    82  ...      73    80   \n", "208    90      90        85     87      85        90    85  ...      88    82   \n", "829    90      90        88     87      88        90    93  ...      88  <NA>   \n", "\n", "     诺丁汉大学  巴斯大学  埃克塞特大学  贝尔法斯特女王大学  school_tier  圣安德鲁斯大学  帝国理工学院  布里斯托大学  \n", "54      75    75      75         75          211       77      85    <NA>  \n", "65      75    75      75         75          211       77      85    <NA>  \n", "68      75    75      75         75          211       77      85      85  \n", "72      75    75      75         75          211       77      85    <NA>  \n", "77      75    75      75         80          211       77      85    <NA>  \n", "79      75    75      75         75          211       77      85    <NA>  \n", "208     75    75      75         75         None       82    <NA>    <NA>  \n", "829     83    80      80         80         None     <NA>    <NA>    <NA>  \n", "\n", "[8 rows x 24 columns]"]}, "execution_count": 199, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定义目标学校名称列表\n", "target_schools = ['中国地质大学', '中国石油大学', '中国矿业大学', '上海体育']\n", "\n", "# 创建布尔掩码，检查 'school_name_cn' 是否包含目标学校名称之一\n", "mask = uk_school_gpa_df['school_name_cn'].str.contains('|'.join(target_schools))\n", "\n", "# 使用布尔掩码筛选行\n", "uk_school_gpa_df[mask]"]}, {"cell_type": "code", "execution_count": 205, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>伦敦政治经济学院</th>\n", "      <th>华威大学</th>\n", "      <th>...</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "      <th>school_tier</th>\n", "      <th>圣安德鲁斯大学</th>\n", "      <th>帝国理工学院</th>\n", "      <th>布里斯托大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>中国地质大学（武汉）</td>\n", "      <td>China University of Geosciences (Wuhan)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>中国石油大学（北京）</td>\n", "      <td>China University of Petroleum (Beijing)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>中国矿业大学</td>\n", "      <td>China University of Mining and Technology (Xuz...</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>中国地质大学（北京）</td>\n", "      <td>China University of Geosciences (Beijing)</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>中国石油大学（华东）</td>\n", "      <td>China University of Petroleum (Huadong) or (Ea...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>中国矿业大学（北京）</td>\n", "      <td>China University of Mining and Technology - Be...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>82</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>211</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>上海体育大学</td>\n", "      <td>Shanghai University of Sport</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>87</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>88</td>\n", "      <td>82</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>None</td>\n", "      <td>82</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>829</th>\n", "      <td>中国矿业大学徐海学院</td>\n", "      <td>Xuhai College, China University of Mining and ...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>90</td>\n", "      <td>93</td>\n", "      <td>...</td>\n", "      <td>88</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>None</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 24 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                                     school_name_en  牛津大学  \\\n", "54      中国地质大学（武汉）            China University of Geosciences (Wuhan)    85   \n", "65      中国石油大学（北京）            China University of Petroleum (Beijing)    85   \n", "68          中国矿业大学  China University of Mining and Technology (Xuz...    85   \n", "72      中国地质大学（北京）          China University of Geosciences (Beijing)    85   \n", "77      中国石油大学（华东）  China University of Petroleum (Huadong) or (Ea...    90   \n", "79      中国矿业大学（北京）  China University of Mining and Technology - Be...    90   \n", "208         上海体育大学                       Shanghai University of Sport    90   \n", "829     中国矿业大学徐海学院  Xuhai College, China University of Mining and ...    90   \n", "\n", "     剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  伦敦政治经济学院  华威大学  ...  谢菲尔德大学  杜伦大学  \\\n", "54     85      85        85     85      85        90    82  ...      73    80   \n", "65     85      85        85     85      85        90    82  ...      73    80   \n", "68     85      85        85     85      85        90    82  ...      73    80   \n", "72     85      85        85     85      85        90    82  ...      73    80   \n", "77     90      90        85     85      85        90    82  ...      73    80   \n", "79     90      90        85     85      85        90    82  ...      73    80   \n", "208    90      90        85     87      85        90    85  ...      88    82   \n", "829    90      90        88     87      88        90    93  ...      88  <NA>   \n", "\n", "     诺丁汉大学  巴斯大学  埃克塞特大学  贝尔法斯特女王大学  school_tier  圣安德鲁斯大学  帝国理工学院  布里斯托大学  \n", "54      75    75      75         75          211       77      85      82  \n", "65      75    75      75         75          211       77      85      85  \n", "68      75    75      75         75          211       77      85      85  \n", "72      75    75      75         75          211       77      85      82  \n", "77      75    75      75         80          211       77      85      85  \n", "79      75    75      75         75          211       77      85      85  \n", "208     75    75      75         75         None       82    <NA>      82  \n", "829     83    80      80         80         None     <NA>    <NA>    <NA>  \n", "\n", "[8 rows x 24 columns]"]}, "execution_count": 205, "metadata": {}, "output_type": "execute_result"}], "source": ["# 定义目标学校名称列表\n", "target_schools_82 = ['中国地质大学', '上海体育']\n", "target_schools_85 = [ '中国石油大学', '中国矿业大学']\n", "\n", "# 创建布尔掩码，检查 'school_name_cn' 是否包含目标学校名称之一\n", "mask82 = uk_school_gpa_df['school_name_cn'].str.contains('|'.join(target_schools_82))\n", "mask85 = uk_school_gpa_df['school_name_cn'].str.contains('|'.join(target_schools_85))\n", "\n", "\n", "uk_school_gpa_df.loc[mask82, '布里斯托大学'] = 82\n", "uk_school_gpa_df.loc[mask85, '布里斯托大学'] = 85\n", "\n", "# 单独处理徐海学院...\n", "uk_school_gpa_df.loc[829, '布里斯托大学'] = pd.NA\n", "\n", "## 处理后检查：\n", "\n", "# 定义目标学校名称列表\n", "target_schools = ['中国地质大学', '中国石油大学', '中国矿业大学', '上海体育']\n", "\n", "# 创建布尔掩码，检查 'school_name_cn' 是否包含目标学校名称之一\n", "mask = uk_school_gpa_df['school_name_cn'].str.contains('|'.join(target_schools))\n", "\n", "# 使用布尔掩码筛选行\n", "uk_school_gpa_df[mask]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["中外合办的特殊处理... 温州肯温、北师uic、港中深、北理莫斯科"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>伦敦政治经济学院</th>\n", "      <th>华威大学</th>\n", "      <th>...</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "      <th>school_tier</th>\n", "      <th>圣安德鲁斯大学</th>\n", "      <th>帝国理工学院</th>\n", "      <th>布里斯托大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>香港中文大学（深圳）</td>\n", "      <td>The Chinese University of Hong Kong, Shenzhen</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>88</td>\n", "      <td>85</td>\n", "      <td>88</td>\n", "      <td>85</td>\n", "      <td>80</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>中外合办</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>温州肯恩大学</td>\n", "      <td>Wenzhou-Kean University</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>85</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>中外合办</td>\n", "      <td>82</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>807</th>\n", "      <td>北京师范大学-香港浸会大学联合国际学院</td>\n", "      <td>Beijing Normal University-Hong Kong Baptist Un...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>85</td>\n", "      <td>88</td>\n", "      <td>90</td>\n", "      <td>93</td>\n", "      <td>...</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>中外合办</td>\n", "      <td>82</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 24 columns</p>\n", "</div>"], "text/plain": ["          school_name_cn                                     school_name_en  \\\n", "43            香港中文大学（深圳）      The Chinese University of Hong Kong, Shenzhen   \n", "204               温州肯恩大学                            Wenzhou-Kean University   \n", "807  北京师范大学-香港浸会大学联合国际学院  Beijing Normal University-Hong Kong Baptist Un...   \n", "\n", "     牛津大学  剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  伦敦政治经济学院  华威大学  ...  谢菲尔德大学  \\\n", "43     85    85      85        88     85      88        85    80  ...      73   \n", "204    90    90      90        88     87      88        90    85  ...      85   \n", "807    90    90      90        88     85      88        90    93  ...      85   \n", "\n", "     杜伦大学  诺丁汉大学  巴斯大学  埃克塞特大学  贝尔法斯特女王大学  school_tier  圣安德鲁斯大学  帝国理工学院  \\\n", "43     80     75    75      75         75         中外合办       77      85   \n", "204    80     75    80      75         80         中外合办       82      85   \n", "807  <NA>     83    80      75         80         中外合办       82      85   \n", "\n", "     布里斯托大学  \n", "43     <NA>  \n", "204    <NA>  \n", "807    <NA>  \n", "\n", "[3 rows x 24 columns]"]}, "execution_count": 209, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df[(uk_school_gpa_df['school_name_cn'] == '温州肯恩大学') |\n", "                (uk_school_gpa_df['school_name_cn'] == '北京师范大学-香港浸会大学联合国际学院') |\n", "                (uk_school_gpa_df['school_name_cn'] == '香港中文大学（深圳）') |\n", "                (uk_school_gpa_df['school_name_cn'] == '北京理工大学-莫斯科大学联合学院')] # 北理莫斯科在目前的gpa要求中没有，后续再单独添加了..."]}, {"cell_type": "code", "execution_count": 212, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>伦敦政治经济学院</th>\n", "      <th>华威大学</th>\n", "      <th>...</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "      <th>school_tier</th>\n", "      <th>圣安德鲁斯大学</th>\n", "      <th>帝国理工学院</th>\n", "      <th>布里斯托大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>香港中文大学（深圳）</td>\n", "      <td>The Chinese University of Hong Kong, Shenzhen</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>88</td>\n", "      <td>85</td>\n", "      <td>88</td>\n", "      <td>85</td>\n", "      <td>80</td>\n", "      <td>...</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>中外合办</td>\n", "      <td>77</td>\n", "      <td>85</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>温州肯恩大学</td>\n", "      <td>Wenzhou-Kean University</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>85</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>中外合办</td>\n", "      <td>82</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>807</th>\n", "      <td>北京师范大学-香港浸会大学联合国际学院</td>\n", "      <td>Beijing Normal University-Hong Kong Baptist Un...</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>85</td>\n", "      <td>88</td>\n", "      <td>90</td>\n", "      <td>93</td>\n", "      <td>...</td>\n", "      <td>85</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>中外合办</td>\n", "      <td>82</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 24 columns</p>\n", "</div>"], "text/plain": ["          school_name_cn                                     school_name_en  \\\n", "43            香港中文大学（深圳）      The Chinese University of Hong Kong, Shenzhen   \n", "204               温州肯恩大学                            Wenzhou-Kean University   \n", "807  北京师范大学-香港浸会大学联合国际学院  Beijing Normal University-Hong Kong Baptist Un...   \n", "\n", "     牛津大学  剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  伦敦政治经济学院  华威大学  ...  谢菲尔德大学  \\\n", "43     85    85      85        88     85      88        85    80  ...      73   \n", "204    90    90      90        88     87      88        90    85  ...      85   \n", "807    90    90      90        88     85      88        90    93  ...      85   \n", "\n", "     杜伦大学  诺丁汉大学  巴斯大学  埃克塞特大学  贝尔法斯特女王大学  school_tier  圣安德鲁斯大学  帝国理工学院  \\\n", "43     80     75    75      75         75         中外合办       77      85   \n", "204    80     75    80      75         80         中外合办       82      85   \n", "807  <NA>     83    80      75         80         中外合办       82      85   \n", "\n", "     布里斯托大学  \n", "43       82  \n", "204      85  \n", "807      85  \n", "\n", "[3 rows x 24 columns]"]}, "execution_count": 212, "metadata": {}, "output_type": "execute_result"}], "source": ["# 对照圣安德鲁斯的等级赋分...\n", "uk_school_gpa_df.loc[43, '布里斯托大学'] = 82\n", "uk_school_gpa_df.loc[204, '布里斯托大学'] = 85\n", "uk_school_gpa_df.loc[807, '布里斯托大学'] = 85\n", "\n", "uk_school_gpa_df[(uk_school_gpa_df['school_name_cn'] == '温州肯恩大学') |\n", "                (uk_school_gpa_df['school_name_cn'] == '北京师范大学-香港浸会大学联合国际学院') |\n", "                (uk_school_gpa_df['school_name_cn'] == '香港中文大学（深圳）') |\n", "                (uk_school_gpa_df['school_name_cn'] == '北京理工大学-莫斯科大学联合学院')] # 北理莫斯科在目前的gpa要求中没有，后续再单独添加了..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 结果整理与合并"]}, {"cell_type": "code", "execution_count": 229, "metadata": {}, "outputs": [], "source": ["# 指定学校名称顺序\n", "university_order = ['帝国理工学院','牛津大学','剑桥大学','伦敦大学学院','伦敦大学国王学院','爱丁堡大学','曼彻斯特大学','布里斯托大学','伦敦政治经济学院','华威大学','伯明翰大学','格拉斯哥大学','利兹大学',\n", "                    '南安普顿大学','谢菲尔德大学','杜伦大学','诺丁汉大学','圣安德鲁斯大学','巴斯大学','埃克塞特大学','贝尔法斯特女王大学']\n", "\n", "# 提取固定的列名\n", "fixed_columns = ['school_name_cn', 'school_name_en', 'school_tier']\n", "\n", "\n", "# 重新排序列\n", "uk_school_gpa_df = uk_school_gpa_df[fixed_columns + university_order]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# uk_school_gpa_df.to_csv('暂存院校GPA要求.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将各列转成字典\n", "# 获取需要转换的列名（排除 school_name_cn, school_name_en, school_tier）\n", "columns_to_convert = [col for col in uk_school_gpa_df.columns if col not in ['school_name_cn', 'school_name_en', 'school_tier']]\n", "\n", "# 创建一个新的 DataFrame 来存储结果\n", "result_df = pd.DataFrame(columns=['school_name_cn', 'school_name_en', 'school_tier', 'gpa_requirements'])\n", "\n", "# 遍历每一行并转换\n", "for index, row in uk_school_gpa_df.iterrows():\n", "    gpa_dict = {col: row[col] for col in columns_to_convert}\n", "    result_df.loc[index] = [row['school_name_cn'], row['school_name_en'], row['school_tier'], gpa_dict]"]}, {"cell_type": "code", "execution_count": 249, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "      <th>school_name_en</th>\n", "      <th>school_tier</th>\n", "      <th>gpa_requirements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>综合</td>\n", "      <td>4111010003</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>Tsinghua University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>综合</td>\n", "      <td>4111010001</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>2</td>\n", "      <td></td>\n", "      <td>Peking University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>综合</td>\n", "      <td>4133010335</td>\n", "      <td>杭州市</td>\n", "      <td>教育部</td>\n", "      <td>3</td>\n", "      <td></td>\n", "      <td>Zhejiang University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>综合</td>\n", "      <td>4131010248</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>4</td>\n", "      <td></td>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>复旦大学</td>\n", "      <td>综合</td>\n", "      <td>4131010246</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>5</td>\n", "      <td></td>\n", "      <td>Fudan University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1305</th>\n", "      <td>黑龙江工程学院昆仑旅游学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013304</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1306</th>\n", "      <td>黑龙江财经学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013298</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Heilongjiang University of Finance and Economics</td>\n", "      <td>None</td>\n", "      <td>{'帝国理工学院': &lt;NA&gt;, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1307</th>\n", "      <td>齐鲁医药学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>淄博市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qilu Medical University</td>\n", "      <td>None</td>\n", "      <td>{'帝国理工学院': &lt;NA&gt;, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1308</th>\n", "      <td>齐鲁理工学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>济南市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qilu Institute of Technology</td>\n", "      <td>None</td>\n", "      <td>{'帝国理工学院': &lt;NA&gt;, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1309</th>\n", "      <td>齐齐哈尔工程学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>齐齐哈尔市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qiqihar Institute of Technology</td>\n", "      <td>None</td>\n", "      <td>{'帝国理工学院': &lt;NA&gt;, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1310 rows × 10 columns</p>\n", "</div>"], "text/plain": ["               学校名称 学校类型       学校标识码    所在地     主管部门  软科排名  备注  \\\n", "0              清华大学   综合  4111010003    北京市      教育部     1       \n", "1              北京大学   综合  4111010001    北京市      教育部     2       \n", "2              浙江大学   综合  4133010335    杭州市      教育部     3       \n", "3            上海交通大学   综合  4131010248    上海市      教育部     4       \n", "4              复旦大学   综合  4131010246    上海市      教育部     5       \n", "...             ...  ...         ...    ...      ...   ...  ..   \n", "1305  黑龙江工程学院昆仑旅游学院  NaN  4123013304   哈尔滨市  黑龙江省教育厅  <NA>  民办   \n", "1306        黑龙江财经学院  NaN  4123013298   哈尔滨市  黑龙江省教育厅  <NA>  民办   \n", "1307         齐鲁医药学院  NaN  **********    淄博市   山东省教育厅  <NA>  民办   \n", "1308         齐鲁理工学院  NaN  **********    济南市   山东省教育厅  <NA>  民办   \n", "1309       齐齐哈尔工程学院  NaN  **********  齐齐哈尔市  黑龙江省教育厅  <NA>  民办   \n", "\n", "                                        school_name_en school_tier  \\\n", "0                                  Tsinghua University         985   \n", "1                                    Peking University         985   \n", "2                                  Zhejiang University         985   \n", "3                        Shanghai Jiao Tong University         985   \n", "4                                     Fudan University         985   \n", "...                                                ...         ...   \n", "1305                                               NaN         NaN   \n", "1306  Heilongjiang University of Finance and Economics        None   \n", "1307                           Qilu Medical University        None   \n", "1308                      Qilu Institute of Technology        None   \n", "1309                   Qiqihar Institute of Technology        None   \n", "\n", "                                       gpa_requirements  \n", "0     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "1     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "2     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "3     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "4     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "...                                                 ...  \n", "1305                                                NaN  \n", "1306  {'帝国理工学院': <NA>, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1307  {'帝国理工学院': <NA>, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1308  {'帝国理工学院': <NA>, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1309  {'帝国理工学院': <NA>, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "\n", "[1310 rows x 10 columns]"]}, "execution_count": 249, "metadata": {}, "output_type": "execute_result"}], "source": ["home_school_df = pd.merge(home_school_df, result_df, left_on='学校名称', right_on='school_name_cn', how='left')\n", "home_school_df.drop(columns=['school_name_cn'], inplace=True)\n", "home_school_df"]}, {"cell_type": "code", "execution_count": 295, "metadata": {}, "outputs": [{"data": {"text/plain": ["school_name_cn       0\n", "school_name_en       0\n", "school_tier       1101\n", "帝国理工学院            1101\n", "牛津大学                 0\n", "剑桥大学                 0\n", "伦敦大学学院               0\n", "伦敦大学国王学院             0\n", "爱丁堡大学                0\n", "曼彻斯特大学               0\n", "布里斯托大学             947\n", "伦敦政治经济学院             0\n", "华威大学                 0\n", "伯明翰大学                0\n", "格拉斯哥大学               0\n", "利兹大学                50\n", "南安普顿大学               0\n", "谢菲尔德大学               0\n", "杜伦大学               699\n", "诺丁汉大学                0\n", "圣安德鲁斯大学            434\n", "巴斯大学                 0\n", "埃克塞特大学               0\n", "贝尔法斯特女王大学            0\n", "dtype: int64"]}, "execution_count": 295, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 251, "metadata": {}, "outputs": [{"data": {"text/plain": ["学校名称                   0\n", "学校类型                 523\n", "学校标识码                  2\n", "所在地                    2\n", "主管部门                   2\n", "软科排名                 523\n", "备注                     0\n", "school_name_en        99\n", "school_tier         1192\n", "gpa_requirements      99\n", "dtype: int64"]}, "execution_count": 251, "metadata": {}, "output_type": "execute_result"}], "source": ["home_school_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# home_school_df.to_csv('境内院校数据库（初版）.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 数据读取"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_tier</th>\n", "      <th>帝国理工学院</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>...</th>\n", "      <th>格拉斯哥大学</th>\n", "      <th>利兹大学</th>\n", "      <th>南安普顿大学</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>圣安德鲁斯大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>Tsinghua University</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>72</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>Peking University</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>72</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>Zhejiang University</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>72</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>72</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>南京大学</td>\n", "      <td>Nanjing University</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>72</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1214</th>\n", "      <td>四川电影电视学院</td>\n", "      <td>Sichuan Film and Television College</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>...</td>\n", "      <td>90</td>\n", "      <td>80</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1215</th>\n", "      <td>四川文化艺术学院</td>\n", "      <td>Sichuan College of Culture and Arts</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>...</td>\n", "      <td>90</td>\n", "      <td>80</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1216</th>\n", "      <td>武汉传媒学院</td>\n", "      <td>Wuhan University of Communication</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>...</td>\n", "      <td>90</td>\n", "      <td>80</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1217</th>\n", "      <td>青岛电影学院</td>\n", "      <td>Beijing Film Academy Modern Creative Media Col...</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>...</td>\n", "      <td>90</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1218</th>\n", "      <td>昆明传媒学院</td>\n", "      <td>Wenhua College of Yunnan Arts University</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>87</td>\n", "      <td>88</td>\n", "      <td>...</td>\n", "      <td>90</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>90</td>\n", "      <td>88</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>83</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "      <td>80</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1219 rows × 24 columns</p>\n", "</div>"], "text/plain": ["     school_name_cn                                     school_name_en  \\\n", "0              清华大学                                Tsinghua University   \n", "1              北京大学                                  Peking University   \n", "2              浙江大学                                Zhejiang University   \n", "3            上海交通大学                      Shanghai Jiao Tong University   \n", "4              南京大学                                 Nanjing University   \n", "...             ...                                                ...   \n", "1214       四川电影电视学院                Sichuan Film and Television College   \n", "1215       四川文化艺术学院                Sichuan College of Culture and Arts   \n", "1216         武汉传媒学院                  Wuhan University of Communication   \n", "1217         青岛电影学院  Beijing Film Academy Modern Creative Media Col...   \n", "1218         昆明传媒学院           Wenhua College of Yunnan Arts University   \n", "\n", "     school_tier  帝国理工学院  牛津大学  剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  ...  \\\n", "0            985      85    85    85      85        85     85      85  ...   \n", "1            985      85    85    85      85        85     85      85  ...   \n", "2            985      85    85    85      85        85     85      85  ...   \n", "3            985      85    85    85      85        85     85      85  ...   \n", "4            985      85    85    85      85        85     85      85  ...   \n", "...          ...     ...   ...   ...     ...       ...    ...     ...  ...   \n", "1214         NaN    <NA>    90    90      90        88     87      88  ...   \n", "1215         NaN    <NA>    90    90      90        88     87      88  ...   \n", "1216         NaN    <NA>    90    90      90        88     87      88  ...   \n", "1217         NaN    <NA>    90    90      90        88     87      88  ...   \n", "1218         NaN    <NA>    90    90      90        88     87      88  ...   \n", "\n", "      格拉斯哥大学  利兹大学  南安普顿大学  谢菲尔德大学  杜伦大学  诺丁汉大学  圣安德鲁斯大学  巴斯大学  埃克塞特大学  \\\n", "0         70    75      75      73    80     75       72    75      75   \n", "1         70    75      75      73    80     75       72    75      75   \n", "2         70    75      75      73    80     75       72    75      75   \n", "3         70    75      75      73    80     75       72    75      75   \n", "4         70    75      75      73    80     75       72    75      75   \n", "...      ...   ...     ...     ...   ...    ...      ...   ...     ...   \n", "1214      90    80      90      88  <NA>     83     <NA>    80      80   \n", "1215      90    80      90      88  <NA>     83     <NA>    80      80   \n", "1216      90    80      90      88  <NA>     83     <NA>    80      80   \n", "1217      90  <NA>      90      88  <NA>     83     <NA>    80      80   \n", "1218      90  <NA>      90      88  <NA>     83     <NA>    80      80   \n", "\n", "      贝尔法斯特女王大学  \n", "0            75  \n", "1            75  \n", "2            75  \n", "3            75  \n", "4            75  \n", "...         ...  \n", "1214         80  \n", "1215         80  \n", "1216         80  \n", "1217         80  \n", "1218         80  \n", "\n", "[1219 rows x 24 columns]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df = pd.read_csv('院校数据/暂存院校GPA要求.csv')\n", "# 选择所有数值列并将其类型转换为Int64\n", "numeric_cols = uk_school_gpa_df.select_dtypes(include=['number']).columns\n", "uk_school_gpa_df[numeric_cols] = uk_school_gpa_df[numeric_cols].astype('Int64')\n", "uk_school_gpa_df"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "      <th>school_name_en</th>\n", "      <th>school_tier</th>\n", "      <th>gpa_requirements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>综合</td>\n", "      <td>4111010003</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>Tsinghua University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>综合</td>\n", "      <td>4111010001</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>Peking University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>综合</td>\n", "      <td>4133010335</td>\n", "      <td>杭州市</td>\n", "      <td>教育部</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>Zhejiang University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>综合</td>\n", "      <td>4131010248</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>复旦大学</td>\n", "      <td>综合</td>\n", "      <td>4131010246</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>Fudan University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1305</th>\n", "      <td>黑龙江工程学院昆仑旅游学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013304</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1306</th>\n", "      <td>黑龙江财经学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013298</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Heilongjiang University of Finance and Economics</td>\n", "      <td>NaN</td>\n", "      <td>{'帝国理工学院': None, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1307</th>\n", "      <td>齐鲁医药学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>淄博市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qilu Medical University</td>\n", "      <td>NaN</td>\n", "      <td>{'帝国理工学院': None, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1308</th>\n", "      <td>齐鲁理工学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>济南市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qilu Institute of Technology</td>\n", "      <td>NaN</td>\n", "      <td>{'帝国理工学院': None, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1309</th>\n", "      <td>齐齐哈尔工程学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>齐齐哈尔市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qiqihar Institute of Technology</td>\n", "      <td>NaN</td>\n", "      <td>{'帝国理工学院': None, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1310 rows × 10 columns</p>\n", "</div>"], "text/plain": ["               学校名称 学校类型       学校标识码    所在地     主管部门  软科排名   备注  \\\n", "0              清华大学   综合  4111010003    北京市      教育部     1  NaN   \n", "1              北京大学   综合  4111010001    北京市      教育部     2  NaN   \n", "2              浙江大学   综合  4133010335    杭州市      教育部     3  NaN   \n", "3            上海交通大学   综合  4131010248    上海市      教育部     4  NaN   \n", "4              复旦大学   综合  4131010246    上海市      教育部     5  NaN   \n", "...             ...  ...         ...    ...      ...   ...  ...   \n", "1305  黑龙江工程学院昆仑旅游学院  NaN  4123013304   哈尔滨市  黑龙江省教育厅  <NA>   民办   \n", "1306        黑龙江财经学院  NaN  4123013298   哈尔滨市  黑龙江省教育厅  <NA>   民办   \n", "1307         齐鲁医药学院  NaN  **********    淄博市   山东省教育厅  <NA>   民办   \n", "1308         齐鲁理工学院  NaN  **********    济南市   山东省教育厅  <NA>   民办   \n", "1309       齐齐哈尔工程学院  NaN  **********  齐齐哈尔市  黑龙江省教育厅  <NA>   民办   \n", "\n", "                                        school_name_en school_tier  \\\n", "0                                  Tsinghua University         985   \n", "1                                    Peking University         985   \n", "2                                  Zhejiang University         985   \n", "3                        Shanghai Jiao Tong University         985   \n", "4                                     Fudan University         985   \n", "...                                                ...         ...   \n", "1305                                               NaN         NaN   \n", "1306  Heilongjiang University of Finance and Economics         NaN   \n", "1307                           Qilu Medical University         NaN   \n", "1308                      Qilu Institute of Technology         NaN   \n", "1309                   Qiqihar Institute of Technology         NaN   \n", "\n", "                                       gpa_requirements  \n", "0     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "1     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "2     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "3     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "4     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "...                                                 ...  \n", "1305                                                 {}  \n", "1306  {'帝国理工学院': None, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1307  {'帝国理工学院': None, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1308  {'帝国理工学院': None, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1309  {'帝国理工学院': None, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "\n", "[1310 rows x 10 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["test_home_school_df = pd.read_csv('院校数据/境内院校数据库（初版list）.csv', dtype={'学校标识码': 'str', '软科排名': 'Int64'})\n", "\n", "# 定义一个函数来解析 gpa_requirements 列\n", "def parse_gpa_requirements(s):\n", "    if pd.isna(s):  # 检查是否为 NaN\n", "        return {}\n", "    \n", "    # 使用正则表达式将 <NA> 替换为 None\n", "    s = re.sub(r'<NA>', 'None', s)\n", "    # 使用 ast.literal_eval 解析字符串\n", "    parsed_dict = ast.literal_eval(s)\n", "    return parsed_dict\n", "\n", "# 应用解析函数\n", "test_home_school_df['gpa_requirements'] = test_home_school_df['gpa_requirements'].apply(parse_gpa_requirements)\n", "test_home_school_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 以下部分英国院校GPA要求调整："]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 牛津剑桥：\n", "  \n", "    剑桥：prestigious uni - 85；good uni - 88；others - 90\n", "\n", "    牛津：85% from a Double First Class University, former Project 985 or Project 211 institution or 90% from other institutions."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["剑桥大学\n", "90    1143\n", "85      76\n", "Name: count, dtype: Int64\n", "牛津大学\n", "90    1143\n", "85      76\n", "Name: count, dtype: Int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_tier</th>\n", "      <th>帝国理工学院</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>...</th>\n", "      <th>格拉斯哥大学</th>\n", "      <th>利兹大学</th>\n", "      <th>南安普顿大学</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>圣安德鲁斯大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>兰州大学</td>\n", "      <td>Lanzhou University</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>中国海洋大学</td>\n", "      <td>Ocean University of China</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>西北农林科技大学</td>\n", "      <td>Northwest A&amp;F University or Northwest Agricult...</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>中央民族大学</td>\n", "      <td>Minzu University of China</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4 rows × 24 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                                     school_name_en  \\\n", "40            兰州大学                                 Lanzhou University   \n", "63          中国海洋大学                          Ocean University of China   \n", "87        西北农林科技大学  Northwest A&F University or Northwest Agricult...   \n", "123         中央民族大学                          Minzu University of China   \n", "\n", "    school_tier  帝国理工学院  牛津大学  剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  ...  \\\n", "40          985      85    85    85      85        85     85      85  ...   \n", "63          985      85    85    85      85        85     85      85  ...   \n", "87          985      85    90    90      90        85     85      85  ...   \n", "123         985      85    90    90      90        85     85      85  ...   \n", "\n", "     格拉斯哥大学  利兹大学  南安普顿大学  谢菲尔德大学  杜伦大学  诺丁汉大学  圣安德鲁斯大学  巴斯大学  埃克塞特大学  \\\n", "40       70    75      75      73    80     75       75    75      75   \n", "63       70    75      75      73    80     75       75    75      75   \n", "87       70    75      75      73    80     75       75    75      75   \n", "123      70    75      75      75    80     75       75    75      75   \n", "\n", "     贝尔法斯特女王大学  \n", "40          75  \n", "63          75  \n", "87          80  \n", "123         80  \n", "\n", "[4 rows x 24 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["print(uk_school_gpa_df['剑桥大学'].value_counts())\n", "print(uk_school_gpa_df['牛津大学'].value_counts())\n", "uk_school_gpa_df[uk_school_gpa_df['school_tier'] == '985'].tail(4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 985对于牛剑都在应该同一个分数等级（见官网），但初始的分数要求中 西北农林和中央民族 分数要求比其他985高...需要修正。目前92学校的数目也是和85分要求数目（76所学校）对不上的。"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["school_tier\n", "NaN     1101\n", "211       74\n", "985       38\n", "中外合办       6\n", "Name: count, dtype: int64"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df['school_tier'].value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["剑桥大学\n", "90    1101\n", "88      80\n", "85      38\n", "Name: count, dtype: Int64\n", "牛津大学\n", "90    1101\n", "85     118\n", "Name: count, dtype: Int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_tier</th>\n", "      <th>帝国理工学院</th>\n", "      <th>牛津大学</th>\n", "      <th>剑桥大学</th>\n", "      <th>伦敦大学学院</th>\n", "      <th>伦敦大学国王学院</th>\n", "      <th>爱丁堡大学</th>\n", "      <th>曼彻斯特大学</th>\n", "      <th>...</th>\n", "      <th>格拉斯哥大学</th>\n", "      <th>利兹大学</th>\n", "      <th>南安普顿大学</th>\n", "      <th>谢菲尔德大学</th>\n", "      <th>杜伦大学</th>\n", "      <th>诺丁汉大学</th>\n", "      <th>圣安德鲁斯大学</th>\n", "      <th>巴斯大学</th>\n", "      <th>埃克塞特大学</th>\n", "      <th>贝尔法斯特女王大学</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>兰州大学</td>\n", "      <td>Lanzhou University</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>中国海洋大学</td>\n", "      <td>Ocean University of China</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>西北农林科技大学</td>\n", "      <td>Northwest A&amp;F University or Northwest Agricult...</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>73</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>中央民族大学</td>\n", "      <td>Minzu University of China</td>\n", "      <td>985</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>90</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>85</td>\n", "      <td>...</td>\n", "      <td>70</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>75</td>\n", "      <td>80</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4 rows × 24 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                                     school_name_en  \\\n", "40            兰州大学                                 Lanzhou University   \n", "63          中国海洋大学                          Ocean University of China   \n", "87        西北农林科技大学  Northwest A&F University or Northwest Agricult...   \n", "123         中央民族大学                          Minzu University of China   \n", "\n", "    school_tier  帝国理工学院  牛津大学  剑桥大学  伦敦大学学院  伦敦大学国王学院  爱丁堡大学  曼彻斯特大学  ...  \\\n", "40          985      85    85    85      85        85     85      85  ...   \n", "63          985      85    85    85      85        85     85      85  ...   \n", "87          985      85    85    85      90        85     85      85  ...   \n", "123         985      85    85    85      90        85     85      85  ...   \n", "\n", "     格拉斯哥大学  利兹大学  南安普顿大学  谢菲尔德大学  杜伦大学  诺丁汉大学  圣安德鲁斯大学  巴斯大学  埃克塞特大学  \\\n", "40       70    75      75      73    80     75       75    75      75   \n", "63       70    75      75      73    80     75       75    75      75   \n", "87       70    75      75      73    80     75       75    75      75   \n", "123      70    75      75      75    80     75       75    75      75   \n", "\n", "     贝尔法斯特女王大学  \n", "40          75  \n", "63          75  \n", "87          80  \n", "123         80  \n", "\n", "[4 rows x 24 columns]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# 修改剑桥大学列的 GPA 要求\n", "uk_school_gpa_df.loc[uk_school_gpa_df['school_tier'] == '985', '剑桥大学'] = 85\n", "uk_school_gpa_df.loc[(uk_school_gpa_df['school_tier'] == '211') | (uk_school_gpa_df['school_tier'] == '中外合办'), '剑桥大学'] = 88\n", "uk_school_gpa_df.loc[~((uk_school_gpa_df['school_tier'] == '985') | \n", "                        (uk_school_gpa_df['school_tier'] == '211') | \n", "                        (uk_school_gpa_df['school_tier'] == '中外合办')), '剑桥大学'] = 90\n", "\n", "# 修改牛津大学列的 GPA 要求\n", "uk_school_gpa_df.loc[(uk_school_gpa_df['school_tier'] == '985') | \n", "                     (uk_school_gpa_df['school_tier'] == '211') | \n", "                     (uk_school_gpa_df['school_tier'] == '中外合办'), '牛津大学'] = 85\n", "uk_school_gpa_df.loc[~((uk_school_gpa_df['school_tier'] == '985') | \n", "                        (uk_school_gpa_df['school_tier'] == '211') | \n", "                        (uk_school_gpa_df['school_tier'] == '中外合办')), '牛津大学'] = 90\n", "\n", "# 结果检查\n", "print(uk_school_gpa_df['剑桥大学'].value_counts())\n", "print(uk_school_gpa_df['牛津大学'].value_counts())\n", "uk_school_gpa_df[uk_school_gpa_df['school_tier'] == '985'].tail(4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### UCL和LSE 2等1的GPA要求好像确实就这么高..."]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["伦敦大学学院\n", "90    1143\n", "85      76\n", "Name: count, dtype: Int64\n", "伦敦政治经济学院\n", "90    1180\n", "85      39\n", "Name: count, dtype: Int64\n"]}], "source": ["print(uk_school_gpa_df['伦敦大学学院'].value_counts())\n", "print(uk_school_gpa_df['伦敦政治经济学院'].value_counts())\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 王爱曼的GPA要求偏高...对所有学校都最低85有问题..."]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["伦敦大学国王学院\n", "88    1077\n", "85     142\n", "Name: count, dtype: Int64\n", "爱丁堡大学\n", "87    1080\n", "85     139\n", "Name: count, dtype: Int64\n", "曼彻斯特大学\n", "88    1077\n", "85     142\n", "Name: count, dtype: Int64\n"]}], "source": ["print(uk_school_gpa_df['伦敦大学国王学院'].value_counts())\n", "print(uk_school_gpa_df['爱丁堡大学'].value_counts())\n", "print(uk_school_gpa_df['曼彻斯特大学'].value_counts())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["曼大： core list ( Top 141 所; Tier 1 68所）; 其他表述：From a ranked (China = Top/Tier 1 institutions) / From an acceptable institution  tier2/3是某个学院可能具体的划分。曼彻斯特大学把国内院校分成3类，top university141所，tier1院校68所，还有的都叫其他院校。top university和tier 1 university合起来，叫core university，一共是209所\n", "- 理工科：CS85(1) EEE83/85(1) 80/83(8) 化学地科80(7) 82(4) 材料75/80(8) 数学86(5)\n", "- 环发人文社科：调整后list中的学校全80分，之前三档学校之间各差2分\n", "- 商学院：严卡list，会计金融（36所学校）管理（81所学校） 81/83/85"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["伦敦大学国王学院\n", "85    1077\n", "80     142\n", "Name: count, dtype: Int64\n", "爱丁堡大学\n", "85    1080\n", "80     139\n", "Name: count, dtype: Int64\n", "曼彻斯特大学\n", "83    1077\n", "80     142\n", "Name: count, dtype: Int64\n"]}], "source": ["# 修改伦敦大学国王学院列的 GPA 要求\n", "uk_school_gpa_df.loc[uk_school_gpa_df['伦敦大学国王学院'] == 85, '伦敦大学国王学院'] = 80\n", "uk_school_gpa_df.loc[uk_school_gpa_df['伦敦大学国王学院'] == 88, '伦敦大学国王学院'] = 85\n", "\n", "\n", "# 修改爱丁堡大学列的 GPA 要求\n", "uk_school_gpa_df.loc[uk_school_gpa_df['爱丁堡大学'] == 85, '爱丁堡大学'] = 80\n", "uk_school_gpa_df.loc[uk_school_gpa_df['爱丁堡大学'] == 87, '爱丁堡大学'] = 85\n", "\n", "\n", "# 修改曼彻斯特大学列的 GPA 要求\n", "uk_school_gpa_df.loc[uk_school_gpa_df['曼彻斯特大学'] == 85, '曼彻斯特大学'] = 80\n", "uk_school_gpa_df.loc[uk_school_gpa_df['曼彻斯特大学'] == 88, '曼彻斯特大学'] = 83\n", "\n", "\n", "# 检查\n", "print(uk_school_gpa_df['伦敦大学国王学院'].value_counts())\n", "print(uk_school_gpa_df['爱丁堡大学'].value_counts())\n", "print(uk_school_gpa_df['曼彻斯特大学'].value_counts())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 不在List中的院校是否过少？（即GPA要求直接为空值...代表该学校直接就不在List中，申请不了该学校...）"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["school_name_cn       0\n", "school_name_en       0\n", "school_tier       1101\n", "帝国理工学院            1101\n", "牛津大学                 0\n", "剑桥大学                 0\n", "伦敦大学学院               0\n", "伦敦大学国王学院             0\n", "爱丁堡大学                0\n", "曼彻斯特大学               0\n", "布里斯托大学             947\n", "伦敦政治经济学院             0\n", "华威大学                 0\n", "伯明翰大学                0\n", "格拉斯哥大学               0\n", "利兹大学                50\n", "南安普顿大学               0\n", "谢菲尔德大学               0\n", "杜伦大学               699\n", "诺丁汉大学                0\n", "圣安德鲁斯大学            434\n", "巴斯大学                 0\n", "埃克塞特大学               0\n", "贝尔法斯特女王大学            0\n", "dtype: int64"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 诺丁汉基本没问题，各分数等级能对上原始List中各等级的数量。之后再细分2：2专业（占比10%）和法学院专业即可。诺丁汉也确实是所有学校都可以申请（其他都算第三等级学校...）"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["诺丁汉大学\n", "83    794\n", "78    255\n", "75    170\n", "Name: count, dtype: Int64"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["uk_school_gpa_df['诺丁汉大学'].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 导出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "      <th>school_name_en</th>\n", "      <th>school_tier</th>\n", "      <th>gpa_requirements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>综合</td>\n", "      <td>4111010003</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>Tsinghua University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>综合</td>\n", "      <td>4111010001</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>Peking University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>综合</td>\n", "      <td>4133010335</td>\n", "      <td>杭州市</td>\n", "      <td>教育部</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>Zhejiang University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>综合</td>\n", "      <td>4131010248</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "      <td>Shanghai Jiao Tong University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>复旦大学</td>\n", "      <td>综合</td>\n", "      <td>4131010246</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>Fudan University</td>\n", "      <td>985</td>\n", "      <td>{'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1305</th>\n", "      <td>黑龙江工程学院昆仑旅游学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013304</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1306</th>\n", "      <td>黑龙江财经学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013298</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Heilongjiang University of Finance and Economics</td>\n", "      <td>NaN</td>\n", "      <td>{'帝国理工学院': &lt;NA&gt;, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1307</th>\n", "      <td>齐鲁医药学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>淄博市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qilu Medical University</td>\n", "      <td>NaN</td>\n", "      <td>{'帝国理工学院': &lt;NA&gt;, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1308</th>\n", "      <td>齐鲁理工学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>济南市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qilu Institute of Technology</td>\n", "      <td>NaN</td>\n", "      <td>{'帝国理工学院': &lt;NA&gt;, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1309</th>\n", "      <td>齐齐哈尔工程学院</td>\n", "      <td>NaN</td>\n", "      <td>**********</td>\n", "      <td>齐齐哈尔市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "      <td>Qiqihar Institute of Technology</td>\n", "      <td>NaN</td>\n", "      <td>{'帝国理工学院': &lt;NA&gt;, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1310 rows × 10 columns</p>\n", "</div>"], "text/plain": ["               学校名称 学校类型       学校标识码    所在地     主管部门  软科排名   备注  \\\n", "0              清华大学   综合  4111010003    北京市      教育部     1  NaN   \n", "1              北京大学   综合  4111010001    北京市      教育部     2  NaN   \n", "2              浙江大学   综合  4133010335    杭州市      教育部     3  NaN   \n", "3            上海交通大学   综合  4131010248    上海市      教育部     4  NaN   \n", "4              复旦大学   综合  4131010246    上海市      教育部     5  NaN   \n", "...             ...  ...         ...    ...      ...   ...  ...   \n", "1305  黑龙江工程学院昆仑旅游学院  NaN  4123013304   哈尔滨市  黑龙江省教育厅  <NA>   民办   \n", "1306        黑龙江财经学院  NaN  4123013298   哈尔滨市  黑龙江省教育厅  <NA>   民办   \n", "1307         齐鲁医药学院  NaN  **********    淄博市   山东省教育厅  <NA>   民办   \n", "1308         齐鲁理工学院  NaN  **********    济南市   山东省教育厅  <NA>   民办   \n", "1309       齐齐哈尔工程学院  NaN  **********  齐齐哈尔市  黑龙江省教育厅  <NA>   民办   \n", "\n", "                                        school_name_en school_tier  \\\n", "0                                  Tsinghua University         985   \n", "1                                    Peking University         985   \n", "2                                  Zhejiang University         985   \n", "3                        Shanghai Jiao Tong University         985   \n", "4                                     Fudan University         985   \n", "...                                                ...         ...   \n", "1305                                               NaN         NaN   \n", "1306  Heilongjiang University of Finance and Economics         NaN   \n", "1307                           Qilu Medical University         NaN   \n", "1308                      Qilu Institute of Technology         NaN   \n", "1309                   Qiqihar Institute of Technology         NaN   \n", "\n", "                                       gpa_requirements  \n", "0     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "1     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "2     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "3     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "4     {'帝国理工学院': 85, '牛津大学': 85, '剑桥大学': 85, '伦敦大学学院...  \n", "...                                                 ...  \n", "1305                                                NaN  \n", "1306  {'帝国理工学院': <NA>, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1307  {'帝国理工学院': <NA>, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1308  {'帝国理工学院': <NA>, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "1309  {'帝国理工学院': <NA>, '牛津大学': 90, '剑桥大学': 90, '伦敦大学...  \n", "\n", "[1310 rows x 10 columns]"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["home_school_df = pd.read_csv('院校数据/境内院校数据库（初始无list版）.csv', dtype={'学校标识码': 'str', '软科排名': 'Int64'})\n", "\n", "# 将各列转成字典\n", "# 获取需要转换的列名（排除 school_name_cn, school_name_en, school_tier）\n", "columns_to_convert = [col for col in uk_school_gpa_df.columns if col not in ['school_name_cn', 'school_name_en', 'school_tier']]\n", "\n", "# 创建一个新的 DataFrame 来存储结果\n", "result_df = pd.DataFrame(columns=['school_name_cn', 'school_name_en', 'school_tier', 'gpa_requirements'])\n", "\n", "# 遍历每一行并转换\n", "for index, row in uk_school_gpa_df.iterrows():\n", "    gpa_dict = {col: row[col] for col in columns_to_convert}\n", "    result_df.loc[index] = [row['school_name_cn'], row['school_name_en'], row['school_tier'], gpa_dict]\n", "\n", "home_school_df = pd.merge(home_school_df, result_df, left_on='学校名称', right_on='school_name_cn', how='left')\n", "home_school_df.drop(columns=['school_name_cn'], inplace=True)\n", "home_school_df\n", "\n", "# uk_school_gpa_df.to_csv('院校数据/暂存院校GPA要求2.csv', index=False, encoding='utf-8-sig')\n", "# home_school_df.to_csv('境内院校数据库.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 教育部院校名单中未在GPA名单中的学校处理分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["检查哪些教育部名单学校没有list...要设定一个分数...??"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["99个院校名出现在教育部全国普通高等学校名单中，但未出现在GPA名单中\n"]}, {"data": {"text/plain": ["{'上海中侨职业技术大学',\n", " '上海公安学院',\n", " '上海兴伟学院',\n", " '上海立达学院',\n", " '中国消防救援学院',\n", " '中国科学院大学',\n", " '中国青年政治学院',\n", " '丽江师范学院',\n", " '南京中医药大学翰林学院',\n", " '南京工业职业技术大学',\n", " '南宁师范大学师园学院',\n", " '南宁职业技术大学',\n", " '南昌职业大学',\n", " '厦门医学院',\n", " '合肥理工学院',\n", " '哈尔滨职业技术大学',\n", " '唐山工业职业技术大学',\n", " '四川工程职业技术大学',\n", " '天津中德应用技术大学',\n", " '安徽公安学院',\n", " '安徽艺术学院',\n", " '山东外事职业大学',\n", " '山东外国语职业技术大学',\n", " '山东工程职业技术大学',\n", " '山西工学院',\n", " '广东以色列理工学院',\n", " '广东工商职业技术大学',\n", " '广东轻工职业技术大学',\n", " '广州科技职业技术大学',\n", " '广西城市职业大学',\n", " '广西职业师范学院',\n", " '康复大学',\n", " '成都艺术职业大学',\n", " '新疆农业大学科学技术学院',\n", " '新疆农业职业技术大学',\n", " '新疆和田学院',\n", " '新疆天山职业技术大学',\n", " '昆山杜克大学',\n", " '朝阳师范学院',\n", " '柳州职业技术大学',\n", " '民政职业大学',\n", " '江西职业技术大学',\n", " '江西软件职业技术大学',\n", " '江西飞行学院',\n", " '沈阳工业大学工程学院',\n", " '沈阳航空航天大学北方科技学院',\n", " '河北水利电力学院',\n", " '河南体育学院',\n", " '河南科技职业大学',\n", " '泉州职业技术大学',\n", " '浙江广厦建设职业技术大学',\n", " '浙江机电职业技术大学',\n", " '海南比勒费尔德应用科学大学',\n", " '海南科技职业大学',\n", " '深圳北理莫斯科大学',\n", " '深圳技术大学',\n", " '深圳理工大学',\n", " '深圳职业技术大学',\n", " '湖南中医药大学湘杏学院',\n", " '湖南农业大学东方科技学院',\n", " '湖南工业大学科技学院',\n", " '湖南汽车工程职业大学',\n", " '湖南理工学院南湖学院',\n", " '湖南科技大学潇湘学院',\n", " '湖南软件职业技术大学',\n", " '湘潭理工学院',\n", " '滇西应用技术大学',\n", " '漯河食品工程职业大学',\n", " '甘肃林业职业技术大学',\n", " '甘肃警察学院',\n", " '福建商学院',\n", " '肇庆医学院',\n", " '苏州科技大学天平学院',\n", " '茅台学院',\n", " '西安信息职业大学',\n", " '西安汽车职业大学',\n", " '西湖大学',\n", " '贵州交通职业大学',\n", " '贵州黔南科技学院',\n", " '辽宁理工职业大学',\n", " '运城职业技术大学',\n", " '邢台医学院',\n", " '郑州美术学院',\n", " '重庆中医药学院',\n", " '重庆机电职业技术大学',\n", " '重庆电子科技职业大学',\n", " '金华职业技术大学',\n", " '长安大学兴华学院',\n", " '长春汽车职业技术大学',\n", " '长沙工业学院',\n", " '长沙理工大学城南学院',\n", " '陇南师范学院',\n", " '陕西警察学院',\n", " '青海理工学院',\n", " '青海职业技术大学',\n", " '首钢工学院',\n", " '香港城市大学（东莞）',\n", " '香港科技大学（广州）',\n", " '黑龙江工程学院昆仑旅游学院'}"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["missing_schools = set(home_school_df['学校名称']) - set(uk_school_gpa_df['school_name_cn'])\n", "print(f'{len(missing_schools)}个院校名出现在教育部全国普通高等学校名单中，但未出现在GPA名单中')\n", "missing_schools"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>昆山杜克大学</td>\n", "      <td>综合</td>\n", "      <td>4132016406</td>\n", "      <td>昆山市</td>\n", "      <td>江苏省教育厅</td>\n", "      <td>103</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>深圳北理莫斯科大学</td>\n", "      <td>综合</td>\n", "      <td>4144016409</td>\n", "      <td>深圳市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>106</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "    </tr>\n", "    <tr>\n", "      <th>406</th>\n", "      <td>深圳技术大学</td>\n", "      <td>综合</td>\n", "      <td>4144014655</td>\n", "      <td>深圳市</td>\n", "      <td>广东省</td>\n", "      <td>294</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>529</th>\n", "      <td>厦门医学院</td>\n", "      <td>医药</td>\n", "      <td>4135012631</td>\n", "      <td>厦门市</td>\n", "      <td>福建省</td>\n", "      <td>376</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>646</th>\n", "      <td>上海公安学院</td>\n", "      <td>政法</td>\n", "      <td>4131010283</td>\n", "      <td>上海市</td>\n", "      <td>上海市</td>\n", "      <td>469</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1292</th>\n", "      <td>青海职业技术大学</td>\n", "      <td>NaN</td>\n", "      <td>4163012973</td>\n", "      <td>西宁市</td>\n", "      <td>青海省</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1294</th>\n", "      <td>首钢工学院</td>\n", "      <td>NaN</td>\n", "      <td>4111011831</td>\n", "      <td>北京市</td>\n", "      <td>北京市</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1295</th>\n", "      <td>香港城市大学（东莞）</td>\n", "      <td>NaN</td>\n", "      <td>4144014851</td>\n", "      <td>东莞市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1296</th>\n", "      <td>香港科技大学（广州）</td>\n", "      <td>NaN</td>\n", "      <td>4144016412</td>\n", "      <td>广州市</td>\n", "      <td>广东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>中外合作办学及内地与港澳合作办学</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1305</th>\n", "      <td>黑龙江工程学院昆仑旅游学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013304</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>99 rows × 7 columns</p>\n", "</div>"], "text/plain": ["               学校名称 学校类型       学校标识码   所在地     主管部门  软科排名                备注\n", "130          昆山杜克大学   综合  4132016406   昆山市   江苏省教育厅   103  中外合作办学及内地与港澳合作办学\n", "137       深圳北理莫斯科大学   综合  4144016409   深圳市   广东省教育厅   106  中外合作办学及内地与港澳合作办学\n", "406          深圳技术大学   综合  4144014655   深圳市      广东省   294               NaN\n", "529           厦门医学院   医药  4135012631   厦门市      福建省   376               NaN\n", "646          上海公安学院   政法  4131010283   上海市      上海市   469               NaN\n", "...             ...  ...         ...   ...      ...   ...               ...\n", "1292       青海职业技术大学  NaN  4163012973   西宁市      青海省  <NA>               NaN\n", "1294          首钢工学院  NaN  4111011831   北京市      北京市  <NA>               NaN\n", "1295     香港城市大学（东莞）  NaN  4144014851   东莞市   广东省教育厅  <NA>  中外合作办学及内地与港澳合作办学\n", "1296     香港科技大学（广州）  NaN  4144016412   广州市   广东省教育厅  <NA>  中外合作办学及内地与港澳合作办学\n", "1305  黑龙江工程学院昆仑旅游学院  NaN  4123013304  哈尔滨市  黑龙江省教育厅  <NA>                民办\n", "\n", "[99 rows x 7 columns]"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["home_school_df[home_school_df['学校名称'].isin(missing_schools)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["保证每个都有gpa requirement"]}, {"cell_type": "markdown", "metadata": {}, "source": ["几所重要大学可以手动查询添加分数要求..."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# school_name_mapping_dict = {\n", "#     '湖州师范大学': '湖州师范学院',\n", "#     '牡丹江医学院': '牡丹江医科大学',\n", "#     '贵州财经大学商务学院': '贵州黔南经济学院',\n", "#     '海南医学院': '海南医科大学',\n", "#     '四川外国语大学成都学院': '成都外国语学院',\n", "#     '桂林电子科技大学信息科技学院': '桂林信息科技学院',\n", "#     '桂林理工大学博文管理学院': '南宁理工学院',\n", "#     '江苏警官学校': '江苏警官学院',\n", "#     '淮北师范大学信息学院': '淮北理工学院',\n", "#     '西北师范大学知行学院': '兰州石化职业技术大学',\n", "#     '赣南医学院': '赣南医科大学',\n", "#     '中国矿业大学银川学院': '银川科技学院',\n", "#     '福建工程学院': '福建理工大学',\n", "#     '浙江海洋大学东海科学技术学院': '浙江药科职业大学',\n", "#     '兰州财经大学长青学院': '兰州资源环境职业技术大学',\n", "#     '北京工商大学嘉华学院': '北京金融科技学院',\n", "#     '上海体育学院': '上海体育大学',\n", "#     '香港中文大学': '香港中文大学（深圳）',\n", "#     '合肥学院': '合肥大学',\n", "#     '潍坊医学院': '山东第二医科大学',\n", "#     '东莞理工学院城市学院': '东莞城市学院',\n", "#     '广西中医药大学塞恩斯新医药学院': '广西中医药大学赛恩斯新医药学院',\n", "#     '河南科技学院新科学院': '新乡工程学院',\n", "#     '滨州学院': '山东航空学院',\n", "#     '云南师范大学商学院': '昆明城市学院',\n", "#     '重庆科技学院': '重庆科技大学',\n", "#     '信阳师范学院': '信阳师范大学',\n", "#     '江苏森林警察学院': '南京警察学院',\n", "#     '山西师范大学现代文理学院': '山西电子科技学院',\n", "#     '西南科技大学城市学院': '绵阳城市学院',\n", "#     '河北中医学院': '河北中医药大学',\n", "#     '阜阳师范大学信息工程学院': '阜阳理工学院',\n", "#     '吉首大学张家界学院': '张家界学院',\n", "#     '太原科技大学华科学院': '山西科技学院',\n", "#     '铁道警察学院': '郑州警察学院',\n", "#     '广西师范大学漓江学院': '桂林学院',\n", "#     '贵州民族大学人文科技学院': '贵阳人文科技学院',\n", "#     '宁夏师范学院': '宁夏师范大学',\n", "#     '浙江科技学院': '浙江科技大学',\n", "#     '长江大学工程技术学院': '荆州学院',\n", "#     '济南大学泉城学院': '烟台科技学院',\n", "#     '广西大学行健文理学院': '广西农业职业技术大学',\n", "#     '北京电影学院现代创意媒体学院': '青岛电影学院',\n", "#     '嘉兴学院': '嘉兴大学',\n", "#     '云南大学滇池学院': '滇池学院',\n", "#     '云南艺术学院文化学院': '昆明传媒学院',\n", "#     '安徽师范大学皖江学院': '芜湖学院',\n", "#     '蚌埠医学院': '蚌埠医科大学',\n", "#     '佛山科学技术学院': '佛山大学',\n", "#     '贵州师范大学求是学院': '贵阳康养职业大学',\n", "#     '陕西国际贸易商贸学院': '陕西国际商贸学院',\n", "#     '中国社会科学院': '中国社会科学院大学',\n", "#     '广东工业大学华立学院': '广州华立学院',\n", "#     '四川大学锦城学院': '成都锦城学院',\n", "#     '贵州大学明德学院': '贵阳信息科技学院',\n", "#     '南昌工程学院': '江西水利电力大学',\n", "#     '吉林化工学院': '吉林化工大学',\n", "#     '天水师范学院': '天水师范大学',\n", "#     '山西医科大学晋祠学院': '晋中健康学院',\n", "#     '常熟理工学院': '苏州工学院',\n", "#     '新乡医学院': '河南医药大学',\n", "#     '新乡医学院三全学院': '豫北医学院',\n", "#     '桂林医学院': '桂林医科大学',\n", "#     '湖南文理学院芙蓉学院': '常德学院',\n", "#     '湖南理工学院南湖学院': '岳阳学院',\n", "#     '绍兴文理学院元培学院': '绍兴理工学院',\n", "#     '西藏农牧学院': '西藏农牧大学',\n", "# }\n", "\n", "# with open('院校数据/院校List/school_name_mapping.json', 'w') as f:\n", "#     json.dump(school_name_mapping_dict, f, ensure_ascii=False, indent=4)"]}], "metadata": {"kernelspec": {"display_name": "tunshu_data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}