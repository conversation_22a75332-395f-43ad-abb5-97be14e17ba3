import pandas as pd
import numpy as np
from datetime import datetime

from sqlalchemy import (
    Column,
    Text, 
    Integer, 
    String, 
    Float, 
    ForeignKey,
    DateTime,
    UniqueConstraint
)
from sqlalchemy.dialects.postgresql import JSONB

from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.dialects.postgresql import insert as pg_insert # 导入upsert功能


# --- 1. 数据库和模型设置 ---
Base = declarative_base()

class AISelectionProgram(Base):
    """
    项目数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_programs 表
    存储项目信息数据
    """
    __tablename__ = "ai_selection_programs"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校信息
    school_name_cn = Column(String(50), nullable=False, index=True, comment="学校中文名")
    school_name_en = Column(String(100), nullable=True, index=True, comment="学校英文名")
    school_region = Column(String(10), nullable=True, comment="学校所在地区")
    school_labels = Column(String(200), nullable=True, comment="学校标签")
    school_ranks = Column(String(100), nullable=True, comment="学校排名信息")
    school_qs_rank = Column(String(20), nullable=True, comment="学校QS排名")

    # 项目基本信息
    program_code = Column(Integer, nullable=True, comment="项目代码")
    program_website = Column(String(300), nullable=True, comment="项目官网")
    program_name_cn = Column(String(100), nullable=False, index=True, comment="项目中文名")
    program_name_en = Column(String(200), nullable=True, index=True, comment="项目英文名")
    program_category = Column(String(50), nullable=True, comment="项目类别")
    program_direction = Column(String(50), nullable=True, comment="项目方向")
    faculty = Column(String(50), nullable=True, comment="所在学院")
    enrollment_time = Column(String(50), nullable=True, comment="入学时间")
    program_duration = Column(String(50), nullable=True, comment="项目时长")
    program_tuition = Column(String(100), nullable=True, comment="项目学费")
    
    # 项目申请相关信息
    application_time = Column(Text, nullable=True, comment="申请时间")
    application_requirements = Column(Text, nullable=True, comment="申请要求")
    gpa_requirements = Column(Text, nullable=True, comment="GPA要求")
    language_requirements = Column(Text, nullable=True, comment="语言要求")
    interview_type = Column(String(50), nullable=True, comment="面试类型")
    interview_experience = Column(Text, nullable=True, comment="面试经验")

    # 项目详情
    program_objectives = Column(Text, nullable=True, comment="项目培养目标")
    courses = Column(Text, nullable=True, comment="课程设置")
    consultant_analysis = Column(Text, nullable=True, comment="顾问分析")

    # 其他信息
    other_cost = Column(String(100), nullable=True, comment="其他费用")
    degree = Column(String(10), nullable=True, comment="申请学位类型")
    degree_evaluation = Column(Text, nullable=True, comment="留服认证")

    # 向量嵌入（用于语义匹配）
    embedding = Column(JSONB, nullable=True, comment="项目描述的向量嵌入")
    
    # 时间戳字段
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关键：建立与分数要求表的“一对多”关系
    # 'scores' 是一个虚拟字段，可以让你通过 program.scores 访问其所有分数要求
    scores = relationship("AISelectionProgramScoreRequirement", back_populates="program", cascade="all, delete-orphan")

# 分数要求表
class AISelectionProgramScoreRequirement(Base):
    """
    项目分数要求数据库模型类，存储每个项目对特定本科院校的分数要求。
    这是一个“长”表结构，具有良好的扩展性和查询性能。
    """
    __tablename__ = "ai_selection_program_score_requirements"

    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")

    # 外键，关联到项目信息表
    program_id = Column(Integer, ForeignKey("ai_selection_programs.id"), nullable=False, index=True)

    # 具体的本科院校名称
    home_school_name = Column(String(100), nullable=False, index=True, comment="国内本科院校名称")

    # 分数要求
    # 允许分数为 NULL，以表示“无法申请”
    score_requirement = Column(Float, nullable=True, comment="分数要求, NULL表示无法申请")

    # 时间戳字段
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关键：建立与项目表的反向关系
    # 'program' 是一个虚拟字段，可以让你通过 score.program 访问其所属的项目信息
    program = relationship("AISelectionProgram", back_populates="scores")

    #  添加 program_id 和 home_school_name 的复合唯一约束
    __table_args__ = (
        UniqueConstraint('program_id', 'home_school_name', name='uq_program_home_school'),
    )

    def __repr__(self):
        return f"<Score(program_id={self.program_id}, school='{self.home_school_name}', score={self.score_requirement})>"


# --- 2. 数据库连接和初始化 ---
DATABASE_URL = "postgresql://postgres:123456789@localhost:5432/tunshuedu_db" # 请替换为你的数据库连接字符串


def import_scores_from_csv(csv_path: str, batch_size: int = 100000):
    """
    从CSV文件导入项目分数要求，支持增量更新和分批处理。
    
    :param csv_path: CSV文件的路径。
    :param batch_size: 每批处理的数据行数。
    """
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    # 自动建表: 检查所有继承自 Base 的模型，如果表不存在，则创建
    print("正在检查并创建数据库表（如果不存在）...")
    Base.metadata.create_all(bind=engine)
    print("表已就绪。")

    # --- 3. 读取和转换数据 ---
    print(f"从 {csv_path} 读取数据...")
    df_wide = pd.read_csv(csv_path)

    base_info_columns = ['program_id', 'school_name_cn', 'program_code', 'program_name_cn', 'program_name_en', 'faculty']
    home_school_name_columns = [col for col in df_wide.columns if col not in base_info_columns]

    print("将宽表数据转换为长表格式...")
    df_long = pd.melt(
        df_wide,
        id_vars=['program_id'],
        value_vars=home_school_name_columns,
        var_name='home_school_name',
        value_name='score_requirement'
    )
    
    # 将 pandas 的空值标记 <NA> 或 NaN 替换为 None，以便数据库能正确处理为 NULL
    df_long['score_requirement'] = df_long['score_requirement'].replace({np.nan: None})
    
    # 清理掉完全没有 program_id 的无效行（以防万一）
    df_long.dropna(subset=['program_id'], inplace=True)
    df_long['program_id'] = df_long['program_id'].astype(int)

    records_to_upsert = df_long.to_dict('records')
    total_records = len(records_to_upsert)
    print(f"数据转换完成，共计 {total_records} 条记录需要处理。")

    # --- 4. 分批次写入数据库 (Upsert) ---
    db_session = SessionLocal()
    try:
        print(f"开始分批写入数据库，每批 {batch_size} 条...")
        for i in range(0, total_records, batch_size):
            batch = records_to_upsert[i:i + batch_size]
            if not batch:
                continue

            # 构建 ON CONFLICT DO UPDATE (Upsert) 语句
            stmt = pg_insert(AISelectionProgramScoreRequirement).values(batch)
            
            # 定义冲突时的更新操作
            # constraint 指定了唯一约束的名称
            # set_ 指定了冲突时需要更新的字段
            update_stmt = stmt.on_conflict_do_update(
                constraint='uq_program_home_school',
                set_=dict(score_requirement=stmt.excluded.score_requirement, updated_at=datetime.utcnow())
            )
            
            db_session.execute(update_stmt)
            print(f"已处理 {min(i + batch_size, total_records)} / {total_records} 条记录...")

        db_session.commit()
        print("所有数据成功同步到数据库！")

    except Exception as e:
        db_session.rollback()
        print(f"处理过程中发生错误: {e}")
    finally:
        db_session.close()


# --- 5. 执行导入 ---
if __name__ == "__main__":
    # 假设你的项目信息(ai_selection_programs)已经存在于数据库中
    # 并且CSV中的 program_id 能在项目中找到
    csv_file_path = "uk18_au8_programs_score_requirements.csv" # 请替换为你的CSV文件路径
    import_scores_from_csv(csv_file_path, batch_size=100000)