{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import wordninja\n", "from thefuzz import fuzz, process\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from requests.adapters import HTTPAdapter\n", "import ssl\n", "\n", "from PIL import Image\n", "from io import BytesIO"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1 境外院校数据库制备"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.1 基本数据爬取"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# 直接发送HTTP请求获取网页内容，会经典报错SSLError: HTTPSConnectionPool(host='www.compassedu.hk', port=443):\n", "#  Max retries exceeded with url: /qs (Caused by SSLError(SSLError(1, '[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010)')))\n", "# url = \"https://www.compassedu.hk/qs\"\n", "# response = requests.get(url)\n", "\n", "# 还是要通过创建一个自定义的HTTP适配器解决\n", "class SSLContextAdapter(HTTPAdapter):\n", "    def __init__(self, ssl_context=None, **kwargs):\n", "        self.ssl_context = ssl_context\n", "        super().__init__(**kwargs)\n", "\n", "    def init_poolmanager(self, *args, **kwargs):\n", "        kwargs['ssl_context'] = self.ssl_context\n", "        return super(SSLConte<PERSON>t<PERSON><PERSON><PERSON><PERSON>, self).init_poolmanager(*args, **kwargs)\n", "        \n", "# 创建一个自定义的SSL上下文\n", "context = ssl.create_default_context()\n", "context.set_ciphers('DEFAULT:@SECLEVEL=1')  # 设置较低的安全级别要求\n", "\n", "# 创建一个session对象并挂载我们的自定义适配器\n", "session = requests.Session()\n", "adapter = SSLContextAdapter(ssl_context=context)\n", "session.mount('https://', adapter)\n", "\n", "url = \"https://www.compassedu.hk/qs\"\n", "headers = {\n", "        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36'\n", "}\n", "response = session.get(url, headers=headers)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# 解析爬取到的网页\n", "html_content = response.text\n", "\n", "# 使用BeautifulSoup解析HTML内容\n", "soup = BeautifulSoup(html_content, 'html.parser')\n", "\n", "# 初始化一个空列表来存储学校信息\n", "school_data = []\n", "\n", "# 查找包含所有院校排名的<div class=\"result-list\">元素\n", "result_list = soup.find('div', class_='result-list')\n", "\n", "# 遍历每一个<a>标签，其中包含了学校的详细信息\n", "for a_tag in result_list.find_all('a'):\n", "    # 构造完整的学校网站链接\n", "    school_website = 'https://www.compassedu.hk' + a_tag['href']\n", "    \n", "    # 获取每个学校详情所在的<div class=\"rank-item\">\n", "    rank_item = a_tag.find('div', class_='rank-item')\n", "    \n", "    # 提取学校排名\n", "    rank = rank_item.find('div', class_='rank-tr1').text.strip()\n", "    \n", "    # 提取学校logo链接\n", "    logo_img = rank_item.find('img')\n", "    logo_link = logo_img['src'] if logo_img else None\n", "    \n", "    # 提取学校名称（中文和英文）\n", "    univ_name_div = rank_item.find('div', class_='univ-name')\n", "    chinese_name = univ_name_div.find('div', class_='cname line-one').text.strip()\n", "    english_name = univ_name_div.find('div', class_='ename line-one').text.strip()\n", "    \n", "    # 将提取的信息添加到列表中\n", "    school_data.append({\n", "        '学校中文名': chinese_name,\n", "        '学校英文名': english_name,\n", "        '学校QS排名': rank,\n", "        '学校网站链接': school_website,\n", "        '学校logo链接': logo_link\n", "    })\n", "\n", "# 将学校数据转换为DataFrame\n", "aboard_school_df = pd.DataFrame(school_data)\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>学校logo链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "      <td>http://logo.compassedu.hk/85.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "      <td>http://logo.compassedu.hk/3.png?imageMogr2/aut...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "      <td>http://logo.compassedu.hk/86.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "      <td>http://logo.compassedu.hk/8.png?imageMogr2/aut...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "      <td>http://logo.compassedu.hk/80.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>于韦斯屈莱大学</td>\n", "      <td>University of Jyväskylä</td>\n", "      <td>498</td>\n", "      <td>https://www.compassedu.hk/univ_1313</td>\n", "      <td>http://logo.compassedu.hk/1313.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>498</th>\n", "      <td>托木斯克国立大学</td>\n", "      <td>Tomsk State University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1391</td>\n", "      <td>http://logo.compassedu.hk/1391.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>499</th>\n", "      <td>哥斯达黎加大学</td>\n", "      <td>Universidad de Costa Rica</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1788</td>\n", "      <td>http://logo.compassedu.hk/1788.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>500</th>\n", "      <td>西北工业大学</td>\n", "      <td>Northwestern Polytechnical University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/college_2462</td>\n", "      <td>http://info.compassedu.hk/collegee/2021/2462.p...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>501</th>\n", "      <td>中央昆士兰大学</td>\n", "      <td>Central Queensland University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1527</td>\n", "      <td>http://logo.compassedu.hk/1527.png?imageMogr2/...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>502 rows × 5 columns</p>\n", "</div>"], "text/plain": ["        学校中文名                                  学校英文名 学校QS排名  \\\n", "0      麻省理工学院  Massachusetts Institute of Technology      1   \n", "1      帝国理工学院                Imperial College London      2   \n", "2       斯坦福大学                    Stanford University      3   \n", "3        牛津大学                   University of Oxford      4   \n", "4        哈佛大学                     Harvard University      5   \n", "..        ...                                    ...    ...   \n", "497   于韦斯屈莱大学                University of Jyväskylä    498   \n", "498  托木斯克国立大学                 Tomsk State University    499   \n", "499   哥斯达黎加大学              Universidad de Costa Rica    499   \n", "500    西北工业大学  Northwestern Polytechnical University    499   \n", "501   中央昆士兰大学          Central Queensland University    499   \n", "\n", "                                     学校网站链接  \\\n", "0         https://www.compassedu.hk/univ_85   \n", "1          https://www.compassedu.hk/univ_3   \n", "2         https://www.compassedu.hk/univ_86   \n", "3          https://www.compassedu.hk/univ_8   \n", "4         https://www.compassedu.hk/univ_80   \n", "..                                      ...   \n", "497     https://www.compassedu.hk/univ_1313   \n", "498     https://www.compassedu.hk/univ_1391   \n", "499     https://www.compassedu.hk/univ_1788   \n", "500  https://www.compassedu.hk/college_2462   \n", "501     https://www.compassedu.hk/univ_1527   \n", "\n", "                                              学校logo链接  \n", "0    http://logo.compassedu.hk/85.png?imageMogr2/au...  \n", "1    http://logo.compassedu.hk/3.png?imageMogr2/aut...  \n", "2    http://logo.compassedu.hk/86.png?imageMogr2/au...  \n", "3    http://logo.compassedu.hk/8.png?imageMogr2/aut...  \n", "4    http://logo.compassedu.hk/80.png?imageMogr2/au...  \n", "..                                                 ...  \n", "497  http://logo.compassedu.hk/1313.png?imageMogr2/...  \n", "498  http://logo.compassedu.hk/1391.png?imageMogr2/...  \n", "499  http://logo.compassedu.hk/1788.png?imageMogr2/...  \n", "500  http://info.compassedu.hk/collegee/2021/2462.p...  \n", "501  http://logo.compassedu.hk/1527.png?imageMogr2/...  \n", "\n", "[502 rows x 5 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# # 如果需要，可以将DataFrame保存到CSV文件中\n", "# aboard_school_df.to_csv('院校数据/aboard_schools.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.2 Logo网址转存"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["aboard_school_df = pd.read_csv('院校数据/aboard_schools.csv')"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>学校logo链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "      <td>http://logo.compassedu.hk/85.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "      <td>http://logo.compassedu.hk/3.png?imageMogr2/aut...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "      <td>http://logo.compassedu.hk/86.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "      <td>http://logo.compassedu.hk/8.png?imageMogr2/aut...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "      <td>http://logo.compassedu.hk/80.png?imageMogr2/au...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>于韦斯屈莱大学</td>\n", "      <td>University of Jyväskylä</td>\n", "      <td>498</td>\n", "      <td>https://www.compassedu.hk/univ_1313</td>\n", "      <td>http://logo.compassedu.hk/1313.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>498</th>\n", "      <td>托木斯克国立大学</td>\n", "      <td>Tomsk State University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1391</td>\n", "      <td>http://logo.compassedu.hk/1391.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>499</th>\n", "      <td>哥斯达黎加大学</td>\n", "      <td>Universidad de Costa Rica</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1788</td>\n", "      <td>http://logo.compassedu.hk/1788.png?imageMogr2/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>500</th>\n", "      <td>西北工业大学</td>\n", "      <td>Northwestern Polytechnical University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/college_2462</td>\n", "      <td>http://info.compassedu.hk/collegee/2021/2462.p...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>501</th>\n", "      <td>中央昆士兰大学</td>\n", "      <td>Central Queensland University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1527</td>\n", "      <td>http://logo.compassedu.hk/1527.png?imageMogr2/...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>502 rows × 5 columns</p>\n", "</div>"], "text/plain": ["        学校中文名                                  学校英文名  学校QS排名  \\\n", "0      麻省理工学院  Massachusetts Institute of Technology       1   \n", "1      帝国理工学院                Imperial College London       2   \n", "2       斯坦福大学                    Stanford University       3   \n", "3        牛津大学                   University of Oxford       4   \n", "4        哈佛大学                     Harvard University       5   \n", "..        ...                                    ...     ...   \n", "497   于韦斯屈莱大学                University of Jyväskylä     498   \n", "498  托木斯克国立大学                 Tomsk State University     499   \n", "499   哥斯达黎加大学              Universidad de Costa Rica     499   \n", "500    西北工业大学  Northwestern Polytechnical University     499   \n", "501   中央昆士兰大学          Central Queensland University     499   \n", "\n", "                                     学校网站链接  \\\n", "0         https://www.compassedu.hk/univ_85   \n", "1          https://www.compassedu.hk/univ_3   \n", "2         https://www.compassedu.hk/univ_86   \n", "3          https://www.compassedu.hk/univ_8   \n", "4         https://www.compassedu.hk/univ_80   \n", "..                                      ...   \n", "497     https://www.compassedu.hk/univ_1313   \n", "498     https://www.compassedu.hk/univ_1391   \n", "499     https://www.compassedu.hk/univ_1788   \n", "500  https://www.compassedu.hk/college_2462   \n", "501     https://www.compassedu.hk/univ_1527   \n", "\n", "                                              学校logo链接  \n", "0    http://logo.compassedu.hk/85.png?imageMogr2/au...  \n", "1    http://logo.compassedu.hk/3.png?imageMogr2/aut...  \n", "2    http://logo.compassedu.hk/86.png?imageMogr2/au...  \n", "3    http://logo.compassedu.hk/8.png?imageMogr2/aut...  \n", "4    http://logo.compassedu.hk/80.png?imageMogr2/au...  \n", "..                                                 ...  \n", "497  http://logo.compassedu.hk/1313.png?imageMogr2/...  \n", "498  http://logo.compassedu.hk/1391.png?imageMogr2/...  \n", "499  http://logo.compassedu.hk/1788.png?imageMogr2/...  \n", "500  http://info.compassedu.hk/collegee/2021/2462.p...  \n", "501  http://logo.compassedu.hk/1527.png?imageMogr2/...  \n", "\n", "[502 rows x 5 columns]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df "]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed to download 金边大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/2350.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download Bogaziçi大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/1596.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 米兰圣心天主教大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/2001.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 毕尔坎特大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/2101.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 维多利亚州立大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/2215.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 穆罕默德·本·法赫德王子大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/2156.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 拉曼鲁尔大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/1373.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 拜罗伊特大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/2023.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 塔什干灌溉与农业机械化工程大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/1645.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 中央大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/1477.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 塞萨洛尼基亚里士多德大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/1655.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 弗雷伯格大学技术大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/1291.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 华沙工业大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/1726.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 阿卜杜勒拉曼费萨尔大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/2358.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Failed to download 哥斯达黎加大学.png: 404 Client Error: Not Found for url: http://logo.compassedu.hk/1788.png?imageMogr2/auto-orient/thumbnail/100x100%3E/blur/1x0/quality/77%7Cimageslim\n", "Total downloaded: 487\n", "Total failed: 15\n", "Failed indices: [233, 372, 408, 414, 415, 435, 436, 447, 469, 478, 485, 486, 488, 492, 499]\n"]}], "source": ["# 创建保存图片的文件夹\n", "output_folder = 'school_logos'\n", "if not os.path.exists(output_folder):\n", "    os.makedirs(output_folder)\n", "\n", "def download_image(url, filename):\n", "    try:\n", "        response = requests.get(url)\n", "        response.raise_for_status()\n", "        image = Image.open(BytesIO(response.content))\n", "        image.save(os.path.join(output_folder, filename))\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Failed to download {filename}: {str(e)}\")\n", "        return False\n", "\n", "success_count = 0\n", "failure_count = 0\n", "failed_indices = []\n", "\n", "for index, row in aboard_school_df.iterrows():\n", "    school_name = row['学校中文名']\n", "    logo_url = row['学校logo链接']\n", "    filename = f\"{school_name}.png\"\n", "    if download_image(logo_url, filename):\n", "        success_count += 1\n", "    else:\n", "        failure_count += 1\n", "        failed_indices.append(index)\n", "\n", "print(f\"Total downloaded: {success_count}\")\n", "print(f\"Total failed: {failure_count}\")\n", "if failed_indices:\n", "    print(\"Failed indices:\", failed_indices)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此外：印度理工学院德里分校（123）、康斯坦茨大学（444）为纯白纯黑无效logo..."]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["def generate_new_url(school_name):\n", "    return f\"https://tunshutech.com/school_logos/{school_name}.png\"\n", "\n", "for index, row in aboard_school_df.iterrows():\n", "    school_name = row['学校中文名']\n", "    new_url = generate_new_url(school_name)\n", "    aboard_school_df.at[index, '学校logo链接'] = new_url"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>学校logo链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "      <td>https://tunshutech.com/school_logos/麻省理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "      <td>https://tunshutech.com/school_logos/帝国理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "      <td>https://tunshutech.com/school_logos/斯坦福大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "      <td>https://tunshutech.com/school_logos/牛津大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "      <td>https://tunshutech.com/school_logos/哈佛大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>于韦斯屈莱大学</td>\n", "      <td>University of Jyväskylä</td>\n", "      <td>498</td>\n", "      <td>https://www.compassedu.hk/univ_1313</td>\n", "      <td>https://tunshutech.com/school_logos/于韦斯屈莱大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>498</th>\n", "      <td>托木斯克国立大学</td>\n", "      <td>Tomsk State University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1391</td>\n", "      <td>https://tunshutech.com/school_logos/托木斯克国立大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>499</th>\n", "      <td>哥斯达黎加大学</td>\n", "      <td>Universidad de Costa Rica</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1788</td>\n", "      <td>https://tunshutech.com/school_logos/哥斯达黎加大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>500</th>\n", "      <td>西北工业大学</td>\n", "      <td>Northwestern Polytechnical University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/college_2462</td>\n", "      <td>https://tunshutech.com/school_logos/西北工业大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>501</th>\n", "      <td>中央昆士兰大学</td>\n", "      <td>Central Queensland University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1527</td>\n", "      <td>https://tunshutech.com/school_logos/中央昆士兰大学.png</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>502 rows × 5 columns</p>\n", "</div>"], "text/plain": ["        学校中文名                                  学校英文名  学校QS排名  \\\n", "0      麻省理工学院  Massachusetts Institute of Technology       1   \n", "1      帝国理工学院                Imperial College London       2   \n", "2       斯坦福大学                    Stanford University       3   \n", "3        牛津大学                   University of Oxford       4   \n", "4        哈佛大学                     Harvard University       5   \n", "..        ...                                    ...     ...   \n", "497   于韦斯屈莱大学                University of Jyväskylä     498   \n", "498  托木斯克国立大学                 Tomsk State University     499   \n", "499   哥斯达黎加大学              Universidad de Costa Rica     499   \n", "500    西北工业大学  Northwestern Polytechnical University     499   \n", "501   中央昆士兰大学          Central Queensland University     499   \n", "\n", "                                     学校网站链接  \\\n", "0         https://www.compassedu.hk/univ_85   \n", "1          https://www.compassedu.hk/univ_3   \n", "2         https://www.compassedu.hk/univ_86   \n", "3          https://www.compassedu.hk/univ_8   \n", "4         https://www.compassedu.hk/univ_80   \n", "..                                      ...   \n", "497     https://www.compassedu.hk/univ_1313   \n", "498     https://www.compassedu.hk/univ_1391   \n", "499     https://www.compassedu.hk/univ_1788   \n", "500  https://www.compassedu.hk/college_2462   \n", "501     https://www.compassedu.hk/univ_1527   \n", "\n", "                                             学校logo链接  \n", "0      https://tunshutech.com/school_logos/麻省理工学院.png  \n", "1      https://tunshutech.com/school_logos/帝国理工学院.png  \n", "2       https://tunshutech.com/school_logos/斯坦福大学.png  \n", "3        https://tunshutech.com/school_logos/牛津大学.png  \n", "4        https://tunshutech.com/school_logos/哈佛大学.png  \n", "..                                                ...  \n", "497   https://tunshutech.com/school_logos/于韦斯屈莱大学.png  \n", "498  https://tunshutech.com/school_logos/托木斯克国立大学.png  \n", "499   https://tunshutech.com/school_logos/哥斯达黎加大学.png  \n", "500    https://tunshutech.com/school_logos/西北工业大学.png  \n", "501   https://tunshutech.com/school_logos/中央昆士兰大学.png  \n", "\n", "[502 rows x 5 columns]"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.3 Logo图片补充和院校信息补充"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>学校logo链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "      <td>https://tunshutech.com/school_logos/麻省理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "      <td>https://tunshutech.com/school_logos/帝国理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "      <td>https://tunshutech.com/school_logos/斯坦福大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "      <td>https://tunshutech.com/school_logos/牛津大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "      <td>https://tunshutech.com/school_logos/哈佛大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>于韦斯屈莱大学</td>\n", "      <td>University of Jyväskylä</td>\n", "      <td>498</td>\n", "      <td>https://www.compassedu.hk/univ_1313</td>\n", "      <td>https://tunshutech.com/school_logos/于韦斯屈莱大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>498</th>\n", "      <td>托木斯克国立大学</td>\n", "      <td>Tomsk State University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1391</td>\n", "      <td>https://tunshutech.com/school_logos/托木斯克国立大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>499</th>\n", "      <td>哥斯达黎加大学</td>\n", "      <td>Universidad de Costa Rica</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1788</td>\n", "      <td>https://tunshutech.com/school_logos/哥斯达黎加大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>500</th>\n", "      <td>西北工业大学</td>\n", "      <td>Northwestern Polytechnical University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/college_2462</td>\n", "      <td>https://tunshutech.com/school_logos/西北工业大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>501</th>\n", "      <td>中央昆士兰大学</td>\n", "      <td>Central Queensland University</td>\n", "      <td>499</td>\n", "      <td>https://www.compassedu.hk/univ_1527</td>\n", "      <td>https://tunshutech.com/school_logos/中央昆士兰大学.png</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>502 rows × 5 columns</p>\n", "</div>"], "text/plain": ["        学校中文名                                  学校英文名  学校QS排名  \\\n", "0      麻省理工学院  Massachusetts Institute of Technology       1   \n", "1      帝国理工学院                Imperial College London       2   \n", "2       斯坦福大学                    Stanford University       3   \n", "3        牛津大学                   University of Oxford       4   \n", "4        哈佛大学                     Harvard University       5   \n", "..        ...                                    ...     ...   \n", "497   于韦斯屈莱大学                University of Jyväskylä     498   \n", "498  托木斯克国立大学                 Tomsk State University     499   \n", "499   哥斯达黎加大学              Universidad de Costa Rica     499   \n", "500    西北工业大学  Northwestern Polytechnical University     499   \n", "501   中央昆士兰大学          Central Queensland University     499   \n", "\n", "                                     学校网站链接  \\\n", "0         https://www.compassedu.hk/univ_85   \n", "1          https://www.compassedu.hk/univ_3   \n", "2         https://www.compassedu.hk/univ_86   \n", "3          https://www.compassedu.hk/univ_8   \n", "4         https://www.compassedu.hk/univ_80   \n", "..                                      ...   \n", "497     https://www.compassedu.hk/univ_1313   \n", "498     https://www.compassedu.hk/univ_1391   \n", "499     https://www.compassedu.hk/univ_1788   \n", "500  https://www.compassedu.hk/college_2462   \n", "501     https://www.compassedu.hk/univ_1527   \n", "\n", "                                             学校logo链接  \n", "0      https://tunshutech.com/school_logos/麻省理工学院.png  \n", "1      https://tunshutech.com/school_logos/帝国理工学院.png  \n", "2       https://tunshutech.com/school_logos/斯坦福大学.png  \n", "3        https://tunshutech.com/school_logos/牛津大学.png  \n", "4        https://tunshutech.com/school_logos/哈佛大学.png  \n", "..                                                ...  \n", "497   https://tunshutech.com/school_logos/于韦斯屈莱大学.png  \n", "498  https://tunshutech.com/school_logos/托木斯克国立大学.png  \n", "499   https://tunshutech.com/school_logos/哥斯达黎加大学.png  \n", "500    https://tunshutech.com/school_logos/西北工业大学.png  \n", "501   https://tunshutech.com/school_logos/中央昆士兰大学.png  \n", "\n", "[502 rows x 5 columns]"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["# aboard_school_df = pd.read_csv('院校数据/aboard_schools.csv')\n", "aboard_school_df "]}, {"cell_type": "markdown", "metadata": {}, "source": ["人工寻找下列缺失院校Logo并处理到100*100格式，可从QS网站或指南者网站中直接寻找..."]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'伍斯特理工学院',\n", " '伦敦商学院',\n", " '伦敦大学亚非学院',\n", " '伦敦大学城市学院',\n", " '伦敦大学金史密斯学院',\n", " '伦斯勒理工学院',\n", " '佐治亚大学',\n", " '佐治亚州立大学',\n", " '佩珀代因大学',\n", " '克兰菲尔德大学',\n", " '克莱姆森大学',\n", " '北卡罗来纳州立大学罗利分校',\n", " '南卫理公会大学',\n", " '叶史瓦大学',\n", " '圣塔克拉拉大学',\n", " '威廉玛丽学院',\n", " '威斯敏斯特大学',\n", " '布兰迪斯大学',\n", " '康涅狄格大学',\n", " '德州大学达拉斯分校',\n", " '斯蒂文斯理工学院',\n", " '新加坡科技设计大学',\n", " '新加坡管理大学',\n", " '旧金山大学',\n", " '明尼苏达大学双城分校',\n", " '本特利大学',\n", " '杜兰大学',\n", " '杨百翰大学',\n", " '波士顿学院',\n", " '澳门城市大学',\n", " '澳门理工大学',\n", " '白金汉大学',\n", " '福特汉姆大学',\n", " '科罗拉多大学波德分校',\n", " '纽约市立大学巴鲁克学院',\n", " '维克森林大学',\n", " '罗格斯大学',\n", " '罗格斯大学纽瓦克分校',\n", " '美利坚大学',\n", " '考文垂大学',\n", " '贝勒大学',\n", " '里海大学',\n", " '雪城大学',\n", " '香港岭南大学',\n", " '香港恒生大学',\n", " '香港教育大学',\n", " '香港树仁大学',\n", " '香港都会大学'}"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["set(program_df['学校中文名'].unique()) - set(aboard_school_df['学校中文名'].unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来补充这些本身缺失Logo的院校的信息（QS500+）到境外院校数据库中\n", "> 注意这些新补充的院校（本质是项目数据库中有，但爬取的QS前500名单中没有的）院校如果排名在前500，说明这些学校在指南者项目数据库中中文名和QS榜单中中文名不一致！\n", "> \n", "> 比如：项目数据库中是 明尼苏达大学双城分校/北卡罗来纳州立大学罗利分校/科罗拉多大学波德分校/罗格斯大学/伦敦大学城市学院 ，排名中就是 罗格斯大学新布朗斯维克分校/伦敦大学城市圣乔治等...\n", ">\n", "> 先以项目数据库为准...\n", ">\n", "\n", "注意排名的处理（整型化，比如701-710就当作701名）\n", "> 具体QS排名的规则：前700有具体排名；701-800隔10；801-1000隔50；1001-1400隔200；之后为1401+"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["# 找出需要补充的学校\n", "new_schools = set(program_df['学校中文名'].unique()) - set(aboard_school_df['学校中文名'].unique())\n", "\n", "# 创建一个新的 DataFrame 来存储新增的学校信息\n", "new_school_data = []\n", "\n", "for school in new_schools:\n", "    rank_value = program_df.loc[program_df['学校中文名'] == school, '学校排名(QS26)'].iloc[0]\n", "    \n", "    # 提取排名中的数字部分\n", "    if isinstance(rank_value, str):\n", "        if '-' in rank_value:\n", "            rank_str = rank_value.split('-')[0]\n", "            rank = int(rank_str)\n", "        else:\n", "            rank = int(rank_value)\n", "    elif isinstance(rank_value, float):\n", "        if pd.isna(rank_value):  # 处理 NaN 值\n", "            rank = rank_value\n", "        else:\n", "            rank = int(rank_value)\n", "    else:\n", "        raise ValueError(f\"Unexpected type for ranking: {type(rank_value)}\")\n", "    \n", "    english_name = program_df.loc[program_df['学校中文名'] == school, '学校英文名'].iloc[0]\n", "    logo_link = f\"https://tunshutech.com/school_logos/{school}.png\"\n", "    new_school_data.append({\n", "        '学校中文名': school,\n", "        '学校英文名': english_name,\n", "        '学校QS排名': rank,\n", "        '学校网站链接': '',\n", "        '学校logo链接': logo_link\n", "    })\n", "\n", "new_school_df = pd.DataFrame(new_school_data)\n", "new_school_df['学校QS排名'] = new_school_df['学校QS排名'].astype('Int64')"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>学校logo链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>罗格斯大学纽瓦克分校</td>\n", "      <td>Rutgers University-Newark</td>\n", "      <td>771</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/罗格斯大学纽瓦克分校...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>香港恒生大学</td>\n", "      <td>The Hang Seng University of Hong Kong</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/香港恒生大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>香港岭南大学</td>\n", "      <td>Lingnan University</td>\n", "      <td>701</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/香港岭南大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>科罗拉多大学波德分校</td>\n", "      <td>University of Colorado Boulder</td>\n", "      <td>299</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/科罗拉多大学波德分校...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>旧金山大学</td>\n", "      <td>University of San Francisco</td>\n", "      <td>1201</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/旧金山大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>美利坚大学</td>\n", "      <td>American University</td>\n", "      <td>587</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/美利坚大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>明尼苏达大学双城分校</td>\n", "      <td>University of Minnesota Twin Cities</td>\n", "      <td>210</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/明尼苏达大学双城分校...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>伍斯特理工学院</td>\n", "      <td>Worcester Polytechnic Institute</td>\n", "      <td>851</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/伍斯特理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>澳门理工大学</td>\n", "      <td>Macao Polytechnic University</td>\n", "      <td>901</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/澳门理工大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>澳门城市大学</td>\n", "      <td>City University of Macau</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/澳门城市大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>新加坡科技设计大学</td>\n", "      <td>Singapore University of Technology and Design</td>\n", "      <td>519</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/新加坡科技设计大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>考文垂大学</td>\n", "      <td>Coventry University</td>\n", "      <td>558</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/考文垂大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>贝勒大学</td>\n", "      <td>Baylor University</td>\n", "      <td>1001</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/贝勒大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>香港树仁大学</td>\n", "      <td>HongKong S hueY an University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/香港树仁大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>南卫理公会大学</td>\n", "      <td>Southern Methodist University</td>\n", "      <td>1001</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/南卫理公会大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>克兰菲尔德大学</td>\n", "      <td>Cranfield University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/克兰菲尔德大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>伦斯勒理工学院</td>\n", "      <td>Rensselaer Polytechnic Institute</td>\n", "      <td>695</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/伦斯勒理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>康涅狄格大学</td>\n", "      <td>University of Connecticut</td>\n", "      <td>534</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/康涅狄格大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>罗格斯大学</td>\n", "      <td>Rutgers University-New Brunswick</td>\n", "      <td>328</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/罗格斯大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>德州大学达拉斯分校</td>\n", "      <td>University of Texas Dallas</td>\n", "      <td>597</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/德州大学达拉斯分校.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>杨百翰大学</td>\n", "      <td>Brigham Young University-Provo</td>\n", "      <td>1001</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/杨百翰大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>伦敦大学金史密斯学院</td>\n", "      <td><PERSON><PERSON><PERSON>, University of London</td>\n", "      <td>711</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/伦敦大学金史密斯学院...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>福特汉姆大学</td>\n", "      <td>Fordham University</td>\n", "      <td>1001</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/福特汉姆大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>佐治亚大学</td>\n", "      <td>University of Georgia</td>\n", "      <td>525</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/佐治亚大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>纽约市立大学巴鲁克学院</td>\n", "      <td><PERSON>, CUNY</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/纽约市立大学巴鲁克学...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>佩珀代因大学</td>\n", "      <td>Pepperdine University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/佩珀代因大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>杜兰大学</td>\n", "      <td>Tulane University</td>\n", "      <td>597</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/杜兰大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>伦敦商学院</td>\n", "      <td>London Business School</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/伦敦商学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>克莱姆森大学</td>\n", "      <td>Clemson University</td>\n", "      <td>951</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/克莱姆森大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>里海大学</td>\n", "      <td>Lehigh University</td>\n", "      <td>668</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/里海大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>香港教育大学</td>\n", "      <td>The Education University of Hong Kong</td>\n", "      <td>530</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/香港教育大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>伦敦大学亚非学院</td>\n", "      <td>SOAS University of London</td>\n", "      <td>511</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/伦敦大学亚非学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>威斯敏斯特大学</td>\n", "      <td>University of Westminster</td>\n", "      <td>801</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/威斯敏斯特大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>斯蒂文斯理工学院</td>\n", "      <td>Stevens Institute of Technology</td>\n", "      <td>673</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/斯蒂文斯理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>北卡罗来纳州立大学罗利分校</td>\n", "      <td>North Carolina State University</td>\n", "      <td>272</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/北卡罗来纳州立大学罗...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>威廉玛丽学院</td>\n", "      <td><PERSON></td>\n", "      <td>1001</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/威廉玛丽学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>波士顿学院</td>\n", "      <td>Boston College</td>\n", "      <td>526</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/波士顿学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>白金汉大学</td>\n", "      <td>University of Buckingham</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/白金汉大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>新加坡管理大学</td>\n", "      <td>Singapore Management University</td>\n", "      <td>511</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/新加坡管理大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>雪城大学</td>\n", "      <td>Syracuse University</td>\n", "      <td>741</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/雪城大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>香港都会大学</td>\n", "      <td>HongKong Metropolitan University</td>\n", "      <td>781</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/香港都会大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>维克森林大学</td>\n", "      <td>Wake Forest University</td>\n", "      <td>791</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/维克森林大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>布兰迪斯大学</td>\n", "      <td>Brandeis University</td>\n", "      <td>741</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/布兰迪斯大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>圣塔克拉拉大学</td>\n", "      <td>Santa Clara University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/圣塔克拉拉大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>本特利大学</td>\n", "      <td>Bentley University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/本特利大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>叶史瓦大学</td>\n", "      <td>Yeshiva University</td>\n", "      <td>624</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/叶史瓦大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>佐治亚州立大学</td>\n", "      <td>Georgia State University</td>\n", "      <td>781</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/佐治亚州立大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>伦敦大学城市学院</td>\n", "      <td>City, University of London</td>\n", "      <td>310</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/伦敦大学城市学院.png</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            学校中文名                                          学校英文名  学校QS排名  \\\n", "0      罗格斯大学纽瓦克分校                      Rutgers University-Newark     771   \n", "1          香港恒生大学          The Hang Seng University of Hong Kong    <NA>   \n", "2          香港岭南大学                             Lingnan University     701   \n", "3      科罗拉多大学波德分校                 University of Colorado Boulder     299   \n", "4           旧金山大学                    University of San Francisco    1201   \n", "5           美利坚大学                            American University     587   \n", "6      明尼苏达大学双城分校            University of Minnesota Twin Cities     210   \n", "7         伍斯特理工学院                Worcester Polytechnic Institute     851   \n", "8          澳门理工大学                   Macao Polytechnic University     901   \n", "9          澳门城市大学                       City University of Macau    <NA>   \n", "10      新加坡科技设计大学  Singapore University of Technology and Design     519   \n", "11          考文垂大学                            Coventry University     558   \n", "12           贝勒大学                              Baylor University    1001   \n", "13         香港树仁大学                  HongKong S hueY an University    <NA>   \n", "14        南卫理公会大学                  Southern Methodist University    1001   \n", "15        克兰菲尔德大学                           Cranfield University    <NA>   \n", "16        伦斯勒理工学院               Rensselaer Polytechnic Institute     695   \n", "17         康涅狄格大学                      University of Connecticut     534   \n", "18          罗格斯大学               Rutgers University-New Brunswick     328   \n", "19      德州大学达拉斯分校                     University of Texas Dallas     597   \n", "20          杨百翰大学                 Brigham Young University-Provo    1001   \n", "21     伦敦大学金史密斯学院               Goldsmiths, University of London     711   \n", "22         福特汉姆大学                             Fordham University    1001   \n", "23          佐治亚大学                          University of Georgia     525   \n", "24    纽约市立大学巴鲁克学院                Bernard M. <PERSON>uch College, CUNY    <NA>   \n", "25         佩珀代因大学                          Pepperdine University    <NA>   \n", "26           杜兰大学                              Tulane University     597   \n", "27          伦敦商学院                         London Business School    <NA>   \n", "28         克莱姆森大学                             Clemson University     951   \n", "29           里海大学                              Lehigh University     668   \n", "30         香港教育大学          The Education University of Hong Kong     530   \n", "31       伦敦大学亚非学院                     SOAS University of London      511   \n", "32        威斯敏斯特大学                      University of Westminster     801   \n", "33       斯蒂文斯理工学院                Stevens Institute of Technology     673   \n", "34  北卡罗来纳州立大学罗利分校                North Carolina State University     272   \n", "35         威廉玛丽学院                                   <PERSON>    1001   \n", "36          波士顿学院                                 Boston College     526   \n", "37          白金汉大学                       University of Buckingham    <NA>   \n", "38        新加坡管理大学                Singapore Management University     511   \n", "39           雪城大学                            Syracuse University     741   \n", "40         香港都会大学               HongKong Metropolitan University     781   \n", "41         维克森林大学                         Wake Forest University     791   \n", "42         布兰迪斯大学                            Brandeis University     741   \n", "43        圣塔克拉拉大学                         Santa Clara University    <NA>   \n", "44          本特利大学                             Bentley University    <NA>   \n", "45          叶史瓦大学                             Yeshiva University     624   \n", "46        佐治亚州立大学                       Georgia State University     781   \n", "47       伦敦大学城市学院                     City, University of London     310   \n", "\n", "   学校网站链接                                           学校logo链接  \n", "0          https://tunshutech.com/school_logos/罗格斯大学纽瓦克分校...  \n", "1             https://tunshutech.com/school_logos/香港恒生大学.png  \n", "2             https://tunshutech.com/school_logos/香港岭南大学.png  \n", "3          https://tunshutech.com/school_logos/科罗拉多大学波德分校...  \n", "4              https://tunshutech.com/school_logos/旧金山大学.png  \n", "5              https://tunshutech.com/school_logos/美利坚大学.png  \n", "6          https://tunshutech.com/school_logos/明尼苏达大学双城分校...  \n", "7            https://tunshutech.com/school_logos/伍斯特理工学院.png  \n", "8             https://tunshutech.com/school_logos/澳门理工大学.png  \n", "9             https://tunshutech.com/school_logos/澳门城市大学.png  \n", "10         https://tunshutech.com/school_logos/新加坡科技设计大学.png  \n", "11             https://tunshutech.com/school_logos/考文垂大学.png  \n", "12              https://tunshutech.com/school_logos/贝勒大学.png  \n", "13            https://tunshutech.com/school_logos/香港树仁大学.png  \n", "14           https://tunshutech.com/school_logos/南卫理公会大学.png  \n", "15           https://tunshutech.com/school_logos/克兰菲尔德大学.png  \n", "16           https://tunshutech.com/school_logos/伦斯勒理工学院.png  \n", "17            https://tunshutech.com/school_logos/康涅狄格大学.png  \n", "18             https://tunshutech.com/school_logos/罗格斯大学.png  \n", "19         https://tunshutech.com/school_logos/德州大学达拉斯分校.png  \n", "20             https://tunshutech.com/school_logos/杨百翰大学.png  \n", "21         https://tunshutech.com/school_logos/伦敦大学金史密斯学院...  \n", "22            https://tunshutech.com/school_logos/福特汉姆大学.png  \n", "23             https://tunshutech.com/school_logos/佐治亚大学.png  \n", "24         https://tunshutech.com/school_logos/纽约市立大学巴鲁克学...  \n", "25            https://tunshutech.com/school_logos/佩珀代因大学.png  \n", "26              https://tunshutech.com/school_logos/杜兰大学.png  \n", "27             https://tunshutech.com/school_logos/伦敦商学院.png  \n", "28            https://tunshutech.com/school_logos/克莱姆森大学.png  \n", "29              https://tunshutech.com/school_logos/里海大学.png  \n", "30            https://tunshutech.com/school_logos/香港教育大学.png  \n", "31          https://tunshutech.com/school_logos/伦敦大学亚非学院.png  \n", "32           https://tunshutech.com/school_logos/威斯敏斯特大学.png  \n", "33          https://tunshutech.com/school_logos/斯蒂文斯理工学院.png  \n", "34         https://tunshutech.com/school_logos/北卡罗来纳州立大学罗...  \n", "35            https://tunshutech.com/school_logos/威廉玛丽学院.png  \n", "36             https://tunshutech.com/school_logos/波士顿学院.png  \n", "37             https://tunshutech.com/school_logos/白金汉大学.png  \n", "38           https://tunshutech.com/school_logos/新加坡管理大学.png  \n", "39              https://tunshutech.com/school_logos/雪城大学.png  \n", "40            https://tunshutech.com/school_logos/香港都会大学.png  \n", "41            https://tunshutech.com/school_logos/维克森林大学.png  \n", "42            https://tunshutech.com/school_logos/布兰迪斯大学.png  \n", "43           https://tunshutech.com/school_logos/圣塔克拉拉大学.png  \n", "44             https://tunshutech.com/school_logos/本特利大学.png  \n", "45             https://tunshutech.com/school_logos/叶史瓦大学.png  \n", "46           https://tunshutech.com/school_logos/佐治亚州立大学.png  \n", "47          https://tunshutech.com/school_logos/伦敦大学城市学院.png  "]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["new_school_df"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>学校logo链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "      <td>https://tunshutech.com/school_logos/麻省理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "      <td>https://tunshutech.com/school_logos/帝国理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "      <td>https://tunshutech.com/school_logos/斯坦福大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "      <td>https://tunshutech.com/school_logos/牛津大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "      <td>https://tunshutech.com/school_logos/哈佛大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>545</th>\n", "      <td>佩珀代因大学</td>\n", "      <td>Pepperdine University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/佩珀代因大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>546</th>\n", "      <td>伦敦商学院</td>\n", "      <td>London Business School</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/伦敦商学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>547</th>\n", "      <td>白金汉大学</td>\n", "      <td>University of Buckingham</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/白金汉大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>548</th>\n", "      <td>圣塔克拉拉大学</td>\n", "      <td>Santa Clara University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/圣塔克拉拉大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>549</th>\n", "      <td>本特利大学</td>\n", "      <td>Bentley University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td></td>\n", "      <td>https://tunshutech.com/school_logos/本特利大学.png</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>550 rows × 5 columns</p>\n", "</div>"], "text/plain": ["       学校中文名                                  学校英文名  学校QS排名  \\\n", "0     麻省理工学院  Massachusetts Institute of Technology       1   \n", "1     帝国理工学院                Imperial College London       2   \n", "2      斯坦福大学                    Stanford University       3   \n", "3       牛津大学                   University of Oxford       4   \n", "4       哈佛大学                     Harvard University       5   \n", "..       ...                                    ...     ...   \n", "545   佩珀代因大学                  Pepperdine University    <NA>   \n", "546    伦敦商学院                 London Business School    <NA>   \n", "547    白金汉大学               University of Buckingham    <NA>   \n", "548  圣塔克拉拉大学                 Santa Clara University    <NA>   \n", "549    本特利大学                     Bentley University    <NA>   \n", "\n", "                                学校网站链接  \\\n", "0    https://www.compassedu.hk/univ_85   \n", "1     https://www.compassedu.hk/univ_3   \n", "2    https://www.compassedu.hk/univ_86   \n", "3     https://www.compassedu.hk/univ_8   \n", "4    https://www.compassedu.hk/univ_80   \n", "..                                 ...   \n", "545                                      \n", "546                                      \n", "547                                      \n", "548                                      \n", "549                                      \n", "\n", "                                            学校logo链接  \n", "0     https://tunshutech.com/school_logos/麻省理工学院.png  \n", "1     https://tunshutech.com/school_logos/帝国理工学院.png  \n", "2      https://tunshutech.com/school_logos/斯坦福大学.png  \n", "3       https://tunshutech.com/school_logos/牛津大学.png  \n", "4       https://tunshutech.com/school_logos/哈佛大学.png  \n", "..                                               ...  \n", "545   https://tunshutech.com/school_logos/佩珀代因大学.png  \n", "546    https://tunshutech.com/school_logos/伦敦商学院.png  \n", "547    https://tunshutech.com/school_logos/白金汉大学.png  \n", "548  https://tunshutech.com/school_logos/圣塔克拉拉大学.png  \n", "549    https://tunshutech.com/school_logos/本特利大学.png  \n", "\n", "[550 rows x 5 columns]"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["# 合并新的 DataFrame 到原有的 DataFrame 中\n", "combined_df = pd.concat([aboard_school_df, new_school_df], ignore_index=True)\n", "\n", "# 将学校排名列转换为整型并按排名排序\n", "combined_df['学校QS排名'] = combined_df['学校QS排名'].astype('Int64')\n", "combined_df.sort_values(by='学校QS排名', inplace=True)\n", "combined_df.reset_index(drop=True, inplace=True)\n", "\n", "combined_df"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["# 导出最终的 DataFrame 到 CSV 文件\n", "# combined_df.to_csv('境外院校数据库.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.4 检查排名数据和项目数据中院校中文名一致，但英文名不一致的情况"]}, {"cell_type": "markdown", "metadata": {}, "source": ["等新数据爬下来再进一步检查...应该排名数据中很多是新的了；还有一部分是当时为了对齐QS的名字改的...（那些加了简称的一般都是...）\n", "\n", "是否考虑专门加一列简称列？"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["school_program_df = pd.read_csv('项目数据库（原始爬取版）.csv')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["180\n", "180\n"]}], "source": ["print(len(school_program_df['school_name_cn'].unique()))\n", "print(len(school_program_df['school_name_en'].unique()))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["school_program_df = school_program_df[['school_name_cn','school_name_en','school_ranks']].drop_duplicates()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_ranks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2026qs第2名&lt;br/&gt;2025times第6名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>125</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>2026qs第3名&lt;br/&gt;2025usnews第4名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>2026qs第4名&lt;br/&gt;2025times第3名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>2026qs第5名&lt;br/&gt;2025usnews第3名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10529</th>\n", "      <td>科罗拉多大学波德分校</td>\n", "      <td>University of Colorado—Boulder</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10627</th>\n", "      <td>罗格斯大学纽瓦克分校</td>\n", "      <td>Rutgers University-Newark</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10887</th>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>Technical University of Munich Asia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11064</th>\n", "      <td>香港演艺学院</td>\n", "      <td>The Hong Kong Academy for Performing Arts</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11390</th>\n", "      <td>佐治亚州立大学</td>\n", "      <td>Georgia State University</td>\n", "      <td>2025usnews第196名&lt;br/&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>180 rows × 3 columns</p>\n", "</div>"], "text/plain": ["      school_name_cn                             school_name_en  \\\n", "0             麻省理工学院      Massachusetts Institute of Technology   \n", "13            帝国理工学院                    Imperial College London   \n", "125            斯坦福大学                        Stanford University   \n", "152             牛津大学                       University of Oxford   \n", "333             哈佛大学                         Harvard University   \n", "...              ...                                        ...   \n", "10529     科罗拉多大学波德分校             University of Colorado—Boulder   \n", "10627     罗格斯大学纽瓦克分校                  Rutgers University-Newark   \n", "10887    慕尼黑工业大学亚洲分校        Technical University of Munich Asia   \n", "11064         香港演艺学院  The Hong Kong Academy for Performing Arts   \n", "11390        佐治亚州立大学                   Georgia State University   \n", "\n", "                           school_ranks  \n", "0      2026qs第1名<br/>2025usnews第2名<br/>  \n", "13      2026qs第2名<br/>2025times第6名<br/>  \n", "125    2026qs第3名<br/>2025usnews第4名<br/>  \n", "152     2026qs第4名<br/>2025times第3名<br/>  \n", "333    2026qs第5名<br/>2025usnews第3名<br/>  \n", "...                                 ...  \n", "10529                               NaN  \n", "10627                               NaN  \n", "10887                               NaN  \n", "11064                               NaN  \n", "11390              2025usnews第196名<br/>  \n", "\n", "[180 rows x 3 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["school_program_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["aboard_school_df = pd.read_csv('境外院校数据库.csv', dtype={'学校QS排名': 'Int64'})"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["aboard_school_df.drop(columns=['学校logo链接'], inplace=True)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>545</th>\n", "      <td>佩珀代因大学</td>\n", "      <td>Pepperdine University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>546</th>\n", "      <td>伦敦商学院</td>\n", "      <td>London Business School</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>547</th>\n", "      <td>白金汉大学</td>\n", "      <td>University of Buckingham</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>548</th>\n", "      <td>圣塔克拉拉大学</td>\n", "      <td>Santa Clara University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>549</th>\n", "      <td>本特利大学</td>\n", "      <td>Bentley University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>550 rows × 4 columns</p>\n", "</div>"], "text/plain": ["       学校中文名                                  学校英文名  学校QS排名  \\\n", "0     麻省理工学院  Massachusetts Institute of Technology       1   \n", "1     帝国理工学院                Imperial College London       2   \n", "2      斯坦福大学                    Stanford University       3   \n", "3       牛津大学                   University of Oxford       4   \n", "4       哈佛大学                     Harvard University       5   \n", "..       ...                                    ...     ...   \n", "545   佩珀代因大学                  Pepperdine University    <NA>   \n", "546    伦敦商学院                 London Business School    <NA>   \n", "547    白金汉大学               University of Buckingham    <NA>   \n", "548  圣塔克拉拉大学                 Santa Clara University    <NA>   \n", "549    本特利大学                     Bentley University    <NA>   \n", "\n", "                                学校网站链接  \n", "0    https://www.compassedu.hk/univ_85  \n", "1     https://www.compassedu.hk/univ_3  \n", "2    https://www.compassedu.hk/univ_86  \n", "3     https://www.compassedu.hk/univ_8  \n", "4    https://www.compassedu.hk/univ_80  \n", "..                                 ...  \n", "545                                NaN  \n", "546                                NaN  \n", "547                                NaN  \n", "548                                NaN  \n", "549                                NaN  \n", "\n", "[550 rows x 4 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# 合并两个 DataFrame\n", "merged_df = pd.merge(aboard_school_df, school_program_df, left_on='学校中文名', right_on='school_name_cn',how='inner')\n", "\n", "# 找出学校中文名相同但学校英文名不同的行\n", "result_df = merged_df[merged_df['学校英文名'] != merged_df['school_name_en']]"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_ranks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>明尼苏达大学双城分校</td>\n", "      <td>University of Minnesota Twin Cities</td>\n", "      <td>210</td>\n", "      <td>NaN</td>\n", "      <td>明尼苏达大学双城分校</td>\n", "      <td>University of Minnesota-Twin Cities</td>\n", "      <td>2025usnews第54名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>北卡罗来纳州立大学罗利分校</td>\n", "      <td>North Carolina State University</td>\n", "      <td>272</td>\n", "      <td>NaN</td>\n", "      <td>北卡罗来纳州立大学罗利分校</td>\n", "      <td>North Carolina State University—Raleigh</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>科罗拉多大学波德分校</td>\n", "      <td>University of Colorado Boulder</td>\n", "      <td>299</td>\n", "      <td>NaN</td>\n", "      <td>科罗拉多大学波德分校</td>\n", "      <td>University of Colorado—Boulder</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>132</th>\n", "      <td>伦敦大学亚非学院</td>\n", "      <td>SOAS University of London</td>\n", "      <td>511</td>\n", "      <td>NaN</td>\n", "      <td>伦敦大学亚非学院</td>\n", "      <td>SOA<PERSON>, University of London</td>\n", "      <td>2025times第65名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>德州大学达拉斯分校</td>\n", "      <td>University of Texas Dallas</td>\n", "      <td>597</td>\n", "      <td>NaN</td>\n", "      <td>德州大学达拉斯分校</td>\n", "      <td>University of Texas-Dallas</td>\n", "      <td>2025usnews第109名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>香港都会大学</td>\n", "      <td>HongKong Metropolitan University</td>\n", "      <td>781</td>\n", "      <td>NaN</td>\n", "      <td>香港都会大学</td>\n", "      <td>Hong Kong Metropolitan University</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>威廉玛丽学院</td>\n", "      <td><PERSON></td>\n", "      <td>1001</td>\n", "      <td>NaN</td>\n", "      <td>威廉玛丽学院</td>\n", "      <td><PERSON> &amp; Mary</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>167</th>\n", "      <td>香港树仁大学</td>\n", "      <td>HongKong S hueY an University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>香港树仁大学</td>\n", "      <td>Hong Kong Shue Yan University</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169</th>\n", "      <td>纽约市立大学巴鲁克学院</td>\n", "      <td><PERSON>, CUNY</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>纽约市立大学巴鲁克学院</td>\n", "      <td>Bernard <PERSON>，CUNY</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             学校中文名                                学校英文名  学校QS排名 学校网站链接  \\\n", "93      明尼苏达大学双城分校  University of Minnesota Twin Cities     210    NaN   \n", "104  北卡罗来纳州立大学罗利分校      North Carolina State University     272    NaN   \n", "114     科罗拉多大学波德分校       University of Colorado Boulder     299    NaN   \n", "132       伦敦大学亚非学院           SOAS University of London      511    NaN   \n", "142      德州大学达拉斯分校           University of Texas Dallas     597    NaN   \n", "152         香港都会大学     HongKong Metropolitan University     781    NaN   \n", "163         威廉玛丽学院                         <PERSON>    1001    NaN   \n", "167         香港树仁大学        HongKong S hueY an University    <NA>    NaN   \n", "169    纽约市立大学巴鲁克学院      Bernard M. <PERSON>uch College, CUNY    <NA>    NaN   \n", "\n", "    school_name_cn                           school_name_en  \\\n", "93      明尼苏达大学双城分校      University of Minnesota-Twin Cities   \n", "104  北卡罗来纳州立大学罗利分校  North Carolina State University—Raleigh   \n", "114     科罗拉多大学波德分校           University of Colorado—Boulder   \n", "132       伦敦大学亚非学院               SOAS, University of London   \n", "142      德州大学达拉斯分校               University of Texas-Dallas   \n", "152         香港都会大学        Hong Kong Metropolitan University   \n", "163         威廉玛丽学院                           William & Mary   \n", "167         香港树仁大学            Hong Kong Shue Yan University   \n", "169    纽约市立大学巴鲁克学院            Bernard M.Baruch College，CUNY   \n", "\n", "             school_ranks  \n", "93    2025usnews第54名<br/>  \n", "104                   NaN  \n", "114                   NaN  \n", "132    2025times第65名<br/>  \n", "142  2025usnews第109名<br/>  \n", "152                   NaN  \n", "163                   NaN  \n", "167                   NaN  \n", "169                   NaN  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["result_df"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_ranks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2026qs第2名&lt;br/&gt;2025times第6名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>2026qs第3名&lt;br/&gt;2025usnews第4名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>2026qs第4名&lt;br/&gt;2025times第3名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>2026qs第5名&lt;br/&gt;2025usnews第3名&lt;br/&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>175</th>\n", "      <td>科罗拉多大学波德分校</td>\n", "      <td>University of Colorado Boulder</td>\n", "      <td>299</td>\n", "      <td>NaN</td>\n", "      <td>科罗拉多大学波德分校</td>\n", "      <td>University of Colorado—Boulder</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>罗格斯大学纽瓦克分校</td>\n", "      <td>Rutgers University-Newark</td>\n", "      <td>771</td>\n", "      <td>NaN</td>\n", "      <td>罗格斯大学纽瓦克分校</td>\n", "      <td>Rutgers University-Newark</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>Technical University of Munich Asia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>香港演艺学院</td>\n", "      <td>The Hong Kong Academy for Performing Arts</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>佐治亚州立大学</td>\n", "      <td>Georgia State University</td>\n", "      <td>781</td>\n", "      <td>NaN</td>\n", "      <td>佐治亚州立大学</td>\n", "      <td>Georgia State University</td>\n", "      <td>2025usnews第196名&lt;br/&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>180 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          学校中文名                                  学校英文名  学校QS排名  \\\n", "0        麻省理工学院  Massachusetts Institute of Technology       1   \n", "1        帝国理工学院                Imperial College London       2   \n", "2         斯坦福大学                    Stanford University       3   \n", "3          牛津大学                   University of Oxford       4   \n", "4          哈佛大学                     Harvard University       5   \n", "..          ...                                    ...     ...   \n", "175  科罗拉多大学波德分校         University of Colorado Boulder     299   \n", "176  罗格斯大学纽瓦克分校              Rutgers University-Newark     771   \n", "177         NaN                                    NaN    <NA>   \n", "178         NaN                                    NaN    <NA>   \n", "179     佐治亚州立大学               Georgia State University     781   \n", "\n", "                                学校网站链接 school_name_cn  \\\n", "0    https://www.compassedu.hk/univ_85         麻省理工学院   \n", "1     https://www.compassedu.hk/univ_3         帝国理工学院   \n", "2    https://www.compassedu.hk/univ_86          斯坦福大学   \n", "3     https://www.compassedu.hk/univ_8           牛津大学   \n", "4    https://www.compassedu.hk/univ_80           哈佛大学   \n", "..                                 ...            ...   \n", "175                                NaN     科罗拉多大学波德分校   \n", "176                                NaN     罗格斯大学纽瓦克分校   \n", "177                                NaN    慕尼黑工业大学亚洲分校   \n", "178                                NaN         香港演艺学院   \n", "179                                NaN        佐治亚州立大学   \n", "\n", "                                school_name_en  \\\n", "0        Massachusetts Institute of Technology   \n", "1                      Imperial College London   \n", "2                          Stanford University   \n", "3                         University of Oxford   \n", "4                           Harvard University   \n", "..                                         ...   \n", "175             University of Colorado—Boulder   \n", "176                  Rutgers University-Newark   \n", "177        Technical University of Munich Asia   \n", "178  The Hong Kong Academy for Performing Arts   \n", "179                   Georgia State University   \n", "\n", "                         school_ranks  \n", "0    2026qs第1名<br/>2025usnews第2名<br/>  \n", "1     2026qs第2名<br/>2025times第6名<br/>  \n", "2    2026qs第3名<br/>2025usnews第4名<br/>  \n", "3     2026qs第4名<br/>2025times第3名<br/>  \n", "4    2026qs第5名<br/>2025usnews第3名<br/>  \n", "..                                ...  \n", "175                               NaN  \n", "176                               NaN  \n", "177                               NaN  \n", "178                               NaN  \n", "179              2025usnews第196名<br/>  \n", "\n", "[180 rows x 7 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["right_merged_df = pd.merge(aboard_school_df, school_program_df, left_on='学校中文名', right_on='school_name_cn',how='right')\n", "right_merged_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_ranks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>南安普顿大学马来西亚分校</td>\n", "      <td>University of Southampton Malaysia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>圣何塞州立大学</td>\n", "      <td>San Jose State University</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>马来西亚莫纳什大学</td>\n", "      <td>Monash University Malaysia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>Technical University of Munich Asia</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>NaN</td>\n", "      <td>香港演艺学院</td>\n", "      <td>The Hong Kong Academy for Performing Arts</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    学校中文名 学校英文名  学校QS排名 学校网站链接 school_name_cn  \\\n", "158   NaN   NaN    <NA>    NaN   南安普顿大学马来西亚分校   \n", "170   NaN   NaN    <NA>    NaN        圣何塞州立大学   \n", "173   NaN   NaN    <NA>    NaN      马来西亚莫纳什大学   \n", "177   NaN   NaN    <NA>    NaN    慕尼黑工业大学亚洲分校   \n", "178   NaN   NaN    <NA>    NaN         香港演艺学院   \n", "\n", "                                school_name_en school_ranks  \n", "158         University of Southampton Malaysia          NaN  \n", "170                  San Jose State University          NaN  \n", "173                 Monash University Malaysia          NaN  \n", "177        Technical University of Munich Asia          NaN  \n", "178  The Hong Kong Academy for Performing Arts          NaN  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# 新增的原境外院校数据库中没有的学校...\n", "right_merged_df[right_merged_df['学校中文名'].isna()]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_29880\\2324763138.py:4: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  output_df.rename(columns={'学校QS排名': 'qs2026'}, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_29880\\2324763138.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  output_df.sort_values(by='qs2026', inplace=True)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>qs2026</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>172</th>\n", "      <td>香港树仁大学</td>\n", "      <td>Hong Kong Shue Yan University</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>马来西亚莫纳什大学</td>\n", "      <td>Monash University Malaysia</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>本特利大学</td>\n", "      <td>Bentley University</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>Technical University of Munich Asia</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>香港演艺学院</td>\n", "      <td>The Hong Kong Academy for Performing Arts</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>180 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                             school_name_en  qs2026\n", "0           麻省理工学院      Massachusetts Institute of Technology       1\n", "1           帝国理工学院                    Imperial College London       2\n", "2            斯坦福大学                        Stanford University       3\n", "3             牛津大学                       University of Oxford       4\n", "4             哈佛大学                         Harvard University       5\n", "..             ...                                        ...     ...\n", "172         香港树仁大学              Hong Kong Shue Yan University    <NA>\n", "173      马来西亚莫纳什大学                 Monash University Malaysia    <NA>\n", "174          本特利大学                         Bentley University    <NA>\n", "177    慕尼黑工业大学亚洲分校        Technical University of Munich Asia    <NA>\n", "178         香港演艺学院  The Hong Kong Academy for Performing Arts    <NA>\n", "\n", "[180 rows x 3 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["output_df = right_merged_df[['school_name_cn','school_name_en','学校QS排名']]\n", "\n", "# 重命名列名\n", "output_df.rename(columns={'学校QS排名': 'qs2026'}, inplace=True)\n", "\n", "# 按照qs2026列进行排序\n", "output_df.sort_values(by='qs2026', inplace=True)\n", "\n", "output_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导出到CSV文件\n", "# output_df.to_csv('university_rankings.csv', index=False, encoding='utf-8-sig')\n", "# 改名为program_schools_qs2026.csv后手动调整恢复成700+的院校的排名范围...供项目数据库合并表排名使用..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.5 项目数据更新后新增院校的信息补充"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["program_df = pd.read_csv('项目数据库.csv')\n", "aboard_school_df = pd.read_csv('境外院校数据库.csv', dtype={'学校QS排名': 'Int64'})"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'南安普顿大学马来西亚分校', '圣何塞州立大学', '慕尼黑工业大学亚洲分校', '香港演艺学院', '马来西亚莫纳什大学'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["set(program_df['school_name_cn'].unique()) - set(aboard_school_df['学校中文名'].unique())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_55864\\2684889754.py:11: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  aboard_school_df = pd.concat([aboard_school_df, new_schools_df], ignore_index=True)\n"]}], "source": ["new_schools = [\n", "    {'学校中文名': '南安普顿大学马来西亚分校', '学校英文名': 'University of Southampton Malaysia', '学校QS排名': None, '学校网站链接': 'https://www.compassedu.hk/univ_3012', '学校logo链接': 'https://tunshutech.com/school_logos/南安普顿大学马来西亚分校.png'},\n", "    {'学校中文名': '圣何塞州立大学', '学校英文名': 'San Jose State University', '学校QS排名': None, '学校网站链接': 'https://www.compassedu.hk/univ_320', '学校logo链接': 'https://tunshutech.com/school_logos/圣何塞州立大学.png'},\n", "    {'学校中文名': '慕尼黑工业大学亚洲分校', '学校英文名': 'Technical University of Munich Asia', '学校QS排名': None, '学校网站链接': 'https://www.compassedu.hk/univ_348', '学校logo链接': 'https://tunshutech.com/school_logos/慕尼黑工业大学亚洲分校.png'},\n", "    {'学校中文名': '香港演艺学院', '学校英文名': 'The Hong Kong Academy for Performing Arts', '学校QS排名': None, '学校网站链接': 'https://www.compassedu.hk/univ_2397', '学校logo链接': 'https://tunshutech.com/school_logos/香港演艺学院.png'},\n", "    {'学校中文名': '马来西亚莫纳什大学', '学校英文名': 'Monash University Malaysia', '学校QS排名': None, '学校网站链接': 'https://www.compassedu.hk/univ_2940', '学校logo链接': 'https://tunshutech.com/school_logos/马来西亚莫纳什大学.png'}\n", "]\n", "\n", "# 将新的学校数据转换为 DataFrame 并追加到原 DataFrame 中\n", "new_schools_df = pd.DataFrame(new_schools)\n", "aboard_school_df = pd.concat([aboard_school_df, new_schools_df], ignore_index=True)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校中文名</th>\n", "      <th>学校英文名</th>\n", "      <th>学校QS排名</th>\n", "      <th>学校网站链接</th>\n", "      <th>学校logo链接</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>1</td>\n", "      <td>https://www.compassedu.hk/univ_85</td>\n", "      <td>https://tunshutech.com/school_logos/麻省理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>2</td>\n", "      <td>https://www.compassedu.hk/univ_3</td>\n", "      <td>https://tunshutech.com/school_logos/帝国理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>3</td>\n", "      <td>https://www.compassedu.hk/univ_86</td>\n", "      <td>https://tunshutech.com/school_logos/斯坦福大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>4</td>\n", "      <td>https://www.compassedu.hk/univ_8</td>\n", "      <td>https://tunshutech.com/school_logos/牛津大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>5</td>\n", "      <td>https://www.compassedu.hk/univ_80</td>\n", "      <td>https://tunshutech.com/school_logos/哈佛大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>550</th>\n", "      <td>南安普顿大学马来西亚分校</td>\n", "      <td>University of Southampton Malaysia</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>https://www.compassedu.hk/univ_3012</td>\n", "      <td>https://tunshutech.com/school_logos/南安普顿大学马来西亚...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>551</th>\n", "      <td>圣何塞州立大学</td>\n", "      <td>San Jose State University</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>https://www.compassedu.hk/univ_320</td>\n", "      <td>https://tunshutech.com/school_logos/圣何塞州立大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>552</th>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>Technical University of Munich Asia</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>https://www.compassedu.hk/univ_348</td>\n", "      <td>https://tunshutech.com/school_logos/慕尼黑工业大学亚洲分...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>553</th>\n", "      <td>香港演艺学院</td>\n", "      <td>The Hong Kong Academy for Performing Arts</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>https://www.compassedu.hk/univ_2397</td>\n", "      <td>https://tunshutech.com/school_logos/香港演艺学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>554</th>\n", "      <td>马来西亚莫纳什大学</td>\n", "      <td>Monash University Malaysia</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>https://www.compassedu.hk/univ_2940</td>\n", "      <td>https://tunshutech.com/school_logos/马来西亚莫纳什大学.png</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>555 rows × 5 columns</p>\n", "</div>"], "text/plain": ["            学校中文名                                      学校英文名  学校QS排名  \\\n", "0          麻省理工学院      Massachusetts Institute of Technology       1   \n", "1          帝国理工学院                    Imperial College London       2   \n", "2           斯坦福大学                        Stanford University       3   \n", "3            牛津大学                       University of Oxford       4   \n", "4            哈佛大学                         Harvard University       5   \n", "..            ...                                        ...     ...   \n", "550  南安普顿大学马来西亚分校         University of Southampton Malaysia    <NA>   \n", "551       圣何塞州立大学                  San Jose State University    <NA>   \n", "552   慕尼黑工业大学亚洲分校        Technical University of Munich Asia    <NA>   \n", "553        香港演艺学院  The Hong Kong Academy for Performing Arts    <NA>   \n", "554     马来西亚莫纳什大学                 Monash University Malaysia    <NA>   \n", "\n", "                                  学校网站链接  \\\n", "0      https://www.compassedu.hk/univ_85   \n", "1       https://www.compassedu.hk/univ_3   \n", "2      https://www.compassedu.hk/univ_86   \n", "3       https://www.compassedu.hk/univ_8   \n", "4      https://www.compassedu.hk/univ_80   \n", "..                                   ...   \n", "550  https://www.compassedu.hk/univ_3012   \n", "551   https://www.compassedu.hk/univ_320   \n", "552   https://www.compassedu.hk/univ_348   \n", "553  https://www.compassedu.hk/univ_2397   \n", "554  https://www.compassedu.hk/univ_2940   \n", "\n", "                                              学校logo链接  \n", "0       https://tunshutech.com/school_logos/麻省理工学院.png  \n", "1       https://tunshutech.com/school_logos/帝国理工学院.png  \n", "2        https://tunshutech.com/school_logos/斯坦福大学.png  \n", "3         https://tunshutech.com/school_logos/牛津大学.png  \n", "4         https://tunshutech.com/school_logos/哈佛大学.png  \n", "..                                                 ...  \n", "550  https://tunshutech.com/school_logos/南安普顿大学马来西亚...  \n", "551    https://tunshutech.com/school_logos/圣何塞州立大学.png  \n", "552  https://tunshutech.com/school_logos/慕尼黑工业大学亚洲分...  \n", "553     https://tunshutech.com/school_logos/香港演艺学院.png  \n", "554  https://tunshutech.com/school_logos/马来西亚莫纳什大学.png  \n", "\n", "[555 rows x 5 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导出到CSV文件\n", "# aboard_school_df.to_csv('境外院校数据库.csv', index=False, encoding='utf-8-sig')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.6 其他排名与院校其他信息添加"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 境外院校数据和数据库模型全部更新，新增四大排名数据和地区城市数据等；从原QS500院校减至181个项目数据库中含有的院校；\n", "> \n", "> 注意该部分新数据库df文件获得的代码和人工操作不在此notebook中（见飞书 https://tunshutech.feishu.cn/wiki/JStuw42jgiNRd1k0PdGcP2Einwc?from=from_copylink）...不过新数据库df的生成依靠了前面数据处理得到的df"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["aboard_school_df = pd.read_csv(\n", "    '境外院校数据库.csv',\n", "        dtype={\n", "        'school_code': 'Int64', \n", "        'school_usnews_rank': 'object'\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>school_name_cn</th>\n", "      <th>school_name_en</th>\n", "      <th>school_code</th>\n", "      <th>school_city</th>\n", "      <th>school_region</th>\n", "      <th>school_qs_rank</th>\n", "      <th>school_usnews_rank</th>\n", "      <th>school_the_rank</th>\n", "      <th>school_arwu_rank</th>\n", "      <th>school_ranks</th>\n", "      <th>school_labels</th>\n", "      <th>school_logo_url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>麻省理工学院</td>\n", "      <td>Massachusetts Institute of Technology</td>\n", "      <td>85</td>\n", "      <td>马萨诸塞州</td>\n", "      <td>美国</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>2026qs第1名&lt;br/&gt;2025usnews第2名&lt;br/&gt;</td>\n", "      <td>爱国者联盟&lt;br&gt;美国大学协会&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>https://tunshutech.com/school_logos/麻省理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>帝国理工学院</td>\n", "      <td>Imperial College London</td>\n", "      <td>3</td>\n", "      <td>伦敦</td>\n", "      <td>英国</td>\n", "      <td>2</td>\n", "      <td>11</td>\n", "      <td>9</td>\n", "      <td>25</td>\n", "      <td>2026qs第2名&lt;br/&gt;2025times第6名&lt;br/&gt;</td>\n", "      <td>G5超级精英大学&lt;br&gt;金三角名校&lt;br&gt;</td>\n", "      <td>https://tunshutech.com/school_logos/帝国理工学院.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>斯坦福大学</td>\n", "      <td>Stanford University</td>\n", "      <td>86</td>\n", "      <td>加利福尼亚州</td>\n", "      <td>美国</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>2026qs第3名&lt;br/&gt;2025usnews第4名&lt;br/&gt;</td>\n", "      <td>新常春藤&lt;br&gt;美国大学协会&lt;br&gt;全球大学高研院联盟&lt;br&gt;</td>\n", "      <td>https://tunshutech.com/school_logos/斯坦福大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>牛津大学</td>\n", "      <td>University of Oxford</td>\n", "      <td>8</td>\n", "      <td>牛津</td>\n", "      <td>英国</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>2026qs第4名&lt;br/&gt;2025times第3名&lt;br/&gt;</td>\n", "      <td>罗素大学集团&lt;br&gt;金三角名校&lt;br&gt;G5超级精英大学&lt;br&gt;全球大学校长论坛&lt;br&gt;</td>\n", "      <td>https://tunshutech.com/school_logos/牛津大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>哈佛大学</td>\n", "      <td>Harvard University</td>\n", "      <td>80</td>\n", "      <td>马萨诸塞州</td>\n", "      <td>美国</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>2026qs第5名&lt;br/&gt;2025usnews第3名&lt;br/&gt;</td>\n", "      <td>常春藤联盟&lt;br&gt;全球大学高研院联盟&lt;br&gt;</td>\n", "      <td>https://tunshutech.com/school_logos/哈佛大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>香港树仁大学</td>\n", "      <td>Hong Kong Shue Yan University</td>\n", "      <td>403</td>\n", "      <td>中国香港</td>\n", "      <td>中国香港</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>香港法定大学&lt;br&gt;财政自资私立大学&lt;br&gt;</td>\n", "      <td>https://tunshutech.com/school_logos/香港树仁大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>马来西亚莫纳什大学</td>\n", "      <td>Monash University Malaysia</td>\n", "      <td>2940</td>\n", "      <td>梳邦再也</td>\n", "      <td>马来西亚</td>\n", "      <td>36</td>\n", "      <td>38</td>\n", "      <td>58</td>\n", "      <td>82</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>https://tunshutech.com/school_logos/马来西亚莫纳什大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>本特利大学</td>\n", "      <td>Bentley University</td>\n", "      <td>302</td>\n", "      <td>马萨诸塞州</td>\n", "      <td>美国</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>私立大学&lt;br&gt;</td>\n", "      <td>https://tunshutech.com/school_logos/本特利大学.png</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>慕尼黑工业大学亚洲分校</td>\n", "      <td>Technical University of Munich Asia</td>\n", "      <td>348</td>\n", "      <td>新加坡</td>\n", "      <td>新加坡</td>\n", "      <td>22</td>\n", "      <td>79</td>\n", "      <td>26</td>\n", "      <td>47</td>\n", "      <td>NaN</td>\n", "      <td>多元文化&lt;br&gt;</td>\n", "      <td>https://tunshutech.com/school_logos/慕尼黑工业大学亚洲分...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>香港演艺学院</td>\n", "      <td>The Hong Kong Academy for Performing Arts</td>\n", "      <td>2387</td>\n", "      <td>香港</td>\n", "      <td>中国香港</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>https://tunshutech.com/school_logos/香港演艺学院.png</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>181 rows × 12 columns</p>\n", "</div>"], "text/plain": ["    school_name_cn                             school_name_en  school_code  \\\n", "0           麻省理工学院      Massachusetts Institute of Technology           85   \n", "1           帝国理工学院                    Imperial College London            3   \n", "2            斯坦福大学                        Stanford University           86   \n", "3             牛津大学                       University of Oxford            8   \n", "4             哈佛大学                         Harvard University           80   \n", "..             ...                                        ...          ...   \n", "176         香港树仁大学              Hong Kong Shue Yan University          403   \n", "177      马来西亚莫纳什大学                 Monash University Malaysia         2940   \n", "178          本特利大学                         Bentley University          302   \n", "179    慕尼黑工业大学亚洲分校        Technical University of Munich Asia          348   \n", "180         香港演艺学院  The Hong Kong Academy for Performing Arts         2387   \n", "\n", "    school_city school_region school_qs_rank school_usnews_rank  \\\n", "0         马萨诸塞州            美国              1                  2   \n", "1            伦敦            英国              2                 11   \n", "2        加利福尼亚州            美国              3                  3   \n", "3            牛津            英国              4                  4   \n", "4         马萨诸塞州            美国              5                  1   \n", "..          ...           ...            ...                ...   \n", "176        中国香港          中国香港            NaN                NaN   \n", "177        梳邦再也          马来西亚             36                 38   \n", "178       马萨诸塞州            美国            NaN                NaN   \n", "179         新加坡           新加坡             22                 79   \n", "180          香港          中国香港            NaN                NaN   \n", "\n", "    school_the_rank school_arwu_rank                      school_ranks  \\\n", "0                 2                3  2026qs第1名<br/>2025usnews第2名<br/>   \n", "1                 9               25   2026qs第2名<br/>2025times第6名<br/>   \n", "2                 6                2  2026qs第3名<br/>2025usnews第4名<br/>   \n", "3                 1                6   2026qs第4名<br/>2025times第3名<br/>   \n", "4                 3                1  2026qs第5名<br/>2025usnews第3名<br/>   \n", "..              ...              ...                               ...   \n", "176             NaN              NaN                               NaN   \n", "177              58               82                               NaN   \n", "178             NaN              NaN                               NaN   \n", "179              26               47                               NaN   \n", "180             NaN              NaN                               NaN   \n", "\n", "                                   school_labels  \\\n", "0                爱国者联盟<br>美国大学协会<br>全球大学校长论坛<br>   \n", "1                          G5超级精英大学<br>金三角名校<br>   \n", "2                新常春藤<br>美国大学协会<br>全球大学高研院联盟<br>   \n", "3    罗素大学集团<br>金三角名校<br>G5超级精英大学<br>全球大学校长论坛<br>   \n", "4                         常春藤联盟<br>全球大学高研院联盟<br>   \n", "..                                           ...   \n", "176                       香港法定大学<br>财政自资私立大学<br>   \n", "177                                          NaN   \n", "178                                     私立大学<br>   \n", "179                                     多元文化<br>   \n", "180                                          NaN   \n", "\n", "                                       school_logo_url  \n", "0       https://tunshutech.com/school_logos/麻省理工学院.png  \n", "1       https://tunshutech.com/school_logos/帝国理工学院.png  \n", "2        https://tunshutech.com/school_logos/斯坦福大学.png  \n", "3         https://tunshutech.com/school_logos/牛津大学.png  \n", "4         https://tunshutech.com/school_logos/哈佛大学.png  \n", "..                                                 ...  \n", "176     https://tunshutech.com/school_logos/香港树仁大学.png  \n", "177  https://tunshutech.com/school_logos/马来西亚莫纳什大学.png  \n", "178      https://tunshutech.com/school_logos/本特利大学.png  \n", "179  https://tunshutech.com/school_logos/慕尼黑工业大学亚洲分...  \n", "180     https://tunshutech.com/school_logos/香港演艺学院.png  \n", "\n", "[181 rows x 12 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["school_name_cn         0\n", "school_name_en         0\n", "school_code            0\n", "school_city            0\n", "school_region          0\n", "school_qs_rank        12\n", "school_usnews_rank    13\n", "school_the_rank       25\n", "school_arwu_rank      29\n", "school_ranks          22\n", "school_labels          4\n", "school_logo_url        0\n", "dtype: int64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'school_name_cn': 13,\n", " 'school_name_en': 67,\n", " 'school_city': 9,\n", " 'school_region': 4,\n", " 'school_qs_rank': 9,\n", " 'school_usnews_rank': 4,\n", " 'school_the_rank': 8,\n", " 'school_arwu_rank': 8,\n", " 'school_ranks': 36,\n", " 'school_labels': 107,\n", " 'school_logo_url': 53}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 检查字符串长度, 用于后续数据库字段长度设置\n", "def longest_string_length(df):\n", "    max_lengths = {}\n", "    for column in df.select_dtypes(exclude=[int, float]).columns:\n", "        if df[column].dtype == object or df[column].dtype.name.startswith('str'):\n", "            max_lengths[column] = int(df[column].astype(str).apply(len).max())\n", "    return max_lengths\n", "\n", "length_check_result = longest_string_length(aboard_school_df)\n", "\n", "length_check_result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 院校Logo链接更新"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["aboard_school_df = pd.read_csv(\n", "    '境外院校数据库.csv',\n", "        dtype={\n", "        'school_code': 'Int64', \n", "        'school_usnews_rank': 'object'\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["0         https://tunshutech.com/school_logos/麻省理工学院.png\n", "1         https://tunshutech.com/school_logos/帝国理工学院.png\n", "2          https://tunshutech.com/school_logos/斯坦福大学.png\n", "3           https://tunshutech.com/school_logos/牛津大学.png\n", "4           https://tunshutech.com/school_logos/哈佛大学.png\n", "                             ...                        \n", "176       https://tunshutech.com/school_logos/香港树仁大学.png\n", "177    https://tunshutech.com/school_logos/马来西亚莫纳什大学.png\n", "178        https://tunshutech.com/school_logos/本特利大学.png\n", "179    https://tunshutech.com/school_logos/慕尼黑工业大学亚洲分...\n", "180       https://tunshutech.com/school_logos/香港演艺学院.png\n", "Name: school_logo_url, Length: 181, dtype: object"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df['school_logo_url']"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 更新school_logo_url列中的字符串\n", "aboard_school_df['school_logo_url'] = aboard_school_df['school_logo_url'].str.replace('tunshutech.com', 'tunshuedu.com')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["0          https://tunshuedu.com/school_logos/麻省理工学院.png\n", "1          https://tunshuedu.com/school_logos/帝国理工学院.png\n", "2           https://tunshuedu.com/school_logos/斯坦福大学.png\n", "3            https://tunshuedu.com/school_logos/牛津大学.png\n", "4            https://tunshuedu.com/school_logos/哈佛大学.png\n", "                             ...                        \n", "176        https://tunshuedu.com/school_logos/香港树仁大学.png\n", "177     https://tunshuedu.com/school_logos/马来西亚莫纳什大学.png\n", "178         https://tunshuedu.com/school_logos/本特利大学.png\n", "179    https://tunshuedu.com/school_logos/慕尼黑工业大学亚洲分校...\n", "180        https://tunshuedu.com/school_logos/香港演艺学院.png\n", "Name: school_logo_url, Length: 181, dtype: object"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["aboard_school_df['school_logo_url']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# aboard_school_df.to_csv('境外院校数据库.csv', index=False, encoding='utf-8-sig')"]}], "metadata": {"kernelspec": {"display_name": "tunshu_data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}