<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API配置测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .config-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>🔧 API配置测试页面</h1>
    
    <div class="info">
        <strong>说明：</strong>此页面用于测试前端API配置是否正确，验证不同环境下的API地址解析。
    </div>

    <h2>📋 当前配置</h2>
    <div id="config-info"></div>

    <h2>🧪 API连接测试</h2>
    <button onclick="testApiConnection()">测试API连接</button>
    <button onclick="testWeChatConfig()">测试微信配置</button>
    <button onclick="testAuthEndpoint()">测试认证端点</button>
    
    <div id="test-results"></div>

    <h2>🔍 环境信息</h2>
    <div id="env-info"></div>

    <script>
        // 显示当前配置
        function showCurrentConfig() {
            const configDiv = document.getElementById('config-info');
            
            // 模拟获取环境变量（在实际Vue应用中会从import.meta.env获取）
            const apiUrl = window.location.hostname === 'localhost' ? '' : 'https://api.tunshuedu.com';
            const isDev = window.location.hostname === 'localhost';
            
            configDiv.innerHTML = `
                <div class="config-item">
                    <strong>API基础URL:</strong> ${apiUrl || '(使用代理)'}
                </div>
                <div class="config-item">
                    <strong>环境模式:</strong> ${isDev ? '开发环境' : '生产环境'}
                </div>
                <div class="config-item">
                    <strong>当前域名:</strong> ${window.location.origin}
                </div>
                <div class="config-item">
                    <strong>代理状态:</strong> ${isDev ? '启用 (Vite代理)' : '禁用'}
                </div>
            `;
        }

        // 测试API连接
        async function testApiConnection() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="info">正在测试API连接...</div>';
            
            try {
                const response = await fetch('/api/auth/wechat/qr-config');
                const data = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            ✅ API连接成功！
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            ❌ API连接失败: ${response.status} ${response.statusText}
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        ❌ 网络错误: ${error.message}
                        <br><strong>可能原因:</strong>
                        <ul>
                            <li>后端服务未启动</li>
                            <li>代理配置错误</li>
                            <li>跨域问题</li>
                        </ul>
                    </div>
                `;
            }
        }

        // 测试微信配置
        async function testWeChatConfig() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="info">正在测试微信配置...</div>';
            
            try {
                const response = await fetch('/api/auth/wechat/qr-config');
                const data = await response.json();
                
                if (response.ok && data.appid) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            ✅ 微信配置正常！
                            <div class="config-item">
                                <strong>AppID:</strong> ${data.appid}
                            </div>
                            <div class="config-item">
                                <strong>回调地址:</strong> ${data.redirect_uri}
                            </div>
                            <div class="config-item">
                                <strong>State:</strong> ${data.state?.substring(0, 20)}...
                            </div>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="warning">
                            ⚠️ 微信配置可能有问题
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ 微信配置测试失败: ${error.message}</div>
                `;
            }
        }

        // 测试认证端点
        async function testAuthEndpoint() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="info">正在测试认证端点...</div>';
            
            try {
                const response = await fetch('/api/auth/me');
                
                if (response.status === 401) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            ✅ 认证端点正常！(返回401未授权，符合预期)
                        </div>
                    `;
                } else {
                    const data = await response.json();
                    resultsDiv.innerHTML = `
                        <div class="info">
                            ℹ️ 认证端点响应: ${response.status}
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">❌ 认证端点测试失败: ${error.message}</div>
                `;
            }
        }

        // 显示环境信息
        function showEnvironmentInfo() {
            const envDiv = document.getElementById('env-info');
            envDiv.innerHTML = `
                <div class="config-item">
                    <strong>User Agent:</strong> ${navigator.userAgent}
                </div>
                <div class="config-item">
                    <strong>当前时间:</strong> ${new Date().toLocaleString()}
                </div>
                <div class="config-item">
                    <strong>页面URL:</strong> ${window.location.href}
                </div>
            `;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            showCurrentConfig();
            showEnvironmentInfo();
        });
    </script>
</body>
</html>
