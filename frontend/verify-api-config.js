#!/usr/bin/env node
/**
 * API配置验证脚本
 * 
 * 用于验证前端API配置是否正确，检查硬编码URL问题
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function checkEnvironmentFiles() {
  log('blue', '\n🔍 检查环境配置文件...')

  const envFiles = [
    '.env',
    '.env.development',
    '.env.production',
    '.env.example'
  ]

  let allGood = true

  envFiles.forEach(file => {
    const filePath = path.join(__dirname, file)
    if (fs.existsSync(filePath)) {
      log('green', `✅ ${file} 存在`)

      // 检查内容 - 允许在VITE_BACKEND_URL和注释中使用localhost:8000
      const content = fs.readFileSync(filePath, 'utf8')
      const lines = content.split('\n')

      let hasProblematicHardcode = false
      lines.forEach((line, index) => {
        if (line.includes('localhost:8000')) {
          // 允许的情况：
          // 1. 注释行
          // 2. VITE_BACKEND_URL 配置
          // 3. 示例或说明文本
          const isComment = line.trim().startsWith('#')
          const isBackendUrl = line.includes('VITE_BACKEND_URL')
          const isApiUrl = line.includes('VITE_API_URL=http://localhost:8000')

          if (isApiUrl) {
            log('red', `❌ ${file}:${index + 1} VITE_API_URL 不应该硬编码 localhost:8000`)
            hasProblematicHardcode = true
          } else if (!isComment && !isBackendUrl) {
            log('red', `❌ ${file}:${index + 1} 包含硬编码的 localhost:8000`)
            hasProblematicHardcode = true
          }
        }
      })

      if (hasProblematicHardcode) {
        allGood = false
      }
    } else {
      log('yellow', `⚠️  ${file} 不存在`)
      if (file !== '.env.example') {
        allGood = false
      }
    }
  })

  return allGood
}

function checkSourceFiles() {
  log('blue', '\n🔍 检查源代码文件...')

  let allGood = true

  function checkFile(filePath, allowedPatterns = []) {
    if (!fs.existsSync(filePath)) return

    const content = fs.readFileSync(filePath, 'utf8')
    const relativePath = path.relative(__dirname, filePath)

    // 检查硬编码的API URL（排除允许的模式）
    const problematicPatterns = [
      'localhost:8000',
      'http://localhost:8000',
      'https://localhost:8000'
    ]

    problematicPatterns.forEach(pattern => {
      if (content.includes(pattern)) {
        // 检查是否在允许的上下文中
        const isAllowed = allowedPatterns.some(allowed => {
          const regex = new RegExp(allowed, 'i')
          return regex.test(content)
        })

        if (!isAllowed) {
          log('red', `❌ ${relativePath} 包含硬编码: ${pattern}`)
          allGood = false
        }
      }
    })
  }
  
  // 检查关键文件
  const filesToCheck = [
    { file: 'src/utils/request.js', allowedPatterns: [] },
    { file: 'src/api/auth.js', allowedPatterns: [] },
    {
      file: 'vite.config.js',
      allowedPatterns: [
        'VITE_BACKEND_URL.*localhost:8000',  // 允许在环境变量默认值中
        'process\\.env\\.VITE_BACKEND_URL.*localhost:8000'
      ]
    }
  ]

  filesToCheck.forEach(({ file, allowedPatterns }) => {
    checkFile(path.join(__dirname, file), allowedPatterns)
  })
  
  if (allGood) {
    log('green', '✅ 未发现硬编码的API URL')
  }
  
  return allGood
}

function checkViteConfig() {
  log('blue', '\n🔍 检查Vite配置...')
  
  const viteConfigPath = path.join(__dirname, 'vite.config.js')
  if (!fs.existsSync(viteConfigPath)) {
    log('red', '❌ vite.config.js 不存在')
    return false
  }
  
  const content = fs.readFileSync(viteConfigPath, 'utf8')
  
  // 检查代理配置
  if (content.includes("proxy:") && content.includes("'/api'")) {
    log('green', '✅ Vite代理配置正确')
  } else {
    log('red', '❌ Vite代理配置缺失或不正确')
    return false
  }
  
  // 检查是否移除了硬编码的define
  if (content.includes("'import.meta.env.VITE_API_URL': JSON.stringify('http://localhost:8000')")) {
    log('red', '❌ vite.config.js 仍包含硬编码的API URL')
    return false
  } else {
    log('green', '✅ vite.config.js 已移除硬编码的API URL')
  }
  
  return true
}

function showRecommendations() {
  log('blue', '\n💡 环境配置建议:')
  
  console.log(`
${colors.bold}开发环境${colors.reset} (.env.development):
  VITE_API_URL=
  # 留空使用Vite代理到localhost:8000

${colors.bold}生产环境${colors.reset} (.env.production):
  VITE_API_URL=https://api.tunshuedu.com
  # 使用实际的生产API域名

${colors.bold}测试环境${colors.reset} (.env.staging):
  VITE_API_URL=https://staging-api.tunshuedu.com
  # 使用测试环境的API域名
`)
}

function main() {
  log('bold', '🚀 API配置验证开始...')
  
  const envCheck = checkEnvironmentFiles()
  const sourceCheck = checkSourceFiles()
  const viteCheck = checkViteConfig()
  
  const allPassed = envCheck && sourceCheck && viteCheck
  
  if (allPassed) {
    log('green', '\n🎉 所有检查通过！API配置正确。')
    log('green', '✅ 前端将根据环境自动选择正确的API地址')
    log('green', '✅ 开发环境使用代理，生产环境使用配置的域名')
  } else {
    log('red', '\n❌ 发现配置问题，请根据上述提示进行修复。')
  }
  
  showRecommendations()
  
  process.exit(allPassed ? 0 : 1)
}

main()
