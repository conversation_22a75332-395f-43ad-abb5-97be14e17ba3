<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #4F46E5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3730A3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信登录功能测试</h1>
        
        <div class="test-section">
            <h3>1. 测试微信二维码配置获取</h3>
            <button onclick="testQRConfig()">获取二维码配置</button>
            <div id="qr-config-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试微信回调处理</h3>
            <p>模拟微信扫码后的回调处理：</p>
            <button onclick="testCallback()">模拟回调处理</button>
            <div id="callback-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试跨域postMessage通信</h3>
            <button onclick="testPostMessage()">测试postMessage</button>
            <div id="postmessage-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试登录页面回调处理</h3>
            <p>测试登录页面直接处理微信回调：</p>
            <button onclick="openLoginWithCallback()">打开带回调参数的登录页面</button>
        </div>
    </div>

    <script>
        // 测试二维码配置获取
        async function testQRConfig() {
            const resultDiv = document.getElementById('qr-config-result');
            resultDiv.textContent = '正在获取配置...';
            
            try {
                const response = await fetch('http://localhost:8000/api/auth/wechat/qr-config');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 配置获取成功:\n' + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 配置获取失败:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 网络错误:\n' + error.message;
            }
        }

        // 测试回调处理
        async function testCallback() {
            const resultDiv = document.getElementById('callback-result');
            resultDiv.textContent = '正在测试回调处理...';
            
            // 模拟微信回调参数
            const testCode = 'test_code_' + Date.now();
            const testState = 'test_state_' + Date.now();
            
            try {
                const response = await fetch('http://localhost:8000/api/auth/wechat/callback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        code: testCode,
                        state: testState
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 回调处理成功:\n' + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 回调处理失败:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 网络错误:\n' + error.message;
            }
        }

        // 测试postMessage通信
        function testPostMessage() {
            const resultDiv = document.getElementById('postmessage-result');
            resultDiv.textContent = '正在测试postMessage通信...';
            
            // 监听postMessage
            const messageHandler = (event) => {
                if (event.data && event.data.type === 'test_response') {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ postMessage通信成功:\n' + JSON.stringify(event.data, null, 2);
                    window.removeEventListener('message', messageHandler);
                }
            };
            
            window.addEventListener('message', messageHandler);
            
            // 发送测试消息给自己
            setTimeout(() => {
                window.postMessage({
                    type: 'test_response',
                    message: 'postMessage通信正常',
                    timestamp: new Date().toISOString()
                }, '*');
            }, 100);
            
            // 5秒后清理监听器
            setTimeout(() => {
                window.removeEventListener('message', messageHandler);
                if (resultDiv.textContent.includes('正在测试')) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ postMessage通信超时';
                }
            }, 5000);
        }

        // 打开带回调参数的登录页面
        function openLoginWithCallback() {
            const testCode = 'test_code_' + Date.now();
            const testState = 'test_state_' + Date.now();
            const loginUrl = `http://localhost:3000/login?code=${testCode}&state=${testState}`;

            window.open(loginUrl, '_blank');
        }
    </script>
</body>
</html>
