# 环境变量配置示例
# 复制此文件为 .env.local 并根据实际情况修改

# ===========================================
# API配置
# ===========================================

# API基础URL
# 开发环境：留空使用Vite代理 (推荐)
# 生产环境：填写完整的API域名，如 https://api.tunshuedu.com
VITE_API_URL=

# ===========================================
# 应用配置
# ===========================================

# 应用标题
VITE_APP_TITLE=囤鼠教育

# 应用版本
VITE_APP_VERSION=0.4.1

# ===========================================
# 开发配置
# ===========================================

# 是否启用开发模式
VITE_DEV_MODE=true

# 是否启用调试模式
VITE_DEBUG=true

# ===========================================
# 环境说明
# ===========================================

# 开发环境 (development):
# - VITE_API_URL 留空，使用 Vite 代理到后端服务
# - 启用调试和开发模式

# 生产环境 (production):
# - VITE_API_URL 设置为实际的API域名
# - 关闭调试和开发模式

# 测试环境 (staging):
# - VITE_API_URL 设置为测试环境的API域名
# - 可选择性启用调试模式

# ===========================================
# 开发者配置 (可选)
# ===========================================

# 后端服务地址 (仅用于Vite代理配置)
# 默认: http://localhost:8000
# VITE_BACKEND_URL=http://localhost:8000
