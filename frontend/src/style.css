/* 移除默认样式，使用 Tailwind CSS */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 新增：打印模式样式 */
/* 当 body 添加 .is-printing 类时，以下样式生效 */
body.is-printing {
  /* 隐藏所有直接子元素 */
  > * {
    display: none !important;
  }

  /* 仅显示 .printable-content 及其所有父级 */
  .printable-content,
  .printable-content-parent,
  .printable-content-grandparent {
    display: block !important;
    height: auto !important;
    overflow: visible !important;
  }
}
