/**
 * 侧边栏权限管理组合式函数
 * 
 * 处理侧边栏的权限检查和视图模式切换逻辑
 */

import { computed } from 'vue'
import { useSidebarStore } from '@/stores/sidebar'

/**
 * 侧边栏权限管理组合式函数
 * @returns {Object} 侧边栏权限相关的响应式数据和方法
 */
export function useSidebarPermissions() {
  const sidebarStore = useSidebarStore()

  // 现在这些都是 store 中的计算属性，直接使用
  const canSwitchViewMode = computed(() => sidebarStore.canSwitchViewMode)
  const shouldShowTestFeatures = computed(() => sidebarStore.shouldShowTestFeatures)

  // 获取当前视图模式的显示文本
  const viewModeText = computed(() => {
    return sidebarStore.isDevMode ? '开发版' : '上线版'
  })

  // 获取当前视图模式的图标
  const viewModeIcon = computed(() => {
    return sidebarStore.isDevMode ? 'code' : 'public'
  })

  // 获取切换按钮的样式类
  const viewModeButtonClass = computed(() => {
    return sidebarStore.isDevMode ? 
      'bg-blue-100 text-blue-700 hover:bg-blue-200' : 
      'bg-green-100 text-green-700 hover:bg-green-200'
  })

  // 获取切换按钮的提示文本
  const viewModeTooltip = computed(() => {
    return sidebarStore.isDevMode ? '切换到上线版视角' : '切换到开发版视角'
  })

  return {
    // 侧边栏store的响应式数据
    isDevMode: computed(() => sidebarStore.isDevMode),
    isProductionMode: computed(() => sidebarStore.isProductionMode),

    // 权限检查
    canSwitchViewMode,
    shouldShowTestFeatures,

    // 视图模式相关
    viewModeText,
    viewModeIcon,
    viewModeButtonClass,
    viewModeTooltip,

    // 方法
    toggleViewMode: sidebarStore.toggleViewMode,
    setViewMode: sidebarStore.setViewMode
  }
}
