/**
 * 权限管理组合式函数
 * 
 * 提供权限检查的响应式接口，方便在组件中使用
 */

import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { PERMISSIONS, ROLES } from '@/utils/permissions'

/**
 * 权限管理组合式函数
 * @returns {Object} 权限检查相关的响应式数据和方法
 */
export function usePermissions() {
  const userStore = useUserStore()

  // 基础权限检查
  const hasPermission = (permission) => {
    return userStore.hasPermission(permission)
  }

  // 角色检查
  const isAdmin = computed(() => userStore.isAdmin())
  const isDev = computed(() => userStore.isDev())
  const isUser = computed(() => userStore.isNormalUser())

  // 测试功能权限
  const hasTestAccess = computed(() => userStore.hasTestAccess())

  // 具体功能权限
  const canAccessAdminPanel = computed(() => hasPermission(PERMISSIONS.ADMIN_PANEL))
  const canManageUsers = computed(() => hasPermission(PERMISSIONS.USER_MANAGEMENT))
  const canUseDevTools = computed(() => hasPermission(PERMISSIONS.DEV_TOOLS))
  const canManageClients = computed(() => hasPermission(PERMISSIONS.CLIENT_MANAGEMENT))
  const canUseWritingTools = computed(() => hasPermission(PERMISSIONS.WRITING_TOOLS))
  const canUseAITools = computed(() => hasPermission(PERMISSIONS.AI_TOOLS))
  const canUseCRM = computed(() => hasPermission(PERMISSIONS.CRM_SYSTEM))
  const canUseSchoolMatching = computed(() => hasPermission(PERMISSIONS.SCHOOL_MATCHING))
  const canExportDocuments = computed(() => hasPermission(PERMISSIONS.EXPORT_DOCUMENTS))

  // 用户角色信息
  const userRole = computed(() => userStore.getUserRole())
  const userPermissions = computed(() => userStore.getUserPermissions())

  // 权限组合检查
  const canAccessTestFeatures = computed(() => {
    return isAdmin.value || isDev.value
  })

  const canAccessAdvancedFeatures = computed(() => {
    return hasPermission(PERMISSIONS.ADVANCED_FEATURES)
  })

  // 权限检查工具方法
  const checkPermission = (permission) => {
    return hasPermission(permission)
  }

  const checkMultiplePermissions = (permissions, requireAll = true) => {
    if (requireAll) {
      return permissions.every(permission => hasPermission(permission))
    } else {
      return permissions.some(permission => hasPermission(permission))
    }
  }

  const hasAnyRole = (roles) => {
    const currentRole = userRole.value
    return roles.includes(currentRole)
  }

  return {
    // 基础权限检查
    hasPermission: checkPermission,
    checkMultiplePermissions,
    hasAnyRole,

    // 角色检查
    isAdmin,
    isDev,
    isUser,
    userRole,
    userPermissions,

    // 功能权限
    hasTestAccess,
    canAccessTestFeatures,
    canAccessAdvancedFeatures,
    canAccessAdminPanel,
    canManageUsers,
    canUseDevTools,
    canManageClients,
    canUseWritingTools,
    canUseAITools,
    canUseCRM,
    canUseSchoolMatching,
    canExportDocuments,

    // 权限常量（方便在模板中使用）
    PERMISSIONS,
    ROLES
  }
}
