/**
 * 套餐分配管理组合式函数
 * 
 * 处理组织权益分配的业务逻辑
 */

import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useOrganizationPermissions } from './useOrganizationPermissions'
import {
  getAvailablePackages,
  allocatePackage,
  revokePackage,
  getOrganizationAllocations,
  getMyAllocatedPackages,
  validateAllocationPermission,
  formatPackageName,
  getPackageInfo,
  formatAllocationStatus
} from '@/api/organizationBenefits'

/**
 * 套餐分配管理组合式函数
 * @returns {Object} 套餐分配相关的响应式数据和方法
 */
export function usePackageAllocation() {
  // 安全地获取组织权限
  let organizationPermissions
  try {
    organizationPermissions = useOrganizationPermissions()
  } catch (error) {
    console.error('获取组织权限失败:', error)
    // 提供默认值
    organizationPermissions = {
      currentIdentity: ref(null),
      currentOrganization: ref(null),
      isOrganizationOwner: ref(false),
      isOrganizationIdentity: ref(false)
    }
  }

  const {
    currentIdentity,
    currentOrganization,
    isOrganizationOwner,
    isOrganizationIdentity
  } = organizationPermissions

  // 响应式数据
  const loading = ref(false)
  const availablePackages = ref([])
  const organizationAllocations = ref([])
  const myAllocations = ref([])

  // 计算属性：是否可以执行分配操作
  const canAllocatePackages = computed(() => {
    if (!currentOrganization.value) return false
    
    const validation = validateAllocationPermission(
      currentIdentity.value,
      currentOrganization.value.id
    )
    
    return validation.canAllocate
  })

  // 计算属性：权限验证消息
  const permissionMessage = computed(() => {
    if (!currentOrganization.value) return '请切换到组织身份'
    
    const validation = validateAllocationPermission(
      currentIdentity.value,
      currentOrganization.value.id
    )
    
    return validation.reason
  })

  // 获取可分配的套餐列表
  const fetchAvailablePackages = async (organizationId = null) => {
    const orgId = organizationId || currentOrganization.value?.id
    if (!orgId) {
      ElMessage.error('请先切换到组织身份')
      return
    }

    // 权限检查：只有组织管理员才能查看可分配套餐
    if (!isOrganizationOwner.value) {
      console.warn('权限不足：只有组织管理员可以查看可分配套餐')
      return []
    }

    try {
      loading.value = true
      const response = await getAvailablePackages(orgId)
      
      // 处理套餐数据，添加格式化信息
      availablePackages.value = response.packages.map(pkg => {
        const packageInfo = getPackageInfo(pkg.package_id)
        return {
          ...pkg,
          formatted_name: packageInfo.name,
          package_info: packageInfo,
          can_allocate: pkg.available_count > 0
        }
      })
      
      console.log('可分配套餐列表:', availablePackages.value)
      return availablePackages.value
      
    } catch (error) {
      console.error('获取可分配套餐失败:', error)
      ElMessage.error(error.response?.data?.detail || '获取可分配套餐失败')
      return []
    } finally {
      loading.value = false
    }
  }

  // 分配套餐给成员
  const handleAllocatePackage = async (allocationData, options = {}) => {
    const { showConfirm = true, refreshData = true, showMessage = true } = options
    
    // 权限验证
    if (!canAllocatePackages.value) {
      if (showMessage) ElMessage.error(permissionMessage.value)
      return false
    }

    try {
      loading.value = true
      
      // 确认分配操作
      if (showConfirm) {
        await ElMessageBox.confirm(
          `确定要将套餐"${getPackageInfo(allocationData.package_id).name}"分配给该成员吗？`,
          '确认分配',
          {
            confirmButtonText: '确定分配',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      }

      const response = await allocatePackage(allocationData)
      
      if (response.success) {
        if (showMessage) ElMessage.success(response.message || '套餐分配成功')
        
        // 根据参数决定是否刷新数据
        if (refreshData) {
          await Promise.all([
            fetchAvailablePackages(),
            fetchOrganizationAllocations()
          ])
        }
        
        return true
      } else {
        if (showMessage) ElMessage.error(response.message || '套餐分配失败')
        return false
      }
      
    } catch (error) {
      if (error === 'cancel') {
        return false // 用户取消操作
      }
      
      console.error('分配套餐失败:', error)
      if (showMessage) ElMessage.error(error.response?.data?.detail || '分配套餐失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 回收成员的套餐
  const handleRevokePackage = async (revocationData) => {
    // 权限验证
    if (!canAllocatePackages.value) {
      ElMessage.error(permissionMessage.value)
      return false
    }

    try {
      loading.value = true
      
      // 确认回收操作
      await ElMessageBox.confirm(
        `确定要回收该成员的套餐"${getPackageInfo(revocationData.package_id).name}"吗？回收后该成员将无法继续使用相关功能。`,
        '确认回收',
        {
          confirmButtonText: '确定回收',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const response = await revokePackage(revocationData)
      
      if (response.success) {
        ElMessage.success(response.message || '套餐回收成功')
        
        // 刷新相关数据
        await Promise.all([
          fetchAvailablePackages(),
          fetchOrganizationAllocations()
        ])
        
        return true
      } else {
        ElMessage.error(response.message || '套餐回收失败')
        return false
      }
      
    } catch (error) {
      if (error === 'cancel') {
        return false // 用户取消操作
      }
      
      console.error('回收套餐失败:', error)
      ElMessage.error(error.response?.data?.detail || '回收套餐失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取组织分配列表
  const fetchOrganizationAllocations = async (organizationId = null, statusFilter = null) => {
    const orgId = organizationId || currentOrganization.value?.id
    if (!orgId) {
      ElMessage.error('请先切换到组织身份')
      return
    }

    // 权限检查：只有组织管理员才能查看分配列表
    if (!isOrganizationOwner.value) {
      console.warn('权限不足：只有组织管理员可以查看分配列表')
      return []
    }

    try {
      loading.value = true
      const params = statusFilter ? { status_filter: statusFilter } : {}
      const response = await getOrganizationAllocations(orgId, params)
      
      // 处理分配数据，添加格式化信息
      organizationAllocations.value = response.allocations.map(allocation => {
        const packageInfo = getPackageInfo(allocation.package_id)
        return {
          ...allocation,
          formatted_package_name: packageInfo.name,
          package_info: packageInfo,
          formatted_status: formatAllocationStatus(allocation.status),
          allocated_user_name: allocation.allocated_user?.username || '未知用户',
          allocated_user_email: allocation.allocated_user?.email || ''
        }
      })
      
      console.log('组织分配列表:', organizationAllocations.value)
      return organizationAllocations.value
      
    } catch (error) {
      console.error('获取组织分配列表失败:', error)
      ElMessage.error(error.response?.data?.detail || '获取分配列表失败')
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取我的分配列表
  const fetchMyAllocations = async (organizationId = null) => {
    const orgId = organizationId || currentOrganization.value?.id
    if (!orgId) {
      ElMessage.error('请先切换到组织身份')
      return
    }

    try {
      loading.value = true
      const response = await getMyAllocatedPackages(orgId)
      
      // 处理分配数据，添加格式化信息
      myAllocations.value = response.allocations.map(allocation => ({
        ...allocation,
        formatted_package_name: formatPackageName(allocation.package_id),
        formatted_status: formatAllocationStatus(allocation.status)
      }))
      
      console.log('我的分配列表:', myAllocations.value)
      return myAllocations.value
      
    } catch (error) {
      console.error('获取我的分配列表失败:', error)
      ElMessage.error(error.response?.data?.detail || '获取我的分配列表失败')
      return []
    } finally {
      loading.value = false
    }
  }

  // 刷新所有数据
  const refreshAllData = async () => {
    if (!currentOrganization.value) return
    
    try {
      await Promise.all([
        fetchAvailablePackages(),
        fetchOrganizationAllocations(),
        fetchMyAllocations()
      ])
      
      // ElMessage.success('数据已刷新')
    } catch (error) {
      console.error('刷新数据失败:', error)
      ElMessage.error('刷新数据失败')
    }
  }

  return {
    // 响应式数据
    loading,
    availablePackages,
    organizationAllocations,
    myAllocations,
    
    // 计算属性
    canAllocatePackages,
    permissionMessage,
    
    // 方法
    fetchAvailablePackages,
    handleAllocatePackage,
    handleRevokePackage,
    fetchOrganizationAllocations,
    fetchMyAllocations,
    refreshAllData,
    
    // 工具方法
    formatPackageName,
    formatAllocationStatus
  }
}
