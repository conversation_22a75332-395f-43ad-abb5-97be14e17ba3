/**
 * 组织权限管理组合式函数
 * 
 * 处理组织身份检查和权限控制逻辑
 */

import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * 组织权限管理组合式函数
 * @returns {Object} 组织权限相关的响应式数据和方法
 */
export function useOrganizationPermissions() {
  const authStore = useAuthStore()

  // 当前身份信息
  const currentIdentity = computed(() => authStore.currentIdentity)

  // 检查是否为组织身份
  const isOrganizationIdentity = computed(() => {
    return currentIdentity.value?.identity_type === 'organization'
  })

  // 检查是否为个人身份
  const isPersonalIdentity = computed(() => {
    return currentIdentity.value?.identity_type === 'personal'
  })

  // 当前组织信息
  const currentOrganization = computed(() => {
    if (!isOrganizationIdentity.value) return null
    
    return {
      id: currentIdentity.value.organization_id,
      name: currentIdentity.value.organization_name,
      role: currentIdentity.value.organization_role
    }
  })

  // 检查是否为组织管理员
  const isOrganizationOwner = computed(() => {
    return currentOrganization.value?.role === 'owner'
  })

  // 检查是否为组织成员
  const isOrganizationMember = computed(() => {
    return currentOrganization.value?.role === 'member'
  })

  // 检查是否可以访问账号管理
  const canAccessAccountManagement = computed(() => {
    // 个人身份：可以访问（创建组织或管理自己创建的组织）
    if (isPersonalIdentity.value) return true
    
    // 组织身份：只有owner可以访问
    if (isOrganizationIdentity.value) return isOrganizationOwner.value
    
    return false
  })

  // 检查是否可以看到组织管理菜单
  const canSeeOrganizationManagement = computed(() => {
    // 个人身份：可以看到（可以创建组织）
    if (isPersonalIdentity.value) return true
    
    // 组织身份：都可以看到（owner管理，member查看权益）
    if (isOrganizationIdentity.value) return true
    
    return false
  })

  // 检查是否可以管理组织成员
  const canManageOrganizationMembers = computed(() => {
    return isOrganizationOwner.value
  })

  // 检查是否可以创建组织
  const canCreateOrganization = computed(() => {
    return isPersonalIdentity.value
  })

  // 获取组织角色标签
  const getOrganizationRoleLabel = () => {
    if (!currentOrganization.value) return ''
    
    const roleMap = {
      owner: '管理员',
      member: '成员'
    }
    
    return roleMap[currentOrganization.value.role] || currentOrganization.value.role
  }

  return {
    // 身份状态
    currentIdentity,
    currentOrganization,
    isOrganizationIdentity,
    isPersonalIdentity,
    
    // 角色检查
    isOrganizationOwner,
    isOrganizationMember,
    
    // 权限检查
    canAccessAccountManagement,
    canSeeOrganizationManagement,
    canManageOrganizationMembers,
    canCreateOrganization,
    
    // 工具方法
    getOrganizationRoleLabel
  }
} 