/**
 * 专业数据处理工具函数
 * 提供统一的数据格式化、验证和处理功能
 */

/**
 * 检查字段是否有有效值
 * @param {any} value - 要检查的值
 * @returns {boolean} - 是否有有效值
 */
export const shouldShowField = (value) => {
  if (!value) return false

  const stringValue = value.toString().trim()

  // 检查各种无效值
  return stringValue !== '' &&
         stringValue !== 'null' &&
         stringValue !== 'undefined' &&
         stringValue !== 'NaN' &&
         stringValue.toLowerCase() !== 'nan'
}

/**
 * 格式化字段值，处理空值和无效值
 * @param {any} value - 要格式化的值
 * @param {string} defaultText - 默认文本
 * @returns {string} - 格式化后的值
 */
export const formatFieldValue = (value, defaultText = '暂无信息') => {
  if (!value || value === 'null' || value === 'undefined') return defaultText

  const stringValue = value.toString().trim()

  // 检查是否为NaN字符串
  if (stringValue === 'NaN' || stringValue.toLowerCase() === 'nan') {
    return defaultText
  }

  return stringValue
}

/**
 * 清理HTML内容，去除标签和特殊字符
 * @param {string} content - HTML内容
 * @returns {string} - 清理后的纯文本
 */
export const formatHtmlContent = (content) => {
  if (!content) return ''
  return content
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .trim()
}

/**
 * 格式化申请时间字符串
 * @param {string} timeStr - 申请时间字符串
 * @returns {string} - 格式化后的时间字符串
 */
export const formatApplicationTime = (timeStr) => {
  if (!timeStr) return '暂无信息'
  return timeStr.replace(/\|/g, '\n').trim()
}

/**
 * 获取元素的准确高度（包括margin）
 * @param {HTMLElement} element - DOM元素
 * @returns {number} - 元素的总高度
 */
export const getElementTotalHeight = (element) => {
  if (!element) return 0

  const rect = element.getBoundingClientRect()
  const computedStyle = window.getComputedStyle(element)
  const marginTop = parseFloat(computedStyle.marginTop) || 0
  const marginBottom = parseFloat(computedStyle.marginBottom) || 0

  return rect.height + marginTop + marginBottom
}

/**
 * 从排名字符串中提取QS排名数字
 * @param {string} rankingStr - 排名字符串
 * @returns {number|null} - 排名数字或null
 */
export const extractQSRank = (rankingStr) => {
  if (!rankingStr) return null
  const match = rankingStr.match(/\d+/)
  return match ? parseInt(match[0]) : null
}

/**
 * 格式化学费信息
 * @param {string} tuition - 学费信息
 * @returns {string} - 格式化后的学费
 */
export const formatTuition = (tuition) => {
  if (!tuition) return '暂无信息'
  
  // 处理常见的学费格式
  const cleaned = tuition.toString().trim()
  
  // 如果已经包含货币符号，直接返回
  if (cleaned.includes('$') || cleaned.includes('£') || cleaned.includes('€') || cleaned.includes('¥') || cleaned.includes('港币') || cleaned.includes('新币')) {
    return cleaned
  }
  
  // 如果只是数字，尝试添加适当的货币符号
  if (/^\d+$/.test(cleaned)) {
    return `${cleaned}（具体货币请查看官网）`
  }
  
  return cleaned
}

/**
 * 格式化项目时长
 * @param {string} duration - 项目时长
 * @returns {string} - 格式化后的时长
 */
export const formatDuration = (duration) => {
  if (!duration) return '暂无信息'
  
  const cleaned = duration.toString().trim()
  
  // 如果已经包含"年"或"月"，直接返回
  if (cleaned.includes('年') || cleaned.includes('月') || cleaned.includes('Year') || cleaned.includes('Month')) {
    return cleaned
  }
  
  // 如果是纯数字，假设是年
  if (/^\d+$/.test(cleaned)) {
    return `${cleaned}年`
  }
  
  return cleaned
}

/**
 * 统一的专业数据格式化函数
 * @param {Object} program - 原始专业数据
 * @returns {Object} - 格式化后的专业数据
 */
export const formatProgramData = (program) => {
  if (!program) return null
  
  return {
    // 基本信息
    program_name_cn: formatFieldValue(program.专业中文名 || program.program_name_cn),
    program_name_en: formatFieldValue(program.专业英文名 || program.program_name_en),
    school_name_cn: formatFieldValue(program.学校中文名 || program.school_name_cn),
    school_name_en: formatFieldValue(program.学校英文名 || program.school_name_en),
    
    // 分类信息
    program_category: formatFieldValue(program.专业大类 || program.program_category),
    program_direction: formatFieldValue(program.专业方向 || program.program_direction),
    faculty: formatFieldValue(program.所在学院 || program.faculty),
    
    // 地区和排名
    school_region: formatFieldValue(program.location || program.school_region),
    school_qs_rank: extractQSRank(program.ranking || program.school_qs_rank),
    
    // 学术信息
    degree: formatFieldValue(program.degree || '硕士'),
    program_tuition: formatTuition(program.tuitionRange || program.program_tuition),
    program_duration: formatDuration(program.项目时长 || program.program_duration),
    program_intake: formatFieldValue(program.入学时间 || program.program_intake),
    
    // 申请信息
    application_time: formatFieldValue(program.deadline || program.application_time),
    gpa_requirements: formatFieldValue(program.gpa_requirements),
    language_requirements: formatFieldValue(program.language_requirements),
    application_requirements: formatFieldValue(program.application_requirements),
    
    // 详细信息
    program_objectives: formatHtmlContent(program.培养目标 || program.program_objectives),
    courses: formatFieldValue(program.课程设置 || program.courses),
    program_website: formatFieldValue(program.项目官网 || program.program_website),
    
    // 保留原始数据的ID
    id: program.id || program.program_id,
    program_id: program.program_id || program.id
  }
}

/**
 * 创建防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} - 防抖后的函数
 */
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 计算动态高度的辅助函数
 * @param {HTMLElement} leftColumn - 左侧列元素
 * @param {HTMLElement} middleColumn - 中间列元素
 * @param {Object} options - 配置选项
 * @returns {Promise<number>} - 计算出的高度
 */
export const calculateDynamicHeight = (leftColumn, middleColumn, options = {}) => {
  return new Promise((resolve) => {
    const {
      minHeight = 450, // 提高最小高度，确保能显示6个课程项目
      maxHeightRatio = 0.6,
      gridWidth = 800,
      delay = 50
    } = options
    
    try {
      // 创建临时容器来测量左中列的自然高度
      const tempContainer = document.createElement('div')
      tempContainer.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: 100%;
        visibility: hidden;
        pointer-events: none;
      `
      document.body.appendChild(tempContainer)

      // 克隆左侧列和中间列到临时容器中
      const leftClone = leftColumn.cloneNode(true)
      const middleClone = middleColumn.cloneNode(true)
      
      // 创建临时网格布局
      const tempGrid = document.createElement('div')
      tempGrid.style.cssText = `
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        width: ${gridWidth}px;
      `
      
      tempGrid.appendChild(leftClone)
      tempGrid.appendChild(middleClone)
      tempContainer.appendChild(tempGrid)

      // 等待浏览器渲染
      setTimeout(() => {
        try {
          const leftHeight = getElementTotalHeight(leftClone)
          const middleHeight = getElementTotalHeight(middleClone)
          const maxHeight = Math.max(leftHeight, middleHeight)
          
          // 设置最小高度和最大高度限制
          const maxAllowedHeight = window.innerHeight * maxHeightRatio
          const finalHeight = Math.min(Math.max(maxHeight, minHeight), maxAllowedHeight)
          
          resolve(finalHeight)
        } catch (error) {
          console.warn('计算动态高度时出错:', error)
          resolve(minHeight)
        } finally {
          // 清理临时容器
          document.body.removeChild(tempContainer)
        }
      }, delay)
    } catch (error) {
      console.warn('创建临时容器时出错:', error)
      resolve(minHeight)
    }
  })
}

/**
 * 创建ResizeObserver来监听元素大小变化
 * @param {HTMLElement[]} elements - 要监听的元素数组
 * @param {Function} callback - 回调函数
 * @returns {ResizeObserver} - ResizeObserver实例
 */
export const createResizeObserver = (elements, callback) => {
  const observer = new ResizeObserver((entries) => {
    // 只有当观察到的元素真正改变大小时才触发回调
    let hasRealChange = false
    for (const entry of entries) {
      if (entry.contentRect.height > 0) {
        hasRealChange = true
        break
      }
    }
    
    if (hasRealChange) {
      callback(entries)
    }
  })

  elements.forEach(element => {
    if (element) {
      observer.observe(element)
    }
  })

  return observer
}

/**
 * 程序数据验证函数
 * @param {Object} program - 程序数据
 * @returns {Object} - 验证结果
 */
export const validateProgramData = (program) => {
  const errors = []
  const warnings = []
  
  // 必需字段检查
  if (!program.program_name_cn && !program.program_name_en) {
    errors.push('缺少专业名称')
  }
  
  if (!program.school_name_cn && !program.school_name_en) {
    errors.push('缺少学校名称')
  }
  
  // 建议字段检查
  if (!program.program_category) {
    warnings.push('缺少专业分类信息')
  }
  
  if (!program.school_region) {
    warnings.push('缺少学校地区信息')
  }
  
  if (!program.application_time) {
    warnings.push('缺少申请时间信息')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 根据专业数据生成摘要信息
 * @param {Object} program - 专业数据
 * @returns {string} - 摘要信息
 */
export const generateProgramSummary = (program) => {
  if (!program) return ''
  
  const parts = []
  
  if (program.school_name_cn) {
    parts.push(program.school_name_cn)
  }
  
  if (program.program_name_cn) {
    parts.push(program.program_name_cn)
  }
  
  if (program.program_category) {
    parts.push(`${program.program_category}类`)
  }
  
  if (program.degree) {
    parts.push(program.degree)
  }
  
  if (program.program_duration) {
    parts.push(`学制${program.program_duration}`)
  }
  
  return parts.join(' · ')
}

export default {
  shouldShowField,
  formatFieldValue,
  formatHtmlContent,
  formatApplicationTime,
  getElementTotalHeight,
  extractQSRank,
  formatTuition,
  formatDuration,
  formatProgramData,
  debounce,
  calculateDynamicHeight,
  createResizeObserver,
  validateProgramData,
  generateProgramSummary
} 