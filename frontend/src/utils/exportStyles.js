import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, UnderlineType } from 'docx';

/**
 * PDF 导出样式
 * 这些样式会在生成PDF前被注入到HTML内容中。
 * 您可以在这里自定义字体、字号、边距等。
 */
export const pdfStyles = `
  body {
    font-family: 'Times New Roman', Times, serif; /* 固定为 Times New Roman 字体 */
    font-size: 10pt; /* 固定为 10号 字体 */
    line-height: 1.15; /* 单倍行距 (1.15 更具可读性) */
    color: #333;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Helvetica', Arial, sans-serif;
    color: #000;
    margin: 0;
    padding: 0;
    page-break-after: avoid;
  }
  h1 { font-size: 16pt; font-weight: bold; margin-top: 1em; margin-bottom: 0.4em; }
  h2 { font-size: 13pt; font-weight: bold; margin-top: 0.8em; margin-bottom: 0.4em; }
  h3 { font-size: 11pt; font-weight: bold; margin-top: 0.6em; margin-bottom: 0.3em; }
  p { 
    margin: 0 0 0.4em 0; 
    text-align: justify;
  }
  ul, ol { 
    margin: 0.4em 0; 
    padding-left: 1.5em; 
  }
  li { 
    margin: 0.25em 0; 
  }
  strong { font-weight: bold; }
  em { font-style: italic; }
  u { text-decoration: underline; }
  blockquote { 
    border-left: 2px solid #ddd; 
    padding-left: 1em; 
    margin: 1em 0; 
    font-style: italic; 
    color: #555;
  }
`;

/**
 * 内部函数：将HTML节点转换为DOCX的Run元素
 */
function convertNodesToRuns(nodes, styles = {}) {
    let runs = [];
    for (const node of nodes) {
        if (node.nodeType === Node.TEXT_NODE) {
            if (node.textContent) {
                runs.push(new TextRun({ text: node.textContent, ...styles }));
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            const newStyles = { ...styles };
            const tag = node.tagName.toUpperCase();
            if (tag === 'STRONG' || tag === 'B') newStyles.bold = true;
            if (tag === 'EM' || tag === 'I') newStyles.italics = true;
            if (tag === 'U') newStyles.underline = { type: UnderlineType.SINGLE };

            runs = runs.concat(convertNodesToRuns(node.childNodes, newStyles));
        }
    }
    return runs;
}

/**
 * 内部函数：将HTML内容转换为DOCX的段落和元素数组
 */
function convertHtmlToElements(htmlContent) {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    
    const docxElements = [];

    for (const blockElement of tempDiv.childNodes) {
        if (blockElement.nodeType !== Node.ELEMENT_NODE) continue;

        const tag = blockElement.tagName.toUpperCase();
        const alignment = ALIGNMENT_MAP[blockElement.style.textAlign] || AlignmentType.LEFT;
        const paragraphOptions = { alignment };
        
        if (HEADING_MAP[tag]) {
            paragraphOptions.heading = HEADING_MAP[tag];
        }

        if (tag === 'UL' || tag === 'OL') {
             for (const listItem of blockElement.querySelectorAll('li')) {
                 const liAlignment = ALIGNMENT_MAP[listItem.style.textAlign] || alignment;
                 const runs = convertNodesToRuns(listItem.childNodes);
                 if(runs.length > 0) {
                    docxElements.push(new Paragraph({
                        children: runs,
                        bullet: { level: 0 },
                        alignment: liAlignment,
                    }));
                 }
            }
            continue;
        }

        if (tag === 'HR') {
            docxElements.push(new Paragraph({ thematicBreak: true }));
            continue;
        }
        
        const runs = convertNodesToRuns(blockElement.childNodes);

        if (runs.length > 0) {
            paragraphOptions.children = runs;
            docxElements.push(new Paragraph(paragraphOptions));
        } else if (blockElement.textContent.trim() === '' && docxElements.length > 0) {
             docxElements.push(new Paragraph({}));
        }
    }

    if (docxElements.length === 0) {
        return [new Paragraph({ children: [new TextRun(tempDiv.textContent || '')] })];
    }
  
    return docxElements;
}

const HEADING_MAP = {
  H1: HeadingLevel.HEADING_1,
  H2: HeadingLevel.HEADING_2,
  H3: HeadingLevel.HEADING_3,
  H4: HeadingLevel.HEADING_4,
  H5: HeadingLevel.HEADING_5,
  H6: HeadingLevel.HEADING_6,
};

const ALIGNMENT_MAP = {
  left: AlignmentType.LEFT,
  center: AlignmentType.CENTER,
  right: AlignmentType.RIGHT,
  justify: AlignmentType.JUSTIFIED,
};

/**
 * 导出函数：根据HTML内容创建并返回一个完整的、带样式的DOCX文档对象
 */
export function createDocxFromHtml(htmlContent) {
  const elements = convertHtmlToElements(htmlContent);

  const doc = new Document({
    styles: {
      default: {
        document: {
          run: {
            size: "20", // 10pt (docx单位为半磅)
            font: "Times New Roman",
            color: "000000",
          },
          paragraph: {
            spacing: {
              line: 240, // 单倍行距 (240缇 = 12磅)
            }
          }
        },
      },
      heading1: {
        run: {
          size: "32", // 16pt
          bold: true,
          font: "Times New Roman",
          color: "000000",
        },
        paragraph: {
          spacing: { after: 0 },
        },
      },
      heading2: {
        run: {
          size: "28", // 14pt
          bold: true,
          font: "Times New Roman",
          color: "000000",
        },
        paragraph: {
          spacing: { after: 0 },
        },
      },
      heading3: {
        run: {
          size: "22", // 11pt
          bold: true,
          font: "Times New Roman",
          color: "000000",
        },
        paragraph: {
          spacing: { after: 0 },
        },
      },
    },
    sections: [{
      properties: {},
      children: elements,
    }]
  });

  return doc;
}
