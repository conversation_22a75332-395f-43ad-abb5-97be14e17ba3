/**
 * 用户ID映射工具函数
 * 将真实的用户ID映射为显示ID（真实ID + 10000）
 */

/**
 * 将真实用户ID转换为显示ID
 * @param {number|string} realId - 真实的用户ID
 * @returns {string} 显示用的用户ID
 */
export function mapUserIdForDisplay(realId) {
  if (!realId) {
    return '未知'
  }
  
  // 确保转换为数字
  const numericId = parseInt(realId, 10)
  
  // 如果转换失败，返回原值
  if (isNaN(numericId)) {
    return String(realId)
  }
  
  // 加上10000后返回
  return String(numericId + 10000)
}

/**
 * 将显示ID转换回真实用户ID（如果需要的话）
 * @param {number|string} displayId - 显示的用户ID
 * @returns {number} 真实的用户ID
 */
export function mapDisplayIdToReal(displayId) {
  if (!displayId) {
    return null
  }
  
  // 确保转换为数字
  const numericId = parseInt(displayId, 10)
  
  // 如果转换失败，返回null
  if (isNaN(numericId)) {
    return null
  }
  
  // 减去10000后返回
  return numericId - 10000
}
