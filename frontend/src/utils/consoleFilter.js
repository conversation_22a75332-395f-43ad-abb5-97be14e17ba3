/**
 * 控制台管理工具
 * 用于统一管理控制台输出，减少不必要的调试信息
 */

class ConsoleManager {
  constructor() {
    this.originalConsoleError = null
    this.originalConsoleWarn = null
    this.originalConsoleLog = null
    this.originalConsoleInfo = null
    this.isActive = false
    this.filters = []
    this.debugMode = false
    this.allowedPrefixes = ['[系统]', '[错误]', '[警告]']
  }

  /**
   * 添加错误过滤规则
   * @param {Function|RegExp|String} filter - 过滤规则
   */
  addFilter(filter) {
    this.filters.push(filter)
  }

  /**
   * 移除错误过滤规则
   * @param {Function|RegExp|String} filter - 要移除的过滤规则
   */
  removeFilter(filter) {
    const index = this.filters.indexOf(filter)
    if (index > -1) {
      this.filters.splice(index, 1)
    }
  }

  /**
   * 检查消息是否应该被过滤
   * @param {String} message - 错误消息
   * @returns {Boolean} - 是否应该过滤
   */
  shouldFilter(message) {
    return this.filters.some(filter => {
      if (typeof filter === 'function') {
        return filter(message)
      } else if (filter instanceof RegExp) {
        return filter.test(message)
      } else if (typeof filter === 'string') {
        return message.includes(filter)
      }
      return false
    })
  }

  /**
   * 设置调试模式
   */
  setDebugMode(enabled) {
    this.debugMode = enabled
  }

  /**
   * 检查是否允许输出
   */
  shouldAllow(message) {
    // 在生产环境中，完全禁用console.log和console.info
    if (import.meta.env.PROD) {
      return false
    }

    // 在调试模式下，允许所有输出
    if (this.debugMode) {
      return true
    }

    // 检查是否有允许的前缀
    return this.allowedPrefixes.some(prefix => message.includes(prefix))
  }

  /**
   * 启用控制台管理器
   */
  enable() {
    if (this.isActive) {
      return
    }

    // 保存原始的console方法
    this.originalConsoleError = console.error
    this.originalConsoleWarn = console.warn
    this.originalConsoleLog = console.log
    this.originalConsoleInfo = console.info

    // 重写console.error
    console.error = (...args) => {
      try {
        // 安全地将参数转换为字符串
        const message = args.map(arg => {
          if (typeof arg === 'string') {
            return arg
          } else if (typeof arg === 'object' && arg !== null) {
            try {
              return JSON.stringify(arg)
            } catch {
              return '[Object]'
            }
          } else {
            return String(arg)
          }
        }).join(' ')

        if (!this.shouldFilter(message)) {
          this.originalConsoleError.apply(console, args)
        }
      } catch (error) {
        // 如果处理失败，直接调用原始方法
        this.originalConsoleError.apply(console, args)
      }
    }

    // 重写console.warn
    console.warn = (...args) => {
      // 生产环境禁用warn
      if (import.meta.env.PROD) {
        return
      }

      try {
        // 安全地将参数转换为字符串
        const message = args.map(arg => {
          if (typeof arg === 'string') {
            return arg
          } else if (typeof arg === 'object' && arg !== null) {
            try {
              return JSON.stringify(arg)
            } catch {
              return '[Object]'
            }
          } else {
            return String(arg)
          }
        }).join(' ')

        if (!this.shouldFilter(message)) {
          this.originalConsoleWarn.apply(console, args)
        }
      } catch (error) {
        // 如果处理失败，直接调用原始方法
        this.originalConsoleWarn.apply(console, args)
      }
    }

    // 重写console.log
    console.log = (...args) => {
      try {
        // 安全地将参数转换为字符串
        const message = args.map(arg => {
          if (typeof arg === 'string') {
            return arg
          } else if (typeof arg === 'object' && arg !== null) {
            try {
              return JSON.stringify(arg)
            } catch {
              return '[Object]'
            }
          } else {
            return String(arg)
          }
        }).join(' ')

        if (this.shouldAllow(message)) {
          this.originalConsoleLog.apply(console, args)
        }
      } catch (error) {
        // 如果处理失败，直接调用原始方法
        this.originalConsoleLog.apply(console, args)
      }
    }

    // 重写console.info
    console.info = (...args) => {
      const message = args.join(' ')
      if (this.shouldAllow(message)) {
        this.originalConsoleInfo.apply(console, args)
      }
    }

    this.isActive = true
    this.originalConsoleLog('[系统] 控制台管理器已启用')
  }

  /**
   * 禁用控制台管理器
   */
  disable() {
    if (!this.isActive) {
      return
    }

    // 恢复原始的console方法
    if (this.originalConsoleError) {
      console.error = this.originalConsoleError
      this.originalConsoleError = null
    }

    if (this.originalConsoleWarn) {
      console.warn = this.originalConsoleWarn
      this.originalConsoleWarn = null
    }

    if (this.originalConsoleLog) {
      console.log = this.originalConsoleLog
      this.originalConsoleLog = null
    }

    if (this.originalConsoleInfo) {
      console.info = this.originalConsoleInfo
      this.originalConsoleInfo = null
    }

    this.isActive = false
    console.log('[系统] 控制台管理器已禁用')
  }

  /**
   * 添加微信相关的错误过滤规则
   */
  addWeChatFilters() {
    this.addFilter('localhost.weixin.qq.com')
    this.addFilter('ERR_CONNECTION_RESET')
    this.addFilter(message => message.includes('端口') && message.includes('连接失败'))
    this.addFilter(message => message.includes('尝试下一个端口'))
    this.addFilter('connect_qrconnect_checkLogin_fail')
  }

  /**
   * 添加常见的开发环境噪音过滤规则
   */
  addCommonFilters() {
    // 微信相关错误
    this.addWeChatFilters()

    // 其他常见的开发环境错误
    this.addFilter('ResizeObserver loop limit exceeded')
    this.addFilter('Non-passive event listener')
    this.addFilter('Failed to read the \'responseText\' property from \'XMLHttpRequest\'')
    this.addFilter('InvalidStateError')
    this.addFilter('inspector.js')
  }
}

// 创建全局实例
const consoleManager = new ConsoleManager()

// 导出便捷方法
export const enableConsoleFilter = () => consoleManager.enable()
export const disableConsoleFilter = () => consoleManager.disable()
export const addConsoleFilter = (filter) => consoleManager.addFilter(filter)
export const removeConsoleFilter = (filter) => consoleManager.removeFilter(filter)
export const setDebugMode = (enabled) => consoleManager.setDebugMode(enabled)

// 预设过滤器
export const enableWeChatFilters = () => {
  consoleManager.addWeChatFilters()
  consoleManager.enable()
}

export const enableCommonFilters = () => {
  consoleManager.addCommonFilters()
  consoleManager.enable()
}

// 静默模式 - 只显示重要信息
export const enableSilentMode = () => {
  consoleManager.addCommonFilters()
  consoleManager.setDebugMode(false)
  consoleManager.enable()
}

// 调试模式 - 显示所有信息
export const enableDebugMode = () => {
  consoleManager.setDebugMode(true)
  consoleManager.enable()
}

export default consoleManager
