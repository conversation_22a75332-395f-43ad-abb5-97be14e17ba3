/**
 * format.js - 数据格式化工具
 *
 * 本模块提供各种数据格式化函数，用于在UI中展示数据，包括：
 * 1. 日期和时间格式化
 * 2. 数字和金额格式化
 * 3. 文件大小格式化
 * 4. 个人信息格式化（手机号、身份证等）
 */

/**
 * 格式化日期
 *
 * 将日期对象或日期字符串格式化为指定格式的字符串
 *
 * @param {string|number|Date} date - 日期对象、时间戳或日期字符串
 * @param {string} format - 格式模板，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 *
 * @example
 * // 返回 "2023-05-15"
 * formatDate(new Date(2023, 4, 15))
 *
 * // 返回 "2023年05月15日"
 * formatDate(new Date(2023, 4, 15), 'YYYY年MM月DD日')
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''

  // 确保date是Date对象
  const d = new Date(date)

  // 检查日期是否有效
  if (isNaN(d.getTime())) {
    console.warn('无效的日期:', date)
    return ''
  }

  // 提取日期各部分
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  const weekday = ['日', '一', '二', '三', '四', '五', '六'][d.getDay()]

  // 替换格式模板中的占位符
  return format
    .replace('YYYY', year)
    .replace('YY', String(year).slice(2))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
    .replace('WW', weekday)
}

/**
 * 格式化文件大小
 *
 * 将字节数转换为人类可读的文件大小格式
 *
 * @param {number} bytes - 文件大小（字节数）
 * @param {number} [decimals=2] - 小数位数
 * @returns {string} 格式化后的文件大小
 *
 * @example
 * // 返回 "1.50 MB"
 * formatFileSize(1572864)
 *
 * // 返回 "1.5 MB"
 * formatFileSize(1572864, 1)
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0 || bytes === null || bytes === undefined) return '0 B'
  if (isNaN(parseFloat(bytes))) return '无效大小'

  // 确保bytes是数字
  bytes = Number(bytes)

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  // 计算合适的单位
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  // 如果超出了我们定义的单位范围，使用最大单位
  if (i >= sizes.length) {
    return `${(bytes / Math.pow(k, sizes.length - 1)).toFixed(dm)} ${sizes[sizes.length - 1]}`
  }

  return `${(bytes / Math.pow(k, i)).toFixed(dm)} ${sizes[i]}`
}

/**
 * 格式化金额
 *
 * 将数字格式化为货币格式，包括货币符号和千位分隔符
 *
 * @param {number} amount - 金额
 * @param {string} [currency='¥'] - 货币符号
 * @param {number} [decimals=2] - 小数位数
 * @param {boolean} [space=false] - 货币符号和数字之间是否添加空格
 * @returns {string} 格式化后的金额
 *
 * @example
 * // 返回 "¥1,234.56"
 * formatAmount(1234.56)
 *
 * // 返回 "$ 1,234.56"
 * formatAmount(1234.56, '$', 2, true)
 *
 * // 返回 "€1,234.6"
 * formatAmount(1234.56, '€', 1)
 */
export function formatAmount(amount, currency = '¥', decimals = 2, space = false) {
  // 检查输入是否有效
  if (amount === null || amount === undefined || isNaN(Number(amount))) {
    return ''
  }

  // 确保amount是数字
  amount = Number(amount)

  // 格式化数字部分
  const formattedNumber = amount.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // 添加货币符号
  return `${currency}${space ? ' ' : ''}${formattedNumber}`
}

/**
 * 格式化手机号
 *
 * 将手机号格式化为更易读的格式，支持不同的分隔样式
 *
 * @param {string} phone - 手机号
 * @param {string} [separator='-'] - 分隔符
 * @param {boolean} [mask=false] - 是否隐藏中间部分
 * @returns {string} 格式化后的手机号
 *
 * @example
 * // 返回 "138-1234-5678"
 * formatPhone('13812345678')
 *
 * // 返回 "138 1234 5678"
 * formatPhone('13812345678', ' ')
 *
 * // 返回 "138****5678"
 * formatPhone('13812345678', '', true)
 */
export function formatPhone(phone, separator = '-', mask = false) {
  if (!phone) return ''

  // 移除所有非数字字符
  const cleaned = phone.replace(/\D/g, '')

  // 检查是否是有效的手机号（中国手机号为11位）
  if (cleaned.length !== 11) {
    // 如果不是标准长度，尝试通用格式化
    if (cleaned.length >= 7) {
      const part1 = cleaned.slice(0, 3)
      const part2 = cleaned.slice(3, 7)
      const part3 = cleaned.slice(7)
      return mask
        ? `${part1}****${part3}`
        : `${part1}${separator}${part2}${separator}${part3}`
    }
    return cleaned
  }

  // 标准中国手机号格式化
  const part1 = cleaned.slice(0, 3)
  const part2 = cleaned.slice(3, 7)
  const part3 = cleaned.slice(7, 11)

  return mask
    ? `${part1}****${part3}`
    : `${part1}${separator}${part2}${separator}${part3}`
}

/**
 * 格式化身份证号
 *
 * 将身份证号格式化为更易读的格式，支持隐私保护
 *
 * @param {string} idCard - 身份证号
 * @param {string} [separator='-'] - 分隔符
 * @param {boolean} [mask=false] - 是否隐藏敏感部分
 * @returns {string} 格式化后的身份证号
 *
 * @example
 * // 返回 "110101-1990-0101-0123"
 * formatIdCard('110101199001010123')
 *
 * // 返回 "110101********0123"
 * formatIdCard('110101199001010123', '', true)
 */
export function formatIdCard(idCard, separator = '-', mask = false) {
  if (!idCard) return ''

  // 移除所有非数字和X字符
  const cleaned = idCard.replace(/[^\dXx]/g, '').toUpperCase()

  // 检查是否是有效的身份证号（中国身份证为15位或18位）
  if (cleaned.length !== 15 && cleaned.length !== 18) {
    return cleaned
  }

  if (cleaned.length === 18) {
    const part1 = cleaned.slice(0, 6)  // 地区码
    const part2 = cleaned.slice(6, 10) // 年份
    const part3 = cleaned.slice(10, 14) // 月日
    const part4 = cleaned.slice(14)    // 顺序码和校验码

    return mask
      ? `${part1}********${part4}`
      : `${part1}${separator}${part2}${separator}${part3}${separator}${part4}`
  } else {
    // 15位身份证
    const part1 = cleaned.slice(0, 6)  // 地区码
    const part2 = cleaned.slice(6, 10) // 年月
    const part3 = cleaned.slice(10)    // 日和顺序码

    return mask
      ? `${part1}*****${part3}`
      : `${part1}${separator}${part2}${separator}${part3}`
  }
}

/**
 * 格式化银行卡号
 *
 * 将银行卡号格式化为每4位一组，使用空格分隔
 *
 * @param {string} cardNo - 银行卡号
 * @param {boolean} [mask=false] - 是否隐藏中间部分
 * @returns {string} 格式化后的银行卡号
 *
 * @example
 * // 返回 "6222 0000 1111 2222"
 * formatBankCard('****************')
 *
 * // 返回 "6222 **** **** 2222"
 * formatBankCard('****************', true)
 */
export function formatBankCard(cardNo, mask = false) {
  if (!cardNo) return ''

  // 移除所有非数字字符
  const cleaned = cardNo.replace(/\D/g, '')

  if (mask) {
    // 只显示前4位和后4位，中间用星号代替
    if (cleaned.length <= 8) {
      return cleaned
    }

    const firstFour = cleaned.slice(0, 4)
    const lastFour = cleaned.slice(-4)
    const middleLength = Math.floor((cleaned.length - 8) / 4)

    let masked = firstFour
    for (let i = 0; i < middleLength; i++) {
      masked += ' ****'
    }
    masked += ' ' + lastFour

    return masked
  }

  // 每4位添加一个空格
  return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ')
}

/**
 * 格式化姓名（隐私保护）
 *
 * 将姓名格式化为保护隐私的格式，只显示姓和最后一个字
 *
 * @param {string} name - 姓名
 * @returns {string} 格式化后的姓名
 *
 * @example
 * // 返回 "张*明"
 * formatName('张小明')
 *
 * // 返回 "欧阳*"
 * formatName('欧阳锋')
 */
export function formatName(name) {
  if (!name) return ''

  if (name.length <= 1) {
    return name
  } else if (name.length === 2) {
    return name.charAt(0) + '*'
  } else {
    // 处理复姓情况
    const commonSurnames = ['欧阳', '太史', '端木', '上官', '司马', '东方', '独孤', '南宫', '万俟', '闻人', '夏侯', '诸葛', '尉迟', '公羊', '赫连', '澹台', '皇甫', '宗政', '濮阳', '公冶', '太叔', '申屠', '公孙', '慕容', '仲孙', '钟离', '长孙', '宇文', '司徒', '鲜于', '司空', '闾丘', '子车', '亓官', '司寇', '巫马', '公西', '颛孙', '壤驷', '公良', '漆雕', '乐正', '宰父', '谷梁', '拓跋', '夹谷', '轩辕', '令狐', '段干', '百里', '呼延', '东郭', '南门', '羊舌', '微生', '公户', '公玉', '公仪', '梁丘', '公仲', '公上', '公门', '公山', '公坚', '左丘', '公伯', '西门', '公祖', '第五', '公乘', '贯丘', '公皙', '南荣', '东里', '东宫', '仲长', '子书', '子桑', '即墨', '达奚', '褚师']

    for (const surname of commonSurnames) {
      if (name.startsWith(surname)) {
        return surname + '*'.repeat(name.length - surname.length)
      }
    }

    // 默认处理（假设单姓）
    return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
  }
}

/**
 * 格式化UTC时间为北京时间
 *
 * 将UTC时间转换为UTC+8（北京时间）并格式化显示
 *
 * @param {string|number|Date} utcDateTime - UTC时间
 * @param {string} format - 格式模板，默认 'YYYY-MM-DD HH:mm'
 * @returns {string} 格式化后的北京时间字符串
 *
 * @example
 * // 返回 "2023-05-15 22:30" (UTC时间14:30转换为北京时间22:30)
 * formatUTCToBeijing('2023-05-15T14:30:00Z')
 */
export function formatUTCToBeijing(utcDateTime, format = 'YYYY-MM-DD HH:mm') {
  if (!utcDateTime) return ''

  try {
    // 如果时间字符串没有时区标识符，假设它是UTC时间，添加'Z'
    let utcTimeString = utcDateTime;
    if (typeof utcDateTime === 'string' && !utcDateTime.endsWith('Z') && !utcDateTime.includes('+') && !utcDateTime.includes('-', 10)) {
      utcTimeString = utcDateTime + 'Z';
    }

    // 创建UTC时间对象
    const utcDate = new Date(utcTimeString);

    // 使用JavaScript内置的时区转换
    if (format === 'YYYY-MM-DD HH:mm') {
      return utcDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Shanghai'
      });
    }

    // 对于其他格式，手动转换为北京时间后使用formatDate
    const beijingTime = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);
    return formatDate(beijingTime, format);
  } catch (error) {
    console.warn('时区转换失败:', utcDateTime, error)
    return ''
  }
}