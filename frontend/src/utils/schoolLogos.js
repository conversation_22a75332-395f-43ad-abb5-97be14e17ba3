/**
 * 学校Logo获取工具
 * 用于根据学校名称获取对应的logo图像
 */

// 学校logo映射表（可以根据实际需要扩展）
const schoolLogoMap = {
  // 香港地区
  '香港大学': 'https://www.hku.hk/f/page/5925/HKU%20Logo.png',
  '香港中文大学': 'https://www.cuhk.edu.hk/english/images/cuhk_logo_2x.png',
  '香港科技大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/3/3a/HKUST_Logo.svg/200px-HKUST_Logo.svg.png',
  '香港理工大学': 'https://www.polyu.edu.hk/web/en/home/<USER>/logos/polyu_logo.png',
  '香港城市大学': 'https://www.cityu.edu.hk/template/img/CityU_logo.png',
  '香港浸会大学': 'https://www.hkbu.edu.hk/eng/images/hkbu-logo-2x.png',
  
  // 英国地区
  '牛津大学': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/ff/Oxford-University-Circlet.svg/200px-Oxford-University-Circlet.svg.png',
  '剑桥大学': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/8a/University_of_Cambridge_coat_of_arms_official.svg/200px-University_of_Cambridge_coat_of_arms_official.svg.png',
  '伦敦大学学院': 'https://upload.wikimedia.org/wikipedia/en/thumb/1/1f/University_College_London_logo.svg/200px-University_College_London_logo.svg.png',
  '帝国理工学院': 'https://upload.wikimedia.org/wikipedia/en/thumb/9/9e/Imperial_College_London_crest.svg/200px-Imperial_College_London_crest.svg.png',
  '伦敦政治经济学院': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/8e/LSE_Logo.svg/200px-LSE_Logo.svg.png',
  
  // 美国地区
  '哈佛大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/2/29/Harvard_shield_wreath.svg/200px-Harvard_shield_wreath.svg.png',
  '斯坦福大学': 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b5/Seal_of_Leland_Stanford_Junior_University.svg/200px-Seal_of_Leland_Stanford_Junior_University.svg.png',
  '麻省理工学院': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0c/MIT_logo.svg/200px-MIT_logo.svg.png',
  '加州理工学院': 'https://upload.wikimedia.org/wikipedia/en/thumb/a/a4/Seal_of_the_California_Institute_of_Technology.svg/200px-Seal_of_the_California_Institute_of_Technology.svg.png',
  '耶鲁大学': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/07/Yale_University_Shield_1.svg/200px-Yale_University_Shield_1.svg.png',
  
  // 加拿大地区
  '多伦多大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/0/04/University_of_Toronto_coat_of_arms.svg/200px-University_of_Toronto_coat_of_arms.svg.png',
  '麦吉尔大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/2/29/McGill_University_CoA.svg/200px-McGill_University_CoA.svg.png',
  '英属哥伦比亚大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/5/54/University_of_British_Columbia_coat_of_arms.svg/200px-University_of_British_Columbia_coat_of_arms.svg.png',
  
  // 澳洲地区
  '墨尔本大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/8/8c/University_of_Melbourne_coat_of_arms.svg/200px-University_of_Melbourne_coat_of_arms.svg.png',
  '悉尼大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/0/0c/University_of_Sydney_coat_of_arms.svg/200px-University_of_Sydney_coat_of_arms.svg.png',
  '澳洲国立大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/5/54/Australian_National_University_Logo.svg/200px-Australian_National_University_Logo.svg.png',
  
  // 新加坡地区
  '新加坡国立大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/b/b9/NUS_coat_of_arms.svg/200px-NUS_coat_of_arms.svg.png',
  '南洋理工大学': 'https://upload.wikimedia.org/wikipedia/en/thumb/f/f7/Nanyang_Technological_University.svg/200px-Nanyang_Technological_University.svg.png'
}

/**
 * 根据学校名称获取logo URL
 * @param {string} schoolName - 学校中文名称
 * @returns {string} logo URL，如果没有找到则返回默认logo
 */
export function getSchoolLogo(schoolName) {
  if (!schoolName) return getDefaultLogo()
  
  // 直接匹配
  if (schoolLogoMap[schoolName]) {
    return schoolLogoMap[schoolName]
  }
  
  // 模糊匹配（去掉可能的后缀）
  const cleanName = schoolName.replace(/大学$|学院$|理工$/, '')
  for (const [key, logo] of Object.entries(schoolLogoMap)) {
    if (key.includes(cleanName) || cleanName.includes(key.replace(/大学$|学院$|理工$/, ''))) {
      return logo
    }
  }
  
  return getDefaultLogo()
}

/**
 * 获取默认logo
 * @returns {string} 默认logo URL
 */
export function getDefaultLogo() {
  // 返回一个默认的大学logo SVG
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzRGNDZFNSIvPgo8cGF0aCBkPSJNMTIgMTZIMjhWMThIMTJWMTZaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMjBIMjhWMjJIMTJWMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMjRIMjRWMjZIMTJWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTAgMTBIMzBWMTRIMTBWMTBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTggMjhIMjJWMzJIMThWMjhaIiBmaWxsPSJ3aWl0ZSIvPgo8L3N2Zz4K'
}

/**
 * 预加载学校logo图片
 * @param {string} logoUrl - logo URL
 * @returns {Promise} 预加载Promise
 */
export function preloadSchoolLogo(logoUrl) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve(logoUrl)
    img.onerror = () => resolve(getDefaultLogo()) // 如果加载失败，返回默认logo
    img.src = logoUrl
  })
} 