/**
 * 开发者工具
 * 提供调试模式切换和开发者功能
 */

import { setDebugMode, enableDebugMode, enableSilentMode } from '@/utils/consoleFilter'

class DevTools {
  constructor() {
    this.isDebugMode = false
    this.setupGlobalMethods()
  }

  /**
   * 设置全局开发者方法
   */
  setupGlobalMethods() {
    if (process.env.NODE_ENV === 'development') {
      // 在开发环境中添加全局方法
      window.devTools = {
        // 启用调试模式
        enableDebug: () => {
          this.isDebugMode = true
          enableDebugMode()
          console.log('[系统] 调试模式已启用 - 将显示所有控制台信息')
        },

        // 启用静默模式
        enableSilent: () => {
          this.isDebugMode = false
          enableSilentMode()
          console.log('[系统] 静默模式已启用 - 只显示重要信息')
        },

        // 获取当前模式
        getMode: () => {
          return this.isDebugMode ? 'debug' : 'silent'
        },

        // 显示帮助信息
        help: () => {
          console.log(`
[系统] 开发者工具帮助:
- devTools.enableDebug()  启用调试模式（显示所有控制台信息）
- devTools.enableSilent() 启用静默模式（只显示重要信息）
- devTools.getMode()      获取当前模式
- devTools.help()         显示此帮助信息

当前模式: ${this.getMode()}
          `)
        }
      }

      // 显示开发者工具提示
      console.log('[系统] 开发者工具已加载，输入 devTools.help() 查看可用命令')
    }
  }

  /**
   * 获取当前模式
   */
  getMode() {
    return this.isDebugMode ? 'debug' : 'silent'
  }
}

// 创建全局实例
const devTools = new DevTools()

export default devTools
