/**
 * 自动登录超时管理器
 * 
 * 功能：
 * 1. 监听用户活动事件
 * 2. 管理超时计时器
 * 3. 处理多标签页同步
 * 4. 自动退出登录
 */

import { ElMessage } from 'element-plus'

class AutoLogoutManager {
  constructor() {
    // 🔥 优化：增加超时时间到7天，提升用户体验
    this.TIMEOUT_DURATION = 7 * 24 * 60 * 60 * 1000 // 7天
    this.WARNING_TIME = 0 // 完全禁用警告，无感体验
    
    // 计时器
    this.timeoutTimer = null
    this.warningTimer = null
    
    // 用户活动事件列表
    this.activityEvents = [
      'mousedown',
      'mousemove', 
      'keypress',
      'scroll',
      'touchstart',
      'click',
      'keydown'
    ]
    
    // 绑定的事件处理器
    this.boundActivityHandler = this.handleUserActivity.bind(this)
    this.boundVisibilityHandler = this.handleVisibilityChange.bind(this)
    this.boundStorageHandler = this.handleStorageChange.bind(this)
    
    // 状态标志
    this.isActive = false
    
    // 回调函数
    this.onLogout = null
    this.onWarning = null
    
    // 存储键名
    this.STORAGE_KEY = 'auto_logout_sync'
    this.LAST_ACTIVITY_KEY = 'last_activity_time'
  }

  /**
   * 初始化自动登出管理器
   * @param {Function} logoutCallback - 登出回调函数
   * @param {Function} warningCallback - 警告回调函数
   */
  init(logoutCallback, warningCallback) {
    this.onLogout = logoutCallback
    this.onWarning = warningCallback
    
    // 启动监听
    this.startListening()
    
    // 重置计时器
    this.resetTimer()
    
    // 静默初始化
  }



  /**
   * 开始监听用户活动
   */
  startListening() {
    if (this.isActive) return
    
    this.isActive = true
    
    // 监听用户活动事件
    this.activityEvents.forEach(event => {
      document.addEventListener(event, this.boundActivityHandler, true)
    })
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.boundVisibilityHandler)
    
    // 监听localStorage变化（多标签页同步）
    window.addEventListener('storage', this.boundStorageHandler)
    

  }

  /**
   * 停止监听用户活动
   */
  stopListening() {
    if (!this.isActive) return
    
    this.isActive = false
    
    // 移除用户活动事件监听
    this.activityEvents.forEach(event => {
      document.removeEventListener(event, this.boundActivityHandler, true)
    })
    
    // 移除页面可见性监听
    document.removeEventListener('visibilitychange', this.boundVisibilityHandler)
    
    // 移除localStorage监听
    window.removeEventListener('storage', this.boundStorageHandler)
    
    // 清除计时器
    this.clearTimers()
    

  }

  /**
   * 处理用户活动
   */
  handleUserActivity() {
    if (!this.isActive) return
    
    // 🔥 修复：添加节流机制，避免过于频繁的更新
    const now = Date.now()
    const lastUpdate = localStorage.getItem(this.LAST_ACTIVITY_KEY)
    
    // 如果距离上次更新少于30秒，则跳过此次更新
    if (lastUpdate && (now - parseInt(lastUpdate)) < 30000) {
      return
    }
    
    // 重置计时器
    this.resetTimer()
    
    // 更新最后活动时间（用于多标签页同步）
    this.updateLastActivityTime()
  }

  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.hidden) {
      // 页面隐藏时暂停计时器
      this.pauseTimer()
    } else {
      // 页面显示时检查是否需要恢复计时器
      this.resumeTimer()
    }
  }

  /**
   * 处理localStorage变化（多标签页同步）
   */
  handleStorageChange(event) {
    if (event.key === this.STORAGE_KEY) {
      const data = JSON.parse(event.newValue || '{}')
      
      if (data.action === 'logout') {
        // 其他标签页触发了登出
        console.log('[自动登出] 检测到其他标签页登出，同步执行登出')
        this.executeLogout(false) // 不广播，避免循环
      } else if (data.action === 'activity') {
        // 其他标签页有用户活动
        this.resetTimer()
      }
    }
  }

  /**
   * 重置计时器
   */
  resetTimer() {
    this.clearTimers()
    this.warningShown = false

    // 只设置超时计时器，不设置警告计时器（因为WARNING_TIME为0）
    if (this.WARNING_TIME > 0) {
      // 设置警告计时器（仅当WARNING_TIME大于0时）
      this.warningTimer = setTimeout(() => {
        this.showWarning()
      }, this.TIMEOUT_DURATION - this.WARNING_TIME)
    }

    // 设置超时计时器
    this.timeoutTimer = setTimeout(() => {
      this.executeLogout()
    }, this.TIMEOUT_DURATION)
  }

  /**
   * 暂停计时器
   */
  pauseTimer() {
    this.clearTimers()
  }

  /**
   * 恢复计时器
   */
  resumeTimer() {
    const lastActivity = localStorage.getItem(this.LAST_ACTIVITY_KEY)
    if (lastActivity) {
      const lastActivityTime = parseInt(lastActivity)
      const timeSinceLastActivity = Date.now() - lastActivityTime
      
      // 添加合理性检查，防止时间计算错误
      if (timeSinceLastActivity < 0 || timeSinceLastActivity > this.TIMEOUT_DURATION * 2) {
        console.warn('[自动登出] 检测到时间异常，重新开始计时')
        this.updateLastActivity()
        this.resetTimer()
        return
      }
      
      // 🔥 修复：添加额外的安全检查，避免意外登出
      if (timeSinceLastActivity >= this.TIMEOUT_DURATION) {
        // 添加5分钟的缓冲时间，防止时钟偏差导致的误判
        const bufferTime = 5 * 60 * 1000 // 5分钟缓冲
        if (timeSinceLastActivity >= this.TIMEOUT_DURATION + bufferTime) {
          this.executeLogout()
        } else {
          // 在缓冲时间内，重新开始计时而不是立即登出
          console.warn('[自动登出] 在缓冲时间内，重新开始计时')
          this.updateLastActivity()
          this.resetTimer()
        }
      } else {
        // 重新设置计时器
        const remainingTime = this.TIMEOUT_DURATION - timeSinceLastActivity

        // 🔥 警告功能已完全禁用，提升用户体验

        this.timeoutTimer = setTimeout(() => {
          this.executeLogout()
        }, remainingTime)
      }
    } else {
      // 没有记录，重新开始计时
      this.resetTimer()
    }
  }

  /**
   * 清除所有计时器
   */
  clearTimers() {
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer)
      this.timeoutTimer = null
    }
    
    if (this.warningTimer) {
      clearTimeout(this.warningTimer)
      this.warningTimer = null
    }
  }

  /**
   * 显示超时警告（已完全禁用）
   */
  showWarning() {
    // 🔥 警告功能已完全禁用，提升用户体验
    return
  }

  /**
   * 执行登出
   * @param {boolean} broadcast - 是否广播给其他标签页
   */
  executeLogout(broadcast = true) {

    
    // 停止监听
    this.stopListening()
    
    // 广播登出事件给其他标签页
    if (broadcast) {
      this.broadcastLogout()
    }
    
    // 清理存储的活动时间
    localStorage.removeItem(this.LAST_ACTIVITY_KEY)
    
    // 静默登出，不显示提示消息（避免影响用户体验）
    
    // 调用登出回调
    if (this.onLogout) {
      this.onLogout()
    }
  }

  /**
   * 更新最后活动时间
   */
  updateLastActivityTime() {
    const now = Date.now()
    localStorage.setItem(this.LAST_ACTIVITY_KEY, now.toString())
    
    // 广播活动事件给其他标签页
    this.broadcastActivity()
  }

  /**
   * 广播登出事件
   */
  broadcastLogout() {
    const data = {
      action: 'logout',
      timestamp: Date.now()
    }
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data))
  }

  /**
   * 广播活动事件
   */
  broadcastActivity() {
    const data = {
      action: 'activity',
      timestamp: Date.now()
    }
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data))
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.stopListening()
    this.onLogout = null
    this.onWarning = null

  }
}

// 创建单例实例
const autoLogoutManager = new AutoLogoutManager()

export default autoLogoutManager
