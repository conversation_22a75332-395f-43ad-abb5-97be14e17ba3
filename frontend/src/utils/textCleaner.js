/**
 * 文本清理工具函数
 * 用于清理专业目标、培养目标等内容中的多余字段和HTML标签
 */

/**
 * 清理专业目标内容
 * @param {string} text - 原始文本
 * @returns {string} 清理后的文本
 */
export const cleanProgramObjectives = (text) => {
  if (!text) return ''
  
  return text
    .replace(/展开全部/g, '') // 移除"展开全部"文字
    .replace(/收起/g, '') // 移除"收起"文字
    .replace(/<p>/g, '') // 移除<p>标签
    .replace(/<\/p>/g, '') // 移除</p>标签
    .replace(/<br\s*\/?>/gi, '\n') // 将<br>标签替换为换行
    .replace(/<[^>]*>/g, '') // 移除所有其他HTML标签
    .replace(/&nbsp;/g, ' ') // 替换&nbsp;为空格
    .replace(/&amp;/g, '&') // 替换&amp;为&
    .replace(/&lt;/g, '<') // 替换&lt;为<
    .replace(/&gt;/g, '>') // 替换&gt;为>
    .replace(/&quot;/g, '"') // 替换&quot;为"
    .replace(/&apos;/g, "'") // 替换&apos;为'
    .replace(/\s+/g, ' ') // 合并多个空格为一个
    .replace(/\n\s*\n/g, '\n') // 合并多个换行为一个
    .trim()
}

/**
 * 清理HTML内容，保留基本格式
 * @param {string} html - 原始HTML内容
 * @returns {string} 清理后的文本
 */
export const cleanHtmlContent = (html) => {
  if (!html) return ''
  
  return html
    .replace(/展开全部/g, '') // 移除"展开全部"文字
    .replace(/收起/g, '') // 移除"收起"文字
    .replace(/<br\s*\/?>/gi, '\n') // 将<br>标签替换为换行
    .replace(/<\/p>/gi, '\n\n') // 将</p>标签替换为双换行
    .replace(/<p[^>]*>/gi, '') // 移除<p>标签
    .replace(/<[^>]*>/g, '') // 移除所有其他HTML标签
    .replace(/&nbsp;/g, ' ') // 替换&nbsp;为空格
    .replace(/&amp;/g, '&') // 替换&amp;为&
    .replace(/&lt;/g, '<') // 替换&lt;为<
    .replace(/&gt;/g, '>') // 替换&gt;为>
    .replace(/&quot;/g, '"') // 替换&quot;为"
    .replace(/&apos;/g, "'") // 替换&apos;为'
    .replace(/\s+/g, ' ') // 合并多个空格为一个
    .replace(/\n\s*\n\s*\n/g, '\n\n') // 合并多个换行为双换行
    .trim()
} 