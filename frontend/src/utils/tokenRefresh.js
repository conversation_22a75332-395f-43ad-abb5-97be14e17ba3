/**
 * 自动token刷新管理器
 * 
 * 功能：
 * 1. 监控token过期时间
 * 2. 自动在合适时机刷新token
 * 3. 处理多标签页同步
 * 4. 优雅处理刷新失败
 */

import { refreshToken } from '@/api/auth'
import { getToken, getRefreshToken, setToken } from './auth'

class TokenRefreshManager {
  constructor() {
    // 刷新时机：在token过期前2小时刷新（更早刷新）
    // 将提前刷新窗口从2小时调整为30分钟，减少过早刷新导致的并发问题
    this.REFRESH_BEFORE_EXPIRY = 30 * 60 * 1000 // 30分钟
    
    // 检查间隔：每3分钟检查一次（更频繁检查）
    // 将检查间隔从3分钟调整为5分钟，降低抖动
    this.CHECK_INTERVAL = 5 * 60 * 1000 // 5分钟
    
    // 计时器
    this.checkTimer = null
    this.refreshTimer = null
    
    // 状态标志
    this.isRefreshing = false
    this.isActive = false
    
    // 存储键名
    this.STORAGE_KEY = 'token_refresh_sync'
    this.LAST_REFRESH_KEY = 'last_token_refresh'
    
    // 绑定的事件处理器
    this.boundStorageHandler = this.handleStorageChange.bind(this)
    
    // 调试模式
    this.isDev = process.env.NODE_ENV !== 'production'
  }

  /**
   * 初始化token刷新管理器
   */
  init() {
    if (this.isActive) return
    
    this.isActive = true
    
    // 静默启动，不输出日志
    
    // 监听localStorage变化（多标签页同步）
    window.addEventListener('storage', this.boundStorageHandler)
    
    // 立即检查token状态
    this.checkTokenStatus()
    
    // 设置定期检查
    this.checkTimer = setInterval(() => {
      this.checkTokenStatus()
    }, this.CHECK_INTERVAL)
  }

  /**
   * 停止token刷新管理器
   */
  destroy() {
    if (!this.isActive) return
    
    this.isActive = false
    
    // 清除计时器
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
      this.checkTimer = null
    }
    
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
    
    // 移除事件监听
    window.removeEventListener('storage', this.boundStorageHandler)
    
    // 静默停止
  }

  /**
   * 检查token状态
   */
  checkTokenStatus() {
    const token = getToken()
    
    if (!token) {
      return
    }
    
    try {
      // 解析token获取过期时间
      const tokenData = this.parseJWT(token)
      
      if (!tokenData || !tokenData.exp) {
        return
      }
      
      const now = Date.now()
      const expireTime = tokenData.exp * 1000 // JWT时间戳是秒，需要转换为毫秒
      const timeUntilExpiry = expireTime - now
      
      // 如果已经过期，不处理（由request拦截器处理）
      if (timeUntilExpiry <= 0) {
        return
      }
      
      // 如果即将过期，安排刷新
      if (timeUntilExpiry <= this.REFRESH_BEFORE_EXPIRY) {
        this.scheduleRefresh(Math.max(0, timeUntilExpiry - 60000)) // 提前1分钟刷新
      }
      
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 安排token刷新
   * @param {number} delay 延迟时间（毫秒）
   */
  scheduleRefresh(delay) {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
    }
    
    this.refreshTimer = setTimeout(() => {
      this.performRefresh()
    }, delay)
  }

  /**
   * 执行token刷新
   */
  async performRefresh() {
    if (this.isRefreshing) {
      return
    }
    
    const refreshTokenValue = getRefreshToken()
    
    if (!refreshTokenValue) {
      return
    }
    
    // 🔥 修复：添加双重检查锁定机制，防止多标签页竞争
    const refreshLockKey = 'token_refresh_lock'
    const refreshLockTime = localStorage.getItem(refreshLockKey)
    
    // 如果其他标签页在最近30秒内开始了刷新，则等待
    if (refreshLockTime && (Date.now() - parseInt(refreshLockTime)) < 30000) {
      setTimeout(() => {
        this.checkTokenStatus()
      }, 2000)
      return
    }
    
    this.isRefreshing = true
    
    try {
      // 设置刷新锁定
      localStorage.setItem(refreshLockKey, Date.now().toString())
      
      // 广播刷新开始
      this.broadcastRefreshStart()
      
      const response = await refreshToken(refreshTokenValue)
      
      if (response && response.access_token) {
        // 更新token
        setToken(response.access_token)
        
        // 🔥 修复：同步身份信息到localStorage
        try {
          const tokenPayload = this.parseJWT(response.access_token)
          if (tokenPayload) {
            const currentIdentity = {
              identity_type: tokenPayload.identity_type || 'personal',
              organization_id: tokenPayload.organization_id || null,
              organization_role: tokenPayload.organization_role || null
            }
            localStorage.setItem('currentIdentity', JSON.stringify(currentIdentity))
          }
        } catch (identityError) {
          console.warn('token刷新后身份信息同步失败:', identityError)
        }
        
        // 记录刷新时间
        localStorage.setItem(this.LAST_REFRESH_KEY, Date.now().toString())
        
        // 清除刷新锁定
        localStorage.removeItem(refreshLockKey)
        
        // 广播刷新成功
        this.broadcastRefreshSuccess(response.access_token)
        
        // 重新检查token状态
        setTimeout(() => {
          this.checkTokenStatus()
        }, 1000)
        
      } else {
        throw new Error('刷新响应无效')
      }
      
    } catch (error) {
      // 清除刷新锁定
      localStorage.removeItem(refreshLockKey)
      
      // 广播刷新失败
      this.broadcastRefreshFailure()
      
    } finally {
      this.isRefreshing = false
    }
  }

  /**
   * 解析JWT token
   * @param {string} token JWT token
   * @returns {Object|null} 解析后的payload
   */
  parseJWT(token) {
    try {
      const base64Url = token.split('.')[1]
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      )
      return JSON.parse(jsonPayload)
    } catch (error) {
      return null
    }
  }

  /**
   * 处理localStorage变化（多标签页同步）
   */
  handleStorageChange(event) {
    if (event.key === this.STORAGE_KEY) {
      const data = JSON.parse(event.newValue || '{}')
      
      if (data.action === 'refresh_start') {
        // 其他标签页开始刷新
        this.isRefreshing = true
      } else if (data.action === 'refresh_success') {
        // 其他标签页刷新成功
        this.isRefreshing = false
        
        // 重新检查token状态
        setTimeout(() => {
          this.checkTokenStatus()
        }, 1000)
        
      } else if (data.action === 'refresh_failure') {
        // 其他标签页刷新失败
        this.isRefreshing = false
      }
    }
  }

  /**
   * 广播刷新开始
   */
  broadcastRefreshStart() {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify({
      action: 'refresh_start',
      timestamp: Date.now()
    }))
  }

  /**
   * 广播刷新成功
   * @param {string} newToken 新的access token
   */
  broadcastRefreshSuccess(newToken) {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify({
      action: 'refresh_success',
      token: newToken,
      timestamp: Date.now()
    }))
  }

  /**
   * 广播刷新失败
   */
  broadcastRefreshFailure() {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify({
      action: 'refresh_failure',
      timestamp: Date.now()
    }))
  }

  /**
   * 手动触发刷新
   */
  async manualRefresh() {
    await this.performRefresh()
  }
}

// 创建单例实例
const tokenRefreshManager = new TokenRefreshManager()

export default tokenRefreshManager

/**
 * 组合式函数，提供token刷新功能
 */
export const useTokenRefresh = () => {
  return {
    /**
     * 启动token自动刷新
     */
    start() {
      tokenRefreshManager.init()
    },
    
    /**
     * 停止token自动刷新
     */
    stop() {
      tokenRefreshManager.destroy()
    },
    
    /**
     * 手动刷新token
     */
    async refresh() {
      await tokenRefreshManager.manualRefresh()
    }
  }
}