/**
 * 权限管理工具
 * 
 * 定义系统中的权限常量和角色权限映射关系
 */

// 权限常量定义
export const PERMISSIONS = {
  // 测试功能权限
  TEST_FEATURES: 'test_features',
  
  // 管理员权限
  ADMIN_PANEL: 'admin_panel',
  USER_MANAGEMENT: 'user_management',
  SYSTEM_CONFIG: 'system_config',
  
  // 开发者权限
  DEV_TOOLS: 'dev_tools',
  DEBUG_MODE: 'debug_mode',
  
  // 业务功能权限
  CLIENT_MANAGEMENT: 'client_management',
  WRITING_TOOLS: 'writing_tools',
  AI_TOOLS: 'ai_tools',
  CRM_SYSTEM: 'crm_system',
  SCHOOL_MATCHING: 'school_matching',
  
  // 导出功能权限
  EXPORT_DOCUMENTS: 'export_documents',
  
  // 高级功能权限
  ADVANCED_FEATURES: 'advanced_features'
}

// 角色定义
export const ROLES = {
  ADMIN: 'admin',
  DEV: 'dev', 
  USER: 'user'
}

// 角色权限映射
export const ROLE_PERMISSIONS = {
  [ROLES.ADMIN]: [
    // 管理员拥有所有权限
    PERMISSIONS.TEST_FEATURES,
    PERMISSIONS.ADMIN_PANEL,
    PERMISSIONS.USER_MANAGEMENT,
    PERMISSIONS.SYSTEM_CONFIG,
    PERMISSIONS.DEV_TOOLS,
    PERMISSIONS.DEBUG_MODE,
    PERMISSIONS.CLIENT_MANAGEMENT,
    PERMISSIONS.WRITING_TOOLS,
    PERMISSIONS.AI_TOOLS,
    PERMISSIONS.CRM_SYSTEM,
    PERMISSIONS.SCHOOL_MATCHING,
    PERMISSIONS.EXPORT_DOCUMENTS,
    PERMISSIONS.ADVANCED_FEATURES
  ],
  
  [ROLES.DEV]: [
    // 开发者拥有测试和开发相关权限
    PERMISSIONS.TEST_FEATURES,
    PERMISSIONS.DEV_TOOLS,
    PERMISSIONS.DEBUG_MODE,
    PERMISSIONS.CLIENT_MANAGEMENT,
    PERMISSIONS.WRITING_TOOLS,
    PERMISSIONS.AI_TOOLS,
    PERMISSIONS.CRM_SYSTEM,
    PERMISSIONS.SCHOOL_MATCHING,
    PERMISSIONS.EXPORT_DOCUMENTS
  ],
  
  [ROLES.USER]: [
    // 普通用户只有基础业务权限
    PERMISSIONS.CLIENT_MANAGEMENT,
    PERMISSIONS.WRITING_TOOLS,
    PERMISSIONS.SCHOOL_MATCHING,
    PERMISSIONS.EXPORT_DOCUMENTS
  ]
}

/**
 * 检查角色是否拥有特定权限
 * @param {string} role - 用户角色
 * @param {string} permission - 权限标识
 * @returns {boolean} 是否拥有权限
 */
export const hasRolePermission = (role, permission) => {
  if (!role || !permission) {
    return false
  }
  
  const rolePermissions = ROLE_PERMISSIONS[role] || []
  return rolePermissions.includes(permission)
}

/**
 * 获取角色的所有权限
 * @param {string} role - 用户角色
 * @returns {string[]} 权限列表
 */
export const getRolePermissions = (role) => {
  return ROLE_PERMISSIONS[role] || []
}

/**
 * 检查是否为管理员角色
 * @param {string} role - 用户角色
 * @returns {boolean} 是否为管理员
 */
export const isAdmin = (role) => {
  return role === ROLES.ADMIN
}

/**
 * 检查是否为开发者角色
 * @param {string} role - 用户角色
 * @returns {boolean} 是否为开发者
 */
export const isDev = (role) => {
  return role === ROLES.DEV
}

/**
 * 检查是否为普通用户角色
 * @param {string} role - 用户角色
 * @returns {boolean} 是否为普通用户
 */
export const isUser = (role) => {
  return role === ROLES.USER
}

/**
 * 检查是否有测试功能权限（admin或dev）
 * @param {string} role - 用户角色
 * @returns {boolean} 是否有测试功能权限
 */
export const hasTestFeatureAccess = (role) => {
  return hasRolePermission(role, PERMISSIONS.TEST_FEATURES)
}
