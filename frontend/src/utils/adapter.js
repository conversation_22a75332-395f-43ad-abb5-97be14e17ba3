/**
 * adapter.js - 数据适配器工具
 *
 * 本模块提供数据适配器函数，用于转换数据格式，使后端API返回的数据结构适配前端组件的需求。
 * 主要功能包括：
 * 1. 转换API响应数据结构
 * 2. 处理不同API版本之间的数据结构差异
 * 3. 规范化或标准化来自不同来源的数据
 */

/**
 * 将后端返回的列表数据转换为前端表格组件所需的格式
 *
 * @param {Array} data - 后端返回的数据数组
 * @param {Object} fieldMap - 字段映射对象，键为前端字段名，值为后端字段名
 * @returns {Array} 转换后的数据数组
 *
 * @example
 * // 后端数据: [{user_id: 1, user_name: '张三', user_age: 20}]
 * // 转换为: [{id: 1, name: '张三', age: 20}]
 * adaptTableData(data, {id: 'user_id', name: 'user_name', age: 'user_age'})
 */
export function adaptTableData(data, fieldMap) {
  if (!Array.isArray(data)) {
    console.warn('adaptTableData: 输入数据不是数组')
    return []
  }

  return data.map(item => {
    const result = {}

    Object.entries(fieldMap).forEach(([targetKey, sourceKey]) => {
      // 支持嵌套属性路径，如 'user.profile.name'
      if (sourceKey.includes('.')) {
        const keys = sourceKey.split('.')
        let value = item

        for (const key of keys) {
          value = value?.[key]
          if (value === undefined) break
        }

        result[targetKey] = value
      } else {
        result[targetKey] = item[sourceKey]
      }
    })

    return result
  })
}

/**
 * 将后端返回的分页数据转换为前端分页组件所需的格式
 *
 * @param {Object} response - 后端返回的响应对象
 * @param {Object} options - 配置选项
 * @param {string} [options.itemsKey='items'] - 数据项的键名
 * @param {string} [options.totalKey='total'] - 总数的键名
 * @param {string} [options.pageKey='page'] - 当前页码的键名
 * @param {string} [options.pageSizeKey='page_size'] - 每页条数的键名
 * @returns {Object} 转换后的分页数据对象
 *
 * @example
 * // 后端数据: {results: [...], count: 100, current_page: 1, per_page: 10}
 * // 转换为: {items: [...], total: 100, page: 1, pageSize: 10}
 * adaptPaginationData(response, {
 *   itemsKey: 'results',
 *   totalKey: 'count',
 *   pageKey: 'current_page',
 *   pageSizeKey: 'per_page'
 * })
 */
export function adaptPaginationData(response, options = {}) {
  const {
    itemsKey = 'items',
    totalKey = 'total',
    pageKey = 'page',
    pageSizeKey = 'page_size'
  } = options

  if (!response || typeof response !== 'object') {
    console.warn('adaptPaginationData: 输入数据不是对象')
    return {
      items: [],
      total: 0,
      page: 1,
      pageSize: 10
    }
  }

  return {
    items: response[itemsKey] || [],
    total: response[totalKey] || 0,
    page: response[pageKey] || 1,
    pageSize: response[pageSizeKey] || 10
  }
}

/**
 * 将对象的键名从驼峰命名转换为下划线命名
 *
 * @param {Object} obj - 要转换的对象
 * @returns {Object} 转换后的对象
 *
 * @example
 * // 返回 {user_name: '张三', user_age: 20}
 * camelToSnake({userName: '张三', userAge: 20})
 */
export function camelToSnake(obj) {
  if (!obj || typeof obj !== 'object' || Array.isArray(obj)) {
    return obj
  }

  const result = {}

  Object.entries(obj).forEach(([key, value]) => {
    // 将驼峰命名转换为下划线命名
    const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase()

    // 递归处理嵌套对象
    if (value && typeof value === 'object') {
      result[snakeKey] = Array.isArray(value)
        ? value.map(item => typeof item === 'object' ? camelToSnake(item) : item)
        : camelToSnake(value)
    } else {
      result[snakeKey] = value
    }
  })

  return result
}

/**
 * 将对象的键名从下划线命名转换为驼峰命名
 *
 * @param {Object} obj - 要转换的对象
 * @returns {Object} 转换后的对象
 *
 * @example
 * // 返回 {userName: '张三', userAge: 20}
 * snakeToCamel({user_name: '张三', user_age: 20})
 */
export function snakeToCamel(obj) {
  if (!obj || typeof obj !== 'object' || Array.isArray(obj)) {
    return obj
  }

  const result = {}

  Object.entries(obj).forEach(([key, value]) => {
    // 将下划线命名转换为驼峰命名
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())

    // 递归处理嵌套对象
    if (value && typeof value === 'object') {
      result[camelKey] = Array.isArray(value)
        ? value.map(item => typeof item === 'object' ? snakeToCamel(item) : item)
        : snakeToCamel(value)
    } else {
      result[camelKey] = value
    }
  })

  return result
}

/**
 * 将后端返回的树形数据转换为前端树形组件所需的格式
 *
 * @param {Array} data - 后端返回的数据数组
 * @param {Object} options - 配置选项
 * @param {string} [options.idKey='id'] - ID字段的键名
 * @param {string} [options.parentIdKey='parent_id'] - 父ID字段的键名
 * @param {string} [options.childrenKey='children'] - 子节点字段的键名
 * @param {Function} [options.transform] - 节点转换函数
 * @returns {Array} 转换后的树形数据
 *
 * @example
 * // 后端数据: [{id: 1, name: '父节点', parent_id: null}, {id: 2, name: '子节点', parent_id: 1}]
 * // 转换为: [{id: 1, name: '父节点', children: [{id: 2, name: '子节点'}]}]
 * adaptTreeData(data)
 */
export function adaptTreeData(data, options = {}) {
  const {
    idKey = 'id',
    parentIdKey = 'parent_id',
    childrenKey = 'children',
    transform = item => item
  } = options

  if (!Array.isArray(data)) {
    console.warn('adaptTreeData: 输入数据不是数组')
    return []
  }

  // 创建节点映射
  const nodeMap = {}
  data.forEach(item => {
    nodeMap[item[idKey]] = {
      ...transform(item),
      [childrenKey]: []
    }
  })

  // 构建树形结构
  const tree = []
  data.forEach(item => {
    const id = item[idKey]
    const parentId = item[parentIdKey]

    if (parentId === null || parentId === undefined || parentId === '') {
      // 根节点
      tree.push(nodeMap[id])
    } else if (nodeMap[parentId]) {
      // 子节点
      nodeMap[parentId][childrenKey].push(nodeMap[id])
    } else {
      // 找不到父节点，作为根节点处理
      console.warn(`adaptTreeData: 找不到ID为${parentId}的父节点`)
      tree.push(nodeMap[id])
    }
  })

  return tree
}