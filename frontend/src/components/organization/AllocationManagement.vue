<template>
  <div class="allocation-management">
    <!-- 管理头部 -->
    <div class="management-header">
      <div class="header-info">
        <h3 class="title">套餐分配管理</h3>
        <p class="description">管理组织成员的套餐权益分配</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading" size="small">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button
          type="primary"
          @click="openAllocationDialog"
          :disabled="!canAllocatePackages"
          size="small"
        >
          <el-icon><Plus /></el-icon>
          分配套餐
        </el-button>
      </div>
    </div>

    <!-- 权限提示 -->
    <div v-if="!canAllocatePackages" class="permission-notice">
      <el-alert
        :title="permissionMessage || '请切换到组织身份并确保您是组织管理员'"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Box /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ totalAvailablePackages }}</div>
          <div class="stat-label">可分配套餐</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ activeAllocationsCount }}</div>
          <div class="stat-label">已分配成员</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ totalAllocationsCount }}</div>
          <div class="stat-label">分配记录</div>
        </div>
      </div>
    </div>

    <!-- 分配列表 -->
    <div class="allocation-list">
      <div class="list-header">
        <h4>分配记录</h4>
        <div class="list-filters">
          <el-select
            v-model="statusFilter"
            placeholder="状态筛选"
            clearable
            size="small"
            style="width: 120px"
            @change="handleStatusFilterChange"
          >
            <el-option label="已分配" value="active" />
            <el-option label="已回收" value="revoked" />
          </el-select>
        </div>
      </div>

      <div v-if="loading" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="organizationAllocations.length === 0" class="empty-state">
        <el-empty description="暂无分配记录">
          <el-button type="primary" @click="openAllocationDialog" v-if="canAllocatePackages">
            立即分配套餐
          </el-button>
        </el-empty>
      </div>

      <div v-else class="allocation-table">
        <el-table :data="organizationAllocations" stripe>
          <el-table-column prop="formatted_package_name" label="套餐名称" width="150" />
          <el-table-column prop="allocated_user_name" label="分配成员" width="120" />
          <el-table-column label="分配时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.allocated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="过期时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.allocated_order?.expires_at) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.formatted_status.type" size="small">
                {{ row.formatted_status.label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="row.status === 'active' && canAllocatePackages"
                type="danger"
                size="small"
                text
                @click="handleRevokeAllocation(row)"
              >
                回收
              </el-button>
              <span v-else-if="row.status === 'revoked'" class="text-gray-400">
                已回收
              </span>
              <span v-else class="text-gray-400">
                无权限
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分配对话框 -->
    <PackageAllocationDialog
      v-model:visible="allocationDialogVisible"
      :organization-id="organizationId"
      @success="handleAllocationSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Refresh, Plus, Box, User, Check } from '@element-plus/icons-vue'
import { usePackageAllocation } from '@/composables/usePackageAllocation'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import PackageAllocationDialog from './PackageAllocationDialog.vue'

// Props
const props = defineProps({
  organizationId: {
    type: Number,
    required: true
  }
})

// 组合式函数
let packageAllocation, organizationPermissions

try {
  packageAllocation = usePackageAllocation()
  organizationPermissions = useOrganizationPermissions()
} catch (error) {
  console.error('组合式函数初始化失败:', error)
  // 提供默认值
  packageAllocation = {
    loading: ref(false),
    availablePackages: ref([]),
    organizationAllocations: ref([]),
    canAllocatePackages: ref(false),
    permissionMessage: ref('组合式函数初始化失败'),
    fetchAvailablePackages: async () => {},
    fetchOrganizationAllocations: async () => {},
    handleRevokePackage: async () => {}
  }
  organizationPermissions = {
    currentOrganization: ref(null)
  }
}

const {
  loading,
  availablePackages,
  organizationAllocations,
  canAllocatePackages,
  permissionMessage,
  fetchAvailablePackages,
  fetchOrganizationAllocations,
  handleRevokePackage
} = packageAllocation

const { currentOrganization } = organizationPermissions

// 响应式数据
const allocationDialogVisible = ref(false)
const statusFilter = ref('')

// 计算属性
const totalAvailablePackages = computed(() => {
  return availablePackages.value.reduce((total, pkg) => total + pkg.available_count, 0)
})

const activeAllocationsCount = computed(() => {
  return organizationAllocations.value.filter(allocation => allocation.status === 'active').length
})

const totalAllocationsCount = computed(() => {
  return organizationAllocations.value.length
})

// 方法
const loadData = async () => {
  await Promise.all([
    fetchAvailablePackages(props.organizationId),
    fetchOrganizationAllocations(props.organizationId, statusFilter.value)
  ])
}

// 监听组织变化
watch(() => props.organizationId, (newId) => {
  if (newId) {
    loadData()
  }
}, { immediate: true })

const refreshData = async () => {
  await loadData()
}

const openAllocationDialog = () => {
  allocationDialogVisible.value = true
}

const handleAllocationSuccess = () => {
  // 分配成功后刷新数据
  refreshData()
}

const handleStatusFilterChange = () => {
  fetchOrganizationAllocations(props.organizationId, statusFilter.value)
}

const handleRevokeAllocation = async (allocation) => {
  const revocationData = {
    organization_id: props.organizationId,
    allocated_user_id: allocation.allocated_user_id,
    package_id: allocation.package_id
  }
  
  const success = await handleRevokePackage(revocationData)
  if (success) {
    // 回收成功后刷新数据
    refreshData()
  }
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'

  try {
    // 如果时间字符串没有时区标识符，假设它是UTC时间，添加'Z'
    let utcTimeString = dateTime;
    if (!dateTime.endsWith('Z') && !dateTime.includes('+') && !dateTime.includes('-', 10)) {
      utcTimeString = dateTime + 'Z';
    }

    // 创建UTC时间对象并转换为北京时间显示
    const utcDate = new Date(utcTimeString);

    return utcDate.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Shanghai' // 明确指定北京时区
    });
  } catch (error) {
    console.warn('时间格式化失败:', dateTime, error);
    return '-';
  }
}



// 生命周期
onMounted(() => {
  if (props.organizationId) {
    loadData()
  }
})
</script>

<style scoped>
.allocation-management {
  background: white;
  border-radius: 8px;
  padding: 24px;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-info .title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.header-info .description {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.permission-notice {
  margin-bottom: 24px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #4f46e5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #606266;
  margin-top: 4px;
}

.allocation-list {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.list-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.loading-state,
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.allocation-table {
  background: white;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}
</style>
