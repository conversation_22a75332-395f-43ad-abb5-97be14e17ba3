<template>
  <el-dialog
    v-model="dialogVisible"
    title="分配套餐权益"
    width="800px"
    class="simple-dialog compact-dialog"
    @close="handleClose"
    :style="{ 
      '--el-dialog-bg-color': '#ffffff',
      '--el-dialog-header-bg-color': '#ffffff',
      '--el-dialog-footer-bg-color': '#ffffff'
    }"
  >
    <div class="simple-content">
      <!-- 权限检查提示 -->
      <div v-if="!canAllocatePackages || !hasValidOrganizationId" class="mb-6">
        <div class="bg-white border border-orange-200 rounded-lg p-4">
          <div class="flex items-center space-x-3">
            <span class="material-icons-outlined text-orange-600">warning</span>
            <p class="text-sm text-orange-700 leading-6">
              {{ hasValidOrganizationId ? permissionMessage : '请先切换到组织身份' }}
            </p>
          </div>
        </div>
      </div>

      <!-- 分配表单 - 三步骤整合在一个界面 -->
      <div v-else class="allocation-form space-y-6">
        <!-- 第一部分：选择套餐 -->
        <div class="form-section">
          <label class="block text-sm font-medium text-gray-900 mb-3">选择要分配的套餐</label>
          
          <div v-if="loadingPackages" class="py-4">
            <div class="animate-pulse space-y-3">
              <div class="h-16 bg-gray-200 rounded-lg"></div>
              <div class="h-16 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
          
          <div v-else-if="availablePackages.length === 0" class="text-center py-8">
            <div class="text-gray-400 mb-4">
              <span class="material-icons-outlined text-4xl">inventory_2</span>
            </div>
            <p class="text-sm text-gray-600 leading-6 mb-4">暂无可分配的套餐</p>
            <button
              @click="refreshPackages"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-indigo-600 bg-white border border-indigo-200 rounded-lg hover:border-indigo-300 hover:bg-white transition-colors"
            >
              <span class="material-icons-outlined text-sm mr-1">refresh</span>
              刷新套餐列表
            </button>
          </div>
          
          <div v-else class="space-y-3">
            <div
              v-for="pkg in availablePackages"
              :key="pkg.package_id"
              class="package-card"
              :class="{ 
                'selected': selectedPackage?.package_id === pkg.package_id,
                'disabled': !pkg.can_allocate
              }"
              @click="selectPackage(pkg)"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center flex-1">
                  <!-- 套餐图标 -->
                  <div 
                    class="w-10 h-10 rounded-full flex items-center justify-center mr-3 flex-shrink-0"
                    :style="{ 
                      backgroundColor: pkg.package_info?.bgColor || '#F9FAFB',
                      color: pkg.package_info?.color || '#6B7280'
                    }"
                  >
                    <span class="material-icons-outlined text-lg">
                      {{ pkg.package_info?.icon || 'inventory_2' }}
                    </span>
                  </div>
                  
                  <!-- 套餐信息 -->
                  <div class="flex-1">
                    <div class="font-medium text-gray-900 mb-1">{{ pkg.formatted_name }}</div>
                    <div class="text-xs text-gray-500 space-y-1">
                      <div>
                        <span :class="{ 'text-red-500 font-medium': pkg.available_count === 0, 'text-green-600 font-medium': pkg.available_count > 0 }">
                          {{ pkg.available_count > 0 ? `可分配 ${pkg.available_count} 个` : '暂无可分配' }}
                        </span>
                      </div>
                      <div class="text-gray-400">
                        已购买 {{ pkg.purchased_count }} 个 (含管理员 {{ pkg.owner_reserved_count || 1 }} 个)
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 选择状态 -->
                <div class="flex-shrink-0">
                  <span v-if="selectedPackage?.package_id === pkg.package_id" class="material-icons-outlined text-indigo-600">
                    radio_button_checked
                  </span>
                  <span v-else-if="pkg.can_allocate" class="material-icons-outlined text-gray-400">
                    radio_button_unchecked
                  </span>
                  <span v-else class="text-xs text-gray-400">无可分配</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二部分：选择成员 -->
        <div v-if="selectedPackage" class="form-section">
          <label class="block text-sm font-medium text-gray-900 mb-3">选择要分配给的成员</label>


          
          <!-- 成员搜索 -->
          <div class="mb-4">
            <el-input
              v-model="memberSearchQuery"
              placeholder="搜索成员姓名"
              clearable
              @input="handleMemberSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          
          <div v-if="loadingMembers" class="py-4">
            <div class="animate-pulse space-y-3">
              <div class="h-12 bg-gray-200 rounded-lg"></div>
              <div class="h-12 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
          
          <div v-else-if="filteredMembers.length === 0" class="text-center py-8">
            <div class="text-gray-400 mb-4">
              <span class="material-icons-outlined text-4xl">person_off</span>
            </div>
            <p class="text-sm text-gray-600 leading-6">暂无可分配的成员</p>
          </div>
          
          <div v-else class="space-y-2 max-h-48 overflow-y-auto">
            <div
              v-for="member in filteredMembers"
              :key="member.user_id"
              class="member-card"
              :class="{
                'selected': isMemberSelected(member),
                'disabled': !canSelectMember(member),
                'owner-default': member.role === 'owner'
              }"
              @click="selectMember(member)"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center flex-1">
                  <div class="font-medium text-gray-900 mr-3">{{ member.organization_username }}</div>
                  <div class="flex items-center space-x-2">
                    <span 
                      class="px-2 py-1 rounded-full text-xs font-medium"
                      :class="member.role === 'owner' ? 'bg-purple-100 text-purple-700' : 'bg-gray-100 text-gray-600'"
                    >
                      {{ member.role === 'owner' ? '管理员' : '成员' }}
                    </span>
                    <span 
                      v-if="selectedPackage && memberHasPackage(member, selectedPackage.package_id)"
                      class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700"
                    >
                      已拥有
                    </span>
                  </div>
                </div>
                <div class="flex-shrink-0">
                  <!-- 组织所有者：显示灰色打勾，表示默认拥有权限 -->
                  <span
                    v-if="member.role === 'owner'"
                    class="material-icons-outlined text-gray-400"
                    title="管理员默认拥有套餐权限"
                  >
                    check_circle
                  </span>
                  <!-- 普通成员：正常的选择状态 -->
                  <span
                    v-else-if="isMemberSelected(member)"
                    class="material-icons-outlined text-indigo-600"
                  >
                    check_circle
                  </span>
                  <span
                    v-else-if="canSelectMember(member)"
                    class="material-icons-outlined text-gray-400"
                  >
                    radio_button_unchecked
                  </span>
                  <span
                    v-else
                    class="material-icons-outlined text-gray-300"
                  >
                    cancel
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第三部分：确认分配信息 -->
        <div v-if="selectedPackage && selectedMembers.length > 0" class="form-section">
          <label class="block text-sm font-medium text-gray-900 mb-3">确认分配信息</label>
          
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-700">套餐</span>
                <div class="flex items-center">
                  <div 
                    class="w-6 h-6 rounded-full flex items-center justify-center mr-2"
                    :style="{ 
                      backgroundColor: selectedPackage.package_info?.bgColor || '#F9FAFB',
                      color: selectedPackage.package_info?.color || '#6B7280'
                    }"
                  >
                    <span class="material-icons-outlined text-sm">
                      {{ selectedPackage.package_info?.icon || 'inventory_2' }}
                    </span>
                  </div>
                  <span class="text-sm font-medium text-gray-900">{{ selectedPackage.formatted_name }}</span>
                </div>
              </div>
              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-700">分配给</span>
                  <span class="text-xs text-gray-500">{{ selectedMembers.length }}/{{ maxAllowedSelections }}</span>
                </div>
                <div class="space-y-2">
                  <div 
                    v-for="member in selectedMembers" 
                    :key="member.user_id"
                    class="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-2"
                  >
                    <div class="flex items-center">
                      <span class="text-sm font-medium text-gray-900 mr-2">{{ member.organization_username }}</span>
                      <span 
                        class="px-2 py-1 rounded-full text-xs font-medium"
                        :class="member.role === 'owner' ? 'bg-purple-100 text-purple-700' : 'bg-gray-100 text-gray-600'"
                      >
                        {{ member.role === 'owner' ? '管理员' : '成员' }}
                      </span>
                    </div>
                    <button 
                      @click="selectMember(member)"
                      class="text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <span class="material-icons-outlined text-sm">close</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="mt-3">
            <p class="text-xs text-gray-500 text-left">
              确认分配后该成员将立即获得套餐权益
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="flex justify-end">
        <el-button
          v-if="canAllocatePackages && hasValidOrganizationId && selectedPackage && selectedMembers.length > 0"
          type="primary"
          :loading="loading"
          @click="handleConfirmAllocation"
        >
          确认分配
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 批量分配确认对话框 -->
  <el-dialog
    v-model="batchConfirmVisible"
    title="确认分配"
    width="500px"
    class="simple-dialog compact-dialog"
    @close="handleBatchConfirmCancel"
  >
    <div class="simple-content">
      <div class="mb-6">
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
              <span class="material-icons-outlined text-orange-600 text-xl">warning</span>
            </div>
          </div>
          <div class="flex-1">
            <p class="text-sm text-gray-600 leading-6 mb-4">
              确定要将套餐"<strong>{{ batchConfirmData.packageName }}</strong>"分配给以下 {{ batchConfirmData.memberCount }} 位成员吗？
            </p>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <p class="text-sm text-gray-700 leading-6">{{ batchConfirmData.memberNames }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-end">
        <el-button
          type="primary"
          :loading="batchConfirmLoading"
          @click="handleBatchConfirmOk"
        >
          确定分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { usePackageAllocation } from '@/composables/usePackageAllocation'
// import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import { getOrganizationMembers } from '@/api/organizations'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  organizationId: {
    type: [Number, undefined],
    default: undefined,
    validator: (value) => {
      // 允许Number类型或undefined，但如果是Number必须大于0
      return value === undefined || (typeof value === 'number' && value > 0)
    }
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 组合式函数
const {
  loading,
  availablePackages,
  organizationAllocations,
  canAllocatePackages,
  permissionMessage,
  fetchAvailablePackages,
  fetchOrganizationAllocations,
  handleAllocatePackage
} = usePackageAllocation()

// const { currentOrganization } = useOrganizationPermissions()

// 响应式数据
const dialogVisible = ref(false)
const selectedPackage = ref(null)
const selectedMembers = ref([])
const loadingPackages = ref(false)
const loadingMembers = ref(false)
const memberSearchQuery = ref('')
const organizationMembers = ref([])

// 批量分配确认对话框状态
const batchConfirmVisible = ref(false)
const batchConfirmLoading = ref(false)
const batchConfirmData = ref({
  packageName: '',
  memberCount: 0,
  memberNames: ''
})
let batchConfirmResolve = null

// 计算属性
const hasValidOrganizationId = computed(() => {
  return props.organizationId !== undefined && props.organizationId > 0
})

const filteredMembers = computed(() => {
  // 🔥 修改：将组织所有者显示在第一位，但设为不可选择状态
  let allMembers = [...organizationMembers.value]

  // 如果有搜索条件，先进行搜索过滤
  if (memberSearchQuery.value) {
    const query = memberSearchQuery.value.toLowerCase()
    allMembers = allMembers.filter(member =>
      member.organization_username?.toLowerCase().includes(query) ||
      member.user_info?.email?.toLowerCase().includes(query)
    )
  }

  // 将所有者排在第一位，其他成员按原顺序排列
  const owners = allMembers.filter(member => member.role === 'owner')
  const regularMembers = allMembers.filter(member => member.role !== 'owner')

  return [...owners, ...regularMembers]
})

// 计算可分配的最大数量
const maxAllowedSelections = computed(() => {
  return selectedPackage.value?.available_count || 0
})

// 是否已达到选择上限
const isSelectionLimitReached = computed(() => {
  return selectedMembers.value.length >= maxAllowedSelections.value
})

// 检查成员是否已选择
const isMemberSelected = (member) => {
  return selectedMembers.value.some(selected => selected.user_id === member.user_id)
}

// 检查成员是否可以被选择
const canSelectMember = (member) => {
  // 组织所有者不可选择，因为默认拥有权限
  if (member.role === 'owner') return false

  return isMemberSelected(member) || !isSelectionLimitReached.value
}

// 检查成员是否已经拥有指定套餐
const memberHasPackage = (member, packageId) => {
  return organizationAllocations.value.some(allocation => 
    allocation.allocated_user_id === member.user_id && 
    allocation.package_id === packageId && 
    allocation.status === 'active'
  )
}

// 过滤掉已经拥有当前套餐的成员
const getMembersWithoutPackage = (members, packageId) => {
  return members.filter(member => !memberHasPackage(member, packageId))
}

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initDialog()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  
  // 强制修改对话框样式为白色背景
  if (newVal) {
    setTimeout(() => {
      const dialogHeader = document.querySelector('.el-dialog__header')
      const dialogBody = document.querySelector('.el-dialog__body')
      const dialogFooter = document.querySelector('.el-dialog__footer')
      
      if (dialogHeader) {
        dialogHeader.style.background = '#ffffff'
        dialogHeader.style.backgroundColor = '#ffffff'
        dialogHeader.style.borderBottom = 'none'
      }
      
      if (dialogBody) {
        dialogBody.style.background = '#ffffff'
        dialogBody.style.backgroundColor = '#ffffff'
      }
      
      if (dialogFooter) {
        dialogFooter.style.background = '#ffffff'
        dialogFooter.style.backgroundColor = '#ffffff'
        dialogFooter.style.borderTop = 'none'
      }
    }, 50)
  }
})

// 监听选择的套餐变化，清空已选择的成员
watch(() => selectedPackage.value, () => {
  selectedMembers.value = []
})

// 方法
const initDialog = async () => {
  selectedPackage.value = null
  selectedMembers.value = []
  memberSearchQuery.value = ''
  
  // 只有在有有效的organizationId时才加载数据
  if (hasValidOrganizationId.value) {
    // 并行加载数据
    await Promise.all([
      refreshPackages(),
      loadOrganizationMembers(),
      fetchOrganizationAllocations(props.organizationId)
    ])
  }
}

const refreshPackages = async () => {
  if (!hasValidOrganizationId.value) return
  
  loadingPackages.value = true
  try {
    await fetchAvailablePackages(props.organizationId)
  } finally {
    loadingPackages.value = false
  }
}

const loadOrganizationMembers = async () => {
  if (!hasValidOrganizationId.value) return
  
  loadingMembers.value = true
  try {
    const response = await getOrganizationMembers(props.organizationId, { limit: 100 })
    // 显示所有组织成员，包括管理员，允许给任何成员分配套餐
    organizationMembers.value = response
  } catch (error) {
    console.error('加载组织成员失败:', error)
  } finally {
    loadingMembers.value = false
  }
}

const selectPackage = (pkg) => {
  if (pkg.can_allocate) {
    selectedPackage.value = pkg
  }
}

const selectMember = (member) => {
  // 如果成员不能被选择，直接返回
  if (!canSelectMember(member)) return
  
  const isSelected = isMemberSelected(member)
  
  if (isSelected) {
    // 如果已选择，则移除
    selectedMembers.value = selectedMembers.value.filter(selected => selected.user_id !== member.user_id)
  } else {
    // 如果未选择且未达到上限，则添加
    selectedMembers.value.push(member)
  }
}

const handleMemberSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

// 显示批量分配确认对话框
const showBatchAllocationConfirm = (packageName, memberCount, memberNames) => {
  return new Promise((resolve) => {
    batchConfirmData.value = {
      packageName,
      memberCount,
      memberNames
    }
    batchConfirmResolve = resolve
    batchConfirmVisible.value = true
  })
}

// 批量确认对话框确定按钮处理
const handleBatchConfirmOk = () => {
  if (batchConfirmResolve) {
    batchConfirmResolve(true)
    batchConfirmResolve = null
  }
  batchConfirmVisible.value = false
}

// 批量确认对话框取消按钮处理
const handleBatchConfirmCancel = () => {
  if (batchConfirmResolve) {
    batchConfirmResolve(false)
    batchConfirmResolve = null
  }
  batchConfirmVisible.value = false
}

const handleConfirmAllocation = async () => {
  if (!hasValidOrganizationId.value || !selectedPackage.value || selectedMembers.value.length === 0) return
  
  // 过滤掉已经拥有该套餐的成员
  const membersToAllocate = getMembersWithoutPackage(selectedMembers.value, selectedPackage.value.package_id)
  const membersAlreadyHave = selectedMembers.value.filter(member => 
    memberHasPackage(member, selectedPackage.value.package_id)
  )
  
  // 如果有成员已经拥有该套餐，给出提示
  if (membersAlreadyHave.length > 0) {
    const memberNames = membersAlreadyHave.map(m => m.organization_username).join('、')
    ElMessage.warning(`${memberNames} 已经拥有该套餐，将跳过分配`)
  }
  
  // 如果没有成员需要分配
  if (membersToAllocate.length === 0) {
    ElMessage.info('所选成员都已拥有该套餐，无需重复分配')
    return
  }
  
  // 批量分配确认 - 使用自定义确认对话框
  const memberNames = membersToAllocate.map(m => m.organization_username).join('、')
  const confirmResult = await showBatchAllocationConfirm(
    selectedPackage.value.formatted_name,
    membersToAllocate.length,
    memberNames
  )
  
  if (!confirmResult) {
    return // 用户取消操作
  }
  
  let successCount = 0
  let failureCount = 0
  
  // 批量分配给需要的成员
  for (const member of membersToAllocate) {
    const allocationData = {
      organization_id: props.organizationId,
      allocated_user_id: member.user_id,
      package_id: selectedPackage.value.package_id
    }
    
    // 在批量分配时，禁用确认对话框、消息提示，并且不立即刷新数据
    const success = await handleAllocatePackage(allocationData, {
      showConfirm: false,
      showMessage: false, 
      refreshData: false
    })
    
    if (success) {
      successCount++
    } else {
      failureCount++
    }
  }
  
  // 批量分配完成后，统一刷新数据
  if (successCount > 0) {
    await Promise.all([
      fetchAvailablePackages(),
      fetchOrganizationAllocations(props.organizationId)
    ])
  }
  
  // 显示结果
  if (successCount > 0) {
    emit('success')
    if (failureCount === 0) {
      // 全部成功
      ElMessage.success(`已成功分配给 ${successCount} 位成员`)
    } else {
      // 部分成功
      ElMessage.warning(`已分配给 ${successCount} 位成员，${failureCount} 位分配失败`)
    }
    handleClose()
  } else {
    // 全部失败
    ElMessage.error('分配失败，请稍后重试')
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style>
/* 简洁对话框样式 - 无分割线整体页面，上下紧凑布局 */
/* 强制覆盖所有可能的灰色背景 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
    background: #ffffff !important;
    background-color: #ffffff !important;
  }
  
  .el-dialog__header {
    background: #ffffff !important;
    background-color: #ffffff !important;
    border-bottom: none !important;
    padding: 16px 32px 8px !important;
  }
  
  .el-dialog__title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin-bottom: 0 !important;
    line-height: 1.2 !important;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px !important;
    background: #ffffff !important;
    background-color: #ffffff !important;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px !important;
    background: #ffffff !important;
    background-color: #ffffff !important;
    border-top: none !important;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
  }

  /* 次要按钮样式 */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
  }
  
  /* 输入框样式 */
  .el-input .el-input__wrapper {
    --el-input-focus-border-color: #4F46E5 !important;
    --el-input-hover-border-color: #6366F1 !important;
  }
}

/* 强制覆盖全局灰色背景样式 - 最高优先级 */
.el-dialog.simple-dialog .el-dialog__header,
.el-dialog.compact-dialog .el-dialog__header,
:deep(.simple-dialog) .el-dialog__header,
:deep(.compact-dialog) .el-dialog__header {
  background: #ffffff !important;
  background-color: #ffffff !important;
  border-bottom: none !important;
}

.el-dialog.simple-dialog .el-dialog__body,
.el-dialog.compact-dialog .el-dialog__body,
:deep(.simple-dialog) .el-dialog__body,
:deep(.compact-dialog) .el-dialog__body {
  background: #ffffff !important;
  background-color: #ffffff !important;
}

.el-dialog.simple-dialog .el-dialog__footer,
.el-dialog.compact-dialog .el-dialog__footer,
:deep(.simple-dialog) .el-dialog__footer,
:deep(.compact-dialog) .el-dialog__footer {
  background: #ffffff !important;
  background-color: #ffffff !important;
  border-top: none !important;
}

/* 通过Element类名直接覆盖 */
div.el-dialog__wrapper .el-dialog .el-dialog__header,
div.el-dialog__wrapper .el-dialog .el-dialog__body,
div.el-dialog__wrapper .el-dialog .el-dialog__footer {
  background: #ffffff !important;
  background-color: #ffffff !important;
}

/* 覆盖Tailwind的bg-gray-50样式 */
.simple-dialog .el-dialog__header.bg-gray-50,
.compact-dialog .el-dialog__header.bg-gray-50,
.simple-dialog .el-dialog__footer.bg-gray-50,
.compact-dialog .el-dialog__footer.bg-gray-50 {
  background: #ffffff !important;
  background-color: #ffffff !important;
}

/* 最终解决方案 - 使用属性选择器强制覆盖 */
.el-dialog[class*="simple-dialog"] .el-dialog__header,
.el-dialog[class*="compact-dialog"] .el-dialog__header,
[class*="simple-dialog"] .el-dialog .el-dialog__header,
[class*="compact-dialog"] .el-dialog .el-dialog__header {
  background: white !important;
  background-color: white !important;
  border-bottom: none !important;
}

.el-dialog[class*="simple-dialog"] .el-dialog__body,
.el-dialog[class*="compact-dialog"] .el-dialog__body,
[class*="simple-dialog"] .el-dialog .el-dialog__body,
[class*="compact-dialog"] .el-dialog .el-dialog__body {
  background: white !important;
  background-color: white !important;
}

.el-dialog[class*="simple-dialog"] .el-dialog__footer,
.el-dialog[class*="compact-dialog"] .el-dialog__footer,
[class*="simple-dialog"] .el-dialog .el-dialog__footer,
[class*="compact-dialog"] .el-dialog .el-dialog__footer {
  background: white !important;
  background-color: white !important;
  border-top: none !important;
}

/* 表单区域样式 */
.form-section {
  @apply space-y-3;
}

/* 套餐卡片样式 */
.package-card {
  @apply border border-gray-200 rounded-lg p-4 cursor-pointer transition-all duration-200 bg-white;
}

.package-card:hover {
  @apply border-indigo-300 bg-white;
}

.package-card.selected {
  @apply border-indigo-500 bg-white;
}

.package-card.disabled {
  @apply opacity-60 cursor-not-allowed bg-white;
}

.package-card.disabled:hover {
  @apply border-gray-200 bg-white;
}

/* 成员卡片样式 */
.member-card {
  @apply border border-gray-200 rounded-lg p-3 cursor-pointer transition-all duration-200 bg-white;
}

.member-card:hover {
  @apply border-indigo-300 bg-white;
}

.member-card.selected {
  @apply border-indigo-500 bg-white;
}

.member-card.disabled {
  @apply border-gray-200 bg-gray-50 cursor-not-allowed opacity-50;
}

.member-card.disabled:hover {
  @apply border-gray-200 bg-gray-50;
}

/* 组织所有者特殊样式 */
.member-card.owner-default {
  @apply bg-gray-50 border-gray-300 cursor-default;
  opacity: 0.8;
}

.member-card.owner-default:hover {
  @apply border-gray-300 bg-gray-50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.simple-dialog .el-dialog) {
    width: 90% !important;
    min-width: 320px;
    max-width: 600px;
    margin: 5vh auto !important;
  }
  
  :deep(.simple-dialog .el-dialog__header) {
    padding: 12px 20px 6px;
  }
  
  :deep(.simple-dialog .el-dialog__body) {
    padding: 6px 20px 6px;
  }
  
  :deep(.simple-dialog .el-dialog__footer) {
    padding: 8px 20px 12px;
  }
}
</style>
