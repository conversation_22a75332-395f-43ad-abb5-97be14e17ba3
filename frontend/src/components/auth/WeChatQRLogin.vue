<template>
  <div class="wechat-qr-login">
    <!-- 微信登录按钮样式标题 -->
    <div class="text-center mb-4">
      <div class="w-full py-4 px-4 rounded-xl bg-gray-200 text-gray-700 text-center font-medium cursor-not-allowed border border-gray-300 transition-all duration-200">
        <div class="flex items-center justify-center space-x-3">
          <img src="@/assets/wechat_logo.png" alt="微信" class="w-6 h-6" />
          <span class="text-lg">微信扫码登录</span>
        </div>
      </div>
      <p class="text-xs text-gray-600 text-center mt-3">请使用微信扫描下方二维码</p>
    </div>

    <!-- 二维码容器 - 移除边框和背景 -->
    <div class="qr-container relative mx-auto mb-6 overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex flex-col items-center justify-center absolute inset-0 bg-white rounded-2xl z-20">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mb-3"></div>
        <p class="text-gray-700 text-sm">正在加载二维码...</p>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="flex flex-col items-center justify-center absolute inset-0 bg-white rounded-2xl z-20 p-6">
        <svg class="w-12 h-12 text-red-500 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <p class="text-red-600 text-sm font-medium mb-2">加载失败</p>
        <p class="text-gray-600 text-xs text-center mb-4 max-w-xs">{{ errorMessage }}</p>
        <button
          @click="handleRetry"
          class="px-4 py-2 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors"
        >
          重试
        </button>
      </div>

      <!-- 微信二维码容器 - 始终存在于DOM中 -->
      <div
        id="wechat-qr-container"
        class="qr-content flex items-center justify-center relative"
        :class="{ 'opacity-0': loading || error }"
      >
        <!-- 扫码成功遮罩层 -->
        <transition name="scan-success">
          <div v-if="scanSuccess" class="absolute inset-0 z-30 flex items-center justify-center">
            <!-- 高斯模糊背景 -->
            <div class="absolute inset-0 backdrop-blur-md bg-white/30 rounded-lg"></div>

            <!-- 成功图标 -->
            <div class="relative z-10 flex flex-col items-center justify-center">
              <!-- 打勾动画 -->
              <div class="success-checkmark">
                <div class="check-icon"></div>
              </div>

              <!-- 成功文字 -->
              <p class="text-green-600 font-medium text-sm mt-4">扫码成功</p>
              <p class="text-gray-700 text-xs mt-1">正在登录...</p>
            </div>
          </div>
        </transition>
      </div>
    </div>

    <!-- 服务条款和隐私政策 -->
    <div v-if="!loading && !error" class="text-center">
      <div class="text-center space-x-4">
        <button
          @click="showTermsModal = true"
          class="text-sm transition-colors underline"
          style="color: #4F46E5;"
          @mouseover="$event.target.style.color = '#3730A3'"
          @mouseleave="$event.target.style.color = '#4F46E5'"
        >
          服务条款
        </button>
        <span class="text-gray-500">|</span>
        <button
          @click="showPrivacyModal = true"
          class="text-sm transition-colors underline"
          style="color: #4F46E5;"
          @mouseover="$event.target.style.color = '#3730A3'"
          @mouseleave="$event.target.style.color = '#4F46E5'"
        >
          隐私政策
        </button>
      </div>
    </div>



    <!-- 服务条款模态框 -->
    <transition name="modal">
      <div v-if="showTermsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <transition name="modal-content">
          <div v-if="showTermsModal" class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">服务条款</h2>
              <button
                @click="showTermsModal = false"
                class="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6 overflow-y-auto max-h-[60vh]">
              <div class="space-y-4 text-sm text-gray-700 leading-relaxed">
                <h4 class="text-base font-medium text-gray-800 mb-3">用户协议</h4>
                <p class="mb-4">欢迎签署本《用户协议》（以下简称"本协议"）并使用TunshuEdu智能留学选校平台（定义见下文）服务。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">相关定义</h4>
                <ul class="space-y-2 ml-4">
                  <li><strong>TunshuEdu智能留学选校平台：</strong> 指 囤鼠科技（四川）有限公司（以下简称"囤鼠科技"）运营的智能留学选校SAAS产品。</li>
                  <li><strong>TunshuEdu服务提供者：</strong> 指提供TunshuEdu智能留学选校平台互联网信息及软件技术服务的 囤鼠科技（四川）有限公司。</li>
                  <li><strong>用户或您：</strong> 指使用TunshuEdu智能留学选校平台产品或服务的个人。</li>
                </ul>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">协议适用范围</h4>
                <p class="mb-4">本协议由您和囤鼠科技签署，自您确认接受之日起或自您使用TunshuEdu智能留学选校平台之发生之时起生效。除非另有明确规定，平台新增的任何功能、新产品、新服务，均无条件地适用本协议。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">账户与使用说明</h4>
                <p class="mb-4">在您使用TunshuEdu智能留学选校平台服务时，需要您先行进行用户注册和实名认证。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">用户资格说明</h4>
                <p class="mb-4">请确认在您开始注册使用TunshuEdu智能留学选校平台服务前，您已具备中华人民共和国法律法规规定的与您行为相适应的民事行为能力。若您不具备前述与您行为相适应的民事行为能力而进行用户注册，则您及您的监护人应依照法律规定承担因此而导致的一切后果。</p>
              </div>
            </div>

            <!-- 模态框底部 -->
            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                @click="showTermsModal = false"
                class="px-6 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 transition-colors"
              >
                我已阅读
              </button>
            </div>
          </div>
        </transition>
      </div>
    </transition>

    <!-- 隐私政策模态框 -->
    <transition name="modal">
      <div v-if="showPrivacyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <transition name="modal-content">
          <div v-if="showPrivacyModal" class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">隐私政策</h2>
              <button
                @click="showPrivacyModal = false"
                class="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6 overflow-y-auto max-h-[60vh]">
              <div class="space-y-4 text-sm text-gray-700 leading-relaxed">
                <p class="text-gray-600 mb-4">协议发布及生效日期：2025年7月15日起</p>

                <p class="mb-4">以下协议详细介绍了 囤鼠科技（四川）有限公司（以下简称"我们"）会如何通过TunshuEdu智能留学选校平台（以下简称"本产品"）收集、使用、共享、存储和保护您的个人信息，以及您对此所拥有的权利。在您开始使用我们的产品和服务前，请务必仔细先行阅读该隐私政策并理解本政策，请您充分理解和同意后再开始使用。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">用户信息收集</h4>
                <p class="mb-4">为了确保能够向您提供服务，我们仅会收集对于下列服务的个人信息：</p>

                <ul class="list-disc list-inside space-y-2 ml-4">
                  <li><strong>账户信息：</strong> 当您申请使用平台时，需要向我们提供的手机号码及设置的密码等信息，为了确保您能够成功访问您的服务，我们将记录此信息。</li>
                  <li><strong>对话信息：</strong> 为了确保服务的质量和稳定性，在您使用平台服务时，我们将收集您在产品交互过程中输入的内容本身相关信息。</li>
                  <li><strong>反馈信息：</strong> 为了确保服务的质量和稳定性，在您使用平台服务时，我们将收集您对生成的内容和反馈信息。</li>
                  <li><strong>投诉建议信息：</strong> 为了更好的处理您的投诉和建议，我们将需要您在提交投诉建议时所提供的相关信息。</li>
                  <li><strong>设备信息及个人用户标识：</strong> 当您使用我们的服务时，可能会涉及到第三方 SDK 的使用，这些 SDK 可能会请求获取您的设备权限，以便在不同的手机设备配置三方平台上登录和保护功能。</li>
                </ul>
              </div>
            </div>

            <!-- 模态框底部 -->
            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                @click="showPrivacyModal = false"
                class="px-6 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 transition-colors"
              >
                我已阅读
              </button>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { getWeChatQRConfig } from '@/api/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')
const qrConfig = ref(null)
const showTermsModal = ref(false)
const showPrivacyModal = ref(false)
const scanSuccess = ref(false) // 新增：扫码成功状态

// 定义emits
const emit = defineEmits(['login-success'])

// 微信登录JS实例
let wxLoginInstance = null
let retryCount = 0
const maxRetries = 3

// 强制刷新标志
const forceRefresh = ref(false)

// postMessage监听器
let messageListener = null

// 配置缓存时间戳
const configTimestamp = ref(null)

// 检查配置是否过期（5分钟过期）
const isConfigExpired = () => {
  if (!configTimestamp.value) return true
  const now = Date.now()
  const expireTime = 5 * 60 * 1000 // 5分钟
  return (now - configTimestamp.value) > expireTime
}

// 检查是否需要强制刷新
const checkShouldForceRefresh = () => {
  // 检查localStorage中是否有强制刷新标记
  const forceRefreshFlag = localStorage.getItem('wechat_qr_force_refresh')
  if (forceRefreshFlag) {
    localStorage.removeItem('wechat_qr_force_refresh')
    return true
  }

  // 检查配置是否过期
  if (isConfigExpired()) {
    return true
  }

  return false
}



// 初始化微信二维码
const initWeChatQR = async () => {
  try {
    loading.value = true
    error.value = false
    errorMessage.value = ''
    scanSuccess.value = false // 重置扫码成功状态

    // 等待DOM更新，确保容器元素存在
    await nextTick()

    // 检查是否需要强制刷新配置
    const shouldForceRefresh = forceRefresh.value || !qrConfig.value || isConfigExpired()

    // 并行处理：同时获取配置和加载SDK，显著提升速度
    const [config] = await Promise.all([
      // 根据需要获取新配置或使用缓存的配置
      shouldForceRefresh ? getWeChatQRConfig() : qrConfig.value,
      // 并行加载SDK（如果还未加载）
      loadWeChatSDK()
    ])

    // 更新缓存配置和时间戳
    qrConfig.value = config
    configTimestamp.value = Date.now()
    forceRefresh.value = false

    // 检查DOM状态（优化后只检查一次）
    const container = document.getElementById('wechat-qr-container')
    if (!container) {
      throw new Error('二维码容器未找到，请检查DOM结构')
    }

    // 清理之前的实例
    if (wxLoginInstance) {
      container.innerHTML = ''
      wxLoginInstance = null
    }

    // 创建微信登录实例（使用缓存的配置）
    if (window.WxLogin) {
      wxLoginInstance = new window.WxLogin({
        self_redirect: false, // 保持false，避免CSP问题
        id: "wechat-qr-container",
        appid: config.appid,
        scope: config.scope,
        redirect_uri: encodeURIComponent(config.redirect_uri),
        state: config.state,
        style: config.style,
        href: config.href
      })

      // 启动postMessage监听
      startPostMessageListener()

      // 监听登录状态
      startPollingForCallback()

      loading.value = false
      retryCount = 0
    } else {
      throw new Error('微信登录SDK加载失败')
    }
  } catch (err) {
    console.log('[错误] 微信二维码初始化失败')
    loading.value = false
    error.value = true

    // 根据不同的错误类型提供更具体的错误信息
    if (err.message.includes('Network Error') || err.code === 'NETWORK_ERROR') {
      errorMessage.value = '网络连接失败，请检查网络设置'
    } else if (err.response?.status === 500) {
      const detail = err.response?.data?.detail || '服务器内部错误'
      errorMessage.value = `服务器错误：${detail}`
    } else if (err.response?.status === 404) {
      errorMessage.value = 'API接口不存在，请联系技术支持'
    } else if (err.response?.status === 503) {
      errorMessage.value = '服务暂时不可用，请稍后重试'
    } else if (err.message.includes('微信SDK加载失败')) {
      errorMessage.value = '微信服务加载失败，请检查网络连接'
    } else if (err.message.includes('二维码容器')) {
      errorMessage.value = '页面加载异常，请刷新页面重试'
    } else {
      errorMessage.value = err.response?.data?.detail || err.message || '未知错误，请重试'
    }

    // 只在开发环境记录详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.log('[错误] 微信登录详细信息:', {
        message: err.message,
        status: err.response?.status
      })
    }
  }
}

// 预加载微信JS SDK（优化：提前开始加载）
let sdkLoadingPromise = null

const loadWeChatSDK = () => {
  // 如果已经有正在进行的加载，直接返回该Promise
  if (sdkLoadingPromise) {
    return sdkLoadingPromise
  }

  sdkLoadingPromise = new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.WxLogin) {
      resolve()
      return
    }

    // 检查是否正在加载
    const existingScript = document.querySelector('script[src*="wxLogin.js"]')
    if (existingScript) {
      // 等待加载完成，优化检查间隔
      const checkLoaded = () => {
        if (window.WxLogin) {
          resolve()
        } else {
          setTimeout(checkLoaded, 50) // 减少检查间隔
        }
      }
      checkLoaded()
      return
    }

    // 创建script标签，优化加载策略
    const script = document.createElement('script')
    script.src = 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js'
    script.async = true
    script.crossOrigin = 'anonymous' // 添加跨域属性
    
    script.onload = () => {
      if (window.WxLogin) {
        resolve()
      } else {
        reject(new Error('微信SDK加载后未找到WxLogin对象'))
      }
    }
    
    script.onerror = () => {
      sdkLoadingPromise = null // 重置Promise，允许重试
      reject(new Error('微信SDK加载失败，请检查网络连接'))
    }
    
    document.head.appendChild(script)
    
    // 优化超时时间
    setTimeout(() => {
      if (!window.WxLogin) {
        sdkLoadingPromise = null // 重置Promise，允许重试
        reject(new Error('微信SDK加载超时'))
      }
    }, 8000) // 减少超时时间到8秒
  })

  return sdkLoadingPromise
}

// 启动postMessage监听器，处理iframe跨域通信
const startPostMessageListener = () => {
  // 清理之前的监听器
  if (messageListener) {
    window.removeEventListener('message', messageListener)
  }

  messageListener = (event) => {
    try {
      // 检查消息来源
      if (event.origin !== 'https://open.weixin.qq.com') {
        return
      }

      // 尝试解析消息数据
      let data = event.data
      if (typeof data === 'string') {
        try {
          data = JSON.parse(data)
        } catch (e) {
          // 如果不是JSON，检查是否是URL格式的回调
          if (data.includes('code=') && data.includes('state=')) {
            console.log('[微信登录] 收到URL格式的回调:', data)
            const url = new URL(data)
            const code = url.searchParams.get('code')
            const state = url.searchParams.get('state')
            if (code && state) {
              handleCallback(code, state)
            }
            return
          }
          return
        }
      }

      // 处理JSON格式的回调数据
      if (data && data.code && data.state) {
        console.log('[微信登录] 收到JSON格式的回调:', data)
        handleCallback(data.code, data.state)
      }
    } catch (error) {
      console.log('[微信登录] postMessage处理错误:', error)
    }
  }

  window.addEventListener('message', messageListener)
  console.log('[微信登录] postMessage监听器已启动')
}



// 轮询检查回调参数
let pollingTimer = null
let qrRefreshTimer = null

const startPollingForCallback = () => {
  pollingTimer = setInterval(() => {
    checkCallback()
  }, 500) // 减少轮询间隔到500ms，提高响应速度
  
  // SaaS系统优化：二维码自动刷新（微信二维码通常2分钟过期）
  qrRefreshTimer = setTimeout(() => {
    if (!scanSuccess.value && !loading.value && !error.value) {
      console.log('[系统] 二维码即将过期，自动刷新')
      initWeChatQR()
    }
  }, 110000) // 1分50秒后自动刷新，确保二维码始终有效
}

const stopPollingForCallback = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
  
  // 清除二维码刷新定时器
  if (qrRefreshTimer) {
    clearTimeout(qrRefreshTimer)
    qrRefreshTimer = null
  }
}

// 检查URL参数中的回调
const checkCallback = async () => {
  const { code, state } = route.query

  if (code && state) {
    stopPollingForCallback()
    
    try {
      // 先显示扫码成功状态
      scanSuccess.value = true
      
      // 并行处理：显示动画的同时开始登录处理
      const [, response] = await Promise.all([
        // 减少延迟时间，让用户看到扫码成功效果
        new Promise(resolve => setTimeout(resolve, 300)),
        // 同时开始处理微信登录回调
        authStore.handleWeChatCallback(code, state)
      ])
      
      if (response.access_token) {
        // 发出登录成功事件，传递用户数据
        emit('login-success', response.user)
      }
    } catch (error) {
      console.log('[错误] 微信登录回调处理失败')
      
      // SaaS系统优化：更智能的错误处理
      let errorMsg = '登录失败，请重试'
      let needForceRefresh = false

      if (error.response?.status === 401) {
        errorMsg = '授权已过期，正在重新生成二维码'
        needForceRefresh = true
      } else if (error.response?.status === 429) {
        errorMsg = '请求过于频繁，请稍后再试'
      } else if (error.response?.status >= 500) {
        errorMsg = '服务暂时不可用，正在自动重试'
      } else if (error.response?.data?.detail) {
        // 检查是否是state参数相关的错误
        const detail = error.response.data.detail.toLowerCase()
        if (detail.includes('state') || detail.includes('无效') || detail.includes('过期')) {
          needForceRefresh = true
          // 使用统一的过期处理函数
          handleExpiredCallback('二维码已过期')
          // 不使用return，让代码继续执行到finally块
          // 设置一个标志，避免执行后续的错误处理逻辑
          scanSuccess.value = false
          loading.value = false
          return
        }
      }

      ElMessage.error(error.response?.data?.detail || errorMsg)

      // 根据错误类型决定恢复策略
      scanSuccess.value = false
      if (error.response?.status === 401 || needForceRefresh) {
        // 授权过期或state无效，不再尝试重新生成二维码，直接重定向
        if (!needForceRefresh) {
          // 如果不是state过期，而是其他401错误，则重定向到登录页
          setTimeout(() => {
            router.replace('/login').then(() => {
              ElMessage.warning('登录会话已过期，请重新登录')
            })
          }, 1500)
        }
        // 如果是state过期，上面已经处理了重定向
      } else {
        // 其他错误，清除URL参数并重新初始化
        await router.replace({ path: '/login' })
        setTimeout(() => {
          initWeChatQR()
        }, 1000)
      }
    } finally {
      loading.value = false
    }
  }
}

// 检测并处理过期的回调URL
const handleExpiredCallback = (message = '登录状态已过期') => {
  ElMessage.warning(`${message}，正在返回登录页面...`)
  
  // 清除可能的轮询
  stopPollingForCallback()
  
  setTimeout(() => {
    router.replace('/login').then(() => {
      ElMessage.info('请重新扫码登录')
    }).catch(() => {
      // 如果路由跳转失败，尝试页面刷新
      window.location.href = '/login'
    })
  }, 2000)
}

// SaaS系统智能重试机制
const handleRetry = async () => {
  if (retryCount < maxRetries) {
    retryCount++
    
    // 检查网络状态
    if (!navigator.onLine) {
      ElMessage.warning('网络连接已断开，请检查网络后重试')
      return
    }
    
    // 智能延迟：根据重试次数递增延迟时间
    const delayTime = retryCount * 1000 // 1秒、2秒、3秒
    console.log(`[系统] 第${retryCount}次重试，延迟${delayTime}ms`)
    
    setTimeout(() => {
      // 重试时不清除配置缓存，加快重试速度
      initWeChatQR()
    }, delayTime)
  } else {
    ElMessage.error('重试次数过多，请刷新页面或稍后再试')
  }
}



// SaaS系统优化：事件监听器
let handleNetworkChange = null
let handleVisibilityChange = null

// 监听路由变化，当邀请参数变化时重新初始化二维码
watch(
  () => route.query,
  (newQuery, oldQuery) => {
    // 检查是否是邀请相关参数变化
    const invitationParams = ['code', 'org', 'username', 'invite_token', 'token']
    const hasInvitationParamChange = invitationParams.some(param => 
      newQuery[param] !== oldQuery[param]
    )
    
    if (hasInvitationParamChange) {
      console.log('[微信登录] 检测到邀请参数变化，重新初始化二维码')
      // 强制刷新二维码
      forceRefresh.value = true
      qrConfig.value = null
      configTimestamp.value = null
      // 延迟一点时间确保DOM更新完成
      setTimeout(() => {
        initWeChatQR()
      }, 100)
    }
  },
  { deep: true }
)

onMounted(async () => {
  // 添加网络状态监听
  handleNetworkChange = () => {
    if (navigator.onLine && error.value) {
      console.log('[系统] 网络已恢复，自动重试')
      // 网络恢复时自动重试
      setTimeout(() => {
        if (error.value) {
          retryCount = 0 // 重置重试次数
          initWeChatQR()
        }
      }, 1000)
    }
  }
  
  window.addEventListener('online', handleNetworkChange)
  window.addEventListener('offline', () => {
    console.log('[系统] 网络连接已断开')
  })
  
  // SaaS系统优化：页面可见性监听，节省资源
  handleVisibilityChange = () => {
    if (document.hidden) {
      // 页面隐藏时暂停轮询
      if (pollingTimer) {
        clearInterval(pollingTimer)
        pollingTimer = null
        console.log('[系统] 页面隐藏，暂停轮询')
      }
    } else {
      // 页面显示时检查是否需要刷新二维码
      const shouldRefresh = checkShouldForceRefresh()
      if (shouldRefresh) {
        console.log('[系统] 检测到需要刷新二维码')
        forceRefresh.value = true
        qrConfig.value = null
        configTimestamp.value = null
        initWeChatQR()
      } else if (!pollingTimer && !scanSuccess.value && !error.value && !loading.value) {
        // 页面显示时恢复轮询
        console.log('[系统] 页面显示，恢复轮询')
        startPollingForCallback()
      }
    }
  }
  
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 立即开始预加载SDK，不等待结果
  loadWeChatSDK().catch(() => {
    // 预加载失败不影响后续流程，会在initWeChatQR中重试
  })

  // 检查是否需要强制刷新二维码
  const shouldForceRefreshOnMount = checkShouldForceRefresh()
  if (shouldForceRefreshOnMount) {
    forceRefresh.value = true
  }

  // 立即检查是否是回调，提高响应速度
  await checkCallback()

  // 如果是回调URL但处理失败，设置超时重定向
  if (route.query.code && route.query.state) {
    // 设置10秒超时，如果还在回调页面就自动重定向
    setTimeout(() => {
      // 检查是否仍在回调URL且未成功登录
      if (route.path.includes('/auth/wechat/callback') && !authStore.token) {
        console.log('[系统] 回调处理超时，自动重定向到登录页面')
        handleExpiredCallback('登录处理超时')
      }
    }, 10000) // 10秒超时
  }

  // 如果不是回调，快速初始化二维码
  if (!route.query.code) {
    // 确保DOM完全渲染后再初始化
    await nextTick()
    // 减少延迟，加快二维码显示
    setTimeout(() => {
      initWeChatQR()
    }, 50)
  }
})

onUnmounted(() => {
  // 清理资源
  stopPollingForCallback()

  // 清理postMessage监听器
  if (messageListener) {
    window.removeEventListener('message', messageListener)
    messageListener = null
  }

  // 清理网络监听器
  if (handleNetworkChange) {
    window.removeEventListener('online', handleNetworkChange)
  }
  window.removeEventListener('offline', () => {})

  // 清理页面可见性监听器
  if (handleVisibilityChange) {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  }

  if (wxLoginInstance) {
    const container = document.getElementById('wechat-qr-container')
    if (container) {
      container.innerHTML = ''
    }
    wxLoginInstance = null
  }

  console.log('[微信登录] 组件卸载，清理所有资源')
})
</script>

<style scoped>
.wechat-qr-login {
  @apply w-full max-w-sm mx-auto;
}

/* 二维码容器样式 - 去掉边框和背景 */
.qr-container {
  width: 280px;
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.qr-content {
  width: 100%;
  height: 100%;
  min-height: 260px;
}

/* 覆盖微信二维码默认样式 - 去掉所有装饰 */
:deep(.impowerBox) {
  width: 100% !important;
  max-width: 280px !important;
  margin: 0 auto !important;
  text-align: center !important;
  background: transparent !important;
  border: none !important;
}

:deep(.impowerBox .qrcode) {
  width: 260px !important;
  height: 260px !important;
  margin: 0 auto !important;
  border: none !important;
  border-radius: 0 !important;
  overflow: visible !important;
  padding: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
}

:deep(.impowerBox .qrcode img) {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 0 !important;
}

:deep(.impowerBox .title) {
  display: none !important;
}

:deep(.impowerBox .info) {
  width: 260px !important;
  max-width: 260px !important;
  margin: 4px auto 0 auto !important;
  font-size: 12px !important;
  color: #4b5563 !important;
  text-align: center !important;
  line-height: 1.4 !important;
  padding: 0 4px !important;
}

:deep(.impowerBox .status) {
  text-align: center !important;
  font-size: 12px !important;
  color: #4b5563 !important;
  margin-top: 4px !important;
  line-height: 1.3 !important;
  padding: 0 !important;
}

:deep(.status_icon) {
  display: none !important;
}

/* 确保二维码容器内容居中 */
:deep(#wechat-qr-container) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  overflow: visible !important;
}

/* 进一步优化微信SDK生成的元素 */
:deep(.impowerBox iframe) {
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 确保二维码图片无额外样式 */
:deep(.impowerBox .qrcode canvas),
:deep(.impowerBox .qrcode svg) {
  width: 260px !important;
  height: 260px !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

/* 移动端二维码图片优化 */
@media (max-width: 640px) {
  :deep(.impowerBox .qrcode canvas),
  :deep(.impowerBox .qrcode svg) {
    width: 240px !important;
    height: 240px !important;
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .qr-container {
    width: 260px;
    height: 260px;
  }

  :deep(.impowerBox .qrcode) {
    width: 240px !important;
    height: 240px !important;
  }

  :deep(.impowerBox .qrcode img) {
    width: 100% !important;
    height: 100% !important;
  }

  :deep(.impowerBox) {
    max-width: 260px !important;
  }

  :deep(.impowerBox .info) {
    width: 240px !important;
    max-width: 240px !important;
  }

  .qr-content {
    min-height: 240px;
  }

  .success-checkmark {
    width: 60px;
    height: 60px;
  }

  .check-icon {
    width: 60px;
    height: 60px;
  }

  .check-icon::before {
    width: 18px;
    height: 9px;
    border-width: 2px;
    border-top: none;
    border-right: none;
  }
}

/* 加载和错误状态的过渡动画 */
.qr-content {
  transition: opacity 0.3s ease-in-out;
}

/* 扫码成功遮罩层的过渡动画 */
.scan-success-enter-active,
.scan-success-leave-active {
  transition: opacity 0.5s ease-in-out;
}

.scan-success-enter-from,
.scan-success-leave-to {
  opacity: 0;
}

/* 模态框过渡动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

/* 成功图标样式 */
.success-checkmark {
  width: 80px;
  height: 80px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: checkmark-scale 0.6s ease-in-out;
}

.check-icon {
  width: 80px;
  height: 80px;
  position: relative;
  border-radius: 50%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-icon::before {
  content: '';
  position: absolute;
  width: 25px;
  height: 12px;
  border: 3px solid white;
  border-top: none;
  border-right: none;
  transform: rotate(-45deg) translate(-2px, -2px);
  animation: checkmark-draw 0.4s ease-in-out 0.2s both;
}

/* 打勾动画 */
@keyframes checkmark-scale {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkmark-draw {
  0% {
    width: 0;
    height: 0;
  }
  50% {
    width: 25px;
    height: 0;
  }
  100% {
    width: 25px;
    height: 12px;
  }
}

/* 移除旧的打勾样式 */
.line-tip,
.line-long,
.icon-circle,
.icon-fix {
  display: none;
}
</style>