<template>
  <!-- 专业详情横向悬浮窗 -->
  <div
    v-if="visible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click="handleClose"
  >
    <div
      class="bg-white rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 ease-in-out"
      :class="{
        'w-[98vw] max-w-[90rem] max-h-[90vh]': !isFullscreen,
        'w-screen h-screen max-w-none rounded-none': isFullscreen
      }"
      @click.stop
    >
      <!-- 悬浮窗头部 -->
      <div class="relative overflow-hidden">
        <!-- 背景单色层 -->
        <div class="absolute inset-0 bg-[#4F46E5]"></div>
        
        <!-- 装饰性几何图案 -->
        <div class="absolute inset-0 opacity-10">
          <div class="absolute top-0 right-0 w-32 h-32 bg-white rounded-full transform translate-x-16 -translate-y-16"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full transform -translate-x-12 translate-y-12"></div>
          <div class="absolute top-1/2 right-1/4 w-16 h-16 bg-white rounded-full transform -translate-y-1/2"></div>
        </div>
        
        <!-- 主要内容 -->
        <div class="relative z-10 px-6 py-5 flex items-center justify-between">
          <!-- 左侧：图标和标题区域 -->
          <div class="flex items-center space-x-4">
            <!-- 图标容器 -->
            <div class="flex-shrink-0 w-12 h-12 bg-white bg-opacity-20 backdrop-blur-sm rounded-xl flex items-center justify-center border border-white border-opacity-30">
              <span class="material-icons-outlined text-white text-2xl">school</span>
            </div>
            
            <!-- 标题和描述 -->
            <div class="flex flex-col">
              <h2 class="text-xl font-bold text-white leading-tight">专业详情</h2>
              <p class="text-sm text-white text-opacity-90 mt-0.5 leading-relaxed">详细了解专业信息与申请要求</p>
            </div>
          </div>
          
          <!-- 右侧：操作按钮区域 -->
          <div class="flex items-center space-x-3">
            <!-- 全屏按钮 -->
            <button
              @click="toggleFullscreen"
              class="group flex-shrink-0 w-10 h-10 bg-white bg-opacity-0 hover:bg-opacity-20 rounded-lg flex items-center justify-center transition-all duration-200 border border-white border-opacity-0 hover:border-opacity-30"
              title="全屏显示"
            >
              <span class="material-icons-outlined text-white text-lg group-hover:scale-110 transition-transform duration-200">
                {{ isFullscreen ? 'fullscreen_exit' : 'fullscreen' }}
              </span>
            </button>
            
            <!-- 关闭按钮 -->
            <button
              @click="handleClose"
              class="group flex-shrink-0 w-10 h-10 bg-white bg-opacity-0 hover:bg-opacity-20 rounded-lg flex items-center justify-center transition-all duration-200 border border-white border-opacity-0 hover:border-opacity-30"
              title="关闭详情"
            >
              <span class="material-icons-outlined text-white text-lg group-hover:scale-110 transition-transform duration-200">close</span>
            </button>
          </div>
        </div>
        
        <!-- 底部装饰线 -->
        <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white to-transparent opacity-30"></div>
      </div>

      <!-- 悬浮窗内容 -->
      <div v-if="loading" class="flex items-center justify-center h-full">
        <el-skeleton animated>
          <template #template>
            <div class="p-8 space-y-4">
              <el-skeleton-item variant="h1" style="width: 60%;" />
              <el-skeleton-item variant="text" style="width: 80%;" />
              <el-skeleton-item variant="text" style="width: 70%;" />
            </div>
          </template>
        </el-skeleton>
      </div>

      <div v-else-if="program" class="overflow-y-auto p-6 dialog-content-scroll">
        <!-- 重新设计的区域卡片布局 -->
        <div class="space-y-4">
          <!-- 顶部：专业信息和院校logo -->
          <div class="bg-gray-100 rounded-xl p-6">
            <div class="flex items-center">
              <div class="w-16 h-16 bg-white rounded-lg flex items-center justify-center mr-6 shadow-sm">
                <img
                  :src="getSchoolLogo(program)"
                  :alt="program.school_name_cn + ' Logo'"
                  class="w-12 h-12 object-contain"
                  @error="handleLogoError"
                />
              </div>
              <div>
                <div class="flex items-center gap-2 mb-1">
                  <h3 class="text-lg font-bold text-gray-800">{{ program.school_name_cn }} - {{ program.program_name_cn }}</h3>
                  <a
                    v-if="shouldShowField(program.program_website)"
                    :href="program.program_website"
                    target="_blank"
                    rel="noopener noreferrer"
                    @click.stop
                    class="group flex-shrink-0 flex items-center text-sm text-indigo-600 hover:text-indigo-800 transition-colors duration-200"
                  >
                    <span class="material-icons-outlined text-xs mr-0.5">open_in_new</span>
                    <span class="group-hover:underline">查看官网</span>
                  </a>
                </div>
                <p class="text-sm text-gray-600">{{ program.school_name_en }}{{ program.program_name_en ? ' - ' + program.program_name_en : '' }}</p>
                <div class="flex items-center mt-1.5">
                  <span class="material-icons-outlined text-gray-400 text-xs mr-1">location_on</span>
                  <span class="text-sm text-gray-600">{{ program.school_region }}</span>
                  <span v-if="program.school_qs_rank" class="ml-3 px-2 py-0.5 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                    QS {{ program.school_qs_rank }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 主要内容区域：调整为左中右布局，比例3:4:5 -->
          <div class="grid grid-cols-12 gap-4 items-start align-top detail-grid">
            <!-- 左侧列：申请时间和专业目标 -->
            <div ref="leftColumnRef" class="col-span-3 space-y-4 detail-column">
              <!-- 申请时间 -->
              <div class="bg-gray-100 rounded-xl p-4 min-h-[200px]">
                <h4 class="font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-sm mr-2">schedule</span>
                  申请时间
                </h4>
                <div v-if="shouldShowField(program.application_time)" class="space-y-3">
                  <div v-for="(timeItem, index) in parseApplicationTime(program.application_time)" :key="index"
                       class="flex flex-col p-3 bg-white rounded-lg border border-gray-200">
                    <div class="flex items-start justify-between mb-1">
                      <div class="flex-1 pr-2">
                        <span class="text-sm font-medium text-gray-800 break-words leading-relaxed">{{ timeItem.name }}</span>
                      </div>
                      <span :class="getTimeStatusClass(timeItem.status)" class="px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap flex-shrink-0">
                        {{ getTimeStatusText(timeItem.status) }}
                      </span>
                    </div>
                    <div v-if="timeItem.date" class="text-xs text-gray-600">
                      {{ formatTimeDate(timeItem.date) }}
                    </div>
                  </div>
                </div>
                <div v-else class="flex items-center justify-center h-32">
                  <div class="text-sm text-gray-400 text-center">暂无申请时间信息</div>
                </div>
              </div>

              <!-- 专业目标 -->
              <div class="bg-gray-100 rounded-xl p-4">
                <ProgramObjectives
                  :objectives="program.program_objectives"
                  mode="dialog"
                  :in-dialog="true"
                />
              </div>
            </div>

            <!-- 中间列：基本信息和申请要求 -->
            <div ref="middleColumnRef" class="col-span-4 space-y-4 detail-column">
              <!-- 基本信息 -->
              <div class="bg-gray-100 rounded-xl p-4">
                <h4 class="font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-sm mr-2">info</span>
                  基本信息
                </h4>
                <div class="grid grid-cols-2 gap-3 text-sm">
                  <div v-if="shouldShowField(program.program_direction)" class="bg-white p-3 rounded-lg">
                    <span class="text-gray-500 text-xs block mb-1">专业方向</span>
                    <span class="font-medium text-gray-800">{{ formatFieldValue(program.program_direction) }}</span>
                  </div>
                  <div v-if="shouldShowField(program.faculty)" class="bg-white p-3 rounded-lg">
                    <span class="text-gray-500 text-xs block mb-1">所属学院</span>
                    <span class="font-medium text-gray-800">{{ formatFieldValue(program.faculty) }}</span>
                  </div>
                  <div v-if="shouldShowField(program.degree)" class="bg-white p-3 rounded-lg">
                    <span class="text-gray-500 text-xs block mb-1">学位类型</span>
                    <span class="font-medium text-gray-800">{{ formatFieldValue(program.degree) }}</span>
                  </div>
                  <div v-if="shouldShowField(program.program_duration)" class="bg-white p-3 rounded-lg">
                    <span class="text-gray-500 text-xs block mb-1">项目时长</span>
                    <span class="font-medium text-gray-800">{{ formatFieldValue(program.program_duration) }}</span>
                  </div>
                  <div v-if="shouldShowField(program.program_tuition)" class="bg-white p-3 rounded-lg">
                    <span class="text-gray-500 text-xs block mb-1">学费</span>
                    <span class="font-medium text-gray-800">{{ formatFieldValue(program.program_tuition) }}</span>
                  </div>
                  <div v-if="shouldShowField(program.program_intake)" class="bg-white p-3 rounded-lg">
                    <span class="text-gray-500 text-xs block mb-1">入学时间</span>
                    <span class="font-medium text-gray-800">{{ formatFieldValue(program.program_intake) }}</span>
                  </div>
                </div>
              </div>

              <!-- 申请要求 -->
              <div class="bg-gray-100 rounded-xl p-4">
                <h4 class="font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-sm mr-2">assignment</span>
                  申请要求
                </h4>
                <div v-if="shouldShowField(program.gpa_requirements) || shouldShowField(program.language_requirements) || shouldShowField(program.application_requirements)"
                     class="bg-white p-4 rounded-lg">
                  <div class="space-y-3 text-sm">
                    <div v-if="shouldShowField(program.gpa_requirements)">
                      <span class="text-gray-500 block mb-1">GPA要求</span>
                      <div class="text-gray-800">{{ formatFieldValue(program.gpa_requirements) }}</div>
                    </div>
                    <div v-if="shouldShowField(program.language_requirements)">
                      <span class="text-gray-500 block mb-1">语言要求</span>
                      <LanguageRequirements
                        :language-requirements="program.language_requirements"
                        :in-dialog="true"
                      />
                    </div>
                    <div v-if="shouldShowField(program.application_requirements)">
                      <span class="text-gray-500 block mb-1">其他要求</span>
                      <div class="text-gray-800 whitespace-pre-line leading-relaxed">{{ formatHtmlContent(program.application_requirements) }}</div>
                    </div>
                  </div>
                </div>
                <div v-else class="bg-white p-4 rounded-lg">
                  <div class="text-sm text-gray-400 text-center py-4">暂无申请要求信息</div>
                </div>
              </div>
            </div>

            <!-- 右侧列：课程设置 -->
            <div ref="courseSettingsRef" class="col-span-5 bg-gray-100 rounded-xl p-4 flex flex-col course-settings-card">
              <h4 class="font-semibold text-gray-800 mb-4 flex items-center flex-shrink-0">
                <span class="material-icons-outlined text-sm mr-2">menu_book</span>
                课程设置
              </h4>
              <div class="flex-1 course-content-container">
                <div v-if="shouldShowField(program.courses)" class="text-sm course-structure-wrapper">
                  <CourseStructure
                    :course-structure="program.courses"
                    :in-dialog="true"
                    :dynamic-height="dynamicHeight"
                    @category-changed="handleCategoryChanged"
                  />
                </div>
                <div v-else class="flex items-center justify-center min-h-[200px]">
                  <div class="text-sm text-gray-400 text-center">暂无课程设置信息</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import LanguageRequirements from '@/components/common/LanguageRequirements.vue'
import CourseStructure from '@/components/common/CourseStructure.vue'
import ProgramObjectives from '@/components/common/ProgramObjectives.vue'
import { getSchoolLogo as getSchoolLogoFallback } from '@/utils/schoolLogos'
import { useSchoolLogosStore } from '@/stores/schoolLogos'
import {
  shouldShowField,
  formatFieldValue,
  formatHtmlContent,
  formatApplicationTime,
  calculateDynamicHeight,
  createResizeObserver,
  debounce
} from '@/utils/programUtils'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  program: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'close'])

// 状态
const isFullscreen = ref(false)
const dynamicHeight = ref(300)

// DOM引用
const leftColumnRef = ref(null)
const middleColumnRef = ref(null)
const courseSettingsRef = ref(null)

// 使用全局logo store
const logoStore = useSchoolLogosStore()

// ResizeObserver实例
let resizeObserver = null



/**
 * 获取学校Logo的方法（使用全局logo store）
 */
const getSchoolLogo = (program) => {
  // 如果传入的是字符串，转换为对象格式
  if (typeof program === 'string') {
    program = { school_name_cn: program };
  }

  const schoolNameCn = program.school_name_cn;

  // 方案1: 优先使用传入对象中的school_logo_url字段（第一优先级）
  if (program.school_logo_url && program.school_logo_url.trim() !== '') {
    return program.school_logo_url;
  }

  // 方案2: 使用全局logo store（第二优先级）
  return logoStore.getSchoolLogo(schoolNameCn);
};

// 处理logo加载错误
const handleLogoError = (event) => {
  // 当logo加载失败时，替换为默认logo
  const schoolNameCn = props.program?.school_name_cn || '';
  event.target.src = logoStore.getSchoolLogoFallback(schoolNameCn);
}

// 解析申请时间字符串
const parseApplicationTime = (timeStr) => {
  if (!timeStr) return []

  // 清理HTML标签
  const cleanText = timeStr
    .replace(/<br\s*\/?>/gi, ' ')
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/\s+/g, ' ')
    .trim()

  const timeItems = []

  try {
    // 处理多种分隔符格式
    let parts = []

    if (cleanText.includes('|')) {
      parts = cleanText.split('|').map(part => part.trim())
    } else if (cleanText.includes(';')) {
      parts = cleanText.split(';').map(part => part.trim())
    } else if (cleanText.includes('\n')) {
      parts = cleanText.split('\n').map(part => part.trim()).filter(part => part)
    } else {
      // 尝试匹配时间节点模式
      const timeNodePattern = /([^()]+)\(([^)]+)\)/g
      let match
      while ((match = timeNodePattern.exec(cleanText)) !== null) {
        parts.push(match[0])
      }

      if (parts.length === 0) {
        parts = [cleanText]
      }
    }

    for (const part of parts) {
      if (!part.trim()) continue

      let name = ''
      let dateStr = ''

      // 匹配格式：节点名称(日期)
      let match = part.match(/^(.+?)\s*\(([^)]+)\)\s*$/)
      if (match) {
        name = match[1].trim()
        dateStr = match[2].trim()
      } else {
        // 匹配格式：节点名称: 日期
        match = part.match(/^(.+?):\s*(.+)$/)
        if (match) {
          name = match[1].trim()
          dateStr = match[2].trim()
        } else {
          name = part.trim()
          dateStr = ''
        }
      }

      // 解析日期
      let date = null
      if (dateStr) {
        date = parseTimeDate(dateStr)
      }

      // 确定状态
      const status = determineTimeStatus(name, date)

      timeItems.push({
        name,
        date,
        status,
        originalText: part
      })
    }
  } catch (error) {
    console.warn('解析申请时间失败:', error)
    return [{
      name: cleanText,
      date: null,
      status: 'unknown',
      originalText: cleanText
    }]
  }

  return timeItems.filter(item => item.name)
}

// 解析日期字符串
const parseTimeDate = (dateStr) => {
  if (!dateStr) return null

  try {
    const cleanDateStr = dateStr.trim()

    // 格式：YYYY-MM-DD
    if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(cleanDateStr)) {
      return new Date(cleanDateStr)
    }

    // 格式：YYYY/MM/DD
    if (/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(cleanDateStr)) {
      return new Date(cleanDateStr.replace(/\//g, '-'))
    }

    // 其他格式尝试直接解析
    const parsed = new Date(cleanDateStr)
    return isNaN(parsed.getTime()) ? null : parsed
  } catch (error) {
    return null
  }
}

// 确定时间状态
const determineTimeStatus = (name, date) => {
  const now = new Date()
  const nameStr = name.toLowerCase()

  if (!date) {
    if (nameStr.includes('开放') || nameStr.includes('开始') || nameStr.includes('open')) {
      return 'active'
    }
    if (nameStr.includes('截止') || nameStr.includes('结束') || nameStr.includes('deadline') || nameStr.includes('close')) {
      return 'pending'
    }
    return 'unknown'
  }

  if (date < now) {
    if (nameStr.includes('截止') || nameStr.includes('结束') || nameStr.includes('deadline') || nameStr.includes('close')) {
      return 'expired'
    } else {
      return 'completed'
    }
  } else {
    if (nameStr.includes('开放') || nameStr.includes('开始') || nameStr.includes('open')) {
      return 'pending'
    } else {
      return 'active'
    }
  }
}

// 格式化时间显示
const formatTimeDate = (date) => {
  if (!date) return ''

  try {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    const currentYear = new Date().getFullYear()
    if (year === currentYear) {
      return `${month}-${day}`
    }

    return `${year}-${month}-${day}`
  } catch (error) {
    return ''
  }
}

// 获取时间状态样式类
const getTimeStatusClass = (status) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-700'
    case 'active':
      return 'bg-blue-100 text-blue-700'
    case 'pending':
      return 'bg-yellow-100 text-yellow-700'
    case 'expired':
      return 'bg-red-100 text-red-700'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

// 获取时间状态文本
const getTimeStatusText = (status) => {
  switch (status) {
    case 'completed':
      return '已开始'
    case 'active':
      return '进行中'
    case 'pending':
      return '待开始'
    case 'expired':
      return '已截止'
    default:
      return '未知'
  }
}

// 使用防抖函数来优化高度计算
const debouncedCalculateHeight = debounce(async () => {
  if (leftColumnRef.value && middleColumnRef.value) {
    try {
      const height = await calculateDynamicHeight(leftColumnRef.value, middleColumnRef.value, {
        minHeight: 450, // 确保能显示6个课程项目
        maxHeightRatio: 0.6,
        gridWidth: 800,
        delay: 50
      })
      dynamicHeight.value = height
      
      // 调试信息（仅在开发环境输出）
      if (process.env.NODE_ENV === 'development') {
        console.log('📏 动态高度计算完成:', height + 'px')
      }
    } catch (error) {
      console.warn('计算动态高度失败:', error)
      dynamicHeight.value = 450
    }
  }
}, 150)

// 处理课程分类变化
const handleCategoryChanged = () => {
  // 分类变化时重新计算高度，使用防抖函数
  debouncedCalculateHeight()
}

// 方法
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
  
  // 清理ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
  
  // 退出全屏状态
  if (isFullscreen.value) {
    isFullscreen.value = false
  }
  
  // 重置动态高度为更合理的初始值
  setTimeout(() => {
    dynamicHeight.value = 450
  }, 300)
}

const handleDialogUpdate = (value) => {
  emit('update:visible', value)
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  // 全屏切换后重新计算高度
  nextTick(() => {
    debouncedCalculateHeight()
  })
}

// 监听props变化
const setupResizeObserver = () => {
  if (props.visible && leftColumnRef.value && middleColumnRef.value && !resizeObserver) {
    resizeObserver = createResizeObserver(
      [leftColumnRef.value, middleColumnRef.value],
      () => {
        debouncedCalculateHeight()
      }
    )
  }
}

// 监听visible变化
const handleVisibilityChange = () => {
  if (props.visible && props.program) {
    // 设置一个更合理的初始高度，确保能显示6个课程项目
    // 根据CourseStructure组件的计算逻辑，6个课程项目大约需要400-450px
    dynamicHeight.value = 450

    // 学校logo统一由列表批量预加载，这里不再触发单项请求

    // 等待DOM完全渲染后再计算高度
    nextTick(() => {
      nextTick(() => {
        // 延迟确保所有内容都已渲染完成
        setTimeout(() => {
          debouncedCalculateHeight()
          setupResizeObserver()
        }, 200)
      })
    })
  }
}

// 组件挂载时的初始化
onMounted(() => {
  if (props.visible) {
    handleVisibilityChange()
  }
})

// 组件卸载时的清理
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
})

// 监听props变化
computed(() => {
  if (props.visible && props.program) {
    nextTick(() => {
      handleVisibilityChange()
    })
  }
})
</script>

<style scoped>
/* 悬浮窗动画 */
.fixed.inset-0 {
  animation: fadeIn 0.3s ease-out;
}

.bg-white.rounded-2xl {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 悬浮窗内容滚动区域样式 */
.dialog-content-scroll {
  max-height: calc(90vh - 120px);
  /* 预留滚动条空间，避免内容增多时宽度抖动 */
  scrollbar-gutter: stable;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
}

.dialog-content-scroll::-webkit-scrollbar {
  width: 8px;
}

.dialog-content-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.dialog-content-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.dialog-content-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 详情弹窗Grid布局优化 */
.detail-grid {
  align-items: start;
  grid-auto-rows: min-content;
}

.detail-column {
  height: fit-content;
  align-self: start;
}

/* 课程设置卡片样式 */
.course-settings-card {
  min-height: 300px;
}

.course-content-container {
  min-height: 200px;
}

.course-structure-wrapper {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .bg-white.rounded-2xl {
    width: 100vw !important;
    height: 100vh !important;
    border-radius: 0 !important;
  }

  .grid.grid-cols-12 {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .col-span-12,
  .col-span-3,
  .col-span-4,
  .col-span-5 {
    grid-column: span 1 !important;
  }

  .grid.grid-cols-2 {
    grid-template-columns: 1fr !important;
  }
}
</style> 