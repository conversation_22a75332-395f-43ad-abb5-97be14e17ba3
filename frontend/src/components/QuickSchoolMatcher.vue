<template>
  <div class="quick-school-matcher">
    <div class="text-center py-8">
      <div class="text-gray-400 mb-4">
        <span class="material-icons-outlined text-4xl">school</span>
      </div>
      <h5 class="text-base font-medium text-gray-700 mb-2">快速添加专业</h5>
      <p class="text-sm text-gray-500 mb-4">前往选校匹配页面添加目标专业</p>
      <el-button 
        type="primary" 
        @click="goToSchoolAssistant"
        class="px-6"
      >
        <span class="material-icons-outlined text-sm mr-1">add</span>
        前往选校匹配
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToSchoolAssistant = () => {
  // 跳转到选校匹配页面
  router.push('/school-assistant')
}
</script>

<style scoped>
.quick-school-matcher {
  padding: 1rem;
}
</style>
