<template>
  <header class="bg-white shadow-sm h-16 flex items-center justify-between px-6 border-b border-gray-200">
    <!-- 左侧：侧边栏收缩按钮 -->
    <div class="flex items-center">
      <button
        @click="sidebarStore.toggleCollapse"
        class="p-2 rounded-lg text-gray-600 hover:text-primary hover:bg-gray-50 transition-all duration-200"
        title="收缩/展开侧边栏"
      >
        <span class="material-icons-outlined text-[20px]">
          {{ sidebarStore.isCollapsed ? 'menu_open' : 'menu' }}
        </span>
      </button>
    </div>

    <!-- 右侧：通知和用户信息 -->
    <div class="flex items-center space-x-3">
      <!-- 通知铃铛 -->
      <NotificationBell />

      <!-- 组织名称 + 用户名称 -->
      <div class="text-gray-600 hidden md:flex items-center header-user-info">
        <!-- 统一显示：组织名称|用户名称 或 仅用户名称 -->
        <div class="user-identity-display">
          <span v-if="currentIdentity?.identity_type === 'organization'" class="text-blue-600 font-medium">
            {{ currentIdentity.organization_name }}
          </span>
          <span v-if="currentIdentity?.identity_type === 'organization'" class="text-gray-400">|</span>
          <span class="text-gray-600 font-medium">{{ displayName }}</span>
        </div>
      </div>
      <el-dropdown trigger="click">
        <div class="avatar-wrapper flex items-center cursor-pointer transition-all hover:opacity-80 rounded-lg p-1">
          <UserAvatar
            :avatar-url="authStore.user?.avatar_url"
            :display-name="displayName"
            size="medium"
            class="shadow-sm"
          />
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- 用户信息区域 -->
            <div class="px-4 py-3 border-b border-gray-100">
              <div class="flex items-center space-x-3">
                <UserAvatar
                  :avatar-url="authStore.user?.avatar_url"
                  :display-name="displayName"
                  size="medium"
                  class="flex-shrink-0"
                />
                <div class="flex-1 min-w-0 overflow-hidden">
                  <p class="text-sm font-medium text-gray-900 truncate" :title="displayName">{{ displayName }}</p>
                  <p class="text-xs text-gray-500 mt-0.5 truncate" :title="`id：${displayUserId}`">id：{{ displayUserId }}</p>
                </div>
              </div>
            </div>
            
            <!-- 切换角色选项 -->
            <el-dropdown-item @click="handleShowIdentityDialog" class="h-10">
              <div class="flex items-center h-full">
                <span class="material-icons-outlined text-sm mr-2">
                  {{ currentIdentity?.identity_type === 'personal' ? 'person' : 'business' }}
                </span>
                切换角色
              </div>
            </el-dropdown-item>
            <el-dropdown-item @click="handleProfile" class="h-10">
              <div class="flex items-center h-full">
                <span class="material-icons-outlined text-sm mr-2">
                  {{ currentIdentity?.identity_type === 'organization' ? 'business' : 'person' }}
                </span>
                {{ currentIdentity?.identity_type === 'organization' ? '组织资料' : '个人资料' }}
              </div>
            </el-dropdown-item>
            <el-dropdown-item @click="handleOrderQuery" class="h-10">
              <div class="flex items-center h-full">
                <span class="material-icons-outlined text-sm mr-2">receipt_long</span>
                订单查询
              </div>
            </el-dropdown-item>
            <el-dropdown-item @click="handleLogout" class="h-10">
              <div class="flex items-center h-full">
                <span class="material-icons-outlined text-sm mr-2">logout</span>
                退出登录
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 身份切换对话框 -->
    <el-dialog
      v-model="showIdentityDialog"
      title="选择工作身份"
      width="500px"
      class="simple-dialog compact-dialog"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      @close="showIdentityDialog = false"
    >
      <div class="simple-content">
        <div class="mb-6">
          <p class="text-sm text-slate-500 leading-6 text-center mb-6 font-medium">
            选择您要使用的工作身份，不同身份下的数据和权限完全独立
          </p>
          
          <!-- 身份选择卡片 -->
          <div class="space-y-3">
          
          <!-- 个人身份选项 -->
          <div 
            @click="handleSelectIdentity({ identity_type: 'personal' })"
            :class="[
              'group relative bg-white rounded-2xl p-5 border-2 cursor-pointer transition-all duration-300',
              'hover:shadow-lg hover:border-indigo-300 hover:-translate-y-0.5',
              isSelectedIdentity('personal') 
                ? 'border-indigo-500 bg-gradient-to-r from-indigo-50 to-blue-50 shadow-md' 
                : 'border-gray-200'
            ]"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div :class="[
                  'w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300',
                  isSelectedIdentity('personal') 
                    ? 'bg-indigo-500 shadow-lg shadow-indigo-200' 
                    : 'bg-gray-400 group-hover:bg-indigo-400'
                ]">
                  <span class="material-icons-outlined text-white text-lg">person</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 mb-1">个人身份</h3>
                  <p class="text-sm text-gray-600">
                    独立的个人工作空间，数据隔离
                  </p>
                </div>
              </div>
              <div class="flex-shrink-0">
                <div v-if="isSelectedIdentity('personal')" 
                     class="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                  <span class="material-icons-outlined text-white text-sm">check</span>
                </div>
                <div v-else class="w-6 h-6 border-2 border-gray-300 rounded-full group-hover:border-indigo-400 transition-colors duration-300"></div>
              </div>
            </div>
          </div>
          
          <!-- 组织身份选项 -->
          <div 
            v-for="orgIdentity in authStore.availableIdentities?.filter(i => i.identity_type === 'organization')"
            :key="orgIdentity.organization_id"
            @click="handleSelectIdentity({ 
              identity_type: 'organization', 
              organization_id: orgIdentity.organization_id 
            })"
            :class="[
              'group relative bg-white rounded-2xl p-5 border-2 cursor-pointer transition-all duration-300',
              'hover:shadow-lg hover:border-indigo-300 hover:-translate-y-0.5',
              isSelectedIdentity('organization', orgIdentity.organization_id)
                ? 'border-indigo-500 bg-gradient-to-r from-indigo-50 to-blue-50 shadow-md' 
                : 'border-gray-200'
            ]"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div :class="[
                  'w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300',
                  isSelectedIdentity('organization', orgIdentity.organization_id)
                    ? 'bg-indigo-500 shadow-lg shadow-indigo-200' 
                    : 'bg-gray-400 group-hover:bg-indigo-400'
                ]">
                  <span class="material-icons-outlined text-white text-lg">business</span>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="flex items-center gap-3 mb-1">
                    <h3 class="text-lg font-semibold text-gray-900 truncate">{{ orgIdentity.organization_name }}</h3>
                    <span :class="[
                      'px-2.5 py-1 text-xs font-medium rounded-full flex-shrink-0 border',
                      orgIdentity.organization_role === 'owner' 
                        ? 'bg-amber-100 text-amber-700 border-amber-200' 
                        : 'bg-blue-100 text-blue-700 border-blue-200'
                    ]">
                      {{ orgIdentity.organization_role === 'owner' ? '管理员' : '成员' }}
                    </span>
                  </div>
                  <p class="text-sm text-gray-600">
                    组织成员身份，享受团队协作功能
                  </p>
                </div>
              </div>
              <div class="flex-shrink-0">
                <div v-if="isSelectedIdentity('organization', orgIdentity.organization_id)" 
                     class="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                  <span class="material-icons-outlined text-white text-sm">check</span>
                </div>
                <div v-else class="w-6 h-6 border-2 border-gray-300 rounded-full group-hover:border-indigo-400 transition-colors duration-300"></div>
              </div>
            </div>
          </div>
          
          <!-- 无组织身份时的提示 -->
          <div 
            v-if="!authStore.availableIdentities?.some(i => i.identity_type === 'organization')"
            class="mt-3 px-4 py-6 border border-dashed border-gray-200 rounded-xl bg-gradient-to-br from-slate-50 to-gray-50"
          >
            <div class="text-center py-6">
              <el-icon size="32" color="#9CA3AF" class="mb-2">
                <OfficeBuilding />
              </el-icon>
              <p class="text-sm text-gray-500 mb-2">暂无组织身份</p>
              <p class="text-xs text-gray-400">创建或加入组织后即可使用组织身份</p>
            </div>
          </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-center">
          <el-button 
            type="primary" 
            @click="handleConfirmIdentitySelection"
            size="default"
            class="confirm-button"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </header>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSidebarStore } from '@/stores/sidebar'
import { ElMessage } from 'element-plus'
import UserAvatar from '@/components/common/UserAvatar.vue'
import NotificationBell from '@/components/common/NotificationBell.vue'
import { mapUserIdForDisplay } from '@/utils/userIdMapper'
import { User, OfficeBuilding, Check } from '@element-plus/icons-vue'
import { ref } from 'vue'

const router = useRouter()
const authStore = useAuthStore()
const sidebarStore = useSidebarStore()

// 身份切换对话框状态
const showIdentityDialog = ref(false)
// 选中的待切换身份（先选择，后确认）
const selectedIdentity = ref(null)

// 用户显示名称
const displayName = computed(() => {
  return authStore.user?.nickname || authStore.user?.username || '用户'
})

// 计算显示的用户ID
const displayUserId = computed(() => {
  return mapUserIdForDisplay(authStore.user?.id || authStore.user?.username || 'unknown')
})

// 当前身份信息
const currentIdentity = computed(() => authStore.currentIdentity)

// 获取当前身份显示名称
const getCurrentIdentityName = () => {
  if (!currentIdentity.value) return '未选择身份'
  
  if (currentIdentity.value.identity_type === 'personal') {
    return displayName.value
  } else {
    return currentIdentity.value.organization_name || '组织身份'
  }
}

// 获取当前身份类型显示文本
const getCurrentIdentityType = () => {
  if (!currentIdentity.value) return ''
  
  if (currentIdentity.value.identity_type === 'personal') {
    return '个人身份'
  } else {
    const role = currentIdentity.value.organization_role
    const roleText = role === 'owner' ? '创建者' : '成员'
    return `组织身份 (${roleText})`
  }
}

// 处理订单查询
const handleOrderQuery = () => {
  router.push('/account/orders')
}

// 处理个人资料
const handleProfile = () => {
  router.push('/account/profile')
}

// 处理显示身份切换对话框
const handleShowIdentityDialog = () => {
  // 打开时默认选中当前身份
  if (currentIdentity.value?.identity_type === 'organization') {
    selectedIdentity.value = {
      identity_type: 'organization',
      organization_id: currentIdentity.value.organization_id
    }
  } else {
    selectedIdentity.value = { identity_type: 'personal' }
  }
  showIdentityDialog.value = true
}

// 先选择，再确认切换
const handleSelectIdentity = (identity) => {
  selectedIdentity.value = identity
}

const isSelectedIdentity = (type, organizationId = null) => {
  if (!selectedIdentity.value) return false
  if (type === 'personal') return selectedIdentity.value.identity_type === 'personal'
  return selectedIdentity.value.identity_type === 'organization' && selectedIdentity.value.organization_id === organizationId
}

const handleConfirmIdentitySelection = async () => {
  if (!selectedIdentity.value) return
  try {
    await authStore.switchUserIdentity(selectedIdentity.value)
    showIdentityDialog.value = false
    ElMessage.success('身份切换成功')
    // 角色切换成功后，自动跳转到总览界面
    router.push('/dashboard')
  } catch (error) {
    console.error('身份切换失败:', error)
    ElMessage.error('身份切换失败')
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    authStore.logout()
    // ElMessage.success('退出成功')
  } catch (error) {
    console.error('Logout failed:', error)
    // ElMessage.error('退出失败')
  }
}
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 移除原有的头像样式，现在使用UserAvatar组件 */

/* 头像样式 - 移除焦点框 */
.avatar-wrapper:focus,
.avatar-wrapper:active,
:deep(.avatar-wrapper:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* 下拉菜单样式优化 */
:deep(.el-dropdown-menu) {
  min-width: 320px !important;
  width: 320px !important;
  padding: 4px 0;
}

/* 更具体的选择器确保样式生效 */
:deep(.el-popper .el-dropdown-menu) {
  min-width: 320px !important;
  width: 320px !important;
}

/* 针对Element Plus的弹出层容器 */
:deep(.el-dropdown__popper .el-dropdown-menu) {
  min-width: 320px !important;
  width: 320px !important;
}

:deep(.el-dropdown-menu__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 12px;
  margin: 0;
}

:deep(.el-dropdown-menu__item:hover),
:deep(.el-dropdown-menu__item:focus),
:deep(.el-dropdown-menu__item:active) {
  color: #6366f1 !important; /* 统一的紫色 */
  background-color: rgba(99, 102, 241, 0.05) !important; /* 极淡的紫色背景 */
  outline: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

:deep(.el-dropdown-menu__item .material-icons-outlined) {
  font-size: 18px;
  color: var(--el-text-color-regular);
  margin-right: 8px;
}

:deep(.el-dropdown-menu__item:hover .material-icons-outlined),
:deep(.el-dropdown-menu__item:focus .material-icons-outlined),
:deep(.el-dropdown-menu__item:active .material-icons-outlined) {
  color: #6366f1 !important; /* 与悬停文字相同的紫色 */
}

/* 覆盖Element Plus的默认蓝色焦点和激活状态 */
:deep(.el-dropdown-menu__item.is-disabled:hover),
:deep(.el-dropdown-menu__item.is-disabled:focus) {
  background-color: transparent !important;
  color: var(--el-text-color-disabled) !important;
}

:deep(.el-popper) {
  --el-dropdown-menuItem-hover-fill: rgba(99, 102, 241, 0.05) !important;
  --el-dropdown-menuItem-hover-color: #6366f1 !important;
}

:deep(.el-dropdown) {
  outline: none !important;
}

:deep(.el-dropdown__popper) {
  outline: none !important;
}

:deep(.el-dropdown-link),
:deep(.el-dropdown-link:focus),
:deep(.el-dropdown-link:active) {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* 简洁对话框样式 - 遵循设计标准 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 36px !important;
    padding: 0 20px !important;
    font-size: 14px !important;
    line-height: 1 !important;
    min-width: 80px !important;
  }

  /* 确认按钮特殊样式 - 更长的宽度 */
  .el-button.confirm-button {
    min-width: 120px !important;
    padding: 0 32px !important;
  }

  .el-button--primary:hover,
  .el-button[type="primary"]:hover {
    background-color: #4338CA !important;
    border-color: #4338CA !important;
    color: #FFFFFF !important;
  }

  .el-button--primary:active,
  .el-button[type="primary"]:active {
    background-color: #3730A3 !important;
    border-color: #3730A3 !important;
    color: #FFFFFF !important;
  }

  .el-button--primary:focus,
  .el-button[type="primary"]:focus {
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
  }

  /* 次要按钮样式 */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
    --el-button-active-text-color: #4F46E5 !important;
    --el-button-active-border-color: #4F46E5 !important;
    --el-button-active-bg-color: #F3F4F6 !important;
  }

  .el-button:not(.el-button--primary):hover {
    color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    background-color: #F8FAFF !important;
  }

  .el-button:not(.el-button--primary):active {
    color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    background-color: #F3F4F6 !important;
  }

  .el-button:not(.el-button--primary):focus {
    color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
  }
}

/* 表头用户信息样式优化 */
.header-user-info {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 1.25;
}

.header-user-info span {
  white-space: nowrap;
  vertical-align: middle;
}

/* 确保组织名称和用户名称在同一行且字体大小一致 */
.user-identity-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 16px;
  line-height: 24px;
}

.user-identity-display span {
  font-size: 16px;
  line-height: 24px;
  vertical-align: baseline;
}
</style>