<template>
  <aside
    class="h-full bg-white border-r border-gray-100 shadow-sm flex flex-col transition-all duration-300 ease-in-out"
    :class="sidebarStore.isCollapsed ? 'w-16' : 'w-56'"
  >
    <!-- Logo区域 -->
    <div class="h-16 flex items-center justify-center border-b border-gray-100 transition-all duration-300"
         :class="sidebarStore.isCollapsed ? 'px-2' : 'px-6'">
      <div class="flex items-center">
        <!-- 根据收缩状态显示不同尺寸的logo，统一使用企业logo或默认logo -->
        <img 
          :src="logoUrl" 
          :alt="logoAlt" 
          :class="sidebarStore.isCollapsed ? 'w-14 h-14' : 'h-14'"
          class="object-contain transition-all duration-300" 
        />
      </div>
    </div>



    <!-- 导航菜单 -->
    <nav class="flex-1 overflow-y-auto scrollbar-thin transition-all duration-300"
         :class="sidebarStore.isCollapsed ? 'p-2' : 'p-4'">

      <!-- 视图模式切换按钮 - 只有admin和dev用户可见 -->
      <div v-if="canSwitchViewMode" :class="sidebarStore.isCollapsed ? 'mb-2' : 'mb-4'">
        <!-- 收缩状态：显示简化的切换按钮 -->
        <div v-if="sidebarStore.isCollapsed" class="flex justify-center">
          <button @click="toggleViewMode"
                  class="w-8 h-8 flex items-center justify-center rounded-lg transition-colors duration-200"
                  :class="viewModeButtonClass"
                  :title="viewModeTooltip">
            <span class="material-icons-outlined text-[16px]">
              {{ viewModeIcon }}
            </span>
          </button>
        </div>

        <!-- 展开状态：显示完整的切换选择器 -->
        <div v-else class="bg-gray-100 rounded-full p-1 flex">
          <!-- 上线版选项 -->
          <button @click="setViewMode('production')"
                  class="flex-1 px-3 py-2 text-sm font-medium rounded-full transition-all duration-200"
                  :class="isProductionMode ?
                    'bg-blue-500 text-white shadow-sm' :
                    'text-gray-600 hover:text-gray-800'">
            上线版
          </button>

          <!-- 开发版选项 -->
          <button @click="setViewMode('dev')"
                  class="flex-1 px-3 py-2 text-sm font-medium rounded-full transition-all duration-200"
                  :class="isDevMode ?
                    'bg-blue-500 text-white shadow-sm' :
                    'text-gray-600 hover:text-gray-800'">
            开发版
          </button>
        </div>
      </div>

      <!-- 通用功能 -->
      <div :class="sidebarStore.isCollapsed ? 'mb-2' : 'mb-6'">
        <p v-if="!sidebarStore.isCollapsed" class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider sidebar-text-animate" style="animation-delay: 30ms">通用</p>
        <div class="space-y-0.5">
          <router-link to="/dashboard"
                       class="sidebar-link"
                       :class="{ 'sidebar-link-active': $route.path === '/dashboard' }"
                       :title="sidebarStore.isCollapsed ? '总览' : ''"
                       @click="handleLinkClick">
            <span class="material-icons-outlined text-[20px]">dashboard</span>
            <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 60ms">总览</span>
          </router-link>




        </div>
        <!-- 收缩状态下的分隔线 -->
        <div v-if="sidebarStore.isCollapsed" class="flex justify-center mt-2 mb-1">
          <div class="w-8 h-px bg-gray-300"></div>
        </div>
      </div>

      <!-- 定校规划 -->
      <div :class="sidebarStore.isCollapsed ? 'mb-2' : 'mb-6'">
        <p v-if="!sidebarStore.isCollapsed" class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider sidebar-text-animate" style="animation-delay: 120ms">定校规划</p>
        <div class="space-y-0.5">
          <a href="#"
             class="sidebar-link"
             :class="{ 'sidebar-link-active': $route.path === '/school-assistant' }"
             :title="sidebarStore.isCollapsed ? '选校匹配' : ''"
             @click.prevent="handleSchoolAssistantClick">
            <span class="material-icons-outlined text-[20px]">school</span>
            <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 150ms">选校匹配</span>
          </a>
        </div>
        <!-- 收缩状态下的分隔线 -->
        <div v-if="sidebarStore.isCollapsed" class="flex justify-center mt-2 mb-1">
          <div class="w-8 h-px bg-gray-300"></div>
        </div>
      </div>



      <!-- 文书写作 - 仅完整版显示 -->
      <div v-if="displayModeStore.isFullMode()" :class="sidebarStore.isCollapsed ? 'mb-2' : 'mb-6'">
        <p v-if="!sidebarStore.isCollapsed" class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider sidebar-text-animate" style="animation-delay: 300ms">文书写作</p>
        <div class="space-y-0.5">
          <!-- 客户档案 - 始终可用 -->
          <router-link to="/clients" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path.startsWith('/clients') }" @click="handleLinkClick">
            <span class="material-icons-outlined text-[20px]">people</span>
            <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 330ms">客户档案</span>
          </router-link>

          <!-- 推荐信 - 始终可用 -->
          <router-link to="/write/recommendation" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/write/recommendation' }" @click="handleLinkClick">
            <span class="material-icons-outlined text-[20px]">edit_note</span>
            <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 360ms">推荐信</span>
          </router-link>

          <!-- 简历 - 始终可用 -->
          <router-link to="/write/cv" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/write/cv' }" @click="handleLinkClick">
            <span class="material-icons-outlined text-[20px]">article</span>
            <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 390ms">简历</span>
          </router-link>

          <!-- 个人陈述 - 始终可用 -->
          <router-link to="/write/ps" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/write/ps' }" @click="handleLinkClick">
            <span class="material-icons-outlined text-[20px]">rate_review</span>
            <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 420ms">个人陈述</span>
          </router-link>
        </div>
        <!-- 收缩状态下的分隔线 -->
        <div v-if="sidebarStore.isCollapsed" class="flex justify-center mt-2 mb-1">
          <div class="w-8 h-px bg-gray-300"></div>
        </div>
      </div>

      <!-- AI工具 - 仅完整版显示 -->
      <div v-if="displayModeStore.isFullMode()" :class="sidebarStore.isCollapsed ? 'mb-2' : 'mb-6'">
        <p v-if="!sidebarStore.isCollapsed" class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider sidebar-text-animate" style="animation-delay: 450ms">AI工具</p>
        <div class="space-y-0.5">
          <!-- 降AI率 - 始终可用 -->
          <router-link to="/ai-reducer" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/ai-reducer' }" @click="handleLinkClick">
            <span class="material-icons-outlined text-[20px]">tune</span>
            <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 480ms">降AI率</span>
          </router-link>
        </div>
        <!-- 收缩状态下的分隔线 -->
        <div v-if="sidebarStore.isCollapsed" class="flex justify-center mt-2 mb-1">
          <div class="w-8 h-px bg-gray-300"></div>
        </div>
      </div>

      <!-- 组织管理 - 根据身份显示，仅完整版显示 -->
      <div v-if="canSeeOrganizationManagement && displayModeStore.isFullMode()" :class="sidebarStore.isCollapsed ? 'mb-2' : 'mb-6'">
        <p v-if="!sidebarStore.isCollapsed" class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider sidebar-text-animate" style="animation-delay: 510ms">组织管理</p>
        <div class="space-y-0.5">
          <router-link to="/enterprise/benefits"
                       class="sidebar-link"
                       :class="{ 'sidebar-link-active': $route.path === '/enterprise/benefits' }"
                       :title="sidebarStore.isCollapsed ? '权益管理' : ''"
                       @click="handleLinkClick">
            <span class="material-icons-outlined text-[20px]">workspace_premium</span>
            <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 540ms">权益管理</span>
          </router-link>

          <!-- 账号管理 - 根据组织权限显示，始终可用 -->
          <template v-if="canAccessAccountManagement">
            <!-- 有权限：可点击的链接 -->
            <router-link to="/enterprise/accounts"
                         class="sidebar-link"
                         :class="{ 'sidebar-link-active': $route.path === '/enterprise/accounts' }"
                         :title="sidebarStore.isCollapsed ? '账号管理' : ''"
                         @click="handleLinkClick">
              <span class="material-icons-outlined text-[20px]">group</span>
              <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 570ms">账号管理</span>
            </router-link>
          </template>

          <!-- 组织成员无权限时，不显示账号管理菜单 -->
        </div>
        <!-- 收缩状态下的分隔线 -->
        <div v-if="sidebarStore.isCollapsed" class="flex justify-center mt-2 mb-1">
          <div class="w-8 h-px bg-gray-300"></div>
        </div>
      </div>

      <!-- CRM系统 - 在完整版和纯净版都显示 -->
      <div :class="sidebarStore.isCollapsed ? 'mb-2' : 'mb-6'">
        <p v-if="!sidebarStore.isCollapsed" class="px-4 text-xs font-medium text-gray-400 mb-3 uppercase tracking-wider sidebar-text-animate" style="animation-delay: 600ms">CRM系统</p>
        <div class="space-y-0.5">
          <!-- 开发版 + 组织身份：显示具体功能按钮 -->
          <template v-if="shouldShowCRMSubmenus">
            <router-link to="/crm/market-resources" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/crm/market-resources' }">
              <span class="material-icons-outlined text-[20px]">campaign</span>
              <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 630ms">市场资源</span>
            </router-link>

            <router-link to="/crm/valid-customers" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/crm/valid-customers' }">
              <span class="material-icons-outlined text-[20px]">people</span>
              <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 660ms">有效客户</span>
            </router-link>

            <router-link to="/crm/customer-management" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path === '/crm/customer-management' }">
              <span class="material-icons-outlined text-[20px]">verified</span>
              <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 690ms">签约客户</span>
            </router-link>

            <router-link to="/crm/organization/departments" class="sidebar-link" :class="{ 'sidebar-link-active': $route.path.startsWith('/crm/organization') }">
              <span class="material-icons-outlined text-[20px]">corporate_fare</span>
              <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 720ms">组织管理</span>
            </router-link>
          </template>

          <!-- 上线版：显示"即将上线" -->
          <template v-else-if="isProductionMode">
            <div class="sidebar-link-disabled" title="即将上线">
              <div v-if="!sidebarStore.isCollapsed" class="sidebar-link-left">
                <span class="material-icons-outlined text-[20px]">business_center</span>
                <span class="sidebar-text-animate" style="animation-delay: 750ms">CRM系统</span>
              </div>
              <span v-if="sidebarStore.isCollapsed" class="material-icons-outlined text-[20px]">business_center</span>
              <span v-if="!sidebarStore.isCollapsed" class="coming-soon-badge sidebar-text-animate" style="animation-delay: 780ms">即将上线</span>
            </div>
          </template>

          <!-- 开发版 + 个人身份：显示可点击的CRM系统，点击跳转到提示页面 -->
          <template v-else>
            <a href="#" class="sidebar-link" @click.prevent="handleCRMSystemClick">
              <span class="material-icons-outlined text-[20px]">business_center</span>
              <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 750ms">CRM系统</span>
            </a>
          </template>
        </div>
      </div>


    </nav>

    <!-- 底部功能按钮 -->
    <div class="border-t border-gray-100 transition-all duration-300"
         :class="sidebarStore.isCollapsed ? 'p-2' : 'p-4'">

      <!-- 个人身份：显示充值按钮 -->
      <div v-if="isPersonalIdentity" class="space-y-2">
        <router-link to="/account/recharge" class="recharge-button" @click="handleLinkClick"
                     :class="sidebarStore.isCollapsed ? 'justify-center' : ''">
          <span class="material-icons-outlined text-[20px]"
                :class="sidebarStore.isCollapsed ? '' : 'mr-2'">account_balance_wallet</span>
          <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 750ms">账户充值</span>
        </router-link>
      </div>

      <!-- 组织身份：只有owner可以看到充值按钮 -->
      <div v-else>
        <router-link v-if="canAccessAccountManagement" to="/account/recharge" class="recharge-button" @click="handleLinkClick"
                     :class="sidebarStore.isCollapsed ? 'justify-center' : ''">
          <span class="material-icons-outlined text-[20px]"
                :class="sidebarStore.isCollapsed ? '' : 'mr-2'">account_balance_wallet</span>
          <span v-if="!sidebarStore.isCollapsed" class="sidebar-text-animate" style="animation-delay: 750ms">账户充值</span>
        </router-link>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useSidebarStore } from '@/stores/sidebar'
import { useDisplayModeStore } from '@/stores/displayMode'
import { useSidebarPermissions } from '@/composables/useSidebarPermissions'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import { getOrganizationDetail } from '@/api/organizations'

const router = useRouter()
const sidebarStore = useSidebarStore()
const displayModeStore = useDisplayModeStore()

// 使用侧边栏权限管理组合式函数
const {
  canSwitchViewMode,
  shouldShowTestFeatures,
  viewModeIcon,
  viewModeButtonClass,
  viewModeTooltip,
  toggleViewMode,
  setViewMode,
  isDevMode,
  isProductionMode
} = useSidebarPermissions()

// 使用组织权限管理组合式函数
const {
  canAccessAccountManagement,
  canSeeOrganizationManagement,
  isOrganizationIdentity,
  isPersonalIdentity,
  currentOrganization
} = useOrganizationPermissions()

// CRM系统菜单显示逻辑
const shouldShowCRMSubmenus = computed(() => {
  // 只有组织身份 + 开发版才显示CRM子菜单
  return isOrganizationIdentity.value && shouldShowTestFeatures.value
})

// 初始化侧边栏状态
onMounted(() => {
  sidebarStore.initializeSidebar()
})

// 套餐验证状态
const isValidatingPackage = ref(false)

// Logo相关状态
const organizationLogo = ref(null)
const logoLoading = ref(false)

// 计算属性：动态logo URL
const logoUrl = computed(() => {
  // 如果有企业logo，优先使用企业logo
  if (organizationLogo.value) {
    return organizationLogo.value
  }
  
  // 个人身份或无企业logo时，使用默认TunshuEdu logo
  // 根据收缩状态选择不同的默认logo（保持原有逻辑作为回退）
  if (sidebarStore.isCollapsed) {
    return new URL('@/assets/logo.svg', import.meta.url).href
  } else {
    return new URL('@/assets/Logo_TunshuEdu_W.svg', import.meta.url).href
  }
})

// 计算属性：logo alt文本
const logoAlt = computed(() => {
  if (organizationLogo.value && currentOrganization.value) {
    return `${currentOrganization.value.name} Logo`
  }
  return 'TunshuEdu Logo'
})

// 获取组织logo
const fetchOrganizationLogo = async () => {
  if (!isOrganizationIdentity.value || !currentOrganization.value?.id) {
    organizationLogo.value = null
    return
  }

  try {
    logoLoading.value = true
    const response = await getOrganizationDetail(currentOrganization.value.id)
    
    // 直接使用数据库返回的logo_url路径
    organizationLogo.value = response.logo_url || null
  } catch (error) {
    console.warn('获取组织logo失败:', error)
    organizationLogo.value = null
  } finally {
    logoLoading.value = false
  }
}

// 监听组织身份变化，重新获取logo
watch(
  () => currentOrganization.value?.id,
  (newOrgId) => {
    if (newOrgId) {
      fetchOrganizationLogo()
    } else {
      organizationLogo.value = null
    }
  },
  { immediate: true }
)

// 监听logo更新事件，实现自动刷新
onMounted(() => {
  const handleLogoUpdate = (event) => {
    if (event.detail?.logo_url) {
      organizationLogo.value = event.detail.logo_url
    }
  }
  
  window.addEventListener('organization-logo-updated', handleLogoUpdate)
  
  // 组件卸载时清理事件监听
  const cleanup = () => {
    window.removeEventListener('organization-logo-updated', handleLogoUpdate)
  }
  
  // 在Vue 3中使用onUnmounted来清理
  onUnmounted(cleanup)
})



// 处理CRM系统点击 - 个人身份跳转到提示页面
const handleCRMSystemClick = async () => {
  if (isOrganizationIdentity.value) {
    // 组织身份：不处理，让默认路由生效
    return
  }
  
  // 个人身份：跳转到CRM提示页面
  await router.push('/crm')
  handleLinkClick()
}

// 处理选校匹配点击 - 直接跳转，页面内进行套餐检测
const handleSchoolAssistantClick = async () => {
  if (isValidatingPackage.value) {
    return // 防止重复点击
  }

  try {
    isValidatingPackage.value = true

    // 直接跳转到选校匹配页面，让页面内部处理套餐检测和高斯模糊
    await router.push('/school-assistant')
    handleLinkClick() // 确保页面滚动到顶部
  } catch (error) {
    console.error('跳转选校匹配页面失败:', error)
    ElMessage.error({
      message: '页面跳转失败，请稍后重试',
      duration: 3000,
      showClose: true
    })
  } finally {
    isValidatingPackage.value = false
  }
}

// 处理侧边栏链接点击，确保页面滚动到顶部
const handleLinkClick = () => {
  // 延迟执行，确保路由已经切换
  setTimeout(() => {
    const mainContent = document.querySelector('.page-content-wrapper')
    if (mainContent) {
      mainContent.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      })
    }
  }, 50)
}
</script>

<style lang="postcss" scoped>
/* 侧边栏展开状态的链接样式 */
aside:not(.w-16) .sidebar-link {
  @apply flex items-center space-x-3 px-4 py-2.5 text-sm rounded-lg text-gray-600 hover:bg-gray-50 transition-all duration-300;
}

/* 侧边栏收缩状态的链接样式 */
aside.w-16 .sidebar-link {
  @apply flex items-center justify-center px-2 py-2.5 text-sm rounded-lg text-gray-600 hover:bg-gray-50 transition-all duration-300;
}

.sidebar-link-active {
  @apply bg-primary bg-opacity-10 text-primary font-medium;
}

/* 侧边栏展开状态的禁用链接样式 */
aside:not(.w-16) .sidebar-link-disabled {
  @apply flex items-center px-4 py-2.5 text-sm rounded-lg text-gray-600 cursor-not-allowed relative transition-all duration-300;
  background-color: transparent;
}

/* 侧边栏收缩状态的禁用链接样式 */
aside.w-16 .sidebar-link-disabled {
  @apply flex items-center justify-center px-2 py-2.5 text-sm rounded-lg text-gray-600 cursor-not-allowed relative transition-all duration-300;
  background-color: transparent;
}

.sidebar-link-disabled:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 展开状态下禁用链接的布局 */
aside:not(.w-16) .sidebar-link-disabled {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 禁用链接的左侧内容（图标+文字） */
aside:not(.w-16) .sidebar-link-disabled .sidebar-link-left {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 禁用链接的文字样式，使用灰色 */
aside:not(.w-16) .sidebar-link-disabled .sidebar-link-left span:not(.material-icons-outlined) {
  color: #9ca3af; /* 灰色文字 */
  font-weight: normal;
}

/* 禁用链接的图标样式，使用灰色 */
aside:not(.w-16) .sidebar-link-disabled .sidebar-link-left .material-icons-outlined {
  color: #9ca3af; /* 灰色图标 */
}

/* 收缩状态下禁用链接的图标样式 */
aside.w-16 .sidebar-link-disabled .material-icons-outlined {
  color: #9ca3af; /* 灰色图标 */
}

/* 展开状态下禁用链接的图标间距 */
aside:not(.w-16) .sidebar-link-disabled .material-icons-outlined {
  margin-right: 12px;
}

/* 收缩状态下禁用链接的图标不需要右边距 */
aside.w-16 .sidebar-link-disabled .material-icons-outlined {
  margin-right: 0;
}

.coming-soon-badge {
  @apply text-xs px-2 py-0.5 rounded-full bg-orange-100 text-orange-600 font-medium;
  font-size: 10px;
  white-space: nowrap;
}



.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* 炫彩充值按钮 */
.recharge-button {
  @apply flex items-center justify-center w-full px-4 py-3 rounded-lg text-white font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
  text-decoration: none;
}

.recharge-button:hover {
  background-position: right center;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.recharge-button:active {
  transform: translateY(0) scale(0.98);
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 渐进式文字展开动画 */
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-text-animate {
  animation: slideInFromLeft 0.2s ease-out forwards;
  opacity: 0;
}
</style>