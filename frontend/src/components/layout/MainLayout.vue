<template>
  <div class="flex h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <Sidebar />

    <!-- 主要内容区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- 顶部导航栏 -->
      <Header />

      <!-- 内容区域 -->
      <main
        class="flex-1 page-content-wrapper min-h-0"
        :class="{
          'p-6 overflow-auto': !isFullscreen,
          'p-0 overflow-hidden': isFullscreen
        }"
      >
        <router-view v-slot="{ Component }">
          <!-- 使用更快的过渡效果，减少过渡时间 -->
          <transition name="page-fade" mode="out-in" :duration="{ enter: 100, leave: 100 }">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
    </div>


  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAutoLogoutStore } from '@/stores/autoLogout'
import Sidebar from './Sidebar.vue'
import Header from './Header.vue'

const route = useRoute()
const autoLogoutStore = useAutoLogoutStore()

// 检查当前路由是否需要全屏显示
const isFullscreen = computed(() => {
  return route.meta?.fullscreen === true || route.path.includes('/writing/')
})

// 组件挂载时初始化自动登出功能
onMounted(() => {
  // 🔥 修复：延迟初始化，确保认证状态已稳定
  setTimeout(() => {
    autoLogoutStore.initialize()
  }, 1000)
})

// 组件卸载时清理自动登出功能
onUnmounted(() => {
  autoLogoutStore.destroy()
})
</script>

<style scoped>
/* 主布局样式 */

/* 页面切换动画 - 优化过渡效果 */
.page-fade-enter-active,
.page-fade-leave-active {
  transition: opacity 0.1s ease;
}

.page-fade-enter-from,
.page-fade-leave-to {
  opacity: 0;
}

/* 确保内容区域有相对定位，以便子元素可以使用绝对定位 */
.page-content-wrapper {
  position: relative;
}
</style>