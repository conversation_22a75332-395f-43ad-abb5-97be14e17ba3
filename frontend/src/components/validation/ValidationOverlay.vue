<template>
  <div class="validation-overlay">
    <!-- 高斯模糊遮罩层 -->
    <div class="blur-mask"></div>
    
    <!-- 提示内容 -->
    <div class="overlay-content">
      <div class="content-card">
        <!-- 验证中状态 -->
        <div v-if="isValidating" class="text-content">
          <div class="loading-spinner">
            <div class="spinner"></div>
          </div>
          <h3 class="title">正在验证套餐权限</h3>
          <p class="message">请稍候，正在检查您的套餐状态...</p>
        </div>

        <!-- 验证失败状态 -->
        <div v-else>
          <!-- 标题和消息 -->
          <div class="text-content">
            <h3 class="title">{{ suggestion.title }}</h3>
            <p class="message">{{ suggestion.message }}</p>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button
              v-if="suggestion.actionUrl"
              type="primary"
              @click="handleAction"
              class="action-btn"
            >
              {{ suggestion.actionText }}
            </el-button>

            <el-button
              v-else
              type="primary"
              @click="handleRetry"
              class="action-btn"
            >
              {{ suggestion.actionText }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { AISelectionValidationService } from '@/api/validation'

const props = defineProps({
  reason: {
    type: String,
    required: true
  },
  visible: {
    type: Boolean,
    default: true
  },
  isValidating: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['retry', 'close'])

const router = useRouter()

// 获取处理建议
const suggestion = computed(() => {
  return AISelectionValidationService.getActionSuggestion(props.reason)
})

// 验证中状态
const isValidating = computed(() => props.isValidating)

// 处理操作按钮点击
const handleAction = () => {
  if (suggestion.value.actionUrl) {
    router.push(suggestion.value.actionUrl)
  } else {
    handleRetry()
  }
}

// 处理重试
const handleRetry = () => {
  emit('retry')
}
</script>

<style scoped>
.validation-overlay {
  @apply absolute inset-0 z-50;
  pointer-events: auto;
}

.blur-mask {
  @apply absolute inset-0;
  backdrop-filter: blur(4px);
  background: rgba(255, 255, 255, 0.3);
  will-change: backdrop-filter;
  /* 为不支持backdrop-filter的浏览器提供降级方案 */
  background-image:
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.overlay-content {
  @apply absolute inset-0 flex items-center justify-center p-6;
}

.content-card {
  @apply bg-white rounded-xl shadow-lg border border-gray-200 p-8 max-w-md w-full text-center;
  animation: slideUp 0.15s ease-out;
  transform: translateZ(0); /* 启用硬件加速 */
}

.text-content {
  @apply mb-8;
}

.title {
  @apply text-xl font-semibold text-gray-900 mb-3;
}

.message {
  @apply text-gray-600 leading-relaxed;
}

.action-buttons {
  @apply flex justify-center w-full;
}

.action-btn {
  @apply w-full px-4 py-2 text-sm font-medium rounded-lg;
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  transition: all 0.2s ease !important;
  border: none !important;
}

.action-btn:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  transform: none !important;
}

/* 加载动画 */
.loading-spinner {
  @apply flex justify-center mb-4;
}

.spinner {
  @apply w-8 h-8 border-4 border-gray-200 border-t-purple-600 rounded-full;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .overlay-content {
    @apply p-4;
  }

  .content-card {
    @apply p-6;
  }

  .title {
    @apply text-lg;
  }
}
</style>
