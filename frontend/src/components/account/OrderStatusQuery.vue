<template>
  <div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-2xl shadow-lg p-8">
      <!-- 标题 -->
      <div class="text-center mb-6">
        <div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style="background-color: rgba(79, 70, 229, 0.1);">
          <span class="material-icons-outlined text-2xl" style="color: #4F46E5;">receipt_long</span>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">订单状态查询</h2>
        <p class="text-gray-600">查询您的支付订单状态，确认积分到账情况</p>
      </div>

      <!-- 订单号输入 -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">订单号</label>
        <div class="flex space-x-3">
          <input
            v-model="orderNo"
            type="text"
            placeholder="请输入订单号"
            class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 outline-none transition-colors"
            style="focus:ring-color: #4F46E5; focus:border-color: #4F46E5;"
            :disabled="isQuerying"
          />
          <button
            @click="queryOrderStatus"
            :disabled="!orderNo.trim() || isQuerying"
            class="px-6 py-3 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200"
            style="background-color: #4F46E5;"
            @mouseover="$event.target.style.backgroundColor = '#4338CA'"
            @mouseout="$event.target.style.backgroundColor = '#4F46E5'"
          >
            <span v-if="isQuerying" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              查询中...
            </span>
            <span v-else>查询状态</span>
          </button>
        </div>
      </div>

      <!-- 查询结果 -->
      <div v-if="queryResult" class="mb-6">
        <!-- 成功状态 -->
        <div v-if="queryResult.success && queryResult.order" class="border rounded-lg p-6">
          <!-- 订单基本信息 -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">订单信息</h3>
            <span
              :class="getStatusClass(queryResult.order.status)"
              :style="queryResult.order.status === 'refunded' ? 'background-color: #4F46E5;' : ''"
              class="px-3 py-1 rounded-full text-sm font-medium"
            >
              {{ getStatusText(queryResult.order.status) }}
            </span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <div class="text-sm text-gray-500">订单号</div>
              <div class="font-medium">{{ queryResult.order.order_no }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">支付金额</div>
              <div class="font-medium">¥{{ queryResult.order.amount }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">积分数量</div>
              <div class="font-medium">{{ queryResult.order.credits }}积分</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">支付方式</div>
              <div class="font-medium">{{ getPaymentMethodText(queryResult.order.payment_method) }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">创建时间</div>
              <div class="font-medium">{{ formatDateTime(queryResult.order.created_at) }}</div>
            </div>
            <div v-if="queryResult.order.payment_time">
              <div class="text-sm text-gray-500">支付时间</div>
              <div class="font-medium">{{ formatDateTime(queryResult.order.payment_time) }}</div>
            </div>
          </div>

          <!-- 支付成功提示 -->
          <div v-if="queryResult.order.status === 'paid'" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div class="flex items-center">
              <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
              <div>
                <div class="font-medium text-green-800">支付成功！</div>
                <div class="text-sm text-green-600">{{ queryResult.order.credits }}积分已成功充值到您的账户</div>
              </div>
            </div>
          </div>

          <!-- 支付失败提示 -->
          <div v-else-if="queryResult.order.status === 'failed'" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div class="flex items-center">
              <span class="material-icons-outlined text-red-600 mr-2">error</span>
              <div>
                <div class="font-medium text-red-800">支付失败</div>
                <div class="text-sm text-red-600">请重新发起支付或联系客服</div>
              </div>
            </div>
          </div>

          <!-- 待支付提示 -->
          <div v-else-if="queryResult.order.status === 'pending'" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div class="flex items-center">
              <span class="material-icons-outlined text-yellow-600 mr-2">schedule</span>
              <div>
                <div class="font-medium text-yellow-800">等待支付</div>
                <div class="text-sm text-yellow-600">请完成支付，支付完成后点击查询按钮确认状态</div>
              </div>
            </div>
          </div>

          <!-- 第三方查询信息 -->
          <div v-if="queryResult.trade_info" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="text-sm text-gray-600 mb-2">第三方支付信息</div>
            <div class="text-xs text-gray-500">
              <div v-for="(value, key) in queryResult.trade_info" :key="key">
                {{ key }}: {{ value }}
              </div>
            </div>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center">
            <span class="material-icons-outlined text-red-600 mr-2">error</span>
            <div>
              <div class="font-medium text-red-800">查询失败</div>
              <div class="text-sm text-red-600">{{ queryResult.message || '未知错误' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-4">
        <button
          @click="$emit('back')"
          class="flex-1 px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200"
        >
          返回充值
        </button>
        <button
          v-if="queryResult?.order?.status === 'paid'"
          @click="$emit('goToDashboard')"
          class="flex-1 px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200"
        >
          查看余额
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { queryPaymentStatus } from '@/api/account'

// 定义事件
const emit = defineEmits(['back', 'goToDashboard'])

// 定义props
const props = defineProps({
  initialOrderNo: {
    type: String,
    default: ''
  }
})

// 响应式数据
const orderNo = ref(props.initialOrderNo)
const isQuerying = ref(false)
const queryResult = ref(null)

// 查询订单状态
const queryOrderStatus = async () => {
  if (!orderNo.value.trim()) {
    ElMessage.warning('请输入订单号')
    return
  }

  try {
    isQuerying.value = true
    queryResult.value = null

    const response = await queryPaymentStatus(orderNo.value.trim())
    queryResult.value = response

    if (response.success) {
      ElMessage.success('查询成功')
      
      // 如果订单状态是已支付，发送事件通知父组件更新积分
      if (response.order?.status === 'paid') {
        emit('paymentSuccess', response.order)
      }
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询订单状态失败:', error)
    
    if (error.response?.status === 404) {
      ElMessage.error('订单不存在，请检查订单号是否正确')
    } else if (error.response?.status === 403) {
      ElMessage.error('无权访问此订单')
    } else if (error.response?.status === 401) {
      ElMessage.error('请先登录')
    } else {
      ElMessage.error('查询失败，请稍后重试')
    }
    
    queryResult.value = {
      success: false,
      message: error.response?.data?.detail || error.message || '查询失败'
    }
  } finally {
    isQuerying.value = false
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusClasses = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'paid': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'cancelled': 'bg-gray-100 text-gray-800',
    'refunded': 'text-white'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusTexts = {
    'pending': '待支付',
    'paid': '已支付',
    'failed': '支付失败',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return statusTexts[status] || '未知状态'
}

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  const methodTexts = {
    'alipay': '支付宝',
    'wechat': '微信支付'
  }
  return methodTexts[method] || method
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 如果有初始订单号，自动查询
if (props.initialOrderNo) {
  queryOrderStatus()
}
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
</style>
