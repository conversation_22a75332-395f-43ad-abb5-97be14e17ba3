<template>
  <div class="relative notification-bell">
    <!-- 铃铛图标按钮 -->
    <button
      @click="handleToggleNotifications"
      class="relative p-2 rounded-xl text-gray-500 hover:text-primary hover:bg-gray-50/80 transition-all duration-300 group select-none flex items-center justify-center"
      title="系统通知"
      :aria-label="`系统通知${hasUnread ? ` (${unreadCount}条未读)` : ''}`"
    >
      <span class="material-icons-outlined text-[20px] transition-transform group-hover:scale-110 duration-300 select-none">
        notifications
      </span>
      
      <!-- 未读数量徽章 -->
      <transition
        enter-active-class="transition-all duration-200"
        enter-from-class="scale-0 opacity-0"
        enter-to-class="scale-100 opacity-100"
        leave-active-class="transition-all duration-150"
        leave-from-class="scale-100 opacity-100"
        leave-to-class="scale-0 opacity-0"
      >
        <span
          v-if="hasUnread"
          class="absolute -top-0.5 -right-0.5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center font-semibold shadow-md border-2 border-white"
          :aria-label="`${unreadCount}条未读通知`"
        >
          {{ unreadCount > 9 ? '9+' : unreadCount }}
        </span>
      </transition>
    </button>

    <!-- 通知面板 -->
    <transition
      name="notification-panel"
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 scale-95 translate-y-2"
      enter-to-class="opacity-100 scale-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 scale-100 translate-y-0"
      leave-to-class="opacity-0 scale-95 translate-y-2"
    >
      <div
        v-if="showPanel"
        class="absolute right-0 top-full mt-3 w-[380px] bg-gray-50 rounded-2xl shadow-xl border border-gray-100/80 z-50"
        @click.stop
      >
        <!-- 头部 -->
        <div class="px-5 py-4 flex items-center justify-between">
          <div class="flex items-center space-x-2.5">
            <h3 class="text-lg font-semibold text-gray-900">通知</h3>
            <span 
              v-if="hasUnread" 
              class="px-2.5 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full"
            >
              {{ unreadCount }}条新消息
            </span>
          </div>
          <div class="flex items-center space-x-1">
            <button
              v-if="hasUnread"
              @click="handleMarkAllAsRead"
              class="px-3 py-1.5 text-xs text-gray-600 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 font-medium"
            >
              全部已读
            </button>
            <button
              @click="handleCloseNotifications"
              class="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-all duration-200"
              aria-label="关闭通知面板"
            >
              <span class="material-icons-outlined text-[18px]">close</span>
            </button>
          </div>
        </div>

        <!-- 通知列表 -->
        <div class="max-h-[480px] overflow-y-auto custom-scrollbar">
          <div v-if="notifications.length === 0" class="px-5 py-12 text-center">
            <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <span class="material-icons-outlined text-gray-400 text-[28px]">notifications_none</span>
            </div>
            <p class="text-gray-500 text-sm font-medium">暂无通知</p>
            <p class="text-gray-400 text-xs mt-1">有新消息时会在这里显示</p>
          </div>
          
          <div v-else class="pb-2">
            <div
              v-for="(notification, index) in notifications"
              :key="notification.id"
              class="relative mx-2.5 mb-2 p-4 rounded-xl transition-all duration-200 cursor-pointer group"
              :class="[
                !notification.isRead 
                  ? 'bg-gradient-to-r from-primary/5 to-indigo-50/50 hover:from-primary/8 hover:to-indigo-50/80 border border-primary/10' 
                  : 'bg-gray-50/60 hover:bg-gray-100/80',
                index !== notifications.length - 1 ? '' : 'mb-0'
              ]"
              @click="handleNotificationClick(notification)"
            >
              <div class="flex items-start space-x-3">
                <!-- 类型指示器 -->
                <div class="flex-shrink-0 mt-0.5">
                  <div 
                    class="w-2 h-2 rounded-full"
                    :class="[
                      notification.type === 'maintenance' ? 'bg-amber-400' :
                      notification.type === 'feature' ? 'bg-blue-400' :
                      notification.type === 'security' ? 'bg-red-400' :
                      notification.type === 'promotion' ? 'bg-green-400' :
                      'bg-gray-400'
                    ]"
                  ></div>
                </div>

                <!-- 内容区域 -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-start justify-between mb-2">
                    <h4 
                      class="text-sm text-gray-900 font-medium leading-5"
                      :class="{ 'font-semibold': !notification.isRead }"
                    >
                      {{ notification.title }}
                    </h4>
                    <!-- 未读指示器和时间 -->
                    <div class="flex items-center space-x-2 ml-2">
                      <span class="text-xs text-gray-400 whitespace-nowrap">
                        {{ formatTime(notification.createdAt) }}
                      </span>
                      <div 
                        v-if="!notification.isRead"
                        class="w-2 h-2 bg-primary rounded-full flex-shrink-0"
                        aria-label="未读"
                      ></div>
                    </div>
                  </div>
                  
                  <p class="text-sm text-gray-600 line-clamp-4 leading-relaxed mb-3">
                    {{ notification.content }}
                  </p>
                  
                  <!-- 底部标签和优先级 -->
                  <div class="flex items-center justify-between">
                    <span 
                      class="text-xs px-2.5 py-1 rounded-full font-medium"
                      :class="[
                        notification.type === 'maintenance' ? 'bg-amber-100 text-amber-700' :
                        notification.type === 'feature' ? 'bg-blue-100 text-blue-700' :
                        notification.type === 'security' ? 'bg-red-100 text-red-700' :
                        notification.type === 'promotion' ? 'bg-green-100 text-green-700' :
                        'bg-gray-100 text-gray-700'
                      ]"
                    >
                      {{ 
                        notification.type === 'maintenance' ? '系统维护' :
                        notification.type === 'feature' ? '功能更新' :
                        notification.type === 'security' ? '安全提醒' :
                        notification.type === 'promotion' ? '活动通知' :
                        '系统消息'
                      }}
                    </span>
                    <span 
                      v-if="notification.priority === 'high'"
                      class="text-xs px-2.5 py-1 bg-red-100 text-red-700 rounded-full font-medium"
                    >
                      重要
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- 遮罩层 -->
    <div
      v-if="showPanel"
      class="fixed inset-0 z-40 bg-black/5"
      @click="handleCloseNotifications"
      aria-hidden="true"
    ></div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useNotificationsStore } from '@/stores/notifications'

const notificationsStore = useNotificationsStore()

// 计算属性
const notifications = computed(() => notificationsStore.notifications)
const showPanel = computed(() => notificationsStore.showPanel)
const unreadCount = computed(() => notificationsStore.unreadCount)
const hasUnread = computed(() => notificationsStore.hasUnread)

// 事件处理函数
const handleToggleNotifications = () => {
  notificationsStore.togglePanel()
}

const handleCloseNotifications = () => {
  notificationsStore.closePanel()
}

const handleMarkAllAsRead = () => {
  notificationsStore.markAllAsRead()
}

const handleNotificationClick = (notification) => {
  // 标记为已读
  notificationsStore.markAsRead(notification.id)
  
  // 处理点击行为，如果有actionData则根据其配置执行相应动作
  if (notification.actionData) {
    const { action_type, page_url } = notification.actionData
    
    if (action_type === 'page' && page_url) {
      // 跳转到指定页面
      console.log('跳转到页面:', page_url)
      // 这里可以使用 router.push(page_url) 进行路由跳转
    } else if (action_type === 'external_link' && notification.actionData.external_url) {
      // 打开外部链接
      window.open(notification.actionData.external_url, '_blank')
    }
  } else {
    // 根据通知类型的默认处理
    switch (notification.type) {
      case 'maintenance':
        console.log('跳转到系统状态页面')
        break
      case 'feature':
        console.log('跳转到功能介绍页面')
        break
      case 'security':
        console.log('跳转到安全设置页面')
        break
      case 'promotion':
        console.log('跳转到活动页面')
        break
      default:
        console.log('默认处理')
    }
  }
}

// 辅助函数
const getNotificationTypeInfo = (type) => {
  return notificationsStore.getNotificationTypeInfo(type)
}

const formatTime = (timeString) => {
  return notificationsStore.formatTime(timeString)
}

const getPriorityText = (priority) => {
  return notificationsStore.getPriorityText(priority)
}

const getPriorityStyle = (priority) => {
  return notificationsStore.getPriorityStyle(priority)
}

// 生命周期
onMounted(() => {
  notificationsStore.initNotifications()
  
  // 监听ESC键关闭面板
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

const handleKeydown = (event) => {
  if (event.key === 'Escape' && showPanel.value) {
    handleCloseNotifications()
  }
}
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
  /* 防止文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 防止整个通知组件被选择 */
.notification-bell {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 防止按钮被选择 */
button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* 动画效果优化 */
.notification-panel-enter-active,
.notification-panel-leave-active {
  transform-origin: top right;
}

/* 通知项动画效果 */
.group:hover {
  transform: translateY(-1px);
}

/* 渐变背景动画 */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 微光效果 */
.notification-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-panel {
    width: calc(100vw - 24px);
    max-width: 360px;
    right: 12px;
    left: 12px;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .notification-panel {
    width: calc(100vw - 16px);
    max-width: none;
    right: 8px;
    left: 8px;
  }
}
</style> 