<template>
  <div class="course-structure" :class="{ 'dialog-mode': inDialog }">
    <!-- 紧凑模式：简洁显示 -->
    <div v-if="compact" class="compact-structure">
      <div class="course-summary" :class="{ 'dialog-summary': inDialog }">
        <div class="summary-stats">
          <span class="stat-item">
            <span class="stat-number">{{ totalCourses }}</span>
            <span class="stat-label">门课程</span>
          </span>
          <span v-if="requiredCourses > 0" class="stat-item">
            <span class="stat-number">{{ requiredCourses }}</span>
            <span class="stat-label">必修</span>
          </span>
          <span v-if="electiveCourses > 0" class="stat-item">
            <span class="stat-number">{{ electiveCourses }}</span>
            <span class="stat-label">选修</span>
          </span>
        </div>
        <div v-if="courseDescription" class="course-description-compact">
          {{ truncateText(courseDescription, inDialog ? 80 : 100) }}
        </div>
      </div>
    </div>

    <!-- 完整模式：详细展示 -->
    <div v-else class="full-structure">
      <!-- 课程描述 -->
      <div v-if="courseDescription" class="course-description">
        <h4 class="description-title">课程描述</h4>
        <p class="description-content">{{ courseDescription }}</p>
      </div>

      <!-- 课程统计 -->
      <div v-if="totalCourses > 0" class="course-stats">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <span class="material-icons-outlined">school</span>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ totalCourses }}</div>
              <div class="stat-label">总课程数</div>
            </div>
          </div>
          <div v-if="requiredCourses > 0" class="stat-card required">
            <div class="stat-icon">
              <span class="material-icons-outlined">bookmark</span>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ requiredCourses }}</div>
              <div class="stat-label">必修课程</div>
            </div>
          </div>
          <div v-if="electiveCourses > 0" class="stat-card elective">
            <div class="stat-icon">
              <span class="material-icons-outlined">bookmark_border</span>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ electiveCourses }}</div>
              <div class="stat-label">选修课程</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程分类展示 -->
      <div v-if="courseCategories.length > 0" class="course-categories">
        <div class="categories-header">
          <h4 class="categories-title">课程分类</h4>
          <div class="categories-tabs">
            <button
              v-for="category in courseCategories"
              :key="category.name"
              @click="switchCategory(category.name)"
              :class="['category-tab', { active: activeCategory === category.name }]"
            >
              {{ category.name }}
              <span class="course-count">({{ category.courses.length }})</span>
            </button>
          </div>
        </div>

        <!-- 当前分类的课程列表 - 智能高度显示 -->
        <div class="course-list-container" :style="{ height: actualContentHeight + 'px' }">
          <div 
            ref="courseListScrollRef" 
            class="course-list-scroll"
            :class="{ 'enable-scroll': needsScroll, 'no-scroll': !needsScroll }"
            @scroll="handleScroll"
          >
            <div
              v-for="course in activeCategoryCourses"
              :key="course.id"
              class="course-item"
            >
              <div class="course-main">
                <h5 class="course-name-cn">{{ course.nameCn }}</h5>
                <p v-if="course.nameEn" class="course-name-en">{{ course.nameEn }}</p>
                <p v-if="course.specialization" class="course-specialization">{{ course.specialization }}</p>
              </div>
              <div class="course-meta">
                <span
                  :class="['course-type-badge', course.type]"
                >
                  {{ getCourseTypeText(course.type) }}
                </span>
                <span v-if="course.credits" class="course-credits">
                  {{ course.credits }}学分
                </span>
              </div>
            </div>
          </div>
          

        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!courseDescription && totalCourses === 0" class="text-center py-4">
      <span class="text-gray-400 text-sm">暂无课程设置信息</span>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'

// 定义事件
const emit = defineEmits(['category-changed'])

// Props定义
const props = defineProps({
  // 课程设置字符串
  courseStructure: {
    type: String,
    default: ''
  },
  // 是否使用紧凑模式
  compact: {
    type: Boolean,
    default: false
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  },
  // 是否在弹窗中使用
  inDialog: {
    type: Boolean,
    default: false
  },
  // 动态高度
  dynamicHeight: {
    type: Number,
    default: 300
  }
})

// 响应式状态
const activeCategory = ref('')

// 课程列表滚动容器的引用
const courseListScrollRef = ref(null)

// 清理HTML标签和特殊字符
const cleanHtmlText = (text) => {
  if (!text) return ''
  
  return text
    .replace(/<br\s*\/?>/gi, '\n') // 替换<br/>为换行
    .replace(/<[^>]*>/g, '') // 移除所有HTML标签
    .replace(/&nbsp;/g, ' ') // 替换&nbsp;为空格
    .replace(/&amp;/g, '&') // 替换&amp;为&
    .replace(/&lt;/g, '<') // 替换&lt;为<
    .replace(/&gt;/g, '>') // 替换&gt;为>
    .trim()
}

// 清理课程文本，增强鲁棒性
const cleanCourseText = (text) => {
  if (!text) return ''
  
  return text
    .replace(/\s+/g, ' ') // 合并多个空格为一个
    .replace(/\|\s*\|/g, '|') // 移除空的分隔符
    .trim()
}

// 解析课程结构
const parsedStructure = computed(() => {
  if (!props.courseStructure || props.courseStructure.trim() === '') {
    return {
      description: '',
      courses: [],
      categories: []
    }
  }

  const cleanedText = cleanHtmlText(props.courseStructure)
  
  try {
    // 分离课程描述和课程列表
    const parts = cleanedText.split(/课程列表[：:]/i)
    const description = parts[0]?.replace(/课程描述[：:]?/i, '').trim() || ''
    const courseListText = parts[1] || ''

    // 解析课程列表
    const courses = []
    if (courseListText) {
      const courseLines = courseListText.split('\n').filter(line => line.trim())
      
      for (const line of courseLines) {
        const course = parseCourse(line.trim())
        if (course) {
          courses.push(course)
        }
      }
    }

    // 按类型分类课程
    const categories = categorizeCourses(courses)

    return {
      description,
      courses,
      categories
    }
  } catch (error) {
    console.warn('解析课程结构失败:', error)
    return {
      description: cleanedText,
      courses: [],
      categories: []
    }
  }
})

// 解析单个课程
const parseCourse = (courseText) => {
  if (!courseText) return null

  try {
    // 清理课程文本
    const cleanedText = cleanCourseText(courseText)
    
    // 支持多种格式：
    // 格式1（4个字段）：课程中文名 | 课程英文名 | 课程类型 | 可选学科方向
    // 格式2（3个字段）：课程中文名 | 课程英文名 | 课程类型
    const parts = cleanedText.split('|').map(part => part.trim()).filter(part => part)
    
    if (parts.length >= 1) {
      const nameCn = parts[0] || ''
      const nameEn = parts[1] || ''
      const typeText = parts[2] || '其他课程'
      const specialization = parts[3] || '' // 可选学科方向（如果有的话）
      
      // 确定课程类型
      let type = 'other'
      if (typeText.includes('必修')) {
        type = 'required'
      } else if (typeText.includes('选修')) {
        type = 'elective'
      } else if (typeText.includes('核心')) {
        type = 'core'
      } else if (typeText.includes('学科课程')) {
        type = 'subject'
      } else if (typeText.includes('顶点经验')) {
        type = 'capstone'
      }

      return {
        id: `course_${Date.now()}_${Math.random()}`,
        nameCn,
        nameEn,
        type,
        typeText,
        specialization, // 新增：可选学科方向
        credits: extractCredits(courseText),
        originalText: courseText // 保存原始文本用于调试
      }
    }
  } catch (error) {
    console.warn('解析课程失败:', error, courseText)
  }

  return null
}

// 提取学分信息
const extractCredits = (text) => {
  const creditMatch = text.match(/(\d+)\s*学分/i)
  return creditMatch ? creditMatch[1] : null
}

// 课程分类
const categorizeCourses = (courses) => {
  const categories = []
  const categoryMap = new Map()
  const specializationMap = new Map()

  // 按类型分组
  for (const course of courses) {
    const categoryName = getCourseTypeText(course.type)
    
    if (!categoryMap.has(categoryName)) {
      categoryMap.set(categoryName, {
        name: categoryName,
        courses: []
      })
    }
    
    categoryMap.get(categoryName).courses.push(course)
  }

  // 按可选学科方向分组（如果有的话）
  for (const course of courses) {
    if (course.specialization && course.specialization.trim()) {
      const specializationName = course.specialization.trim()
      
      if (!specializationMap.has(specializationName)) {
        specializationMap.set(specializationName, {
          name: specializationName,
          courses: []
        })
      }
      
      specializationMap.get(specializationName).courses.push(course)
    }
  }

  // 添加"全部"分类
  if (courses.length > 0) {
    categories.push({
      name: '全部',
      courses: [...courses]
    })
  }

  // 添加课程类型分类
  categories.push(...Array.from(categoryMap.values()))

  // 添加可选学科方向分类
  if (specializationMap.size > 0) {
    categories.push(...Array.from(specializationMap.values()))
  }

  return categories
}

// 计算属性
const courseDescription = computed(() => parsedStructure.value.description)
const courseCategories = computed(() => parsedStructure.value.categories)
const totalCourses = computed(() => parsedStructure.value.courses.length)
const requiredCourses = computed(() => 
  parsedStructure.value.courses.filter(course => course.type === 'required').length
)
const electiveCourses = computed(() => 
  parsedStructure.value.courses.filter(course => course.type === 'elective').length
)

const activeCategoryCourses = computed(() => {
  // 如果还没有设置活跃分类，且有可用分类，则使用第一个分类
  if (!activeCategory.value && courseCategories.value.length > 0) {
    activeCategory.value = courseCategories.value[0].name
  }

  const category = courseCategories.value.find(cat => cat.name === activeCategory.value)
  const courses = category ? category.courses : []

  // 调试信息（仅在开发环境输出）
  if (process.env.NODE_ENV === 'development') {
    console.log('📋 活跃分类课程计算:', {
      '当前活跃分类': activeCategory.value,
      '找到的分类': category?.name || '未找到',
      '课程数量': courses.length,
      '所有可用分类': courseCategories.value.map(cat => `${cat.name}(${cat.courses.length}门课程)`)
    })
  }

  return courses
})

// 判断是否需要滚动
const needsScroll = computed(() => {
  const courseCount = activeCategoryCourses.value.length
  return courseCount > 6
})

// 滚动状态
const hasScrolled = ref(false)

// 监听滚动事件
const handleScroll = () => {
  if (courseListScrollRef.value) {
    const scrollTop = courseListScrollRef.value.scrollTop
    hasScrolled.value = scrollTop > 10 // 滚动超过10px就认为用户已经开始滚动
  }
}

// 重置滚动状态
const resetScrollState = () => {
  hasScrolled.value = false
  if (courseListScrollRef.value) {
    courseListScrollRef.value.scrollTop = 0
  }
}

// 监听分类变化，重置滚动状态
watch(activeCategory, () => {
  resetScrollState()
})

// 监听窗口大小变化，重新计算高度
const handleResize = () => {
  // 发射事件通知父组件重新计算高度
  emit('category-changed', activeCategory.value)
}

// 在组件挂载时添加窗口大小监听
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

// 在组件卸载时移除监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})

// 计算单个课程项目的实际高度
const calculateCourseItemHeight = (course) => {
  // 检查是否为移动设备
  const isMobile = window.innerWidth <= 640
  
  if (isMobile) {
    // 移动设备：垂直布局，基础高度更大
    let height = 32 // padding (16px * 2)
    
    // 中文名称行
    height += 22
    
    // 英文名称行（如果存在）
    if (course.nameEn && course.nameEn.trim()) {
      height += 18
    }
    
    // 专业方向行（如果存在）
    if (course.specialization && course.specialization.trim()) {
      height += 20
    }
    
    // meta信息行（类型标签 + 学分）
    height += 24
    
    // 间距
    height += 8
    
    // 确保最小高度为80px（移动设备）
    return Math.max(height, 80)
  } else {
    // 桌面设备：水平布局，padding为12px * 2 = 24px
    let height = 24 // padding (12px * 2)
    
    // 中文名称行：text-sm (14px) + line-height (1.4) = 19.6px ≈ 20px
    height += 20
    
    // 英文名称行（如果存在）：text-xs (12px) + mt-0.5 (2px) + line-height (1.3) = 17.6px ≈ 18px
    if (course.nameEn && course.nameEn.trim()) {
      height += 18
    }
    
    // 专业方向行（如果存在）：text-xs (12px) + mt-1 (4px) + line-height (1.3) = 19.6px ≈ 20px
    if (course.specialization && course.specialization.trim()) {
      height += 20
    }
    
    // 与CSS中的min-height: 60px保持一致
    return Math.max(height, 60)
  }
}

// 计算当前分类所有课程的总高度
const calculateTotalContentHeight = () => {
  const courses = activeCategoryCourses.value
  if (courses.length === 0) return 60 // 空状态更紧凑的高度
  
  // 计算所有课程项目的实际高度总和
  const totalHeight = courses.reduce((sum, course) => {
    return sum + calculateCourseItemHeight(course)
  }, 0)
  
  // 添加分隔线高度（课程之间的分隔线）
  const dividerHeight = Math.max(0, courses.length - 1) * 1 // 每条分隔线1px
  
  // 容器内边距：上下各8px
  const containerPadding = 16
  
  const finalHeight = totalHeight + dividerHeight + containerPadding
  
  // 调试信息（仅在开发环境输出）
  if (process.env.NODE_ENV === 'development') {
    console.log('📊 总高度计算详情:', {
      '课程数量': courses.length,
      '课程总高度': totalHeight + 'px',
      '分隔线高度': dividerHeight + 'px',
      '容器内边距': containerPadding + 'px',
      '最终总高度': finalHeight + 'px'
    })
  }
  
  return finalHeight
}

// 计算实际需要的高度
const actualContentHeight = computed(() => {
  const courseCount = activeCategoryCourses.value.length
  if (courseCount === 0) return 60 // 空状态固定高度，与calculateTotalContentHeight保持一致

  const maxVisibleItems = 6 // 最多显示6个课程项目
  
  // 如果课程数量少于等于6个，使用实际内容高度（不受dynamicHeight限制）
  if (courseCount <= maxVisibleItems) {
    // 直接计算课程项目的实际高度，不添加额外的容器内边距
    const coursesHeight = activeCategoryCourses.value.reduce((sum, course) => {
      return sum + calculateCourseItemHeight(course)
    }, 0)
    
    // 只添加分隔线高度，不添加额外的容器内边距
    const dividerHeight = Math.max(0, courseCount - 1) * 1 // 每条分隔线1px
    
    const contentHeight = coursesHeight + dividerHeight
    
          // 调试信息（仅在开发环境输出）
      if (process.env.NODE_ENV === 'development') {
        console.log('📏 课程容器高度计算 (少于等于6个):', {
        '课程数量': courseCount,
        '课程总高度': coursesHeight + 'px',
        '分隔线高度': dividerHeight + 'px',
        '最终高度': contentHeight + 'px',
        '当前分类': activeCategory.value,
        '需要滚动': '否'
      })
    }
    
    return contentHeight
  }
  
  // 如果课程数量多于6个，计算前6个课程的实际高度，但受dynamicHeight限制
  const first6Courses = activeCategoryCourses.value.slice(0, 6)
  const first6Height = first6Courses.reduce((sum, course) => {
    return sum + calculateCourseItemHeight(course)
  }, 0)
  
  // 添加分隔线高度（前6个课程之间的分隔线）
  const dividerHeight = 5 * 1 // 5条分隔线，每条1px
  
  // 容器内边距：上下各8px
  const containerPadding = 16
  
  const calculatedHeight = first6Height + dividerHeight + containerPadding
  
  // 多于6个课程时，使用dynamicHeight作为最大限制
  const finalHeight = Math.min(calculatedHeight, props.dynamicHeight)
  
  // 调试信息（仅在开发环境输出）
  if (process.env.NODE_ENV === 'development') {
    console.log('📏 课程容器高度计算 (多于6个):', {
      '课程数量': courseCount,
      '前6个课程高度': calculatedHeight + 'px',
      '动态高度限制': props.dynamicHeight + 'px',
      '最终使用高度': finalHeight + 'px',
      '当前分类': activeCategory.value,
      '需要滚动': '是',
      '隐藏课程数': courseCount - maxVisibleItems,
      '前6个课程详情': first6Courses.map(course => ({
        名称: course.nameCn,
        英文名: course.nameEn || '无',
        专业方向: course.specialization || '无',
        计算高度: calculateCourseItemHeight(course) + 'px'
      }))
    })
  }

  return finalHeight
})

// 方法
const getCourseTypeText = (type) => {
  const typeMap = {
    required: '必修课程',
    elective: '选修课程',
    core: '核心课程',
    subject: '学科课程',
    capstone: '顶点经验',
    other: '其他课程'
  }
  return typeMap[type] || '其他课程'
}

const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 切换课程分类并滚动到顶部
const switchCategory = (categoryName) => {
  activeCategory.value = categoryName

  // 发射分类变化事件，通知父组件重新计算高度
  emit('category-changed', categoryName)

  // 重置滚动状态
  resetScrollState()
}

// 监听分类变化，初始化活跃分类
watch(courseCategories, (newCategories) => {
  if (newCategories.length > 0 && !activeCategory.value) {
    activeCategory.value = newCategories[0].name

    // 调试信息（仅在开发环境输出）
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 初始化活跃分类:', {
        '设置分类': newCategories[0].name,
        '分类总数': newCategories.length,
        '所有分类': newCategories.map(cat => cat.name)
      })
    }
  }
}, { immediate: true })

// 监听courseStructure变化，确保重新初始化
watch(() => props.courseStructure, () => {
  // 当课程结构数据变化时，重置activeCategory以触发重新初始化
  activeCategory.value = ''
}, { immediate: true })


</script>

<style scoped>
.course-structure {
  @apply w-full;
}

/* 紧凑模式样式 */
.compact-structure {
  @apply space-y-2;
}

.course-summary {
  @apply bg-gray-50 rounded-lg p-3;
}

/* 弹窗模式下的样式调整 */
.dialog-mode .course-summary.dialog-summary {
  @apply bg-gray-50 rounded p-2;
  font-size: 0.75rem;
}

.summary-stats {
  @apply flex items-center space-x-4 mb-2;
}

.stat-item {
  @apply flex items-center space-x-1 text-sm;
}

.stat-item .stat-number {
  @apply font-bold text-primary;
}

.stat-item .stat-label {
  @apply text-gray-600;
}

.course-description-compact {
  @apply text-xs text-gray-600 leading-relaxed;
}

/* 完整模式样式 */
.full-structure {
  @apply space-y-4;
}

.course-description {
  @apply bg-purple-50 rounded-lg p-4;
}

.description-title {
  @apply text-sm font-semibold text-purple-800 mb-2;
}

.description-content {
  @apply text-sm text-purple-700 leading-relaxed;
}

/* 课程统计 */
.course-stats {
  @apply bg-white rounded-lg border border-gray-200 p-4;
}

.stats-grid {
  @apply grid grid-cols-1 sm:grid-cols-3 gap-4;
}

.stat-card {
  @apply flex items-center space-x-3 p-3 bg-gray-50 rounded-lg;
}

.stat-card.required {
  @apply bg-purple-50;
}

.stat-card.elective {
  @apply bg-purple-50;
}

.stat-card .stat-icon {
  @apply text-gray-500;
}

.stat-card.required .stat-icon {
  @apply text-purple-600;
}

.stat-card.elective .stat-icon {
  @apply text-purple-600;
}

.stat-card .stat-info {
  @apply flex-1;
}

.stat-card .stat-number {
  @apply text-lg font-bold text-gray-800;
}

.stat-card .stat-label {
  @apply text-xs text-gray-600;
}

/* 课程分类 */
.course-categories {
  @apply bg-white rounded-lg border border-gray-200 overflow-hidden;
}

.categories-header {
  @apply p-4 border-b border-gray-200;
}

.categories-title {
  @apply text-sm font-semibold text-gray-800 mb-3;
}

.categories-tabs {
  @apply flex flex-wrap gap-2;
}

.category-tab {
  @apply px-3 py-1.5 text-xs font-medium rounded-full border transition-colors;
  @apply border-gray-200 text-gray-600 hover:border-primary hover:text-primary;
}

.category-tab.active {
  @apply bg-primary text-white border-primary;
}

.category-tab .course-count {
  @apply ml-1 opacity-75;
}

/* 课程列表容器 */
.course-list-container {
  @apply relative;
}

.course-list-scroll {
  @apply h-full divide-y divide-gray-100;
}

/* 启用滚动的样式 */
.course-list-scroll.enable-scroll {
  @apply overflow-y-auto;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

.course-list-scroll.enable-scroll::-webkit-scrollbar {
  width: 6px;
}

.course-list-scroll.enable-scroll::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.course-list-scroll.enable-scroll::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded;
}

.course-list-scroll.enable-scroll::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* 不需要滚动的样式 */
.course-list-scroll.no-scroll {
  @apply overflow-hidden;
}



.course-item {
  @apply flex items-start justify-between p-3 hover:bg-gray-50 transition-colors;
  min-height: 60px; /* 确保每个课程项目有合适的高度 */
}

.course-main {
  @apply flex-1 min-w-0;
}

.course-name-cn {
  @apply text-sm font-medium text-gray-800;
  line-height: 1.4;
}

.course-name-en {
  @apply text-xs text-gray-500 mt-0.5;
  line-height: 1.3;
}

.course-meta {
  @apply flex items-start flex-col space-y-1 ml-4 mt-1;
}

.course-type-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.course-type-badge.required {
  @apply bg-green-100 text-green-800;
}

.course-type-badge.elective {
  @apply bg-yellow-100 text-yellow-800;
}

.course-type-badge.core {
  @apply bg-blue-100 text-blue-800;
}

.course-type-badge.subject {
  @apply bg-purple-100 text-purple-800;
}

.course-type-badge.capstone {
  @apply bg-red-100 text-red-800;
}

.course-type-badge.other {
  @apply bg-gray-100 text-gray-800;
}

.course-credits {
  @apply text-xs text-gray-500;
}

.course-specialization {
  @apply text-xs text-gray-500 mt-1 italic;
  line-height: 1.3;
}



/* 响应式设计 */
@media (max-width: 640px) {
  .stats-grid {
    @apply grid-cols-1;
  }
  
  .categories-tabs {
    @apply flex-col;
  }
  
  .course-item {
    @apply flex-col items-start space-y-2 p-4;
    min-height: 80px; /* 移动设备上增加最小高度 */
  }
  
  .course-meta {
    @apply ml-0 self-start flex-row space-x-2 space-y-0;
  }
  
  .course-main {
    @apply w-full;
  }
}
</style>
