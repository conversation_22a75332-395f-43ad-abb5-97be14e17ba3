<template>
  <div class="application-timeline w-full" :class="{ 'dialog-mode': inDialog, 'compact-dialog': inDialog }">
    <!-- 入学季节标题 -->
    <div v-if="semesterTitle" class="semester-title" :class="inDialog ? 'mb-2' : 'mb-4'">
      <h3 class="font-semibold text-primary flex items-center" :class="inDialog ? 'text-sm' : 'text-lg'">
        <span class="material-icons-outlined text-primary mr-2" :class="inDialog ? 'text-sm' : ''">school</span>
        {{ semesterTitle }}
      </h3>
    </div>

    <!-- 紧凑模式：单行显示 -->
    <div v-if="compact" class="compact-timeline">
      <div class="flex items-center space-x-2" :class="inDialog ? 'text-xs' : 'text-sm'">
        <span
          v-for="(node, index) in filteredTimelineNodes"
          :key="index"
          class="inline-flex items-center"
        >
          <span
            :class="[getNodeStatusClass(node.status), 'px-2 py-1 rounded-full font-medium whitespace-nowrap text-xs']"
          >
            {{ node.name }}
            <span v-if="node.date" class="ml-1 opacity-75">
              ({{ formatDate(node.date) }})
            </span>
          </span>
          <span
            v-if="index < filteredTimelineNodes.length - 1"
            class="mx-1 text-gray-300"
          >
            |
          </span>
        </span>
      </div>
    </div>

    <!-- 完整模式：时间线展示 -->
    <div v-else class="full-timeline">
      <div class="relative">
        <!-- 时间线主线 -->
        <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-gray-200" :class="inDialog ? 'left-2' : 'left-4'"></div>

        <!-- 时间节点 -->
        <div :class="inDialog ? 'space-y-2' : 'space-y-4'">
          <div
            v-for="(node, index) in filteredTimelineNodes"
            :key="index"
            class="relative flex items-start"
          >
            <!-- 节点圆点 -->
            <div class="relative z-10 flex items-center justify-center">
              <div
                :class="[getNodeDotClass(node.status), 'rounded-full border-2 flex items-center justify-center', inDialog ? 'w-6 h-6' : 'w-8 h-8']"
              >
                <span class="material-icons-outlined" :class="inDialog ? 'text-xs' : 'text-sm'">
                  {{ getNodeIcon(node.status) }}
                </span>
              </div>
            </div>

            <!-- 节点内容 -->
            <div :class="inDialog ? 'ml-2 flex-1 min-w-0' : 'ml-4 flex-1 min-w-0'">
              <div class="bg-white rounded-lg border border-gray-200 shadow-sm" :class="inDialog ? 'p-2' : 'p-3'">
                <div class="flex items-center justify-between">
                  <h4
                    :class="[getNodeTitleClass(node.status), 'font-medium', inDialog ? 'text-xs' : 'text-sm']"
                  >
                    {{ node.name }}
                  </h4>
                  <span
                    v-if="node.date"
                    :class="getNodeDateClass(node.status)"
                    class="text-xs"
                  >
                    {{ formatDate(node.date) }}
                  </span>
                </div>

                <!-- 状态标签 -->
                <div v-if="!inDialog" class="mt-2">
                  <span
                    :class="getNodeStatusClass(node.status)"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  >
                    <span
                      :class="getStatusDotClass(node.status)"
                      class="w-1.5 h-1.5 rounded-full mr-1.5"
                    ></span>
                    {{ getStatusText(node.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredTimelineNodes.length === 0 && !semesterTitle" class="text-center py-4">
      <span class="text-gray-400 text-sm">暂无申请时间信息</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 申请时间字符串，支持多种格式
  applicationTime: {
    type: String,
    default: ''
  },
  // 是否使用紧凑模式
  compact: {
    type: Boolean,
    default: false
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  },
  // 是否在弹窗中使用（调整样式以匹配弹窗设计）
  inDialog: {
    type: Boolean,
    default: false
  }
})

// 清理HTML标签和特殊字符
const cleanHtmlText = (text) => {
  if (!text) return ''

  return text
    .replace(/<br\s*\/?>/gi, ' | ') // 替换<br/>为分隔符，保持结构
    .replace(/<[^>]*>/g, '') // 移除所有HTML标签
    .replace(/&nbsp;/g, ' ') // 替换&nbsp;为空格
    .replace(/&amp;/g, '&') // 替换&amp;为&
    .replace(/&lt;/g, '<') // 替换&lt;为<
    .replace(/&gt;/g, '>') // 替换&gt;为>
    .replace(/\s*\|\s*/g, ' | ') // 标准化分隔符格式
    .replace(/\s+/g, ' ') // 合并多个空格为一个
    .trim()
}

// 提取入学季节标题
const semesterTitle = computed(() => {
  if (!props.applicationTime || props.applicationTime.trim() === '') {
    return ''
  }

  const cleanedTimeStr = cleanHtmlText(props.applicationTime)

  // 处理多个入学季节的情况，只取第一个作为标题
  const firstSemesterMatch = cleanedTimeStr.match(/^([^:：]+(?:年|季)(?:入学|申请)?)[:：]?/)

  if (firstSemesterMatch) {
    return firstSemesterMatch[1].trim()
  }

  return ''
})

// 解析申请时间字符串
const timelineNodes = computed(() => {
  if (!props.applicationTime || props.applicationTime.trim() === '') {
    return []
  }

  // 清理HTML标签
  const cleanedTimeStr = cleanHtmlText(props.applicationTime)

  const allNodes = []

  try {
    // 处理多个入学季节的情况，按入学季节分组
    const semesterSections = splitBySemesters(cleanedTimeStr)

    for (const section of semesterSections) {
      const sectionNodes = parseSingleSemesterSection(section)
      allNodes.push(...sectionNodes)
    }
  } catch (error) {
    console.warn('解析申请时间失败:', error)
    // 如果解析失败，返回原始文本作为单个节点
    return [{
      name: cleanedTimeStr,
      date: null,
      status: 'unknown',
      originalText: cleanedTimeStr
    }]
  }

  return allNodes.filter(node => node.name) // 过滤掉空名称的节点
})

// 按入学季节分割字符串
const splitBySemesters = (timeStr) => {
  // 首先检查是否包含复合模式：节点(日期) 入学季节
  // 更新模式以匹配更多情况，包括 "Round 4 截止(2025-03-14) 26年秋季入学: 开放申请"
  const complexPattern = /^(.+?\([^)]*\))\s*(\d{2,4}年[春夏秋冬]季入学.*)$/
  const complexMatch = timeStr.match(complexPattern)

  if (complexMatch) {
    // 找到复合模式，直接分割为两个section
    const firstPart = complexMatch[1].trim()
    const secondPart = complexMatch[2].trim()

    const sections = []
    if (firstPart) sections.push(firstPart)
    if (secondPart) sections.push(secondPart)

    return sections
  }

  // 检查另一种复合模式：节点 入学季节: 其他信息
  // 匹配类似 "Round 4 截止(2025-03-14) 26年秋季入学: 开放申请" 的模式
  // 使用更精确的模式，确保第一部分包含括号或特定关键词
  const anotherComplexPattern = /^(.+?(?:\([^)]*\)|round\s+\d+|第\d+轮).*?)\s+(\d{2,4}年[春夏秋冬]季入学[:：].*)$/i
  const anotherComplexMatch = timeStr.match(anotherComplexPattern)

  if (anotherComplexMatch) {
    const firstPart = anotherComplexMatch[1].trim()
    const secondPart = anotherComplexMatch[2].trim()

    const sections = []
    if (firstPart) sections.push(firstPart)
    if (secondPart) sections.push(secondPart)

    return sections
  }

  // 匹配入学季节模式，如 "25年秋季入学:"、"23年春季入学:" 等
  const semesterPattern = /(\d{2,4}年[春夏秋冬]季入学)[:：]/g
  const sections = []
  let lastIndex = 0
  let match
  const matches = []

  // 先收集所有匹配项
  while ((match = semesterPattern.exec(timeStr)) !== null) {
    matches.push({
      match: match[0],
      semester: match[1],
      startIndex: match.index,
      endIndex: match.index + match[0].length
    })
  }

  // 如果没有找到入学季节模式，将整个字符串作为一个section
  if (matches.length === 0) {
    sections.push(timeStr)
    return sections
  }

  // 按匹配项分割字符串
  for (let i = 0; i < matches.length; i++) {
    const currentMatch = matches[i]
    const nextMatch = matches[i + 1]

    // 处理当前匹配前的内容（如果有）
    if (i === 0 && currentMatch.startIndex > 0) {
      const beforeContent = timeStr.substring(0, currentMatch.startIndex).trim()
      if (beforeContent) {
        sections.push(beforeContent)
      }
    }

    // 确定当前section的结束位置
    const sectionEnd = nextMatch ? nextMatch.startIndex : timeStr.length
    const sectionContent = timeStr.substring(currentMatch.startIndex, sectionEnd).trim()
    
    if (sectionContent) {
      sections.push(sectionContent)
    }
  }

  return sections
}

// 解析单个入学季节的申请时间
const parseSingleSemesterSection = (sectionStr) => {
  if (!sectionStr || !sectionStr.trim()) {
    return []
  }

  // 移除入学季节标题部分
  let processedStr = sectionStr
  const semesterMatch = sectionStr.match(/^([^:：]+(?:年|季)(?:入学|申请)?)[:：]?\s*/)
  if (semesterMatch) {
    processedStr = sectionStr.replace(semesterMatch[0], '').trim()
  }

  const nodes = []
  let parts = []

  // 处理多种分隔符格式
  if (processedStr.includes('|')) {
    parts = processedStr.split('|').map(part => part.trim())
  } else if (processedStr.includes(';')) {
    parts = processedStr.split(';').map(part => part.trim())
  } else if (processedStr.includes('\n')) {
    parts = processedStr.split('\n').map(part => part.trim()).filter(part => part)
  } else {
    // 处理没有明确分隔符的复杂情况
    parts = parseComplexTimeString(processedStr)
  }

  for (const part of parts) {
    if (!part.trim()) continue

    // 匹配多种格式：
    // 1. 节点名称(日期)
    // 2. 节点名称: 日期
    // 3. 日期 节点名称
    // 4. 纯节点名称
    let name = ''
    let dateStr = ''

    // 格式1: 节点名称(日期) - 支持空日期
    let match = part.match(/^(.+?)\s*\(([^)]*)\)\s*$/)
    if (match) {
      name = match[1].trim()
      dateStr = match[2].trim()
      // 处理空日期或"/"的情况
      if (dateStr === '/' || dateStr === '' || dateStr === '-') {
        dateStr = ''
      }
    }
    // 格式2: 节点名称: 日期
    else {
      match = part.match(/^(.+?):\s*(.+)$/)
      if (match) {
        name = match[1].trim()
        dateStr = match[2].trim()
        if (dateStr === '/' || dateStr === '-') {
          dateStr = ''
        }
      }
      // 格式3: 日期 节点名称 (YYYY-MM-DD 开头)
      else {
        match = part.match(/^(\d{4}-\d{1,2}-\d{1,2})\s+(.+)$/)
        if (match) {
          dateStr = match[1].trim()
          name = match[2].trim()
        }
        // 格式4: 纯节点名称
        else {
          name = part.trim()
          dateStr = ''
        }
      }
    }

    // 解析日期
    let date = null
    if (dateStr) {
      date = parseDate(dateStr)
    }

    // 确定节点状态
    const status = determineNodeStatus(name, date)

    nodes.push({
      name,
      date,
      status,
      originalText: part
    })
  }

  return nodes
}

// 解析复杂的时间字符串（没有明确分隔符的情况）
const parseComplexTimeString = (timeStr) => {
  const parts = []

  // 特殊处理：检查是否包含"节点(日期) 入学季节"的复合模式
  const complexPattern = /^(.+?\([^)]*\))\s*(\d{2,4}年[春夏秋冬]季入学.*)$/
  const complexMatch = timeStr.match(complexPattern)

  if (complexMatch) {
    // 找到复合模式，分割为两个独立节点
    const firstNode = complexMatch[1].trim()
    const secondNode = complexMatch[2].trim()

    if (firstNode) parts.push(firstNode)
    if (secondNode) parts.push(secondNode)

    return parts.filter(part => part && part.trim())
  }

  // 检查另一种复合模式：节点 入学季节: 其他信息
  // 匹配类似 "Round 4 截止(2025-03-14) 26年秋季入学: 开放申请" 的模式
  // 使用更精确的模式，确保第一部分包含括号或特定关键词
  const anotherComplexPattern = /^(.+?(?:\([^)]*\)|round\s+\d+|第\d+轮).*?)\s+(\d{2,4}年[春夏秋冬]季入学[:：].*)$/i
  const anotherComplexMatch = timeStr.match(anotherComplexPattern)

  if (anotherComplexMatch) {
    const firstNode = anotherComplexMatch[1].trim()
    const secondNode = anotherComplexMatch[2].trim()

    if (firstNode) parts.push(firstNode)
    if (secondNode) parts.push(secondNode)

    return parts.filter(part => part && part.trim())
  }

  // 第一步：提取所有带括号的时间节点
  const timeNodePattern = /([^()]+?)\(([^)]*)\)/g
  let match
  const extractedNodes = []

  while ((match = timeNodePattern.exec(timeStr)) !== null) {
    extractedNodes.push({
      fullMatch: match[0],
      name: match[1].trim(),
      date: match[2].trim(),
      startIndex: match.index,
      endIndex: match.index + match[0].length
    })
  }

  // 第二步：处理提取的节点和剩余文本
  if (extractedNodes.length > 0) {
    let lastEndIndex = 0

    for (const node of extractedNodes) {
      // 处理节点前的文本
      if (node.startIndex > lastEndIndex) {
        const beforeText = timeStr.substring(lastEndIndex, node.startIndex).trim()
        if (beforeText && isValidTimelineNode(beforeText)) {
          parts.push(beforeText)
        }
      }

      // 添加当前节点
      parts.push(node.fullMatch)
      lastEndIndex = node.endIndex
    }

    // 处理最后一个节点后的文本
    if (lastEndIndex < timeStr.length) {
      const afterText = timeStr.substring(lastEndIndex).trim()
      if (afterText && isValidTimelineNode(afterText)) {
        parts.push(afterText)
      }
    }
  } else {
    // 没有带括号的节点，尝试其他解析方式

    // 尝试按常见模式分割
    const patterns = [
      // 匹配 "年季入学:" 模式
      /(\d{2,4}年[春夏秋冬]季入学[:：][^年]*?)(?=\d{2,4}年[春夏秋冬]季入学|$)/g,
      // 匹配 "Round X" 模式
      /(Round\s+\d+[^R]*?)(?=Round\s+\d+|$)/gi,
      // 匹配日期开头的模式
      /(\d{4}-\d{1,2}-\d{1,2}[^0-9]*?)(?=\d{4}-\d{1,2}-\d{1,2}|$)/g
    ]

    for (const pattern of patterns) {
      const matches = [...timeStr.matchAll(pattern)]
      if (matches.length > 0) {
        parts.push(...matches.map(m => m[1].trim()))
        break
      }
    }

    // 如果还是没有匹配到，将整个字符串作为一个节点
    if (parts.length === 0) {
      parts.push(timeStr)
    }
  }

  return parts.filter(part => part && part.trim())
}

// 检查文本是否是有效的时间线节点
const isValidTimelineNode = (text) => {
  const lowerText = text.toLowerCase()

  // 包含时间相关关键词
  const timeKeywords = [
    '入学', '申请', '开放', '截止', '开始', '结束', 'deadline', 'due', 'open', 'close',
    'round', '轮', '阶段', 'phase', 'stage', '季', 'semester', 'term'
  ]

  return timeKeywords.some(keyword => lowerText.includes(keyword)) ||
         /\d{4}[-/]\d{1,2}[-/]\d{1,2}/.test(text) || // 包含日期
         /\d{2,4}年/.test(text) // 包含年份
}

// 过滤后的时间节点（排除入学季节信息）
const filteredTimelineNodes = computed(() => {
  return timelineNodes.value.filter(node => {
    // 过滤掉可能是入学季节信息的节点
    const name = node.name.toLowerCase()
    // 保留包含申请相关关键词的节点
    return name.includes('申请') ||
           name.includes('截止') ||
           name.includes('开放') ||
           name.includes('开始') ||
           name.includes('结束') ||
           name.includes('round') ||
           name.includes('轮') ||
           name.includes('deadline') ||
           name.includes('due') ||
           (!name.includes('入学') && !name.includes('季'))
  })
})

// 解析日期字符串
const parseDate = (dateStr) => {
  if (!dateStr || dateStr.trim() === '' || dateStr === '/' || dateStr === '-') {
    return null
  }

  try {
    // 处理常见的日期格式
    const cleanDateStr = dateStr.trim()

    // 处理特殊标记
    if (cleanDateStr === '/' || cleanDateStr === '-' || cleanDateStr.toLowerCase() === 'tbd' || cleanDateStr.toLowerCase() === 'tba') {
      return null
    }

    // 格式：YYYY-MM-DD
    if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(cleanDateStr)) {
      const date = new Date(cleanDateStr)
      return isNaN(date.getTime()) ? null : date
    }

    // 格式：YYYY/MM/DD
    if (/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(cleanDateStr)) {
      const date = new Date(cleanDateStr.replace(/\//g, '-'))
      return isNaN(date.getTime()) ? null : date
    }

    // 格式：MM-DD-YYYY
    if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(cleanDateStr)) {
      const parts = cleanDateStr.split('-')
      const date = new Date(`${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`)
      return isNaN(date.getTime()) ? null : date
    }

    // 格式：MM/DD/YYYY
    if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(cleanDateStr)) {
      const parts = cleanDateStr.split('/')
      const date = new Date(`${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`)
      return isNaN(date.getTime()) ? null : date
    }

    // 其他格式尝试直接解析
    const parsed = new Date(cleanDateStr)
    return isNaN(parsed.getTime()) ? null : parsed
  } catch (error) {
    console.warn('日期解析失败:', dateStr, error)
    return null
  }
}

// 确定节点状态
const determineNodeStatus = (name, date) => {
  const now = new Date()
  const nameStr = name.toLowerCase()

  // 如果没有日期，根据名称判断
  if (!date) {
    if (nameStr.includes('开放') || nameStr.includes('开始') || nameStr.includes('open')) {
      return 'active'
    }
    if (nameStr.includes('截止') || nameStr.includes('结束') || nameStr.includes('deadline') || nameStr.includes('due') || nameStr.includes('round')) {
      return 'pending'
    }
    return 'unknown'
  }

  // 根据日期和当前时间判断状态
  const timeDiff = date.getTime() - now.getTime()
  const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))

  if (daysDiff < 0) {
    // 已过期
    if (nameStr.includes('截止') || nameStr.includes('结束') || nameStr.includes('deadline') || nameStr.includes('due') || nameStr.includes('round')) {
      return 'expired'
    } else {
      return 'completed'
    }
  } else if (daysDiff <= 30) {
    // 30天内即将到期
    if (nameStr.includes('截止') || nameStr.includes('结束') || nameStr.includes('deadline') || nameStr.includes('due') || nameStr.includes('round')) {
      return 'active'
    } else {
      return 'active'
    }
  } else {
    // 未来时间
    if (nameStr.includes('开放') || nameStr.includes('开始') || nameStr.includes('open')) {
      return 'pending'
    } else {
      return 'pending'
    }
  }
}

// 格式化日期显示
const formatDate = (date) => {
  if (!date) return ''

  try {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    // 如果是当年，只显示月日
    const currentYear = new Date().getFullYear()
    if (year === currentYear) {
      return `${month}-${day}`
    }

    return `${year}-${month}-${day}`
  } catch (error) {
    return ''
  }
}

// 获取节点状态样式类
const getNodeStatusClass = (status) => {
  const baseClass = 'inline-flex items-center'

  switch (status) {
    case 'completed':
      return `${baseClass} bg-green-50 text-green-700 border border-green-200`
    case 'active':
      return `${baseClass} bg-primary/10 text-primary border border-primary/20`
    case 'pending':
      return `${baseClass} bg-yellow-50 text-yellow-700 border border-yellow-200`
    case 'expired':
      return `${baseClass} bg-red-50 text-red-700 border border-red-200`
    default:
      return `${baseClass} bg-gray-50 text-gray-600 border border-gray-200`
  }
}

// 获取节点圆点样式类
const getNodeDotClass = (status) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500 border-green-500 text-white'
    case 'active':
      return 'bg-primary border-primary text-white'
    case 'pending':
      return 'bg-yellow-500 border-yellow-500 text-white'
    case 'expired':
      return 'bg-red-500 border-red-500 text-white'
    default:
      return 'bg-gray-400 border-gray-400 text-white'
  }
}

// 获取节点图标
const getNodeIcon = (status) => {
  switch (status) {
    case 'completed':
      return 'check'
    case 'active':
      return 'schedule'
    case 'pending':
      return 'hourglass_empty'
    case 'expired':
      return 'close'
    default:
      return 'help_outline'
  }
}

// 获取节点标题样式类
const getNodeTitleClass = (status) => {
  switch (status) {
    case 'completed':
      return 'text-green-800'
    case 'active':
      return 'text-primary'
    case 'pending':
      return 'text-yellow-800'
    case 'expired':
      return 'text-red-800'
    default:
      return 'text-gray-700'
  }
}

// 获取节点日期样式类
const getNodeDateClass = (status) => {
  switch (status) {
    case 'completed':
      return 'text-green-600'
    case 'active':
      return 'text-primary'
    case 'pending':
      return 'text-yellow-600'
    case 'expired':
      return 'text-red-600'
    default:
      return 'text-gray-500'
  }
}

// 获取状态点样式类
const getStatusDotClass = (status) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500'
    case 'active':
      return 'bg-primary'
    case 'pending':
      return 'bg-yellow-500'
    case 'expired':
      return 'bg-red-500'
    default:
      return 'bg-gray-400'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'active':
      return '进行中'
    case 'pending':
      return '待开始'
    case 'expired':
      return '已截止'
    default:
      return '未知'
  }
}



</script>

<style scoped>
.application-timeline {
  @apply w-full;
}

.semester-title {
  @apply border-b border-gray-100 pb-3;
}

.semester-title h3 {
  @apply text-primary;
}

.compact-timeline {
  @apply overflow-x-auto;
}

.full-timeline {
  @apply relative;
}

/* 弹窗模式下的紧凑样式 */
.compact-dialog .full-timeline {
  @apply text-sm;
}

.compact-dialog .semester-title {
  @apply border-b border-purple-100 pb-2;
}

.compact-dialog .semester-title h3 {
  @apply text-purple-600;
}

/* 自定义滚动条样式 */
.compact-timeline::-webkit-scrollbar {
  height: 4px;
}

.compact-timeline::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.compact-timeline::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded;
}

.compact-timeline::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .full-timeline .space-y-4 {
    @apply space-y-3;
  }
  
  .full-timeline .bg-white {
    @apply p-2;
  }
}
</style>
