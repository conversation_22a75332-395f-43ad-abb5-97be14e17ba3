<template>
  <div 
    :class="[
      'user-avatar rounded-full flex items-center justify-center overflow-hidden',
      sizeClass,
      customClass
    ]"
    :style="avatarStyle"
  >
    <!-- 显示头像图片 -->
    <img
      v-if="avatarUrl && !imageError"
      :src="avatarUrl"
      :alt="displayName"
      class="w-full h-full object-cover"
      @error="handleImageError"
      @load="handleImageLoad"
    />
    <!-- 显示首字母 -->
    <span
      v-else
      :class="['font-medium text-white', textSizeClass]"
    >
      {{ userInitial }}
    </span>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  // 头像URL
  avatarUrl: {
    type: String,
    default: ''
  },
  // 用户名称（用于显示首字母）
  displayName: {
    type: String,
    default: ''
  },
  // 头像大小
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large', 'extra-large'].includes(value)
  },
  // 自定义CSS类
  customClass: {
    type: String,
    default: ''
  },
  // 是否使用紫色渐变背景
  useGradient: {
    type: Boolean,
    default: true
  }
})

// 图片加载错误状态
const imageError = ref(false)

// 监听头像URL变化，重置错误状态
watch(() => props.avatarUrl, () => {
  imageError.value = false
})

// 处理图片加载错误
const handleImageError = () => {
  imageError.value = true
}

// 处理图片加载成功
const handleImageLoad = () => {
  imageError.value = false
}

// 获取用户名首字母
const userInitial = computed(() => {
  if (!props.displayName) return '?'
  return props.displayName.charAt(0).toUpperCase()
})

// 头像大小样式类
const sizeClass = computed(() => {
  const sizeMap = {
    'small': 'w-8 h-8',
    'medium': 'w-9 h-9',
    'large': 'w-12 h-12',
    'extra-large': 'w-16 h-16'
  }
  return sizeMap[props.size] || sizeMap.medium
})

// 文字大小样式类
const textSizeClass = computed(() => {
  const textSizeMap = {
    'small': 'text-xs',
    'medium': 'text-sm',
    'large': 'text-lg',
    'extra-large': 'text-xl'
  }
  return textSizeMap[props.size] || textSizeMap.medium
})

// 头像样式
const avatarStyle = computed(() => {
  if (props.avatarUrl && !imageError.value) {
    return {}
  }
  
  // 如果没有头像或加载失败，使用渐变背景
  if (props.useGradient) {
    return {
      background: 'linear-gradient(to right, #4F46E5, #818CF8)'
    }
  }
  
  return {
    backgroundColor: '#6B7280'
  }
})
</script>

<style scoped>
.user-avatar {
  transition: all 0.2s ease-in-out;
}

.user-avatar:hover {
  transform: scale(1.05);
}

/* 确保图片不会超出容器 */
.user-avatar img {
  border-radius: inherit;
}
</style>
