<template>
  <div class="floating-input-container">
    <div class="floating-input-wrapper" :class="{ 'focused': isFocused, 'has-value': hasValue }">
      <input
        ref="inputRef"
        :value="modelValue"
        :placeholder="isFocused ? placeholder : ''"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        class="floating-input"
      />
      <label class="floating-label">{{ label }}</label>
      <div v-if="prefixIcon" class="input-prefix">
        <span class="material-icons-outlined">{{ prefixIcon }}</span>
      </div>
      <div v-if="suffixIcon || clearable && hasValue" class="input-suffix">
        <span v-if="clearable && hasValue" class="material-icons-outlined clear-icon" @click="handleClear">close</span>
        <span v-else-if="suffixIcon" class="material-icons-outlined">{{ suffixIcon }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    required: true
  },
  placeholder: {
    type: String,
    default: ''
  },
  prefixIcon: {
    type: String,
    default: ''
  },
  suffixIcon: {
    type: String,
    default: ''
  },
  clearable: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'clear', 'keydown'])

const inputRef = ref(null)
const isFocused = ref(false)

const hasValue = computed(() => {
  return props.modelValue && props.modelValue.length > 0
})

const handleInput = (event) => {
  emit('update:modelValue', event.target.value)
}

const handleFocus = (event) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  isFocused.value = false
  emit('blur', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleKeydown = (event) => {
  emit('keydown', event)
}

// 暴露focus方法
const focus = () => {
  inputRef.value?.focus()
}

defineExpose({
  focus
})
</script>

<style scoped>
.floating-input-container {
  width: 100%;
}

.floating-input-wrapper {
  position: relative;
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 0.5rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 40px;
  display: flex;
  align-items: center;
}

.floating-input-wrapper:hover {
  border-color: #D1D5DB;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.floating-input-wrapper.focused {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.floating-input {
  width: 100%;
  height: 100%;
  padding: 1rem 1rem 0.375rem 1rem;
  border: none;
  outline: none;
  background: transparent;
  font-size: 0.875rem;
  color: #1F2937;
  font-family: inherit;
}

.floating-input-wrapper.has-value .floating-input,
.floating-input-wrapper.focused .floating-input {
  padding-top: 1rem;
  padding-bottom: 0.375rem;
}

.floating-label {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.875rem;
  color: #9CA3AF;
  pointer-events: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: #FFFFFF;
  padding: 0 0.25rem;
  z-index: 1;
}

.floating-input-wrapper.focused .floating-label,
.floating-input-wrapper.has-value .floating-label {
  top: 0;
  transform: translateY(-50%);
  font-size: 0.65rem;
  color: #4F46E5;
  font-weight: 500;
}

.input-prefix {
  position: absolute;
  left: 1rem;
  top: 60%;
  transform: translateY(-50%);
  color: #6B7280;
  z-index: 2;
  pointer-events: none;
}

.floating-input-wrapper.focused .input-prefix,
.floating-input-wrapper.has-value .input-prefix {
  top: 60%;
}

.floating-input-wrapper:has(.input-prefix) .floating-input {
  padding-left: 3rem;
}

.floating-input-wrapper:has(.input-prefix) .floating-label {
  left: 3rem;
}

.floating-input-wrapper.focused:has(.input-prefix) .floating-label,
.floating-input-wrapper.has-value:has(.input-prefix) .floating-label {
  left: 1rem;
}

.input-suffix {
  position: absolute;
  right: 1rem;
  top: 52%;
  transform: translateY(-50%);
  color: #6B7280;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.floating-input-wrapper.focused .input-suffix,
.floating-input-wrapper.has-value .input-suffix {
  top: 52%;
}

.floating-input-wrapper:has(.input-suffix) .floating-input {
  padding-right: 3rem;
}

.clear-icon {
  cursor: pointer;
  transition: color 0.2s ease;
  font-size: 18px;
}

.clear-icon:hover {
  color: #4F46E5;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-size: 18px;
  line-height: 1;
}

/* 尺寸变体 */
.floating-input-wrapper.size-small {
  min-height: 48px;
}

.floating-input-wrapper.size-small .floating-input {
  font-size: 0.875rem;
  padding: 1.25rem 1rem 0.5rem 1rem;
}

.floating-input-wrapper.size-large {
  min-height: 64px;
}

.floating-input-wrapper.size-large .floating-input {
  font-size: 1.125rem;
  padding: 1.75rem 1rem 0.5rem 1rem;
}

/* 响应式 */
@media (max-width: 768px) {
  .floating-input-wrapper {
    min-height: 52px;
  }
  
  .floating-input {
    font-size: 16px; /* 防止iOS Safari缩放 */
  }
}
</style> 