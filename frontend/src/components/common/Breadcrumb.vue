<template>
  <div class="flex items-center text-sm">
    <router-link to="/" class="text-primary hover:text-primary-dark">
      TunshuEdu
    </router-link>
    <template v-for="(item, index) in breadcrumbs" :key="index">
      <span class="mx-2 text-gray-400">&gt;</span>
      <router-link
        v-if="item.path && !item.isLast"
        :to="item.path"
        class="text-primary hover:text-primary-dark"
      >
        {{ item.title }}
      </router-link>
      <span v-else class="text-gray-600">{{ item.title }}</span>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

interface Breadcrumb {
  title: string
  path?: string
  isLast?: boolean
}

const breadcrumbs = computed<Breadcrumb[]>(() => {
  const { matched, params } = route
  return matched
    .filter(item => item.meta?.title)
    .map((item, index, array) => {
      const isLast = index === array.length - 1
      let title = item.meta?.title as string
      
      // 如果是学生详情页面,使用动态标题
      if (item.name === 'CRMStudentDetail' && params.id) {
        title = '学生详情'
      }
      
      return {
        title,
        path: item.path,
        isLast
      }
    })
})
</script> 