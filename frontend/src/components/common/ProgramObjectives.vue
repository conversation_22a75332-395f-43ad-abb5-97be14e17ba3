<template>
  <div class="program-objectives">
    <!-- 标题区域 -->
    <h4 v-if="showTitle" :class="titleClasses">
      <span class="material-icons-outlined text-sm mr-2">
        {{ titleIcon }}
      </span>
      {{ titleText }}
    </h4>

    <!-- 内容区域 -->
    <div v-if="hasContent" :class="contentClasses">
      <div :class="textClasses">
        {{ cleanedObjectives }}
      </div>
    </div>
    
    <!-- 无内容提示 -->
    <div v-else :class="emptyClasses">
      <div class="text-sm text-gray-400 text-center py-4">
        {{ emptyMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { cleanProgramObjectives } from '@/utils/textCleaner'
import { shouldShowField, formatFieldValue } from '@/utils/programUtils'

// Props定义
const props = defineProps({
  // 专业目标内容
  objectives: {
    type: String,
    default: ''
  },
  
  // 是否显示标题
  showTitle: {
    type: Boolean,
    default: true
  },
  
  // 显示模式：'default' | 'compact' | 'dialog'
  mode: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'compact', 'dialog'].includes(value)
  },
  
  // 是否在对话框中使用
  inDialog: {
    type: Boolean,
    default: false
  },
  
  // 自定义空内容提示
  emptyMessage: {
    type: String,
    default: '暂无专业目标信息'
  },

  // 自定义标题文本
  titleText: {
    type: String,
    default: '专业目标'
  },

  // 自定义CSS类
  customClass: {
    type: String,
    default: ''
  }
})

// 计算属性：是否有内容
const hasContent = computed(() => {
  return shouldShowField(props.objectives)
})

// 计算属性：清理后的专业目标内容
const cleanedObjectives = computed(() => {
  if (!props.objectives) return ''

  // 先使用formatFieldValue进行基础格式化，再使用cleanProgramObjectives进行专业清理
  const formattedValue = formatFieldValue(props.objectives, props.emptyMessage)
  return cleanProgramObjectives(formattedValue)
})

// 计算属性：标题图标
const titleIcon = computed(() => {
  return props.titleText === '培养目标' ? 'lightbulb' : 'flag'
})

// 计算属性：标题样式类
const titleClasses = computed(() => {
  const baseClasses = ['flex', 'items-center']

  if (props.mode === 'compact') {
    baseClasses.push('text-xs', 'font-semibold', 'mb-1.5', 'text-gray-800')
  } else {
    baseClasses.push('font-semibold', 'text-gray-800', 'mb-4')
  }

  return baseClasses.join(' ')
})

// 计算属性：内容容器样式类
const contentClasses = computed(() => {
  const baseClasses = []
  
  if (props.mode === 'dialog' || props.inDialog) {
    baseClasses.push('bg-white p-4 rounded-lg')
  } else if (props.mode === 'compact') {
    baseClasses.push('bg-gray-50 p-3 rounded-lg')
  } else {
    baseClasses.push('bg-white p-4 rounded-lg')
  }
  
  if (props.customClass) {
    baseClasses.push(props.customClass)
  }
  
  return baseClasses.join(' ')
})

// 计算属性：文本样式类
const textClasses = computed(() => {
  const baseClasses = ['text-gray-800', 'whitespace-pre-line', 'leading-relaxed']
  
  if (props.mode === 'compact') {
    baseClasses.push('text-xs')
  } else {
    baseClasses.push('text-sm')
  }
  
  return baseClasses.join(' ')
})

// 计算属性：空内容样式类
const emptyClasses = computed(() => {
  const baseClasses = []
  
  if (props.mode === 'dialog' || props.inDialog) {
    baseClasses.push('bg-white p-4 rounded-lg')
  } else if (props.mode === 'compact') {
    baseClasses.push('bg-gray-50 p-3 rounded-lg')
  } else {
    baseClasses.push('bg-white p-4 rounded-lg')
  }
  
  if (props.customClass) {
    baseClasses.push(props.customClass)
  }
  
  return baseClasses.join(' ')
})
</script>

<style scoped>
.program-objectives {
  /* 基础样式 */
}

/* 紧凑模式样式 */
.program-objectives.compact {
  font-size: 0.875rem;
}

/* 对话框模式样式 */
.program-objectives.dialog {
  /* 对话框特定样式 */
}
</style>
