<template>
  <div class="language-requirements" :class="{ 'dialog-mode': inDialog }">
    <!-- 紧凑模式：简洁显示 -->
    <div v-if="compact" class="compact-requirements">
      <div class="flex flex-wrap gap-2">
        <div
          v-for="(exam, index) in parsedRequirements"
          :key="index"
          class="exam-badge"
          :class="[getExamBadgeClass(exam.type), { 'dialog-badge': inDialog }]"
        >
          <div class="exam-content">
            <div class="exam-type">{{ exam.displayName }}</div>
            <div class="exam-score">{{ exam.totalScore }}</div>
          </div>
          <div v-if="exam.hasSubScores" class="exam-sub-scores">
            <span
              v-for="(score, skill) in exam.subScores"
              :key="skill"
              class="sub-score"
              :title="`${getSkillName(skill)}: ${score}`"
            >
              {{ getSkillShort(skill) }}{{ score }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 完整模式：详细展示 -->
    <div v-else class="full-requirements">
      <div class="space-y-3">
        <div 
          v-for="(exam, index) in parsedRequirements" 
          :key="index"
          class="exam-card"
        >
          <!-- 考试类型头部 -->
          <div class="exam-header" :class="getExamHeaderClass(exam.type)">
            <div class="flex items-center">
              <span class="exam-icon material-icons-outlined">
                {{ getExamIcon(exam.type) }}
              </span>
              <span class="exam-name">{{ exam.displayName }}</span>
            </div>
            <div class="total-score">
              <span class="score-label">总分要求</span>
              <span class="score-value">{{ exam.totalScore }}</span>
            </div>
          </div>

          <!-- 小分要求 -->
          <div v-if="exam.hasSubScores" class="sub-scores-grid">
            <div 
              v-for="(score, skill) in exam.subScores" 
              :key="skill"
              class="sub-score-item"
            >
              <span class="skill-name">{{ getSkillName(skill) }}</span>
              <span class="skill-score" :class="score === '/' ? 'text-gray-400' : 'text-gray-700'">
                {{ score === '/' ? '不限' : score }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="parsedRequirements.length === 0" class="text-center py-4">
      <span class="text-gray-400 text-sm">暂无语言要求信息</span>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'

// Props定义
const props = defineProps({
  // 语言要求字符串
  languageRequirements: {
    type: String,
    default: ''
  },
  // 是否使用紧凑模式
  compact: {
    type: Boolean,
    default: false
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  },
  // 是否在弹窗中使用
  inDialog: {
    type: Boolean,
    default: false
  }
})

// 清理HTML标签和特殊字符
const cleanHtmlText = (text) => {
  if (!text) return ''
  
  return text
    .replace(/<br\s*\/?>/gi, '\n') // 替换<br/>为换行
    .replace(/<[^>]*>/g, '') // 移除所有HTML标签
    .replace(/&nbsp;/g, ' ') // 替换&nbsp;为空格
    .replace(/&amp;/g, '&') // 替换&amp;为&
    .replace(/&lt;/g, '<') // 替换&lt;为<
    .replace(/&gt;/g, '>') // 替换&gt;为>
    .trim()
}

// 解析语言要求
const parsedRequirements = computed(() => {
  if (!props.languageRequirements || props.languageRequirements.trim() === '') {
    return []
  }

  const requirements = []

  try {
    let examBlocks = []
    
    // 优先按 <br/> 分割（原始HTML格式）
    if (props.languageRequirements.includes('<br/>')) {
      examBlocks = props.languageRequirements.split('<br/>').filter(block => block.trim())
    }
    // 如果已经被清理为换行符，按换行分割
    else {
      const cleanedText = cleanHtmlText(props.languageRequirements)
      if (cleanedText.includes('\n')) {
        examBlocks = cleanedText.split('\n').filter(block => block.trim())
      } else {
        // 尝试按考试类型分割（处理连续文本）
        const examTypePattern = /(雅思|托福|IELTS|TOEFL|GRE|GMAT|PTE|Duolingo)/gi
        let matches = []
        let match

        while ((match = examTypePattern.exec(cleanedText)) !== null) {
          matches.push({
            type: match[1],
            index: match.index
          })
        }

        if (matches.length > 0) {
          for (let i = 0; i < matches.length; i++) {
            const start = matches[i].index
            const end = i < matches.length - 1 ? matches[i + 1].index : cleanedText.length
            const block = cleanedText.substring(start, end).trim()
            if (block) {
              examBlocks.push(block)
            }
          }
        } else {
          // 如果没有找到考试类型，将整个文本作为一个块
          examBlocks = [cleanedText]
        }
      }
    }

    for (const block of examBlocks) {
      const exam = parseExamBlock(block.trim())
      if (exam) {
        requirements.push(exam)
      }
    }
  } catch (error) {
    console.warn('解析语言要求失败:', error)
  }

  return requirements
})

// 解析单个考试块
const parseExamBlock = (block) => {
  if (!block) return null

  try {
    // 先清理HTML标签
    const cleanedBlock = cleanHtmlText(block)
    
    // 按 | 分割，获取各个部分
    const parts = cleanedBlock.split('|').map(part => part.trim())

    // 识别考试类型（从第一部分）
    let examType = 'unknown'
    let displayName = '其他'
    const firstPart = parts[0] || ''

    if (/雅思|IELTS/i.test(firstPart)) {
      examType = 'ielts'
      displayName = '雅思'
    } else if (/托福|TOEFL/i.test(firstPart)) {
      examType = 'toefl'
      displayName = '托福'
    } else if (/PTE/i.test(firstPart)) {
      examType = 'pte'
      displayName = 'PTE'
    } else if (/GRE/i.test(firstPart)) {
      examType = 'gre'
      displayName = 'GRE'
    } else if (/GMAT/i.test(firstPart)) {
      examType = 'gmat'
      displayName = 'GMAT'
    } else if (/Duolingo/i.test(firstPart)) {
      examType = 'duolingo'
      displayName = 'Duolingo'
    } else {
      // 如果第一部分没有明确的考试类型，尝试从整个块中提取
      if (/雅思|IELTS/i.test(cleanedBlock)) {
        examType = 'ielts'
        displayName = '雅思'
      } else if (/托福|TOEFL/i.test(cleanedBlock)) {
        examType = 'toefl'
        displayName = '托福'
      } else if (/PTE/i.test(cleanedBlock)) {
        examType = 'pte'
        displayName = 'PTE'
      } else if (/GRE/i.test(cleanedBlock)) {
        examType = 'gre'
        displayName = 'GRE'
      } else if (/GMAT/i.test(cleanedBlock)) {
        examType = 'gmat'
        displayName = 'GMAT'
      } else if (/Duolingo/i.test(cleanedBlock)) {
        examType = 'duolingo'
        displayName = 'Duolingo'
      }
    }

    // 解析总分要求
    let totalScore = '未知'
    for (const part of parts) {
      const totalScoreMatch = part.match(/总分要求[:\s]*(\d+(?:\.\d+)?)/i)
      if (totalScoreMatch) {
        totalScore = totalScoreMatch[1]
        break
      }
    }

    // 解析小分要求
    const subScores = {}
    let hasSubScores = false

    // 查找包含小分要求的部分
    for (const part of parts) {
      if (/小分要求/i.test(part)) {
        // 提取小分要求内容
        const subScoreMatch = part.match(/小分要求[:\s]*(.+)/i)
        if (subScoreMatch) {
          const subScoreText = subScoreMatch[1]

          // 解析各项技能分数
          const skills = ['听力', '阅读', '写作', '口语']
          const skillsEn = ['listening', 'reading', 'writing', 'speaking']

          for (let i = 0; i < skills.length; i++) {
            const skill = skills[i]
            const skillEn = skillsEn[i]

            // 改进的正则表达式，处理多种格式
            // 匹配格式：听力: 7; 或 听力: 76; 或 听力:7 等
            const skillPattern = new RegExp(`${skill}\\s*[:\\s]\\s*([\\d\\.]+|/)\\s*[;\\s]*`, 'i')
            const skillMatch = subScoreText.match(skillPattern)

            if (skillMatch) {
              subScores[skillEn] = skillMatch[1]
              hasSubScores = true
            }
          }
        }
        break
      }
    }

    // 如果没有找到结构化的小分要求，尝试全局搜索
    if (!hasSubScores) {
      const fullText = cleanedBlock
      const skills = ['听力', '阅读', '写作', '口语']
      const skillsEn = ['listening', 'reading', 'writing', 'speaking']

      for (let i = 0; i < skills.length; i++) {
        const skill = skills[i]
        const skillEn = skillsEn[i]

        const skillPattern = new RegExp(`${skill}\\s*[:\\s]\\s*([\\d\\.]+|/)`, 'i')
        const skillMatch = fullText.match(skillPattern)

        if (skillMatch) {
          subScores[skillEn] = skillMatch[1]
          hasSubScores = true
        }
      }
    }

    return {
      type: examType,
      displayName,
      totalScore,
      subScores,
      hasSubScores,
      originalText: block
    }
  } catch (error) {
    console.warn('解析考试块失败:', error, block)
    return null
  }
}

// 获取考试徽章样式类
const getExamBadgeClass = (examType) => {
  switch (examType) {
    case 'ielts':
      return 'ielts-badge'
    case 'toefl':
      return 'toefl-badge'
    case 'pte':
      return 'pte-badge'
    case 'gre':
      return 'gre-badge'
    case 'gmat':
      return 'gmat-badge'
    case 'duolingo':
      return 'duolingo-badge'
    default:
      return 'default-badge'
  }
}

// 获取考试头部样式类
const getExamHeaderClass = (examType) => {
  switch (examType) {
    case 'ielts':
      return 'ielts-header'
    case 'toefl':
      return 'toefl-header'
    case 'pte':
      return 'pte-header'
    case 'gre':
      return 'gre-header'
    case 'gmat':
      return 'gmat-header'
    case 'duolingo':
      return 'duolingo-header'
    default:
      return 'default-header'
  }
}

// 获取考试图标
const getExamIcon = (examType) => {
  switch (examType) {
    case 'ielts':
    case 'toefl':
    case 'pte':
    case 'duolingo':
      return 'language'
    case 'gre':
    case 'gmat':
      return 'school'
    default:
      return 'quiz'
  }
}

// 获取技能名称
const getSkillName = (skill) => {
  const skillMap = {
    listening: '听力',
    reading: '阅读',
    writing: '写作',
    speaking: '口语'
  }
  return skillMap[skill] || skill
}

// 获取技能简称
const getSkillShort = (skill) => {
  const skillMap = {
    listening: '听',
    reading: '读',
    writing: '写',
    speaking: '说'
  }
  return skillMap[skill] || skill.charAt(0).toUpperCase()
}

// 监听 props 变化
watch(() => props.languageRequirements, () => {
  // 只在开发环境且出现解析错误时输出调试信息
  if (process.env.NODE_ENV === 'development' && props.languageRequirements && parsedRequirements.value.length === 0) {
    console.log('[系统] 语言要求解析可能存在问题')
  }
}, { immediate: true })
</script>

<style scoped>
.language-requirements {
  @apply w-full;
}

/* 紧凑模式样式 */
.compact-requirements {
  @apply overflow-x-auto;
}

.exam-badge {
  @apply inline-flex flex-col items-center px-3 py-2 rounded-lg border text-sm font-medium;
  @apply bg-white shadow-sm;
  min-width: 80px;
}

/* 弹窗模式下的徽章样式 */
.dialog-mode .exam-badge.dialog-badge {
  @apply text-xs px-2 py-1;
  font-size: 0.75rem;
  min-width: 60px;
}

.exam-badge .exam-content {
  @apply flex flex-col items-center;
}

.exam-badge .exam-type {
  @apply font-semibold mb-1;
}

.exam-badge .exam-score {
  @apply font-bold text-center;
}

.exam-badge .exam-sub-scores {
  @apply flex items-center justify-center flex-wrap gap-1 text-xs opacity-75 mt-1;
}

.exam-badge .sub-score {
  @apply bg-black bg-opacity-10 px-1 py-0.5 rounded;
}

/* 考试类型特定样式 - 统一紫色主题 */
.ielts-badge {
  @apply border-purple-200 text-purple-800;
  background: linear-gradient(135deg, #faf5ff 0%, #e9d5ff 100%);
}

.toefl-badge {
  @apply border-purple-200 text-purple-800;
  background: linear-gradient(135deg, #faf5ff 0%, #e9d5ff 100%);
}

.pte-badge {
  @apply border-purple-200 text-purple-800;
  background: linear-gradient(135deg, #faf5ff 0%, #e9d5ff 100%);
}

.gre-badge {
  @apply border-purple-200 text-purple-800;
  background: linear-gradient(135deg, #faf5ff 0%, #e9d5ff 100%);
}

.gmat-badge {
  @apply border-purple-200 text-purple-800;
  background: linear-gradient(135deg, #faf5ff 0%, #e9d5ff 100%);
}

.duolingo-badge {
  @apply border-purple-200 text-purple-800;
  background: linear-gradient(135deg, #faf5ff 0%, #e9d5ff 100%);
}

.default-badge {
  @apply border-gray-200 text-gray-800;
  background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
}

/* 完整模式样式 */
.full-requirements {
  @apply space-y-3;
}

.exam-card {
  @apply bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden;
}

.exam-header {
  @apply px-4 py-3 flex items-center justify-between;
}

.exam-header .exam-icon {
  @apply mr-2 text-lg;
}

.exam-header .exam-name {
  @apply font-semibold text-sm;
}

.exam-header .total-score {
  @apply text-right;
}

.exam-header .score-label {
  @apply block text-xs opacity-75;
}

.exam-header .score-value {
  @apply block text-lg font-bold;
}

/* 考试头部特定样式 - 统一紫色主题 */
.ielts-header {
  @apply bg-purple-50 text-purple-800 border-b border-purple-100;
}

.toefl-header {
  @apply bg-purple-50 text-purple-800 border-b border-purple-100;
}

.pte-header {
  @apply bg-purple-50 text-purple-800 border-b border-purple-100;
}

.gre-header {
  @apply bg-purple-50 text-purple-800 border-b border-purple-100;
}

.gmat-header {
  @apply bg-purple-50 text-purple-800 border-b border-purple-100;
}

.duolingo-header {
  @apply bg-purple-50 text-purple-800 border-b border-purple-100;
}

.default-header {
  @apply bg-gray-50 text-gray-800 border-b border-gray-100;
}

/* 小分网格 */
.sub-scores-grid {
  @apply grid grid-cols-2 sm:grid-cols-4 gap-3 p-4;
}

.sub-score-item {
  @apply text-center;
}

.sub-score-item .skill-name {
  @apply block text-xs text-gray-500 mb-1;
}

.sub-score-item .skill-score {
  @apply block text-sm font-semibold;
}

/* 弹窗模式样式 */
.dialog-mode .exam-badge {
  @apply text-xs px-2 py-1;
}

.dialog-mode .exam-card {
  @apply shadow-none border-purple-100;
}

.dialog-mode .exam-header {
  @apply px-3 py-2;
}

.dialog-mode .sub-scores-grid {
  @apply p-3 gap-2;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .exam-badge {
    @apply text-xs px-2 py-1;
  }

  .exam-badge .exam-sub-scores {
    @apply hidden;
  }

  .sub-scores-grid {
    @apply grid-cols-2 gap-2 p-3;
  }
}
</style>
