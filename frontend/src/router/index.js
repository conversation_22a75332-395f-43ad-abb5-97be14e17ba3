import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'

// 导入路由模块
import authRoutes from './modules/auth'
import dashboardRoutes from './modules/dashboard'
import clientsRoutes from './modules/clients'
import schoolRoutes from './modules/school'
import writingRoutes from './modules/writing'
import aiToolsRoutes from './modules/ai-tools'

import crmRoutes from './modules/crm'
import enterpriseRoutes from './modules/enterprise'
import accountRoutes from './modules/account'
import userRoutes from './modules/user'
import errorRoutes from './modules/error'

// 是否在控制台输出路由日志（可以根据环境变量控制）
const enableRouteLogging = process.env.NODE_ENV !== 'production'

const routes = [
  // 认证路由
  ...authRoutes,

  // 主应用路由
  {
    path: '/',
    component: () => import('@/components/layout/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      // 各功能模块路由
      ...dashboardRoutes,
      ...clientsRoutes,
      ...schoolRoutes,
      ...writingRoutes,
      ...aiToolsRoutes,

      // CRM 系统路由
      ...crmRoutes,
      // 账户管理路由
      ...accountRoutes,
      // 用户功能路由
      ...userRoutes,
      // 组织管理路由
      ...enterpriseRoutes,



    ]
  },

  // 错误页面路由（放在最后捕获所有未匹配的路径）
  ...errorRoutes
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  // 滚动行为配置
  scrollBehavior(to, from, savedPosition) {
    return new Promise((resolve) => {
      // 等待路由组件渲染完成
      setTimeout(() => {
        // 查找主要内容区域的滚动容器
        const mainContent = document.querySelector('.page-content-wrapper')

        if (mainContent) {
          // 如果有保存的滚动位置（浏览器前进/后退），使用保存的位置
          if (savedPosition) {
            mainContent.scrollTo({
              top: savedPosition.top,
              left: savedPosition.left,
              behavior: 'smooth'
            })
            resolve(savedPosition)
            return
          }

          // 如果路由有hash，滚动到对应元素
          if (to.hash) {
            const targetElement = document.querySelector(to.hash)
            if (targetElement) {
              targetElement.scrollIntoView({ behavior: 'smooth' })
              resolve({ el: to.hash })
              return
            }
          }

          // 默认滚动到页面顶部
          mainContent.scrollTo({
            top: 0,
            left: 0,
            behavior: 'smooth'
          })
        }

        resolve({ top: 0, left: 0 })
      }, 100) // 给组件渲染留出时间
    })
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresOwnerPermission = to.matched.some(record => record.meta.requiresOwnerPermission)

  // 根据环境变量控制日志输出
  if (enableRouteLogging) {
    console.log(`[路由守卫] 导航到: ${to.path}, 需要认证: ${requiresAuth}, 需要Owner权限: ${requiresOwnerPermission}, 令牌状态: ${authStore.token ? '存在' : '不存在'}`)
  }

  if (requiresAuth) {
    if (enableRouteLogging) {
      console.log('[路由守卫] 正在验证身份...')
    }

    try {
      const isAuthenticated = await authStore.checkAuth()

      if (enableRouteLogging) {
        console.log(`[路由守卫] 身份验证结果: ${isAuthenticated ? '通过' : '未通过'}`)
      }

      if (!isAuthenticated) {
        if (enableRouteLogging) {
          console.log('[路由守卫] 未认证，重定向到登录页')
        }
        next({ path: '/login', query: { redirect: to.fullPath } })
        return
      }
    } catch (error) {
      console.error('[路由守卫] 验证过程发生错误:', error)
      next({ path: '/login', query: { redirect: to.fullPath } })
      return
    }
  }

  // 检查owner权限
  if (requiresOwnerPermission) {
    try {
      const { canAccessAccountManagement } = useOrganizationPermissions()
      
      if (!canAccessAccountManagement.value) {
        if (enableRouteLogging) {
          console.log('[路由守卫] 权限不足，重定向到仪表盘')
        }
        next({ path: '/dashboard', query: { error: 'permission_denied' } })
        return
      }
    } catch (error) {
      console.error('[路由守卫] 权限检查过程发生错误:', error)
      next({ path: '/dashboard', query: { error: 'permission_check_failed' } })
      return
    }
  }

  // 如果已登录且访问登录页
  if (authStore.token && (to.path === '/login' || to.path === '/register' || to.path === '/forgot-password')) {
    if (enableRouteLogging) {
      console.log('[路由守卫] 已登录用户访问认证页面，重定向到仪表盘')
    }
    next('/dashboard')
    return
  }

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - TunshuEdu`
  } else {
    document.title = 'TunshuEdu - 留学行业的AI工具箱'
  }

  if (enableRouteLogging) {
    console.log('[路由守卫] 允许导航继续')
  }
  next()
})

export default router