/**
 * 账户管理路由配置
 */

export default [
  {
    path: '/account/recharge',
    name: 'AccountRecharge',
    component: () => import('@/views/account/Recharge.vue'),
    meta: {
      title: '账户充值',
      requiresAuth: true,
      requiresOwnerPermission: true
    }
  },
  {
    path: '/account/payment/:orderNo',
    name: 'Payment',
    component: () => import('@/views/account/Payment.vue'),
    meta: {
      title: '订单支付',
      requiresAuth: true,
      requiresOwnerPermission: true
    }
  },
  {
    path: '/account/payment-result',
    name: 'PaymentResult',
    component: () => import('@/views/account/PaymentResult.vue'),
    meta: {
      title: '支付结果',
      requiresAuth: true
    }
  },
  {
    path: '/account/orders',
    name: 'AccountOrders',
    component: () => import('@/views/account/Orders.vue'),
    meta: {
      title: '我的订单',
      requiresAuth: true
    }
  },
  {
    path: '/account/order-query',
    name: 'AccountOrderQuery',
    component: () => import('@/views/account/OrderQuery.vue'),
    meta: {
      title: '订单查询',
      requiresAuth: true
    }
  },
  {
    path: '/account/profile',
    name: 'AccountProfile',
    component: () => import('@/views/account/Profile.vue'),
    meta: {
      title: '个人资料',
      requiresAuth: true
    }
  }
]
