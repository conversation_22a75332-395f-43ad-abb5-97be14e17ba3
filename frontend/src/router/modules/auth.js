// 认证相关路由
export default [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    // 注册页面重定向到登录页面，保留查询参数
    redirect: to => {
      return { path: '/login', query: to.query }
    },
    meta: { requiresAuth: false }
  },
  {
    path: '/invite',
    name: 'Invite',
    // 邀请页面重定向到登录页面，保留查询参数
    redirect: to => {
      return { path: '/login', query: to.query }
    },
    meta: { requiresAuth: false }
  },
  {
    path: '/auth/wechat/callback',
    name: 'WeChatCallback',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresAuth: false }
  }
]
