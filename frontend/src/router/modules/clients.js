// 客户管理路由
export default [
  {
    path: 'clients',
    name: 'ClientList',
    component: () => import('@/views/clients/ClientList.vue'),
    meta: {
      title: '客户档案',
      requiresAuth: true
    }
  },
  // 注意：确保 'new' 路由在动态路由 ':id' 之前
  {
    path: 'clients/new',
    name: 'NewClient',
    component: () => import('@/views/clients/ClientProfile.vue'),
    meta: {
      title: '新建客户',
      requiresAuth: true
    }
  },
  {
    path: 'clients/:id',
    name: 'ClientProfile',
    component: () => import('@/views/clients/ClientProfile.vue'),
    meta: {
      title: '客户详情',
      requiresAuth: true
    }
  }
]
