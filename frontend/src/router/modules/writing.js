// 文书写作路由
export default [
  {
    path: 'writing/cv',
    name: 'WritingCV',
    component: () => import('@/views/writing/CV.vue'),
    meta: {
      title: '简历写作',
      requiresAuth: true,
      fullscreen: true
    }
  },
  {
    path: 'writing/rl',
    name: 'WritingRL',
    component: () => import('@/views/writing/RL.vue'),
    meta: {
      title: '推荐信写作',
      requiresAuth: true,
      fullscreen: true
    }
  },
  {
    path: 'writing/ps',
    name: 'WritingPS',
    component: () => import('@/views/writing/PS.vue'),
    meta: {
      title: '个人陈述写作',
      requiresAuth: true,
      fullscreen: true
    }
  },
  // 保持向后兼容的旧路由
  {
    path: 'write/cv',
    redirect: '/writing/cv'
  },
  {
    path: 'write/recommendation',
    redirect: '/writing/rl'
  },
  {
    path: 'write/ps',
    redirect: '/writing/ps'
  }
]
