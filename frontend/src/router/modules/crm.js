export default [
  {
    path: 'crm',
    name: 'CRMSystem',
    component: () => import('@/views/crm/CRMSystem.vue'),
    meta: {
      title: 'CRM系统',
      requiresAuth: true,
      keepAlive: false
    }
  },
  
  // === CRM系统路由 ===
  {
    path: 'crm/market-resources',
    name: 'CRMMarketResources',
    component: () => import('@/views/crm/MarketResources.vue'),
    meta: {
      title: '市场资源',
      requiresAuth: true,
      keepAlive: true
    }
  },
  {
    path: 'crm/valid-customers',
    name: 'CRMValidCustomers',
    component: () => import('@/views/crm/ValidCustomers.vue'),
    meta: {
      title: '有效客户',
      requiresAuth: true,
      keepAlive: true
    }
  },

  {
    path: 'crm/customer-management',
    name: 'CRMCustomerManagement',
    component: () => import('@/views/crm/CustomerManagement.vue'),
    meta: {
      title: '签约客户',
      requiresAuth: true,
      keepAlive: true
    }
  },
  
  // === 组织管理模块 ===
  {
    path: 'crm/organization/departments',
    name: 'CRMOrganizationManagement',
    component: () => import('@/views/crm/organization/DepartmentManagement.vue'),
    meta: {
      title: '组织管理',
      requiresAuth: true,
      keepAlive: true
    }
  }
] 