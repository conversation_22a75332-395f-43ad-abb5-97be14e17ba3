# CRM系统设计规范

## 概述

本文档定义了 CRM 系统中所有页面的统一设计标准，以 MarketResources.vue 页面为设计基准，确保整个 CRM 系统界面的一致性和用户体验的统一性。

## 1. 页面布局规范

### 1.1 页面容器结构
```vue
<template>
  <div class="crm-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span>CRM系统</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">页面名称</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">页面标题</h2>
    </div>

    <!-- 主要内容卡片 -->
    <div class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">图标名称</span>
          卡片标题
        </div>
        <div class="flex items-center gap-3">
          <!-- 操作按钮 -->
        </div>
      </div>

      <div class="pro-card-body">
        <!-- 页面内容 -->
      </div>
    </div>
  </div>
</template>
```

### 1.2 CSS 类名规范
- 页面容器：`crm-page max-w-7xl mx-auto`
- 专业卡片：`pro-card`
- 卡片头部：`pro-card-header`
- 卡片标题：`pro-card-title`
- 卡片内容：`pro-card-body`

## 2. 颜色主题规范

### 2.1 主色调（紫色主题）
- **主要颜色**：`#4F46E5` (Indigo-600)
- **悬停状态**：`#4338CA` (Indigo-700)
- **激活状态**：`#3730A3` (Indigo-800)
- **浅色背景**：`#EEF2FF` (Indigo-50)
- **边框颜色**：`#C7D2FE` (Indigo-200)

### 2.2 文字颜色
- **主标题**：`#111827` (Gray-900)
- **副标题**：`#374151` (Gray-700)
- **提示文字**：`#6B7280` (Gray-500)
- **面包屑**：`#6B7280` (Gray-500)
- **面包屑当前页**：`#111827` (Gray-900)

### 2.3 背景颜色
- **页面背景**：继承父级背景
- **卡片背景**：`#FFFFFF`
- **统计卡片渐变背景**：
  - 蓝色系：`from-cyan-50 to-blue-50`
  - 红色系：`from-rose-50 to-pink-50`
  - 绿色系：`from-lime-50 to-green-50`
  - 紫色系：`from-violet-50 to-purple-50`

## 3. 统计概览区域规范

### 3.1 统计卡片布局
```vue
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <div class="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-xl p-5 border border-cyan-100/50 shadow-sm hover:shadow-md transition-all duration-200">
    <div class="flex items-center space-x-2 mb-3">
      <span class="material-icons-outlined text-cyan-600">图标</span>
      <span class="text-sm font-medium text-slate-700">统计标题</span>
    </div>
    <div class="text-3xl font-bold text-slate-800">{{ 数值 }}</div>
  </div>
</div>
```

### 3.2 统计卡片颜色方案
| 统计类型 | 背景渐变 | 图标颜色 | 边框颜色 |
|---------|---------|---------|---------|
| 总数统计 | `from-cyan-50 to-blue-50` | `text-cyan-600` | `border-cyan-100/50` |
| 待处理 | `from-rose-50 to-pink-50` | `text-rose-600` | `border-rose-100/50` |
| 已完成 | `from-lime-50 to-green-50` | `text-lime-600` | `border-lime-100/50` |
| 新增统计 | `from-violet-50 to-purple-50` | `text-violet-600` | `border-violet-100/50` |

### 3.3 统计卡片规范
- **圆角**：`rounded-xl` (12px)
- **内边距**：`p-5` (20px)
- **阴影**：`shadow-sm` 默认，`hover:shadow-md` 悬停
- **过渡效果**：`transition-all duration-200`
- **数值字体**：`text-3xl font-bold text-slate-800`
- **标题字体**：`text-sm font-medium text-slate-700`

## 4. 筛选区域规范

### 4.1 筛选区域布局
```vue
<div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6">
  <!-- 搜索框 -->
  <el-input
    v-model="searchQuery"
    placeholder="请输入搜索关键词"
    size="default"
    class="w-full sm:w-64"
    clearable
  >
    <template #prefix>
      <span class="material-icons-outlined text-gray-400">search</span>
    </template>
  </el-input>

  <!-- 筛选下拉框 -->
  <el-select 
    v-model="filterValue" 
    placeholder="筛选条件" 
    clearable 
    size="default"
    class="w-full sm:w-48"
  >
    <el-option label="选项1" value="value1" />
  </el-select>

  <!-- 重置按钮 -->
  <el-button 
    @click="handleResetFilter"
    class="border-gray-300 text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
  >
    <span class="material-icons-outlined text-sm mr-1">refresh</span>
    重置
  </el-button>
</div>
```

### 4.2 筛选控件规范
- **搜索框宽度**：移动端 `w-full`，桌面端 `sm:w-64`
- **下拉框宽度**：移动端 `w-full`，桌面端 `sm:w-48`
- **控件间距**：`gap-4`
- **控件尺寸**：统一使用 `size="default"`
- **重置按钮样式**：灰色边框，悬停时浅灰色背景

## 5. 数据列表规范

### 5.1 表头设计
```vue
<div class="hidden md:flex items-center bg-gray-100 rounded-lg px-4 py-3 mb-4 text-sm font-medium text-gray-600 min-h-[60px]">
  <div class="flex-1 min-w-0 grid grid-cols-12 gap-4 items-center">
    <div class="col-span-2 flex items-center">列标题1</div>
    <div class="col-span-2 flex items-center justify-center">列标题2</div>
    <div class="col-span-1 flex items-center justify-center">操作</div>
  </div>
</div>
```

### 5.2 数据行设计
```vue
<div class="space-y-4">
  <div
    v-for="item in dataList"
    :key="item.id"
    class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100 cursor-pointer"
    @click="handleViewDetail(item)"
  >
    <div class="flex items-center min-h-[60px]">
      <div class="flex-1 min-w-0 grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
        <!-- 数据列 -->
        <div class="col-span-12 md:col-span-2 flex items-center">
          <div class="w-full">
            <div class="md:hidden text-xs font-medium text-gray-600 mb-1">列标题</div>
            <div class="text-sm text-gray-900">{{ item.value }}</div>
          </div>
        </div>
        
        <!-- 操作列 -->
        <div class="col-span-12 md:col-span-1 flex items-center justify-center" @click.stop>
          <!-- 操作按钮 -->
        </div>
      </div>
    </div>
  </div>
</div>
```

### 5.3 数据行规范
- **背景颜色**：默认 `bg-gray-50`，悬停 `hover:bg-gray-100`
- **圆角**：`rounded-lg` (8px)
- **内边距**：`p-4` (16px)
- **最小高度**：`min-h-[60px]`
- **阴影效果**：悬停时 `hover:shadow-md`
- **过渡效果**：`transition-all duration-200`

## 6. 状态标签规范

### 6.1 状态标签样式
```vue
<!-- 基础状态标签 -->
<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
  状态文字
</span>

<!-- 圆形状态标签 -->
<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200">
  状态文字
</span>
```

### 6.2 状态颜色方案
| 状态类型 | 背景色 | 文字色 | 边框色 | 使用场景 |
|---------|--------|--------|--------|----------|
| 信息/默认 | `bg-blue-50` | `text-blue-700` | `border-blue-200` | 一般信息标识 |
| 成功/完成 | `bg-green-50` | `text-green-700` | `border-green-200` | 已完成、成功状态 |
| 警告/待处理 | `bg-yellow-100` | `text-yellow-800` | `border-yellow-200` | 待处理、警告状态 |
| 危险/错误 | `bg-red-50` | `text-red-700` | `border-red-200` | 错误、危险状态 |
| 紫色/特殊 | `bg-purple-50` | `text-purple-700` | `border-purple-200` | 特殊标识 |

## 7. 操作按钮规范

### 7.1 主要操作按钮
```vue
<el-button 
  type="primary" 
  size="small" 
  @click="handleAction"
  class="main-action-button"
>
  <span class="material-icons-outlined text-sm mr-1">add</span>
  操作名称
</el-button>
```

**样式规范：**
```css
.main-action-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.main-action-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}
```

### 7.2 下拉操作菜单
```vue
<el-dropdown @command="handleAction" trigger="click" class="resource-action-dropdown">
  <el-button link size="small" class="action-dropdown-trigger">
    <span class="material-icons-outlined text-lg">more_vert</span>
  </el-button>
  <template #dropdown>
    <el-dropdown-menu class="resource-dropdown-menu">
      <el-dropdown-item command="action1" class="dropdown-menu-item">
        <div class="flex items-center">
          <span class="material-icons-outlined text-sm mr-2.5 text-blue-600">图标</span>
          <span class="text-sm font-medium text-gray-700">操作名称</span>
        </div>
      </el-dropdown-item>
    </el-dropdown-menu>
  </template>
</el-dropdown>
```

**下拉菜单样式：**
```css
/* 触发按钮 */
.action-dropdown-trigger {
  color: #6B7280 !important;
  padding: 4px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  width: 32px !important;
  height: 32px !important;
}

.action-dropdown-trigger:hover {
  color: #4F46E5 !important;
  background-color: #F3F4F6 !important;
}

/* 下拉菜单 */
:deep(.resource-dropdown-menu) {
  border-radius: 7px !important;
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.13) !important;
  border: 1px solid #E5E7EB !important;
  padding: 3px 0 !important;
  min-width: 130px !important;
}

/* 菜单项 */
:deep(.dropdown-menu-item) {
  padding: 7px 14px !important;
  margin: 1px 3px !important;
  border-radius: 5px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

:deep(.dropdown-menu-item:hover) {
  background-color: #F9FAFB !important;
}
```

### 7.3 操作图标颜色规范
| 操作类型 | 图标颜色 | 使用场景 |
|---------|---------|----------|
| 分配/指派 | `text-blue-600` | 分配资源、指派任务 |
| 编辑/修改 | `text-green-600` | 编辑信息、修改数据 |
| 查看/详情 | `text-gray-600` | 查看详情、浏览信息 |
| 删除/移除 | `text-red-600` | 删除数据、移除项目 |

## 8. 分页组件规范

### 8.1 分页布局
```vue
<div class="flex justify-between items-center mt-6" v-if="totalItems > 0">
  <div class="text-sm text-gray-500">
    共 {{ totalItems }} 条记录
  </div>
  <el-pagination
    v-if="totalPages > 1"
    v-model:current-page="currentPage"
    v-model:page-size="pageSize"
    :total="totalItems"
    layout="prev, pager, next"
    @current-change="handlePageChange"
    class="!mt-0"
  />
</div>
```

### 8.2 分页规范
- **默认每页数量**：10条
- **布局方式**：`layout="prev, pager, next"`
- **统计信息**：左侧显示总记录数
- **分页器**：右侧显示，使用紫色主题

## 9. 空状态设计规范

### 9.1 空状态布局
```vue
<div v-if="dataList.length === 0" class="text-center py-12">
  <div class="text-gray-400 mb-2">
    <span class="material-icons-outlined text-4xl">相关图标</span>
  </div>
  <p class="text-gray-500">{{ searchQuery ? '未找到匹配的数据' : '暂无数据' }}</p>
</div>
```

### 9.2 空状态规范
- **图标大小**：`text-4xl` (36px)
- **图标颜色**：`text-gray-400`
- **文字颜色**：`text-gray-500`
- **垂直内边距**：`py-12` (48px)
- **居中对齐**：`text-center`

## 10. 响应式设计规范

### 10.1 断点规范
- **移动端**：`< 768px`
- **平板端**：`768px - 1024px`
- **桌面端**：`> 1024px`

### 10.2 移动端适配
```css
@media (max-width: 1024px) {
  .crm-page {
    @apply p-4;
  }
}

@media (max-width: 768px) {
  .crm-page {
    @apply p-3;
  }
  
  /* 统计卡片堆叠 */
  .stats-grid {
    @apply grid-cols-1 sm:grid-cols-2;
  }
  
  /* 筛选区域垂直排列 */
  .filter-section {
    @apply flex-col items-stretch;
  }
}
```

## 11. 表单对话框规范

### 11.1 继承悬浮框设计规范
CRM 系统中的所有对话框都应遵循《悬浮框设计规范.md》中的标准，包括：
- 使用 `simple-dialog compact-dialog` 类名
- 紫色主题 `#4F46E5`
- 标准的内边距和字体规范
- 统一的按钮样式

### 11.2 CRM 特定对话框样式
```vue
<!-- 编辑模式对话框标题 -->
<el-dialog
  v-model="dialogVisible"
  :title="isEditMode ? '编辑资源' : '添加资源'"
  width="600px"
  class="simple-dialog compact-dialog"
>
  <!-- 内容 -->
</el-dialog>
```

## 12. 样式模板

### 12.1 页面基础样式
```css
/* CRM页面基础样式 */
.crm-page {
  @apply p-6;
}

/* Pro Card 样式 */
.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.pro-card-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.pro-card-title {
  @apply flex items-center text-lg font-medium text-gray-900;
}

.pro-card-title .icon {
  @apply mr-2 text-gray-500;
}

.pro-card-body {
  @apply p-6;
}
```

### 12.2 紫色主题全局变量
```css
/* 页面级别的Element Plus主色调覆盖 */
.crm-page {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #7C3AED !important;
  --el-color-primary-light-5: #A855F7 !important;
  --el-color-primary-light-7: #C084FC !important;
  --el-color-primary-light-8: #DDD6FE !important;
  --el-color-primary-light-9: #EDE9FE !important;
  --el-color-primary-dark-2: #3730A3 !important;
}
```

### 12.3 输入控件主题
```css
/* 输入框紫色主题 */
:deep(.el-input .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-input .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

/* 选择框紫色主题 */
:deep(.el-select) {
  --el-color-primary: #4F46E5;
}

/* 下拉选项样式 */
:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #EEF2FF !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
}
```

## 13. 检查清单

使用本标准设计 CRM 页面时，请确保：

### 13.1 页面结构
- [ ] 使用了正确的页面容器类：`crm-page max-w-7xl mx-auto`
- [ ] 包含面包屑导航
- [ ] 有清晰的页面标题
- [ ] 使用了 Pro Card 结构

### 13.2 颜色主题
- [ ] 所有主要操作使用紫色主题 `#4F46E5`
- [ ] 状态标签使用了正确的颜色方案
- [ ] 统计卡片使用了渐变背景
- [ ] 输入控件焦点颜色为紫色

### 13.3 布局和交互
- [ ] 统计概览区域使用了网格布局
- [ ] 筛选区域响应式布局正确
- [ ] 数据列表支持移动端显示
- [ ] 操作按钮有正确的悬停效果
- [ ] 分页组件位置和样式正确

### 13.4 响应式设计
- [ ] 移动端布局正常
- [ ] 统计卡片在小屏幕上正确堆叠
- [ ] 筛选控件在移动端垂直排列
- [ ] 数据列表在移动端显示完整

## 14. 使用示例

### 14.1 完整页面示例
```vue
<template>
  <div class="crm-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span>CRM系统</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">客户管理</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">客户管理</h2>
    </div>

    <!-- 主要内容卡片 -->
    <div class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">people</span>
          客户列表
        </div>
        <div class="flex items-center gap-3">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleAddCustomer"
            class="main-action-button"
          >
            <span class="material-icons-outlined text-sm mr-1">add</span>
            添加客户
          </el-button>
        </div>
      </div>

      <div class="pro-card-body">
        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-xl p-5 border border-cyan-100/50 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-cyan-600">people</span>
              <span class="text-sm font-medium text-slate-700">总客户数</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.total }}</div>
          </div>
        </div>

        <!-- 筛选区域 -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6">
          <el-input
            v-model="searchQuery"
            placeholder="请输入客户姓名/电话搜索"
            size="default"
            class="w-full sm:w-64"
            clearable
          >
            <template #prefix>
              <span class="material-icons-outlined text-gray-400">search</span>
            </template>
          </el-input>
        </div>

        <!-- 数据列表 -->
        <div class="space-y-4">
          <div
            v-for="customer in customerList"
            :key="customer.id"
            class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100 cursor-pointer"
          >
            <!-- 数据内容 -->
          </div>
        </div>

        <!-- 分页 -->
        <div class="flex justify-between items-center mt-6" v-if="totalCustomers > 0">
          <div class="text-sm text-gray-500">
            共 {{ totalCustomers }} 条客户记录
          </div>
          <el-pagination
            v-if="totalPages > 1"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="totalCustomers"
            layout="prev, pager, next"
            class="!mt-0"
          />
        </div>
      </div>
    </div>
  </div>
</template>
```

## 15. 维护说明

- 本标准基于 MarketResources.vue 页面设计
- 基于 Element Plus + TailwindCSS + Vue 3 技术栈
- 所有新增 CRM 页面都应遵循此标准
- 与《悬浮框设计规范.md》配合使用
- 如需修改标准，请更新此文档并通知团队
- 定期检查现有页面是否符合最新标准

---

*最后更新时间：2024年1月*
*版本：v1.0*
*基于：MarketResources.vue 设计*

### 更新日志
- **v1.0 (2024-01)**：
  - 基于 MarketResources.vue 页面创建初始版本
  - 定义完整的 CRM 系统设计规范
  - 包含页面布局、颜色主题、组件规范
  - 添加响应式设计和移动端适配规范
  - 提供完整的样式模板和使用示例