# 悬浮框（对话框）设计标准

## 概述

本文档定义了项目中所有悬浮框（Dialog/Modal）的统一设计标准，确保界面的一致性和用户体验的统一性。

## 1. 基础结构规范

### 1.1 HTML 结构
```vue
<el-dialog
  v-model="dialogVisible"
  title="对话框标题"
  width="500px"
  class="simple-dialog compact-dialog"
  @close="handleCloseDialog"
>
  <div class="simple-content">
    <!-- 内容区域 -->
  </div>
  
  <template #footer>
    <div class="flex justify-end space-x-4">
      <!-- 按钮区域 -->
    </div>
  </template>
</el-dialog>
```

### 1.2 CSS 类名规范
- 主容器类：`simple-dialog compact-dialog`
- 内容容器类：`simple-content`

## 2. 尺寸规范

### 2.1 对话框宽度
| 用途类型 | 推荐宽度 | 使用场景 |
|---------|---------|----------|
| 简单表单 | 500px | 单一操作，1-3个字段 |
| 复杂表单 | 600px | 多字段表单，需要更多空间 |
| 信息展示 | 450px | 纯信息展示，确认操作 |
| 大型表单 | 800px | 复杂业务表单，多个分组 |

### 2.2 高度
- 自适应内容高度
- 最大高度不超过视窗的 90%
- 内容过多时显示滚动条

## 3. 间距规范

### 3.1 外层间距
```css
/* 头部区域 */
.el-dialog__header {
  padding: 16px 32px 8px;
}

/* 内容区域 */
.el-dialog__body {
  padding: 8px 32px 8px;
}

/* 底部区域 */
.el-dialog__footer {
  padding: 12px 32px 16px;
}
```

### 3.2 内容区间距
| 元素类型 | 间距值 | TailwindCSS 类 | 使用说明 |
|---------|-------|----------------|----------|
| 大区块间距 | 24px | `mb-6` | 不同功能区域之间 |
| 中等区块间距 | 16px | `mb-4` | 表单项与表单项之间 |
| 标签与控件间距 | 12px | `mb-3` | label 与 input 之间 |
| 按钮间距 | 16px | `space-x-4` | 底部按钮之间 |
| 控件组内间距 | 12px | `space-x-3` | 输入框与按钮之间 |

### 3.3 文字行高
- 标题文字：`line-height: 1.2`
- 提示文字：`leading-6` (line-height: 1.5)
- 按钮文字：`line-height: 1`

## 4. 颜色规范

### 4.1 主色调
- 主要按钮：`#4F46E5` (Indigo-600)
- 悬停状态：`#4338CA` (Indigo-700) 
- 激活状态：`#3730A3` (Indigo-800)
- 焦点阴影：`rgba(79, 70, 229, 0.2)`

### 4.2 文字颜色
- 标题文字：`#111827` (Gray-900)
- 标签文字：`#111827` (Gray-900)
- 提示文字：`#6B7280` (Gray-500)
- 关闭按钮：`#6B7280` (Gray-500)

### 4.3 背景颜色
- 对话框背景：`#FFFFFF`
- 普通按钮悬停：`#F8FAFF`

## 5. 字体规范

### 5.1 字号
| 元素类型 | 字号 | TailwindCSS 类 |
|---------|------|----------------|
| 对话框标题 | 18px | `text-lg` |
| 表单标签 | 14px | `text-sm` |
| 提示文字 | 14px | `text-sm` |
| 按钮文字 | 13px | - |

### 5.2 字重
| 元素类型 | 字重 | TailwindCSS 类 |
|---------|------|----------------|
| 对话框标题 | 600 | `font-semibold` |
| 表单标签 | 500 | `font-medium` |
| 普通文字 | 400 | `font-normal` |

## 6. 按钮规范

### 6.1 按钮尺寸
- 高度：32px
- 内边距：`0 12px`
- 字体大小：13px

### 6.2 按钮类型
| 按钮类型 | 使用场景 | 样式类 |
|---------|----------|--------|
| 主要按钮 | 确认、提交操作 | `type="primary"` |
| 次要按钮 | 取消、关闭操作 | 默认样式 |

### 6.3 按钮排列
- 右对齐排列
- 主要按钮在右侧
- 按钮间距：16px (`space-x-4`)

## 7. 表单元素规范

### 7.1 输入框
```vue
<el-input
  v-model="value"
  placeholder="请输入..."
  maxlength="50"
  show-word-limit
/>
```

### 7.2 标签
```vue
<label class="block text-sm font-medium text-gray-900 mb-3">
  标签文字
</label>
```

### 7.3 提示文字
```vue
<p class="text-sm text-gray-600 leading-6">
  提示信息内容
</p>
```

## 8. 样式模板

### 8.1 完整 CSS 样式
```css
/* 简洁对话框样式 - 无分割线整体页面，上下紧凑布局 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
  }

  /* 次要按钮样式 */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
  }
}
```

## 9. 使用示例

### 9.1 简单表单对话框
```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加用户"
    width="500px"
    class="simple-dialog compact-dialog"
    @close="handleClose"
  >
    <div class="simple-content">
      <div class="mb-6">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-900 mb-3">用户名</label>
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
            maxlength="50"
            show-word-limit
          />
        </div>
        <p class="text-sm text-gray-600 leading-6">
          用户名将作为登录凭证，请确保唯一性
        </p>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-end space-x-4">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
        >
          确认添加
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
```

## 10. 响应式适配

### 10.1 移动端适配
- 移动设备上对话框宽度调整为 90% 视窗宽度
- 最小宽度：320px
- 最大宽度：500px

```css
@media (max-width: 768px) {
  .simple-dialog .el-dialog {
    width: 90% !important;
    min-width: 320px;
    max-width: 500px;
    margin: 5vh auto !important;
  }
}
```

## 11. 检查清单

使用本标准设计对话框时，请确保：

- [ ] 使用了正确的 CSS 类名：`simple-dialog compact-dialog`
- [ ] 对话框宽度符合内容需求和标准
- [ ] 间距使用了标准的 TailwindCSS 类
- [ ] 颜色符合主题规范
- [ ] 按钮高度为 32px，使用正确的样式
- [ ] 文字使用了正确的字号和字重
- [ ] 提示文字使用了 `leading-6` 行高
- [ ] 按钮右对齐，主要按钮在右侧
- [ ] 关闭按钮样式正确
- [ ] 在移动端能正常显示

## 12. 维护说明

- 本标准基于 Element Plus + TailwindCSS + Vue 3 技术栈
- 所有新增对话框都应遵循此标准
- 如需修改标准，请更新此文档并通知团队
- 定期检查现有对话框是否符合最新标准

---

*最后更新时间：2024年*
*版本：v1.0