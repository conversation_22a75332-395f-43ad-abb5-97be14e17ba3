<template>
  <div class="benefits-management-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span>组织管理</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">权益管理</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">{{ isOrganizationIdentity && currentOrganization ? currentOrganization.name : '权益管理' }}</h2>
      <div v-if="isOrganizationOwner && currentOrganization" class="flex gap-3">
        <el-button type="primary" @click="handleDistributeRights">
          <span class="material-icons-outlined text-sm mr-1">person_add</span>
          分配账号
        </el-button>
      </div>
    </div>

          <!-- 主要内容区 -->
      <div class="space-y-6">
        <!-- 权限提示框（非管理员可见） -->
        <div v-if="isOrganizationIdentity && currentOrganization && !isOrganizationOwner" class="bg-white border border-gray-200 rounded-lg p-4">
          <div class="flex items-start space-x-3">
            <span class="material-icons-outlined text-gray-600 text-lg">account_circle</span>
            <div>
              <h3 class="text-sm font-medium text-gray-900 mb-1">当前权限</h3>
              <p class="text-sm text-gray-600 leading-6 mb-3">
                您当前在 <strong>{{ currentOrganization.name }}</strong> 组织中是普通成员。
              </p>
              <p class="text-sm text-gray-600 leading-6">
                套餐分配和管理功能仅限组织管理员使用。如需管理权限，请联系组织管理员。
              </p>
            </div>
          </div>
        </div>

        <!-- 权益明细卡片 -->
      <div class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">analytics</span>
            权益明细
          </div>
        </div>
        <div class="pro-card-body">
          <!-- 加载状态 -->
          <div v-if="isLoadingPackage || isLoadingCredit || isLoadingMembers" class="text-center py-8">
            <div class="animate-pulse">
              <div class="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-24 mx-auto mb-2"></div>
              <div class="h-3 bg-gray-200 rounded w-32 mx-auto"></div>
            </div>
          </div>

          <!-- 权益卡片 - 固定三列布局，始终显示 -->
          <div v-if="!isLoadingPackage && !isLoadingCredit && !isLoadingMembers" class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- 智能积分 - 始终正常显示 -->
            <div class="bg-gray-50 rounded-lg p-4 flex flex-col h-full">
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-purple-600">psychology</span>
                <span class="text-sm font-medium text-gray-900">智能积分</span>
              </div>
              <div class="flex-1 flex flex-col justify-between">
                <div>
                  <div class="flex items-center space-x-2 mb-1">
                    <span class="text-xs text-gray-500">组织共享</span>
                    <span v-if="creditStatus.accountStatus === 'active'" class="text-xs text-green-600">● 正常</span>
                    <span v-else class="text-xs text-red-600">● 不足</span>
                  </div>
                  <div class="text-3xl font-bold text-gray-900 mb-3">{{ creditStatus.balance }}</div>
                </div>
                <div class="w-full bg-purple-200 rounded-full h-2">
                  <div
                    class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                    :style="{ width: creditStatus.balance > 0 ? '100%' : '0%' }"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 专业库匹配 - 有套餐时正常显示，无套餐时高斯模糊 -->
            <div class="bg-gray-50 rounded-lg p-4 flex flex-col h-full relative" :class="{ 'light-blur': !packageStatus.hasPackage }">
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-green-600">analytics</span>
                <span class="text-sm font-medium text-gray-900">专业库匹配</span>
              </div>
              <div class="flex-1 flex flex-col justify-between">
                <div>
                  <div class="flex items-center space-x-2 mb-1">
                    <span v-if="packageStatus.hasPackage && !packageStatus.isExpired" class="text-xs text-green-600">● 有效</span>
                    <span v-else-if="packageStatus.hasPackage && packageStatus.isExpired" class="text-xs text-red-600">● 已过期</span>
                    <span v-else class="text-xs text-gray-400">● 未激活</span>
                    <span v-if="packageStatus.hasPackage && packageStatus.daysRemaining > 0" class="text-xs text-gray-500">
                      剩余{{ packageStatus.daysRemaining }}天
                    </span>
                  </div>
                  <div class="flex items-baseline space-x-2 mb-3">
                    <span class="material-icons-outlined text-green-600 text-xl">all_inclusive</span>
                    <span class="text-2xl font-bold text-gray-900">无限</span>
                  </div>
                </div>
                <div class="w-full bg-green-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300"
                    :class="packageStatus.hasPackage && !packageStatus.isExpired ? 'bg-green-600' : 'bg-gray-400'"
                    :style="{ width: packageStatus.hasPackage && !packageStatus.isExpired ? '100%' : '0%' }"
                  ></div>
                </div>
              </div>
              <!-- 未激活遮罩 -->
              <div v-if="!packageStatus.hasPackage" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-10 rounded-lg">
                <span class="material-icons-outlined text-gray-400 text-3xl">lock</span>
              </div>
            </div>

            <!-- 定校书生成 - 有套餐时正常显示，无套餐时高斯模糊 -->
            <div class="bg-gray-50 rounded-lg p-4 flex flex-col h-full relative" :class="{ 'light-blur': !packageStatus.hasPackage }">
              <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-purple-600">table_chart</span>
                <span class="text-sm font-medium text-gray-900">定校书生成（Excel表格）</span>
              </div>
              <div class="flex-1 flex flex-col justify-between">
                <div>
                  <div class="flex items-center space-x-2 mb-1">
                    <span v-if="packageStatus.hasPackage && !packageStatus.isExpired" class="text-xs text-green-600">● 有效</span>
                    <span v-else-if="packageStatus.hasPackage && packageStatus.isExpired" class="text-xs text-red-600">● 已过期</span>
                    <span v-else class="text-xs text-gray-400">● 未激活</span>
                    <span v-if="packageStatus.hasPackage && packageStatus.daysRemaining > 0" class="text-xs text-gray-500">
                      剩余{{ packageStatus.daysRemaining }}天
                    </span>
                  </div>
                  <div class="flex items-baseline space-x-2 mb-3">
                    <span class="material-icons-outlined text-purple-600 text-xl">all_inclusive</span>
                    <span class="text-2xl font-bold text-gray-900">无限</span>
                  </div>
                </div>
                <div class="w-full bg-purple-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300"
                    :class="packageStatus.hasPackage && !packageStatus.isExpired ? 'bg-purple-600' : 'bg-gray-400'"
                    :style="{ width: packageStatus.hasPackage && !packageStatus.isExpired ? '100%' : '0%' }"
                  ></div>
                </div>
              </div>
              <!-- 未激活遮罩 -->
              <div v-if="!packageStatus.hasPackage" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-10 rounded-lg">
                <span class="material-icons-outlined text-gray-400 text-3xl">lock</span>
              </div>
            </div>
          </div>

          <!-- 未订阅套餐提醒卡片 - 显示在三个权益卡片下方 -->
          <div v-if="!packageStatus.hasPackage" class="mt-6">
            <div class="bg-gradient-to-r from-orange-50 to-red-50 border-l-4 border-orange-400 rounded-lg p-6 shadow-sm">
              <div class="flex items-start space-x-4">
                <!-- 警告图标 -->
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                    <span class="material-icons-outlined text-orange-600 text-xl">warning</span>
                  </div>
                </div>

                <!-- 提醒内容 -->
                <div class="flex-1">
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="text-lg font-semibold text-orange-800 mb-1">未订阅套餐</h3>
                      <p class="text-sm text-orange-700 leading-relaxed">
                        您还未购买选校套餐，部分功能暂不可用。购买套餐后即可享受完整的选校规划服务。
                      </p>
                    </div>

                    <!-- 立即购买按钮 -->
                    <div class="flex-shrink-0 ml-4">
                      <a
                        href="/account/recharge"
                        class="inline-flex items-center px-4 py-2 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                        style="background-color: #4F46E5; border-color: #4F46E5;"
                        @mouseover="$event.target.style.backgroundColor = '#4338CA'"
                        @mouseout="$event.target.style.backgroundColor = '#4F46E5'"
                      >
                        <span class="material-icons-outlined text-sm mr-1">shopping_cart</span>
                        立即购买
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 账号明细卡片 -->
      <div class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">account_circle</span>
            账号明细
          </div>
        </div>
        <div class="pro-card-body">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 左侧：账号统计 -->
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <!-- 总成员数 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="material-icons-outlined text-blue-600">groups</span>
                    <span class="text-sm font-medium text-gray-900">{{ isOrganizationIdentity ? '组织成员' : '总账户' }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mb-1">总数</div>
                  <div class="text-2xl font-bold text-gray-900">{{ organizationStats.totalMembers }}</div>
                </div>

                <!-- 激活成员数 -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="material-icons-outlined text-green-600">verified_user</span>
                    <span class="text-sm font-medium text-gray-900">激活成员</span>
                  </div>
                  <div class="text-xs text-gray-500 mb-1">数量</div>
                  <div class="text-2xl font-bold text-gray-900">{{ organizationStats.activeMembers }}</div>
                </div>

                <!-- 套餐权益（所有成员可见） -->
                <div v-if="currentOrganization" class="bg-gray-50 rounded-lg p-4">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="material-icons-outlined text-purple-600">inventory_2</span>
                    <span class="text-sm font-medium text-gray-900">{{ isOrganizationOwner ? '可分配套餐' : '组织套餐' }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mb-1">数量</div>
                  <div class="text-2xl font-bold text-gray-900">{{ allocationStats.totalAvailablePackages }}</div>
                </div>

                <!-- 权益分配（所有成员可见） -->
                <div v-if="currentOrganization" class="bg-gray-50 rounded-lg p-4">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="material-icons-outlined text-green-600">assignment_ind</span>
                    <span class="text-sm font-medium text-gray-900">{{ isOrganizationOwner ? '已分配成员' : '权益持有者' }}</span>
                  </div>
                  <div class="text-xs text-gray-500 mb-1">数量</div>
                  <div class="text-2xl font-bold text-gray-900">{{ allocationStats.activeAllocationsCount }}</div>
                </div>
              </div>
            </div>

            <!-- 右侧：权益分配率统计 -->
            <div class="flex flex-col items-center justify-center">
              <div class="relative w-48 h-48 mb-4">
                <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="#e5e7eb"
                    stroke-width="8"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="#10b981"
                    stroke-width="8"
                    stroke-dasharray="282.7"
                    :stroke-dashoffset="282.7 - (282.7 * accountActivationPercent / 100)"
                    class="transition-all duration-500"
                  />
                </svg>
                <div class="absolute inset-0 flex flex-col items-center justify-center">
                  <div class="text-3xl font-bold text-gray-900">{{ accountActivationPercent }}%</div>
                  <div class="text-sm text-gray-500">权益分配率</div>
                </div>
              </div>

              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span class="text-sm text-gray-600">已分配权益</span>
                  <span class="text-sm font-medium text-gray-900">{{ organizationStats.packageHolders }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
                  <span class="text-sm text-gray-600">未分配权益</span>
                  <span class="text-sm font-medium text-gray-900">{{ organizationStats.totalMembers - organizationStats.packageHolders }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- 分配记录卡片（仅组织管理员可见） -->
      <div v-if="isOrganizationOwner && currentOrganization" class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">assignment</span>
            分配记录
          </div>
        </div>
        <div class="pro-card-body">
          <div v-if="loadingAllocations" class="text-center py-8">
            <div class="animate-pulse">
              <div class="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-24 mx-auto mb-2"></div>
              <div class="h-3 bg-gray-200 rounded w-32 mx-auto"></div>
            </div>
          </div>

          <div v-else-if="organizationAllocations.length === 0" class="text-center py-8">
            <div class="text-gray-400 mb-4">
              <span class="material-icons-outlined text-4xl">inbox</span>
            </div>
            <div class="text-gray-500">暂无分配记录</div>
          </div>

          <div v-else class="allocation-records">
            <div class="allocation-table-container-with-scroll">
              <table class="w-full">
                <thead class="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">套餐名称</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分配成员</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分配时间</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">过期时间</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr
                    v-for="allocation in organizationAllocations"
                    :key="`${allocation.allocated_user_id}-${allocation.package_id}`"
                    class="hover:bg-gray-50"
                  >
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <div class="flex items-center">
                        <div
                          class="w-6 h-6 rounded-full flex items-center justify-center mr-2 flex-shrink-0"
                          :style="{
                            backgroundColor: allocation.package_info?.bgColor || '#F9FAFB',
                            color: allocation.package_info?.color || '#6B7280'
                          }"
                        >
                          <span class="material-icons-outlined text-sm">
                            {{ allocation.package_info?.icon || 'inventory_2' }}
                          </span>
                        </div>
                        <span>{{ allocation.formatted_package_name }}</span>
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ allocation.allocated_user_name }}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDateTime(allocation.allocated_at) }}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDateTime(allocation.allocated_order?.expires_at) }}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <span
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                        :class="{
                          'bg-green-100 text-green-800': allocation.status === 'active',
                          'bg-gray-100 text-gray-800': allocation.status === 'revoked'
                        }"
                      >
                        {{ allocation.formatted_status.label }}
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        v-if="allocation.status === 'active' && isOrganizationOwner"
                        @click="handleRevokeAllocation(allocation)"
                        class="text-red-600 hover:text-red-900 transition-colors"
                      >
                        回收
                      </button>
                      <span v-else-if="allocation.status !== 'active'" class="text-gray-400">已回收</span>
                      <span v-else class="text-gray-400">无权限</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- 积分使用明细卡片 -->
      <div class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">history</span>
            使用明细
          </div>
        </div>
        <div class="pro-card-body">
          <div v-if="loadingTransactions" class="text-center py-8">
            <div class="animate-pulse">
              <div class="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-24 mx-auto mb-2"></div>
              <div class="h-3 bg-gray-200 rounded w-32 mx-auto"></div>
            </div>
          </div>

          <div v-else-if="filteredTransactions.length === 0" class="text-center py-8">
            <div class="text-gray-400 mb-4">
              <span class="material-icons-outlined text-4xl">receipt_long</span>
            </div>
            <div class="text-gray-500">暂无使用记录</div>
          </div>

          <div v-else class="transaction-records">
            <div class="allocation-table-container-with-scroll">
              <table class="w-full">
                <thead class="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易类型</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务类型</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">积分变动</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易时间</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr
                    v-for="transaction in filteredTransactions"
                    :key="transaction.id"
                    class="hover:bg-gray-50"
                  >
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <div class="flex items-center">
                        <div
                          class="w-6 h-6 rounded-full flex items-center justify-center mr-2 flex-shrink-0"
                          :class="{
                            'bg-green-100 text-green-600': transaction.transaction_type === 'recharge',
                            'bg-red-100 text-red-600': transaction.transaction_type === 'consumption',
                            'bg-blue-100 text-blue-600': transaction.transaction_type === 'refund'
                          }"
                        >
                          <span class="material-icons-outlined text-sm">
                            {{ getTransactionTypeIcon(transaction.transaction_type) }}
                          </span>
                        </div>
                        <span>{{ getTransactionTypeLabel(transaction.transaction_type) }}</span>
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ getServiceTypeLabel(transaction.service_type) }}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                      <span
                        :class="{
                          'text-green-600': transaction.amount > 0,
                          'text-red-600': transaction.amount < 0
                        }"
                      >
                        {{ transaction.amount > 0 ? '+' : '' }}{{ transaction.amount }}
                      </span>
                    </td>
                    <td class="px-4 py-4 text-sm text-gray-900 max-w-xs">
                      <div class="truncate" :title="getSimpleDescription(transaction)">
                        {{ getSimpleDescription(transaction) }}
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDateTime(transaction.created_at) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>


          </div>
        </div>
      </div>
    </div>

    <!-- 套餐分配对话框 -->
    <PackageAllocationDialog
      v-if="currentOrganization?.id"
      v-model:visible="allocationDialogVisible"
      :organization-id="currentOrganization.id"
      @success="handleAllocationSuccess"
    />

    <!-- 自定义确认对话框 -->
    <el-dialog
      v-model="confirmDialogVisible"
      :title="confirmDialog.title"
      width="500px"
      class="simple-dialog compact-dialog"
      @close="handleConfirmCancel"
    >
      <div class="simple-content">
        <div class="mb-6">
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 rounded-full flex items-center justify-center"
                   :class="confirmDialog.type === 'warning' ? 'bg-orange-100' : 'bg-red-100'">
                <span class="material-icons-outlined text-xl"
                      :class="confirmDialog.type === 'warning' ? 'text-orange-600' : 'text-red-600'">
                  {{ confirmDialog.type === 'warning' ? 'warning' : 'error' }}
                </span>
              </div>
            </div>
            <div class="flex-1">
              <p class="text-sm text-gray-600 leading-6" v-html="confirmDialog.content"></p>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end">
          <el-button
            type="primary"
            :loading="confirmDialog.loading"
            @click="handleConfirmOk"
          >
            {{ confirmDialog.confirmText }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getPackageStatus, getCreditStatus } from '@/api/validation'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import { usePackageAllocation } from '@/composables/usePackageAllocation'
import { getOrganizationMembers } from '@/api/organizations'
import { revokePackage } from '@/api/organizationBenefits'
import { getCreditTransactions } from '@/api/account'
import PackageAllocationDialog from '@/components/organization/PackageAllocationDialog.vue'

// 使用组织权限管理组合式函数
const {
  currentOrganization,
  isOrganizationOwner,
  isOrganizationIdentity
} = useOrganizationPermissions()

// 使用套餐分配管理组合式函数
const {
  availablePackages,
  organizationAllocations,
  canAllocatePackages,
  permissionMessage,
  fetchAvailablePackages,
  fetchOrganizationAllocations
} = usePackageAllocation()

// 加载状态
const isLoadingPackage = ref(false)
const isLoadingCredit = ref(false)
const isLoadingMembers = ref(false)
const loadingAllocations = ref(false)

// 分配管理相关状态

// 套餐状态数据
const packageStatus = ref({
  hasPackage: false,
  packageName: '',
  purchaseTime: '',
  expiresAt: '',
  isExpired: false,
  daysRemaining: 0
})

// 积分状态数据
const creditStatus = ref({
  balance: 0,
  accountStatus: 'inactive',
  sufficientForAiSelection: false
})

// 组织成员数据
const organizationMembers = ref([])
const organizationStats = ref({
  totalMembers: 1,
  activeMembers: 1,
  packageHolders: 0
})

// 积分交易记录相关状态
const creditTransactions = ref([])
const loadingTransactions = ref(false)

// 自动刷新定时器
let autoRefreshTimer = null

// 分配统计计算属性
const allocationStats = computed(() => {
  // 管理员可以看到详细的分配统计
  if (isOrganizationOwner.value) {
    const totalAvailablePackages = availablePackages.value.reduce((total, pkg) => total + pkg.available_count, 0)
    const activeAllocationsCount = organizationAllocations.value.filter(allocation => allocation.status === 'active').length
    const totalAllocationsCount = organizationAllocations.value.length

    return {
      totalAvailablePackages,
      activeAllocationsCount,
      totalAllocationsCount
    }
  }

  // 成员看到基于组织统计的简化信息
  return {
    totalAvailablePackages: packageStatus.value.hasPackage ? 1 : 0, // 组织是否有套餐
    activeAllocationsCount: organizationStats.value.packageHolders, // 有权益的成员数
    totalAllocationsCount: organizationStats.value.packageHolders
  }
})

// 计算属性：权益分配百分比 - 基于组织成员和套餐状态
const accountActivationPercent = computed(() => {
  const totalMembers = organizationStats.value.totalMembers
  if (totalMembers === 0) return 0

  // 如果有套餐，则所有成员都算已分配权益
  const assignedMembers = packageStatus.value.hasPackage ? totalMembers : 0
  return Math.round((assignedMembers / totalMembers) * 100)
})

// 过滤后的交易记录 - 排除导出相关记录
const filteredTransactions = computed(() => {
  return creditTransactions.value.filter(transaction => {
    // 过滤掉导出相关的记录
    if (transaction.operation_type === 'export_usage' || 
        (transaction.description && transaction.description.includes('导出客户'))) {
      return false
    }
    return true
  })
})

// 获取套餐状态
const fetchPackageStatus = async () => {
  try {
    isLoadingPackage.value = true
    const response = await getPackageStatus()

    if (response.success) {
      const data = response.data
      packageStatus.value = {
        hasPackage: data.has_package,
        packageName: data.package_name || '',
        purchaseTime: data.purchase_time || '',
        expiresAt: data.expires_at || '',
        isExpired: data.is_expired || false,
        daysRemaining: data.days_remaining || 0
      }
    } else {
      console.warn('获取套餐状态失败:', response.message)
    }
  } catch (error) {
    console.error('获取套餐状态失败:', error)
    ElMessage.error('获取套餐状态失败')
  } finally {
    isLoadingPackage.value = false
  }
}

// 获取积分状态
const fetchCreditStatus = async () => {
  try {
    isLoadingCredit.value = true
    const response = await getCreditStatus()

    if (response.success) {
      const data = response.data
      creditStatus.value = {
        balance: data.balance || 0,
        accountStatus: data.account_status || 'inactive',
        sufficientForAiSelection: data.sufficient_for_ai_selection || false
      }
    } else {
      console.warn('获取积分状态失败:', response.message)
    }
  } catch (error) {
    console.error('获取积分状态失败:', error)
    ElMessage.error('获取积分状态失败')
  } finally {
    isLoadingCredit.value = false
  }
}

// 获取组织成员数据
const fetchOrganizationMembers = async () => {
  if (!currentOrganization.value || !isOrganizationIdentity.value) {
    // 个人身份时的默认值
    organizationStats.value = {
      totalMembers: 1,
      activeMembers: 1,
      packageHolders: packageStatus.value.hasPackage ? 1 : 0
    }
    return
  }

  try {
    isLoadingMembers.value = true
    const response = await getOrganizationMembers(currentOrganization.value.id, {
      limit: 100
    })
    
    organizationMembers.value = response
    
    // 计算统计数据
    const totalMembers = response.length
    const activeMembers = response.filter(member => member.is_active).length
    const packageHolders = packageStatus.value.hasPackage ? totalMembers : 0
    
    organizationStats.value = {
      totalMembers,
      activeMembers,
      packageHolders
    }
    
  } catch (error) {
    console.error('获取组织成员失败:', error)
    
    // 如果是权限错误，不显示错误消息，静默处理
    if (error.response?.status === 403) {
      console.warn('权限不足，使用默认统计数据')
    } else {
      ElMessage.error('获取组织成员失败')
    }
    
    // 使用默认值，避免显示错误
    organizationStats.value = {
      totalMembers: 1,
      activeMembers: 1,
      packageHolders: packageStatus.value.hasPackage ? 1 : 0
    }
  } finally {
    isLoadingMembers.value = false
  }
}

// 获取分配数据
const fetchAllocationData = async () => {
  if (!currentOrganization.value?.id || !isOrganizationOwner.value) return
  
  loadingAllocations.value = true
  try {
    await Promise.all([
      fetchAvailablePackages(currentOrganization.value.id),
      fetchOrganizationAllocations(currentOrganization.value.id)
    ])
  } catch (error) {
    console.error('获取分配数据失败:', error)
  } finally {
    loadingAllocations.value = false
  }
}

// 分配对话框状态
const allocationDialogVisible = ref(false)

// 自定义确认对话框状态
const confirmDialogVisible = ref(false)
const confirmDialog = ref({
  title: '',
  content: '',
  type: 'warning',
  confirmText: '确定',
  cancelText: '取消',
  loading: false,
  onConfirm: null,
  onCancel: null
})

// 显示自定义确认对话框
const showConfirmDialog = (options) => {
  confirmDialog.value = {
    title: options.title || '确认操作',
    content: options.content || '确定要执行此操作吗？',
    type: options.type || 'warning',
    confirmText: options.confirmText || '确定',
    cancelText: options.cancelText || '取消',
    loading: false,
    onConfirm: options.onConfirm || null,
    onCancel: options.onCancel || null
  }
  confirmDialogVisible.value = true
}

// 确认对话框确定按钮处理
const handleConfirmOk = async () => {
  if (confirmDialog.value.onConfirm) {
    confirmDialog.value.loading = true
    try {
      await confirmDialog.value.onConfirm()
      confirmDialogVisible.value = false
    } catch (error) {
      console.error('确认操作失败:', error)
    } finally {
      confirmDialog.value.loading = false
    }
  } else {
    confirmDialogVisible.value = false
  }
}

// 确认对话框取消按钮处理
const handleConfirmCancel = () => {
  if (confirmDialog.value.onCancel) {
    confirmDialog.value.onCancel()
  }
  confirmDialogVisible.value = false
}

// 事件处理：分配账号
const handleDistributeRights = () => {
  // 检查权限
  if (!isOrganizationIdentity.value) {
    ElMessage.error('请切换到组织身份后再执行分配操作')
    return
  }

  if (!isOrganizationOwner.value) {
    ElMessage.error('只有组织管理员可以执行套餐分配操作')
    return
  }

  // 打开分配对话框
  allocationDialogVisible.value = true
}

// 回收分配处理
const handleRevokeAllocation = async (allocation) => {
  const packageName = allocation.formatted_package_name || '未知套餐'
  const userName = allocation.allocated_user_name || '未知用户'
  
  showConfirmDialog({
    title: '确认回收套餐',
    content: `
      <div class="space-y-4">
        <div class="text-base text-gray-900 leading-6">
          确定要回收 <strong>${userName}</strong> 的套餐
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-purple-100 text-purple-700 ml-1">${packageName}</span>
          吗？
        </div>
        <p class="text-sm text-gray-500 leading-6">
          回收后该成员将无法继续使用相关功能。
        </p>
      </div>
    `,
    type: 'warning',
    confirmText: '确定回收',
    cancelText: '取消',
    onConfirm: async () => {
      const revocationData = {
        organization_id: currentOrganization.value.id,
        allocated_user_id: allocation.allocated_user_id,
        package_id: allocation.package_id
      }
      
      try {
        const response = await revokePackage(revocationData)
        
        if (response.success) {
          ElMessage.success(response.message || '套餐回收成功')
          // 回收成功后刷新数据
          await fetchAllocationData()
        } else {
          ElMessage.error(response.message || '套餐回收失败')
        }
      } catch (error) {
        console.error('回收套餐失败:', error)
        ElMessage.error(error.response?.data?.detail || '回收套餐失败')
      }
    }
  })
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'

  try {
    // 如果时间字符串没有时区标识符，假设它是UTC时间，添加'Z'
    let utcTimeString = dateTime;
    if (!dateTime.endsWith('Z') && !dateTime.includes('+') && !dateTime.includes('-', 10)) {
      utcTimeString = dateTime + 'Z';
    }

    // 创建UTC时间对象并转换为北京时间显示
    const utcDate = new Date(utcTimeString);

    return utcDate.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Shanghai' // 明确指定北京时区
    });
  } catch (error) {
    console.warn('时间格式化失败:', dateTime, error);
    return '-';
  }
}

// 获取交易类型图标
const getTransactionTypeIcon = (type) => {
  const icons = {
    recharge: 'add_circle',
    consumption: 'remove_circle',
    refund: 'undo'
  }
  return icons[type] || 'circle'
}

// 获取交易类型标签
const getTransactionTypeLabel = (type) => {
  const labels = {
    recharge: '充值',
    consumption: '消费',
    refund: '退款'
  }
  return labels[type] || type
}

// 获取服务类型标签
const getServiceTypeLabel = (type) => {
  if (!type) return '-'
  
  const labels = {
    ai_detection: 'AI工具',
    ai_selection: '定校规划', 
    ai_writing: 'AI写作',
    ai_matching: 'AI匹配'
  }
  return labels[type] || type
}

// 获取简洁描述
const getSimpleDescription = (transaction) => {
  if (!transaction) return '-'
  
  // 对于消费类型的交易，根据描述内容智能识别
  if (transaction.transaction_type === 'consumption' && transaction.description) {
    const desc = transaction.description
    
    // 识别查AI率服务
    if (desc.includes('查AI率服务扣费') || desc.includes('查AI率服务') || desc.includes('detect_ai')) {
      return '查AI率'
    }
    
    // 识别降AI率服务  
    if (desc.includes('降AI率服务扣费') || desc.includes('降AI率服务') || desc.includes('humanize_text')) {
      return '降AI率'
    }
    
    // 识别智能匹配服务
    if (desc.includes('AI智能匹配服务') || desc.includes('智能匹配') || transaction.service_type === 'ai_selection') {
      return '智能匹配'
    }
    
    // 识别AI写作服务
    if (desc.includes('AI写作') || transaction.service_type === 'ai_writing') {
      return 'AI写作'
    }
    
    // 识别AI匹配服务
    if (desc.includes('AI匹配') || transaction.service_type === 'ai_matching') {
      return '智能匹配'
    }
  }
  
  // 充值和退款显示简洁描述
  if (transaction.transaction_type === 'recharge') {
    return '积分充值'
  }
  
  if (transaction.transaction_type === 'refund') {
    return '积分退款'
  }
  
  // 其他情况显示原描述或默认值
  return transaction.description || '-'
}

// 获取积分交易记录
const fetchCreditTransactions = async (silent = false) => {
  try {
    // 静默模式下不显示加载状态
    if (!silent) {
      loadingTransactions.value = true
    }
    
    // 获取最近50条记录，足够显示使用明细
    const params = {
      page: 1,
      page_size: 50
    }
    
    const response = await getCreditTransactions(params)
    
    if (response.success) {
      creditTransactions.value = response.transactions || []
    } else {
      console.warn('获取积分交易记录失败:', response.message)
      if (!silent) {
        creditTransactions.value = []
      }
    }
  } catch (error) {
    console.error('获取积分交易记录失败:', error)
    // 静默模式下不显示错误消息，避免打扰用户
    if (!silent && error.response?.status !== 401) {
      ElMessage.error('获取积分交易记录失败')
    }
    if (!silent) {
      creditTransactions.value = []
    }
  } finally {
    if (!silent) {
      loadingTransactions.value = false
    }
  }
}



// 启动自动刷新
const startAutoRefresh = () => {
  // 清除现有定时器
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer)
  }
  
  // 设置30秒自动刷新
  autoRefreshTimer = setInterval(() => {
    // 静默刷新，不显示加载状态
    fetchCreditTransactions(true)
  }, 30000)
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshTimer) {
    clearInterval(autoRefreshTimer)
    autoRefreshTimer = null
  }
}

// 分配成功处理
const handleAllocationSuccess = async () => {
  // 自动刷新相关数据
  try {
    const promises = [
      fetchPackageStatus(),
      fetchCreditStatus(),
      fetchOrganizationMembers(),
      fetchCreditTransactions() // 也刷新积分交易记录
    ]

    // 如果是组织管理员，也刷新分配数据
    if (isOrganizationOwner.value && currentOrganization.value?.id) {
      promises.push(fetchAllocationData())
    }

    await Promise.allSettled(promises)
  } catch (error) {
    console.error('刷新数据失败:', error)
  }
}

// 监听组织变化和权限状态，加载分配数据
watch([() => currentOrganization.value?.id, () => isOrganizationOwner.value], ([newId, isOwner]) => {
  if (newId && isOwner) {
    fetchAllocationData()
  }
}, { immediate: true })

// 生命周期：组件挂载
onMounted(async () => {
  // 并行加载套餐状态、积分状态、组织成员数据和积分交易记录
  const promises = [
    fetchPackageStatus(),
    fetchCreditStatus(),
    fetchOrganizationMembers(),
    fetchCreditTransactions()
  ]
  
  // 如果是组织管理员，也加载分配数据
  if (isOrganizationOwner.value && currentOrganization.value?.id) {
    promises.push(fetchAllocationData())
  }
  
  await Promise.allSettled(promises)
  
  // 启动自动刷新
  startAutoRefresh()
})

// 生命周期：组件卸载
onUnmounted(() => {
  // 停止自动刷新
  stopAutoRefresh()
})
</script>

<style scoped>
/* 组织管理页面紫色主题按钮样式 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

:deep(.el-button--primary.is-link) {
  --el-button-text-color: #4F46E5;
}

:deep(.el-button--primary.is-link:hover) {
  --el-button-hover-text-color: #4338CA;
}

/* 选择框紫色主题 */
:deep(.el-select) {
  --el-color-primary: #4F46E5;
  --el-color-primary-light-3: #6366F1;
  --el-color-primary-light-5: #8B5CF6;
  --el-color-primary-light-7: #A78BFA;
  --el-color-primary-light-8: #C4B5FD;
  --el-color-primary-light-9: #DDD6FE;
  --el-color-primary-dark-2: #4338CA;
}

:deep(.el-select .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

/* 下拉选项紫色主题 */
:deep(.el-select-dropdown .el-select-dropdown__item.is-selected) {
  color: #4F46E5;
  background-color: #F3F4F6;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #DDD6FE;
}

/* 输入数字框紫色主题 */
:deep(.el-input-number) {
  --el-color-primary: #4F46E5;
}

:deep(.el-input-number .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-input-number .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

:deep(.el-input-number .el-input-number__increase),
:deep(.el-input-number .el-input-number__decrease) {
  --el-button-text-color: #4F46E5;
  --el-button-hover-text-color: #4338CA;
  --el-button-hover-bg-color: #DDD6FE;
}

/* 对话框确认按钮紫色主题 */
:deep(.el-dialog .el-dialog__footer .el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

/* 消息确认框按钮紫色主题 */
:deep(.el-message-box .el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

/* 全局Element Plus主题变量覆盖 - 确保所有组件使用紫色主题 */
:deep(:root) {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #8B5CF6 !important;
  --el-color-primary-light-7: #A78BFA !important;
  --el-color-primary-light-8: #C4B5FD !important;
  --el-color-primary-light-9: #DDD6FE !important;
  --el-color-primary-dark-2: #4338CA !important;
}

/* 强制覆盖任何可能的蓝色样式 */
:deep(.el-button--primary),
:deep(.el-button[type="primary"]),
:deep(button.el-button--primary) {
  --el-button-bg-color: #4F46E5 !important;
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important;
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important;
  --el-button-active-border-color: #3730A3 !important;
}

/* 确保所有选择框和输入框都使用紫色主题 */
:deep(.el-select),
:deep(.el-input),
:deep(.el-input-number),
:deep(.el-date-editor) {
  --el-color-primary: #4F46E5 !important;
}

/* 移除任何蓝色阴影效果 */
:deep(.el-button--primary:focus),
:deep(.el-button--primary:active),
:deep(.el-select .el-input__wrapper:focus),
:deep(.el-input .el-input__wrapper:focus),
:deep(.el-input-number .el-input__wrapper:focus) {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
}
/* 简洁对话框样式 - 无分割线整体页面，上下紧凑布局 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
  }

  /* 次要按钮样式 */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
  }
}

/* 页面特定样式 */
.benefits-management-page {
  @apply p-6;
}

/* 分配记录表格样式 - 带滚动条 */
.allocation-table-container-with-scroll {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  max-height: 400px; /* 限制最大高度，超过6行记录时显示滚动条 */
  overflow-y: auto; /* 垂直滚动 */
  overflow-x: auto; /* 水平滚动（移动端） */
}

.allocation-table-container-with-scroll table {
  border-collapse: collapse;
  min-width: 800px; /* 确保表格有最小宽度 */
  width: 100%;
}

.allocation-table-container-with-scroll thead th {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  position: sticky;
  top: 0;
  z-index: 10;
}

.allocation-table-container-with-scroll tbody tr:hover {
  background-color: #f9fafb;
}

.allocation-table-container-with-scroll tbody td {
  border-bottom: 1px solid #f3f4f6;
}

.allocation-table-container-with-scroll tbody tr:last-child td {
  border-bottom: none;
}

/* 自定义滚动条样式 */
.allocation-table-container-with-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.allocation-table-container-with-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.allocation-table-container-with-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.allocation-table-container-with-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 轻度高斯模糊效果 */
.light-blur {
  filter: blur(1px);
  opacity: 0.7;
  transition: all 0.3s ease;
}

/* 移动端适配 */
@media (max-width: 768px) {
  :deep(.simple-dialog .el-dialog) {
    width: 90% !important;
    min-width: 320px;
    max-width: 500px;
    margin: 5vh auto !important;
  }
  
  :deep(.simple-dialog .el-dialog__header) {
    padding: 12px 20px 6px;
  }
  
  :deep(.simple-dialog .el-dialog__body) {
    padding: 6px 20px 6px;
  }
  
  :deep(.simple-dialog .el-dialog__footer) {
    padding: 8px 20px 12px;
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .benefits-management-page {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .benefits-management-page {
    padding: 0.75rem;
  }

  .grid.grid-cols-1.lg\\:grid-cols-2 {
    gap: 1rem;
  }

  /* 移动端分配记录表格优化 */
  .allocation-table-container-with-scroll {
    border-radius: 6px;
    max-height: 300px; /* 移动端减小最大高度 */
  }

  .allocation-table-container-with-scroll table {
    min-width: 600px; /* 移动端减小最小宽度 */
  }

  .allocation-table-container-with-scroll th,
  .allocation-table-container-with-scroll td {
    padding: 8px 12px; /* 减小内边距 */
    font-size: 0.875rem; /* 减小字体 */
  }

  /* 移动端未订阅套餐提醒卡片样式优化 */
  .bg-gradient-to-r.from-orange-50.to-red-50 {
    padding: 1rem;
  }

  .bg-gradient-to-r.from-orange-50.to-red-50 .flex.items-center.justify-between {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .bg-gradient-to-r.from-orange-50.to-red-50 .flex-shrink-0.ml-4 {
    margin-left: 0;
    width: 100%;
  }

  .bg-gradient-to-r.from-orange-50.to-red-50 .inline-flex.items-center {
    width: 100%;
    justify-content: center;
  }
}
</style> 