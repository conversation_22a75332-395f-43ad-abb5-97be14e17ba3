<template>
  <div class="account-management-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span>组织管理</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">账号管理</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">{{ pageTitle }}</h2>
    </div>

    <!-- 个人身份：创建组织提示 -->
    <div v-if="isPersonalIdentity && canCreateOrganization" class="pro-card">
      <div class="pro-card-body">
        <div class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <span class="material-icons-outlined text-6xl">business</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">创建您的组织</h3>
          <p class="text-gray-600 mb-6">
            创建组织后，您可以邀请团队成员，统一管理账号和权限
          </p>
          <el-button 
            type="primary" 
            size="large" 
            @click="handleCreateOrganization"
            class="create-org-button"
          >
            <span class="material-icons-outlined text-sm mr-2">add</span>
            创建组织
          </el-button>
        </div>
      </div>
    </div>

    <!-- 组织身份：成员管理 -->
    <div v-else-if="isOrganizationIdentity && currentOrganization" class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">group</span>
          账号列表
        </div>
        <div class="flex items-center gap-3">
          <el-input
            v-model="searchQuery"
            placeholder="请输入姓名/邮箱搜索"
            size="small"
            class="w-64"
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <span class="material-icons-outlined text-gray-400">search</span>
            </template>
          </el-input>

          <el-button 
            type="primary" 
            size="small" 
            @click="handleAddSubAccount"
            :disabled="!isOrganizationOwner"
            class="add-member-button"
          >
            <span class="material-icons-outlined text-sm mr-1">add</span>
            邀请成员
          </el-button>
        </div>
      </div>

      <div class="pro-card-body">
        <!-- 表头 -->
        <div class="hidden md:flex items-center bg-gray-50 rounded-lg px-4 py-3 mb-4 text-xs font-medium text-gray-500 uppercase tracking-wider min-h-[60px]">
          <div class="w-16 flex-shrink-0 flex items-center justify-center"></div> <!-- 头像占位 -->
          <div class="flex-1 min-w-0 grid grid-cols-12 gap-4 items-center">
            <div class="col-span-2 flex items-center">姓名</div>
            <div class="col-span-2 flex items-center">创建时间</div>
            <div class="col-span-2 flex items-center justify-center md:justify-start">账号类型</div>
            <div class="col-span-2 flex items-center justify-center md:justify-start">账号状态</div>
            <div class="col-span-3 flex items-center">权限</div>
            <div class="col-span-1 flex items-center justify-center">操作</div>
          </div>
        </div>

        <!-- 账号横排卡片列表 -->
        <div class="space-y-4">
          <div
            v-for="row in paginatedAccounts"
            :key="row.id"
            class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100"
          >
            <div class="flex items-center min-h-[60px]">
              <!-- 用户头像 -->
              <div class="w-16 flex-shrink-0 flex items-center justify-center">
                <UserAvatar
                  :avatar-url="row.avatar_url || row.avatar"
                  :display-name="row.name"
                  size="large"
                />
              </div>

              <!-- 内容区域 -->
              <div class="flex-1 min-w-0 grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <!-- 姓名列 -->
                <div class="col-span-12 md:col-span-2 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">姓名</div>
                    <h3 class="font-medium text-gray-900 text-sm truncate">{{ row.name || '未知用户' }}</h3>
                  </div>
                </div>

                <!-- 创建时间列 -->
                <div class="col-span-12 md:col-span-2 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">创建时间</div>
                    <div class="text-xs text-gray-500 truncate">{{ row.createdAt }}</div>
                  </div>
                </div>

                <!-- 账号类型列 -->
                <div class="col-span-12 md:col-span-2 flex items-center justify-center md:justify-start">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">账号类型</div>
                    <div class="flex justify-center md:justify-start">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getAccountTypeClass(row.accountType)"
                      >
                        {{ getAccountTypeLabel(row.accountType) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 账号状态列 -->
                <div class="col-span-12 md:col-span-2 flex items-center justify-center md:justify-start">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">账号状态</div>
                    <div class="flex justify-center md:justify-start">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getStatusClass(row.status)"
                      >
                        {{ getStatusLabel(row.status) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 权限列 -->
                <div class="col-span-12 md:col-span-3 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">权限</div>
                    <div class="flex flex-wrap gap-1 justify-center md:justify-start">
                      <span
                        v-for="permission in getPermissionsText(row.permissions).split(',')"
                        :key="permission"
                        class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                      >
                        {{ permission }}
                      </span>
                      <span
                        v-if="getPermissionsText(row.permissions) === '无权限'"
                        class="text-xs text-gray-400"
                      >
                        无权限
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 操作列 -->
                <div class="col-span-12 md:col-span-1 flex items-center justify-center">
                  <div class="w-full flex justify-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">操作</div>
                    <el-dropdown @command="(command) => handleAccountAction(command, row)" trigger="click">
                      <el-button link size="small" class="!text-gray-400 hover:!text-gray-600">
                        <span class="material-icons-outlined">more_vert</span>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item
                            command="edit"
                            :disabled="!canEditRow(row)"
                          >
                            <span class="material-icons-outlined text-sm mr-2">edit</span>
                            编辑设置
                          </el-dropdown-item>
                          <el-dropdown-item
                            command="deactivate"
                            :disabled="row.accountType === 'main' || row.status === 'disabled' || !isOrganizationOwner"
                          >
                            <span class="material-icons-outlined text-sm mr-2">block</span>
                            停用账号
                          </el-dropdown-item>
                          <el-dropdown-item
                            command="delete"
                            :disabled="row.accountType === 'main' || !isOrganizationOwner"
                            divided
                          >
                            <span class="material-icons-outlined text-sm mr-2">delete</span>
                            移除成员
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="paginatedAccounts.length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-2">
              <span class="material-icons-outlined text-4xl">group_off</span>
            </div>
            <div class="text-sm text-gray-500">{{ searchQuery ? '未找到匹配的账号' : '暂无账号数据' }}</div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="flex justify-between items-center mt-6" v-if="filteredMembers.length > 0">
          <div class="text-xs text-gray-500">
            共 {{ filteredMembers.length }} 个账号
          </div>
          <el-pagination
            v-if="totalPages > 1"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="filteredMembers.length"
            layout="prev, pager, next"
            @current-change="handleCurrentChange"
            class="!mt-0"
          />
        </div>
      </div>
    </div>

    <!-- 无权限访问提示 -->
    <div v-else-if="isOrganizationIdentity && !isOrganizationOwner" class="pro-card">
      <div class="pro-card-body">
        <div class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <span class="material-icons-outlined text-6xl">block</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">无权限访问</h3>
          <p class="text-gray-600 mb-6">
            您当前是组织成员身份，只有组织管理员可以访问账号管理功能
          </p>
          <div class="text-xs text-gray-500 space-y-1">
            <p>当前组织：{{ currentOrganization?.name }}</p>
            <p>您的角色：组织成员</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 身份信息异常提示 -->
    <div v-else class="pro-card">
      <div class="pro-card-body">
        <div class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <span class="material-icons-outlined text-6xl">error_outline</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">身份信息异常</h3>
          <p class="text-gray-600 mb-6">
            无法确定您的当前身份，请重新登录或联系系统管理员
          </p>
          <el-button type="primary" @click="router.push('/login')">
            重新登录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加子账号对话框 -->
    <el-dialog
      v-model="addAccountDialogVisible"
      title="添加子账号"
      width="600px"
      class="simple-dialog compact-dialog"
      @close="handleCloseAddAccountDialog"
    >
      <div class="simple-content py-4">
        <!-- 按照图片设计的简洁布局 -->
        <div class="space-y-6">
          <!-- 邀请链接区域 -->
          <div v-if="activeInvitations.length > 0" class="space-y-4">
            <!-- 说明文字 -->
            <div class="text-gray-600 text-sm leading-relaxed">
              分享下方链接给需要添加账号的用户，用户自主操作即可加入组织
            </div>
            
            <!-- 链接输入框和复制按钮 -->
            <div class="flex items-center space-x-3">
              <el-input
                :value="activeInvitations[0].invite_link"
                readonly
                class="flex-1"
                placeholder="邀请链接"
              />
              <el-button
                type="primary"
                @click="handleCopyInviteLink(activeInvitations[0].invite_link)"
                class="px-6"
              >
                复制链接
              </el-button>
            </div>

            <!-- 有效期提示 -->
            <div class="text-gray-500 text-sm">
              链接有效期为{{ activeInvitations[0].remaining_hours }}小时
            </div>
          </div>

          <!-- 无邀请链接时自动生成 -->
          <div v-else class="flex items-center justify-center py-8">
            <el-button
              type="primary"
              @click="generateNewInviteLink"
              :loading="loadingInviteLink"
              size="large"
            >
              生成邀请链接
            </el-button>
          </div>
        </div>
      </div>


    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="权限管理"
      width="600px"
      class="!rounded-lg"
      @close="handleClosePermissionDialog"
    >
      <div v-if="selectedAccount" class="space-y-4">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center text-blue-600 font-medium">
            {{ selectedAccount.name?.charAt(0) || '?' }}
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900">{{ selectedAccount.name }}</div>
            <div class="text-xs text-gray-500">加入时间：{{ selectedAccount.createdAt }}</div>
          </div>
        </div>
        
        <el-form :model="permissionForm" label-width="120px">
          <el-form-item label="组织用户名">
            <el-input 
              v-model="permissionForm.organization_username" 
              placeholder="请输入组织用户名"
              maxlength="50"
              show-word-limit
            />
            <div class="text-xs text-gray-500 mt-1">
              成员在组织中的唯一标识
            </div>
          </el-form-item>
          
          <el-form-item label="账号状态">
            <el-switch 
              v-model="permissionForm.is_active"
              active-text="激活"
              inactive-text="停用"
              :active-value="true"
              :inactive-value="false"
              :disabled="selectedAccount && selectedAccount.accountType === 'main'"
            />
            <div class="text-xs text-gray-500 mt-1">
              停用后该成员将无法访问组织功能
            </div>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosePermissionDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSavePermissions"
            :loading="loading"
          >
            保存设置
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建组织对话框 -->
    <el-dialog
      v-model="createOrganizationDialogVisible"
      title="创建组织"
      width="500px"
      class="simple-dialog compact-dialog"
      @close="handleCloseCreateOrganizationDialog"
    >
      <div class="simple-content">
        <div class="mb-6">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-900 mb-3">组织名称</label>
            <el-input
              v-model="organizationForm.name"
              placeholder="请输入组织名称"
              maxlength="100"
              show-word-limit
              @keyup.enter="handleConfirmCreateOrganization"
            />
          </div>
          <p class="text-sm text-gray-600 leading-6">
            组织名称将作为您团队的标识，创建后可以邀请成员加入
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="createOrganizationDialogVisible = false">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="handleConfirmCreateOrganization"
            :loading="createOrganizationLoading"
            :disabled="!organizationForm.name.trim()"
          >
            {{ createOrganizationLoading ? '创建中...' : '创建组织' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import UserAvatar from '@/components/common/UserAvatar.vue'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import { 
  createOrganization,
  getOrganizationMembers, 
  updateOrganizationMember, 
  removeOrganizationMember 
} from '@/api/organizations'
import {
  getMyInvitationLink,
  getActiveInvitationLinks,
  revokeInvitationLink
} from '@/api/invitation'

const authStore = useAuthStore()
const router = useRouter()

// 使用组织权限管理组合式函数
const {
  currentOrganization,
  isOrganizationOwner,
  isOrganizationIdentity,
  isPersonalIdentity,
  canAccessAccountManagement,
  canCreateOrganization
} = useOrganizationPermissions()



// 监听组织状态变化，用于界面更新
watch(currentOrganization, async (newOrg) => {
  if (newOrg && isOrganizationIdentity.value && newOrg.id) {
    // 切换到组织身份时，加载组织数据
    fetchMembers()
    // 如果是Owner，预加载邀请链接（延迟执行确保状态同步）
    if (isOrganizationOwner.value) {
      // 使用nextTick确保所有响应式状态都已更新
      await nextTick()
      loadActiveInvitations()
    }
  } else {
    // 切换到个人身份或无组织时，清空组织数据
    membersData.value = []
    activeInvitations.value = []
  }
})

// 成员列表数据
const membersData = ref([])
const loading = ref(false)

// 搜索
const searchQuery = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const addAccountDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const createOrganizationDialogVisible = ref(false)
const selectedAccount = ref(null)

// 权限管理表单数据
const permissionForm = ref({
  organization_username: '',
  is_active: true
})

// 创建组织表单数据
const organizationForm = ref({
  name: ''
})

// 邀请相关状态
const inviteLink = ref('')
const loadingInviteLink = ref(false)
const createOrganizationLoading = ref(false)
const activeInvitations = ref([])
const loadingActiveInvitations = ref(false)

// 可用权限列表 - 简化为两种角色
const availableRoles = ref([
  { label: '管理员', value: 'owner', description: '拥有所有权限，可以管理组织和成员' },
  { label: '成员', value: 'member', description: '普通成员权限，可以使用组织功能' }
])

// 计算属性：页面标题
const pageTitle = computed(() => {
  if (isOrganizationIdentity.value && currentOrganization.value) {
    return currentOrganization.value.name
  }
  return '账号管理'
})

// 计算属性：筛选后的成员列表
const filteredMembers = computed(() => {
  let filtered = membersData.value

  // 搜索筛选 - 支持姓名、邮箱搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(member =>
      (member.organization_username || '').toLowerCase().includes(query) ||
      (member.user_info?.nickname || '').toLowerCase().includes(query) ||
      (member.user_info?.username || '').toLowerCase().includes(query) ||
      (member.user_info?.email || '').toLowerCase().includes(query)
    )
  }

  return filtered
})

// 计算属性：总页数
const totalPages = computed(() => {
  return Math.ceil(filteredMembers.value.length / pageSize.value)
})

// 计算属性：当前页的成员列表
const paginatedAccounts = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredMembers.value.slice(start, end).map(member => ({
    id: member.user_id,
    name: member.organization_username || member.user_info?.nickname || member.user_info?.username,
    avatar: member.user_info?.avatar_url || '',
    accountType: member.role === 'owner' ? 'main' : 'sub',
    status: member.is_active ? 'normal' : 'disabled',
    permissions: [member.role],
    createdAt: member.joined_at ? formatDate(member.joined_at) : '未知',
    lastActiveAt: null,
    organizationUsername: member.organization_username,
    memberData: member
  }))
})

// 当前登录用户ID
const currentUserId = computed(() => authStore.user?.id)

// 是否可编辑该行
const canEditRow = (row) => {
  if (!isOrganizationOwner.value) return false
  if (row.accountType !== 'main') return true
  return row.id === currentUserId.value
}

// 获取组织成员列表
const fetchMembers = async () => {
  if (!currentOrganization.value) return

  try {
    loading.value = true
    const response = await getOrganizationMembers(currentOrganization.value.id, {
      search: searchQuery.value,
      limit: 100 // 获取更多数据，前端分页
    })
    membersData.value = response
  } catch (error) {
    console.error('获取成员列表失败:', error)
    ElMessage.error('获取成员列表失败')
  } finally {
    loading.value = false
  }
}

// 注意：新的邀请模式不再需要邀请列表，因为每个用户只有一个固定的邀请链接



// 方法：获取账号类型标签
const getAccountTypeLabel = (accountType) => {
  const typeMap = {
    main: '主账号',
    sub: '子账号'
  }
  return typeMap[accountType] || '未知'
}

// 方法：获取账号类型样式
const getAccountTypeClass = (accountType) => {
  const classMap = {
    main: 'bg-purple-100 text-purple-800',
    sub: 'bg-blue-100 text-blue-800'
  }
  return classMap[accountType] || 'bg-gray-100 text-gray-800'
}

// 方法：获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    normal: '正常',
    disabled: '已停用'
  }
  return statusMap[status] || '未知'
}

// 方法：获取状态样式
const getStatusClass = (status) => {
  const classMap = {
    normal: 'bg-green-100 text-green-800',
    disabled: 'bg-red-100 text-red-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

// 方法：获取权限显示文本
const getPermissionsText = (permissions) => {
  if (!permissions || permissions.length === 0) return '无权限'
  
  const permissionMap = {
    owner: '管理员',
    member: '成员'
  }
  
  return permissions.map(p => permissionMap[p] || p).join(', ')
}

// 搜索处理
const handleSearch = () => {
  // 搜索时重置到第一页
  currentPage.value = 1
}

// 分页处理
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 事件处理：创建组织
const handleCreateOrganization = () => {
  organizationForm.value = { name: '' }
  createOrganizationDialogVisible.value = true
}

// 事件处理：关闭创建组织对话框
const handleCloseCreateOrganizationDialog = () => {
  organizationForm.value = { name: '' }
  createOrganizationDialogVisible.value = false
}

// 事件处理：确认创建组织
const handleConfirmCreateOrganization = async () => {
  if (!organizationForm.value.name.trim()) {
    ElMessage.warning('请输入组织名称')
    return
  }

  try {
    createOrganizationLoading.value = true

    const response = await createOrganization({
      name: organizationForm.value.name.trim()
    })

    ElMessage.success('组织创建成功！')
    createOrganizationDialogVisible.value = false

    // 刷新用户身份信息
    await authStore.fetchUserIdentities()

    // 切换到新创建的组织身份
    await authStore.switchUserIdentity({
      identity_type: 'organization',
      organization_id: response.id
    })

    ElMessage.success(`已切换到组织：${response.name}`)

  } catch (error) {
    console.error('创建组织失败:', error)

    let message = '创建组织失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)
  } finally {
    createOrganizationLoading.value = false
  }
}

// 事件处理：打开邀请子账号对话框
const handleAddSubAccount = async () => {
  if (!isOrganizationOwner.value) {
    ElMessage.warning('只有组织管理员可以邀请新成员')
    return
  }

  // 打开对话框
  addAccountDialogVisible.value = true

  // 加载当前有效的邀请链接
  await loadActiveInvitations()

  // 如果没有有效的邀请链接，自动生成一个
  if (activeInvitations.value.length === 0) {
    await generateNewInviteLink()
  }
}

// 加载当前有效的邀请链接列表
const loadActiveInvitations = async () => {
  // 严格的权限检查，防止在身份状态未同步时调用API
  if (!currentOrganization.value?.id || 
      !isOrganizationIdentity.value || 
      !isOrganizationOwner.value) {
    console.log('跳过加载邀请链接：权限不足或身份状态未同步')
    return
  }

  try {
    loadingActiveInvitations.value = true

    const response = await getActiveInvitationLinks(currentOrganization.value.id)
    activeInvitations.value = response.active_invitations || []

  } catch (error) {
    console.error('获取邀请链接列表失败:', error)

    let message = '获取邀请链接列表失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)

  } finally {
    loadingActiveInvitations.value = false
  }
}

// 生成新的邀请链接
const generateNewInviteLink = async () => {
  // 严格的权限检查，防止在身份状态未同步时调用API
  if (!currentOrganization.value?.id || 
      !isOrganizationIdentity.value || 
      !isOrganizationOwner.value) {
    console.log('跳过生成邀请链接：权限不足或身份状态未同步')
    return
  }

  try {
    loadingInviteLink.value = true

    const response = await getMyInvitationLink(currentOrganization.value.id)

    ElMessage.success('新的邀请链接已生成')

    // 重新加载邀请链接列表
    await loadActiveInvitations()

  } catch (error) {
    console.error('生成邀请链接失败:', error)

    let message = '生成邀请链接失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)
    inviteLink.value = '' // 清空链接
  } finally {
    loadingInviteLink.value = false
  }
}

// 撤销邀请链接
const handleRevokeInvitation = async (invitationId) => {
  try {
    await ElMessageBox.confirm(
      '确定要撤销这个邀请链接吗？撤销后该链接将立即失效。',
      '确认撤销',
      {
        confirmButtonText: '确定撤销',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await revokeInvitationLink(invitationId)
    ElMessage.success('邀请链接已撤销')

    // 重新加载邀请链接列表
    await loadActiveInvitations()

  } catch (error) {
    if (error === 'cancel') return

    console.error('撤销邀请链接失败:', error)

    let message = '撤销邀请链接失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)
  }
}

// 事件处理：复制邀请链接
const handleCopyInviteLink = async (linkToCopy) => {
  if (!linkToCopy) {
    ElMessage.warning('邀请链接无效')
    return
  }

  try {
    await navigator.clipboard.writeText(linkToCopy)
    ElMessage.success('邀请链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 格式化时间显示
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return ''

  const date = new Date(dateTimeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 事件处理：编辑权限
const handleEditPermissions = (account) => {
  if (!isOrganizationOwner.value) {
    ElMessage.warning('只有组织管理员可以编辑成员权限')
    return
  }

  // 仅允许编辑自己（owner），禁止编辑其他owner
  if (account.accountType === 'main' && account.id !== currentUserId.value) {
    ElMessage.warning('不能编辑其他管理员的设置')
    return
  }

  selectedAccount.value = account
  permissionForm.value = {
    organization_username: account.organizationUsername,
    is_active: account.status === 'normal'
  }
  permissionDialogVisible.value = true
}

// 事件处理：保存权限
const handleSavePermissions = async () => {
  if (!selectedAccount.value) return

  try {
    loading.value = true

    await updateOrganizationMember(
      currentOrganization.value.id,
      selectedAccount.value.id,
      permissionForm.value
    )

    ElMessage.success('权限更新成功')
    permissionDialogVisible.value = false
    
    // 刷新成员列表
    await fetchMembers()

  } catch (error) {
    console.error('更新权限失败:', error)
    
    let message = '更新权限失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }
    
    ElMessage.error(message)
  } finally {
    loading.value = false
  }
}

// 事件处理：删除账号
const handleDeleteAccount = async (account) => {
  if (!isOrganizationOwner.value) {
    ElMessage.warning('只有组织管理员可以移除成员')
    return
  }

  // 主账号不能删除
  if (account.accountType === 'main') {
    ElMessage.warning('主账号不能移除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要移除成员 "${account.name}" 吗？\n\n移除后该成员将无法访问组织功能。`,
      '移除成员确认',
      {
        confirmButtonText: '确认移除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    await removeOrganizationMember(currentOrganization.value.id, account.id)
    ElMessage.success(`已成功移除成员 "${account.name}"`)
    
    // 刷新成员列表
    await fetchMembers()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error)
      
      let message = '移除成员失败，请重试'
      if (error.response?.data?.detail) {
        message = error.response.data.detail
      }
      
      ElMessage.error(message)
    }
  }
}

// 事件处理：注销账号
const handleDeactivateAccount = async (account) => {
  if (!isOrganizationOwner.value) {
    ElMessage.warning('只有组织管理员可以停用成员')
    return
  }

  // 主账号不能注销
  if (account.accountType === 'main') {
    ElMessage.warning('主账号不能停用')
    return
  }

  // 已经停用的账号不能再次注销
  if (account.status === 'disabled') {
    ElMessage.warning('该账号已经处于停用状态')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要停用账号 "${account.name}" 吗？\n\n停用后该账号将无法登录系统，但可以重新激活。`,
      '停用账号确认',
      {
        confirmButtonText: '确认停用',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    await updateOrganizationMember(
      currentOrganization.value.id,
      account.id,
      { is_active: false }
    )

    ElMessage.success(`已成功停用账号 "${account.name}"`)
    
    // 刷新成员列表
    await fetchMembers()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('停用账号失败:', error)
      
      let message = '停用账号失败，请重试'
      if (error.response?.data?.detail) {
        message = error.response.data.detail
      }
      
      ElMessage.error(message)
    }
  }
}

// 工具方法：格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 生命周期：组件挂载
onMounted(async () => {
  // 获取用户身份信息
  try {
    await authStore.fetchUserIdentities()
  } catch (error) {
    console.error('获取用户身份失败:', error)
    ElMessage.error('获取用户身份信息失败，请刷新页面重试')
  }

  // 等待一个周期让响应式数据更新
  await nextTick()

  // 如果当前是组织身份，加载组织数据（加强守卫）
  if (isOrganizationIdentity.value && currentOrganization.value?.id) {
    await fetchMembers()
    // 如果是Owner，预加载邀请链接
    if (isOrganizationOwner.value) {
      loadActiveInvitations()
    }
  }
})

// 监听搜索变化
watch(searchQuery, () => {
  currentPage.value = 1
})

// 事件处理：关闭添加账号对话框
const handleCloseAddAccountDialog = () => {
  inviteLink.value = ''
  loadingInviteLink.value = false
  addAccountDialogVisible.value = false
}

// 事件处理：关闭权限管理对话框
const handleClosePermissionDialog = () => {
  selectedAccount.value = null
  permissionForm.value = {
    organization_username: '',
    is_active: true
  }
  permissionDialogVisible.value = false
}

// 事件处理：账号操作分发
const handleAccountAction = async (command, account) => {
  switch (command) {
    case 'edit':
      handleEditPermissions(account)
      break
    case 'deactivate':
      await handleDeactivateAccount(account)
      break
    case 'delete':
      await handleDeleteAccount(account)
      break
  }
}
</script>

<style scoped>
/* 组织管理页面紫色主题按钮样式 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

:deep(.el-button--primary.is-link) {
  --el-button-text-color: #4F46E5;
}

:deep(.el-button--primary.is-link:hover) {
  --el-button-hover-text-color: #4338CA;
}

/* 选择框紫色主题 */
:deep(.el-select) {
  --el-color-primary: #4F46E5;
  --el-color-primary-light-3: #6366F1;
  --el-color-primary-light-5: #8B5CF6;
  --el-color-primary-light-7: #A78BFA;
  --el-color-primary-light-8: #C4B5FD;
  --el-color-primary-light-9: #DDD6FE;
  --el-color-primary-dark-2: #4338CA;
}

:deep(.el-select .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

/* 下拉选项紫色主题 */
:deep(.el-select-dropdown .el-select-dropdown__item.is-selected) {
  color: #4F46E5;
  background-color: #F3F4F6;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #DDD6FE;
}

/* 输入框紫色主题 */
:deep(.el-input .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-input .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

/* 对话框确认按钮紫色主题 */
:deep(.el-dialog .el-dialog__footer .el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

/* 消息确认框按钮紫色主题 */
:deep(.el-message-box .el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

/* 全局Element Plus主题变量覆盖 - 确保所有组件使用紫色主题 */
:deep(:root) {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #8B5CF6 !important;
  --el-color-primary-light-7: #A78BFA !important;
  --el-color-primary-light-8: #C4B5FD !important;
  --el-color-primary-light-9: #DDD6FE !important;
  --el-color-primary-dark-2: #4338CA !important;
}

/* 强制覆盖任何可能的蓝色样式 */
:deep(.el-button--primary),
:deep(.el-button[type="primary"]),
:deep(button.el-button--primary) {
  --el-button-bg-color: #4F46E5 !important;
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important;
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important;
  --el-button-active-border-color: #3730A3 !important;
}

/* 确保所有选择框和输入框都使用紫色主题 */
:deep(.el-select),
:deep(.el-input),
:deep(.el-input-number),
:deep(.el-date-editor) {
  --el-color-primary: #4F46E5 !important;
}

/* 移除任何蓝色阴影效果 */
:deep(.el-button--primary:focus),
:deep(.el-button--primary:active),
:deep(.el-select .el-input__wrapper:focus),
:deep(.el-input .el-input__wrapper:focus),
:deep(.el-input-number .el-input__wrapper:focus) {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
}

/* 简洁对话框样式 - 无分割线整体页面，上下紧凑布局 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none; /* 移除分割线 */
    padding: 16px 32px 8px; /* 减少上下间距，保持左右间距 */
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px; /* 最小化上下间距，保持左右间距 */
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px; /* 减少上下间距，保持左右间距 */
    background: #ffffff;
    border-top: none; /* 移除分割线 */
  }

  /* 关闭按钮样式 - 与邀请按钮高度一致 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 确保对话框内的按钮使用紫色主题，与关闭按钮高度一致 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
  }

  .el-button--primary:hover,
  .el-button[type="primary"]:hover {
    background-color: #4338CA !important;
    border-color: #4338CA !important;
    color: #FFFFFF !important;
  }

  .el-button--primary:active,
  .el-button[type="primary"]:active {
    background-color: #3730A3 !important;
    border-color: #3730A3 !important;
    color: #FFFFFF !important;
  }

  .el-button--primary:focus,
  .el-button[type="primary"]:focus {
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
  }

  /* 普通按钮样式 - 与关闭按钮高度一致，悬浮紫色主题 */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
    --el-button-active-text-color: #4F46E5 !important;
    --el-button-active-border-color: #4F46E5 !important;
    --el-button-active-bg-color: #F3F4F6 !important;
  }

  .el-button:not(.el-button--primary):hover {
    color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    background-color: #F8FAFF !important;
  }

  .el-button:not(.el-button--primary):active {
    color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    background-color: #F3F4F6 !important;
  }

  .el-button:not(.el-button--primary):focus {
    color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
  }
}

/* 简洁对话框样式 - 无分割线整体页面，上下紧凑布局 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
  }

  /* 次要按钮样式 */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
  }
}



/* 邀请成员按钮样式 - 使用系统紫色主题 */
.add-member-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.add-member-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

.add-member-button:focus {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
}

/* 创建组织按钮样式 - 使用系统紫色主题 */
.create-org-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
  padding: 12px 32px !important;
}

.create-org-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  color: white !important;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15) !important;
}

.create-org-button:focus {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
}
/* 页面特定样式 */
.account-management-page {
  @apply p-6;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .account-management-page {
    @apply p-4;
  }
}

@media (max-width: 768px) {
  .account-management-page {
    @apply p-3;
  }
  
  .grid.grid-cols-1.md\\:grid-cols-4 {
    @apply grid-cols-2;
  }
}
</style> 