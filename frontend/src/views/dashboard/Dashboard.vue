<template>
  <div class="bg-gray-50 max-w-7xl mx-auto py-6">
    <!-- 欢迎信息卡片 -->
    <div class="bg-gradient-to-r from-primary to-primary-light rounded-xl shadow-lg p-6 mb-6 text-white relative overflow-hidden">
      <!-- 装饰性背景图案 -->
      <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
      <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-5 rounded-full translate-y-6 -translate-x-6"></div>
      
      <div class="relative z-10 flex items-start justify-between">
        <div class="flex-1">
          <h1 class="text-3xl font-bold text-white mb-2">总览</h1>
          <p class="text-white text-opacity-90 text-sm sm:text-base leading-relaxed">{{ timeGreeting }}{{ displayName }}，{{ welcomeMessage }}。</p>
        </div>
        
        <!-- 显示模式切换按钮 - 组织成员可见 -->
        <div v-if="canAccessDisplayModeSwitch" class="flex items-center ml-4">
          <div class="flex bg-white bg-opacity-20 rounded-md p-0.5">
            <button
              @click="handleToggleDisplayMode('full')"
              :class="[
                'px-2 py-1 text-xs font-medium rounded transition-all duration-200',
                displayModeStore.isFullMode()
                  ? 'bg-white text-primary shadow-sm'
                  : 'text-white hover:bg-white hover:bg-opacity-20'
              ]"
            >
              <span class="material-icons-outlined text-xs mr-1 align-middle">visibility</span>
              <span class="hidden sm:inline">完整版</span>
            </button>
            <button
              @click="handleToggleDisplayMode('clean')"
              :class="[
                'px-2 py-1 text-xs font-medium rounded transition-all duration-200',
                displayModeStore.isCleanMode()
                  ? 'bg-white text-primary shadow-sm'
                  : 'text-white hover:bg-white hover:bg-opacity-20'
              ]"
            >
              <span class="material-icons-outlined text-xs mr-1 align-middle">visibility_off</span>
              <span class="hidden sm:inline">纯净版</span>
            </button>
          </div>
        </div>
      </div>
    </div>




    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
      <!-- 左侧：用户信息和积分 -->
      <div class="space-y-6">
        <!-- 当前账户 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-900">当前账户</h2>
            <span class="text-xs text-gray-500">Current Account</span>
          </div>
          
          <div class="flex items-center space-x-3 mb-4">
            <UserAvatar
              :avatar-url="authStore.user?.avatar_url || userStore.userInfo.avatar_url"
              :display-name="displayName"
              size="large"
            />
            <div>
              <div class="font-medium text-gray-900">{{ displayName }}</div>
              <!-- <div class="text-sm text-gray-500">{{ userRole }}</div> -->
            </div>
          </div>

          <div class="space-y-2 text-sm">
            <!-- 隐藏邮箱显示 -->
            <!--
            <div class="flex items-center text-gray-600">
              <span class="material-icons-outlined text-sm mr-2">email</span>
              {{ authStore.user?.email || userStore.userInfo.email }}
            </div>
            -->
            <!-- 组织名称 (仅组织账号显示) -->
            <div v-if="isOrganizationAccount" class="flex items-center text-gray-600">
              <span class="material-icons-outlined text-sm mr-2">business</span>
              组织：{{ organizationName }}
            </div>
            
            <div class="flex items-center text-gray-600">
              <span class="material-icons-outlined text-sm mr-2">
                {{ getRoleIcon() }}
              </span>
              账户类型：{{ getRoleText() }}
            </div>

            <div class="flex items-center text-gray-600">
              <span class="material-icons-outlined text-sm mr-2">schedule</span>
              最后登录：{{ formatLastLogin() }}
            </div>
          </div>
        </div>

        <!-- 我的套餐 - 仅完整版显示 -->
        <div v-if="displayModeStore.isFullMode()" class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <h2 class="text-lg font-semibold text-gray-900">我的套餐</h2>
              <!-- 套餐身份标识 - 显示在标题旁边 -->
              <div v-if="packageIdentityType" class="flex items-center space-x-1 px-2 py-1 bg-gray-50 rounded-full">
                <span class="material-icons-outlined text-xs" :class="packageIdentityType === 'organization' ? 'text-blue-600' : 'text-[#4F46E5]'">
                  {{ packageIdentityType === 'organization' ? 'business' : 'person' }}
                </span>
                <span class="text-xs font-medium" :class="packageIdentityType === 'organization' ? 'text-blue-700' : 'text-[#4F46E5]'">
                  {{ packageIdentityName }}
                </span>
              </div>
            </div>
            <span class="text-xs text-gray-500">My Package</span>
          </div>

          <!-- 有套餐时的显示 -->
          <div v-if="hasPlan && currentPlan" class="space-y-4" :class="{ 'opacity-50': isLoadingPlan }">
            <div class="flex items-center space-x-3 mb-4">
              <span class="material-icons-outlined text-primary text-2xl">workspace_premium</span>
              <div>
                <div class="flex items-center space-x-2">
                  <h3 class="font-medium text-gray-900 text-lg">{{ currentPlan.name }}</h3>
                  <!-- 体验套餐有效状态小绿点 -->
                  <div
                    v-if="isTrialPackage && !currentPlan.isExpired"
                    class="flex items-center space-x-1"
                    title="体验套餐有效"
                  >
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-xs text-green-600 font-medium">有效</span>
                  </div>
                </div>
                <p class="text-sm text-gray-500">{{ currentPlan.description }}</p>
              </div>
            </div>

            <!-- 套餐状态提示 -->
            <div v-if="currentPlan.isExpired" class="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <div class="flex items-center">
                <span class="material-icons-outlined text-red-500 text-sm mr-2">warning</span>
                <span class="text-sm text-red-700 font-medium">套餐已过期</span>
              </div>
            </div>
            <div v-else-if="currentPlan.daysRemaining <= 7" class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
              <div class="flex items-center">
                <span class="material-icons-outlined text-yellow-500 text-sm mr-2">schedule</span>
                <span class="text-sm text-yellow-700 font-medium">套餐将在 {{ currentPlan.daysRemaining }} 天后过期</span>
              </div>
            </div>

            <div class="space-y-3 text-sm">
              <div class="flex items-center justify-between">
                <span class="text-gray-600">开始日期</span>
                <span class="font-medium text-gray-900">{{ formatDate(currentPlan.startDate) }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">到期日期</span>
                <span class="font-medium text-gray-900">{{ formatDate(currentPlan.expiryDate) }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">剩余天数</span>
                <span
                  :class="getRemainingDaysClass(currentPlan.expiryDate)"
                  class="font-medium"
                >
                  {{ getRemainingDays(currentPlan.expiryDate) }}天
                </span>
              </div>
            </div>

            <div class="pt-4 border-t">
              <button
                @click="handleRenewPlan"
                class="w-full px-4 py-2 bg-primary hover:bg-primary-dark text-white text-sm font-medium rounded-lg transition-colors duration-200"
              >
                续费套餐
              </button>
            </div>
          </div>

          <!-- 没有套餐时的显示 -->
          <div v-else class="text-center py-4" :class="{ 'opacity-50': isLoadingPlan }">
            <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-3">
              <span class="material-icons-outlined text-gray-400 text-xl">shopping_cart</span>
            </div>
            <h3 class="text-base font-medium text-gray-900 mb-1">暂无套餐</h3>
            <p class="text-gray-500 text-sm mb-4">
              {{ packageIdentityType === 'organization' ? '为组织购买套餐后即可享受完整服务' : '购买套餐后即可享受完整服务' }}
            </p>
            <button
              @click="handleBuyPlan"
              class="w-full px-4 py-2 bg-primary hover:bg-primary-dark text-white text-sm font-medium rounded-lg transition-colors duration-200"
            >
              立即购买{{ packageIdentityType === 'organization' ? '组织套餐' : '' }}
            </button>
          </div>
        </div>

        <!-- 我的积分 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <h2 class="text-lg font-semibold text-gray-900">我的积分</h2>
              <!-- 身份标识 - 显示在标题旁边 -->
              <div class="flex items-center space-x-1 px-2 py-1 bg-gray-50 rounded-full">
                <span class="material-icons-outlined text-xs" :class="creditIdentityType === 'organization' ? 'text-blue-600' : 'text-[#4F46E5]'">
                  {{ creditIdentityType === 'organization' ? 'business' : 'person' }}
                </span>
                <span class="text-xs font-medium" :class="creditIdentityType === 'organization' ? 'text-blue-700' : 'text-[#4F46E5]'">
                  {{ creditIdentityName }}
                </span>
              </div>
            </div>
            <span class="text-xs text-gray-500">My Credits</span>
          </div>

          <div class="text-center mb-4">
            <div class="text-4xl font-bold mb-1" :class="isLoadingBalance ? 'text-gray-400 animate-pulse' : 'text-gray-900'">
              {{ isLoadingBalance ? '---' : userPoints }}
            </div>
            <div class="text-sm text-gray-500">点</div>
          </div>

          <p class="text-sm text-gray-600 mb-4 leading-relaxed">
            {{ creditIdentityType === 'organization' ? '组织积分用于团队成员共同使用的Token计费。' : '个人积分用于大模型业务使用的Token数计费。' }}
          </p>

          <router-link
            to="/account/recharge"
            class="w-full bg-primary text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors block text-center"
          >
            充值积分
          </router-link>
        </div>


      </div>

      <!-- 中间：功能指引 -->
      <div class="bg-white rounded-xl shadow-sm p-6 h-fit">
        <div class="flex justify-between items-center mb-2">
          <h2 class="text-lg font-semibold text-gray-900">功能指引</h2>
          <span class="text-xs text-gray-500">Feature Guide</span>
        </div>
        <p class="text-sm text-gray-500 mb-6">多种功能助力高效服务客户</p>

        <div class="space-y-4" v-memo="[displayModeStore.mode]">

          <!-- 选校匹配 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/school-assistant')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center flex-shrink-0">
                <AcademicCapIcon class="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">选校匹配</h3>
                <p class="text-sm text-gray-500 mt-1">上万offer分析，院校快速匹配，方案精准生成</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始选校 ›</span>
              </div>
            </div>
          </div>

          <!-- 智能建档 - 仅完整版显示 -->
          <div v-if="displayModeStore.isFullMode()" class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/clients')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center flex-shrink-0">
                <UserIcon class="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">智能建档</h3>
                <p class="text-sm text-gray-500 mt-1">上传客户信息意愿一分钟快速建档</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始建档 ›</span>
              </div>
            </div>
          </div>

          <!-- 文书撰写 - 仅完整版显示 -->
          <div v-if="displayModeStore.isFullMode()" class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/write/cv')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center flex-shrink-0">
                <DocumentTextIcon class="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">文书撰写</h3>
                <p class="text-sm text-gray-500 mt-1">撰写高效高质，低AI率呈现，确保文书不会千篇一律</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始撰写 ›</span>
              </div>
            </div>
          </div>

          <!-- AI降重 - 仅完整版显示 -->
          <div v-if="displayModeStore.isFullMode()" class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/ai-reducer')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-orange-100 flex items-center justify-center flex-shrink-0">
                <PencilSquareIcon class="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">AI降重</h3>
                <p class="text-sm text-gray-500 mt-1">AI快速降重，智能优化润色，规避原意擅改</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始优化 ›</span>
              </div>
            </div>
          </div>

          <!-- 账号管理 - 仅完整版显示 -->
          <div v-if="displayModeStore.isFullMode()" class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer" @click="$router.push('/enterprise/accounts')">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-indigo-100 flex items-center justify-center flex-shrink-0">
                <UserGroupIcon class="w-6 h-6 text-indigo-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">账号管理</h3>
                <p class="text-sm text-gray-500 mt-1">成员便捷管理，部门分类筛选，权限快速分配</p>
                <span class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始管理 ›</span>
              </div>
            </div>
          </div>

          <!-- CRM 客户管理 - 即将开放，在完整版和纯净版都显示 -->
          <div class="p-4 rounded-lg border border-gray-200 bg-gray-50 opacity-60 cursor-not-allowed">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-gray-200 flex items-center justify-center flex-shrink-0">
                <ChartBarIcon class="w-6 h-6 text-gray-400" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-600">CRM客户管理</h3>
                <p class="text-sm text-gray-400 mt-1">潜在客户跟进，签约客户服务进度可视化，提升团队协作效率</p>
                <span class="text-gray-400 text-sm mt-2 inline-block">即将开放</span>
              </div>
            </div>
          </div>

        </div>
      </div>

      <!-- 右侧：近期客户和更新动态 -->
      <div>
        <!-- 近期客户 -->
        <div class="bg-white rounded-xl shadow-sm p-4">
          <div class="flex justify-between items-center mb-3">
            <div>
              <h2 class="text-base font-semibold text-gray-900">近期客户</h2>
              <p class="text-xs text-gray-500">近期服务的客户</p>
            </div>
            <router-link
              to="/clients"
              class="text-xs text-primary hover:text-primary-dark flex items-center"
            >
              查看全部
              <span class="material-icons-outlined text-xs ml-1">arrow_forward</span>
            </router-link>
          </div>

          <!-- 客户列表容器 - 限制高度并支持滚动，最多显示5个客户 -->
          <div class="max-h-52 overflow-y-auto">
            <div class="space-y-3">
              <div v-if="isLoading" class="space-y-2">
                <div v-for="i in 3" :key="i" class="flex items-center justify-between p-2 animate-pulse">
                  <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 rounded-full bg-gray-200"></div>
                    <div>
                      <div class="h-3 bg-gray-200 rounded w-20 mb-1"></div>
                      <div class="h-2 bg-gray-200 rounded w-12"></div>
                    </div>
                  </div>
                  <div class="h-2 bg-gray-200 rounded w-12"></div>
                </div>
              </div>

              <div v-else-if="recentClients.length === 0" class="text-center py-4">
                <p class="text-xs text-gray-500">暂无近期客户</p>
              </div>

              <div
                v-else
                v-for="client in recentClients"
                :key="client.id_hashed"
                class="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer"
                @click="handleViewClient(client.id_hashed)"
              >
                <div class="flex items-center space-x-2">
                  <div class="w-6 h-6 rounded-full bg-primary bg-opacity-10 flex items-center justify-center text-primary text-xs">
                    {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                  </div>
                  <div>
                    <div class="font-medium text-xs text-gray-900">{{ client.name }}</div>
                    <div class="text-xs text-gray-500">{{ client.location || '未知地区' }}</div>
                  </div>
                </div>
                <div class="text-xs text-gray-500">
                  {{ formatDate(client.updated_at || client.updatedAt) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 更新动态 - 仅完整版显示 -->
        <div v-if="displayModeStore.isFullMode()" class="bg-white rounded-xl shadow-sm p-4 mt-4">
          <div class="flex justify-between items-center mb-3">
            <h2 class="text-base font-semibold text-gray-900">更新</h2>
            <span class="text-xs text-gray-500">What's new</span>
          </div>
          <div class="space-y-3" v-memo="[updates.length]">
            <div v-for="update in updates" :key="update.id" class="border-l-2 border-primary pl-3 py-2">
              <div class="flex items-center space-x-2 text-xs text-gray-500">
                <span>📅</span>
                <span>{{ update.date }}</span>
              </div>
              <h3 class="text-sm font-medium text-gray-900 mt-1">{{ update.title }}</h3>
              <p class="text-xs text-gray-500 mt-1 leading-relaxed">{{ update.content }}</p>
            </div>
          </div>
        </div>

        <!-- 邀请码卡片 - 仅个人身份显示 -->
        <div v-if="!isOrganizationAccount" class="bg-white rounded-xl shadow-sm p-4 mt-4">
          <div class="flex justify-between items-center mb-3">
            <h2 class="text-base font-semibold text-gray-900">邀请好友</h2>
            <span class="text-xs text-gray-500">Invite Friends</span>
          </div>

          <div v-if="invitationLoading" class="flex items-center justify-center py-4">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>

          <div v-else-if="invitationData" class="space-y-4">
            <!-- 邀请码显示 -->
            <div>
              <label class="block text-xs font-medium text-gray-600 mb-3">我的邀请码</label>
              <div class="flex items-center space-x-3">
                <div class="flex-1 px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg font-mono text-sm tracking-wider text-center">
                  {{ invitationData.invitation_code }}
                </div>
                <button
                  @click="handleCopyInvitationLink"
                  class="px-4 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors text-xs"
                >
                  复制
                </button>
              </div>
            </div>

            <!-- 邀请说明和查看详情 -->
            <div class="text-xs text-gray-500 leading-relaxed pt-1">
              <div class="mb-2">邀请好友注册，双方都可获得3天试用套餐</div>
              <router-link
                to="/user/invitation"
                class="text-primary hover:text-primary-dark transition-colors"
              >
                查看详细统计 →
              </router-link>
            </div>
          </div>

          <div v-else class="text-center py-4">
            <div class="text-xs text-gray-500 mb-2">获取邀请码失败</div>
            <button
              @click="loadInvitationData"
              class="text-xs text-primary hover:text-primary-dark transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAuthStore } from '@/stores/auth'
import { useDisplayModeStore } from '@/stores/displayMode'
import { useRouter, useRoute } from 'vue-router'
import { getClientList } from '@/api/client'
import { getCreditBalance, getCreditAccount } from '@/api/account'
import { getPackageStatus } from '@/api/validation'
import { getMyPersonalInvitationLink } from '@/api/invitation'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import UserAvatar from '@/components/common/UserAvatar.vue'
import {
  DocumentTextIcon,
  PencilSquareIcon,
  UserIcon,
  AcademicCapIcon,
  ChartBarIcon,
  UserGroupIcon
} from '@heroicons/vue/24/outline'

dayjs.locale('zh-cn')

const userStore = useUserStore()
const authStore = useAuthStore()
const displayModeStore = useDisplayModeStore()
const router = useRouter()
const route = useRoute()

// 用户显示名称
const displayName = computed(() => {
  return authStore.user?.nickname ||
         authStore.user?.username ||
         userStore.userInfo.nickname ||
         userStore.userInfo.username ||
         'Pocky'
})

// 判断是否为组织账号
const isOrganizationAccount = computed(() => {
  // 检查 currentIdentity 中的身份类型
  const identityType = authStore.currentIdentity?.identity_type
  const organizationId = authStore.currentIdentity?.organization_id
  
  // 如果身份类型是 organization，则为组织账号
  return identityType === 'organization' && organizationId
})

// 判断是否可以访问显示模式切换功能
const canAccessDisplayModeSwitch = computed(() => {
  // 组织的任何成员都可以选择完整版和纯净版显示模式
  return isOrganizationAccount.value
})

// 组织名称
const organizationName = computed(() => {
  // 从 currentIdentity 中获取组织名称
  const orgName = authStore.currentIdentity?.organization_name || 
                  authStore.currentIdentity?.organization?.name ||
                  authStore.user?.organization?.name ||
                  authStore.user?.organization_name ||
                  userStore.userInfo.organization?.name ||
                  userStore.userInfo.organization_name
  
  return orgName || '未知组织'
})

// 欢迎消息
const welcomeMessage = computed(() => {
  if (isOrganizationAccount.value) {
    return `欢迎回到${organizationName.value}管理系统`
  } else {
    return '欢迎回到TunshuEdu'
  }
})

// 用户角色显示 - 已隐藏
// const userRole = computed(() => {
//   return '开发者'
// })

// 智能时间问候语
const timeGreeting = computed(() => {
  const hour = new Date().getHours()
  
  if (hour >= 5 && hour < 12) {
    return '上午好'
  } else if (hour >= 12 && hour < 18) {
    return '下午好'
  } else if (hour >= 18 && hour < 23) {
    return '晚上好'
  } else {
    return '夜深了'
  }
})


// 积分相关数据
const userPoints = ref(0)
const isLoadingBalance = ref(false)
const creditIdentityType = ref('personal')
const creditIdentityName = ref('个人身份')

// 套餐相关数据
const hasPlan = ref(false)
const currentPlan = ref(null)
const isLoadingPlan = ref(false)
const packageIdentityType = ref('')
const packageIdentityName = ref('')

// 初始加载状态
const isInitialLoading = ref(true)

// 邀请码相关数据
const invitationData = ref(null)
const invitationLoading = ref(false)

// 判断是否为体验套餐
const isTrialPackage = computed(() => {
  return currentPlan.value?.name === '体验套餐'
})

// 近期客户数据
const recentClients = ref([])
const isLoading = ref(true)

const updates = ref([
  {
    id: 2,
    title: 'TunshuEdu V0.2 Beta',
    content: '亲爱的用户，我们很高兴地通知您，我们进行了一次重要更新。本次更新正式上线了文书写作、AI降查重工具和组织管理功能。基于先进的AI技术和大数据分析，为留学申请提供全方位智能化服务，涵盖个人档案管理、智能选校匹配、专业文书创作等核心环节，致力于为每位用户提供更高效、更精准、更个性化的留学申请解决方案。',
    date: '2025-08-17'
  },
  {
    id: 1,
    title: 'TunshuEdu V0.1 Beta',
    content: '我们很高兴地宣布TunshuEdu V0.1 Beta版正式上线！本次发布的核心功能是智能定校模块，基于先进的AI算法为留学申请者提供个性化的学校和专业推荐。系统能够根据学生的学术背景等多维度因素，智能匹配最适合的院校和专业，让留学申请更加精准高效。',
    date: '2025-07-22'
  }
])

// 添加日期格式化函数
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD')
}

// 获取近期客户列表（支持静默刷新）
const fetchRecentClients = async (silent = false) => {
  try {
    if (!silent) {
      isLoading.value = true
    }
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000)

    const response = await getClientList({
      limit: 5,
      sort: 'updated_at:desc',
      signal: controller.signal,
      _t: new Date().getTime()
    })

    clearTimeout(timeoutId)

    let clients = []
    if (Array.isArray(response)) {
      clients = response
    } else if (response?.items && Array.isArray(response.items)) {
      clients = response.items
    } else if (response?.data && Array.isArray(response.data)) {
      clients = response.data
    } else {
      if (!silent) {
        console.warn('获取客户列表响应格式不符合预期:', response)
      }
    }

    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.updatedAt || 0)
      const dateB = new Date(b.updated_at || b.updatedAt || 0)
      return dateB - dateA
    })

    recentClients.value = clients.slice(0, 5)
    
    if (!silent) {
      console.log('近期客户数据刷新完成，客户数量:', recentClients.value.length)
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      if (!silent) console.warn('获取客户列表请求超时')
    } else {
      if (!silent) console.error('获取近期客户失败:', error)
    }
    recentClients.value = []
  } finally {
    if (!silent) {
      isLoading.value = false
    }
  }
}

// 查看客户详情
const handleViewClient = (clientId) => {
  router.push(`/clients/${clientId}`)
}

// 获取积分余额（支持静默刷新）
const fetchCreditBalance = async (silent = false) => {
  try {
    if (!silent) {
      isLoadingBalance.value = true
    }
    const response = await getCreditBalance()

    if (response.success) {
      userPoints.value = response.balance || 0
      creditIdentityType.value = response.identity_type || 'personal'
      creditIdentityName.value = response.identity_name || '个人身份'
      
      if (!silent) {
        console.log('积分余额信息:', {
          balance: response.balance,
          identityType: response.identity_type,
          identityName: response.identity_name,
          organizationId: response.organization_id
        })
      }
    } else {
      if (!silent) {
        console.warn('获取积分余额失败:', response.message)
      }
    }
  } catch (error) {
    if (!silent) {
      console.error('获取积分余额失败:', error)
      if (error.response?.status === 401) {
        console.warn('用户未认证，无法获取积分余额')
      }
    }
  } finally {
    if (!silent) {
      isLoadingBalance.value = false
    }
  }
}

// 获取套餐状态（支持静默刷新）
const fetchPackageStatus = async (silent = false) => {
  try {
    if (!silent) {
      isLoadingPlan.value = true
    }
    const response = await getPackageStatus()

    if (response.success) {
      const packageData = response.data
      hasPlan.value = packageData.has_package
      
      // 获取身份信息
      packageIdentityType.value = packageData.identity_type || ''
      packageIdentityName.value = packageData.identity_name || ''

      if (packageData.has_package) {
        // 根据API文档的数据结构映射
        currentPlan.value = {
          name: packageData.package_name,
          description: '选校规划',
          startDate: packageData.purchase_time,
          expiryDate: packageData.expires_at,
          isExpired: packageData.is_expired || packageData.expiry_status?.is_expired,
          daysRemaining: packageData.days_remaining || packageData.expiry_status?.days_remaining || 0
        }
        
        if (!silent) {
          console.log('套餐状态信息:', {
            hasPackage: packageData.has_package,
            identityType: packageData.identity_type,
            identityName: packageData.identity_name,
            organizationId: packageData.organization_id,
            packageName: packageData.package_name
          })
        }
      } else {
        currentPlan.value = null
        if (!silent) {
          console.log('当前身份无套餐:', {
            identityType: packageData.identity_type,
            identityName: packageData.identity_name
          })
        }
      }
    } else {
      if (!silent) {
        console.warn('获取套餐状态失败:', response.message)
      }
    }
  } catch (error) {
    if (!silent) {
      console.error('获取套餐状态失败:', error)
      if (error.response?.status === 401) {
        console.warn('用户未认证，无法获取套餐状态')
      }
    }
  } finally {
    if (!silent) {
      isLoadingPlan.value = false
    }
  }
}



// 文书撰写点击事件
const handleWritingClick = () => {
  // 可以跳转到文书选择页面，或者显示文书类型选择菜单
  router.push('/write/ps')
}

// 显示模式切换事件
const handleToggleDisplayMode = (mode) => {
  if (mode === 'full') {
    displayModeStore.setFullMode()
  } else if (mode === 'clean') {
    displayModeStore.setCleanMode()
  }
}

// 格式化最后登录时间
const formatLastLogin = () => {
  // 获取当前时间作为最后登录时间
  const now = dayjs()
  return now.format('YYYY-MM-DD HH:mm')
}

// 获取用户角色对应的图标
const getRoleIcon = () => {
  // 如果是组织账号，显示组织相关图标
  if (isOrganizationAccount.value) {
    const orgRole = authStore.currentIdentity?.role || 
                   authStore.currentIdentity?.organization_role ||
                   authStore.user?.organization_role || 
                   authStore.user?.role_in_organization ||
                   userStore.userInfo.organization_role ||
                   userStore.userInfo.role_in_organization
    
    switch (orgRole) {
      case 'admin':
      case 'owner':
        return 'admin_panel_settings'
      case 'member':
      default:
        return 'groups'
    }
  }
  
  // 如果不是组织账号，显示系统角色图标
  const role = authStore.user?.role || userStore.userInfo.role
  switch (role) {
    case 'admin':
      return 'admin_panel_settings'
    case 'dev':
      return 'code'
    case 'user':
    default:
      return 'person'
  }
}

// 获取用户角色对应的文本
const getRoleText = () => {
  // 如果是组织账号，显示组织内角色
  if (isOrganizationAccount.value) {
    const orgRole = authStore.currentIdentity?.role || 
                   authStore.currentIdentity?.organization_role ||
                   authStore.user?.organization_role || 
                   authStore.user?.role_in_organization ||
                   userStore.userInfo.organization_role ||
                   userStore.userInfo.role_in_organization
    

    
    switch (orgRole) {
      case 'admin':
      case 'owner':
        return '管理员'
      case 'member':
      default:
        return '成员'
    }
  }
  
  // 如果不是组织账号，显示系统角色
  const role = authStore.user?.role || userStore.userInfo.role
  switch (role) {
    case 'admin':
      return '管理员账户'
    case 'dev':
      return '开发者账户'
    case 'user':
    default:
      return '普通账户'
  }
}

// 套餐相关方法

const getRemainingDays = (expiryDate) => {
  const now = dayjs()
  const expiry = dayjs(expiryDate)
  const diff = expiry.diff(now, 'day')
  return Math.max(0, diff)
}

const getRemainingDaysClass = (expiryDate) => {
  const remainingDays = getRemainingDays(expiryDate)
  if (remainingDays <= 0) return 'text-red-600'
  if (remainingDays <= 30) return 'text-yellow-600'
  return 'text-green-600'
}

const handleRenewPlan = () => {
  // 跳转到套餐续费页面
  router.push('/account/recharge')
}

const handleBuyPlan = () => {
  // 跳转到套餐购买页面
  router.push('/account/recharge')
}

// 邀请码相关方法
const loadInvitationData = async () => {
  try {
    invitationLoading.value = true
    const response = await getMyPersonalInvitationLink()
    invitationData.value = response
  } catch (error) {
    console.error('获取邀请链接失败:', error)
    invitationData.value = null
  } finally {
    invitationLoading.value = false
  }
}

const handleCopyInvitationLink = async () => {
  if (!invitationData.value?.invite_link) return

  try {
    await navigator.clipboard.writeText(invitationData.value.invite_link)
    ElMessage.success('邀请链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 在组件挂载时获取数据
onMounted(async () => {
  // 保持用户选择的显示模式，不强制重置
  
  // 检查是否有权限错误参数
  if (route.query.error === 'permission_denied') {
    ElMessage.warning('权限不足：只有组织管理员才能进行账户充值操作')
    // 清除URL参数
    router.replace('/dashboard')
  } else if (route.query.error === 'permission_check_failed') {
    ElMessage.error('权限检查失败，请稍后重试')
    // 清除URL参数
    router.replace('/dashboard')
  }

  try {
    // 先加载基础数据
    const basicResults = await Promise.allSettled([
      userStore.fetchUserInfo(true),
      authStore.fetchCurrentIdentity(), // 获取当前身份信息
      fetchCreditBalance(),
      fetchPackageStatus(),
      fetchRecentClients() // 获取近期客户
    ])
    
    basicResults.forEach((result, index) => {
      if (result.status === 'rejected') {
        const apis = ['用户信息', '当前身份', '积分余额', '套餐状态', '近期客户']
        console.warn(`${apis[index]}加载失败:`, result.reason)
      }
    })
    
    // 如果是个人身份，才加载邀请数据
    if (!isOrganizationAccount.value) {
      try {
        await loadInvitationData()
      } catch (error) {
        console.warn('邀请码加载失败:', error)
      }
    }
    
    console.log('Dashboard数据加载完成')
  } catch (error) {
    console.error('加载数据时出错:', error)
  } finally {
    // 初始加载完成
    isInitialLoading.value = false
  }
})

// 监听身份变化，自动刷新积分和套餐数据
watch(
  () => authStore.currentIdentity,
  async (newIdentity, oldIdentity) => {
    // 如果有新的身份信息，就刷新数据
    if (newIdentity) {
      // 检查是否是真正的身份变化（排除页面初始化时的情况）
      const isIdentityChange = oldIdentity && newIdentity && 
        oldIdentity !== null && // 确保不是从null变化来的（页面初始化）
        (newIdentity.identity_type !== oldIdentity.identity_type || 
         newIdentity.organization_id !== oldIdentity.organization_id)
      
      if (isIdentityChange) {
        console.log('Dashboard检测到身份切换，自动刷新数据:', {
          old: oldIdentity,
          new: newIdentity
        })
        
        // 身份切换时静默刷新积分、套餐和近期客户数据
        const results = await Promise.allSettled([
          fetchCreditBalance(true), // 静默刷新
          fetchPackageStatus(true), // 静默刷新
          fetchRecentClients(true)  // 静默刷新近期客户
        ])
        
        results.forEach((result, index) => {
          if (result.status === 'rejected') {
            const apis = ['积分余额', '套餐状态', '近期客户']
            console.warn(`Dashboard身份切换后${apis[index]}刷新失败:`, result.reason)
          }
        })
        
        // 切换角色时自动显示完整版
        displayModeStore.setFullMode()
        
        // 处理邀请数据：个人身份加载，组织身份清除
        if (newIdentity.identity_type === 'personal') {
          // 切换到个人身份，加载邀请数据
          try {
            await loadInvitationData()
          } catch (error) {
            console.warn('切换到个人身份后邀请码加载失败:', error)
          }
        } else {
          // 切换到组织身份，清除邀请数据
          invitationData.value = null
          invitationLoading.value = false
        }
      }
    }
  },
  { deep: true } // 深度监听对象变化
)

// 自动刷新数据的方法
const refreshAllData = async (silent = true) => {
  try {
    const results = await Promise.allSettled([
      fetchCreditBalance(silent),
      fetchPackageStatus(silent),
      fetchRecentClients(silent)
    ])
    
    if (!silent) {
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          const apis = ['积分余额', '套餐状态', '近期客户']
          console.warn(`${apis[index]}刷新失败:`, result.reason)
        }
      })
    }
  } catch (error) {
    if (!silent) {
      console.error('Dashboard数据刷新失败:', error)
    }
  }
}
</script>

<style scoped>
/* Material Icons 支持 */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: 1fr 1.5fr 1fr;
  }
}

/* 添加渐变动画 */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient 6s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 添加卡片悬停效果 */
.rounded-lg {
  transition: all 0.3s ease;
}

.rounded-lg:hover {
  transform: translateY(0px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px 0px rgba(0, 0, 0, 0.05);
}

/* 添加内容加载动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(0px); }
  to { opacity: 1; transform: translateY(0); }
}

.bg-white {
  animation: fadeIn 0.3s ease-out forwards;
}
</style>