<template>
  <div class="h-screen bg-white flex overflow-hidden">
    <!-- 左侧表单区域 -->
    <div class="w-96 flex-shrink-0 bg-white border-r border-gray-200">
      <div class="h-full overflow-y-auto px-6 pt-6 pb-16">
        <!-- 页面标题 -->
        <div class="mb-6">
          <h2 class="text-lg font-medium text-gray-900">简历</h2>
        </div>
        
        <div class="space-y-6">
          <!-- 客户档案 -->
            <div>
                          <div class="flex items-center space-x-2 mb-3">
                <span class="material-icons-outlined text-[#4F46E5] text-lg">person</span>
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <span class="text-red-500 mr-1">*</span>客户档案
                </span>
                <button 
                  type="button"
                  @click="navigateToClientList"
                  class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium ml-auto flex items-center"
                >
                  + 新建档案
                </button>
              </div>

            <!-- 已选择客户信息显示 -->
            <div v-if="selectedClient" class="mb-3">
              <div class="flex items-center p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                <div class="w-8 h-8 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-xs font-medium mr-3">
                  {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                </div>
                <div class="flex-1">
                                      <div class="font-medium text-gray-800 text-sm">{{ selectedClient.name }}</div>
                    <div class="text-xs text-gray-500">
                      {{ selectedClient.school || '暂无学校信息' }}
                    </div>
                </div>
                <button 
                  @click="clearSelectedClient"
                  class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <span class="material-icons-outlined text-sm">clear</span>
                </button>
              </div>
            </div>

            <!-- 客户选择搜索框 -->
            <div v-else class="relative client-selector">
              <AnimatedInput
                v-model="clientSearchQuery"
                label="搜索客户"
                placeholder="输入客户姓名、联系方式或学生ID"
                type="input"
                @input="handleClientSearch"
                @focus="handleClientSearchFocus"
              />

              <!-- 搜索结果下拉框 -->
              <Teleport to="body">
                <div 
                  v-if="showClientSelector && (clientSearchResults.length > 0 || clientSearchLoading)" 
                  ref="clientDropdown"
                  class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto client-dropdown"
                  :style="dropdownStyle"
                >
                  <!-- 搜索结果 -->
                  <div v-if="clientSearchResults.length > 0" class="p-1">
                    <div 
                      v-for="client in clientSearchResults" 
                      :key="client.id_hashed"
                      @click="selectClient(client)"
                      class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                    >
                      <div class="w-6 h-6 rounded-full bg-[#4F46E5]/10 text-[#4F46E5] flex items-center justify-center text-xs font-medium mr-2">
                        {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                      </div>
                                              <div class="flex-1">
                          <div class="font-medium text-gray-800 text-xs">{{ client.name }}</div>
                          <div class="text-xs text-gray-500">
                            {{ client.school || '暂无学校信息' }}
                          </div>
                        </div>
                    </div>
                  </div>

                  <!-- 无搜索结果 -->
                  <div v-else-if="clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                    未找到匹配的客户
                  </div>
                  
                  <!-- 默认提示（首次点击且没有搜索内容时） -->
                  <div v-else-if="!clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                    输入客户姓名、联系方式或学生ID进行搜索
                  </div>

                  <!-- 加载状态 -->
                  <div v-else-if="clientSearchLoading" class="p-3 text-center text-gray-500 text-xs">
                    <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                    搜索中...
                  </div>
                </div>
              </Teleport>
            </div>
          </div>

          <!-- CV版本名称 -->
          <div v-if="selectedClient">
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-[#4F46E5] text-lg">label</span>
              <span class="text-sm font-medium text-gray-700 flex items-center">
                <span class="text-red-500 mr-1">*</span>CV版本名称
              </span>
            </div>

            <AnimatedInput
              v-model="formData.versionName"
              label="为这份CV命名"
              placeholder="例如：商科方向、工程方向、金融申请专用等"
              type="input"
              :required="true"
            />

            <div class="text-xs text-gray-500 mt-1">
              建议根据申请方向或用途命名，便于后续在PS生成时选择合适的CV版本
            </div>
          </div>

          <!-- 选择经历 -->
          <div v-if="selectedClient && clientModuleData">
            <label class="form-label text-red-500">
              <span class="text-red-500 mr-1">*</span>选择经历
              <span class="text-sm text-[#4F46E5] font-medium">(已选 {{ totalSelectedCount }} 项)</span>
            </label>
            
            <div class="space-y-4 max-h-80 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-gray-50"
                 v-if="Object.keys(clientModuleData).some(key => clientModuleData[key]?.length > 0)">
              
              <!-- 如果没有任何经历数据时的提示 -->
              <div v-if="!Object.keys(clientModuleData).some(key => clientModuleData[key]?.length > 0)" 
                   class="text-center text-gray-500 py-8">
                该客户暂无可选择的经历数据
              </div>
              <!-- 教育经历 -->
              <div v-if="clientModuleData.education?.length > 0" class="experience-section">
                <h3 class="section-title">教育经历</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.education" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedEducationIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedEducationIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.school }} - {{ item.major || '专业未填' }}</span>
                  </label>
                </div>
              </div>

              <!-- 学术经历 -->
              <div v-if="clientModuleData.academic?.length > 0" class="experience-section">
                <h3 class="section-title">学术经历</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.academic" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedAcademicIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedAcademicIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.title || '学术项目' }}</span>
                  </label>
                </div>
              </div>

              <!-- 工作经历 -->
              <div v-if="clientModuleData.work?.length > 0" class="experience-section">
                <h3 class="section-title">工作经历</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.work" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedWorkIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedWorkIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.company || '公司' }} - {{ item.position || '职位' }}</span>
                  </label>
                </div>
              </div>

              <!-- 课外活动 -->
              <div v-if="clientModuleData.activities?.length > 0" class="experience-section">
                <h3 class="section-title">课外活动</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.activities" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedActivityIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedActivityIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.name || '课外活动' }}</span>
                  </label>
                </div>
              </div>

              <!-- 荣誉奖项 -->
              <div v-if="clientModuleData.awards?.length > 0" class="experience-section">
                <h3 class="section-title">荣誉奖项</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.awards" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedAwardIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedAwardIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.name || '奖项' }}</span>
                  </label>
                </div>
              </div>

              <!-- 技能特长 -->
              <div v-if="clientModuleData.skills?.length > 0" class="experience-section">
                <h3 class="section-title">技能特长</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.skills" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedSkillIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedSkillIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.type }}: {{ item.description }}</span>
                  </label>
                </div>
              </div>

              <!-- 语言成绩 -->
              <div v-if="clientModuleData.language_scores?.length > 0" class="experience-section">
                <h3 class="section-title">语言成绩</h3>
                <div class="section-items">
                  <label v-for="item in clientModuleData.language_scores" :key="item.id" class="experience-item">
                    <el-checkbox 
                      :model-value="formData.selectedLanguageScoreIds.includes(item.id)" 
                      @change="() => toggleSelection('selectedLanguageScoreIds', item.id)" 
                      size="small"
                    />
                    <span class="item-content">{{ item.type?.toUpperCase() }} - {{ item.score }}</span>
                  </label>
                </div>
              </div>
            </div>
            </div>

            <!-- 简历语言 -->
            <div>
              <label class="form-label">简历语言</label>
              <div class="flex space-x-3">
                <button 
                  @click="formData.language = 'english'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.language === 'english' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  英文
                </button>
                <button 
                  @click="formData.language = 'chinese'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.language === 'chinese' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  中文
                </button>
              </div>
            </div>

            <!-- 额外信息 -->
            <div>
              <label class="form-label">额外信息</label>
              <el-input
                v-model="formData.additionalInfo"
                type="textarea"
              :rows="7"
              placeholder="请输入补充信息..."
                class="form-textarea"
            ></el-input>
          </div>

          <!-- 版本信息 -->
          <!-- <div class="space-y-4">
            <!-- 版本名称 -->
            <!-- <div>
              <label class="form-label">版本名称</label>
              <el-input
                v-model="formData.versionName"
                placeholder="例如：申请MIT版本、初稿版本等..."
                class="form-input"
              ></el-input>
            </div> -->

            <!-- 目标专业 -->
            <!-- <div>
              <label class="form-label">目标专业</label>
              <el-input
                v-model="formData.targetMajor"
                placeholder="请输入目标专业"
                class="form-input"
              ></el-input>
            </div>
          </div> -->

            <!-- 底部按钮区域 -->
            <div class="pt-6 border-t border-gray-100 mt-6 pb-4">
            <!-- 生成按钮 -->
              <button 
              v-if="!isGenerating"
                @click="handleGenerateCV" 
              :disabled="!isFormValid"
                :class="[
                'w-full py-3 px-4 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center relative overflow-hidden',
                isFormValid
                  ? 'bg-[#4F46E5] hover:bg-[#4338CA] shadow-sm cursor-pointer hover:shadow-lg'
                    : 'bg-gray-400 cursor-not-allowed'
                ]"
              >
              <span class="flex items-center">
                <span class="material-icons-outlined mr-2 text-lg">auto_fix_high</span>
                开始智能生成
              </span>
            </button>
            
            <!-- 生成中状态和取消按钮 -->
            <div v-else class="space-y-3">
              <!-- 生成状态显示 -->
              <div class="w-full py-3 px-4 rounded-lg bg-[#4F46E5] text-white font-medium flex items-center justify-center relative overflow-hidden">
                <span class="flex items-center">
                  <span class="material-icons-outlined animate-spin mr-2 text-lg">auto_fix_high</span>
                  <span class="typing-text">AI正在智能生成中</span>
                  <span class="dots">
                    <span class="dot">.</span>
                    <span class="dot">.</span>
                    <span class="dot">.</span>
                </span>
                </span>
                <!-- 生成中的进度条效果 -->
                <div class="absolute bottom-0 left-0 h-1 bg-white/30 rounded-full progress-bar"></div>
              </div>
              
              <!-- 取消按钮 -->
              <button 
                @click="handleCancelGeneration"
                class="w-full py-2 px-4 rounded-lg border border-gray-300 text-gray-600 hover:text-gray-800 hover:border-gray-400 transition-colors duration-200 flex items-center justify-center"
              >
                <span class="material-icons-outlined mr-2 text-sm">stop</span>
                取消生成
              </button>
            </div>
            
            <!-- 温馨提示 -->
            <div class="mt-3 text-xs text-gray-500 text-center">
              <span class="material-icons-outlined text-xs align-middle mr-1">info</span>
              <span v-if="!isGenerating">根据您选择的经历生成个性化简历</span>
              <span v-else>正在使用先进AI技术为您量身定制简历内容...</span>
            </div>
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧编辑器区域 -->
    <div class="flex-1 bg-white h-full overflow-hidden">
      <TiptapEditor
        v-model="cvContent"
        document-type="cv"
        placeholder="选择客户档案并点击左侧'开始智能生成'获取AI智能简历，生成后可以编辑并保存..."
        @save="handleSave"
        @export="handleExport"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted, onBeforeUnmount, Teleport } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router'
import TiptapEditor from '@/components/writing/TiptapEditor.vue'
import AnimatedInput from '@/components/common/AnimatedInput.vue'
import { Packer } from 'docx'
import html2pdf from 'html2pdf.js'
import { saveAs } from 'file-saver'
import { pdfStyles, createDocxFromHtml } from '@/utils/exportStyles.js'

import { getSchoolLogo } from '@/utils/schoolLogos'
import { searchClients } from '@/api/client'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 客户档案相关状态
const selectedClient = ref(null)
const showClientSelector = ref(false)
const clientSearchQuery = ref('')
const clientSearchResults = ref([])
const clientSearchLoading = ref(false)
const clientModuleData = ref(null)
const clientDropdown = ref(null)
const dropdownStyle = ref({})



// 表单数据
const formData = reactive({
  versionName: '', // CV版本名称
  language: 'english',
  additionalInfo: '',
  selectedEducationIds: [],
  selectedAcademicIds: [],
  selectedWorkIds: [],
  selectedActivityIds: [],
  selectedAwardIds: [],
  selectedSkillIds: [],
  selectedLanguageScoreIds: []
})

// CV内容
const cvContent = ref('')
const isGenerating = ref(false)
const abortController = ref(null)

// 验证表单是否有效
const isFormValid = computed(() => {
  return selectedClient.value !== null && formData.versionName.trim() !== ''
})

// 计算总选择数
const totalSelectedCount = computed(() => {
  return Object.keys(formData).filter(key => key.startsWith('selected')).reduce((sum, key) => {
    // 确保formData[key]存在且是数组
    const value = formData[key];
    if (Array.isArray(value)) {
      return sum + value.length;
    }
    return sum;
  }, 0);
});

// 导航到客户列表页面
const navigateToClientList = () => {
  router.push('/clients')
}



// 计算下拉框位置
const calculateDropdownPosition = () => {
  const inputElement = document.querySelector('.client-selector .animated-input-container')
  if (inputElement) {
    const rect = inputElement.getBoundingClientRect()
    dropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999
    }
  }
}



// 处理客户搜索输入框 focus 事件
const handleClientSearchFocus = async () => {
  showClientSelector.value = true
  calculateDropdownPosition()
  
  // 如果没有搜索内容，加载默认客户列表
  if (!clientSearchQuery.value || clientSearchQuery.value.trim() === '') {
    await loadDefaultClientList()
  }
}

// 加载默认客户列表 - 完全模仿PS模块
const loadDefaultClientList = async () => {
  clientSearchLoading.value = true
  try {
    const response = await searchClients('')
    let clients = response.data || response || []

    // 按最近修改时间排序
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0)
      const dateB = new Date(b.updated_at || b.created_at || 0)
      return dateB - dateA // 倒序排列，最新的在前面
    })

    clientSearchResults.value = clients
  } catch (error) {
    console.error('加载客户列表失败:', error)
    clientSearchResults.value = []
  } finally {
    clientSearchLoading.value = false
  }
}

// 处理客户搜索 - 完全模仿PS模块
const handleClientSearch = async (query) => {
  if (!query || query.trim() === '') {
    if (showClientSelector.value) {
      await loadDefaultClientList()
    } else {
      clientSearchResults.value = []
    }
    return
  }

  clientSearchLoading.value = true
  try {
    const response = await searchClients(query.trim())
    let clients = response.data || response || []

    // 按最近修改时间排序搜索结果
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0)
      const dateB = new Date(b.updated_at || b.created_at || 0)
      return dateB - dateA // 倒序排列，最新的在前面
    })

    clientSearchResults.value = clients
    console.log('搜索结果:', filteredResults)
  } catch (error) {
    console.error('搜索客户失败:', error)
    clientSearchResults.value = []
    ElMessage.error('搜索客户失败')
  } finally {
    clientSearchLoading.value = false
  }
}

// 选择客户
const selectClient = async (client) => {
  try {
    selectedClient.value = client
    showClientSelector.value = false
    clientSearchQuery.value = ''
    clientSearchResults.value = []
    
    // 重置选择状态和版本信息
    Object.keys(formData).forEach(key => {
      if(key.startsWith('selected')) {
        formData[key] = []
      }
    })
    formData.versionName = ''

    // 获取客户详细模块数据
    await getClientModuleData(client.id_hashed)
  } catch (error) {
    console.error('选择客户时发生错误:', error)
    ElMessage.error('获取客户详细信息失败')
    // 重置选择状态
    selectedClient.value = null
    clientModuleData.value = null
  }
}

// 点击外部关闭下拉框 - 完全模仿PS模块
const handleClickOutside = (event) => {
  // 客户档案下拉框
  const dropdown = clientDropdown.value
  const clientSelector = document.querySelector('.client-selector')

  if (dropdown && clientSelector &&
      !dropdown.contains(event.target) &&
      !clientSelector.contains(event.target)) {
    showClientSelector.value = false
  }
}

// 监听窗口滚动和调整大小，更新下拉框位置
const handleWindowEvent = () => {
  if (showClientSelector.value) {
    calculateDropdownPosition()
  }
}

// 页面切换时自动取消生成
onBeforeRouteLeave((to, from, next) => {
  if (isGenerating.value) {
    handleCancelGeneration()
          // 页面切换时静默取消生成，不显示提示
  }
  next()
})

// 组件卸载时自动取消生成
onBeforeUnmount(() => {
  if (isGenerating.value) {
    handleCancelGeneration()
  }
})

// 生命周期
onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('scroll', handleWindowEvent, true)
  window.addEventListener('resize', handleWindowEvent)
  
  // 检查是否为编辑模式
  await handleEditMode()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('scroll', handleWindowEvent, true)
  window.removeEventListener('resize', handleWindowEvent)
})

// 处理编辑模式
const handleEditMode = async () => {
  const clientId = route.query.client
  const editId = route.query.edit

  // 如果有客户ID，自动选择客户
  if (clientId) {
    try {
      // 首先获取客户信息
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/ai-writing/cv/clients/${clientId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const client = await response.json()
        await selectClient(client)
      }
    } catch (error) {
      console.error('获取客户信息失败:', error)
    }
  }

  // 如果有编辑ID，加载CV内容
  if (editId) {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/ai-writing/cv/documents/${editId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const document = await response.json()
        // 优先使用content_markdown字段，如果没有则使用content字段
        const content = document.content_markdown || document.content || ''
        cvContent.value = content

        // 如果有版本名称，可以设置到表单中
        if (document.version_name) {
          formData.versionName = document.version_name
        }

                  // ElMessage.success('CV内容已加载')
      } else {
        ElMessage.error('加载CV内容失败')
      }
    } catch (error) {
      console.error('加载CV内容失败:', error)
      ElMessage.error('加载CV内容失败')
    }
  }
}

// 清除选中的客户
const clearSelectedClient = () => {
  selectedClient.value = null
  clientModuleData.value = null
  cvContent.value = ''

  // 重置选择状态和版本信息
  Object.keys(formData).forEach(key => {
    if(key.startsWith('selected')) {
      formData[key] = []
    }
  })
  formData.versionName = ''
}





// 获取学校Logo URL（优先使用数据库中的school_logo_url）
const getSchoolLogoUrl = (programDetails) => {
  if (!programDetails) return getDefaultSchoolLogo()

  // 优先使用数据库中的school_logo_url
  if (programDetails.school_logo_url) {
    return programDetails.school_logo_url
  }

  // 如果没有数据库logo，回退到硬编码映射表
  if (programDetails.school_name_cn) {
    return getSchoolLogo(programDetails.school_name_cn)
  }

  return getDefaultSchoolLogo()
}

// 获取默认学校Logo
const getDefaultSchoolLogo = () => {
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzRGNDZFNSIvPgo8cGF0aCBkPSJNMTIgMTZIMjhWMThIMTJWMTZaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMjBIMjhWMjJIMTJWMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMjRIMjRWMjZIMTJWMjRaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTAgMTBIMzBWMTRIMTBWMTBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTggMjhIMjJWMzJIMThWMjhaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'
}

// 处理图片加载错误
const handleImageError = (event) => {
  // 如果图片加载失败，使用默认logo
  event.target.src = getDefaultSchoolLogo()
}

// 获取客户模块数据
const getClientModuleData = async (clientId) => {
  try {
    const token = localStorage.getItem('token')
    const response = await fetch(`/api/ai-writing/cv/clients/${clientId}/modules`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    if (!response.ok) {
      throw new Error('获取客户模块数据失败')
    }
    const data = await response.json()
    console.log('获取到客户模块数据:', data)
    clientModuleData.value = data

    // 默认全选所有经历
    autoSelectAllExperiences(data)
  } catch (error) {
    console.error('获取客户模块数据失败:', error)
    throw error
  }
}

// 自动选择所有经历
const autoSelectAllExperiences = (moduleData) => {
  console.log('开始自动选择所有经历...')

  // 确保所有选择数组字段都已初始化
  if (!Array.isArray(formData.selectedEducationIds)) formData.selectedEducationIds = []
  if (!Array.isArray(formData.selectedAcademicIds)) formData.selectedAcademicIds = []
  if (!Array.isArray(formData.selectedWorkIds)) formData.selectedWorkIds = []
  if (!Array.isArray(formData.selectedActivityIds)) formData.selectedActivityIds = []
  if (!Array.isArray(formData.selectedAwardIds)) formData.selectedAwardIds = []
  if (!Array.isArray(formData.selectedSkillIds)) formData.selectedSkillIds = []
  if (!Array.isArray(formData.selectedLanguageScoreIds)) formData.selectedLanguageScoreIds = []

  // 教育经历
  if (moduleData.education && Array.isArray(moduleData.education)) {
    formData.selectedEducationIds = moduleData.education.map(item => item.id)
    console.log('自动选择教育经历:', formData.selectedEducationIds.length, '项')
  }
  
  // 学术经历
  if (moduleData.academic && Array.isArray(moduleData.academic)) {
    formData.selectedAcademicIds = moduleData.academic.map(item => item.id)
    console.log('自动选择学术经历:', formData.selectedAcademicIds.length, '项')
  }
  
  // 工作经历
  if (moduleData.work && Array.isArray(moduleData.work)) {
    formData.selectedWorkIds = moduleData.work.map(item => item.id)
    console.log('自动选择工作经历:', formData.selectedWorkIds.length, '项')
  }
  
  // 课外活动
  if (moduleData.activities && Array.isArray(moduleData.activities)) {
    formData.selectedActivityIds = moduleData.activities.map(item => item.id)
    console.log('自动选择课外活动:', formData.selectedActivityIds.length, '项')
  }
  
  // 荣誉奖项
  if (moduleData.awards && Array.isArray(moduleData.awards)) {
    formData.selectedAwardIds = moduleData.awards.map(item => item.id)
    console.log('自动选择荣誉奖项:', formData.selectedAwardIds.length, '项')
  }
  
  // 技能特长
  if (moduleData.skills && Array.isArray(moduleData.skills)) {
    formData.selectedSkillIds = moduleData.skills.map(item => item.id)
    console.log('自动选择技能特长:', formData.selectedSkillIds.length, '项')
  }
  
  // 语言成绩
  if (moduleData.language_scores && Array.isArray(moduleData.language_scores)) {
    formData.selectedLanguageScoreIds = moduleData.language_scores.map(item => item.id)
    console.log('自动选择语言成绩:', formData.selectedLanguageScoreIds.length, '项')
  }
  
  console.log('自动选择完成，总计选择:', totalSelectedCount.value, '项经历')
}

// 通用选择切换函数
const toggleSelection = (key, id) => {
  const selectedArray = formData[key];
  const index = selectedArray.indexOf(id);
  if (index > -1) {
    selectedArray.splice(index, 1);
  } else {
    selectedArray.push(id);
  }
}

// 已使用通用的 toggleSelection 函数替代各种特定的切换函数

// 生成CV (流式输出)
const handleGenerateCV = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请先选择客户档案')
    return
  }

  // 检查是否至少选择了一项经历
  if (totalSelectedCount.value === 0) {
    ElMessage.warning('请至少选择一项经历')
    return
  }

  isGenerating.value = true
  
  // 创建取消控制器
  abortController.value = new AbortController()
  
  try {
    // 将 Proxy 响应式数组转换为普通数组，避免 JSON 序列化丢失数据
    const requestBody = {
      client_id: selectedClient.value.id_hashed,
      language: formData.language,
      additional_info: formData.additionalInfo,
      selected_education_ids: [...formData.selectedEducationIds],
      selected_academic_ids: [...formData.selectedAcademicIds],
      selected_work_ids: [...formData.selectedWorkIds],
      selected_activity_ids: [...formData.selectedActivityIds],
      selected_award_ids: [...formData.selectedAwardIds],
      selected_skill_ids: [...formData.selectedSkillIds],
      selected_language_score_ids: [...formData.selectedLanguageScoreIds]
    }

    // 调试输出，确认序列化内容
    console.group('=== CV 生成请求详细信息 ===')
    console.log('客户ID:', requestBody.client_id)
    console.log('语言:', requestBody.language)
    console.log('额外信息:', requestBody.additional_info)
    console.log('选择的教育经历IDs:', requestBody.selected_education_ids)
    console.log('选择的学术经历IDs:', requestBody.selected_academic_ids)
    console.log('选择的工作经历IDs:', requestBody.selected_work_ids)
    console.log('选择的课外活动IDs:', requestBody.selected_activity_ids)
    console.log('选择的奖项IDs:', requestBody.selected_award_ids)
    console.log('选择的技能IDs:', requestBody.selected_skill_ids)
    console.log('选择的语言成绩IDs:', requestBody.selected_language_score_ids)
    console.log('完整请求体对象:', requestBody)
    console.log('JSON 序列化结果:', JSON.stringify(requestBody))
    console.groupEnd()

    const token = localStorage.getItem('token')
    const response = await fetch('/api/ai-writing/cv/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestBody),
      signal: abortController.value.signal
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('CV生成失败:', errorData)
              ElMessage.error('生成失败')
      return
    }

    // 检查是否支持流式处理
    if (!response.body) {
      ElMessage.error('浏览器不支持流式响应')
      return
    }

    // 处理流式响应
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let fullMarkdownContent = ''
    let lastCompleteLineIndex = 0
    
    // 初始化编辑器内容
    cvContent.value = ''
    
    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          break
        }

        // 解码新的数据块
        const chunk = decoder.decode(value, { stream: true })
        fullMarkdownContent += chunk

        // 修复：每次都设置完整内容，而不是追加（与PS/RL模块保持一致）
        cvContent.value = fullMarkdownContent
        
        // 流式处理的延迟，使效果更自然
        await new Promise(resolve => setTimeout(resolve, 50))
      }
      
      // 处理最后的内容（包括可能的不完整行）
      if (fullMarkdownContent.trim()) {
        // 设置最终的 Markdown 内容，TiptapEditor 会自动转换为 HTML 显示
        cvContent.value = fullMarkdownContent
      }
      
      console.log('CV流式生成完成')
      console.log('最终Markdown内容长度:', fullMarkdownContent.length)
      console.log('最终内容长度:', cvContent.value.length)
      
      if (!fullMarkdownContent.trim()) {
        ElMessage.error('生成失败')
        return
      }
      
                  ElMessage.success('CV生成完成')
      
    } catch (streamError) {
      console.error('流式处理错误:', streamError)
              ElMessage.error('生成失败')
    } finally {
      reader.releaseLock()
    }
    
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('CV生成已被用户取消')
      ElMessage.warning('生成已取消')
    } else {
      console.error('CV生成请求失败:', error)
          ElMessage.error('生成失败')
    }
  } finally {
    isGenerating.value = false
    abortController.value = null
  }
}

// 取消生成
const handleCancelGeneration = () => {
  if (abortController.value) {
    abortController.value.abort()
  }
}

// 保存处理
const handleSave = async (data) => {
  console.log('Saving CV:', data)

  if (!selectedClient.value) {
    ElMessage.warning('请先选择客户档案')
    return
  }

  // 优先使用传递的Markdown内容，如果没有则使用cvContent.value
  const contentToSave = data.markdownContent || cvContent.value
  if (!contentToSave || !contentToSave.trim()) {
    ElMessage.warning('CV内容不能为空')
    return
  }

  // 检查版本名称
  let versionName = formData.versionName.trim()
  if (!versionName) {
    ElMessage.warning('请输入CV版本名称')
    return
  }

  try {
    const requestBody = {
      client_id: selectedClient.value.id_hashed,
      content_markdown: contentToSave, // 使用Markdown内容
      version_name: versionName
    }

    console.log('保存CV请求:', requestBody)

    const token = localStorage.getItem('token')
    const response = await fetch('/api/ai-writing/cv/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json()
      console.error('CV保存失败:', errorData)
              ElMessage.error('保存失败')
      return
    }

    const result = await response.json()
    console.log('CV保存成功:', result)
    
              ElMessage.success('保存成功')

    // 保存成功后，清空版本名称以便下次保存时输入新的版本名
    formData.versionName = ''

  } catch (error) {
    console.error('CV保存请求失败:', error)
          ElMessage.error('保存失败')
  }
}

// 导出处理
const handleExport = async (data) => {
  const { content, textContent, format } = data;
  const clientName = selectedClient.value?.name || 'CV';
  const dateStr = new Date().toLocaleDateString('sv');
  const fileName = `${clientName}_CV_${dateStr}`;

  if (!content) {
    ElMessage.warning('没有可导出的内容');
    return;
  }

  try {
    if (format === 'pdf') {
      // 修复：创建临时DOM元素来渲染HTML内容
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      tempDiv.style.cssText = `
        width: 210mm;
        min-height: 297mm;
        padding: 20mm;
        margin: 0 auto;
        background: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        box-sizing: border-box;
      `;

      // 添加样式到临时元素
      const style = document.createElement('style');
      style.textContent = `
        h1 { font-size: 24px; font-weight: bold; margin: 0 0 16px 0; color: #1a1a1a; }
        h2 { font-size: 20px; font-weight: bold; margin: 16px 0 12px 0; color: #1a1a1a; }
        h3 { font-size: 18px; font-weight: bold; margin: 12px 0 8px 0; color: #1a1a1a; }
        p { margin: 8px 0; }
        ul, ol { margin: 8px 0; padding-left: 20px; }
        li { margin: 4px 0; }
        strong { font-weight: bold; }
        em { font-style: italic; }
        hr { border: none; border-top: 1px solid #ddd; margin: 16px 0; }
        blockquote { margin: 16px 0; padding-left: 16px; border-left: 4px solid #ddd; font-style: italic; }
      `;

      tempDiv.appendChild(style);
      document.body.appendChild(tempDiv);

      const opt = {
        margin: [10, 10, 10, 10],
        filename: `${fileName}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          letterRendering: true,
          width: tempDiv.offsetWidth,
          height: tempDiv.offsetHeight
        },
        jsPDF: {
          unit: 'mm',
          format: 'a4',
          orientation: 'portrait'
        },
        pagebreak: { mode: ['css', 'legacy'] }
      };

      try {
        await html2pdf().set(opt).from(tempDiv).save();
        // ElMessage.success('PDF导出完成');
      } finally {
        // 清理临时元素
        document.body.removeChild(tempDiv);
      }

    } else if (format === 'txt') {
      const finalFileName = `${fileName}.txt`;
      const finalTextContent = textContent || content.replace(/<[^>]*>/g, '').replace(/\\n\\s*\\n/g, '\\n\\n').trim();
      const blob = new Blob([finalTextContent], { type: 'text/plain;charset=utf-t' });
      saveAs(blob, finalFileName);
        // ElMessage.success('TXT导出完成');
      
    } else if (format === 'docx') {
      // 使用新的、更强大的HTML到DOCX转换器
      const doc = createDocxFromHtml(content);
      
      const buffer = await Packer.toBlob(doc);
      saveAs(buffer, `${fileName}.docx`);
        // ElMessage.success('DOCX导出完成');
    }
  } catch (error) {
    console.error('导出失败:', error);
      ElMessage.error('导出失败');
  }
}

// 监听身份变化，自动刷新数据
watch(
  () => authStore.currentIdentity,
  async (newIdentity, oldIdentity) => {
    // 如果有新的身份信息，就刷新数据
    if (newIdentity) {
      // 检查是否是真正的身份变化
      const isIdentityChange = oldIdentity && newIdentity && 
        (newIdentity.identity_type !== oldIdentity.identity_type || 
         newIdentity.organization_id !== oldIdentity.organization_id);
      
      if (isIdentityChange) {
        console.log('CV模块检测到身份切换，自动刷新数据:', {
          old: oldIdentity,
          new: newIdentity
        });
        
        // 身份切换时清除相关数据
        try {
          clearAllData();
        } catch (error) {
          console.warn('CV模块身份切换后数据清除失败:', error);
        }
      }
    }
  },
  { deep: true } // 深度监听对象变化
);

// 清除所有数据的方法
const clearAllData = () => {
  // 清除选择的客户
  selectedClient.value = null;
  showClientSelector.value = false;
  clientSearchQuery.value = '';
  clientSearchResults.value = [];
  clientModuleData.value = null;
  
  // 清除表单数据
  formData.versionName = '';
  formData.language = 'english';
  formData.additionalInfo = '';
  formData.selectedEducationIds = [];
  formData.selectedAcademicIds = [];
  formData.selectedWorkIds = [];
  formData.selectedActivityIds = [];
  formData.selectedAwardIds = [];
  formData.selectedSkillIds = [];
  formData.selectedLanguageScoreIds = [];
  
  // 清除CV内容
  cvContent.value = '';
  isGenerating.value = false;
  
  console.log('CV模块数据已清除');
};

// 自动刷新数据的方法
const refreshAllData = async (silent = true) => {
  try {
    clearAllData();
    
    if (!silent) {
      console.log('CV模块数据刷新完成');
    }
  } catch (error) {
    if (!silent) {
      console.error('CV模块数据刷新失败:', error);
    }
  }
};
</script> 

<style scoped>
/* 表单标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 表单输入框样式 - 统一边框颜色与AnimatedInput一致 */
.form-input :deep(.el-input__wrapper) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-input :deep(.el-input__wrapper:focus-within) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

.form-textarea :deep(.el-textarea__inner) {
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

/* 客户选择器样式 */
.client-selector {
  position: relative;
}

.client-dropdown {
  min-width: 250px;
  max-width: 400px;
  /* 预留滚动条空间，避免内容增多时宽度抖动 */
  scrollbar-gutter: stable;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

/* Webkit浏览器滚动条样式 */
.client-dropdown::-webkit-scrollbar {
  width: 6px;
}

.client-dropdown::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.client-dropdown::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.client-dropdown::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-checkbox) {
  --el-color-primary: #4F46E5 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}

/* 经历选择相关样式 */
.experience-section {
  @apply mb-4 last:mb-0;
}

.section-title {
  @apply text-xs font-semibold text-gray-600 mb-2 px-1;
}

.section-items {
  @apply space-y-1;
}

.experience-item {
  @apply flex items-start p-2 hover:bg-white rounded-md cursor-pointer transition-colors duration-200 border border-transparent hover:border-gray-200;
}

.item-content {
  @apply ml-2 text-xs text-gray-700 select-none leading-relaxed flex-1;
}

/* 生成按钮动画效果 */
.typing-text {
  @apply font-medium;
}

.dots {
  @apply ml-1 flex;
}

.dot {
  @apply inline-block w-1 h-1 bg-white rounded-full mx-0.5;
  animation: typing 1.4s infinite;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.progress-bar {
  animation: progress 2s ease-in-out infinite;
  width: 0%;
}

@keyframes progress {
  0% {
    width: 0%;
    opacity: 0.6;
  }
  50% {
    width: 70%;
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 0.3;
  }
}

/* 流式生成状态指示器 */
.streaming-indicator {
  @apply inline-flex items-center px-2 py-1 bg-blue-50 text-blue-600 rounded-full text-xs font-medium;
}

.streaming-indicator::before {
  content: '';
  @apply w-2 h-2 bg-blue-400 rounded-full mr-2;
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

:deep(.el-popper) {
  --el-dropdown-menuItem-hover-color: #6366f1 !important; /* 紫色 */
}

/* 左侧表单区域滚动条样式 */
.flex-1.overflow-y-auto {
  /* 预留滚动条空间，避免内容增多时宽度抖动 */
  scrollbar-gutter: stable;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

/* Webkit浏览器滚动条样式 */
.flex-1.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.flex-1.overflow-y-auto::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 4px;
}

.flex-1.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.flex-1.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 院校专业选择器样式 */
.program-selector {
  position: relative;
}

.program-dropdown {
  min-width: 250px;
  max-width: 400px;
}

.program-selector.disabled {
  opacity: 0.6;
  pointer-events: none;
}
</style> 