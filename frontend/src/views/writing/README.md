# 写作模块功能说明

## 概述
写作模块包含三个主要功能：CV写作、PS写作和推荐信写作。每个模块都采用左侧表单+右侧编辑器的布局设计，提供专业的文书写作体验。

## 🎨 UI设计规范统一

按照系统UI设计标准，所有写作模块已统一采用以下设计规范：

### 颜色系统
- **主色调**: #4F46E5 (紫色) - 用于按钮、焦点状态、强调元素
- **文字主色**: #1F2937 (深灰) - 标题和重要文本
- **文字次要**: #374151 (中灰) - 正文内容
- **边框色**: #E5E7EB (浅灰) - 卡片边框和分隔线

### 按钮系统
- **主要按钮**: 紫色背景，白色文字，圆角8px，悬浮缩放效果
- **次要按钮**: 透明背景，灰色边框，悬浮时变为紫色主题
- **动画效果**: 0.2s过渡动画，悬浮scale(1.02)，点击scale(0.98)

### 卡片系统
- **pro-card结构**: 统一的卡片容器
- **pro-card-header**: 固定高度56px，标题区域
- **pro-card-body**: 内容区域，支持滚动
- **阴影**: 0 1px 3px rgba(0, 0, 0, 0.1)

### 表单系统
- **form-label**: 统一的标签样式，14px字号，中等字重
- **form-input**: 统一的输入框样式，6px圆角
- **焦点状态**: 紫色边框+外发光效果
- **间距**: 标准化的组件间距

### 布局设计
- **左右分栏**: 左侧320px固定宽度表单，右侧自适应编辑器
- **占满屏幕**: 使用h-screen和flex布局占满整个屏幕
- **响应式**: 卡片内容支持垂直滚动

## 主要特性

### Tiptap富文本编辑器 (TiptapEditor.vue)
- **现代化编辑器**：基于Tiptap和ProseMirror的富文本编辑器
- **Markdown自动渲染**：自动将Markdown转换为富文本显示
- **完整键盘支持**：支持所有标准快捷键 (Ctrl+C, Ctrl+V, Ctrl+A, Ctrl+X)
- **可视化工具栏**：提供粗体、斜体、标题、列表、引用等格式化工具
- **字数统计**：实时显示文档字数
- **导出功能**：支持导出为PDF、DOCX、TXT格式
- **高性能**：基于ProseMirror引擎，专为Vue 3设计
- **统一样式**：工具栏和编辑区域使用统一主题色

### CV写作 (CV.vue)
**左侧表单包含：**
- 申请国家选择
- 申请学位（本科/硕士/博士）
- 申请专业
- 语言成绩（托福、雅思、GRE、GMAT）
- 学术背景（当前学校、专业、GPA）
- 个人经历要点
- 技能特长

**功能：**
- 一键生成CV初稿
- 基于填写信息智能生成结构化简历
- 支持英文格式的简历模板
- 使用统一的主题色彩突出重点内容

### PS写作 (PS.vue)
**左侧表单包含：**
- 申请国家和学位
- 文书类型（PS/SOP选择）
- 意向水平
- 语言版本（英文/中文）
- 个人背景信息
- 学术兴趣与目标
- 相关经历
- 选择学校/专业的原因
- 未来规划

**功能：**
- 智能生成PS初稿
- 支持中英文两种语言版本
- 根据填写内容定制化生成个人陈述
- 关键词使用主题色强调

### 推荐信写作 (RL.vue)
**左侧表单包含：**
- 申请信息（国家、学位、专业）
- 被推荐人信息
- 推荐人信息
- 关系类型和认识时间
- 学生表现描述
- 具体事例
- 推荐程度

**功能：**
- 生成专业推荐信模板
- 支持中英文版本
- 根据关系类型自动调整表述
- 包含完整的推荐信格式
- 使用统一的排版和颜色强调

## 使用方法

1. **选择写作类型**：从导航菜单进入对应的写作页面
2. **填写左侧表单**：根据提示填写相关信息
3. **生成初稿**：点击"生成初稿"按钮获取AI生成的文档模板
4. **编辑完善**：在右侧编辑器中修改和完善内容
5. **保存文档**：使用保存功能保存进度
6. **导出文档**：完成后可导出为多种格式

## 技术特性

- **Vue 3 Composition API**：现代化的组件设计
- **Element Plus UI**：专业的UI组件库，深度定制主题
- **TailwindCSS**：灵活的样式系统
- **Tiptap**：现代化的富文本编辑器，专为Vue 3设计
- **类型安全**：完整的TypeScript类型定义
- **响应式设计**：适配不同屏幕尺寸
- **统一UI规范**：符合系统整体设计风格

## 🎯 设计亮点

### 视觉统一性
- 所有组件使用统一的颜色系统和间距规范
- 主题色#4F46E5贯穿整个模块，提升品牌一致性
- 卡片式设计语言，层次分明

### 交互体验
- 流畅的动画过渡效果
- 直观的视觉反馈
- 无障碍友好的键盘导航支持

### 功能完整性
- 左右分栏充分利用屏幕空间
- 表单验证与错误提示
- 智能生成与手动编辑的完美结合

## 后续开发计划

- [ ] 连接后端API实现真实的保存和导出功能
- [ ] 集成AI生成服务
- [ ] 添加模板库功能
- [ ] 实现实时协作编辑
- [ ] 添加文档版本管理
- [ ] 支持更多导出格式
- [ ] 添加语法检查和拼写检查
- [ ] 完善无障碍功能
- [ ] 添加键盘快捷键支持

## 开发规范

### 组件结构
```vue
<template>
  <div class="h-screen bg-gray-50 flex">
    <!-- 左侧表单 -->
    <div class="w-80 flex-shrink-0">
      <div class="pro-card h-full">
        <div class="pro-card-header">
          <h2 class="pro-card-title">
            <span class="icon material-icons-outlined">icon_name</span>
            标题
          </h2>
        </div>
        <div class="pro-card-body overflow-y-auto">
          <!-- 表单内容 -->
        </div>
      </div>
    </div>
    
    <!-- 右侧编辑器 -->
    <div class="flex-1 p-6">
      <SharedTextEditor />
    </div>
  </div>
</template>
```

### 样式规范
- 使用统一的CSS类名：`pro-card`, `form-label`, `primary-btn`等
- 颜色变量：主色#4F46E5，文字色#374151
- 间距系统：遵循4px基准的间距规范
- 动画效果：transition-standard, hover-scale, button-click 