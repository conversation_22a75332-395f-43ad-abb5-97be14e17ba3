<template>
  <div class="school-assistant-container">
    <!-- 1. 顶部导航区: 仿照专业库页面的设计 -->
    <div class="page-header">
      <div class="container">
        <div class="header-content">
          <div class="title-section">
            <h1 class="page-title">{{ pageTitle }}</h1>
            <p class="page-subtitle">{{ pageSubtitle }}</p>
          </div>
          <!-- 统计信息栏 - 仅完整版显示 -->
          <div v-if="displayModeStore.isFullMode()" class="stats-section">
            <div class="stat-item">
              <div class="stat-number">1,000+</div>
              <div class="stat-label">精选院校</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">100%</div>
              <div class="stat-label">数据覆盖率</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">7</div>
              <div class="stat-label">热门地区</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 2. 主要内容区域 -->
    <div class="main-content">
      <div class="container">
        <!-- 主要内容区域 -->
        <div class="flex flex-col xl:flex-row gap-6 relative">
          <!-- 左侧：信息输入表单 - 添加sticky定位 -->
          <div
            class="xl:w-96 xl:flex-shrink-0 transition-all duration-300 sticky-sidebar"
            :class="{
              'lg:w-full': !sidebarCollapsed || !isLargeScreen,
              'lg:w-80': sidebarCollapsed && isLargeScreen,
              'lg:flex-shrink-0': sidebarCollapsed && isLargeScreen
            }"
          >
            <div class="pro-card relative">
              <!-- 验证遮罩层 -->
              <ValidationOverlay
                v-if="validationState.showValidationOverlay"
                :reason="validationState.validationReason"
                :is-validating="validationState.isValidating"
                @retry="retryValidation"
              />

              <!-- 表单标题 -->
              <div class="pro-card-header flex justify-between items-center">
                <h3 class="pro-card-title">申请信息</h3>
                <!-- 在大屏幕上显示折叠按钮 -->
                <button
                  v-if="isLargeScreen && !isExtraLargeScreen"
                  @click="toggleSidebar"
                  class="lg:block xl:hidden text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <span class="material-icons-outlined text-sm">
                    {{ sidebarCollapsed ? 'keyboard_arrow_right' : 'keyboard_arrow_left' }}
                  </span>
                </button>
              </div>

              <!-- 客户档案 - 两种模式都显示 -->
              <div class="px-4 py-3 border-b border-gray-100">
                <div
                  class="flex items-center space-x-2 mb-2 cursor-pointer"
                  @click="toggleSection('client')"
                >
                  <span class="material-icons-outlined text-[#4F46E5] text-lg">person</span>
                  <h4 class="text-sm font-semibold text-gray-600">客户档案</h4>

                  <!-- 帮助提示图标 -->
                  <div class="relative group">
                    <span
                      class="material-icons-outlined text-gray-400 text-sm hover:text-gray-600 cursor-pointer transition-colors"
                      @click.stop
                    >
                      help_outline
                    </span>
                    <!-- 悬浮提示框 -->
                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999]">
                      填写客户名称即可创建导出定校书
                      <!-- 小箭头 -->
                      <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                    </div>
                  </div>

                  <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300"
                    :class="sectionStates.client ? 'rotate-180' : ''">
                    expand_more
                  </span>
                </div>

                <div
                  class="space-y-2 form-section-content client-section-content"
                  :style="{
                    maxHeight: sectionStates.client ? '1000px' : '0px',
                    opacity: sectionStates.client ? 1 : 0,
                    visibility: sectionStates.client ? 'visible' : 'hidden'
                  }"
                >
                  <!-- 已选择客户信息显示 -->
                  <div v-if="selectedClient" class="mb-3">
                    <div class="flex items-center p-3 bg-[#4F46E5]/5 rounded-lg border border-[#4F46E5]/20">
                      <div class="w-8 h-8 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-xs font-medium mr-3">
                        {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                      </div>
                      <div class="flex-1">
                        <div class="font-medium text-gray-800 text-sm">{{ selectedClient.name }}</div>
                        <div class="text-xs text-gray-500">
                          {{ selectedClient.school || '暂无学校信息' }}
                        </div>
                      </div>
                      <button
                        @click.stop="clearSelectedClient"
                        class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
                      >
                        <span class="material-icons-outlined text-sm">clear</span>
                      </button>
                    </div>
                  </div>

                  <!-- 客户选择区域 -->
                  <div v-else>
                    <!-- 搜索框 -->
                    <div class="relative client-selector" :class="{ 'has-error': clientValidationError }">
                      <AnimatedInput
                        v-model="clientSearchQuery"
                        label="填写客户名称"
                        placeholder="输入客户姓名或学生ID"
                        type="input"
                        :hasError="clientValidationError"
                        @input="handleClientSearch"
                        @focus="handleClientSearchFocus"
                        @blur="handleClientInputBlur"
                        @keydown.enter="handleClientInputEnter"
                      />
                      
                      <!-- 客户选择错误提示 -->
                      <div v-if="clientValidationError" class="client-error-message mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                        <div class="flex items-center text-red-600 text-xs">
                          <span class="material-icons-outlined text-red-500 text-sm mr-1">error</span>
                          {{ clientValidationMessage }}
                        </div>
                      </div>

                      <!-- 搜索结果下拉框 -->
                      <Teleport to="body">
                        <div
                          v-if="showClientSelector && (clientSearchResults.length > 0 || clientSearchLoading || clientSearchQuery.trim())"
                          ref="clientDropdown"
                          class="fixed z-[9999] bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto client-dropdown"
                          :style="dropdownStyle"
                        >
                        <!-- 快速创建选项 - 优先显示 -->
                        <div v-if="clientSearchQuery.trim()" class="p-1">
                          <div
                            @click="createQuickClient(clientSearchQuery.trim())"
                            class="flex items-center p-2 hover:bg-[#4F46E5]/5 rounded-md cursor-pointer transition-colors border border-dashed border-[#4F46E5]/30 mb-2"
                          >
                            <div class="w-6 h-6 rounded-full bg-[#4F46E5]/10 text-[#4F46E5] flex items-center justify-center text-xs font-medium mr-2">
                              <span class="material-icons-outlined text-sm">add</span>
                            </div>
                            <div class="flex-1">
                              <div class="font-medium text-[#4F46E5] text-xs">创建快速定校书</div>
                              <div class="text-xs text-gray-500">
                                为 "{{ clientSearchQuery.trim() }}" 创建临时客户档案
                              </div>
                            </div>
                          </div>

                          <!-- 分隔线 -->
                          <div v-if="clientSearchResults.length > 0" class="border-t border-gray-100 my-1"></div>
                        </div>

                        <!-- 搜索结果 -->
                        <div v-if="clientSearchResults.length > 0" class="p-1">
                          <div v-if="clientSearchQuery.trim()" class="text-xs text-gray-400 px-2 py-1 mb-1">
                            或选择现有客户：
                          </div>
                          <div
                            v-for="client in clientSearchResults"
                            :key="client.id_hashed"
                            @click="selectClient(client)"
                            class="flex items-center p-2 hover:bg-gray-50 rounded-md cursor-pointer transition-colors"
                          >
                            <div class="w-6 h-6 rounded-full bg-[#4F46E5]/10 text-[#4F46E5] flex items-center justify-center text-xs font-medium mr-2">
                              {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                            </div>
                            <div class="flex-1">
                              <div class="font-medium text-gray-800 text-xs">{{ client.name }}</div>
                              <div class="text-xs text-gray-500">
                                {{ client.school || '暂无学校信息' }}
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- 默认提示（首次点击且没有搜索内容时） -->
                        <div v-else-if="!clientSearchQuery && !clientSearchLoading && clientSearchResults.length === 0" class="p-3 text-center text-gray-500 text-xs">
                          输入客户姓名、联系方式或学生ID进行搜索
                        </div>

                        <!-- 加载状态 -->
                        <div v-else-if="clientSearchLoading" class="p-3 text-center text-gray-500 text-xs">
                          <span class="material-icons-outlined animate-spin mr-1 text-sm">refresh</span>
                          搜索中...
                        </div>
                        </div>
                      </Teleport>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 模式切换按钮 - 三种模式选择 -->
              <div class="px-4 py-3 border-b border-gray-100">
                <div class="flex items-center bg-gray-100 rounded-lg p-1 space-x-1">
                  <!-- 专业库选校按钮 -->
                  <button
                    @click="setMatchingMode('list')"
                    :class="[
                      'flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200',
                      matchingMode === 'list'
                        ? 'bg-[#4F46E5] text-white shadow-sm'
                        : 'bg-white text-gray-600 hover:text-gray-800'
                    ]"
                  >
                    专业库选校
                  </button>
                  
                  <!-- 案例选校按钮 -->
                  <button
                    @click="setMatchingMode('ai')"
                    :class="[
                      'flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200',
                      matchingMode === 'ai'
                        ? 'bg-[#4F46E5] text-white shadow-sm'
                        : 'bg-white text-gray-600 hover:text-gray-800'
                    ]"
                  >
                    案例选校
                  </button>
                  
                  <!-- 自选校按钮 -->
                  <button
                    @click="setMatchingMode('filter')"
                    :class="[
                      'flex-1 px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200',
                      matchingMode === 'filter'
                        ? 'bg-[#4F46E5] text-white shadow-sm'
                        : 'bg-white text-gray-600 hover:text-gray-800'
                    ]"
                  >
                    自选校
                  </button>
                </div>
              </div>

              <!-- 专业库选校和案例选校模式的表单内容 -->
              <div v-if="matchingMode === 'ai' || matchingMode === 'list'" class="ai-mode-container">
                <form
                  @submit.prevent="handleSubmit"
                  class="divide-y divide-gray-100"
                  :class="{ 'collapsed-form': sidebarCollapsed && isLargeScreen && !isExtraLargeScreen }"
                >
                <!-- 学术背景 -->
                <div class="px-4 py-3">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="material-icons-outlined text-[#4F46E5] text-lg">school</span>
                    <h4 class="text-sm font-semibold text-gray-600">学术背景</h4>
                  </div>
                  
                  <div class="space-y-2">
                    <!-- 使用动画输入组件的表单项 -->
                    <AnimatedInput
                      v-model="profileForm.academic.school"
                      label="本科学校"
                      placeholder="请输入本科学校"
                      type="autocomplete"
                      required
                      :hasError="formValidation.hasValidated && formValidation.errors.academic.school"
                      :fetchSuggestions="querySchools"
                      @input="clearFieldError('academic', 'school'); checkSectionCompletion('academic')"
                      @change="clearFieldError('academic', 'school'); checkSectionCompletion('academic'); checkAndShowClientValidationOnConfirm()"
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.academic.major"
                      label="本科专业"
                      placeholder="请输入本科专业"
                      type="input"
                      required
                      :hasError="formValidation.hasValidated && formValidation.errors.academic.major"
                      @input="clearFieldError('academic', 'major'); checkSectionCompletion('academic')"
                      @change="clearFieldError('academic', 'major'); checkSectionCompletion('academic'); checkAndShowClientValidationOnConfirm()"
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.academic.gpa"
                      label="成绩（百分制）"
                      placeholder="请输入成绩（0-100）"
                      type="input"
                      required
                      :hasError="formValidation.hasValidated && (formValidation.errors.academic.gpa || gpaValidationError)"
                      @input="handleGpaInput"
                      @change="(value) => { handleGpaChange(value); checkAndShowClientValidationOnConfirm(); }"
                    />
                  
                    <!-- 班级排名选项暂时注释，业务代码中未使用
                    <AnimatedInput
                      v-model="profileForm.academic.ranking"
                      label="班级排名"
                      placeholder="请选择班级排名"
                      type="select"
                      :options="[
                        { label: '前5%', value: '5%' },
                        { label: '前10%', value: '10%' },
                        { label: '前20%', value: '20%' },
                        { label: '前50%', value: '50%' }
                      ]"
                      @change="checkSectionCompletion('academic')"
                    />
                    -->
                  </div>
                </div>

                <!-- 留学意向 -->
                <div class="px-4 py-3">
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="material-icons-outlined text-[#4F46E5] text-lg">flight_takeoff</span>
                    <h4 class="text-sm font-semibold text-gray-600">留学意向</h4>
                  </div>
                  
                  <div class="space-y-2">
                    <AnimatedInput
                      v-model="profileForm.intention.countries"
                      label="意向国家/地区"
                      :placeholder="countrySelectionConfig.placeholder"
                      type="select"
                      :options="countryOptionsWithDisabled"
                      :multiple="countrySelectionConfig.multiple"
                      required
                      :hasError="formValidation.hasValidated && formValidation.errors.intention.countries"
                      @change="handleCountryChange"
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.intention.majors"
                      label="意向专业/领域"
                      placeholder="请选择意向专业领域（最多选择3个）"
                      type="select"
                      :hasError="formValidation.hasValidated && formValidation.errors.intention.majors"
                      :optionGroups="majorOptionsWithDisabled"
                      multiple
                      required
                      @change="handleMajorChange"
                    />
                    <!-- End of Selection -->

                  </div>
                </div>

                <!-- 软实力与偏好 - 仅在案例选校模式下显示 -->
                <div v-if="matchingMode === 'ai'" class="px-4 py-3">
                  <div 
                    class="flex items-center space-x-2 mb-2 cursor-pointer"
                    @click="toggleSection('strength')"
                  >
                    <span class="material-icons-outlined text-[#4F46E5] text-lg">stars</span>
                    <h4 class="text-sm font-semibold text-gray-600">软实力与偏好</h4>
                    <span class="text-xs text-gray-500">(可选)</span>
                    <span class="material-icons-outlined text-gray-400 text-sm transition-transform duration-300" 
                      :class="sectionStates.strength ? 'rotate-180' : ''">
                      expand_more
                    </span>
                  </div>
                  
                  <div 
                    class="space-y-2 overflow-hidden form-section-content" 
                    :style="{ 
                      maxHeight: sectionStates.strength ? '1000px' : '0px', 
                      opacity: sectionStates.strength ? 1 : 0,
                      visibility: sectionStates.strength ? 'visible' : 'hidden'
                    }"
                  >
                    <AnimatedInput
                      v-model="profileForm.strength.competition"
                      label="竞赛经历"
                      placeholder="请选择获得的竞赛奖项级别"
                      type="select"
                      :options="[
                        { label: '省级', value: 'provincial' },
                        { label: '国家级', value: 'national' },
                        { label: '国际级', value: 'international' }
                      ]"
                      multiple
                      @change="checkAndShowClientValidationOnConfirm()"
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.strength.internship"
                      label="实习经历"
                      placeholder="请选择实习经历类型"
                      type="select"
                      :options="[
                        { label: '互联网大厂', value: 'internet' },
                        { label: '头部金融机构', value: 'finance' },
                        { label: '4A', value: '4a' },
                        { label: '四大会计事务所', value: 'big4' },
                        { label: '世界500强', value: 'fortune500' },
                        { label: '上市公司', value: 'listed' },
                        { label: '其他', value: 'other' }
                      ]"
                      multiple
                      @change="checkAndShowClientValidationOnConfirm()"
                    />
                  
                    <AnimatedInput
                      v-model="profileForm.strength.research"
                      label="科研经历"
                      placeholder="请选择科研成果类型"
                      type="select"
                      :options="[
                        { label: '普通刊物发表论文', value: 'normal-paper' },
                        { label: '核心刊物发表论文', value: 'core-paper' },
                        { label: 'SCI刊物发表论文', value: 'sci-paper' },
                        { label: '实用新型专利', value: 'utility-patent' },
                        { label: '发明专利', value: 'invention-patent' }
                      ]"
                      multiple
                      @change="checkAndShowClientValidationOnConfirm()"
                    />
                  
                    <!-- 院校排名偏好选项暂时注释，让其默认为不选择
                    <AnimatedInput
                      v-model="profileForm.strength.rankingPreference"
                      label="院校排名偏好"
                      placeholder="请选择排名范围"
                      type="select"
                      :options="[
                        { label: 'QS Top 10', value: 'QS-10' },
                        { label: 'QS Top 50', value: 'QS-50' },
                        { label: 'QS Top 100', value: 'QS-100' }
                      ]"
                      multiple
                    />
                    -->
                  
                    <AnimatedInput
                      v-model="profileForm.strength.preferenceType"
                      label="申请偏好类型"
                      placeholder="请选择申请偏好类型（可选）"
                      type="select"
                      :options="[
                        { label: '排名导向型（更看重学校排名）', value: 'ranking_focused' },
                        { label: '专业导向型（更看重专业匹配）', value: 'major_focused' }
                      ]"
                      @change="checkSectionCompletion('strength'); checkAndShowClientValidationOnConfirm()"
                    />

                    <AnimatedInput
                      v-model="profileForm.intention.duration"
                      label="期望项目时长"
                      placeholder="请选择期望的项目时长（可选）"
                      type="select"
                      :options="[
                        { label: '1年', value: '1' },
                        { label: '1.5年', value: '1.5' },
                        { label: '2年', value: '2' }
                      ]"
                      @change="checkSectionCompletion('strength'); checkAndShowClientValidationOnConfirm()"
                    />
                  </div>
                </div>

                <!-- 提交按钮 -->
                <div class="pro-card-footer bg-gray-50 !flex !justify-end !items-center">
                  <el-button 
                    type="primary" 
                    @click="handleSubmit"
                    :loading="isLoading"
                    class="!bg-[#4F46E5] !border-[#4F46E5] !h-7 !px-3 !text-xs"
                  >
                    <span class="material-icons-outlined mr-1 !text-xs">send</span>
                    开始匹配
                  </el-button>
                </div>
              </form>
              </div>

              <!-- 自选校模式的搜索与筛选界面 -->
              <div v-else-if="matchingMode === 'filter'" class="pro-card-body filter-mode-container">
                <!-- 搜索栏 -->
                <div class="search-section">
                  <div class="search-container">
                    <FloatingLabelInput
                      v-model="filterSearchForm.keyword"
                      label="智能搜索学校及专业"
                      placeholder="支持精确组合搜索（如曼大 金融）"
                      prefix-icon="search"
                      clearable
                      size="large"
                      class="search-input"
                      @input="handleFilterSearch"
                      @clear="handleFilterClearSearch"
                    />
                  </div>
                </div>

                <!-- 筛选栏 -->
                <div class="filter-section">
                  <!-- 地区筛选 -->
                  <div class="filter-group">
                    <div class="filter-group-header">
                      <span class="material-icons-outlined">location_on</span>
                      留学地区
                    </div>
                    <div class="filter-tags">
                      <el-tag
                        :type="filterSearchForm.regions.length === 0 ? 'primary' : undefined"
                        @click="handleFilterRegionClick('')"
                        :class="[
                          'filter-tag',
                          { 'tag-selected': filterSearchForm.regions.length === 0 }
                        ]"
                      >
                        不限
                      </el-tag>
                      <el-tag
                        v-for="region in filterData.regions"
                        :key="region.name"
                        :type="filterSearchForm.regions.includes(region.name) ? 'primary' : undefined"
                        @click="handleFilterRegionClick(region.name)"
                        :class="[
                          'filter-tag',
                          { 'tag-selected': filterSearchForm.regions.includes(region.name) }
                        ]"
                      >
                        {{ region.name }}
                      </el-tag>
                    </div>
                  </div>

                  <!-- 专业大类筛选 -->
                  <div class="filter-group">
                    <div class="filter-group-header">
                      <span class="material-icons-outlined">category</span>
                      专业大类
                    </div>
                    <div class="filter-tags">
                      <el-tag
                        v-for="category in filterData.categories"
                        :key="category.name"
                        :type="selectedFilterCategory === category.name ? 'primary' : undefined"
                        @click="handleFilterCategoryClick(category.name)"
                        :class="[
                          'filter-tag',
                          'category-tag',
                          { 'tag-selected': selectedFilterCategory === category.name }
                        ]"
                      >
                        <span class="category-name">{{ category.name }}</span>
                        <span class="category-arrow" v-if="selectedFilterCategory === category.name">
                          <span class="material-icons-outlined">keyboard_arrow_up</span>
                        </span>
                        <span class="category-arrow" v-else>
                          <span class="material-icons-outlined">keyboard_arrow_down</span>
                        </span>
                      </el-tag>
                    </div>
                  </div>

                  <!-- 专业方向标签 -->
                  <transition name="direction-fade" mode="out-in">
                    <div v-if="selectedFilterCategory" class="filter-group direction-group" :key="selectedFilterCategory">
                      <div class="filter-group-header">
                        <span class="material-icons-outlined">label</span>
                        {{ selectedFilterCategory }}专业方向
                      </div>
                      <transition-group name="direction-list" tag="div" class="filter-tags">
                        <el-tag
                          v-for="direction in filteredFilterDirections"
                          :key="direction.name"
                          :type="filterSearchForm.program_directions.includes(direction.name) ? 'primary' : undefined"
                          @click="handleFilterDirectionClick(direction.name)"
                          :class="[
                            'filter-tag',
                            { 'tag-selected': filterSearchForm.program_directions.includes(direction.name) }
                          ]"
                        >
                          {{ direction.name }}
                        </el-tag>
                      </transition-group>
                    </div>
                  </transition>

                  <!-- 已选择的专业方向显示 -->
                  <div v-if="filterSearchForm.program_directions.length > 0" class="filter-group">
                    <div class="filter-group-header">
                      <span class="material-icons-outlined">done_all</span>
                      已选择的专业方向 ({{ filterSearchForm.program_directions.length }})
                    </div>
                    <div class="selected-tags">
                      <div class="selected-tag-wrapper">
                        <span class="category-prefix">{{ selectedFilterCategory }}：</span>
                        <div class="selected-tags-container">
                          <el-tag
                            v-for="direction in filterSearchForm.program_directions"
                            :key="direction"
                            type="primary"
                            closable
                            @close="handleFilterDirectionRemove(direction)"
                            class="selected-tag"
                          >
                            {{ direction }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 重置按钮 -->
                  <div class="filter-actions">
                    <el-button @click="handleFilterResetFilters" size="small" class="btn-secondary action-btn !h-8 !text-xs !px-3">
                      <span class="material-icons-outlined !text-sm">refresh</span>
                      重置所有筛选
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧：匹配结果展示 -->
          <div class="flex-1 relative main-content-area">
            <!-- 专业库选校和案例选校模式 -->
            <div v-if="matchingMode === 'ai' || matchingMode === 'list'" class="pro-card">
              <div class="pro-card-header">
                <h2 class="pro-card-title">
                  {{ currentResultMode === 'hard_filter' ? '匹配院校' :
                     currentResultMode === 'ai' ? '匹配院校' :
                     enableAiSelection ? '匹配院校' : '匹配院校' }}
                </h2>
              </div>
              
              <!-- 匹配结果内容区域 -->
              <div class="pro-card-body">
                <!-- 推荐进行中 -->
                <div v-if="streamingStatus === 'connecting' || streamingStatus === 'streaming'" class="mb-6">
                  
                  <!-- 进度条显示 -->
                  <div class="max-w-lg mx-auto">
                    <!-- 连接状态指示器 -->
                    <div class="flex items-center justify-center mb-6 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg border border-purple-200">
                      <div class="flex items-center space-x-4">
                        <div class="relative">
                          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-[#4F46E5]"></div>
                          <div class="absolute inset-0 rounded-full border-2 border-[#4F46E5]/20"></div>
                        </div>
                        <div class="flex flex-col">
                          <span class="text-sm font-medium text-[#4F46E5]">
                            {{ streamingStatus === 'connecting' ? '正在连接匹配系统' : currentStage }}
                          </span>
                          <span class="text-xs text-gray-600 mt-1">
                            {{ streamingStatus === 'connecting' ? '建立服务连接中' : 
                               streamingStatus === 'streaming' ? '正在分析申请条件' : '数据处理中' }}
                          </span>
                        </div>
                      </div>
                    </div>

                    <!-- 进度条 -->
                    <div class="w-full bg-gray-200 rounded-full h-3 mb-4 shadow-inner">
                      <div class="bg-gradient-to-r from-[#4F46E5] via-[#7C3AED] to-purple-600 h-3 rounded-full transition-all duration-700 ease-out relative overflow-hidden"
                           :style="{ width: `${isNaN(recommendationProgress) ? 0 : Math.max(0, Math.min(100, recommendationProgress))}%` }">
                        <!-- 流光效果 -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 -skew-x-12 animate-pulse"></div>
                      </div>
                    </div>
                    
                    <!-- 进度文字和百分比 -->
                    <div class="flex items-center justify-between mb-4">
                      <div class="text-sm text-gray-700 font-medium">
                        {{ progressMessage }}
                      </div>
                      <div class="text-sm font-bold text-[#4F46E5]">
                        {{ isNaN(recommendationProgress) ? 0 : Math.round(recommendationProgress) }}%
                      </div>
                    </div>

                  </div>
                </div>

                <!-- 空状态展示 -->
                <div v-else-if="!hasSubmitted" class="py-6">
                  <!-- 顶部说明区域 -->
                  <div class="flex flex-col items-center justify-center py-0">
                    <div class="relative">
                      <!-- 背景装饰 -->
                      <div class="absolute inset-0 bg-gradient-to-br from-[#4F46E5]/10 to-[#7C3AED]/10 rounded-full blur-3xl animate-pulse"></div>
                      
                      <!-- 图标容器 -->
                      <div class="relative bg-transparent p-8 rounded-2xl">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-[#4F46E5] mx-auto animate-float" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                      </div>
                    </div>
                    
                    <div class="text-center mb-6">
                                          <h3 class="text-base font-semibold text-gray-800 mb-2">
                      {{ matchingMode === 'ai' ? '案例驱动，专业匹配' : 
                         matchingMode === 'list' ? '专业库检索，精准匹配' : '个性化匹配服务' }}
                    </h3>
                    <p class="text-sm text-gray-500">
                      {{ matchingMode === 'ai' 
                        ? '基于历史录取数据和成功案例，结合您的学术背景与留学规划，提供专业的院校推荐方案' 
                        : matchingMode === 'list'
                        ? '依托完整的专业数据库，根据您的留学意向进行精确检索，快速定位符合条件的院校项目'
                        : '根据您的个人条件和留学目标，提供个性化的院校匹配服务'
                      }}
                    </p>
                    </div>


                  </div>
                </div>

                <!-- 匹配结果列表 -->
                <div v-else class="space-y-4">
                  <p v-if="filteredRecommendations.length === 0" class="text-gray-500 text-center py-8">
                    暂无匹配结果
                  </p>



                  <!-- 专业库匹配模式：按学校分组显示 -->
                  <div v-if="currentResultMode === 'hard_filter' && schoolGroups.length > 0" class="space-y-4">


                    <!-- 国家/地区筛选按钮 -->
                    <div v-if="availableRegions.length > 1" class="region-filter-container mb-4">
                      <div class="flex flex-wrap gap-2">
                        <!-- 全部按钮 -->
                        <button
                          @click="handleRegionChange('all')"
                          :aria-pressed="selectedRegion === 'all'"
                          :aria-label="`显示所有地区的${recommendations.length}个匹配专业`"
                          :class="[
                            'px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-300 flex items-center space-x-1',
                            selectedRegion === 'all'
                              ? 'bg-[#4F46E5] text-white shadow-sm hover:bg-[#4338CA]'
                              : 'bg-white text-gray-600 border border-gray-200 hover:border-[#4F46E5] hover:text-[#4F46E5] hover:bg-[#4F46E5]/5'
                          ]"
                        >
                          <span class="material-icons-outlined text-xs">public</span>
                          <span>全部</span>
                          <span 
                            :class="[
                              'text-xs px-1.5 py-0.5 rounded-full',
                              selectedRegion === 'all' 
                                ? 'bg-white/20' 
                                : 'bg-gray-100 text-gray-600'
                            ]"
                          >
                            {{ recommendations.length }}
                          </span>
                        </button>

                        <!-- 各国家/地区按钮 -->
                        <button
                          v-for="region in availableRegions"
                          :key="region"
                          @click="handleRegionChange(region)"
                          :aria-pressed="selectedRegion === region"
                          :aria-label="`显示${region}的${recommendations.filter(item => item.location === region).length}个匹配专业`"
                          :class="[
                            'px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-300 flex items-center space-x-1',
                            selectedRegion === region
                              ? 'bg-[#4F46E5] text-white shadow-sm hover:bg-[#4338CA]'
                              : 'bg-white text-gray-600 border border-gray-200 hover:border-[#4F46E5] hover:text-[#4F46E5] hover:bg-[#4F46E5]/5'
                          ]"
                        >
                          <span class="material-icons-outlined text-xs">location_on</span>
                          <span>{{ region }}</span>
                          <span 
                            :class="[
                              'text-xs px-1.5 py-0.5 rounded-full',
                              selectedRegion === region 
                                ? 'bg-white/20' 
                                : 'bg-gray-100 text-gray-600'
                            ]"
                          >
                            {{ recommendations.filter(item => item.location === region).length }}
                          </span>
                        </button>
                      </div>
                    </div>

                    <!-- 学校分组列表 -->
                    <div 
                      v-for="(schoolGroup, index) in schoolGroups" 
                      :key="schoolGroup.schoolKey"
                      class="school-group-card border border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300"
                    >
                      <!-- 学校概览卡片（点击展开/收起） -->
                      <div 
                        @click="toggleSchoolGroup(schoolGroup)"
                        class="school-group-header cursor-pointer p-4 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100 hover:bg-gray-50 transition-colors"
                      >
                        <div class="flex items-center justify-between">
                          <!-- 学校基本信息 -->
                          <div class="flex items-center">
                            <!-- 学校Logo -->
                            <div class="flex-shrink-0 w-12 h-12 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-3 border border-gray-100 shadow-sm">
                              <img 
                                :src="getSchoolLogo({ 学校中文名: schoolGroup.schoolName, 学校英文名: schoolGroup.englishName, school_logo_url: schoolGroup.school_logo_url })"
                                :alt="schoolGroup.schoolName"
                                class="h-8 w-8 object-contain"
                                @error="handleLogoError($event, { 学校中文名: schoolGroup.schoolName, 学校英文名: schoolGroup.englishName })"
                              />
                            </div>
                            
                            <!-- 学校名称和基本信息 -->
                            <div class="flex-grow">
                              <h3 class="text-base font-semibold text-gray-800">{{ schoolGroup.schoolName }}</h3>
                              <p class="text-xs text-gray-500 mt-0.5">{{ schoolGroup.englishName }}</p>
                              <div class="flex items-center mt-1 space-x-4">
                                <span class="text-xs text-gray-600 flex items-center">
                                  <span class="material-icons-outlined text-gray-400 text-xs mr-1">location_on</span>
                                  {{ schoolGroup.location }}
                                </span>
                                <span class="text-xs text-gray-600 flex items-center">
                                  <span class="material-icons-outlined text-gray-400 text-xs mr-1">school</span>
                                  {{ schoolGroup.programs.length }} 个匹配专业
                                </span>
                              </div>
                            </div>
                          </div>

                          <!-- 学校排名和展开图标 -->
                          <div class="flex items-center space-x-3">
                            <!-- 学校排名 -->
                            <div class="ranking-badge">
                              <div class="ranking-label">QS</div>
                              <div class="ranking-number">{{ formatFieldValue(schoolGroup.qsRank, 'N/A') }}</div>
                            </div>
                            
                            <!-- 展开图标 -->
                            <span 
                              class="material-icons-outlined text-gray-400 transition-transform duration-300"
                              :class="{ 'rotate-180': schoolGroup.expanded }"
                            >
                              expand_more
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- 专业列表（展开时显示） -->
                      <div 
                        class="school-programs-container overflow-hidden transition-all duration-400 ease-in-out"
                        :style="{ 
                          maxHeight: schoolGroup.expanded ? schoolGroup.contentHeight + 'px' : '0px',
                          opacity: schoolGroup.expanded ? 1 : 0 
                        }"
                      >
                        <div 
                          class="school-programs-content p-4 space-y-3 bg-gray-25"
                          :ref="el => setProgramsContentRef(schoolGroup.schoolKey, el)"
                        >
                          <!-- 专业卡片列表 -->
                          <div 
                            v-for="(program, pIndex) in schoolGroup.programs" 
                            :key="program.id"
                            :data-program-id="program.program_id || program.id"
                            :data-program-name="program.专业中文名"
                            class="program-card bg-white border border-gray-150 rounded-lg overflow-hidden hover:shadow-sm transition-all duration-300 cursor-pointer"
                            @click="handleViewProgramDetails(program)"
                          >
                            <!-- 专业卡片头部 -->
                            <div class="px-4 py-3 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100">
                              <div class="flex items-start">
                                <!-- 学校Logo区域 -->
                                <div class="flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm">
                                  <img 
                                    :src="getSchoolLogo(program)"
                                    :alt="program.学校中文名"
                                    class="h-10 w-10 object-contain"
                                    @error="handleLogoError($event, program)"
                                  />
                                </div>

                                <!-- 专业名称和基本信息 -->
                                <div class="flex-grow">
                                  <h3 class="text-base font-semibold text-gray-800">{{ program.专业中文名 }}</h3>
                                  <p class="text-xs text-gray-500 mt-0.5">{{ program.专业英文名 }}</p>
                                  <p class="text-xs text-gray-600 mt-1 flex items-center">
                                    <span class="material-icons-outlined text-gray-400 text-xs mr-1">category</span>
                                    {{ program.专业大类 }}
                                  </p>
                                </div>
                              </div>
                            </div>

                            <!-- 卡片主体内容：专业信息 -->
                            <div class="px-4 py-3">
                              <!-- 申请信息 -->
                              <div class="grid grid-cols-3 gap-x-3 gap-y-2 text-xs">
                                <div class="flex flex-col" v-if="shouldShowField(program.专业方向)">
                                  <span class="text-gray-400 text-xs mb-0.5">专业方向</span>
                                  <span class="font-medium text-gray-700">{{ formatFieldValue(program.专业方向, '暂无信息') }}</span>
                                </div>
                                <div class="flex flex-col" v-if="shouldShowField(program.所在学院)">
                                  <span class="text-gray-400 text-xs mb-0.5">所属学院</span>
                                  <span class="font-medium text-gray-700">{{ formatFieldValue(program.所在学院, '暂无信息') }}</span>
                                </div>
                                <div class="flex flex-col">
                                  <span class="text-gray-400 text-xs mb-0.5">学位类型</span>
                                  <span class="font-medium text-gray-700">硕士</span>
                                </div>
                                <div class="flex flex-col" v-if="shouldShowField(program.项目时长)">
                                  <span class="text-gray-400 text-xs mb-0.5">项目时长</span>
                                  <span class="font-medium text-gray-700">{{ formatFieldValue(program.项目时长, '暂无信息') }}</span>
                                </div>
                                <div class="flex flex-col" v-if="shouldShowField(program.tuitionRange)">
                                  <span class="text-gray-400 text-xs mb-0.5">学费</span>
                                  <span class="font-medium text-gray-700">{{ formatFieldValue(program.tuitionRange, '暂无信息') }}</span>
                                </div>
                                <div class="flex flex-col" v-if="shouldShowField(program.专业大类)">
                                  <span class="text-gray-400 text-xs mb-0.5">专业大类</span>
                                  <span class="font-medium text-gray-700">{{ formatFieldValue(program.专业大类, '暂无信息') }}</span>
                                </div>

                              </div>

                              <!-- 亮点标签 -->
                              <div class="mt-3 flex flex-wrap gap-1.5">
                                <span 
                                  v-for="(highlight, hIndex) in program.highlights" 
                                  :key="hIndex"
                                  class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-gray-50 text-gray-600 border border-gray-100"
                                >
                                  {{ highlight }}
                                </span>
                              </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-100 flex justify-between items-center">
                              <a
                                v-if="program.项目官网"
                                :href="program.项目官网"
                                target="_blank"
                                @click.stop
                                class="text-[#4F46E5] text-xs flex items-center group hover:text-[#4338CA] transition-colors"
                              >
                                <span class="material-icons-outlined mr-1 !text-sm">open_in_new</span>
                                <span class="group-hover:underline">查看官网</span>
                              </a>
                              <div v-else class="text-xs text-gray-400">官网信息待查询</div>
                              
                              <div class="flex items-center space-x-2">

                                
                                <!-- 定校书操作按钮（当选择客户时显示） -->
                                <el-button
                                  v-if="selectedClient"
                                  :class="isInClientPrograms(program) ? '!text-green-600 hover:!text-green-700' : '!text-[#4F46E5] hover:!text-[#4338CA]'"
                                  @click.stop="toggleClientProgram(program)"
                                  :disabled="addingToClientPrograms"
                                  size="small"
                                  class="!h-7 !text-xs !rounded-md !px-2.5 transition-colors"
                                >
                                  <span class="material-icons-outlined mr-1 !text-sm">{{ isInClientPrograms(program) ? 'check_circle' : 'add_circle' }}</span>
                                  <span class="!text-xs">{{ isInClientPrograms(program) ? '已添加' : '添加到定校书' }}</span>
                                </el-button>
                                

                              </div>
                            </div>

                            <!-- 专业详细信息（展开时显示） -->
                            <div 
                              class="details-container w-full overflow-hidden" 
                              :style="{ 
                                height: program.detailsHeight + 'px', 
                                opacity: program.showDetails ? 1 : 0,
                                visibility: program.showDetails ? 'visible' : 'hidden',
                                transition: 'height 0.4s ease, opacity 0.4s ease, visibility 0.1s',
                                transitionDelay: program.showDetails ? '0s' : '0.3s, 0.1s, 0.4s'
                              }"
                            >
                              <div class="details-content p-4 bg-gray-50 border-t border-gray-100">
                                <!-- 详情信息，分类显示 -->
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
                                  <!-- 申请要求 -->
                                  <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                                    <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                      <span class="material-icons-outlined text-gray-400 text-sm mr-1">assignment</span>
                                      申请要求
                                    </h4>
                                    <p class="text-xs text-gray-600 leading-relaxed">{{ program.申请要求 || '申请要求信息待查询' }}</p>
                                  </div>
                                  
                                  <!-- 语言要求 -->
                                  <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                                    <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                      <span class="material-icons-outlined text-gray-400 text-sm mr-1">translate</span>
                                      语言要求
                                    </h4>
                                    <p class="text-xs text-gray-600 leading-relaxed">{{ program.语言要求 || '语言要求信息待查询' }}</p>
                                  </div>
                                  
                                  <!-- 项目培养目标 -->
                                  <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                                    <ProgramObjectives
                                      :objectives="program.培养目标"
                                      mode="compact"
                                      :show-title="true"
                                      title-text="培养目标"
                                      empty-message="培养目标信息待查询"
                                      custom-class="!p-0 !bg-transparent"
                                    />
                                  </div>
                                  
                                  <!-- 课程设置 -->
                                  <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                                    <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                      <span class="material-icons-outlined text-gray-400 text-sm mr-1">school</span>
                                      课程设置
                                    </h4>
                                    <p class="text-xs text-gray-600 leading-relaxed">{{ program.课程设置 || '课程设置信息待查询' }}</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 智能推荐模式：原有的单个学校卡片显示 -->
                  <div v-else-if="currentResultMode === 'ai' && filteredRecommendations.length > 0" class="space-y-4">
                    <!-- 学校推荐卡片 - 保持原有布局 -->
                    <div 
                      v-for="(school, index) in filteredRecommendations" 
                      :key="index" 
                      class="school-card relative border border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
                      :data-school-id="school.id"
                      @click="handleViewProgramDetails(school)"
                    >
                      <!-- 保持原有的学校卡片内容... -->
                      <!-- 卡片头部：学校基本信息 -->
                      <div class="px-4 py-3 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100">
                        <div class="flex items-start">
                          <!-- 学校Logo区域 -->
                          <div class="flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm">
                            <img 
                              :src="getSchoolLogo(school)"
                              :alt="school.学校中文名"
                              class="h-10 w-10 object-contain"
                              @error="handleLogoError($event, school)"
                            />
                          </div>

                          <!-- 学校名称和基本信息 -->
                          <div class="flex-grow">
                            <h3 class="text-base font-semibold text-gray-800">{{ school.学校中文名 }} - {{ school.专业中文名 }}</h3>
                            <p class="text-xs text-gray-500 mt-0.5">{{ school.学校英文名 }}{{ school.专业英文名 ? ' - ' + school.专业英文名 : '' }}</p>
                            <p class="text-xs text-gray-600 mt-1 flex items-center">
                              <span class="material-icons-outlined text-gray-400 text-xs mr-1">location_on</span>
                              {{ school.location }}
                            </p>
                          </div>

                          <!-- 评分、等级和排名 -->
                          <div class="flex items-center space-x-2 sm:space-x-3 ml-auto self-center">
                            <!-- 学校排名 - 现代化设计 -->
                            <div class="ranking-badge">
                              <div class="ranking-label">QS</div>
                              <div class="ranking-number">{{ formatFieldValue(school.ranking.match(/\d+/)?.[0], 'N/A') }}</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 卡片主体内容：专业信息 -->
                      <div class="px-4 py-3">
                        <!-- 申请信息 -->
                        <div class="grid grid-cols-3 gap-x-3 gap-y-2 text-xs">
                          <div class="flex flex-col" v-if="shouldShowField(school.专业方向)">
                            <span class="text-gray-400 text-xs mb-0.5">专业方向</span>
                            <span class="font-medium text-gray-700">{{ formatFieldValue(school.专业方向, '暂无信息') }}</span>
                          </div>
                          <div class="flex flex-col" v-if="shouldShowField(school.所在学院)">
                            <span class="text-gray-400 text-xs mb-0.5">所属学院</span>
                            <span class="font-medium text-gray-700">{{ formatFieldValue(school.所在学院, '暂无信息') }}</span>
                          </div>
                          <div class="flex flex-col">
                            <span class="text-gray-400 text-xs mb-0.5">学位类型</span>
                            <span class="font-medium text-gray-700">硕士</span>
                          </div>
                          <div class="flex flex-col" v-if="shouldShowField(school.项目时长)">
                            <span class="text-gray-400 text-xs mb-0.5">项目时长</span>
                            <span class="font-medium text-gray-700">{{ formatFieldValue(school.项目时长, '暂无信息') }}</span>
                          </div>
                          <div class="flex flex-col" v-if="shouldShowField(school.tuitionRange)">
                            <span class="text-gray-400 text-xs mb-0.5">学费</span>
                            <span class="font-medium text-gray-700">{{ formatFieldValue(school.tuitionRange, '暂无信息') }}</span>
                          </div>
                          <div class="flex flex-col" v-if="shouldShowField(school.专业大类)">
                            <span class="text-gray-400 text-xs mb-0.5">专业大类</span>
                            <span class="font-medium text-gray-700">{{ formatFieldValue(school.专业大类, '暂无信息') }}</span>
                          </div>

                        </div>

                        <!-- 亮点标签 -->
                        <div class="mt-3 flex flex-wrap gap-1.5">
                          <span 
                            v-for="(highlight, hIndex) in school.highlights" 
                            :key="hIndex"
                            class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-gray-50 text-gray-600 border border-gray-100"
                          >
                            {{ highlight }}
                          </span>
                        </div>
                      </div>

                      <!-- 操作按钮区域 -->
                      <div class="px-4 py-2.5 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                        <a
                          v-if="school.项目官网"
                          :href="school.项目官网"
                          target="_blank"
                          @click.stop
                          class="text-[#4F46E5] text-xs flex items-center group hover:text-[#4338CA] transition-colors"
                        >
                          <span class="material-icons-outlined mr-1 !text-sm">open_in_new</span>
                          <span class="group-hover:underline">查看官网</span>
                        </a>
                        <div class="flex items-center space-x-2">


                          <!-- 定校书操作按钮（当选择客户时显示） -->
                          <el-button
                            v-if="selectedClient"
                            :class="isInClientPrograms(school) ? '!text-green-600 hover:!text-green-700' : '!text-[#4F46E5] hover:!text-[#4338CA]'"
                            @click.stop="toggleClientProgram(school)"
                            :disabled="addingToClientPrograms"
                            class="!h-7 !text-xs !rounded-md !px-2.5 transition-colors"
                          >
                            <span class="material-icons-outlined mr-1 !text-xs">{{ isInClientPrograms(school) ? 'check_circle' : 'add_circle' }}</span>
                            <span class="!text-xs">{{ isInClientPrograms(school) ? '已添加' : '添加到定校书' }}</span>
                          </el-button>
                        </div>
                      </div>

                      <!-- 详细信息区域 -->
                      <div 
                        class="details-container w-full overflow-hidden" 
                        :style="{ 
                          height: school.detailsHeight + 'px', 
                          opacity: school.showDetails ? 1 : 0,
                          transition: 'height 0.4s ease, opacity 0.4s ease',
                          transitionDelay: school.showDetails ? '0s' : '0s'
                        }"
                      >
                        <div class="details-content p-4 bg-gray-50 border-t border-gray-100">
                          <!-- 详情信息，分类显示 -->
                          <div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
                            <!-- 申请要求 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">assignment</span>
                                申请要求
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.申请要求 }}</p>
                            </div>
                            
                            <!-- 语言要求 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">translate</span>
                                语言要求
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.语言要求 }}</p>
                            </div>
                            
                            <!-- 项目培养目标 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                              <ProgramObjectives
                                :objectives="school.培养目标"
                                mode="compact"
                                :show-title="true"
                                title-text="培养目标"
                                custom-class="!p-0 !bg-transparent"
                              />
                            </div>
                            
                            <!-- 课程设置 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">school</span>
                                课程设置
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.课程设置 }}</p>
                            </div>
                            
                            <!-- 推荐理由 / 匹配说明 -->
                            <div class="bg-white p-3 rounded-lg border border-gray-100 shadow-sm lg:col-span-2">
                              <h4 class="text-xs font-semibold mb-1.5 text-gray-800 flex items-center">
                                <span class="material-icons-outlined text-gray-400 text-sm mr-1">thumb_up</span>
                                {{ currentResultMode === 'ai' ? '推荐理由' : '匹配说明' }}
                              </h4>
                              <p class="text-xs text-gray-600 leading-relaxed">{{ school.reason }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 其他情况：有推荐数据但分组/模式不匹配时的处理 -->
                  <div v-else-if="filteredRecommendations.length > 0" class="space-y-4">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                      <div class="flex items-center text-sm text-yellow-800">
                        <span class="material-icons-outlined text-yellow-600 text-base mr-2">info</span>
                        当前显示的是{{ currentResultMode === 'ai' ? '案例选校匹配' : 
                                     currentResultMode === 'hard_filter' ? '专业库匹配' : '匹配' }}结果，
                        点击"开始匹配"获取最新的{{ enableAiSelection ? '案例选校匹配' : '专业库匹配' }}结果
                      </div>
                    </div>
                    
                    <!-- 简单列表显示 -->
                    <div class="space-y-3">
                      <div 
                        v-for="(school, index) in filteredRecommendations" 
                        :key="index"
                        class="border border-gray-200 rounded-lg p-3 bg-white"
                      >
                        <div class="flex items-center justify-between">
                          <div>
                            <h4 class="text-sm font-medium text-gray-800">{{ school.学校中文名 }}</h4>
                            <p class="text-xs text-gray-500">{{ school.专业中文名 }}</p>
                          </div>
                          <div class="text-xs text-gray-400">
                            {{ currentResultMode === 'ai' ? '案例选校数据' : 
                               currentResultMode === 'hard_filter' ? '专业库匹配数据' : '推荐数据' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 自选校模式 -->
            <div v-else-if="matchingMode === 'filter'" class="pro-card">
              <!-- 结果统计栏 -->
              <div class="pro-card-header">
                <div class="result-info text-sm text-gray-600 flex items-center">
                  <span class="material-icons-outlined text-base mr-1.5">dataset</span>
                  共找到 <strong class="text-gray-800">{{ filterData.pagination.total }}</strong> 个专业项目
                </div>
              </div>

              <!-- 数据内容区域 -->
              <div class="pro-card-body">
                <!-- 固定高度容器避免抖动 -->
                <div class="content-container">
                  <!-- Loading状态 -->
                  <transition name="fade" mode="out-in">
                    <div v-if="filterData.loading" key="loading" class="loading-container">
                      <el-skeleton animated>
                        <template #template>
                          <div class="skeleton-grid">
                            <div
                              v-for="n in 6"
                              :key="n"
                              class="skeleton-card"
                            >
                              <el-skeleton-item variant="image" style="width: 100%; height: 120px;" />
                              <div class="skeleton-content">
                                <el-skeleton-item variant="h3" style="width: 80%;" />
                                <el-skeleton-item variant="text" style="width: 60%;" />
                                <el-skeleton-item variant="text" style="width: 90%;" />
                              </div>
                            </div>
                          </div>
                        </template>
                      </el-skeleton>
                    </div>

                    <!-- 卡片视图 -->
                    <div v-else-if="filterData.programs.length > 0" key="content" class="cards-container">
                      <transition-group name="fade-list" tag="div" class="cards-grid">
                        <div
                          v-for="program in filterData.programs"
                          :key="program.id"
                          class="program-card relative border border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-300"
                          @click="handleViewDetails(program)"
                        >
                          <!-- 卡片头部：学校基本信息 -->
                          <div class="px-4 py-3 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100">
                            <div class="flex items-start">
                              <!-- 学校Logo区域 -->
                              <div class="flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm">
                                <img
                                  :src="getSchoolLogo(program.school_name_cn)"
                                  :alt="program.school_name_cn + ' Logo'"
                                  class="h-10 w-10 object-contain"
                                  @error="handleLogoError($event, { school_name_cn: program.school_name_cn })"
                                />
                              </div>

                              <!-- 学校名称和基本信息 -->
                              <div class="flex-grow">
                                <h3 class="text-base font-semibold text-gray-800">{{ program.school_name_cn }} - {{ program.program_name_cn }}</h3>
                                <p class="text-xs text-gray-500 mt-0.5">{{ program.school_name_en }}{{ program.program_name_en ? ' - ' + program.program_name_en : '' }}</p>
                                <p class="text-xs text-gray-600 mt-1 flex items-center">
                                  <span class="material-icons-outlined text-gray-400 text-xs mr-1">location_on</span>
                                  {{ program.school_region }}
                                </p>
                              </div>

                              <!-- 评分、等级和排名 -->
                              <div class="flex items-center space-x-2 sm:space-x-3 ml-auto self-center">
                                <!-- 学校排名 - 现代化设计 -->
                                <div v-if="program.school_qs_rank" class="ranking-badge">
                                  <div class="ranking-label">QS</div>
                                  <div class="ranking-number">{{ program.school_qs_rank }}</div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 卡片主体内容：专业信息 -->
                          <div class="px-4 py-3">
                            <!-- 申请信息 -->
                            <div class="grid grid-cols-3 gap-x-3 gap-y-2 text-xs">
                              <div v-if="shouldShowField(program.program_direction)" class="flex flex-col">
                                <span class="text-gray-400 text-xs mb-0.5">专业方向</span>
                                <span class="font-medium text-gray-700">{{ formatFieldValue(program.program_direction) }}</span>
                              </div>
                              <div v-if="shouldShowField(program.faculty)" class="flex flex-col">
                                <span class="text-gray-400 text-xs mb-0.5">所属学院</span>
                                <span class="font-medium text-gray-700">{{ formatFieldValue(program.faculty) }}</span>
                              </div>
                              <div v-if="shouldShowField(program.degree)" class="flex flex-col">
                                <span class="text-gray-400 text-xs mb-0.5">学位类型</span>
                                <span class="font-medium text-gray-700">{{ formatFieldValue(program.degree) }}</span>
                              </div>
                              <div v-if="shouldShowField(program.program_duration)" class="flex flex-col">
                                <span class="text-gray-400 text-xs mb-0.5">项目时长</span>
                                <span class="font-medium text-gray-700">{{ formatFieldValue(program.program_duration) }}</span>
                              </div>
                              <div v-if="shouldShowField(program.program_tuition)" class="flex flex-col">
                                <span class="text-gray-400 text-xs mb-0.5">学费</span>
                                <span class="font-medium text-gray-700">{{ formatFieldValue(program.program_tuition) }}</span>
                              </div>
                              <div v-if="shouldShowField(program.program_category)" class="flex flex-col">
                                <span class="text-gray-400 text-xs mb-0.5">专业大类</span>
                                <span class="font-medium text-gray-700">{{ formatFieldValue(program.program_category) }}</span>
                              </div>
                            </div>
                          </div>

                          <!-- 操作按钮区域 -->
                          <div class="px-4 py-2.5 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                            <!-- 左侧：查看官网 -->
                            <div>
                              <a
                                v-if="program.program_website"
                                :href="program.program_website"
                                target="_blank"
                                @click.stop
                                class="text-[#4F46E5] text-xs flex items-center group hover:text-[#4338CA] transition-colors"
                              >
                                <span class="material-icons-outlined mr-1 !text-sm">open_in_new</span>
                                <span class="group-hover:underline">查看官网</span>
                              </a>
                              <div v-else class="text-xs text-gray-400">官网信息待查询</div>
                            </div>

                            <!-- 右侧：操作按钮 -->
                            <div class="flex items-center space-x-2">
                              <!-- 定校书操作按钮（当选择客户时显示） -->
                              <el-button
                                v-if="selectedClient"
                                :class="isInFilterClientPrograms(program) ? '!text-green-600 hover:!text-green-700' : '!text-[#4F46E5] hover:!text-[#4338CA]'"
                                @click.stop="toggleFilterClientProgram(program)"
                                :disabled="addingToClientPrograms"
                                size="small"
                                class="!h-7 !text-xs !rounded-md !px-2.5 transition-colors"
                              >
                                <span class="material-icons-outlined mr-1 !text-sm">{{ isInFilterClientPrograms(program) ? 'check_circle' : 'add_circle' }}</span>
                                <span class="!text-xs">{{ isInFilterClientPrograms(program) ? '已添加' : '添加到定校书' }}</span>
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </transition-group>
                    </div>

                    <!-- 空状态 -->
                    <div v-else key="empty" class="empty-state">
                      <div class="empty-content">
                        <span class="material-icons-outlined empty-icon">search_off</span>
                        <h3>未找到相关专业</h3>
                        <p>请尝试调整搜索条件或筛选条件</p>
                        <el-button type="primary" @click="handleFilterResetFilters" class="btn-primary">
                          重置筛选条件
                        </el-button>
                      </div>
                    </div>
                  </transition>
                </div>

                <!-- 分页组件 -->
                <div v-if="filterData.programs.length > 0" class="pagination-container">
                  <el-pagination
                    v-model:current-page="filterData.pagination.currentPage"
                    :page-size="12"
                    :total="filterData.pagination.total"
                    layout="prev, pager, next"
                    @current-change="handleFilterPageChange"
                    class="pagination-component"
                  />
                </div>
              </div>
            </div>

            <!-- 右侧定校书按钮 -->
            <div
              v-if="selectedClient"
              class="fixed right-0 z-40"
              :style="{ top: `calc(50% - ${64}px)` }"
            >
              <button
                @click="toggleSchoolBookSidebar"
                class="school-book-btn bg-white border border-gray-200 rounded-l-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group active:scale-95 active:shadow-md"
              >
                <div class="flex flex-col items-center justify-center py-4 px-3 space-y-2">
                  <span class="material-icons-outlined text-[#4F46E5] text-xl group-hover:scale-110 transition-transform duration-300">
                    school
                  </span>
                  <div class="flex flex-col items-center space-y-0.5">
                    <span class="text-xs font-medium text-gray-700 leading-tight">
                      查
                    </span>
                    <span class="text-xs font-medium text-gray-700 leading-tight">
                      看
                    </span>
                    <span class="text-xs font-medium text-gray-700 leading-tight">
                      定
                    </span>
                    <span class="text-xs font-medium text-gray-700 leading-tight">
                      校
                    </span>
                    <span class="text-xs font-medium text-gray-700 leading-tight">
                      书
                    </span>
                  </div>
                  <div v-if="clientPrograms.size > 0" class="px-2 py-1 bg-[#4F46E5] text-white text-xs rounded-full font-medium">
                    {{ clientPrograms.size }}
                  </div>
                  <div v-else class="px-2 py-1 bg-gray-400 text-white text-xs rounded-full font-medium">
                    0
                  </div>
                </div>
              </button>
            </div>

            <!-- 右侧定校书侧边栏 -->
            <transition
              name="sidebar-slide"
              @enter="onSidebarEnter"
              @after-enter="onSidebarAfterEnter"
              @leave="onSidebarLeave"
              @after-leave="onSidebarAfterLeave"
            >
              <div
                v-if="selectedClient && showSchoolBookSidebar"
                :key="`sidebar-${selectedClient.id_hashed || selectedClient.id}`"
                class="fixed right-0 w-96 bg-white border-l border-gray-200 shadow-xl z-50 flex flex-col sidebar-container"
                :style="{ top: '64px', height: 'calc(100vh - 64px)' }"
              >
              <!-- 左侧拉回按钮 -->
              <button
                @click="toggleSchoolBookSidebar"
                class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-full bg-white border border-gray-200 border-r-0 rounded-l-lg shadow-lg hover:shadow-xl transition-all duration-300 p-2 z-10 hover:bg-gray-50 active:scale-95 active:shadow-md group"
                style="border-top-right-radius: 0; border-bottom-right-radius: 0;"
                title="收起定校书"
              >
                <span class="material-icons-outlined text-gray-600 text-lg group-hover:scale-110 transition-transform duration-300">chevron_right</span>
              </button>
              <!-- 侧边栏头部 -->
              <div class="flex-shrink-0 px-6 py-5 bg-gradient-to-r from-[#4F46E5]/5 to-[#7C3AED]/5 border-b border-gray-100">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-11 h-11 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-base font-semibold mr-4">
                      {{ selectedClient.name?.charAt(0)?.toUpperCase() || '?' }}
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-800">{{ selectedClient.name }}的定校书</h3>
                      <p class="text-xs text-gray-500 mt-1">{{ clientPrograms.size }} 个院校项目</p>
                    </div>
                  </div>
                  <button
                    @click="toggleSchoolBookSidebar"
                    class="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-all duration-300 active:scale-95 active:bg-gray-200 group"
                  >
                    <span class="material-icons-outlined text-lg group-hover:scale-110 transition-transform duration-300">close</span>
                  </button>
                </div>
              </div>

              <!-- 侧边栏内容 -->
              <div class="flex-1 overflow-y-auto school-book-sidebar" style="max-height: calc(100vh - 64px - 120px - 120px);">
                <div class="p-6">
                  <!-- 加载状态 -->
                  <div v-if="isLoadingClientPrograms" class="text-center py-8">
                    <div class="inline-flex items-center space-x-2 text-[#4F46E5]">
                      <svg class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span class="text-sm font-medium">正在加载定校书...</span>
                    </div>
                  </div>

                  <!-- 定校书列表 -->
                  <transition-group
                    v-else
                    name="program-item"
                    tag="div"
                    class="space-y-3"
                    @enter="onProgramEnter"
                    @leave="onProgramLeave"
                  >
                    <div
                      v-for="(program, index) in sortedClientProgramsList"
                      :key="`program-${program.id || program.program_id || index}`"
                      class="bg-white rounded-xl border border-gray-100 hover:border-gray-200 hover:shadow-sm transition-all duration-300 p-4 program-item"
                      :data-index="index"
                    >
                    <div class="flex items-start justify-between">
                      <!-- 学校Logo -->
                      <div class="flex-shrink-0 mr-3 flex items-center" style="height: 52px;">
                        <img
                          :src="getSchoolLogo({ 学校中文名: program.school_name_cn, 学校英文名: program.school_name_en, 项目官网: program.program_website, school_logo_url: program.school_logo_url })"
                          :alt="program.school_name_cn"
                          class="w-10 h-10 rounded-lg object-cover border border-gray-200 bg-white"
                          @error="handleImageError($event, { 学校中文名: program.school_name_cn, 学校英文名: program.school_name_en })"
                        />
                      </div>

                      <!-- 项目信息 -->
                      <div class="flex-1 min-w-0">
                        <!-- 学校名称 -->
                        <div class="flex items-center mb-1">
                          <h4 class="text-base font-semibold text-gray-900 truncate">{{ program.school_name_cn }}</h4>
                        </div>

                        <!-- 专业名称 -->
                        <p class="text-sm text-gray-700 font-medium mb-2 line-clamp-1">{{ program.program_name_cn }}</p>

                        <!-- 地区和学位信息 -->
                        <div class="flex items-center space-x-3 text-xs text-gray-500">
                          <span class="flex items-center">
                            <span class="material-icons-outlined text-gray-400 text-sm mr-1">location_on</span>
                            {{ program.school_region || '未知地区' }}
                          </span>
                          <span class="flex items-center">
                            <span class="material-icons-outlined text-gray-400 text-sm mr-1">school</span>
                            {{ program.degree || '未知学位' }}
                          </span>
                        </div>
                      </div>

                      <!-- 右侧操作区域：QS排名 + 删除按钮 -->
                      <div class="flex items-center space-x-2 flex-shrink-0 ml-3">
                        <!-- QS排名标签 -->
                        <div v-if="program.school_qs_rank" class="ranking-badge-small">
                          <div class="ranking-label-small">QS</div>
                          <div class="ranking-number-small">{{ program.school_qs_rank }}</div>
                        </div>

                        <!-- 删除按钮 -->
                        <button
                          @click="removeFromClientProgramsFloat(program)"
                          :class="[
                            'p-1.5 rounded-lg transition-all duration-300 group',
                            removingFromClientPrograms.has(program.program_id)
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-gray-400 hover:text-red-500 hover:bg-red-50 active:scale-95 active:bg-red-100'
                          ]"
                          :disabled="removingFromClientPrograms.has(program.program_id)"
                        >
                          <span v-if="!removingFromClientPrograms.has(program.program_id)" class="material-icons-outlined text-base group-hover:scale-110 transition-transform duration-300">delete_outline</span>
                          <svg v-else class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                    </div>
                  </transition-group>

                  <!-- 空状态 -->
                  <div v-if="!isLoadingClientPrograms && sortedClientProgramsList.length === 0" class="text-center py-12">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                      <span class="material-icons-outlined text-gray-400 text-2xl">school</span>
                    </div>
                    <p class="text-base font-medium text-gray-600 mb-2">暂无定校书项目</p>
                    <p class="text-sm text-gray-400">添加专业到定校书后，将在这里显示</p>
                  </div>
                </div>
              </div>

              <!-- 侧边栏底部操作 -->
              <div class="flex-shrink-0 px-6 py-5 bg-white border-t border-gray-100">
                <div class="flex flex-col space-y-3">
                  <!-- 导出按钮 -->
                  <button
                    @click="handleExportSchoolBook"
                    :disabled="clientProgramsList.length === 0 || isExporting"
                    class="w-full bg-[#4F46E5] hover:bg-[#4338CA] disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium py-2 px-3 rounded-lg transition-all duration-300 flex items-center justify-center text-sm"
                  >
                    <span v-if="!isExporting" class="material-icons-outlined text-base mr-1.5">download</span>
                    <svg v-else class="w-4 h-4 animate-spin mr-1.5" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ isExporting ? '正在导出...' : '导出定校书' }}
                  </button>

                  <!-- 查看完整定校书链接 - 只对完整档案客户显示 -->
                  <router-link
                    v-if="selectedClient.profile_type !== false"
                    :to="`/clients/${selectedClient.id_hashed}?tab=schools`"
                    class="w-full text-center text-[#4F46E5] hover:text-[#4338CA] text-sm font-medium py-2.5 px-4 border border-[#4F46E5] rounded-xl transition-all duration-300 hover:bg-[#4F46E5]/5 flex items-center justify-center group"
                  >
                    查看完整定校书
                    <span class="material-icons-outlined text-sm ml-2 transition-transform duration-300 group-hover:translate-x-1">arrow_forward</span>
                  </router-link>
                </div>
              </div>
              </div>
            </transition>

            <!-- 侧边栏遮罩层 -->
            <transition name="overlay-fade">
              <div
                v-if="showSchoolBookSidebar"
                @click="toggleSchoolBookSidebar"
                class="fixed inset-0 bg-black bg-opacity-25 z-40 overlay-backdrop"
              ></div>
            </transition>


          </div>
        </div>
      </div>
    </div>

    <!-- 专业详情悬浮框 -->
    <ProgramDetailDialog
      v-model:visible="detailDialogVisible"
      :program="selectedProgram"
      :loading="detailLoading"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import AnimatedInput from '@/components/common/AnimatedInput.vue';
import ProgramDetailDialog from '@/components/ProgramDetailDialog.vue';
import ProgramObjectives from '@/components/common/ProgramObjectives.vue';
import FloatingLabelInput from '@/components/common/FloatingLabelInput.vue';
import {
  getHomeSchoolList,
  getProgramDetail,
  getProgramList,
  getProgramCount,
  getRegionList,
  getProgramCategories,
  getProgramDirections
} from '@/api/programs';
// 导入客户相关API
import { searchClientsForSchoolMatching, addClientProgram, removeClientProgram, getClientPrograms, getClientById, exportClientSchoolBook, createQuickProfile } from '@/api/client';
import { cleanProgramObjectives } from '@/utils/textCleaner';
import { formatProgramData, shouldShowField, formatFieldValue } from '@/utils/programUtils';
import { getSchoolLogo as getSchoolLogoFallback } from '@/utils/schoolLogos';
import { useSchoolLogosStore } from '@/stores/schoolLogos';
import { useAuthStore } from '@/stores/auth';
import { useDisplayModeStore } from '@/stores/displayMode';
import { AISelectionValidationService } from '@/api/validation';
import ValidationOverlay from '@/components/validation/ValidationOverlay.vue';

/**
 * 选校匹配助手组件
 * 
 * 该组件提供了一个交互式界面，让教育工作者输入学生信息，
 * 获取个性化的院校匹配方案，辅助教育工作者为学生提供精准的留学规划指导。
 * 
 * 主要功能：
 * 1. 多步骤表单收集学生关键信息（基本信息、学术背景、留学意向、软实力）
 * 2. 基于学生数据生成个性化院校匹配方案
 * 3. 匹配结果可筛选和排序
 * 4. 展示院校匹配度和推荐理由
 */

// 创建响应式状态
const isLoading = ref(false);
const activeTab = ref('basic'); // 控制表单标签页
const sortBy = ref('rank-asc'); // 排序方式，默认为推荐排名

// 智能选校开关
const enableAiSelection = ref(false);

// 模式选择缓存 key
const MATCHING_MODE_CACHE_KEY = 'school_assistant_matching_mode';
const AI_SELECTION_CACHE_KEY = 'school_assistant_ai_selection';

// 从缓存中恢复模式设置
const restoreMatchingModeFromCache = () => {
  try {
    const cachedMode = localStorage.getItem(MATCHING_MODE_CACHE_KEY);
    const cachedAiSelection = localStorage.getItem(AI_SELECTION_CACHE_KEY);
    
    if (cachedMode && ['ai', 'list', 'filter'].includes(cachedMode)) {
      matchingMode.value = cachedMode;
      console.log('从缓存恢复匹配模式:', cachedMode);
    }
    
    if (cachedAiSelection !== null) {
      enableAiSelection.value = cachedAiSelection === 'true';
      console.log('从缓存恢复智能选校状态:', enableAiSelection.value);
    }
  } catch (error) {
    console.warn('恢复匹配模式缓存时出错:', error);
    // 如果缓存读取失败，清空值，让主逻辑根据套餐状态设置默认值
    matchingMode.value = '';
    enableAiSelection.value = false;
  }
};

// 保存模式设置到缓存
const saveMatchingModeToCache = (mode, aiSelectionEnabled) => {
  try {
    localStorage.setItem(MATCHING_MODE_CACHE_KEY, mode);
    localStorage.setItem(AI_SELECTION_CACHE_KEY, aiSelectionEnabled.toString());
    console.log('保存匹配模式到缓存:', mode, '，智能选校:', aiSelectionEnabled);
  } catch (error) {
    console.warn('保存匹配模式缓存时出错:', error);
  }
};

// 处理智能选校开关变化
const handleAiSelectionChange = () => {
  // 保存智能选校状态到缓存
  saveMatchingModeToCache(matchingMode.value, enableAiSelection.value);
  // 执行原有的验证逻辑
  checkAndShowClientValidationOnConfirm();
};

// 国家/地区选择限制计算属性
const countrySelectionConfig = computed(() => {
  if (enableAiSelection.value) {
    // 智能匹配模式：只能选择单个国家
    return {
      multiple: false,
      maxCount: 1,
      placeholder: '请选择意向的国家或地区'
    };
  } else {
    // 专业库匹配模式：可以选择最多3个国家
    return {
      multiple: true,
      maxCount: 3,
      placeholder: '请选择意向的国家或地区（可多选）'
    };
  }
});

// 带禁用状态的国家选项计算属性
const countryOptionsWithDisabled = computed(() => {
  const baseOptions = [
    { label: '中国香港', value: '中国香港' },
    { label: '新加坡', value: '新加坡' },
    { label: '英国', value: '英国' },
    { label: '美国', value: '美国' },
    { label: '澳大利亚', value: '澳大利亚' },
    { label: '中国澳门', value: '中国澳门' },
    { label: '马来西亚', value: '马来西亚' }
  ];

  // 如果是专业库匹配模式且已选择3个国家，禁用未选中的选项
  if (!enableAiSelection.value && Array.isArray(profileForm.intention.countries) && profileForm.intention.countries.length >= 3) {
    return baseOptions.map(option => ({
      ...option,
      disabled: !profileForm.intention.countries.includes(option.value)
    }));
  }

  // 其他情况下不禁用任何选项
  return baseOptions.map(option => ({
    ...option,
    disabled: false
  }));
});

// 带禁用状态的专业选项计算属性
const majorOptionsWithDisabled = computed(() => {
  const baseMajorGroups = [
    {
      label: '商科领域',
      options: [
        { label: '金工金数', value: 'financial_engineering' },
        { label: '金融', value: 'finance' },
        { label: '商业分析', value: 'business_analytics' },
        { label: '经济', value: 'economics' },
        { label: '会计', value: 'accounting' },
        { label: '市场营销', value: 'marketing' },
        { label: '信息系统', value: 'information_systems' },
        { label: '管理', value: 'management' },
        { label: '人力资源管理', value: 'hrm' },
        { label: '供应链管理', value: 'scm' },
        { label: '创业与创新', value: 'entrepreneurship' },
        { label: '房地产', value: 'real_estate' },
        { label: '旅游酒店管理', value: 'hospitality' },
        { label: '工商管理', value: 'business_admin' },
        { label: '其他商科', value: 'other_business' }
      ]
    },
    {
      label: '社科领域',
      options: [
        { label: '教育', value: 'education' },
        { label: '建筑', value: 'architecture' },
        { label: '法律', value: 'law' },
        { label: '社会学与社工', value: 'sociology' },
        { label: '国际关系', value: 'international_relations' },
        { label: '哲学', value: 'philosophy' },
        { label: '历史', value: 'history' },
        { label: '公共政策与事务', value: 'public_policy' },
        { label: '艺术', value: 'arts' },
        { label: '公共卫生', value: 'public_health' },
        { label: '心理学', value: 'psychology' },
        { label: '体育', value: 'sports' },
        { label: '药学', value: 'pharmacy' },
        { label: '医学', value: 'medicine' },
        { label: '新闻', value: 'journalism' },
        { label: '影视', value: 'film' },
        { label: '文化', value: 'culture' },
        { label: '媒体与传播', value: 'media_communication' },
        { label: '新媒体', value: 'new_media' },
        { label: '媒介与社会', value: 'media_society' },
        { label: '科学传播', value: 'science_communication' },
        { label: '策略传播', value: 'strategic_communication' },
        { label: '媒体产业', value: 'media_industry' },
        { label: '语言', value: 'language' },
        { label: '其他社科', value: 'other_social_science' }
      ]
    },
    {
      label: '工科领域',
      options: [
        { label: '计算机', value: 'computer_science' },
        { label: '电气电子', value: 'electrical_engineering' },
        { label: '机械工程', value: 'mechanical_engineering' },
        { label: '材料', value: 'materials' },
        { label: '化工', value: 'chemical_engineering' },
        { label: '生物工程', value: 'bioengineering' },
        { label: '土木工程', value: 'civil_engineering' },
        { label: '工程管理', value: 'engineering_management' },
        { label: '环境工程', value: 'environmental_engineering' },
        { label: '工业工程', value: 'industrial_engineering' },
        { label: '能源', value: 'energy' },
        { label: '航空工程', value: 'aerospace' },
        { label: '地球科学', value: 'earth_science' },
        { label: '交通运输', value: 'transportation' },
        { label: '海洋技术', value: 'marine_technology' },
        { label: '食品科学', value: 'food_science' },
        { label: '其他工科', value: 'other_engineering' }
      ]
    },
    {
      label: '理科领域',
      options: [
        { label: '物理', value: 'physics' },
        { label: '化学', value: 'chemistry' },
        { label: '数学', value: 'mathematics' },
        { label: '生物', value: 'biology' },
        { label: '数据科学', value: 'data_science' }
      ]
    }
  ];

  // 获取当前选择的专业列表
  const selectedMajors = Array.isArray(profileForm.intention.majors) ? profileForm.intention.majors : [];

  return baseMajorGroups.map(group => ({
    ...group,
    options: group.options.map(option => ({
      ...option,
      // 如果已选择3个专业，禁用未选中的选项
      disabled: selectedMajors.length >= 3 && !selectedMajors.includes(option.value)
    }))
  }));
});

// 验证相关状态
const validationState = reactive({
  packageAccess: {
    canAccess: true,
    reason: '',
    message: '',
    packageStatus: null
  },
  showValidationOverlay: true, // 初始显示遮罩，验证通过后隐藏
  validationReason: '',
  isValidating: true // 初始为验证中状态
});

// 匹配模式状态
const matchingMode = ref('ai'); // 'ai' 为案例选校，'list' 为专业库选校，'filter' 为自选校

// 自选校模式的数据
const filterData = reactive({
  regions: [],
  categories: [],
  directions: [],
  programs: [],
  loading: false,
  pagination: {
    currentPage: 1,
    pageSize: 12, // 固定每页12个专业
    total: 0
  }
});

// 初始加载状态
const isInitialLoading = ref(true);

// 查询结果缓存，避免重复查询
const queryCache = new Map();

// 自选校搜索表单
const filterSearchForm = reactive({
  keyword: '',
  regions: [],
  program_category: '',
  program_directions: []
});

// 筛选状态
const selectedFilterCategory = ref('');

// 响应式布局状态
const sidebarCollapsed = ref(false);
const isLargeScreen = ref(false);
const isExtraLargeScreen = ref(false);

// 检查屏幕尺寸
const checkScreenSize = () => {
  const width = window.innerWidth;
  isLargeScreen.value = width >= 1024; // lg断点
  isExtraLargeScreen.value = width >= 1280; // xl断点
  
  // 在xl以下的大屏幕上默认收起侧边栏以节省空间
  if (isLargeScreen.value && !isExtraLargeScreen.value && !sidebarCollapsed.value) {
    sidebarCollapsed.value = true;
  }
  // 在xl及以上或小屏幕上展开侧边栏
  else if (!isLargeScreen.value || isExtraLargeScreen.value) {
    sidebarCollapsed.value = false;
  }
};

// 切换侧边栏状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

// 设置匹配模式
const setMatchingMode = (mode) => {
  matchingMode.value = mode;
  // 根据模式自动设置智能选校开关
  if (mode === 'ai') {
    enableAiSelection.value = true;  // 案例选校：启用AI智能匹配
  } else if (mode === 'list') {
    enableAiSelection.value = false; // 专业库选校：专业库匹配，不使用AI
  } else if (mode === 'filter') {
    enableAiSelection.value = false; // 自选校：不使用AI
    // 切换到自选校模式时，加载筛选数据
    loadFilterData();
  }
  
  // 保存模式选择到缓存
  saveMatchingModeToCache(mode, enableAiSelection.value);
};

// 加载自选校模式的数据（支持静默刷新）
const loadFilterData = async (silent = false) => {
  try {
    if (!silent) {
      filterData.loading = true;
    }
    
    await Promise.all([
      loadFilterRegions(silent),
      loadFilterCategories(silent),
      loadFilterDirections(silent),
      loadFilterPrograms(false, silent) // 不重置页面，支持静默刷新
    ]);
    
    if (!silent) {
      console.log('选校筛选数据加载完成');
    }
  } catch (error) {
    if (!silent) {
      console.error('加载筛选数据失败:', error);
    }
  } finally {
    if (!silent) {
      filterData.loading = false;
    }
  }
};

// 加载地区列表（支持静默刷新）
const loadFilterRegions = async (silent = false) => {
  try {
    const result = await getRegionList();
    filterData.regions = Array.isArray(result) ? result : [];
  } catch (error) {
    if (!silent) {
      console.error('加载地区列表失败:', error);
    }
    filterData.regions = [];
  }
};

// 加载专业大类列表（支持静默刷新）
const loadFilterCategories = async (silent = false) => {
  try {
    const result = await getProgramCategories();
    filterData.categories = Array.isArray(result) ? result.filter(category =>
      category && category.name && typeof category.name === 'string'
    ) : [];
  } catch (error) {
    if (!silent) {
      console.error('加载专业大类失败:', error);
    }
    filterData.categories = [];
  }
};

// 加载专业方向列表（支持静默刷新）
const loadFilterDirections = async (silent = false) => {
  try {
    const result = await getProgramDirections();
    filterData.directions = Array.isArray(result) ? result.filter(direction =>
      direction && direction.name && typeof direction.name === 'string'
    ) : [];
  } catch (error) {
    if (!silent) {
      console.error('加载专业方向失败:', error);
    }
    filterData.directions = [];
  }
};

// 简化的专业列表加载函数 - 使用后端智能搜索API（支持静默刷新）
const loadFilterPrograms = async (resetPage = false, silent = false) => {
  if (!silent) {
    filterData.loading = true;
  }

  if (resetPage) {
    filterData.pagination.currentPage = 1;
  }

  try {
    // 构建查询参数
    const queryParams = {
      limit: filterData.pagination.pageSize,
      offset: (filterData.pagination.currentPage - 1) * filterData.pagination.pageSize
    };

    // 智能关键词搜索 - 使用后端新的search参数
    if (filterSearchForm.keyword && filterSearchForm.keyword.trim()) {
      queryParams.search = filterSearchForm.keyword.trim();
    }

    // 地区筛选 - 支持多选，后端会处理OR逻辑
    if (filterSearchForm.regions && filterSearchForm.regions.length > 0) {
      // 对于多个地区，我们需要分别查询然后合并（临时方案）
      // 后续可以优化后端API支持多地区参数
      if (filterSearchForm.regions.length === 1) {
        queryParams.region = filterSearchForm.regions[0];
      }
      // 多地区的情况暂时使用第一个地区，或者可以循环查询
    }

    // 专业大类筛选
    if (filterSearchForm.program_category && filterSearchForm.program_category.trim()) {
      queryParams.program_category = filterSearchForm.program_category.trim();
    }

    // 专业方向筛选 - 支持多选，后端会处理OR逻辑
    if (filterSearchForm.program_directions && filterSearchForm.program_directions.length > 0) {
      // 对于多个专业方向，我们需要分别查询然后合并（临时方案）
      if (filterSearchForm.program_directions.length === 1) {
        queryParams.program_direction = filterSearchForm.program_directions[0];
      }
      // 多方向的情况暂时使用第一个方向，或者可以循环查询
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('执行简化搜索查询:', {
        查询参数: queryParams,
        当前页: filterData.pagination.currentPage,
        页面大小: filterData.pagination.pageSize
      });
    }

    // 处理多选条件的查询逻辑
    let allPrograms = [];
    let totalCount = 0;

    if ((filterSearchForm.regions && filterSearchForm.regions.length > 1) || 
        (filterSearchForm.program_directions && filterSearchForm.program_directions.length > 1)) {
      
      // 有多选条件时，需要组合查询
      const regions = filterSearchForm.regions.length > 0 ? filterSearchForm.regions : [null];
      const directions = filterSearchForm.program_directions.length > 0 ? filterSearchForm.program_directions : [null];
      
      const uniquePrograms = new Map();
      
      for (const region of regions) {
        for (const direction of directions) {
          const batchParams = { ...queryParams };
          if (region) batchParams.region = region;
          if (direction) batchParams.program_direction = direction;
          
          // 暂时设置较大的limit来获取所有结果，后面再分页
          batchParams.limit = 1000;
          batchParams.offset = 0;
          
          try {
            const batchResult = await getProgramList(batchParams);
            if (Array.isArray(batchResult)) {
              batchResult.forEach(program => {
                if (program.id && !uniquePrograms.has(program.id)) {
                  uniquePrograms.set(program.id, program);
                }
              });
            }
          } catch (error) {
            console.warn(`查询失败 (地区="${region || '不限'}", 方向="${direction || '不限'}"):`, error);
          }
        }
      }
      
      allPrograms = Array.from(uniquePrograms.values());
      
      // 排序：按QS排名
      allPrograms.sort((a, b) => {
        const qsRankA = parseInt(a.school_qs_rank) || 999999;
        const qsRankB = parseInt(b.school_qs_rank) || 999999;
        
        if (qsRankA !== qsRankB) {
          return qsRankA - qsRankB;
        }
        
        return (a.school_name_cn || '').localeCompare(b.school_name_cn || '', 'zh-CN');
      });
      
      totalCount = allPrograms.length;
      
      // 前端分页
      const startIndex = (filterData.pagination.currentPage - 1) * filterData.pagination.pageSize;
      const endIndex = startIndex + filterData.pagination.pageSize;
      filterData.programs = allPrograms.slice(startIndex, endIndex);
      
    } else {
      // 单选或无选择条件时，直接使用后端API
      
      // 获取当前页数据
      const programsResult = await getProgramList(queryParams);
      filterData.programs = Array.isArray(programsResult) ? programsResult : [];
      
      // 获取总数（仅在首次加载或重置时）
      if (resetPage || filterData.pagination.currentPage === 1) {
        const countParams = { ...queryParams };
        delete countParams.limit;
        delete countParams.offset;
        
        const countResult = await getProgramCount(countParams);
        totalCount = countResult?.total || 0;
      } else {
        totalCount = filterData.pagination.total; // 保持之前的总数
      }
    }

    // 更新分页信息
    filterData.pagination.total = totalCount;

    if (process.env.NODE_ENV === 'development') {
      console.log('简化搜索完成:', {
        当前页专业数量: filterData.programs.length,
        总专业数: filterData.pagination.total,
        当前页码: filterData.pagination.currentPage,
        优化说明: '使用后端智能搜索API，简化前端逻辑'
      });
    }

    // 预加载学校Logo
    if (filterData.programs.length > 0) {
      try {
        await logoStore.preloadSchoolLogos(filterData.programs);
      } catch (error) {
        console.warn('预加载学校logo失败:', error);
      }
    }

  } catch (error) {
    if (!silent) {
      console.error('加载专业列表失败:', error);
    }
    filterData.programs = [];
    filterData.pagination.total = 0;
  } finally {
    if (!silent) {
      filterData.loading = false;
    }
  }
};



// 计算属性：根据选中的专业大类筛选专业方向
const filteredFilterDirections = computed(() => {
  if (!selectedFilterCategory.value) return [];

  const categoryKeywords = {
    '商科': [
      '金工金数', '金融', '商业分析', '经济', '会计', '市场营销', '信息系统',
      '管理', '人力资源管理', '供应链管理', '创业与创新', '房地产',
      '旅游酒店管理', '工商管理', '其他商科'
    ],
    '工科': [
      '计算机', '电气电子', '机械工程', '材料', '化工', '生物工程',
      '土木工程', '工程管理', '环境工程', '工业工程', '能源',
      '航空工程', '地球科学', '交通运输', '海洋技术', '食品科学', '其他工科'
    ],
    '理科': [
      '物理', '化学', '数学', '生物', '数据科学', '其他理科'
    ],
    '社科': [
      '教育', '建筑', '法律', '社会学与社工', '国际关系', '哲学', '历史',
      '公共政策与事务', '艺术', '公共卫生', '心理学', '体育', '药学', '医学',
      '新闻', '影视', '文化', '媒体与传播', '新媒体', '媒介与社会',
      '科学传播', '策略传播', '媒体产业', '语言', '其他社科'
    ]
  };

  const keywords = categoryKeywords[selectedFilterCategory.value] || [];
  return filterData.directions.filter(direction => keywords.includes(direction.name))
    .sort((a, b) => {
      const aIsOther = a.name.startsWith('其他');
      const bIsOther = b.name.startsWith('其他');
      if (aIsOther && !bIsOther) return 1;
      if (!aIsOther && bIsOther) return -1;
      return 0;
    });
});

// 处理筛选搜索
let filterSearchTimer = null;
const handleFilterSearch = () => {
  if (filterSearchTimer) {
    clearTimeout(filterSearchTimer);
  }
  filterSearchTimer = setTimeout(() => {
    loadFilterPrograms(true);
  }, 300);
};

// 处理筛选变化
const handleFilterChange = () => {
  // 清除缓存，因为筛选条件已改变
  queryCache.clear();
  loadFilterPrograms(true);
};

// 清除筛选搜索
const handleFilterClearSearch = () => {
  loadFilterPrograms(true);
};

// 重置筛选条件
const handleFilterResetFilters = () => {
  Object.keys(filterSearchForm).forEach(key => {
    if (key === 'program_directions' || key === 'regions') {
      filterSearchForm[key] = [];
    } else {
      filterSearchForm[key] = '';
    }
  });
  selectedFilterCategory.value = '';
  loadFilterPrograms(true);
};

// 处理地区点击
const handleFilterRegionClick = (region) => {
  if (region === '') {
    filterSearchForm.regions = [];
  } else {
    if (filterSearchForm.regions.includes(region)) {
      filterSearchForm.regions = filterSearchForm.regions.filter(r => r !== region);
    } else {
      filterSearchForm.regions.push(region);
    }
  }
  handleFilterChange();
};

// 处理专业大类点击
const handleFilterCategoryClick = (category) => {
  if (selectedFilterCategory.value === category) {
    // 取消选择当前大类：清空大类选择和所有专业方向
    selectedFilterCategory.value = '';
    filterSearchForm.program_category = '';
    filterSearchForm.program_directions = [];
  } else {
    // 选择新的大类：设置新大类并清空专业方向
    selectedFilterCategory.value = category;
    filterSearchForm.program_category = category;
    filterSearchForm.program_directions = [];
  }
  handleFilterChange();
};

// 处理专业方向点击
const handleFilterDirectionClick = (direction) => {
  if (filterSearchForm.program_directions.includes(direction)) {
    filterSearchForm.program_directions = filterSearchForm.program_directions.filter(d => d !== direction);
  } else {
    filterSearchForm.program_directions.push(direction);
  }
  handleFilterChange();
};

// 处理删除已选择的专业方向
const handleFilterDirectionRemove = (direction) => {
  filterSearchForm.program_directions = filterSearchForm.program_directions.filter(d => d !== direction);
  handleFilterChange();
};

// 处理分页变化
const handleFilterPageChange = (page) => {
  filterData.pagination.currentPage = page;
  loadFilterPrograms(false); // 不重置页面
  
  // 滚动到页面顶部
  nextTick(() => {
    const mainContent = document.querySelector('.page-content-wrapper');
    if (mainContent) {
      mainContent.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  });
};

// 处理每页大小变化（已禁用，页面大小固定为12）
const handleFilterPageSizeChange = (size) => {
  // 功能已禁用，页面大小固定为12个专业
  console.warn('页面大小变化功能已禁用，固定每页12个专业');
};



// 自选校模式：检查专业是否在客户定校书中
const isInFilterClientPrograms = (program) => {
  if (!selectedClient.value || !program || !program.id) {
    return false;
  }
  const programId = program.id;
  return clientPrograms.value.has(programId);
};

// 自选校模式：切换客户定校书中的专业（优化版本，移除防抖机制）
const toggleFilterClientProgram = async (program) => {
  // 基础验证
  if (!selectedClient.value) {
    ElMessage.warning('请先选择客户');
    return;
  }

  if (!program || !program.id) {
    console.error('无效的专业数据:', program);
    ElMessage.error('专业数据无效');
    return;
  }

  // 防止重复操作
  if (addingToClientPrograms.value) {
    return;
  }

  const programId = program.id;
  const isInPrograms = clientPrograms.value.has(programId);

  try {
    addingToClientPrograms.value = true;

    if (isInPrograms) {
      // 从定校书中移除
      await removeClientProgram(selectedClient.value.id_hashed, programId);

      // 移除成功后，重新从服务器获取最新的定校书数据
      await loadClientPrograms();

      // 移除成功，无需提示
    } else {
      // 添加到定校书 - 使用与智能选校相同的数据结构
      const programData = {
        program_id: programId,
        school_name_cn: program.school_name_cn || '',
        school_name_en: program.school_name_en || '',
        program_name_cn: program.program_name_cn || '',
        program_name_en: program.program_name_en || '',
        program_category: program.program_category || '',
        school_region: program.school_region || '',
        degree: program.degree || '硕士',
        school_qs_rank: program.school_qs_rank || '',
        school_logo_url: program.school_logo_url || '',
        program_website: program.program_website || '',
        gpa_requirements: program.gpa_requirements || '',
        language_requirements: program.language_requirements || '',
        application_requirements: program.application_requirements || '',
        program_tuition: program.program_tuition || '',
        enrollment_time: program.enrollment_time || '',
        program_duration: program.program_duration || '',
        notes: '自选校匹配推荐'
      };

      await addClientProgram(selectedClient.value.id_hashed, programData);

      // 添加成功后，重新从服务器获取最新的定校书数据
      // 这样可以确保显示的数据与服务器一致，包括正确的ID等信息
      await loadClientPrograms();

      // 添加成功，无需提示
    }
  } catch (error) {
    console.error('定校书操作失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  } finally {
    addingToClientPrograms.value = false;
  }
};

// 处理查看专业详情
const handleViewDetails = async (program) => {
  try {
    // 设置选中的专业
    selectedProgram.value = null;
    detailLoading.value = true;
    detailDialogVisible.value = true;

    // 加载专业详情
    const programDetail = await getProgramDetail(program.id);
    selectedProgram.value = programDetail;

    console.log('查看专业详情:', programDetail);
  } catch (error) {
    console.error('加载专业详情失败:', error);
    ElMessage.error('加载专业详情失败');
    detailDialogVisible.value = false;
  } finally {
    detailLoading.value = false;
  }
};

// 处理查看官网
const handleViewWebsite = (program) => {
  if (program.program_website) {
    window.open(program.program_website, '_blank');
  }
};

// 处理Logo错误 - 使用已存在的函数

// 表单折叠状态控制
const sectionStates = reactive({
  client: true,    // 默认展开客户档案
  academic: true,  // 默认展开学术背景
  intention: true, // 默认展开留学意向
  strength: false  // 默认折叠软实力与偏好
});

// 表单完成状态追踪
const sectionCompletion = reactive({
  academic: false,
  intention: false,
  strength: false  // 软实力部分现在也需要检查，默认未完成
});

const userHasInteractedWithSection = reactive({
  academic: false,
  intention: false,
  strength: false
});

// 表单验证状态
const formValidation = reactive({
  errors: {
    academic: {
      school: false,
      major: false,
      gpa: false
    },
    intention: {
      countries: false,
      majors: false
    }
  },
  hasValidated: false
});

// 切换表单区域展开/折叠状态
const toggleSection = (section) => {
  const originallyCollapsed = !sectionStates[section];
  sectionStates[section] = !sectionStates[section];

  // 如果用户手动展开了一个原本是折叠的区域
  if (sectionStates[section] && originallyCollapsed) {
    userHasInteractedWithSection[section] = true;
  }
  // 如果用户手动折叠，则重置交互状态，允许后续的自动折叠/展开
  if (!sectionStates[section]) {
    userHasInteractedWithSection[section] = false;
  }
};

// 检查表单区域完成状态（仅用于状态记录，不自动收缩）
const checkSectionCompletion = (section) => {
  if (section === 'academic') {
    // 安全检查：确保 profileForm 和 profileForm.academic 存在
    if (!profileForm || !profileForm.academic) {
      console.warn('profileForm.academic 不存在，跳过完成状态检查');
      return;
    }
    
    const { school, major, gpa } = profileForm.academic;
    const isComplete = !!school && !!major && !!gpa;
    sectionCompletion.academic = isComplete;
    
    // 实时清除错误状态 - 每个字段独立验证
    if (formValidation.hasValidated) {
      formValidation.errors.academic.school = !school;
      formValidation.errors.academic.major = !major;
      formValidation.errors.academic.gpa = !gpa;
    }
  } else if (section === 'intention') {
    // 安全检查：确保 profileForm 和 profileForm.intention 存在
    if (!profileForm || !profileForm.intention) {
      console.warn('profileForm.intention 不存在，跳过完成状态检查');
      return;
    }
    
    const { countries, majors } = profileForm.intention;
    const isComplete = countries.length > 0 && majors.length > 0;
    sectionCompletion.intention = isComplete;
    
    // 实时清除错误状态 - 每个字段独立验证
    if (formValidation.hasValidated) {
      formValidation.errors.intention.countries = !countries.length;
      formValidation.errors.intention.majors = !majors.length;
    }
  } else if (section === 'strength') {
    // 安全检查：确保 profileForm 和 profileForm.strength 存在
    if (!profileForm || !profileForm.strength) {
      console.warn('profileForm.strength 不存在，跳过完成状态检查');
      return;
    }
    
    const { competition, internship, research, rankingPreference, preferenceType } = profileForm.strength;
    // 软实力部分都是可选的，只要有任何一项填写就算完成
    const hasAnyValue = 
      (Array.isArray(competition) ? competition.length > 0 : !!competition) ||
      (Array.isArray(internship) ? internship.length > 0 : !!internship) ||
      (Array.isArray(research) ? research.length > 0 : !!research) ||
      (Array.isArray(rankingPreference) ? rankingPreference.length > 0 : !!rankingPreference) ||
      !!preferenceType;
    sectionCompletion.strength = hasAnyValue;
  }
};

// 添加单个字段验证清除函数
const clearFieldError = (section, field) => {
  if (formValidation.hasValidated && formValidation.errors[section] && formValidation.errors[section][field] !== undefined) {
    const value = profileForm[section][field];
    if (Array.isArray(value)) {
      // 对于数组类型（如多选），只要有值就清除错误
      formValidation.errors[section][field] = value.length === 0;
    } else {
      // 对于普通字段，只要有值就清除错误（包括字符串、数字等）
      formValidation.errors[section][field] = !value || value === '' || value === null || value === undefined;
    }
  }
};

// 处理国家/地区选择变化
const handleCountryChange = (newValue) => {
  const config = countrySelectionConfig.value;
  
  // 如果是智能匹配模式（单选），直接设置值
  if (!config.multiple) {
    profileForm.intention.countries = newValue;
  } else {
    // 专业库匹配模式（多选），检查数量限制
    if (Array.isArray(newValue) && newValue.length > config.maxCount) {
      // 超出限制，保持原有选择并提示用户
      ElMessage.warning(`专业库匹配模式最多只能选择${config.maxCount}个国家/地区`);
      return;
    }
    profileForm.intention.countries = newValue;
  }
  
  // 执行原有的验证和检查逻辑
  clearFieldError('intention', 'countries');
  checkSectionCompletion('intention');
  checkAndShowClientValidationOnConfirm();
};

// 处理专业选择变化
const handleMajorChange = (newValue) => {
  // 检查数量限制
  if (Array.isArray(newValue) && newValue.length > 3) {
    // 超出限制，保持原有选择并提示用户
    ElMessage.warning('最多只能选择3个专业/领域');
    return;
  }
  
  profileForm.intention.majors = newValue;
  
  // 执行原有的验证和检查逻辑
  clearFieldError('intention', 'majors');
  checkSectionCompletion('intention');
  checkAndShowClientValidationOnConfirm();
};

// 表单标签页配置
const tabs = [
  { key: 'basic', name: '基本信息' },
  { key: 'academic', name: '学术背景' },
  { key: 'intention', name: '留学意向' },
  { key: 'strength', name: '软实力与偏好' },
];

// 表单数据
const profileForm = reactive({
  // 学术背景
  academic: {
    education: 'master', // 默认设置为硕士
    school: '',
    major: '',
    gpa: '',
    gpaScale: '100', // 只支持百分制
    ranking: '',
  },
  // 留学意向
  intention: {
    countries: [],
    majors: [],
    duration: '', // 隐藏字段，默认为空（不限）
  },
  // 软实力与偏好
  strength: {
    research: '',
    internship: '',
    papers: '',
    rankingPreference: [],
    preferenceType: '' // 可选项，默认为空（系统会使用平衡型）
  }
});

// 匹配结果
const recommendations = ref([]);
const hasSubmitted = ref(false);
const currentResultMode = ref(''); // 记录当前结果的实际来源模式：'ai' 或 'hard_filter'

// 专业库匹配模式的学校分组数据
const schoolGroups = ref([]);
const programsContentRefs = ref({});

// 国家/地区筛选相关状态
const selectedRegion = ref('all'); // 当前选中的国家/地区，'all'表示显示所有

// 使用全局store
const logoStore = useSchoolLogosStore();
const authStore = useAuthStore();
const displayModeStore = useDisplayModeStore();

// 身份判断和页面标题
const isOrganizationAccount = computed(() => {
  const identityType = authStore.currentIdentity?.identity_type;
  const organizationId = authStore.currentIdentity?.organization_id;
  return identityType === 'organization' && organizationId;
});

const organizationName = computed(() => {
  const orgName = authStore.currentIdentity?.organization_name || 
                  authStore.currentIdentity?.organization?.name ||
                  authStore.user?.organization?.name ||
                  authStore.user?.organization_name;
  return orgName || '未知组织';
});

const pageTitle = computed(() => {
  if (isOrganizationAccount.value) {
    return `${organizationName.value}选校系统`;
  } else {
    return '选校匹配';
  }
});

const pageSubtitle = computed(() => {
  if (isOrganizationAccount.value) {
    return '让选校更直观、更方便';
  } else {
    return '填写信息，获取精准的院校匹配方案';
  }
});

// GPA验证状态
const gpaValidationError = ref(false);
const gpaErrorMessage = ref('');

// 客户档案相关状态
const selectedClient = ref(null);
const showClientSelector = ref(false);
const clientSearchQuery = ref('');
const clientSearchResults = ref([]);
const clientSearchLoading = ref(false);
const clientPrograms = ref(new Set()); // 存储客户已有的专业ID集合
const clientDropdown = ref(null);
const dropdownStyle = ref({});
const defaultClientList = ref([]); // 缓存默认客户列表

// 客户选择验证状态
const clientValidationError = ref(false);
const clientValidationMessage = ref('');
const hasValidClientSelection = ref(false);

// 定校书侧边栏相关状态
const showSchoolBookSidebar = ref(false); // 侧边栏显示状态
const clientProgramsList = ref([]); // 客户定校书程序列表
const removingFromClientPrograms = ref(new Set()); // 正在删除的项目ID集合
const isExporting = ref(false); // 导出状态
const isToggling = ref(false); // 侧边栏切换状态，防止快速点击
const isLoadingClientPrograms = ref(false); // 定校书数据加载状态

// 定校书项目排序计算属性 - 按地区和QS排名排序
const sortedClientProgramsList = computed(() => {
  try {
    if (!clientProgramsList.value || !Array.isArray(clientProgramsList.value) || clientProgramsList.value.length === 0) {
      return [];
    }

    // 地区优先级映射
    const regionPriority = {
      '美国': 1,
      '英国': 2,
      '加拿大': 3,
      '澳大利亚': 4,
      '新加坡': 5,
      '中国香港': 6,
      '德国': 7,
      '法国': 8,
      '荷兰': 9,
      '瑞士': 10,
      '日本': 11,
      '韩国': 12,
      '中国': 13,
      '其他': 99
    };

    // 过滤掉无效的项目数据
    const validPrograms = clientProgramsList.value.filter(program =>
      program && typeof program === 'object' && program.id
    );

    return [...validPrograms].sort((a, b) => {
      try {
        // 1. 首先按地区排序
        const regionA = (a.school_region && typeof a.school_region === 'string') ? a.school_region : '其他';
        const regionB = (b.school_region && typeof b.school_region === 'string') ? b.school_region : '其他';
        const priorityA = regionPriority[regionA] || 99;
        const priorityB = regionPriority[regionB] || 99;

        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }

        // 2. 同地区内按QS排名排序（数字越小排名越高）
        const qsA = parseInt(a.school_qs_rank) || 999999;
        const qsB = parseInt(b.school_qs_rank) || 999999;

        if (qsA !== qsB) {
          return qsA - qsB;
        }

        // 3. QS排名相同时按学校中文名排序
        const schoolA = (a.school_name_cn && typeof a.school_name_cn === 'string') ? a.school_name_cn : '';
        const schoolB = (b.school_name_cn && typeof b.school_name_cn === 'string') ? b.school_name_cn : '';

        return schoolA.localeCompare(schoolB, 'zh-CN');
      } catch (sortError) {
        console.warn('排序单个项目时出错:', sortError);
        return 0;
      }
    });
  } catch (error) {
    console.warn('计算排序列表时出错:', error);
    return [];
  }
});

// 从悬浮框中删除定校书项目
const removeFromClientProgramsFloat = async (program) => {
  if (!selectedClient.value || removingFromClientPrograms.value.has(program.program_id)) return;

  try {
    removingFromClientPrograms.value.add(program.program_id);

    await removeClientProgram(selectedClient.value.id_hashed, program.program_id);

    // 移除成功后，重新从服务器获取最新的定校书数据
    await loadClientPrograms();

    // 移除成功，无需提示
  } catch (error) {
    console.error('从定校书中删除项目失败:', error);
    ElMessage.error('删除失败，请重试');
  } finally {
    removingFromClientPrograms.value.delete(program.program_id);
  }
};

// 处理图片加载失败
const handleImageError = (event, school) => {
  // 当图片加载失败时，替换为文字logo
  const colors = [
    { bg: '4F46E5', color: 'FFFFFF' }, // 紫色
    { bg: '059669', color: 'FFFFFF' }, // 绿色
    { bg: 'DC2626', color: 'FFFFFF' }, // 红色
    { bg: '2563EB', color: 'FFFFFF' }, // 蓝色
    { bg: 'F59E0B', color: 'FFFFFF' }, // 橙色
    { bg: '7C2D12', color: 'FFFFFF' }, // 棕色
    { bg: '581C87', color: 'FFFFFF' }, // 深紫色
    { bg: '0F766E', color: 'FFFFFF' }  // 青色
  ];
  
  const schoolNameCn = school.学校中文名;
  const firstChar = schoolNameCn ? schoolNameCn.charAt(0) : '?';
  
  // 根据学校名称生成一致的颜色
  const colorIndex = schoolNameCn ? schoolNameCn.charCodeAt(0) % colors.length : 0;
  const selectedColor = colors[colorIndex];
  
  // 生成SVG文字logo
  const textLogo = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(`
    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
      <rect width="40" height="40" fill="#${selectedColor.bg}" rx="8"/>
      <text x="20" y="26" font-family="system-ui, -apple-system, sans-serif" font-size="16" font-weight="600" text-anchor="middle" fill="#${selectedColor.color}">
        ${firstChar}
      </text>
    </svg>
  `)}`;
  
  event.target.src = textLogo;
};

// 计算下拉框位置
const updateDropdownPosition = () => {
  nextTick(() => {
    const searchInput = document.querySelector('.client-selector .animated-input-container');
    if (searchInput && showClientSelector.value) {
      const rect = searchInput.getBoundingClientRect();
      dropdownStyle.value = {
        position: 'fixed',
        top: `${rect.bottom + 4}px`,
        left: `${rect.left}px`,
        width: `${rect.width}px`,
        minWidth: '320px'
      };
    }
  });
};



/**
 * 获取学校Logo的方法（新版本：优先使用数据库logo_url）
 */
const getSchoolLogo = (school) => {
  // 处理不同的输入格式
  let schoolNameCn;
  let schoolLogoUrl;

  if (typeof school === 'string') {
    // 如果传入的是字符串，直接作为学校名称
    schoolNameCn = school;
    schoolLogoUrl = null;
  } else if (school && typeof school === 'object') {
    // 如果传入的是对象，提取相关字段
    schoolNameCn = school.学校中文名 || school.school_name_cn;
    schoolLogoUrl = school.school_logo_url;
  } else {
    return getSchoolLogoFallback('');
  }

  // 方案1: 优先使用传入对象中的school_logo_url字段（第一优先级）
  if (schoolLogoUrl && schoolLogoUrl.trim() !== '') {
    return schoolLogoUrl;
  }

  // 方案2: 使用全局logo store（第二优先级）
  return logoStore.getSchoolLogo(schoolNameCn);
};

/**
 * 模拟数据
 * 注意: 在实际项目中，这些数据应从API获取
 */

// 模拟数据已删除
const mockRecommendations = [];

// 搜索建议 - 境内院校（使用home-schools接口）
const querySchools = async (queryString, callback) => {
  try {
    // 如果没有输入内容，不显示任何下拉选项
    if (!queryString || !queryString.trim()) {
      callback([]);
      return;
    }
    
    // 学校简称到全称的映射表
    const schoolAliasMap = {
      '北大': '北京大学',
      '清华': '清华大学',
      '南大': '南京大学',
      '复旦': '复旦大学',
      '上交': '上海交通大学',
      '浙大': '浙江大学',
      '中大': '中山大学',
      '华科': '华中科技大学',
      '哈工大': '哈尔滨工业大学',
      '西交': '西安交通大学',
      '同济': '同济大学',
      '北航': '北京航空航天大学',
      '北理工': '北京理工大学',
      '东大': '东南大学',
      '天大': '天津大学',
      '大工': '大连理工大学',
      '西工大': '西北工业大学',
      '中南': '中南大学',
      '华南理工': '华南理工大学',
      '电子科大': '电子科技大学',
      '重大': '重庆大学',
      '兰大': '兰州大学',
      '厦大': '厦门大学',
      '武大': '武汉大学',
      '人大': '中国人民大学',
      '师大': '北京师范大学',
      '农大': '中国农业大学',
      '北外': '北京外国语大学',
      '上外': '上海外国语大学',
      '央财': '中央财经大学',
      '上财': '上海财经大学',
      '对外经贸': '对外经济贸易大学',
      '华东师大': '华东师范大学',
      '华中师大': '华中师范大学',
      '东北师大': '东北师范大学',
      '南开': '南开大学',
      '吉大': '吉林大学',
      '山大': '山东大学',
      '川大': '四川大学',
      '中科大': '中国科学技术大学'
    };
    
    const searchTerm = queryString.trim();
    const searchLower = searchTerm.toLowerCase();
    
    // 检查是否为简称，如果是则获取对应的全称
    const fullName = schoolAliasMap[searchTerm];
    
    // 如果有输入内容，使用搜索关键词查询
    // 如果是简称，同时搜索简称和全称以确保能找到目标学校
    const searchKeyword = fullName || searchTerm;
    const response = await getHomeSchoolList({ search: searchKeyword, limit: 2000 });
    let schools = response.data || response;
    
    // 前端二次筛选和排序：提高匹配精度
    
    // 首先筛选出匹配的学校
    schools = schools.filter(school => {
      const schoolName = school.school_name || '';
      const schoolNameLower = schoolName.toLowerCase();
      
      // 完全匹配简称对应的全称
      if (fullName && schoolName === fullName) return true;
      
      // 模糊匹配
      return schoolNameLower.includes(searchLower);
    });
    

    
    // 然后排序
    schools.sort((a, b) => {
      const schoolName = a.school_name || '';
      const schoolNameLower = schoolName.toLowerCase();
      
      const schoolNameB = b.school_name || '';
      const schoolNameLowerB = schoolNameB.toLowerCase();
      
      // 第一优先级：完全匹配简称对应的全称
      if (fullName) {
        const aExactMatch = schoolName === fullName;
        const bExactMatch = schoolNameB === fullName;
        
        // 备用匹配：如果完全匹配失败，尝试部分匹配
        const aPartialMatch = !aExactMatch && schoolName.includes(fullName.slice(0, 3));
        const bPartialMatch = !bExactMatch && schoolNameB.includes(fullName.slice(0, 3));
        
        // 优先完全匹配
        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;
        
        // 然后是部分匹配
        if (aPartialMatch && !bPartialMatch) return -1;
        if (!aPartialMatch && bPartialMatch) return 1;
        

      }
      
      // 第二优先级：开头匹配原始搜索词
      const aStartsMatch = schoolNameLower.startsWith(searchLower);
      const bStartsMatch = schoolNameLowerB.startsWith(searchLower);
      if (aStartsMatch && !bStartsMatch) return -1;
      if (!aStartsMatch && bStartsMatch) return 1;
      
      // 第三优先级：包含匹配
      const aContainsMatch = schoolNameLower.includes(searchLower);
      const bContainsMatch = schoolNameLowerB.includes(searchLower);
      if (aContainsMatch && !bContainsMatch) return -1;
      if (!aContainsMatch && bContainsMatch) return 1;
      
      // 第四优先级：按软科排名排序
      const rankA = a.ranking_ruanke || 9999;
      const rankB = b.ranking_ruanke || 9999;
      return rankA - rankB;
    });
    
    // 限制搜索结果数量
    schools = schools.slice(0, 15);
    
    // 格式化返回结果
    const results = schools.map(school => ({
      value: school.school_name,
      label: school.school_name,
      ranking: school.ranking_ruanke,
      location: school.location
    }));
    
    callback(results);
  } catch (error) {
    console.error("Error fetching home schools:", error);
    callback([]);
  }
};

/* 
// 原国外院校搜索逻辑（已注释）
const querySchoolsInternational = async (queryString, callback) => {
  try {
    let schools = [];
    
    if (!queryString) {
      // 如果没有输入内容，获取所有学校并按QS排名排序显示热门学校
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/ai-selection/data/schools`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      schools = await response.json();
      
      // 按QS排名排序显示热门学校
      schools = schools
        .filter(school => school.school_qs_rank) // 只显示有排名的学校
        .sort((a, b) => {
          const rankA = parseInt(a.school_qs_rank) || 9999;
          const rankB = parseInt(b.school_qs_rank) || 9999;
          return rankA - rankB;
        })
        .slice(0, 15); // 限制显示15个热门学校
    } else {
      // 如果有输入内容，使用地区搜索 + 前端筛选的策略
      const searchTerm = queryString.trim();
      
      // 使用并行搜索策略：全量获取 + 地区筛选
      const token = localStorage.getItem('token');
      const authHeaders = {
        'Authorization': `Bearer ${token}`
      };

      const searchPromises = [
        // 1. 获取所有学校
        fetch(`/api/ai-selection/data/schools`, { headers: authHeaders })
      ];

      // 2. 对于地区性搜索（如"香港"），也尝试按地区精确搜索
      if (['香港', '北京', '上海', '广州', '深圳', '新加坡', '伦敦'].includes(searchTerm)) {
        searchPromises.push(
          fetch(`/api/ai-selection/data/schools?region=${encodeURIComponent(searchTerm)}`, { headers: authHeaders })
        );
      }
      
      // 执行所有搜索
      const responses = await Promise.all(searchPromises);
      
      // 合并所有搜索结果
      const allSchools = [];
      for (const response of responses) {
        if (response.ok) {
          const data = await response.json();
          allSchools.push(...data);
        }
      }
      
      // 去重：基于school_name_cn
      const uniqueSchools = allSchools.filter((school, index, self) => 
        index === self.findIndex(s => s.school_name_cn === school.school_name_cn)
      );
      
      schools = uniqueSchools;
      
      // 前端筛选：支持中英文模糊匹配
      const searchLower = searchTerm.toLowerCase();
      schools = schools.filter(school => {
        const cnMatch = school.school_name_cn && school.school_name_cn.toLowerCase().includes(searchLower);
        const enMatch = school.school_name_en && school.school_name_en.toLowerCase().includes(searchLower);
        
        // 特殊处理香港学校的英文名称变体
        let hongkongMatch = false;
        if (searchLower.includes('hong') && school.school_name_en) {
          const schoolNameLower = school.school_name_en.toLowerCase();
          hongkongMatch = schoolNameLower.includes('hong kong') || 
                         schoolNameLower.includes('hongkong') ||
                         schoolNameLower.includes('hong');
        }
        
        return cnMatch || enMatch || hongkongMatch;
      });
      
      // 按匹配度排序：优先显示开头匹配的结果
      schools.sort((a, b) => {
        const aStartsCn = a.school_name_cn && a.school_name_cn.toLowerCase().startsWith(searchLower);
        const bStartsCn = b.school_name_cn && b.school_name_cn.toLowerCase().startsWith(searchLower);
        const aStartsEn = a.school_name_en && a.school_name_en.toLowerCase().startsWith(searchLower);
        const bStartsEn = b.school_name_en && b.school_name_en.toLowerCase().startsWith(searchLower);
        
        // 优先级：中文开头匹配 > 英文开头匹配 > 中文包含匹配 > 英文包含匹配
        if (aStartsCn && !bStartsCn) return -1;
        if (!aStartsCn && bStartsCn) return 1;
        if (aStartsEn && !bStartsEn) return -1;
        if (!aStartsEn && bStartsEn) return 1;
        
        // 如果都是包含匹配，则按QS排名排序
        const rankA = parseInt(a.school_qs_rank) || 9999;
        const rankB = parseInt(b.school_qs_rank) || 9999;
        return rankA - rankB;
      });
    }
    
    // 格式化返回结果
    const results = schools.map(school => ({
      value: school.school_name_cn,
      label: school.school_name_cn,
      secondaryText: school.school_name_en,
      ranking: school.school_qs_rank
    }));
    
    callback(results);
  } catch (error) {
    console.error("Error fetching schools:", error);
    callback([]);
  }
};
*/



/**
 * 方法定义
 */

// SSE相关状态
const sseConnection = ref(null);
const progressLogs = ref([]);
const currentPreviews = ref([]);
const streamingStatus = ref(''); // 'connecting', 'streaming', 'completed', 'error'
const totalElapsed = ref(0);
const currentStage = ref('');

// 进度条相关状态
const recommendationProgress = ref(0);
const progressMessage = ref('');

// 获取匹配结果的方法 (使用SSE流式接口)
const getRecommendations = async () => {
  isLoading.value = true;
  streamingStatus.value = 'connecting';
  progressLogs.value = [];
  currentPreviews.value = [];
  recommendations.value = [];
  schoolGroups.value = []; // 清空分组数据
  currentResultMode.value = ''; // 重置结果模式
  totalElapsed.value = 0;
  currentStage.value = '';
  recommendationProgress.value = 0; // 重置进度条
  progressMessage.value = '正在连接匹配系统';
  
  // 构建符合API要求的请求体
  const requestData = {
    academic: {
      education: profileForm.academic.education,
      school: profileForm.academic.school,
      major: profileForm.academic.major,
      gpa: profileForm.academic.gpa || "0",  // 保持字符串格式
      gpaScale: profileForm.academic.gpaScale,  // 使用camelCase匹配后端
      ranking: profileForm.academic.ranking || null
    },
    intention: {
      countries: enableAiSelection.value 
        ? (typeof profileForm.intention.countries === 'string' ? [profileForm.intention.countries] : [])
        : (Array.isArray(profileForm.intention.countries) ? profileForm.intention.countries : []),
      majors: Array.isArray(profileForm.intention.majors) ? profileForm.intention.majors : [],
      duration: profileForm.intention.duration || ""  // 传递用户选择的学制时长
    },
    strength: {
      competition: Array.isArray(profileForm.strength.competition) ? profileForm.strength.competition : [],
      internship: Array.isArray(profileForm.strength.internship) ? profileForm.strength.internship : [],
      research: Array.isArray(profileForm.strength.research) ? profileForm.strength.research : [],
      rankingPreference: Array.isArray(profileForm.strength.rankingPreference) ? profileForm.strength.rankingPreference : [],
      preference_type: profileForm.strength.preferenceType || "balanced",  // 如果用户未选择则使用默认值平衡型
      enable_ai_selection: enableAiSelection.value
    }
  };

  // 清理空值和无效数据
  Object.keys(requestData.academic).forEach(key => {
    if (requestData.academic[key] === '' || requestData.academic[key] === null) {
      delete requestData.academic[key];
    }
  });

  // 建立SSE连接
  const apiUrl = '/api/ai-selection/recommendation/recommend/stream';
  
  try {
    
    // 获取认证token
    const token = localStorage.getItem('token');

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API错误响应:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    
    streamingStatus.value = 'streaming';
    recommendationProgress.value = 8; // 连接成功，进度8%
    progressMessage.value = '已连接匹配服务，开始分析';
    
    // 处理流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';

    const processBuffer = async () => {
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // 保留最后一个可能不完整的行
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('data: ')) {
          const dataStr = trimmedLine.slice(6).trim();
          if (dataStr && dataStr !== '[DONE]') {
            try {
              const eventData = JSON.parse(dataStr);
              await handleSSEEvent(eventData);
            } catch (e) {
              console.warn('解析SSE事件失败:', e, dataStr);
            }
          }
        }
      }
    };

    const readStream = async () => {
      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
              await processBuffer();
            }
            break;
          }
          
          buffer += decoder.decode(value, { stream: true });
          await processBuffer();
        }
      } catch (streamError) {
        console.error('流读取错误:', streamError);
        throw streamError;
      }
    };
    
    await readStream();
    
  } catch (error) {
    console.error('SSE连接失败:', error);
    streamingStatus.value = 'error';
    isLoading.value = false;
    recommendationProgress.value = 0;
          progressMessage.value = '匹配失败';
    
    let errorMessage = '匹配服务连接失败，请稍后重试';
    
    if (error.message.includes('HTTP 404')) {
      errorMessage = 'API端点不存在，请检查后端服务是否正确启动';
    } else if (error.message.includes('HTTP 422')) {
      errorMessage = '请求参数格式错误，请检查表单数据';
    } else if (error.message.includes('HTTP 4')) {
      errorMessage = '请求参数有误，请检查填写的信息';
    } else if (error.message.includes('HTTP 5')) {
      errorMessage = '服务器内部错误，请稍后重试';
    } else if (error.name === 'TypeError' || error.message.includes('Failed to fetch')) {
      errorMessage = '无法连接到后端服务，请确认后端是否启动';
    }
    
    console.error('详细错误信息:', error);
    
    ElMessage.error(errorMessage);
  } finally {
    if (streamingStatus.value === 'streaming') {
      streamingStatus.value = 'completed';
      isLoading.value = false;
      hasSubmitted.value = true;
    }
  }
};

// 处理SSE事件
const handleSSEEvent = async (eventData) => {
  const { type, message, elapsed, stage, preview_schools, recommendation, rank, total_elapsed, error } = eventData;
  
  switch (type) {
    case 'mode_selection':
      // 记录实际使用的推荐模式
      currentResultMode.value = eventData.mode === 'hard_filter' ? 'hard_filter' : 'ai';
      currentStage.value = eventData.mode === 'hard_filter' ? '精准匹配模式' : '智能推荐模式';
      progressMessage.value = eventData.message;
      recommendationProgress.value = 10;
      break;

    case 'start':
      recommendationProgress.value = 15;
      progressMessage.value = getProgressMessage('start');
      currentStage.value = '启动匹配引擎';
      break;
      
    case 'progress':
      const progressMap = {
        'profile': 25, 'matching': 45, 'scoring': 65, 'ranking': 80, 'reasoning': 92,
        'filtering': 35, 'analyzing': 20, 'preprocessing': 10, 'calculating': 70, 'finalizing': 95,
        'hard_filter_start': 15, 'filter_criteria': 25, 'hard_filter_complete': 90,
        // 中文stage映射
        '动态范围确定': 20,
        '开始匹配评估': 30,
        '匹配进度更新': 50,
        '实时预览': 60,
        '排名计算': 70,
        '匹配评估完成': 80,
        '候选池确定': 85,
        '排名完成': 88,
        '生成推荐理由': 90,
        '推荐理由已生成': 95,
        '推荐理由生成失败': 50,
        '结果批次': 60,
        '完成': 100
      };
      if (stage && progressMap[stage]) recommendationProgress.value = progressMap[stage];
      if (stage) {
        currentStage.value = getStageDisplayName(stage);
        progressMessage.value = getProgressMessage(stage, eventData);
      }
      break;
      
    case 'recommendation_batch_start':
      recommendationProgress.value = 88;
      progressMessage.value = getProgressMessage('recommendation_batch_start');
      currentStage.value = '撰写专业分析';
      break;
      
    case 'recommendation_complete':
      if (recommendation && typeof rank === 'number') {
        handleRecommendationWithProgramQuery(recommendation, rank);
      }
      break;
      
    case 'recommendation_error':
      console.warn(`第${rank}个推荐生成失败: ${error || '未知错误'}`);
      break;
    
    case 'matching_start':
      recommendationProgress.value = 40;
      progressMessage.value = getProgressMessage('matching_start', eventData);
      currentStage.value = '深度评估专业';
      break;
      
    case 'matching_progress':
      const processed = eventData.processed || 0;
      const total = eventData.total || 20;
      const progressCalc = total > 0 ? (processed / total) * 40 : 0;
      recommendationProgress.value = Math.min(82, 40 + progressCalc);
      progressMessage.value = getProgressMessage('matching_progress', eventData);
      break;
      
    case 'ranking_start':
      recommendationProgress.value = 82;
      progressMessage.value = getProgressMessage('ranking_start');
      currentStage.value = '生成专属排名';
      break;
      
    case 'hard_filter_start':
      recommendationProgress.value = 15;
      progressMessage.value = '开始精准匹配';
      currentStage.value = '条件筛选中';
      currentResultMode.value = 'hard_filter'; // 提前设置模式，确保监听器能正确工作
      break;

    case 'filter_criteria':
      recommendationProgress.value = 25;
      progressMessage.value = '应用筛选条件';
      currentStage.value = '条件匹配中';
      break;

    case 'hard_filter_batch':
              // 处理批量专业库匹配结果
      if (eventData.results && Array.isArray(eventData.results)) {
        
        // 优化：检查专业库匹配结果是否已包含完整信息，减少API调用
        const enrichResults = await Promise.all(
          eventData.results.map(async (result) => {
            try {
              // 检查专业库匹配结果是否已包含足够的展示信息
              const hasRichData = result.school_name_cn &&
                                 result.program_name_cn &&
                                 result.program_category &&
                                 result.program_duration &&
                                 result.program_tuition;

              console.log(`🔍 专业库匹配结果 #${result.rank} 数据完整性:`, {
                school_name_cn: !!result.school_name_cn,
                program_name_cn: !!result.program_name_cn,
                program_category: !!result.program_category,
                program_duration: !!result.program_duration,
                program_tuition: !!result.program_tuition,
                hasRichData: hasRichData
              });

              let programDetail;

              if (hasRichData) {
                // 使用专业库匹配返回的丰富数据，避免额外API调用
                console.log(`✅ 使用专业库数据构建结果 #${result.rank}，无需额外API调用`);
                programDetail = {
                  school_name_cn: result.school_name_cn,
                  school_name_en: result.school_name_en,
                  program_name_cn: result.program_name_cn,
                  program_name_en: result.program_name_en,
                  program_category: result.program_category,
                  program_direction: result.program_direction,
                  faculty: result.faculty,
                  degree: result.degree,
                  program_duration: result.program_duration,
                  program_tuition: result.program_tuition,
                  enrollment_time: result.enrollment_time,
                  program_website: result.program_website,
                  gpa_requirements: result.gpa_requirements,
                  language_requirements: result.language_requirements,
                  application_requirements: result.application_requirements,
                  school_qs_rank: result.school_qs_rank,
                  school_region: result.school_region || result.region_name
                };
              } else {
                // 降级到API调用获取完整信息
                console.log(`🔄 专业库数据不完整，调用API获取结果 #${result.rank} 的详细信息`);
                const token = localStorage.getItem('token');
                const response = await fetch(`/api/ai-selection/data/programs/${result.program_id}`, {
                  method: 'GET',
                  headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache',
                    'Authorization': `Bearer ${token}`
                  }
                });

                if (!response.ok) {
                  throw new Error(`获取专业详情失败: ${response.status}`);
                }

                programDetail = await response.json();
              }

              // 学校logo在后续统一批量预加载，此处不做单项请求
              let schoolLogoUrl = '';

                              // 使用数据库完整信息替换专业库匹配的不完整数据
              return {
                id: result.rank,
                rank: result.rank,
                program_id: result.program_id,

                // 学校信息（使用数据库完整数据）
                学校中文名: programDetail.school_name_cn,
                学校英文名: programDetail.school_name_en,

                // 专业信息（使用数据库完整数据）
                专业中文名: programDetail.program_name_cn,
                专业英文名: programDetail.program_name_en,
                专业大类: programDetail.program_category,
                专业方向: programDetail.program_direction,
                所在学院: programDetail.faculty,

                // 申请和学制信息（使用数据库完整数据）
                入学时间: programDetail.enrollment_time,
                项目时长: programDetail.program_duration,
                项目官网: programDetail.program_website,

                // 费用和要求信息（使用数据库完整数据）
                tuitionRange: programDetail.program_tuition,
                申请要求: programDetail.application_requirements,
                GPA要求: programDetail.gpa_requirements,
                语言要求: programDetail.language_requirements,
                课程设置: programDetail.courses,
                培养目标: cleanProgramObjectives(programDetail.program_objectives),
                其他费用: programDetail.other_cost,
                学位认证: programDetail.degree_evaluation,

                // 地理和排名信息（使用数据库完整数据）
                location: programDetail.school_region,
                ranking: programDetail.school_qs_rank ?
                         `QS排名 #${programDetail.school_qs_rank}` :
                         '排名信息暂无',

                // Logo URL（统一批量预加载填充）
                school_logo_url: schoolLogoUrl,
                
                // 专业库匹配特有信息
                matchSource: result.match_source || 'database_match',
                
                // 默认UI状态
                showDetails: false,
                isFavorite: false,
                detailsHeight: 0,
                detailsLoaded: false,
                originalHeight: 0,
                
                // 专业库匹配模式下的推荐信息
                reason: '基于您的留学意向进行专业库精确匹配',
                highlights: [],
                deadline: programDetail.application_time || '申请截止时间请查看官网'
              };
            } catch (error) {
              console.error(`获取program_id: ${result.program_id} 详情失败:`, error);
              
              // 获取失败时使用原始数据，但标记为数据不完整
              return {
                id: result.rank,
                rank: result.rank,
                program_id: result.program_id,
                
                // 学校信息
                学校中文名: result.school_name_cn || '学校信息获取失败',
                学校英文名: result.school_name_en || '',
                
                // 专业信息
                专业中文名: result.program_name_cn || '专业信息获取失败',
                专业英文名: result.program_name_en || '',
                专业大类: result.program_category || '信息获取失败',
                专业方向: result.program_direction || '信息获取失败',
                所在学院: result.faculty || '信息获取失败',
                
                // 基本信息
                location: result.school_region || '地区未知',
                ranking: result.school_qs_rank ? 
                         `QS排名 #${result.school_qs_rank}` : 
                         '排名信息获取失败',
                tuitionRange: result.program_tuition || '费用信息获取失败',
                
                // Logo URL（失败时为空）
                school_logo_url: result.school_logo_url || '',
                
                // 专业库匹配特有信息
                matchSource: result.match_source || 'database_match',
                
                // 默认UI状态
                showDetails: false,
                isFavorite: false,
                detailsHeight: 0,
                detailsLoaded: false,
                originalHeight: 0,
                
                // 默认信息
                reason: '基于您的留学意向进行专业库精确匹配（详细信息获取失败）',
                highlights: [],
                deadline: '申请截止时间获取失败',
                申请要求: '要求信息获取失败',
                语言要求: '语言要求获取失败',
                课程设置: '课程信息获取失败',
                培养目标: '培养目标获取失败'
              };
            }
          })
        );
        
        // 添加到推荐列表
        recommendations.value.push(...enrichResults);
        
        // 更新进度
        const batchNumber = eventData.batch_number || 0;
        const totalBatches = eventData.total_batches || 1;
        const progressIncrement = totalBatches > 0 ? (batchNumber / totalBatches) * 60 : 0;
        recommendationProgress.value = Math.min(90, 25 + progressIncrement);
        progressMessage.value = '正在整理匹配结果';
      }
      break;

    case 'hard_filter_complete':
      recommendationProgress.value = 100;
      progressMessage.value = '精准匹配完成';
      currentStage.value = '匹配完成';
      streamingStatus.value = 'completed';
      isLoading.value = false;
      hasSubmitted.value = true;
      currentResultMode.value = 'hard_filter'; // 确保标记为专业库匹配模式
      
      // 专业库匹配模式下进行学校分组
      groupRecommendationsBySchool();
      
      // 预加载所有学校的Logo
      if (recommendations.value.length > 0) {
        logoStore.preloadSchoolLogos(recommendations.value);
      }
      
      // 额外检查，如果分组失败，使用备用逻辑
      if (schoolGroups.value.length === 0 && recommendations.value.length > 0) {
        setTimeout(() => {
          groupRecommendationsBySchool();
        }, 100);
      }
      
      // 匹配完成，无需提示
      break;
      
    case 'candidates':
      recommendationProgress.value = 30;
      progressMessage.value = getProgressMessage('candidates', eventData);
      currentStage.value = '智能筛选专业';
      break;
      
    case 'school_matching':
      recommendationProgress.value = 50;
      progressMessage.value = getProgressMessage('school_matching', eventData);
      currentStage.value = '院校适配分析';
      break;
      
    case 'complete':
      totalElapsed.value = total_elapsed || 0;
      currentStage.value = '匹配完成';
      streamingStatus.value = 'completed';
      isLoading.value = false;
      hasSubmitted.value = true;
      recommendationProgress.value = 100;
      progressMessage.value = getProgressMessage('complete');
      currentPreviews.value = [];
      
      // 只有在当前不是专业库匹配模式时，才设置为AI模式
      // 防止覆盖已经设置的专业库匹配模式
      if (currentResultMode.value !== 'hard_filter') {
        currentResultMode.value = 'ai';
      }
      
      // 预加载所有学校的Logo
      if (recommendations.value.length > 0) {
        logoStore.preloadSchoolLogos(recommendations.value);
      }
      break;
      
    case 'error':
      streamingStatus.value = 'error';
      isLoading.value = false;
      recommendationProgress.value = 0;
      progressMessage.value = '匹配服务异常';
      console.error('后端匹配错误:', message, eventData);
      ElMessage.error(message || '匹配过程中出现错误，请稍后重试');
      break;
      
    default:
      console.warn('收到未知SSE事件类型:', type, eventData);
  }
};

// 获取阶段显示名称
const getStageDisplayName = (stage) => {
  // 如果已经是中文，直接返回
  if (/[\u4e00-\u9fa5]/.test(stage)) {
    return stage;
  }

  const stageMap = {
    'profile': '学术背景分析',
    'matching': '院校匹配',
    'scoring': '适配度计算',
    'ranking': '排名生成',
    'reasoning': '推荐分析',
    'filtering': '结果筛选',
    'analyzing': '条件评估',
    'preprocessing': '信息处理',
    'calculating': '算法执行',
    'finalizing': '方案完善',
    'candidates': '筛选候选专业',
    'school_matching': '院校匹配',
    'matching_start': '开始评估专业项目',
    'matching_progress': '评估专业项目中',
    'ranking_start': '生成匹配排名',
    'recommendation_batch_start': '生成详细推荐报告',
    'complete': '匹配完成',
    'recommendation_complete': '完善推荐详情',
    'recommendation_error': '重新处理推荐项',
    'hard_filter_start': '启动精准匹配',
    'filter_criteria': '应用筛选条件',
    'hard_filter_complete': '精准匹配完成'
  };
  return stageMap[stage] || `${stage}处理`;
};

// 获取专业化的进度消息
const getProgressMessage = (stage, eventData = {}) => {
  // 如果已经是中文stage，生成对应的进度消息
  const chineseStageMessages = {
    '动态范围确定': '确定推荐范围中',
    '开始匹配评估': '开始评估专业项目',
    '匹配进度更新': '评估专业项目中',
    '实时预览': '生成实时预览',
    '排名计算': '计算院校排名',
    '匹配评估完成': '专业匹配评估完成',
    '候选池确定': '确定候选专业池',
    '排名完成': '排名生成完成',
    '生成推荐理由': '生成详细推荐理由',
    '推荐理由已生成': '推荐理由生成完成',
    '推荐理由生成失败': '重新处理推荐项',
    '结果批次': '处理匹配结果',
    '完成': '匹配完成'
  };

  if (chineseStageMessages[stage]) {
    return chineseStageMessages[stage];
  }

  const messageMap = {
    'start': '初始化匹配系统',
    'profile': '分析学术背景',
    'matching': '检索院校数据库',
    'scoring': '计算匹配度',
    'ranking': '生成匹配排名',
    'reasoning': '生成匹配理由',
    'filtering': '筛选匹配结果',
    'analyzing': '评估申请条件',
    'preprocessing': '处理申请信息',
    'calculating': '执行匹配算法',
    'finalizing': '完成推荐方案',
    'candidates': '筛选候选专业',
    'school_matching': '匹配院校数据',
    'matching_start': '开始评估专业项目',
    'matching_progress': '评估专业项目中',
    'ranking_start': '生成匹配排名',
    'recommendation_batch_start': '生成详细推荐报告',
    'complete': '匹配完成',
    'recommendation_complete': '完善推荐详情',
    'recommendation_error': '重新处理推荐项',
    'hard_filter_start': '启动精准匹配',
    'filter_criteria': '应用筛选条件',
    'hard_filter_complete': '精准匹配完成'
  };
  return messageMap[stage] || `正在${getStageDisplayName(stage)}`;
};



// 表单验证逻辑
const validateForm = () => {
  formValidation.hasValidated = true;
  let isValid = true;
  
  // 验证学术背景必填项
  const academic = profileForm.academic;
  formValidation.errors.academic.school = !academic.school;
  formValidation.errors.academic.major = !academic.major;
  formValidation.errors.academic.gpa = !academic.gpa;
  
  // 特别验证GPA范围（固定为百分制）
  if (academic.gpa) {
    const gpaValidation = validateGpaValue(academic.gpa, '100');
    gpaValidationError.value = !gpaValidation.isValid;
    gpaErrorMessage.value = gpaValidation.message;
    if (!gpaValidation.isValid) {
      isValid = false;
    }
  }
  
  // 验证留学意向必填项
  const intention = profileForm.intention;
  formValidation.errors.intention.countries = !intention.countries.length;
  formValidation.errors.intention.majors = !intention.majors.length;
  
  // 检查是否有任何错误
  const academicErrors = Object.values(formValidation.errors.academic);
  const intentionErrors = Object.values(formValidation.errors.intention);
  const hasBasicErrors = academicErrors.some(error => error) || intentionErrors.some(error => error);
  const hasGpaError = gpaValidationError.value;
  
  isValid = !hasBasicErrors && !hasGpaError;
  
  return isValid;
};

// 获取错误字段数量
const getErrorCount = (section) => {
  return Object.values(formValidation.errors[section]).filter(error => error).length;
};

// 验证客户选择
const validateClientSelection = () => {
  // 如果没有选择客户，且输入框有内容，显示错误
  if (!selectedClient.value && clientSearchQuery.value && clientSearchQuery.value.trim()) {
    showClientValidationError('必须点击"创建快速定校书"选项才能创建客户档案，直接输入无效');
    return false;
  }
  
  // 如果有输入但没有有效选择
  if (clientSearchQuery.value && clientSearchQuery.value.trim() && !hasValidClientSelection.value) {
    showClientValidationError('请选择现有客户或点击快速建档选项');
    return false;
  }
  
  return true;
};

// 提交表单
const handleSubmit = async () => {
  // 首先验证客户选择
  if (!validateClientSelection()) {
    return;
  }
  
  if (!validateForm()) {
    const academicErrorCount = getErrorCount('academic');
    const intentionErrorCount = getErrorCount('intention');
    const hasGpaError = gpaValidationError.value;

    // 根据错误类型提供不同的提示信息
    let errorMessage = '请完善必填信息后再开始匹配';
    if (hasGpaError) {
      errorMessage = '成绩超出正常范围，请检查后重试';
    } else if (academicErrorCount > 0 || intentionErrorCount > 0) {
      errorMessage = '请完善必填信息后再开始匹配';
    }

    ElMessage.error({
      message: errorMessage,
      duration: 3000,
      showClose: true
    });

    // 自动展开有错误的区域
    if (academicErrorCount > 0 || hasGpaError) {
      sectionStates.academic = true;
    }
    if (intentionErrorCount > 0) {
      sectionStates.intention = true;
    }

    return;
  }

  // 智能选校预验证（仅在启用智能推荐时进行）
  if (enableAiSelection.value) {
    try {
      const preValidationResult = await AISelectionValidationService.preValidate();

      if (!preValidationResult.canProceed) {
        // 显示验证失败的提示
        const suggestion = AISelectionValidationService.getActionSuggestion(preValidationResult.reason);

        ElMessage.error({
          message: suggestion.message,
          duration: 5000,
          showClose: true
        });

        // 如果有操作建议，可以考虑显示操作按钮
        if (suggestion.actionUrl) {
          console.log('建议操作:', suggestion.actionText, suggestion.actionUrl);
        }

        return;
      }
    } catch (error) {
      console.error('预验证失败:', error);
      ElMessage.error({
        message: '验证服务异常，请稍后重试',
        duration: 3000,
        showClose: true
      });
      return;
    }
  }

  // 如果选中的是快速建档客户，更新学术背景信息
  if (selectedClient.value && !selectedClient.value.isTemporary && selectedClient.value.profile_type === false) {
    try {
      const quickProfileData = {
        name: selectedClient.value.name,
        school: profileForm.academic.school || '',
        major: profileForm.academic.major || '',
        gpa: profileForm.academic.gpa || '',
        gpa_scale: profileForm.academic.gpaScale || '100',
        service_type: profileForm.academic.education || 'master'
      };

      console.log('更新快速建档学术背景信息:', quickProfileData);

      // 调用快速建档API更新信息
      const response = await createQuickProfile(quickProfileData);

      if (response && response.id_hashed) {
        console.log('快速建档学术背景更新成功:', response);
      }
    } catch (error) {
      console.error('更新快速建档学术背景失败:', error);
      // 不阻止匹配流程，只是记录错误
    }
  }

  // 如果选中的是临时客户（备选方案），保存快速建档信息
  if (selectedClient.value && selectedClient.value.isTemporary) {
    try {
      const quickProfileData = {
        name: selectedClient.value.name,
        school: profileForm.academic.school || '',
        major: profileForm.academic.major || '',
        gpa: profileForm.academic.gpa || '',
        gpa_scale: profileForm.academic.gpaScale || '100',
        service_type: profileForm.academic.education || 'master'
      };

      console.log('保存临时客户的快速建档信息:', quickProfileData);

      // 调用快速建档API
      const response = await createQuickProfile(quickProfileData);

      if (response && response.id_hashed) {
        // 更新客户信息为正式的数据库记录
        selectedClient.value = {
          ...selectedClient.value,
          id_hashed: response.id_hashed,
          profile_type: response.profile_type,
          isTemporary: false // 标记为已保存到数据库
        };

        // 更新缓存
        try {
          sessionStorage.setItem('quick_client_info', JSON.stringify(selectedClient.value));
        } catch (error) {
          console.error('更新客户缓存失败:', error);
        }

        console.log('临时客户快速建档保存成功:', response);
      }
    } catch (error) {
      console.error('保存临时客户快速建档信息失败:', error);
      // 不阻止匹配流程，只是记录错误
    }
  }

  getRecommendations();
};

// 收藏/取消收藏学校
const toggleFavorite = (school) => {
  school.isFavorite = !school.isFavorite;
  // 收藏操作成功，无需提示
};

// 添加logo加载错误处理方法
const handleLogoError = (event, school) => {
  // 如果logo加载失败，显示学校名称首字母
  event.target.style.display = 'none';
  const container = event.target.parentElement;
  if (container) {
    const fallbackText = document.createElement('span');
    fallbackText.className = 'text-2xl font-bold text-gray-400';
    // 处理不同的数据格式
    const schoolName = school?.学校中文名 || school?.school_name_cn || school?.name || 'U';
    fallbackText.textContent = schoolName.charAt(0);
    container.appendChild(fallbackText);
  }
};

// 动画处理方法
const handleEnter = (el, done) => {
  const height = el.scrollHeight;
  el.style.height = '0px';
  el.style.opacity = '0';
  
  // 触发重绘
  el.offsetHeight;
  
  // 设置过渡属性
  el.style.transition = 'height 0.7s ease, opacity 0.7s ease';
  el.style.height = `${height}px`;
  el.style.opacity = '1';
  
  el.addEventListener('transitionend', function onEnd() {
    el.style.height = 'auto';
    el.removeEventListener('transitionend', onEnd);
    done();
  });
};

const handleLeave = (el, done) => {
  const height = el.scrollHeight;
  el.style.height = `${height}px`;
  
  // 触发重绘
  el.offsetHeight;
  
  // 设置过渡属性
  el.style.transition = 'height 0.7s ease, opacity 0.7s ease';
  el.style.height = '0px';
  el.style.opacity = '0';
  
  el.addEventListener('transitionend', function onEnd() {
    el.style.height = '0px';
    el.removeEventListener('transitionend', onEnd);
    done();
  });
};

// 切换详情显示的方法
const toggleDetails = (school) => {
  if (!school.detailsLoaded) {
    // 第一次展开时计算高度
    school.detailsHeight = 250; // 初始估计高度，可以稍大一些以避免初次闪烁
    school.detailsLoaded = true;
    school.showDetails = true;
    
    // 在下一个渲染周期计算实际高度
    setTimeout(() => {
      const detailsEls = document.querySelectorAll('.details-content');
      for (const el of detailsEls) {
        if (el.closest(`[data-school-id="${school.id}"]`)) {
          school.originalHeight = el.scrollHeight; // 使用 scrollHeight 获取完整内容高度
          school.detailsHeight = school.originalHeight;
          break;
        }
      }
    }, 150); // 增加延迟确保内容渲染完毕
  } else {
    // 已经加载过，直接切换状态
    school.showDetails = !school.showDetails;
    school.detailsHeight = school.showDetails ? school.originalHeight : 0;
  }
};

// 初始化学校数据
const initRecommendations = (schools) => {
  return schools.map(school => {
    return {
      ...school,
      detailsHeight: 0,
      detailsLoaded: false,
      originalHeight: 0, // 添加原始高度字段
      showDetails: false
    };
  });
};

// 处理推荐数据，优先使用SSE返回的丰富信息，减少API调用
const handleRecommendationWithProgramQuery = async (recommendation, rank) => {
  try {
    // 检查SSE返回的数据是否已包含足够的展示信息
    const hasRichData = recommendation.school_name_cn &&
                       recommendation.program_name_cn &&
                       recommendation.program_category &&
                       recommendation.program_duration &&
                       recommendation.program_tuition;

    console.log('🔍 检查SSE数据完整性:', {
      school_name_cn: !!recommendation.school_name_cn,
      program_name_cn: !!recommendation.program_name_cn,
      program_category: !!recommendation.program_category,
      program_duration: !!recommendation.program_duration,
      program_tuition: !!recommendation.program_tuition,
      hasRichData: hasRichData
    });

    console.log('📋 完整SSE推荐数据:', recommendation);

    let programDetail;

    if (hasRichData) {
      // 使用SSE返回的丰富数据，避免额外API调用
      console.log(`✅ 使用SSE数据构建推荐 #${rank}，无需额外API调用`);
      programDetail = {
        school_name_cn: recommendation.school_name_cn,
        school_name_en: recommendation.school_name_en,
        program_name_cn: recommendation.program_name_cn,
        program_name_en: recommendation.program_name_en,
        program_category: recommendation.program_category,
        program_direction: recommendation.program_direction,
        faculty: recommendation.faculty,
        degree: recommendation.degree,
        program_duration: recommendation.program_duration,
        program_tuition: recommendation.program_tuition,
        enrollment_time: recommendation.enrollment_time,
        program_website: recommendation.program_website,
        gpa_requirements: recommendation.gpa_requirements,
        language_requirements: recommendation.language_requirements,
        application_requirements: recommendation.application_requirements,
        school_qs_rank: recommendation.school_qs_rank,
        school_region: recommendation.region_name
      };
    } else {
      // 降级到API调用获取完整信息
      console.log(`🔄 SSE数据不完整，调用API获取推荐 #${rank} 的详细信息`);
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/ai-selection/data/programs/${recommendation.program_id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`获取专业详情失败: ${response.status}`);
      }

      programDetail = await response.json();
    }

    // 学校Logo由批量预加载统一处理，这里不触发单项请求
    let schoolLogoUrl = '';

    // 构建匹配数据，组合推荐结果和专业详情
    const formattedRecommendation = {
      id: rank,
      rank: rank,
      program_id: recommendation.program_id,

      // 学校基本信息（来自专业详情）
      学校中文名: programDetail.school_name_cn,
      学校英文名: programDetail.school_name_en,

      // 专业信息（来自专业详情）
      专业中文名: programDetail.program_name_cn,
      专业英文名: programDetail.program_name_en,
      专业大类: programDetail.program_category,
      专业方向: programDetail.program_direction,
      所在学院: programDetail.faculty,

      // 申请和学制信息（来自专业详情）
      入学时间: programDetail.enrollment_time,
      项目时长: programDetail.program_duration,
      项目官网: programDetail.program_website,

      // 费用和要求信息（来自专业详情）
      tuitionRange: programDetail.program_tuition,
      申请要求: programDetail.application_requirements,
      GPA要求: programDetail.gpa_requirements,
      语言要求: programDetail.language_requirements,
      课程设置: programDetail.courses,
      培养目标: cleanProgramObjectives(programDetail.program_objectives),
      其他费用: programDetail.other_cost,
      学位认证: programDetail.degree_evaluation,

      // 地理和排名信息（来自专业详情）
      location: programDetail.school_region,
      ranking: programDetail.school_qs_rank ?
               `QS排名 #${programDetail.school_qs_rank}` :
               '排名信息暂无',

      // Logo URL（从学校数据库获取）
      school_logo_url: schoolLogoUrl,
      
      // 推荐相关信息（来自推荐结果）
      matchScore: Math.round((recommendation.scores?.total_match || 0.8) * 100),
      reason: '基于您的背景和意向进行智能匹配', // 移除推荐理由生成，使用通用描述
      school_tier: recommendation.school_tier,
      matching_cases_count: recommendation.matching_cases_count || 0,
      
      // 院校特色
      highlights: [`${programDetail.program_direction}专业`],
      deadline: programDetail.application_time || '申请截止待查',
      
      // UI状态
      showDetails: false,
      isFavorite: false,
      detailsHeight: 0,
      detailsLoaded: false,
      originalHeight: 0
    };
    
    // 按排名插入到正确位置
    const existingIndex = recommendations.value.findIndex(r => r.rank === rank);
    if (existingIndex >= 0) {
      // 更新现有推荐
      recommendations.value[existingIndex] = formattedRecommendation;
    } else {
      // 添加新推荐并排序
      recommendations.value.push(formattedRecommendation);
      recommendations.value.sort((a, b) => a.rank - b.rank);
    }
    
    // 更新进度消息
    const completedCount = recommendations.value.length;
    const expectedTotal = 10; // 预期推荐数量
    const progressCalc = expectedTotal > 0 ? (completedCount / expectedTotal) * 9 : 0;
    recommendationProgress.value = Math.min(97, 88 + progressCalc);
    progressMessage.value = '生成匹配方案';
    
  } catch (error) {
    console.error(`查询program_id: ${recommendation.program_id} 失败:`, error);
    
    // 查询失败时使用推荐数据本身的基本信息
    const fallbackRecommendation = {
      id: rank,
      rank: rank,
      program_id: recommendation.program_id,
      
      // 使用推荐数据中的基本信息
      学校中文名: recommendation.school_name || '学校信息获取失败',
      学校英文名: recommendation.school_name || '学校信息获取失败',
      专业中文名: recommendation.program_name_cn || '专业信息获取失败',
      专业英文名: recommendation.program_name_cn || '专业信息获取失败',
      专业大类: recommendation.program_direction || '信息获取失败',
      专业方向: recommendation.program_direction || '信息获取失败',
      所在学院: '信息获取失败',
      
      // 基本信息
      location: recommendation.region_name || '地区未知',
      ranking: '排名信息获取失败',
      matchScore: Math.round((recommendation.scores?.total_match || 0.8) * 100),
      reason: '基于您的背景和意向进行智能匹配', // 移除推荐理由生成，使用通用描述
      school_tier: recommendation.school_tier || 'Tier 1',
      matching_cases_count: recommendation.matching_cases_count || 0,
      
      // Logo URL（失败时为空）
      school_logo_url: '',
      
      // 默认信息
      highlights: ['专业信息获取中'],
      tuitionRange: '费用信息获取失败',
      deadline: '截止时间获取失败',
      申请要求: '要求信息获取失败',
      语言要求: '语言要求获取失败',
      
      // UI状态
      showDetails: false,
      isFavorite: false,
      detailsHeight: 0,
      detailsLoaded: false,
      originalHeight: 0
    };

    // 添加失败的推荐（显示错误信息）
    const existingIndex = recommendations.value.findIndex(r => r.rank === rank);
    if (existingIndex >= 0) {
      recommendations.value[existingIndex] = fallbackRecommendation;
    } else {
      recommendations.value.push(fallbackRecommendation);
      recommendations.value.sort((a, b) => a.rank - b.rank);
    }
  }
};

// 清理SSE连接
const cleanupSSEConnection = () => {
  if (sseConnection.value) {
    try {
      sseConnection.value.close();
    } catch (e) {
      console.warn('关闭SSE连接时出错:', e);
    }
    sseConnection.value = null;
  }
  streamingStatus.value = '';
  isLoading.value = false;
};

// 套餐验证功能
const checkPackageAccess = async () => {
  try {
    validationState.isValidating = true;

    // 进行套餐验证
    const result = await AISelectionValidationService.checkAccess();

    validationState.packageAccess = {
      canAccess: result.canAccess,
      reason: result.reason,
      message: result.message,
      packageStatus: result.packageStatus
    };

    // 根据验证结果决定是否显示遮罩
    if (result.canAccess) {
      // 验证通过，隐藏遮罩
      validationState.showValidationOverlay = false;
    } else {
      // 验证失败，显示遮罩
      validationState.showValidationOverlay = true;
      validationState.validationReason = result.reason;
    }
  } catch (error) {
    console.error('套餐验证失败:', error);
    // 网络错误时不阻止用户使用，隐藏遮罩
    validationState.packageAccess.canAccess = true;
    validationState.showValidationOverlay = false;
  } finally {
    validationState.isValidating = false;
  }
};

// 重试验证
const retryValidation = async () => {
  validationState.showValidationOverlay = false;
  await checkPackageAccess();
};

// 监听套餐购买完成事件
const handlePackagePurchased = async (event) => {
  console.log('收到套餐购买完成事件:', event.detail);
  // 清除验证缓存，强制重新验证
  AISelectionValidationService.clearCache();
  // 重新验证套餐权限
  await checkPackageAccess();
};

// 生命周期钩子
onMounted(async () => {
  try {
    // 先检查套餐访问权限
    await checkPackageAccess();
    
    // 从缓存中恢复用户的模式选择偏好
    restoreMatchingModeFromCache();
    
    // 根据套餐状态和缓存设置默认模式
    if (!matchingMode.value) {
      // 如果没有缓存的模式选择，所有用户都默认显示专业库选校
      matchingMode.value = 'list';
      enableAiSelection.value = false;
      
      if (!validationState.packageAccess.canAccess) {
        console.log('未购买套餐，使用默认匹配模式：专业库选校（带高斯模糊）');
      } else {
        console.log('已购买套餐，使用默认匹配模式：专业库选校');
      }
    } else {
      console.log('已恢复上次使用的匹配模式:', matchingMode.value, '，智能选校:', enableAiSelection.value);
      
      // 如果恢复的模式是自选校，需要加载筛选数据
      if (matchingMode.value === 'filter') {
        console.log('恢复自选校模式，开始加载筛选数据...');
        await loadFilterData();
      }
    }
    
    console.log('选校匹配界面数据初始化完成');
  } catch (error) {
    console.error('选校匹配界面初始化失败:', error);
  } finally {
    // 初始加载完成
    isInitialLoading.value = false;
  }

  // 初始化屏幕尺寸检查
  checkScreenSize();

  // 监听窗口大小变化，重新计算详情区域高度和屏幕尺寸
  window.addEventListener('resize', () => {
    recalculateHeights();
    checkScreenSize();
  });

  // 监听页面离开事件，清理SSE连接
  window.addEventListener('beforeunload', cleanupSSEConnection);

  // 监听套餐购买完成事件
  window.addEventListener('packagePurchased', handlePackagePurchased);

  // 添加全局点击监听器，用于关闭客户选择器
  document.addEventListener('click', handleClickOutside);

  // 页面可见性变化时的处理
  document.addEventListener('visibilitychange', async () => {
    // 当页面重新变为可见时（用户从其他页面返回），重新验证套餐状态
    if (!document.hidden) {
      console.log('页面重新可见，重新验证套餐状态');
      // 清除缓存，强制重新验证
      AISelectionValidationService.clearCache();
      await checkPackageAccess();
    }
  });

  // 检查是否有来自客户档案页面的快速客户信息
  try {
    const quickClientInfo = sessionStorage.getItem('quick_client_info');
    if (quickClientInfo) {
      const clientInfo = JSON.parse(quickClientInfo);
      console.log('检测到快速客户信息:', clientInfo);
      
      // 设置客户信息
      selectedClient.value = clientInfo;
      hasValidClientSelection.value = true;
      
      // 清除sessionStorage中的信息，避免重复使用
      sessionStorage.removeItem('quick_client_info');
    }
  } catch (error) {
    console.warn('解析快速客户信息失败:', error);
  }

  // 预加载一些常见学校的logo作为测试
  try {
    const commonSchools = [
      { 学校中文名: '香港大学' },
      { 学校中文名: '新加坡国立大学' },
      { 学校中文名: '帝国理工学院' },
      { 学校中文名: '伦敦大学学院' },
      { 学校中文名: '南洋理工大学' }
    ];
    await logoStore.preloadSchoolLogos(commonSchools);
  } catch (error) {
    console.warn('预加载常见学校logo失败:', error);
  }
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', () => {
    recalculateHeights();
    checkScreenSize();
  });
  window.removeEventListener('beforeunload', cleanupSSEConnection);
  window.removeEventListener('packagePurchased', handlePackagePurchased);
  cleanupSSEConnection();
});

// 窗口大小变化时重新计算高度
const recalculateHeights = () => {
  if (!recommendations.value.length) return;
  
  setTimeout(() => {
    const detailsEls = document.querySelectorAll('.details-content');
    for (const el of detailsEls) {
      const schoolId = el.closest('[data-school-id]')?.getAttribute('data-school-id');
      if (schoolId) {
        const school = recommendations.value.find(s => s.id == schoolId);
        if (school && school.detailsLoaded) {
          school.originalHeight = el.scrollHeight; // 使用 scrollHeight
          if (school.showDetails) {
            school.detailsHeight = school.originalHeight;
          }
        }
      }
    }
  }, 150); // 增加延迟
};

// 从匹配结果中获取可用的国家/地区列表
const availableRegions = computed(() => {
  if (!recommendations.value || recommendations.value.length === 0) {
    return [];
  }
  
  // 预定义的地区顺序
  const regionOrder = ['中国香港', '新加坡', '英国', '美国', '澳大利亚', '中国澳门', '马来西亚'];
  
  // 从推荐结果中提取所有唯一的地区
  const regionsSet = new Set();
  recommendations.value.forEach(item => {
    if (item.location) {
      regionsSet.add(item.location);
    }
  });
  
  // 按预定义顺序排序，未在列表中的地区排在最后
  const availableList = Array.from(regionsSet).sort((a, b) => {
    const indexA = regionOrder.indexOf(a);
    const indexB = regionOrder.indexOf(b);
    
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    } else if (indexA !== -1) {
      return -1;
    } else if (indexB !== -1) {
      return 1;
    } else {
      return a.localeCompare(b, 'zh-CN');
    }
  });
  
  return availableList;
});

// 根据选中的国家/地区筛选推荐结果
const regionFilteredRecommendations = computed(() => {
  let result = recommendations.value;
  
  // 如果选择了特定国家/地区，进行筛选
  if (selectedRegion.value !== 'all') {
    result = result.filter(item => item.location === selectedRegion.value);
  }
  
  return result;
});

// 排序后的匹配结果（基于筛选后的结果进行排序）
const filteredRecommendations = computed(() => {
  let result = regionFilteredRecommendations.value;
  
  // 排序逻辑
  const [field, order] = sortBy.value.split('-');
  
  return result.sort((a, b) => {
    // 推荐排名排序（默认）
    if (field === 'rank') {
      return order === 'asc' ? a.rank - b.rank : b.rank - a.rank;
    }
    
    // QS排名排序
    if (field === 'ranking') {
      const extractQSRank = (rankingStr) => {
        if (!rankingStr || rankingStr === '排名信息暂无' || rankingStr === '排名信息获取失败') return 9999;
        const match = rankingStr.match(/\d+/);
        return match ? parseInt(match[0], 10) : 9999;
      };
      
      const rankA = extractQSRank(a.ranking);
      const rankB = extractQSRank(b.ranking);
      return order === 'asc' ? rankA - rankB : rankB - rankA;
    }
    
    // 对于其他数值字段
    return order === 'asc' ? a[field] - b[field] : b[field] - a[field];
  });
});

// 监听表单字段变化，实时清除错误状态
watch(() => profileForm.academic.gpa, (newValue) => {
  if (formValidation.hasValidated && newValue && newValue.trim() !== '') {
    formValidation.errors.academic.gpa = false;
  }
});



watch(() => profileForm.academic.school, (newValue) => {
  if (formValidation.hasValidated && newValue && newValue.trim() !== '') {
    formValidation.errors.academic.school = false;
  }
});

watch(() => profileForm.academic.major, (newValue) => {
  if (formValidation.hasValidated && newValue && newValue.trim() !== '') {
    formValidation.errors.academic.major = false;
  }
});

watch(() => profileForm.intention.countries, (newValue) => {
  if (formValidation.hasValidated && Array.isArray(newValue) && newValue.length > 0) {
    formValidation.errors.intention.countries = false;
  }
});

watch(() => profileForm.intention.majors, (newValue) => {
  if (formValidation.hasValidated && Array.isArray(newValue) && newValue.length > 0) {
    formValidation.errors.intention.majors = false;
  }
});

// 监听匹配结果变化，自动进行学校分组（仅在专业库匹配模式下）
watch(() => recommendations.value, (newRecommendations, oldRecommendations) => {
  if (currentResultMode.value === 'hard_filter' && newRecommendations.length > 0) {
    groupRecommendationsBySchool();
  }
  // 当匹配结果发生变化时，重置地区筛选为"全部"
  if (newRecommendations !== oldRecommendations) {
    selectedRegion.value = 'all';
  }
}, { deep: true, immediate: false });

// 监听地区筛选变化，重新分组学校
watch(() => selectedRegion.value, () => {
  if (currentResultMode.value === 'hard_filter') {
    groupRecommendationsBySchool();
  }
}, { immediate: false });

// 监听智能推荐开关变化 - 调整国家选择限制
watch(() => enableAiSelection.value, (newValue) => {
  if (newValue) {
    // 切换到智能匹配模式：确保countries是字符串格式
    if (Array.isArray(profileForm.intention.countries)) {
      if (profileForm.intention.countries.length > 1) {
        // 多个国家，只保留第一个
        const firstCountry = profileForm.intention.countries[0];
        profileForm.intention.countries = firstCountry;
        ElMessage.info('已切换到智能匹配模式，只保留第一个选择的国家/地区');
      } else if (profileForm.intention.countries.length === 1) {
        // 单个国家，从数组转为字符串
        profileForm.intention.countries = profileForm.intention.countries[0];
      } else {
        // 空数组，设为空字符串
        profileForm.intention.countries = '';
      }
    }
    // 如果已经是字符串，不需要处理
  } else {
    // 切换到专业库匹配模式：确保countries是数组格式
    if (!Array.isArray(profileForm.intention.countries)) {
      const currentCountry = profileForm.intention.countries;
      profileForm.intention.countries = currentCountry ? [currentCountry] : [];
    }
  }
});



// GPA范围配置
const gpaRanges = {
  '100': { min: 0, max: 100, step: 0.01, decimalPlaces: 2 }
};

// 验证GPA值是否在正确范围内
const validateGpaValue = (gpaValue, gpaScale) => {
  // 如果没有输入值或没有选择制式，不验证（避免初始状态报错）
  if (!gpaValue || !gpaScale) return { isValid: true, message: '' };
  
  // 去除空格
  const trimmedValue = gpaValue.toString().trim();
  if (!trimmedValue) return { isValid: true, message: '' };
  
  const numValue = parseFloat(trimmedValue);
  const range = gpaRanges[gpaScale];
  
  if (!range) return { isValid: false, message: '' };
  
  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return { isValid: false, message: '' };
  }
  
  // 检查范围 - 超出范围时只标红，不显示错误信息
  if (numValue < range.min || numValue > range.max) {
    return { isValid: false, message: '' };
  }
  
  // 检查小数位数
  const decimalPlaces = (numValue.toString().split('.')[1] || '').length;
  if (decimalPlaces > range.decimalPlaces) {
    return { isValid: false, message: '' };
  }
  return { isValid: true, message: '' };
};

// GPA输入处理函数
const handleGpaInput = (value) => {
  profileForm.academic.gpa = value;
  
  // 实时验证
  const validation = validateGpaValue(value, profileForm.academic.gpaScale);
  gpaValidationError.value = !validation.isValid;
  gpaErrorMessage.value = validation.message;
  
  // 清除基础错误状态
  clearFieldError('academic', 'gpa');
  checkSectionCompletion('academic');
};

// GPA变更处理函数
const handleGpaChange = (value) => {
  profileForm.academic.gpa = value;
  
  // 完整验证（固定为百分制）
  const validation = validateGpaValue(value, '100');
  gpaValidationError.value = !validation.isValid;
  gpaErrorMessage.value = validation.message;
  
  clearFieldError('academic', 'gpa');
  checkSectionCompletion('academic');
};



// === 专业库匹配模式的学校分组功能 ===

// 将匹配结果按学校分组
const groupRecommendationsBySchool = () => {
  if (currentResultMode.value !== 'hard_filter') {
    schoolGroups.value = [];
    return;
  }
  
  if (!regionFilteredRecommendations.value.length) {
    schoolGroups.value = [];
    return;
  }
  
  // 按学校名称分组
  const groupMap = new Map();
  
  // === 保留已存在学校组的展开/折叠状态，避免重组时丢失 ===
  const prevStateMap = new Map(
    schoolGroups.value.map(g => [g.schoolKey, { expanded: g.expanded, contentHeight: g.contentHeight }])
  );
  
  regionFilteredRecommendations.value.forEach((program, index) => {
    const schoolKey = program.学校中文名 || 'unknown';
    
    if (!groupMap.has(schoolKey)) {
      const prev = prevStateMap.get(schoolKey) || { expanded: false, contentHeight: 0 };
      groupMap.set(schoolKey, {
        schoolKey,
        schoolName: program.学校中文名,
        englishName: program.学校英文名,
        location: program.location,
        qsRank: program.ranking?.match(/\d+/)?.[0],
        // 添加school_logo_url字段用于logo显示
        school_logo_url: program.school_logo_url,
        programs: [],
        expanded: prev.expanded,
        contentHeight: prev.contentHeight
      });
    }
    
    // 确保每个专业的UI状态都已正确初始化，但不覆盖已有状态
    if (program.showDetails === undefined) {
      program.showDetails = false;
    }
    if (program.isFavorite === undefined) {
      program.isFavorite = false;
    }
    if (program.detailsHeight === undefined) {
      program.detailsHeight = 0;
    }
    if (program.detailsLoaded === undefined) {
      program.detailsLoaded = false;
    }
    if (program.originalHeight === undefined) {
      program.originalHeight = 0;
    }
    groupMap.get(schoolKey).programs.push(program);
  });
  
  // 转换为数组并排序
  const groupArray = Array.from(groupMap.values());
  
  // 排序逻辑：根据是否选择了特定地区来决定排序方式
  const regionOrder = ['中国香港', '新加坡', '英国', '美国', '澳大利亚', '中国澳门', '马来西亚'];
  
  groupArray.sort((a, b) => {
    // 如果显示全部地区，直接按QS排名排序
    if (selectedRegion.value === 'all') {
      const rankA = parseInt(a.qsRank) || 9999;
      const rankB = parseInt(b.qsRank) || 9999;
      
      // 先按QS排名排序
      if (rankA !== rankB) {
        return rankA - rankB;
      }
      
      // QS排名相同时，按学校名称排序
      return (a.schoolName || '').localeCompare(b.schoolName || '', 'zh-CN');
    }
    
    // 如果选择了特定地区，保持原有的地区优先排序逻辑
    // 第一优先级：按地区排序
    const regionA = a.location || '';
    const regionB = b.location || '';
    
    const regionIndexA = regionOrder.indexOf(regionA);
    const regionIndexB = regionOrder.indexOf(regionB);
    
    // 如果两个地区都在预定义列表中，按列表顺序排序
    if (regionIndexA !== -1 && regionIndexB !== -1) {
      if (regionIndexA !== regionIndexB) {
        return regionIndexA - regionIndexB;
      }
    }
    // 如果只有一个地区在预定义列表中，列表中的排在前面
    else if (regionIndexA !== -1) {
      return -1;
    }
    else if (regionIndexB !== -1) {
      return 1;
    }
    // 如果两个地区都不在预定义列表中，按字母顺序排序
    else if (regionA !== regionB) {
      return regionA.localeCompare(regionB);
    }
    
    // 第二优先级：在同一地区内按QS排名排序
    const rankA = parseInt(a.qsRank) || 9999;
    const rankB = parseInt(b.qsRank) || 9999;
    return rankA - rankB;
  });
  
  schoolGroups.value = groupArray;
  
  // 强制触发响应式更新
  schoolGroups.value = [...schoolGroups.value];

  // 若展开的学校组 contentHeight 丢失，延迟重新计算确保高度正确
  setTimeout(() => {
    schoolGroups.value.forEach(group => {
      if (group.expanded && group.contentHeight === 0) {
        const el = programsContentRefs.value[group.schoolKey];
        if (el) group.contentHeight = el.scrollHeight;
      }
    });
  }, 60);
};

// 切换学校组的展开/收起状态
const toggleSchoolGroup = (schoolGroup) => {
  schoolGroup.expanded = !schoolGroup.expanded;
  
  if (schoolGroup.expanded) {
    // 展开时计算内容高度
    setTimeout(() => {
      const contentEl = programsContentRefs.value[schoolGroup.schoolKey];
      if (contentEl) {
        schoolGroup.contentHeight = contentEl.scrollHeight;
      }
    }, 50);
  } else {
    // 收起时重置高度
    schoolGroup.contentHeight = 0;
  }
};

// 处理国家/地区切换
const handleRegionChange = (region) => {
  selectedRegion.value = region;
};

// 切换专业详情显示
const toggleProgramDetails = (program) => {
  // 阻止事件冒泡
  event?.stopPropagation?.();
  
  // 确保program对象存在必要的属性
  if (program.detailsHeight === undefined) program.detailsHeight = 0;
  if (program.originalHeight === undefined) program.originalHeight = 0;
  if (program.detailsLoaded === undefined) program.detailsLoaded = false;
  
  if (!program.detailsLoaded) {
    // 第一次展开时初始化状态
    program.detailsLoaded = true;
    program.showDetails = true;
    program.detailsHeight = 300; // 设置初始高度，避免闪烁
    
    // 在下一个渲染周期计算实际高度
    setTimeout(() => {
      try {
        // 尝试多种选择器找到详情容器
        let detailsEl = null;

        // 1. 首先尝试使用program_id
        if (program.program_id) {
          const selector1 = `[data-program-id="${program.program_id}"] .details-content`;
          detailsEl = document.querySelector(selector1);
        }

        // 2. 如果找不到，尝试使用id
        if (!detailsEl && program.id) {
          const selector2 = `[data-program-id="${program.id}"] .details-content`;
          detailsEl = document.querySelector(selector2);
        }

        // 3. 最后尝试使用更通用的选择器
        if (!detailsEl) {
          // 找到所有详情容器
          const allDetailsEls = document.querySelectorAll('.details-content');
          // 遍历所有容器，找到最近刚展开的那个
          for (const el of allDetailsEls) {
            const programCard = el.closest('.program-card');
            if (programCard && !detailsEl) {
              detailsEl = el;
            }
          }
        }

        if (detailsEl) {
          const height = detailsEl.scrollHeight;
          program.originalHeight = height;
          program.detailsHeight = height;
        } else {
          // 如果所有选择器都失败，使用备用高度
          program.originalHeight = 350; // 使用默认高度
          program.detailsHeight = program.originalHeight;
        }

        // 重新计算父级学校组的内容高度
        updateSchoolGroupHeight(program);
      } catch (error) {
        console.warn('DOM查询失败，使用默认高度:', error);
        program.originalHeight = 350;
        program.detailsHeight = program.originalHeight;
      }
    }, 300); // 增加延迟确保内容完全渲染
  } else {
    // 已经加载过，直接切换状态
    const newState = !program.showDetails;
    program.showDetails = newState;
    program.detailsHeight = newState ? program.originalHeight : 0;
    
    // 重新计算父级学校组的内容高度
    setTimeout(() => {
      updateSchoolGroupHeight(program);
    }, 100);
  }
};

// 更新学校组高度的辅助函数
const updateSchoolGroupHeight = (program) => {
  // 使用多种ID匹配方式，确保能找到正确的学校组
  const schoolGroup = schoolGroups.value.find(group => {
    return group.programs.some(p => 
      // 使用多种匹配方式：id、program_id、专业中文名+学校中文名
      (p.id === program.id) || 
      (p.program_id === program.program_id) || 
      (p.专业中文名 === program.专业中文名 && p.学校中文名 === program.学校中文名)
    );
  });
  
  if (schoolGroup && schoolGroup.expanded) {
    // 给DOM更新留出时间
    setTimeout(() => {
      const contentEl = programsContentRefs.value[schoolGroup.schoolKey];
      if (contentEl) {
        schoolGroup.contentHeight = contentEl.scrollHeight;
      }
    }, 50);
  }
};

// 切换专业收藏状态
const toggleProgramFavorite = (program) => {
  program.isFavorite = !program.isFavorite;
  // 收藏操作成功，无需提示
};

// 设置专业内容区域的引用
const setProgramsContentRef = (schoolKey, el) => {
  if (el) {
    programsContentRefs.value[schoolKey] = el;
  }
};

// 自动修正结果模式的逻辑 - 用于处理后端事件顺序问题
const autoCorrectResultMode = () => {
  // 检测数据类型不匹配的情况并自动修正
  const hasSchoolGroups = schoolGroups.value.length > 0;
  const hasRecommendations = recommendations.value.length > 0;
  const currentMode = currentResultMode.value;
  
  // 如果有学校分组数据但当前模式不是专业库匹配，说明模式被错误覆盖了
  if (hasSchoolGroups && currentMode !== 'hard_filter') {
    console.warn('检测到数据类型不匹配，自动修正显示模式为专业库匹配');
    currentResultMode.value = 'hard_filter';
    return true; // 表示进行了修正
  }
  
  return false; // 表示无需修正
};

// 在适当的时机调用自动修正
watch(() => [schoolGroups.value.length, recommendations.value.length, currentResultMode.value], 
  ([schoolGroupsLen, recommendationsLen, mode]) => {
    if (schoolGroupsLen > 0 && recommendationsLen > 0) {
      // 短暂延迟后检查是否需要自动修正
      setTimeout(() => {
        autoCorrectResultMode();
      }, 100);
    }
  }, 
  { immediate: true }
);

// 客户操作相关状态
const addingToClientPrograms = ref(false);

// 专业详情悬浮框状态
const detailDialogVisible = ref(false);
const selectedProgram = ref(null);
const detailLoading = ref(false);

// 缓存用户档案检查结果
const userHasFullProfilesCache = ref(null);

// 检查用户是否有完整档案客户
const checkUserHasFullProfiles = async () => {
  // 如果已经缓存了结果，直接返回
  if (userHasFullProfilesCache.value !== null) {
    return userHasFullProfilesCache.value;
  }

  try {
    // 使用选校匹配专用API检查，不传搜索关键词时只返回完整档案客户
    const response = await searchClientsForSchoolMatching('');
    const clients = response.data || response || [];
    const hasProfiles = clients.length > 0;

    // 缓存结果
    userHasFullProfilesCache.value = hasProfiles;
    return hasProfiles;
  } catch (error) {
    console.error('检查用户档案失败:', error);
    // 出错时不缓存，下次重试
    return false;
  }
};

// 加载默认客户列表
const loadDefaultClientList = async () => {
  if (defaultClientList.value.length > 0) {
    // 如果已经有缓存的客户列表，直接使用
    clientSearchResults.value = defaultClientList.value;
    return;
  }

  clientSearchLoading.value = true;
  try {
    // 使用选校匹配专用API，不传搜索关键词时只返回完整档案客户
    const response = await searchClientsForSchoolMatching('');
    const clients = response.data || response || [];

    // 缓存默认客户列表
    defaultClientList.value = clients;
    clientSearchResults.value = clients;
  } catch (error) {
    console.error('加载客户列表失败:', error);
    clientSearchResults.value = [];
  } finally {
    clientSearchLoading.value = false;
  }
};

// 处理客户搜索输入框 focus 事件
const handleClientSearchFocus = async () => {
  showClientSelector.value = true;
  updateDropdownPosition();

  // 如果没有搜索内容，检查用户是否有完整档案客户
  if (!clientSearchQuery.value || clientSearchQuery.value.trim() === '') {
    // 检查用户是否有完整档案客户
    const hasFullProfiles = await checkUserHasFullProfiles();

    // 只有当用户有完整档案客户时，才加载默认客户列表
    if (hasFullProfiles) {
      await loadDefaultClientList();
    } else {
      // 如果没有完整档案客户，不显示下拉框，避免抖动
      clientSearchResults.value = [];
      showClientSelector.value = false;
    }
  }
};

// 监听下拉框显示状态变化，更新位置
watch(showClientSelector, (newValue) => {
  if (newValue) {
    updateDropdownPosition();
    // 监听窗口滚动和大小变化，实时更新位置
    window.addEventListener('scroll', updateDropdownPosition);
    window.addEventListener('resize', updateDropdownPosition);
  } else {
    window.removeEventListener('scroll', updateDropdownPosition);
    window.removeEventListener('resize', updateDropdownPosition);
  }
});

// 监听客户搜索查询变化，只在清空时清除错误
watch(clientSearchQuery, (newValue) => {
  // 只在清空客户名称时清除错误提示
  if (!newValue || !newValue.trim()) {
    clearClientValidationError();
  }
});

// 处理客户输入时的回车键
const handleClientInputEnter = (event) => {
  // 阻止默认的回车行为
  event.preventDefault();
  
  const inputValue = clientSearchQuery.value?.trim();
  if (!inputValue) {
    return;
  }
  
  // 检查是否有匹配的搜索结果或快速建档选项
  const hasSearchResults = clientSearchResults.value && clientSearchResults.value.length > 0;
  const canCreateQuick = inputValue.length > 0;
  
  if (!hasSearchResults && !canCreateQuick) {
    // 没有匹配结果且不能创建快速建档，显示错误
    showClientValidationError('没有找到匹配的客户，请选择快速建档或搜索现有客户');
    return;
  }
  
  // 如果有搜索结果，提示用户选择
  if (hasSearchResults) {
    showClientValidationError('请选择下方的现有客户或点击快速建档选项');
    return;
  }
  
  // 如果没有搜索结果但可以创建快速建档，提示用户点击快速建档
  if (canCreateQuick && !hasSearchResults) {
    showClientValidationError('必须点击"创建快速定校书"选项才能创建客户档案，直接输入无效');
    return;
  }
};

// 处理客户输入框失焦事件
const handleClientInputBlur = () => {
  // 延迟检查，因为用户可能正在点击下拉选项
  setTimeout(() => {
    // 如果下拉框没有显示（用户没有在与下拉选项交互），则检查验证
    if (!showClientSelector.value) {
      checkAndShowClientValidationOnConfirm();
    }
  }, 150);
};

// 显示客户选择验证错误
const showClientValidationError = (message) => {
  clientValidationError.value = true;
  clientValidationMessage.value = message;
  hasValidClientSelection.value = false;
  
  // 错误提示不自动消失，只有在用户进行有效操作或清空客户名称时才清除
};

// 清除客户选择验证错误
const clearClientValidationError = () => {
  clientValidationError.value = false;
  clientValidationMessage.value = '';
};

// 检查客户选择状态并显示提示（如果需要）
const checkAndShowClientValidation = () => {
  // 如果客户名称栏有内容但没有有效选择，显示提示
  if (clientSearchQuery.value && clientSearchQuery.value.trim() && !selectedClient.value) {
    if (!clientValidationError.value) {
      showClientValidationError('必须点击"创建快速定校书"选项才能创建客户档案，直接输入无效');
    }
  } else if (!clientSearchQuery.value || !clientSearchQuery.value.trim()) {
    // 只有在客户名称栏没有内容时才清除错误提示
    clearClientValidationError();
  }
  // 注意：如果已选择客户，错误提示会在选择客户的函数中被清除
};

// 检查并显示客户验证错误（仅在用户确认输入时触发）
const checkAndShowClientValidationOnConfirm = () => {
  // 只在用户确认输入时检查并显示错误
  if (clientSearchQuery.value && clientSearchQuery.value.trim() && !selectedClient.value) {
    if (!clientValidationError.value) {
      showClientValidationError('必须点击"创建快速定校书"选项才能创建客户档案，直接输入无效');
    }
  }
};

// 处理客户搜索
const handleClientSearch = async (query) => {
  if (!query || query.trim() === '') {
    // 如果没有搜索内容，检查用户是否有完整档案客户
    if (showClientSelector.value) {
      const hasFullProfiles = await checkUserHasFullProfiles();
      if (hasFullProfiles) {
        await loadDefaultClientList();
      } else {
        clientSearchResults.value = [];
        showClientSelector.value = false;
      }
    } else {
      clientSearchResults.value = [];
    }
    return;
  }

  // 有搜索内容时，显示下拉框并进行搜索
  showClientSelector.value = true;
  updateDropdownPosition();

  clientSearchLoading.value = true;
  try {
    const response = await searchClientsForSchoolMatching(query.trim());
    let clients = response.data || response || [];

    // 按最近修改时间排序搜索结果
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.created_at || 0);
      const dateB = new Date(b.updated_at || b.created_at || 0);
      return dateB - dateA; // 倒序排列，最新的在前面
    });

    clientSearchResults.value = clients;
  } catch (error) {
    console.error('搜索客户失败:', error);
    clientSearchResults.value = [];
    ElMessage.error('搜索客户失败');
  } finally {
    clientSearchLoading.value = false;
  }
};

// 选择客户
const selectClient = async (client) => {
  try {
    console.log('开始选择客户:', client);
    
    // 清除客户选择验证错误
    clearClientValidationError();
    hasValidClientSelection.value = true;
    
    // 设置选中的客户
    selectedClient.value = client;
    showClientSelector.value = false;
    clientSearchQuery.value = '';
    clientSearchResults.value = [];

    // 清除用户档案缓存，确保数据一致性
    userHasFullProfilesCache.value = null;
    
    // 确保 profileForm 已初始化
    if (!profileForm || !profileForm.academic) {
      console.error('profileForm 未正确初始化');
      ElMessage.error('系统初始化错误，请刷新页面重试');
      return;
    }
    
    console.log('开始获取客户详细信息，ID:', client.id_hashed);
    
    // 获取客户详细信息，包括教育背景
    const clientDetails = await getClientById(client.id_hashed);
    console.log('获取到客户详情:', clientDetails);
    
    // 自动填充学术背景信息
    await fillAcademicBackgroundFromClient(clientDetails);
    
    // 加载客户的定校书专业列表
    await loadClientPrograms();
  } catch (error) {
    console.error('选择客户时发生错误:', error);
    
    // 确保selectedClient已设置，以便定校书功能可用
    selectedClient.value = client;
    
    try {
      // 即使获取详情失败，也要加载定校书
      await loadClientPrograms();
    } catch (loadError) {
      console.error('加载客户定校书失败:', loadError);
    }
    
    // 根据错误类型给出不同的提示
    if (error.message && error.message.includes('Cannot read properties of undefined')) {
      ElMessage.warning('获取客户教育背景信息失败，数据格式可能有问题，请手动填写');
    } else {
      ElMessage.warning('获取客户详细信息失败，请手动填写学术背景信息');
    }
  }
};

// 智能解析GPA格式，只接受百分制成绩
const parseGpaFormat = (gpaStr) => {
  if (!gpaStr || typeof gpaStr !== 'string') {
    return { scale: '100', value: '' };
  }

  const trimmedStr = gpaStr.trim();
  console.log('解析GPA格式:', trimmedStr);

  // 处理包含斜杠分隔的格式（兼容正斜杠 '/' 和反斜杠 '\' 两种格式）
  if (trimmedStr.includes('/') || trimmedStr.includes('\\')) {
    // 统一使用正斜杠进行分割，先将反斜杠替换为正斜杠
    const normalizedStr = trimmedStr.replace(/\\/g, '/');
    const parts = normalizedStr.split('/').map(part => part.trim()).filter(part => part);
    console.log('分割后的GPA部分:', parts);
    
    // 检查是否为其他制式，如果是则不填入
    if (parts.length === 2) {
      const firstValue = parseFloat(parts[0]);
      const secondValue = parseFloat(parts[1]);
      
      if (!isNaN(firstValue) && !isNaN(secondValue)) {
        // 如果是明确的4.0制或5.0制，不填入
        if (secondValue === 4 || secondValue === 4.0 || secondValue === 5 || secondValue === 5.0) {
          console.log('检测到其他制式成绩，不自动填入:', firstValue + '/' + secondValue);
          return { scale: '100', value: '' };
        }
        // 如果是明确的百分制格式
        else if (secondValue === 100) {
          if (firstValue >= 0 && firstValue <= 100) {
            console.log('找到明确百分制格式:', firstValue);
            return { 
              scale: '100', 
              value: formatDecimalPlaces(firstValue.toString(), 2) 
            };
          }
        }
      }
    }
    
    // 在复杂格式中查找百分制成绩（优先查找大于5的数值，因为可能是百分制）
    for (const part of parts) {
      const numValue = parseFloat(part);
      if (!isNaN(numValue) && numValue > 5 && numValue <= 100) {
        console.log('在复杂格式中找到百分制成绩:', numValue);
        return { 
          scale: '100', 
          value: formatDecimalPlaces(numValue.toString(), 2) 
        };
      }
    }
    
    // 如果没有找到明确的百分制成绩，不填入
    console.log('未找到明确的百分制成绩，不自动填入');
    return { scale: '100', value: '' };
  }

  // 处理不包含斜杠的简单格式
  const numericGpa = parseFloat(trimmedStr);
  if (!isNaN(numericGpa)) {
    // 如果数值在0-100范围内，直接作为百分制
    if (numericGpa >= 0 && numericGpa <= 100) {
      // 如果数值在0-5范围内，可能是其他制式，不填入
      if (numericGpa <= 5) {
        console.log('疑似其他制式成绩，不自动填入:', numericGpa);
        return { scale: '100', value: '' };
      }
      
      console.log('识别为百分制成绩:', numericGpa);
      return { 
        scale: '100', 
        value: formatDecimalPlaces(numericGpa.toString(), 2) 
      };
    }
  }

  // 无法识别或不符合百分制格式，不填入
  console.log('无法识别为百分制格式，不自动填入:', trimmedStr);
  return { 
    scale: '100', 
    value: '' 
  };
};

// 格式化小数位数的辅助函数
const formatDecimalPlaces = (numStr, maxDecimals = 2) => {
  const num = parseFloat(numStr);
  if (isNaN(num)) return numStr;
  
  // 保留最多maxDecimals位小数，并去除尾随零
  return parseFloat(num.toFixed(maxDecimals)).toString();
};

// 标准化GPA值，确保格式一致性和合理性（仅百分制）
const standardizeGpaValue = (gpaValue, gpaScale) => {
  if (!gpaValue) return '';
  
  const numValue = parseFloat(gpaValue);
  if (isNaN(numValue)) return gpaValue;
  
  // 百分制：限制在0-100范围内，保留最多2位小数
  const hundredScale = Math.max(0, Math.min(100, numValue));
  return formatDecimalPlaces(hundredScale.toString(), 2);
};

// 从客户信息中自动填充学术背景
const fillAcademicBackgroundFromClient = async (clientDetailsResponse) => {
  try {
    // 处理不同的API响应格式
    const clientDetails = clientDetailsResponse?.data || clientDetailsResponse;
    
    console.log('客户详情响应:', clientDetails);
    
    // 检查客户详情是否存在
    if (!clientDetails) {
      console.log('客户详情为空');
      return;
    }
    
    // 检查是否有教育背景信息
    if (!clientDetails.education || !Array.isArray(clientDetails.education) || clientDetails.education.length === 0) {
      console.log('客户没有教育背景信息或格式不正确');
      return;
    }
    
    console.log('教育背景数据:', clientDetails.education);
    
    // 找到本科教育经历（优先选择学位为 bachelor 或本科的记录）
    let undergraduateEducation = clientDetails.education.find(edu => {
      if (!edu || !edu.degree) return false;
      const degree = edu.degree.toLowerCase();
      return degree.includes('bachelor') || degree.includes('本科') || degree.includes('学士');
    });
    
    // 如果没有找到本科记录，取最新的一条教育记录
    if (!undergraduateEducation && clientDetails.education.length > 0) {
      // 按创建时间或ID排序，取最新的
      const sortedEducation = [...clientDetails.education].sort((a, b) => {
        if (a.created_at && b.created_at) {
          return new Date(b.created_at) - new Date(a.created_at);
        }
        return (b.id || 0) - (a.id || 0);
      });
      undergraduateEducation = sortedEducation[0];
    }
    
    if (undergraduateEducation) {
      console.log('找到教育背景信息:', undergraduateEducation);
      
      // 确保 profileForm.academic 存在
      if (!profileForm.academic) {
        console.error('profileForm.academic 不存在');
        return;
      }
      
      // 填充学校信息
      if (undergraduateEducation.school) {
        profileForm.academic.school = undergraduateEducation.school;
        clearFieldError('academic', 'school');
        console.log('已填充学校:', undergraduateEducation.school);
      }
      
      // 填充专业信息
      if (undergraduateEducation.major) {
        profileForm.academic.major = undergraduateEducation.major;
        clearFieldError('academic', 'major');
        console.log('已填充专业:', undergraduateEducation.major);
      }
      
      // 填充GPA信息
      if (undergraduateEducation.gpa || undergraduateEducation.gpa_scale) {
        console.log('原始GPA数据:', {
          gpa: undergraduateEducation.gpa,
          gpa_scale: undergraduateEducation.gpa_scale
        });

        // 优先使用数据库中的gpa_scale字段
        let gpaScale = undergraduateEducation.gpa_scale || '100'; // 默认百分制
        let gpaValue = undergraduateEducation.gpa || '';

        // 如果数据库中没有gpa_scale，但有gpa值，则智能识别格式
        if (!undergraduateEducation.gpa_scale && undergraduateEducation.gpa) {
          const gpaStr = undergraduateEducation.gpa.toString().trim();
          
          // 增强的GPA格式处理逻辑
          const { scale, value } = parseGpaFormat(gpaStr);
          gpaScale = scale;
          gpaValue = value;
        }

        // 设置GPA值（固定百分制）
        profileForm.academic.gpaScale = '100';

        if (gpaValue) {
          // 标准化GPA值：确保格式一致性（固定百分制）
          const standardizedGpa = standardizeGpaValue(gpaValue, '100');
          profileForm.academic.gpa = standardizedGpa;
          clearFieldError('academic', 'gpa');
          console.log('已填充GPA值:', standardizedGpa, '(原值:', gpaValue, ')');

          // 触发GPA验证（固定百分制）
          const validation = validateGpaValue(standardizedGpa, '100');
          gpaValidationError.value = !validation.isValid;
          gpaErrorMessage.value = validation.message;
          
          if (!validation.isValid) {
            console.warn('GPA值验证失败:', validation.message);
          }
        }

        console.log(`已识别并填充GPA: ${gpaValue}/${gpaScale}制`);
      }
      
      // 检查学术背景部分完成状态
      checkSectionCompletion('academic');
      
      // 如果学术背景部分未展开，则自动展开
      if (!sectionStates.academic) {
        sectionStates.academic = true;
      }
      
      console.log('学术背景信息已自动填充（学校、专业、GPA）');
    } else {
      console.log('未找到合适的教育背景信息');
    }
  } catch (error) {
    console.error('填充学术背景信息时发生错误:', error);
  }
};

// 创建快速客户
const createQuickClient = async (clientName) => {
  if (!clientName || !clientName.trim()) {
    ElMessage.warning('请输入客户名称');
    return;
  }

  // 立即保存模式：直接创建/更新快速建档记录到数据库
  try {
    console.log(`正在为 "${clientName.trim()}" 创建/更新快速建档记录...`);

    // 准备快速建档数据（使用默认值）
    const quickProfileData = {
      name: clientName.trim(),
      school: '', // 默认为空，用户后续可以填写
      major: '', // 默认为空，用户后续可以填写
      gpa: '', // 默认为空，用户后续可以填写
      gpa_scale: '100', // 默认百分制
      service_type: 'master' // 默认硕士
    };

    // 调用快速建档API立即保存到数据库
    // 后端会自动处理新建或更新的逻辑
    const response = await createQuickProfile(quickProfileData);

    if (response && response.id_hashed) {
      // 创建/更新成功，设置为当前选中客户
      const client = {
        id_hashed: response.id_hashed,
        name: response.name,
        profile_type: response.profile_type,
        isTemporary: false, // 标记为已保存到数据库
        created_at: response.created_at
      };

      // 清除客户选择验证错误
      clearClientValidationError();
      hasValidClientSelection.value = true;

      selectedClient.value = client;

      // 清除用户档案缓存，因为可能新增了客户
      userHasFullProfilesCache.value = null;

      // 如果有教育信息，自动填充表单
      if (response.education) {
        const edu = response.education;
        if (edu.school) profileForm.academic.school = edu.school;
        if (edu.major) profileForm.academic.major = edu.major;
        
        // 处理GPA信息，统一转换为百分制
        if (edu.gpa) {
          const { scale, value } = parseGpaFormat(edu.gpa.toString());
          profileForm.academic.gpa = value;
          profileForm.academic.gpaScale = '100'; // 只支持百分制
        } else {
          profileForm.academic.gpaScale = '100'; // 默认百分制
        }
      }

      // 缓存客户信息
      try {
        sessionStorage.setItem('quick_client_info', JSON.stringify(client));
      } catch (error) {
        console.error('保存客户缓存失败:', error);
      }

      // 隐藏客户选择器
      showClientSelector.value = false;
      clientSearchQuery.value = '';
      clientSearchResults.value = [];

      // 加载客户的定校书专业列表（如果是已存在的客户）
      try {
        const programsResponse = await getClientPrograms(client.id_hashed);
        if (programsResponse && programsResponse.length > 0) {
          clientProgramsList.value = programsResponse;
          clientPrograms.value = new Set(programsResponse.map(p => p.program_id || p.id));
        } else {
          // 新客户，初始化空列表
          clientPrograms.value.clear();
          clientProgramsList.value = [];
        }
      } catch (error) {
        console.error('加载客户定校书失败:', error);
        // 初始化空列表
        clientPrograms.value.clear();
        clientProgramsList.value = [];
      }

      // 快速建档成功，无需提示
      console.log('快速建档操作成功:', response);
    } else {
      throw new Error('API响应格式错误');
    }
  } catch (error) {
    console.error('快速建档操作失败:', error);

    // 如果是409冲突错误（客户已存在完整档案）
    if (error.response?.status === 409) {
      ElMessage.error(error.response.data?.detail || '该客户已存在完整档案，请在客户档案中查看');
      return;
    }

    // 其他错误，回退到临时客户模式
    ElMessage.warning('保存到数据库失败，将创建临时客户档案');

    // 创建临时客户对象作为备选方案
    const tempClient = {
      id_hashed: `temp_${Date.now()}`,
      name: clientName.trim(),
      isTemporary: true,
      created_at: new Date().toISOString()
    };

    // 清除客户选择验证错误
    clearClientValidationError();
    hasValidClientSelection.value = true;

    // 设置为当前选中客户
    selectedClient.value = tempClient;

    // 缓存客户信息
    try {
      sessionStorage.setItem('quick_client_info', JSON.stringify(tempClient));
    } catch (error) {
      console.error('保存客户缓存失败:', error);
    }

    // 隐藏客户选择器
    showClientSelector.value = false;
    clientSearchQuery.value = '';
    clientSearchResults.value = [];

    // 初始化客户专业列表
    clientPrograms.value.clear();
    clientProgramsList.value = [];

    // 临时客户创建成功，无需提示
  }
};

// 清除选择的客户
const clearSelectedClient = () => {
  selectedClient.value = null;
  clientPrograms.value.clear();

  // 清除客户选择验证状态
  clearClientValidationError();
  hasValidClientSelection.value = false;

  // 清空客户搜索相关状态
  clientSearchQuery.value = '';
  clientSearchResults.value = [];
  showClientSelector.value = false;

  // 清除侧边栏相关状态
  clientProgramsList.value = [];
  showSchoolBookSidebar.value = false;

  // 清除快速客户缓存
  try {
    sessionStorage.removeItem('quick_client_info');
    sessionStorage.removeItem('quick_client_programs');
  } catch (error) {
    console.error('清除客户缓存失败:', error);
  }
  
  // 清空学术背景表单
  if (profileForm && profileForm.academic) {
    profileForm.academic.school = '';
    profileForm.academic.major = '';
    profileForm.academic.gpa = '';
    profileForm.academic.gpaScale = '100'; // 保持百分制
    
    // 清除相关的表单验证错误状态
    if (formValidation.hasValidated) {
      formValidation.errors.academic.school = false;
      formValidation.errors.academic.major = false;
      formValidation.errors.academic.gpa = false;
    }
    
    // 清除GPA验证错误
    gpaValidationError.value = false;
    gpaErrorMessage.value = '';
    
    // 重新检查学术背景部分完成状态
    checkSectionCompletion('academic');
  }
};

// 加载客户的定校书专业列表
const loadClientPrograms = async () => {
  if (!selectedClient.value) return;

  // 设置加载状态
  isLoadingClientPrograms.value = true;

  try {
    const response = await getClientPrograms(selectedClient.value.id_hashed);
    const programs = response.data || response || [];

    // 将专业ID添加到Set中，便于快速查询
    clientPrograms.value = new Set(programs.map(p => p.program_id || p.id));

    // 处理悬浮框的程序列表，合并基本信息和详细信息
    clientProgramsList.value = programs.map(program => {
      // 如果有详细信息，则使用详细信息，否则使用基本信息
      const details = program.program_details || {};

      return {
        // 基本的client_programs信息
        id: program.id,
        client_id: program.client_id,
        program_id: program.program_id,
        created_at: program.created_at,
        notes: program.notes,

        // 从ai_selection_programs表关联的详细信息
        school_name_cn: details.school_name_cn || program.school_name_cn || '未知学校',
        school_name_en: details.school_name_en || program.school_name_en || '',
        program_name_cn: details.program_name_cn || program.program_name_cn || '未知专业',
        program_name_en: details.program_name_en || program.program_name_en || '',
        program_category: details.program_category || program.program_category || '未知类别',
        school_region: details.school_region || program.region || program.school_region || '未知地区',
        degree: details.degree || program.degree || '硕士',
        school_qs_rank: details.school_qs_rank || '',
        school_logo_url: details.school_logo_url || '',
        program_website: details.program_website || '',
        gpa_requirements: details.gpa_requirements || '',
        language_requirements: details.language_requirements || '',
        application_requirements: details.application_requirements || '',
        program_tuition: details.program_tuition || '',
        enrollment_time: details.enrollment_time || '',
        program_duration: details.program_duration || ''
      };
    });

    console.log('定校书数据加载成功:', {
      clientId: selectedClient.value.id_hashed,
      programCount: programs.length,
      programIds: Array.from(clientPrograms.value)
    });
  } catch (error) {
    console.error('加载客户定校书失败:', error);
    clientPrograms.value.clear();
    clientProgramsList.value = [];
  } finally {
    // 清除加载状态
    isLoadingClientPrograms.value = false;
  }
};

// 切换定校书侧边栏显示/隐藏状态
const toggleSchoolBookSidebar = async () => {
  // 防止快速点击导致的DOM访问错误
  if (isToggling.value) {
    return;
  }

  isToggling.value = true;

  // 使用nextTick确保DOM更新完成
  nextTick(async () => {
    const wasVisible = showSchoolBookSidebar.value;
    showSchoolBookSidebar.value = !showSchoolBookSidebar.value;

    // 如果是打开侧边栏，重新获取最新的定校书数据
    if (!wasVisible && showSchoolBookSidebar.value && selectedClient.value) {
      try {
        await loadClientPrograms();
      } catch (error) {
        console.error('刷新定校书数据失败:', error);
        // 即使刷新失败也要显示侧边栏，避免用户体验问题
      }
    }

    // 延迟重置切换状态，避免动画期间的重复操作
    setTimeout(() => {
      isToggling.value = false;
    }, 300); // 与transition动画时间保持一致
  });
};

// 侧边栏动画处理方法
const onSidebarEnter = (el) => {
  if (!el || !el.style) return;
  try {
    el.style.transform = 'translateX(100%)';
    el.style.opacity = '0';
    el.offsetHeight; // 强制重排
    el.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    el.style.transform = 'translateX(0)';
    el.style.opacity = '1';
  } catch (error) {
    console.warn('侧边栏进入动画失败:', error);
  }
};

const onSidebarAfterEnter = (el) => {
  if (!el || !el.style) return;
  try {
    el.style.transition = '';
  } catch (error) {
    console.warn('侧边栏进入完成动画失败:', error);
  }
};

const onSidebarLeave = (el) => {
  if (!el || !el.style) return;
  try {
    el.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    el.style.transform = 'translateX(100%)';
    el.style.opacity = '0';
  } catch (error) {
    console.warn('侧边栏离开动画失败:', error);
  }
};

const onSidebarAfterLeave = (el) => {
  if (!el || !el.style) return;
  try {
    el.style.transition = '';
    el.style.transform = '';
    el.style.opacity = '';
  } catch (error) {
    console.warn('侧边栏离开完成动画失败:', error);
  }
};

// 项目列表动画处理方法
const onProgramEnter = (el) => {
  const index = parseInt(el.dataset.index) || 0;
  el.style.opacity = '0';
  el.style.transform = 'translateX(-20px) scale(0.95)';
  el.style.transition = 'none';

  setTimeout(() => {
    el.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    el.style.opacity = '1';
    el.style.transform = 'translateX(0) scale(1)';
  }, index * 50);
};

const onProgramLeave = (el) => {
  el.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
  el.style.opacity = '0';
  el.style.transform = 'translateX(20px) scale(0.95)';
  el.style.height = el.offsetHeight + 'px';

  setTimeout(() => {
    el.style.height = '0';
    el.style.marginBottom = '0';
    el.style.paddingTop = '0';
    el.style.paddingBottom = '0';
  }, 150);
};

// 导出定校书Excel文件
const handleExportSchoolBook = async () => {
  // 检查是否有目标专业
  if (clientProgramsList.value.length === 0) {
    ElMessage.warning('请先添加目标专业，再导出定校书');
    return;
  }

  if (!selectedClient.value) {
    ElMessage.error('请先选择客户');
    return;
  }

  try {
    isExporting.value = true;
    const clientId = selectedClient.value.id_hashed;
    const response = await exportClientSchoolBook(clientId);

    // 创建文件名
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const filename = `${selectedClient.value.name || '客户'}_定校书_${timestamp}.xlsx`;

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 释放URL对象
    window.URL.revokeObjectURL(downloadUrl);

    // 导出成功，无需提示

  } catch (error) {
    console.error('导出定校书Excel失败:', error);

    // 根据错误类型显示不同的提示
    if (error.response && error.response.status === 400) {
      ElMessage.error('该客户暂无目标专业，无法生成定校书');
    } else if (error.response && error.response.status === 404) {
      ElMessage.error('客户不存在');
    } else {
      ElMessage.error('导出定校书失败，请稍后重试');
    }
  } finally {
    isExporting.value = false;
  }
};

// 从定校书悬浮框中删除项目
const removeFromClientPrograms = async (program) => {
  if (!selectedClient.value || removingFromClientPrograms.value) return;
  
  removingFromClientPrograms.value = true;
  
  try {
    const programId = program.program_id || program.id;
    await removeClientProgram(selectedClient.value.id_hashed, programId);
    
    // 更新本地状态
    clientPrograms.value.delete(programId);
    clientProgramsList.value = clientProgramsList.value.filter(p => 
      (p.program_id || p.id) !== programId
    );
    
    // 移除成功，无需提示
  } catch (error) {
    console.error('从定校书删除项目失败:', error);
    ElMessage.error('删除失败，请重试');
  } finally {
    removingFromClientPrograms.value = false;
  }
};

// 检查专业是否已在客户定校书中
const isInClientPrograms = (program) => {
  if (!selectedClient.value) return false;
  const programId = program.program_id || program.id;
  return clientPrograms.value.has(programId);
};

// 切换专业在客户定校书中的状态
const toggleClientProgram = async (program) => {
  if (!selectedClient.value || addingToClientPrograms.value) return;
  
  const programId = program.program_id || program.id;
  const isInPrograms = isInClientPrograms(program);
  
  addingToClientPrograms.value = true;
  
  try {
    if (isInPrograms) {
      // 从定校书中移除
      if (selectedClient.value.isTemporary) {
        // 临时客户，只更新本地状态和缓存
        clientPrograms.value.delete(programId);
        clientProgramsList.value = clientProgramsList.value.filter(p =>
          (p.program_id || p.id) !== programId
        );
        // 更新缓存
        try {
          sessionStorage.setItem('quick_client_programs', JSON.stringify(clientProgramsList.value));
        } catch (error) {
          console.error('更新专业缓存失败:', error);
        }
      } else {
        // 正式客户，调用API
        await removeClientProgram(selectedClient.value.id_hashed, programId);

        // 移除成功后，重新从服务器获取最新的定校书数据
        await loadClientPrograms();
      }

      // 移除成功，无需提示
    } else {
      // 添加到定校书
      const programData = {
        program_id: programId,
        school_name_cn: program.学校中文名,
        school_name_en: program.学校英文名,
        program_name_cn: program.专业中文名,
        program_name_en: program.专业英文名,
        program_category: program.专业大类,
        school_region: program.location,
        degree: program.degree || '硕士',
        // 可以添加更多字段
        notes: currentResultMode.value === 'ai' ? program.reason : '专业库匹配推荐'
      };
      
      if (selectedClient.value.isTemporary) {
        // 临时客户，只更新本地状态和缓存
        clientPrograms.value.add(programId);

        // 为悬浮框创建完整的数据结构（包含所有字段）
        const floatBoxData = {
          id: `temp_${Date.now()}_${programId}`, // 临时ID
          client_id: selectedClient.value.id_hashed,
          program_id: programId,
          created_at: new Date().toISOString(),
          notes: programData.notes,

          // 使用真实的专业数据
          school_name_cn: program.学校中文名,
          school_name_en: program.学校英文名,
          program_name_cn: program.专业中文名,
          program_name_en: program.专业英文名,
          program_category: program.专业大类,
          school_region: program.location,
          degree: program.degree || '硕士',
          school_qs_rank: program.ranking?.replace(/QS排名 #/, '') || '',
          school_logo_url: program.school_logo_url || '',
          program_website: program.项目官网 || '',
          gpa_requirements: program.GPA要求 || '',
          language_requirements: program.语言要求 || '',
          application_requirements: program.申请要求 || '',
          program_tuition: program.tuitionRange || '',
          enrollment_time: program.入学时间 || '',
          program_duration: program.项目时长 || ''
        };

        // 更新悬浮框的程序列表
        clientProgramsList.value.push(floatBoxData);

        // 更新缓存
        try {
          sessionStorage.setItem('quick_client_programs', JSON.stringify(clientProgramsList.value));
        } catch (error) {
          console.error('更新专业缓存失败:', error);
        }
      } else {
        // 正式客户，调用API
        await addClientProgram(selectedClient.value.id_hashed, programData);

        // 添加成功后，重新从服务器获取最新的定校书数据
        // 这样可以确保显示的数据与服务器一致，包括正确的ID等信息
        await loadClientPrograms();
      }

      // 添加成功，无需提示
    }
  } catch (error) {
    console.error('定校书操作失败:', error);
    ElMessage.error(isInPrograms ? '移除失败，请重试' : '添加失败，请重试');
  } finally {
    addingToClientPrograms.value = false;
  }
};

// 点击外部关闭客户选择器
// 查看专业详情
const handleViewProgramDetails = async (program) => {
  // 阻止事件冒泡
  event?.stopPropagation?.();

  detailDialogVisible.value = true;
  detailLoading.value = true;

  try {
    // 构建程序ID，优先使用program_id，否则使用id
    const programId = program.program_id || program.id;

    if (programId) {
      // 加载专业详情
      const programDetail = await getProgramDetail(programId);

      // 保留原始数据中的school_logo_url字段，或者重新获取
      if (program.school_logo_url && !programDetail.school_logo_url) {
        programDetail.school_logo_url = program.school_logo_url;
      } else if (!programDetail.school_logo_url && programDetail.school_name_cn) {
        // 如果没有logo信息，尝试从数据库获取
        try {
          const logoUrl = await logoStore.fetchSchoolLogoFromDB(programDetail.school_name_cn);
          if (logoUrl) {
            programDetail.school_logo_url = logoUrl;
          }
        } catch (error) {
          console.warn('获取学校logo失败:', error);
        }
      }

      selectedProgram.value = programDetail;
    } else {
      // 如果没有ID，使用现有数据并格式化
      selectedProgram.value = formatProgramData(program);
    }
  } catch (error) {
    console.error('加载专业详情失败:', error);
    ElMessage.error('加载专业详情失败');
    selectedProgram.value = null;
  } finally {
    detailLoading.value = false;
  }
};

// 关闭专业详情弹窗
const handleCloseDetail = () => {
  detailDialogVisible.value = false;
  setTimeout(() => {
    selectedProgram.value = null;
  }, 300);
};

const handleClickOutside = (event) => {
  const clientSelector = event.target.closest('.client-selector');
  if (!clientSelector) {
    showClientSelector.value = false;
  }
};



// 监听身份变化，自动刷新数据
watch(
  () => authStore.currentIdentity,
  async (newIdentity, oldIdentity) => {
    // 如果有新的身份信息，就刷新数据
    if (newIdentity) {
      // 检查是否是真正的身份变化
      const isIdentityChange = oldIdentity && newIdentity && 
        (newIdentity.identity_type !== oldIdentity.identity_type || 
         newIdentity.organization_id !== oldIdentity.organization_id);
      
      if (isIdentityChange) {
        console.log('选校匹配检测到身份切换，自动刷新数据:', {
          old: oldIdentity,
          new: newIdentity
        });
        
        // 身份切换时静默刷新数据
        try {
          // 如果当前是自选校模式，刷新筛选数据
          if (matchingMode.value === 'filter') {
            await loadFilterData(true); // 静默刷新
          }
          
          // 重新验证套餐权限
          AISelectionValidationService.clearCache();
          await checkPackageAccess();
        } catch (error) {
          console.warn('选校匹配身份切换后数据刷新失败:', error);
        }
      }
    }
  },
  { deep: true } // 深度监听对象变化
);

// 自动刷新数据的方法
const refreshAllData = async (silent = true) => {
  try {
    const tasks = [];
    
    // 如果当前是自选校模式，刷新筛选数据
    if (matchingMode.value === 'filter') {
      tasks.push(loadFilterData(silent));
    }
    
    // 重新验证套餐权限
    if (!silent) {
      AISelectionValidationService.clearCache();
      tasks.push(checkPackageAccess());
    }
    
    if (tasks.length > 0) {
      await Promise.allSettled(tasks);
    }
    
    if (!silent) {
      console.log('选校匹配数据刷新完成');
    }
  } catch (error) {
    if (!silent) {
      console.error('选校匹配数据刷新失败:', error);
    }
  }
};

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', () => {
    recalculateHeights();
    checkScreenSize();
  });
  window.removeEventListener('beforeunload', cleanupSSEConnection);
  document.removeEventListener('click', handleClickOutside);
  cleanupSSEConnection();
});
</script> 

<style scoped>
/* === 选校匹配页面样式 === */
/* 页面容器 */
.school-assistant-container {
  background-color: #F9FAFB;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 1.5rem;
  width: 100%;
}

/* === 顶部导航区 === */
.page-header {
  background: linear-gradient(135deg, #4F46E5 0%, #4338CA 100%);
  color: white;
  padding: 2rem 0;
  margin: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.page-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #FFFFFF;
}

.page-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  color: #FFFFFF;
}

.stats-section {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #FFFFFF;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
  color: #FFFFFF;
}

/* === 主要内容区 === */
.main-content {
  padding: 2rem 0;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .page-header {
    padding: 1.5rem 0;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-section {
    gap: 1rem;
  }

  .main-content {
    padding: 1.5rem 0;
  }
}

/* === 原有样式保留 === */
/* 全局覆盖Element Plus的主题色 - 不使用scoped */

/* 调整表单控件样式 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  @apply !shadow-none bg-slate-50 border border-gray-200 hover:border-gray-300 transition-colors duration-150 rounded-md;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  @apply bg-white border-[#4F46E5] ring-1 ring-[#4F46E5]/50;
  box-shadow: none !important;
}

/* 表单区域折叠/展开动画效果 */
.cursor-pointer {
  @apply hover:text-primary transition-colors duration-200;
}

/* 扩展图标的旋转动画 */
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease-in-out;
}
.material-icons-outlined.text-gray-400 {
  transition: transform 0.3s ease-in-out;
}

/* 折叠区域过渡效果 - 改进以防止抖动 */
.form-section-content {
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease-in-out;
  will-change: max-height, opacity;
  overflow: hidden;
  position: relative;
}

/* Sticky侧边栏样式 */
.sticky-sidebar {
  position: sticky;
  top: 1rem;
  height: fit-content;
  max-height: calc(100vh - 64px - 2rem); /* 减去头部导航栏高度64px */
  overflow-y: auto;
  z-index: 10;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Webkit浏览器滚动条样式 - MacBook优化 */
.sticky-sidebar::-webkit-scrollbar {
  width: 8px; /* 稍微加宽以便MacBook触控板操作 */
}

.sticky-sidebar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.02); /* 轻微背景色便于识别 */
  border-radius: 4px;
}

.sticky-sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
  transition: background-color 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1); /* 增加边框提高可见性 */
}

.sticky-sidebar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* 在小屏幕上禁用sticky定位 - 针对MacBook优化 */
@media (max-width: 1279px) {
  .sticky-sidebar {
    position: static;
    max-height: none;
    overflow-y: visible;
  }
}

/* MacBook Air和MacBook Pro小屏幕特殊处理 */
@media (min-width: 1280px) and (max-width: 1512px) {
  .sticky-sidebar {
    max-height: calc(100vh - 64px - 1.5rem); /* 稍微减少上边距适配小屏幕 */
    top: 0.75rem; /* 减少顶部间距 */
  }
}

/* MacBook Air 11寸特殊适配 (1366x768) */
@media (min-width: 1280px) and (max-width: 1400px) and (max-height: 800px) {
  .sticky-sidebar {
    max-height: calc(100vh - 64px - 1rem); /* 进一步减少边距 */
    top: 0.5rem; /* 更小的顶部间距 */
  }
}

/* 高度不足时的保护措施 - 仅对小屏幕设备 */
@media (max-width: 1440px) and (max-height: 700px) {
  .sticky-sidebar {
    position: static;
    max-height: none;
    overflow-y: visible;
  }
}

/* 平板设备优化 */
@media (max-width: 1024px) {
  .sticky-sidebar {
    position: static;
    max-height: none;
    overflow-y: visible;
  }

  .main-content-area {
    margin-top: 1rem;
  }
}

/* 手机设备优化 */
@media (max-width: 768px) {
  .sticky-sidebar {
    position: static;
    max-height: none;
    overflow-y: visible;
    margin-bottom: 1rem;
  }
}

/* 主内容区域样式 */
.main-content-area {
  /* 确保内容区域有足够的最小宽度 */
  min-width: 0;
  flex: 1;
}

/* 确保整个表单容器具有固定宽度，防止滚动条引起的布局变化 */
.pro-card {
  @apply overflow-x-hidden bg-white rounded-lg shadow-sm border border-gray-100;
  width: 100%;
  margin-bottom: 1rem;
  /* 添加背景色确保可读性 */
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.pro-card-header {
  @apply border-b border-gray-100 flex items-center;
  padding: 0 16px;
  height: 3.5rem;
}

.pro-card-title {
  @apply text-gray-800 font-medium flex items-center;
  font-weight: 600;
}

.pro-card-body {
  @apply p-4;
}

.pro-card-footer {
  @apply p-3.5 border-t border-gray-100 flex justify-end;
}

/* 学校Logo样式 */
.school-logo-container {
  @apply flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm;
}

.school-logo-container img {
  @apply h-10 w-10 object-contain;
}

.school-logo-container span {
  @apply text-2xl font-bold text-gray-400;
  line-height: 1;
}

/* 优化Logo显示 */
.school-card .school-logo-container {
  @apply bg-gray-50;
}

.school-card .school-logo-container img {
  @apply max-w-full max-h-full p-1;
  filter: grayscale(0.1);
}

.school-card:hover .school-logo-container img {
  filter: grayscale(0);
}

/* 浮动动画效果 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 学校卡片样式增强 */
.school-card {
  @apply transform transition-all duration-300;
}

.school-card:hover {
  @apply shadow-lg -translate-y-1 border-[#4F46E5]/20;
}

/* 匹配度标签动画 */
.school-card .px-2\.5 {
  @apply transition-all duration-300;
}

.school-card:hover .px-2\.5 {
  @apply scale-105;
}

/* 优化按钮悬停效果 */
.el-button:hover {
  @apply transform scale-105 transition-transform duration-200;
}





/* === 响应式设计优化 === */
@media (max-width: 640px) {
  .ranking-badge {
    padding: 0.375rem 0.5rem;
    gap: 0.375rem;
    min-width: 3.5rem;
    height: 2rem;
  }

  .ranking-label {
    font-size: 0.625rem;
  }

  .ranking-number {
    font-size: 0.75rem;
  }
}

/* 覆盖 Element Plus 下拉选择框的蓝色主题为紫色 */
:deep(.el-select .el-select__wrapper.is-focused) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}
:deep(.el-select-dropdown__item.is-selected.is-hovering) {
  @apply !bg-[#4F46E5]/20;
}

/* 多选标签 */
:deep(.el-select .el-tag.is-info) {
  @apply !bg-[#4F46E5]/10 !text-[#4F46E5] !border-[#4F46E5]/20;
}

/* 多选标签的关闭按钮 */
:deep(.el-select .el-tag.is-info .el-tag__close) {
  @apply !text-[#4F46E5] hover:!bg-[#4F46E5]/20;
}

/* 禁用覆盖Element Plus的默认主题色变量 */
:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #7C7AED !important;
  --el-color-primary-light-5: #9D9BF2 !important;
  --el-color-primary-light-7: #BEBDF7 !important;
  --el-color-primary-light-8: #CFCEF9 !important;
  --el-color-primary-light-9: #DFDFFB !important;
  --el-color-primary-dark-2: #3F37B7 !important;
}

/* 全局覆盖Element Plus主题色 */
:root {
  --el-color-primary: #4F46E5 !important;
}

/* === 响应式布局优化（MacBook等中等屏幕） === */
@media (min-width: 1024px) and (max-width: 1279px) {
  /* 在lg到xl之间的屏幕（如MacBook）上的优化 */
  .collapsed-form {
    font-size: 0.875rem;
  }
  
  .collapsed-form .animated-input-container {
    margin-bottom: 0.375rem;
  }
  
  .collapsed-form .px-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  
  .collapsed-form .py-3 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  /* 在收起状态下减小标题字体 */
  .collapsed-form h4 {
    font-size: 0.8125rem;
  }
  
  /* 优化按钮间距 */
  .collapsed-form .pro-card-footer {
    padding: 0.75rem 1rem;
  }
  
  /* 优化网格布局间距 */
  .collapsed-form .grid.grid-cols-2 {
    gap: 0.75rem;
  }
}

/* 确保过渡动画流畅 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 专业卡片特殊样式 */
.program-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.program-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.program-card .details-container {
  transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.3s ease;
  overflow: hidden !important;
  will-change: height, opacity;
}

/* 收藏按钮悬停效果 */
.program-card .el-button:hover {
  transform: translateY(-1px);
}

/* 学校分组卡片样式 */
.school-group-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.school-group-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.school-group-header {
  transition: background-color 0.2s ease;
}

.school-group-header:hover {
  background: linear-gradient(90deg, #f8fafc 0%, #ffffff 50%, #f8fafc 100%);
}

.school-programs-container {
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.3s ease;
}

/* 优化边框颜色 */
.border-gray-150 {
  border-color: #f1f3f4;
}

.bg-gray-25 {
  background-color: #fafbfc;
}

/* 确保过渡动画流畅 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 客户档案下拉框样式 */
.client-dropdown {
  background: white !important;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e5e7eb !important;
  /* 预留滚动条空间，避免内容增多时宽度抖动 */
  scrollbar-gutter: stable;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

/* Webkit浏览器滚动条样式 */
.client-dropdown::-webkit-scrollbar {
  width: 6px;
}

.client-dropdown::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.client-dropdown::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.client-dropdown::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 客户定校书悬浮框滚动区域样式 */
.client-programs-scroll {
  /* 预留滚动条空间，避免内容增多时宽度抖动 */
  scrollbar-gutter: stable;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

/* Webkit浏览器滚动条样式 */
.client-programs-scroll::-webkit-scrollbar {
  width: 6px;
}

.client-programs-scroll::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.client-programs-scroll::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.client-programs-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.client-selector {
  position: relative;
  z-index: 1;
}

/* 客户档案区域特殊处理 - 允许下拉框溢出 */
.client-section-content {
  overflow: visible !important;
}

/* 定校书右侧按钮样式 */
.school-book-btn {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(229, 231, 235, 0.8);
  box-shadow: 0 8px 25px -5px rgba(79, 70, 229, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.school-book-btn:hover {
  box-shadow: 0 20px 25px -5px rgba(79, 70, 229, 0.15),
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateX(-2px) scale(1.05);
}

.school-book-btn:active {
  transform: translateX(-1px) scale(0.95);
  box-shadow: 0 8px 15px -5px rgba(79, 70, 229, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 移除按钮点击波纹效果 - 不显示紫色光圈 */



/* 小尺寸QS排名组件样式（用于定校书侧边栏） */
.ranking-badge-small {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(251, 191, 36, 0.4);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(4px);
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 3.5rem;
  height: 1.75rem;
  align-self: center;
}

.ranking-badge-small:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px) scale(1.02);
  background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%) !important;
  border-color: rgba(251, 191, 36, 0.6) !important;
}

.ranking-label-small {
  font-size: 0.5rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1;
}

.ranking-number-small {
  font-size: 0.6875rem;
  font-weight: 700;
  color: #475569;
  line-height: 1;
  font-feature-settings: 'tnum';
}

/* 侧边栏滚动条样式 */
.school-book-sidebar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

.school-book-sidebar::-webkit-scrollbar {
  width: 8px; /* 稍微加宽以便MacBook触控板操作 */
}

.school-book-sidebar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.02); /* 轻微背景色便于识别 */
  border-radius: 4px;
}

.school-book-sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
  transition: background-color 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1); /* 增加边框提高可见性 */
}

.school-book-sidebar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.client-programs-float:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 
              0 10px 20px -5px rgba(0, 0, 0, 0.1),
              0 0 0 1px rgba(79, 70, 229, 0.1);
  transform: translateY(-3px) scale(1.02);
}



/* 项目列表项入场动画 */
@keyframes slide-in-item {
  0% {
    opacity: 0;
    transform: translateX(-15px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 侧边栏滑入滑出动画 */
.sidebar-slide-enter-active,
.sidebar-slide-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar-slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.sidebar-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.sidebar-container {
  will-change: transform, opacity;
}

/* 遮罩层淡入淡出动画 */
.overlay-fade-enter-active,
.overlay-fade-leave-active {
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.overlay-fade-enter-from,
.overlay-fade-leave-to {
  opacity: 0;
}

.overlay-backdrop {
  will-change: opacity;
}

/* 项目列表项动画 */
.program-item-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.program-item-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.program-item-enter-from {
  opacity: 0;
  transform: translateX(-20px) scale(0.95);
}

.program-item-leave-to {
  opacity: 0;
  transform: translateX(20px) scale(0.95);
  height: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.program-item-move {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.program-item {
  will-change: transform, opacity, height;
}

/* 响应式调整悬浮框位置 */
@media (max-width: 768px) {
  .client-programs-float {
    position: fixed !important;
    bottom: 1rem !important;
    right: 1rem !important;
    left: 1rem !important;
    width: auto !important;
    max-width: none !important;
  }
}

@media (max-width: 480px) {
  .client-programs-float {
    bottom: 0.5rem !important;
    right: 0.5rem !important;
    left: 0.5rem !important;
  }
}

/* 确保悬浮框在所有元素之上 */
.client-programs-float {
  z-index: 9999 !important;
}
</style>

<style>
/* 全局覆盖Element Plus的主题色 - 不使用scoped */
.el-select-dropdown__item.is-hovering {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

.el-select-dropdown__item.is-selected {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

.el-select-dropdown__item.is-selected::after {
  background: #4F46E5 !important;
}

.el-select-dropdown__item.is-selected.is-hovering {
  background-color: rgba(79, 70, 229, 0.2) !important;
  color: #4F46E5 !important;
}

/* 成绩制式默认百分制的淡色样式 */
.el-select .el-input__inner::placeholder {
  color: #9ca3af !important;
}

.el-select .el-input__inner {
  color: #6b7280 !important;
}

/* 智能选校复选框自定义样式 */
.ai-selection-checkbox .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

.ai-selection-checkbox .el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: #4F46E5 !important;
}

.ai-selection-checkbox .el-checkbox__inner:hover {
  border-color: #4F46E5 !important;
}

/* === 自选校模式样式 === */
/* 模式切换容器 - 防止抖动 */
.ai-mode-container {
  min-height: 400px;
  transition: all 0.3s ease;
}

.filter-mode-container {
  min-height: 400px;
  transition: all 0.3s ease;
}

/* 模式切换平滑过渡 */
.pro-card-body {
  transition: all 0.3s ease;
}

/* 防止模式切换时的布局跳跃 */
.pro-card {
  position: relative;
  overflow: hidden;
}

/* === 专业方向动画效果 === */
/* 专业方向组的淡入淡出动画 */
.direction-fade-enter-active,
.direction-fade-leave-active {
  transition: all 0.3s ease;
}

.direction-fade-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.direction-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.direction-fade-enter-to,
.direction-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

/* 专业方向列表项的动画 */
.direction-list-enter-active,
.direction-list-leave-active {
  transition: all 0.25s ease;
}

.direction-list-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(-5px);
}

.direction-list-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-5px);
}

.direction-list-enter-to,
.direction-list-leave-from {
  opacity: 1;
  transform: scale(1) translateY(0);
}

/* 专业方向组容器优化 */
.direction-group {
  transform-origin: top;
}

/* 移除可能导致抖动的高度变化 */
.filter-group {
  transition: none !important;
}

.filter-tags {
  transition: none !important;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 1.5rem;
}

.search-container {
  position: relative;
}

.search-input {
  width: 100%;
  margin-bottom: 0.5rem;
}



/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group-header {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.filter-group-header .material-icons-outlined {
  color: #4F46E5;
  font-size: 1rem;
  margin-right: 0.5rem;
}

/* === 地区和专业方向标签 === */
.filter-tags {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.filter-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #E5E7EB;
  background: #FFFFFF;
  color: #6B7280;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  text-align: center;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.filter-tag:hover {
  border-color: #4F46E5;
  color: #4F46E5;
  background: rgba(79, 70, 229, 0.05);
}

.filter-tag.tag-selected {
  background: #4F46E5 !important;
  color: #FFFFFF !important;
  border-color: #4F46E5 !important;
  font-weight: 500;
}

/* === 专业大类样式 - 与ProgramDatabase.vue完全一致 === */
.category-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #E5E7EB;
  background: #FFFFFF;
  color: #6B7280;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-align: center;
  width: 100%;
}

.category-tag:hover {
  border-color: #4F46E5;
  color: #4F46E5;
  background: rgba(79, 70, 229, 0.05);
}

.category-tag .category-name {
  font-weight: 500;
  font-size: 0.875rem;
}

.category-tag .category-arrow .material-icons-outlined {
  font-size: 1rem;
  transition: transform 0.2s ease;
}

.category-tag.tag-selected {
  background: #4F46E5 !important;
  color: #FFFFFF !important;
  border-color: #4F46E5 !important;
  font-weight: 500;
}

.category-tag.tag-selected .category-arrow .material-icons-outlined {
  color: #FFFFFF !important;
}

/* === 已选择专业方向样式 - 与ProgramDatabase.vue完全一致 === */
.selected-tags {
  background: #F8FAFC;
  border: 1px solid #E2E8F0;
  border-radius: 0.5rem;
  padding: 1rem;
}

.selected-tag-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.category-prefix {
  font-weight: 600;
  color: #4F46E5;
  white-space: nowrap;
  font-size: 0.875rem;
  line-height: 2;
}

.selected-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}

.selected-tag {
  background: #4F46E5 !important;
  color: #FFFFFF !important;
  border: none !important;
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 1rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.selected-tag:hover {
  background: #4338CA !important;
  transform: scale(1.02);
}

.selected-tag .el-tag__close {
  color: #FFFFFF !important;
  margin-left: 0.5rem !important;
  font-size: 0.75rem !important;
}

.selected-tag .el-tag__close:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
}

/* === 操作按钮样式 - 与ProgramDatabase.vue完全一致 === */
.filter-actions {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #F1F5F9;
}

.action-btn {
  min-width: 120px !important;
  height: 36px !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.btn-secondary {
  background-color: #F3F4F6;
  border: 1px solid #D1D5DB;
  color: #374151;
}

.btn-secondary:hover {
  background-color: #E5E7EB;
  border-color: #9CA3AF;
}

/* 卡片网格样式 */
.cards-container {
  width: 100%;
}

.cards-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.skeleton-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.skeleton-card {
  border: 1px solid #E5E7EB;
  border-radius: 0.75rem;
  overflow: hidden;
  background-color: #FFFFFF;
}

.skeleton-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.empty-content {
  text-align: center;
  color: #6B7280;
}

.empty-icon {
  font-size: 3rem;
  color: #D1D5DB;
  margin-bottom: 1rem;
}

.empty-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.empty-content p {
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.btn-primary {
  background-color: #4F46E5;
  border-color: #4F46E5;
  color: #FFFFFF;
}

.btn-primary:hover {
  background-color: #4338CA;
  border-color: #4338CA;
}

/* === 现代化QS排名组件样式 === */
.ranking-badge {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.75rem;
  border: 1px solid rgba(251, 191, 36, 0.4);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(4px);
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 5rem;
  height: 2.25rem;
  align-self: center;
}

.ranking-badge:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px) scale(1.05);
  background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%) !important;
  border-color: rgba(251, 191, 36, 0.6) !important;
}

.ranking-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #78716c;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  line-height: 1;
}

.ranking-number {
  font-size: 0.875rem;
  font-weight: 700;
  color: #78716c;
  line-height: 1;
  font-feature-settings: 'tnum';
}

/* === 国家/地区筛选按钮样式 === */
.region-filter-container {
  margin-bottom: 1rem;
}

.region-filter-container button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  font-feature-settings: 'tnum';
}

.region-filter-container button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.region-filter-container button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* === 响应式设计 - 与ProgramDatabase.vue完全一致 === */
@media (max-width: 768px) {
  .filter-tags {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-actions {
    justify-content: center;
  }

  .action-btn {
    width: 100% !important;
  }

  /* 移动端国家/地区筛选按钮优化 */
  .region-filter-container .flex {
    gap: 0.5rem;
  }
  
  .region-filter-container button {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
  
  .region-filter-container button span {
    font-size: 0.75rem;
  }
}

/* === 响应式布局优化（MacBook等中等屏幕） === */
@media (min-width: 1024px) and (max-width: 1279px) {
  /* 在lg到xl之间的屏幕（如MacBook）上的优化 */
  .filter-group {
    margin-bottom: 0.75rem;
  }

  .filter-group-header {
    font-size: 0.8125rem;
    margin-bottom: 0.375rem;
  }

  .filter-tags {
    gap: 0.375rem;
  }

  .filter-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .category-tag {
    padding: 0.375rem 0.75rem;
  }
}

/* === 分页组件样式 === */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1.5rem 1.5rem;
  margin-top: 1rem;
}

.pagination-component {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 6px;
  --el-pagination-button-color: #606266;
  --el-pagination-button-bg-color: #ffffff;
  --el-pagination-button-disabled-color: #c0c4cc;
  --el-pagination-button-disabled-bg-color: #ffffff;
  --el-pagination-hover-color: #4F46E5;
  --el-color-primary: #4F46E5;
}

/* 分页组件样式 - 简化版本（只有页码导航） */

/* 分页按钮样式 */
.pagination-component .el-pager li {
  background-color: #ffffff;
  border: 1px solid #ddd;
  margin: 0 2px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.pagination-component .el-pager li:hover {
  background-color: #4F46E5;
  color: #ffffff;
  border-color: #4F46E5;
}

.pagination-component .el-pager li.is-active {
  background-color: #4F46E5;
  color: #ffffff;
  border-color: #4F46E5;
}

.pagination-component .btn-prev,
.pagination-component .btn-next {
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.pagination-component .btn-prev:hover,
.pagination-component .btn-next:hover {
  background-color: #4F46E5;
  color: #ffffff;
  border-color: #4F46E5;
}

/* 客户选择验证错误样式 */
.client-selector.has-error {
  position: relative;
}

.client-selector.has-error :deep(.animated-input-container) {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.client-selector.has-error :deep(.floating-label-input) {
  border-color: #ef4444 !important;
}

.client-error-message {
  animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -10px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>