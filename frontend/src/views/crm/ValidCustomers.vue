<template>
  <div class="crm-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span>CRM系统</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">有效客户</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">有效客户管理</h2>
    </div>

    <!-- 主要内容卡片 -->
    <div class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">people</span>
          有效客户列表
        </div>
        
        <!-- 切换按钮（仅有权限用户可见） -->
        <div v-if="canViewAllCustomers">
          <el-button 
            size="small" 
            type="primary"
            @click="showOnlyMyCustomers ? showAllCustomers() : showMyCustomers()"
            :loading="toggleLoading"
            :disabled="toggleLoading"
          >
            <span v-if="!toggleLoading" class="material-icons-outlined text-sm mr-1">
              {{ showOnlyMyCustomers ? 'visibility' : 'person' }}
            </span>
            {{ showOnlyMyCustomers ? '查看所有有效客户' : '查看我的有效客户' }}
          </el-button>
        </div>
      </div>

      <div class="pro-card-body">
        <!-- 个人身份访问提示 -->
        <div v-if="!isOrganizationIdentity" class="text-center py-16">
          <div class="max-w-md mx-auto">
            <div class="mb-6">
              <span class="material-icons-outlined text-6xl text-gray-300">people</span>
            </div>
            <h3 class="text-xl font-medium text-gray-900 mb-4">需要组织账号访问</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              CRM系统功能仅限组织账号使用。请切换到组织身份或联系管理员获取组织账号权限。
            </p>
            <div class="space-y-3">
              <el-button type="primary" @click="$router.push('/account/profile')">
                <span class="material-icons-outlined text-sm mr-1">switch_account</span>
                切换身份
              </el-button>
              <div class="text-sm text-gray-500">
                如需创建组织账号，请前往账户管理页面
              </div>
            </div>
          </div>
        </div>

        <!-- 组织身份内容 -->
        <div v-else>
        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div 
            @click="handleStatusCardClick('')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-cyan-50 to-blue-50',
              statusFilter === '' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-cyan-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-cyan-600">people</span>
              <span class="text-sm font-medium text-slate-700">总有效客户</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.total || 0 }}</div>
          </div>
          
          <div 
            @click="handleStatusCardClick('high_intent')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-violet-50 to-purple-50',
              statusFilter === 'high_intent' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-violet-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-violet-600">trending_up</span>
              <span class="text-sm font-medium text-slate-700">高意向</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.intent_level_distribution?.high || 0 }}</div>
          </div>
          
          <div 
            @click="handleStatusCardClick('following')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-cyan-50 to-blue-50',
              statusFilter === 'following' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-cyan-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-cyan-600">schedule</span>
              <span class="text-sm font-medium text-slate-700">跟进中</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.assignment_distribution?.assigned || 0 }}</div>
          </div>
          
          <div 
            @click="handleStatusCardClick('pending')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-rose-50 to-pink-50',
              statusFilter === 'pending' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-rose-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-rose-600">assignment</span>
              <span class="text-sm font-medium text-slate-700">待签约</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.assignment_distribution?.unassigned || 0 }}</div>
          </div>
          
          <div 
            @click="handleStatusCardClick('invalid')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-gray-50 to-slate-50',
              statusFilter === 'invalid' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-gray-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-gray-600">cancel</span>
              <span class="text-sm font-medium text-slate-700">无效资源</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.invalid_count || 0 }}</div>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="border-t border-gray-200 my-8"></div>

        <!-- 筛选区域 -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索姓名、电话"
            clearable
            class="w-full sm:w-64"
            size="default"
          >
            <template #prefix>
              <span class="material-icons-outlined text-gray-400 text-sm">search</span>
            </template>
          </el-input>

          <el-select 
            v-model="intentLevelFilter" 
            placeholder="意向等级" 
            clearable 
            size="default"
            class="w-full sm:w-48"
          >
            <el-option label="全部等级" value="" />
            <el-option label="高意向" value="high" />
            <el-option label="中意向" value="medium" />
            <el-option label="低意向" value="low" />
          </el-select>

          <el-select 
            v-model="salesStaffFilter" 
            placeholder="销售人员" 
            clearable 
            size="default"
            class="w-full sm:w-48"
          >
            <el-option label="全部人员" value="" />
            <el-option label="David" value="David" />
            <el-option label="Sarah" value="Sarah" />
            <el-option label="Michael" value="Michael" />
          </el-select>

          <el-select 
            v-model="statusFilter" 
            placeholder="客户状态" 
            clearable 
            size="default"
            class="w-full sm:w-48"
          >
            <el-option label="全部状态" value="" />
            <el-option label="跟进中" value="following" />
            <el-option label="待签约" value="pending" />
            <el-option label="已签约" value="signed" />
            <el-option label="无效资源" value="invalid" />
          </el-select>
          
          <el-button 
            @click="handleResetFilter"
            class="border-gray-300 text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
          >
            <span class="material-icons-outlined text-sm mr-1">refresh</span>
            重置
          </el-button>
        </div>

        <!-- 表头 -->
        <div class="hidden md:flex items-center bg-gray-100 rounded-lg px-4 py-3 mb-4 text-sm font-medium text-gray-600 min-h-[60px]">
          <div class="flex-1 min-w-0 grid grid-cols-12 gap-2 items-center">
            <div class="col-span-1 flex items-center">客户信息</div>
            <div class="col-span-2 flex items-center justify-center">院校专业</div>
            <div class="col-span-1 flex items-center justify-center">申请地区</div>
            <div class="col-span-1 flex items-center justify-center">销售人员</div>
            <div class="col-span-1 flex items-center justify-center">客户状态</div>
            <div class="col-span-2 flex items-center justify-center">分配时间</div>
            <div class="col-span-2 flex items-center justify-center">跟进时间</div>
            <div class="col-span-1 flex items-center justify-center">无效客户</div>
            <div class="col-span-1 flex items-center justify-center">操作</div>
          </div>
        </div>

        <!-- 有效客户横排卡片列表 -->
        <transition 
          name="customer-list"
          mode="out-in"
          appear
        >
          <div 
            :key="statusFilter + searchKeyword + salesStaffFilter + intentLevelFilter"
            class="customer-list-wrapper space-y-4"
          >
          <div
            v-for="customer in paginatedCustomers"
            :key="customer.id"
            class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100 cursor-pointer"
            @click="handleCustomerCardClick(customer)"
          >
            <div class="flex items-center min-h-[60px]">
              <!-- 内容区域 -->
              <div class="flex-1 min-w-0 grid grid-cols-1 md:grid-cols-12 gap-2 items-center">
                <!-- 客户信息列 -->
                <div class="col-span-12 md:col-span-1 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">客户信息</div>
                    <div>
                      <h3 class="font-medium text-gray-900 text-sm">{{ customer.customer_name || customer.wechat_name || '未知客户' }}</h3>
                      <p class="text-xs text-gray-500">{{ customer.channel_source }}</p>
                    </div>
                  </div>
                </div>

                <!-- 院校专业列 -->
                <div class="col-span-12 md:col-span-2 flex items-center justify-center">
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">院校专业</div>
                    <div class="text-sm text-gray-900">
                      <div class="font-medium truncate">{{ customer.current_school || '-' }}</div>
                      <div class="text-xs text-gray-500 truncate mt-0.5">{{ customer.current_major || '-' }}</div>
                    </div>
                  </div>
                </div>

                <!-- 申请地区列 -->
                <div class="col-span-12 md:col-span-1 flex items-center justify-center">
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">申请地区</div>
                    <div class="text-sm text-gray-900">{{ customer.intent_regions?.join('，') || '-' }}</div>
                  </div>
                </div>

                <!-- 销售人员列 -->
                <div class="col-span-12 md:col-span-1 flex items-center justify-center">
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">销售人员</div>
                    <div class="flex justify-center">
                      <span 
                        v-if="customer.sales_staff_user_id"
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {{ getStaffName(customer.sales_staff_user_id) }}
                      </span>
                      <span v-else class="text-sm text-gray-400">-</span>
                    </div>
                  </div>
                </div>

                <!-- 客户状态列 -->
                <div class="col-span-12 md:col-span-1 flex items-center justify-center">
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">客户状态</div>
                    <div class="flex justify-center">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getStatusClass(customer.assignment_status)"
                      >
                        {{ getStatusLabel(customer.assignment_status) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 分配时间列 -->
                <div class="col-span-12 md:col-span-2 flex items-center justify-center">
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">分配时间</div>
                    <div class="text-sm text-gray-700 whitespace-nowrap font-mono">{{ formatDateTime(customer.created_at) }}</div>
                  </div>
                </div>

                <!-- 跟进时间列 -->
                <div class="col-span-12 md:col-span-2 flex items-center justify-center">
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">跟进时间</div>
                    <div class="text-sm text-gray-700 whitespace-nowrap font-mono">{{ formatDateTime(customer.updated_at) }}</div>
                  </div>
                </div>

                <!-- 无效客户列 -->
                <div class="col-span-12 md:col-span-1 flex items-center justify-center" @click.stop>
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1 text-center">无效客户</div>
                    <div class="flex justify-center">
                      <el-checkbox
                        :model-value="!customer.is_valid"
                        @change="handleInvalidCustomerChange(customer)"
                        size="large"
                      />
                    </div>
                  </div>
                </div>

                <!-- 操作列 -->
                <div class="col-span-12 md:col-span-1 flex items-center justify-center" @click.stop>
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1 text-center">操作</div>
                    <div class="flex justify-center">
                      <el-dropdown @command="(command) => handleCustomerAction(command, customer)" trigger="click" class="customer-action-dropdown">
                        <el-button text size="small" class="action-dropdown-trigger hover:bg-gray-200 rounded-md p-1">
                          <span class="material-icons-outlined text-lg text-gray-600 hover:text-gray-800">more_vert</span>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu class="customer-dropdown-menu">
                            <el-dropdown-item command="edit" class="dropdown-menu-item">
                              <div class="flex items-center">
                                <span class="material-icons-outlined text-sm mr-2.5 text-green-600">edit</span>
                                <span class="text-sm font-medium text-gray-700">编辑客户</span>
                              </div>
                            </el-dropdown-item>
                            <el-dropdown-item 
                              v-if="customer.status !== 'signed'" 
                              command="sign" 
                              class="dropdown-menu-item"
                            >
                              <div class="flex items-center">
                                <span class="material-icons-outlined text-sm mr-2.5 text-blue-600">verified</span>
                                <span class="text-sm font-medium text-gray-700">转为签约客户</span>
                              </div>
                            </el-dropdown-item>

                            <el-dropdown-item command="delete" class="dropdown-menu-item delete-item" divided>
                              <div class="flex items-center">
                                <span class="material-icons-outlined text-sm mr-2.5 text-red-600">delete</span>
                                <span class="text-sm font-medium text-red-600">删除客户</span>
                              </div>
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="paginatedCustomers.length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-2">
              <span class="material-icons-outlined text-4xl">people</span>
            </div>
            <p class="text-gray-500">暂无有效客户数据</p>
          </div>
          </div>
        </transition>

        <!-- 分页 -->
        <div class="flex justify-between items-center mt-6" v-if="filteredCustomers.length > 0">
          <div class="text-sm text-gray-500">
            共 {{ filteredCustomers.length }} 位有效客户
          </div>
          <el-pagination
            v-if="totalPages > 1"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="filteredCustomers.length"
            layout="prev, pager, next"
            @current-change="handleCurrentChange"
            class="!mt-0"
          />
        </div>
      </div>
    </div>

    <!-- 客户反馈表单对话框 -->
    <el-dialog
      v-model="feedbackDialogVisible"
      :title="`客户反馈模板 - ${selectedCustomer?.customer_name || selectedCustomer?.wechat_name || '未知客户'}`"
      width="800px"
      class="simple-dialog compact-dialog"
      @close="handleCloseFeedbackDialog"
    >
      <div class="simple-content" v-if="selectedCustomer">
        <div class="flex items-center space-x-3 mb-6">
                      <div class="w-12 h-12 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] font-medium">
            {{ getCustomerInitials(selectedCustomer.customer_name || selectedCustomer.wechat_name) }}
          </div>
          <div>
            <div class="font-medium text-gray-900">{{ selectedCustomer.customer_name || selectedCustomer.wechat_name || '未知客户' }}</div>
            <div class="text-sm text-gray-500">{{ selectedCustomer.wechat_name }} | {{ selectedCustomer.phone }}</div>
          </div>
        </div>
        
        <el-form :model="feedbackForm" label-width="100px" class="space-y-6">
          <!-- 院校背景信息 -->
          <div class="form-section mb-6">
            <h4 class="form-section-title">院校背景</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <el-form-item label="客户姓名">
                <el-input
                  v-model="feedbackForm.customerName"
                  placeholder="请输入客户真实姓名"
                  maxlength="50"
                />
              </el-form-item>
              
              <el-form-item label="院校信息">
                <el-input
                  v-model="feedbackForm.education.university"
                  placeholder="请输入毕业院校或在读院校"
                  maxlength="100"
                />
              </el-form-item>
              
              <el-form-item label="专业信息">
                <el-input
                  v-model="feedbackForm.education.major"
                  placeholder="请输入专业名称"
                  maxlength="100"
                />
              </el-form-item>
              
              <el-form-item label="年级信息">
                <el-select
                  v-model="feedbackForm.education.grade"
                  placeholder="请选择年级"
                  class="w-full"
                >
                  <el-option label="大一" value="大一" />
                  <el-option label="大二" value="大二" />
                  <el-option label="大三上" value="大三上" />
                  <el-option label="大三下" value="大三下" />
                  <el-option label="大四" value="大四" />
                  <el-option label="毕业" value="毕业" />
                  <el-option label="研一" value="研一" />
                  <el-option label="研二" value="研二" />
                  <el-option label="研三" value="研三" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="均分信息">
                <el-input
                  v-model="feedbackForm.education.gpa"
                  placeholder="请输入GPA或均分"
                  maxlength="50"
                />
              </el-form-item>
            </div>
            
            <el-form-item label="语言信息">
              <el-input
                v-model="feedbackForm.education.language"
                type="textarea"
                placeholder="请输入语言成绩信息，如雅思、托福、四六级等"
                :rows="2"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </div>

          <!-- 申请意向信息 -->
          <div class="form-section mb-6">
            <h4 class="form-section-title">申请意向</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <el-form-item label="期望入学时间">
                <el-input
                  v-model="feedbackForm.application.expectedEntryTime"
                  placeholder="如：2024年9月"
                  maxlength="50"
                />
              </el-form-item>
              
              <el-form-item label="意向地区">
                <el-select
                  v-model="feedbackForm.application.intendedRegion"
                  placeholder="请选择意向地区"
                  class="w-full"
                >
                  <el-option label="香港" value="香港" />
                  <el-option label="英国" value="英国" />
                  <el-option label="美国" value="美国" />
                  <el-option label="澳洲" value="澳洲" />
                  <el-option label="新加坡" value="新加坡" />
                  <el-option label="加拿大" value="加拿大" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </el-form-item>
            </div>
            
            <el-form-item label="意向院校">
              <el-input
                v-model="feedbackForm.application.intendedUniversities"
                type="textarea"
                placeholder="请输入意向申请的院校，可输入多个"
                :rows="2"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="意向专业">
              <el-input
                v-model="feedbackForm.application.intendedMajor"
                placeholder="请输入意向申请的专业"
                maxlength="100"
              />
            </el-form-item>
            
            <el-form-item label="备注">
              <el-input
                v-model="feedbackForm.application.notes"
                type="textarea"
                placeholder="其他备注信息"
                :rows="2"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </div>

          <!-- 原有反馈信息 -->
          <div class="form-section">
            <h4 class="form-section-title">客户反馈</h4>
            <el-form-item label="预算">
              <el-input
                v-model="feedbackForm.budget"
                type="textarea"
                placeholder="中介预算，对比机构的中介费，留学学费+生活费等"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="动机">
              <el-input
                v-model="feedbackForm.motivation"
                type="textarea"
                placeholder="考公考编/上海落户/体验/想去港大港中文/申请不到好的也没必要出去了/不管怎么样都要出去/地区的选择/专业的考虑等"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="紧迫性">
              <el-input
                v-model="feedbackForm.urgency"
                type="textarea"
                placeholder="最早什么时候能做决定，最晚什么时候做决定，原因是什么"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="对比">
              <el-input
                v-model="feedbackForm.comparison"
                type="textarea"
                placeholder="线上线下，机构价格，机构名称，别的机构好在哪/不好在哪，学生还要再对比多久"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="父母">
              <el-input
                v-model="feedbackForm.parents"
                type="textarea"
                placeholder="父母主要/次要做决定，父母关心的问题，有没有熟人机构问题"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="feedbackDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSaveFeedback"
            :loading="saving"
          >
            保存反馈
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 客户预览悬浮框 -->
    <div 
      v-if="previewOverlayVisible && selectedCustomer"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click.self="handleClosePreviewOverlay"
    >
      <div class="bg-white rounded-xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden">
        <!-- 预览框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] font-bold text-lg">
              {{ getCustomerInitials(selectedCustomer.customer_name || selectedCustomer.wechat_name) }}
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900">{{ selectedCustomer.customer_name || selectedCustomer.wechat_name || '未知客户' }}</h3>
              <p class="text-sm text-gray-600">{{ selectedCustomer.channel_source }} · {{ selectedCustomer.sales_staff_user_id ? '已分配销售' : '未分配' }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <template v-if="!isEditMode">
              <el-button 
                type="primary" 
                size="default"
                @click="handleEditCustomer"
                class="edit-customer-button"
              >
                <span class="material-icons-outlined text-sm mr-1">edit</span>
                编辑客户
              </el-button>
            </template>
            <template v-else>
              <el-button 
                @click="handleCancelEdit"
                size="default"
              >
                取消
              </el-button>
              <el-button 
                type="primary" 
                size="default"
                @click="handleSaveEdit"
                :loading="saving"
                class="save-customer-button"
              >
                <span class="material-icons-outlined text-sm mr-1">save</span>
                保存
              </el-button>
            </template>
            <button 
              @click="handleClosePreviewOverlay"
              class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"
            >
              <span class="material-icons-outlined text-sm">close</span>
            </button>
          </div>
        </div>

        <!-- 预览框内容 -->
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 客户信息 (左侧) -->
            <div class="col-span-1 lg:col-span-1 space-y-6">
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">person</span>
                  基本信息
                </h4>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">客户姓名</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedCustomer.customer_name || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">微信昵称</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedCustomer.wechat_name || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">微信账号</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedCustomer.wechat_id || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">联系电话</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedCustomer.phone || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">渠道来源</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedCustomer.channel_source || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">市场人员</span>
                    <span 
                      v-if="selectedCustomer.market_staff_user_id"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                    >
                      {{ getStaffName(selectedCustomer.market_staff_user_id) }}
                    </span>
                    <span v-else class="text-sm text-gray-400">-</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">销售人员</span>
                    <span 
                      v-if="selectedCustomer.sales_staff_user_id"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {{ getStaffName(selectedCustomer.sales_staff_user_id) }}
                    </span>
                    <span v-else class="text-sm text-gray-400">-</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">分配状态</span>
                    <span 
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="getStatusClass(selectedCustomer.assignment_status)"
                    >
                      {{ getStatusLabel(selectedCustomer.assignment_status) }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">school</span>
                  项目信息
                </h4>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">留学项目</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedCustomer.study_project || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">意向等级</span>
                    <span 
                      class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                      :class="getIntentLevelClass(selectedCustomer.intent_level)"
                    >
                      {{ getIntentLevelLabel(selectedCustomer.intent_level) }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">客户状态</span>
                    <span 
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="getStatusClass(selectedCustomer.assignment_status)"
                    >
                      {{ getStatusLabel(selectedCustomer.assignment_status) }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">description</span>
                  客户背景
                </h4>
                <p class="text-sm text-gray-700 leading-relaxed">
                  {{ selectedCustomer.customer_background || '暂无背景信息' }}
                </p>
              </div>

              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">assignment</span>
                  市场补充
                </h4>
                <p class="text-sm text-gray-700 leading-relaxed">
                  {{ selectedCustomer.market_supplement || '暂无市场补充信息' }}
                </p>
              </div>
            </div>

            <!-- 客户反馈 (右侧) -->
            <div class="col-span-1 lg:col-span-2 space-y-6">
              <div class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">feedback</span>
                  客户反馈 - {{ selectedCustomer.customer_name || selectedCustomer.wechat_name || '未知客户' }}
                </h4>
                
                <div v-if="!isEditMode" class="space-y-4">
                  <!-- 院校背景 -->
                  <div>
                    <h5 class="text-sm font-medium text-gray-700 mb-2">院校背景</h5>
                    <div class="bg-white rounded-md p-3 space-y-2 text-sm">
                      <div>
                        <span class="text-gray-600">客户姓名：</span>
                        <span :class="selectedCustomer.customer_name ? 'text-gray-900' : 'text-gray-400'">{{ selectedCustomer.customer_name || '-' }}</span>
                      </div>
                      <div>
                        <span class="text-gray-600">院校：</span>
                        <span :class="(selectedCustomer.current_school || selectedCustomer.feedback?.education?.university) ? 'text-gray-900' : 'text-gray-400'">{{ selectedCustomer.current_school || selectedCustomer.feedback?.education?.university || '-' }}</span>
                      </div>
                      <div>
                        <span class="text-gray-600">专业：</span>
                        <span :class="(selectedCustomer.current_major || selectedCustomer.feedback?.education?.major) ? 'text-gray-900' : 'text-gray-400'">{{ selectedCustomer.current_major || selectedCustomer.feedback?.education?.major || '-' }}</span>
                      </div>
                      <div>
                        <span class="text-gray-600">年级：</span>
                        <span :class="(selectedCustomer.current_grade || selectedCustomer.feedback?.education?.grade) ? 'text-gray-900' : 'text-gray-400'">{{ selectedCustomer.current_grade || selectedCustomer.feedback?.education?.grade || '-' }}</span>
                      </div>
                      <div>
                        <span class="text-gray-600">成绩：</span>
                        <span :class="(selectedCustomer.gpa_value || selectedCustomer.feedback?.education?.gpa) ? 'text-gray-900' : 'text-gray-400'">
                          {{ selectedCustomer.gpa_value ? `${selectedCustomer.gpa_value}/${selectedCustomer.gpa_scale || '100'}` : (selectedCustomer.feedback?.education?.gpa || '-') }}
                        </span>
                      </div>
                      <div>
                        <span class="text-gray-600">语言：</span>
                        <span :class="selectedCustomer.feedback?.education?.language ? 'text-gray-900' : 'text-gray-400'">{{ selectedCustomer.feedback?.education?.language || '-' }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 申请意向 -->
                  <div>
                    <h5 class="text-sm font-medium text-gray-700 mb-2">申请意向</h5>
                    <div class="bg-white rounded-md p-3 space-y-2 text-sm">
                      <div>
                        <span class="text-gray-600">入学时间：</span>
                        <span :class="(selectedCustomer.intended_enrollment_date || selectedCustomer.feedback?.application?.expectedEntryTime) ? 'text-gray-900' : 'text-gray-400'">{{ selectedCustomer.intended_enrollment_date || selectedCustomer.feedback?.application?.expectedEntryTime || '-' }}</span>
                      </div>
                      <div>
                        <span class="text-gray-600">意向地区：</span>
                        <span :class="((selectedCustomer.intent_regions && selectedCustomer.intent_regions.length > 0) || selectedCustomer.feedback?.application?.intendedRegion) ? 'text-gray-900' : 'text-gray-400'">
                          {{ (selectedCustomer.intent_regions && selectedCustomer.intent_regions.length > 0) ? selectedCustomer.intent_regions.join('，') : (selectedCustomer.feedback?.application?.intendedRegion || '-') }}
                        </span>
                      </div>
                      <div>
                        <span class="text-gray-600">意向院校：</span>
                        <span :class="((selectedCustomer.intent_schools && selectedCustomer.intent_schools.length > 0) || selectedCustomer.feedback?.application?.intendedUniversities) ? 'text-gray-900' : 'text-gray-400'">
                          {{ (selectedCustomer.intent_schools && selectedCustomer.intent_schools.length > 0) ? selectedCustomer.intent_schools.join('，') : (selectedCustomer.feedback?.application?.intendedUniversities || '-') }}
                        </span>
                      </div>
                      <div>
                        <span class="text-gray-600">意向专业：</span>
                        <span :class="((selectedCustomer.intent_majors && selectedCustomer.intent_majors.length > 0) || selectedCustomer.feedback?.application?.intendedMajor) ? 'text-gray-900' : 'text-gray-400'">
                          {{ (selectedCustomer.intent_majors && selectedCustomer.intent_majors.length > 0) ? selectedCustomer.intent_majors.join('，') : (selectedCustomer.feedback?.application?.intendedMajor || '-') }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- 详细反馈 -->
                  <div class="space-y-3">
                    <div>
                      <h5 class="text-sm font-medium text-gray-700">预算</h5>
                      <p :class="['text-sm bg-white rounded-md p-2 mt-1', selectedCustomer.feedback_budget ? 'text-gray-600' : 'text-gray-400']">{{ selectedCustomer.feedback_budget || '-' }}</p>
                    </div>
                    <div>
                      <h5 class="text-sm font-medium text-gray-700">动机</h5>
                      <p :class="['text-sm bg-white rounded-md p-2 mt-1', selectedCustomer.feedback_motivation ? 'text-gray-600' : 'text-gray-400']">{{ selectedCustomer.feedback_motivation || '-' }}</p>
                    </div>
                    <div>
                      <h5 class="text-sm font-medium text-gray-700">紧迫性</h5>
                      <p :class="['text-sm bg-white rounded-md p-2 mt-1', selectedCustomer.feedback_urgency ? 'text-gray-600' : 'text-gray-400']">{{ selectedCustomer.feedback_urgency || '-' }}</p>
                    </div>
                    <div>
                      <h5 class="text-sm font-medium text-gray-700">对比</h5>
                      <p :class="['text-sm bg-white rounded-md p-2 mt-1', selectedCustomer.feedback_comparison ? 'text-gray-600' : 'text-gray-400']">{{ selectedCustomer.feedback_comparison || '-' }}</p>
                    </div>
                    <div>
                      <h5 class="text-sm font-medium text-gray-700">父母</h5>
                      <p :class="['text-sm bg-white rounded-md p-2 mt-1', selectedCustomer.feedback_parents ? 'text-gray-600' : 'text-gray-400']">{{ selectedCustomer.feedback_parents || '-' }}</p>
                    </div>
                  </div>
                </div>
                
                <!-- 编辑模式的反馈表单 -->
                <div v-else-if="isEditMode" class="space-y-4">
                  <!-- 院校背景信息 -->
                  <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h5 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                      <span class="material-icons-outlined text-indigo-600 mr-2 text-base">school</span>
                      院校背景
                    </h5>
                    <div class="space-y-3">
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label class="block text-xs text-gray-600 mb-1">客户姓名</label>
                          <el-input
                            v-model="feedbackForm.customerName"
                            placeholder="请输入客户真实姓名"
                            maxlength="50"
                            size="small"
                            class="edit-input"
                          />
                        </div>
                        <div>
                          <label class="block text-xs text-gray-600 mb-1">院校信息</label>
                          <el-input
                            v-model="feedbackForm.education.university"
                            placeholder="请输入毕业院校或在读院校"
                            maxlength="100"
                            size="small"
                            class="edit-input"
                          />
                        </div>
                        
                        <div>
                          <label class="block text-xs text-gray-600 mb-1">专业信息</label>
                          <el-input
                            v-model="feedbackForm.education.major"
                            placeholder="请输入专业名称"
                            maxlength="100"
                            size="small"
                            class="edit-input"
                          />
                        </div>
                        
                        <div>
                          <label class="block text-xs text-gray-600 mb-1">年级信息</label>
                          <el-select
                            v-model="feedbackForm.education.grade"
                            placeholder="请选择年级"
                            size="small"
                            class="w-full edit-select"
                          >
                            <el-option label="大一" value="大一" />
                            <el-option label="大二" value="大二" />
                            <el-option label="大三上" value="大三上" />
                            <el-option label="大三下" value="大三下" />
                            <el-option label="大四" value="大四" />
                            <el-option label="毕业" value="毕业" />
                            <el-option label="研一" value="研一" />
                            <el-option label="研二" value="研二" />
                            <el-option label="研三" value="研三" />
                            <el-option label="其他" value="其他" />
                          </el-select>
                        </div>
                        
                        <div>
                          <label class="block text-xs text-gray-600 mb-1">成绩分数</label>
                          <el-input
                            v-model="feedbackForm.education.gpaValue"
                            placeholder="请输入成绩分数"
                            maxlength="10"
                            size="small"
                            class="edit-input"
                          />
                        </div>
                        
                        <div>
                          <label class="block text-xs text-gray-600 mb-1">成绩制式</label>
                          <el-select
                            v-model="feedbackForm.education.gpaScale"
                            placeholder="请选择成绩制式"
                            size="small"
                            class="w-full edit-select"
                          >
                            <el-option label="百分制 (100)" value="100" />
                            <el-option label="4.0制" value="4.0" />
                            <el-option label="5.0制" value="5.0" />
                            <el-option label="其他" value="其他" />
                          </el-select>
                        </div>
                      </div>
                      
                                              <div>
                          <label class="block text-xs text-gray-600 mb-1">语言信息</label>
                          <el-input
                            v-model="feedbackForm.education.language"
                            type="textarea"
                            placeholder="请输入语言成绩信息，如雅思、托福、四六级等"
                            :rows="2"
                            maxlength="200"
                            class="edit-textarea"
                          />
                        </div>
                    </div>
                  </div>

                  <!-- 申请意向信息 -->
                  <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h5 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                      <span class="material-icons-outlined text-indigo-600 mr-2 text-base">explore</span>
                      申请意向
                    </h5>
                    <div class="space-y-3">
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label class="block text-xs text-gray-600 mb-1">期望入学时间</label>
                          <el-input
                            v-model="feedbackForm.application.expectedEntryTime"
                            placeholder="如：2024年9月"
                            maxlength="50"
                            size="small"
                            class="edit-input"
                          />
                        </div>
                        
                        <div>
                          <label class="block text-xs text-gray-600 mb-1">意向地区</label>
                          <el-select
                            v-model="feedbackForm.application.intendedRegion"
                            placeholder="请选择意向地区"
                            size="small"
                            class="w-full edit-select"
                          >
                            <el-option label="香港" value="香港" />
                            <el-option label="英国" value="英国" />
                            <el-option label="美国" value="美国" />
                            <el-option label="澳洲" value="澳洲" />
                            <el-option label="新加坡" value="新加坡" />
                            <el-option label="加拿大" value="加拿大" />
                            <el-option label="其他" value="其他" />
                          </el-select>
                        </div>
                      </div>
                      
                                              <div>
                          <label class="block text-xs text-gray-600 mb-1">意向院校</label>
                          <el-input
                            v-model="feedbackForm.application.intendedUniversities"
                            type="textarea"
                            placeholder="请输入意向申请的院校，可输入多个"
                            :rows="2"
                            maxlength="300"
                            class="edit-textarea"
                          />
                        </div>
                      
                      <div>
                        <label class="block text-xs text-gray-600 mb-1">意向专业</label>
                        <el-input
                          v-model="feedbackForm.application.intendedMajor"
                          placeholder="请输入意向申请的专业"
                          maxlength="100"
                          size="small"
                          class="edit-input"
                        />
                      </div>
                      
                                              <div>
                          <label class="block text-xs text-gray-600 mb-1">备注</label>
                          <el-input
                            v-model="feedbackForm.application.notes"
                            type="textarea"
                            placeholder="其他备注信息"
                            :rows="2"
                            maxlength="300"
                            class="edit-textarea"
                          />
                        </div>
                    </div>
                  </div>

                  <!-- 客户反馈 -->
                  <div class="bg-white rounded-lg p-4 border border-gray-200">
                    <h5 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                      <span class="material-icons-outlined text-indigo-600 mr-2 text-base">feedback</span>
                      客户反馈
                    </h5>
                    <div class="space-y-3">
                                              <div>
                          <label class="block text-xs text-gray-600 mb-1">预算</label>
                          <el-input
                            v-model="feedbackForm.budget"
                            type="textarea"
                            placeholder="中介预算，对比机构的中介费，留学学费+生活费等"
                            :rows="3"
                            maxlength="500"
                            class="edit-textarea"
                          />
                        </div>
                      
                                              <div>
                          <label class="block text-xs text-gray-600 mb-1">动机</label>
                          <el-input
                            v-model="feedbackForm.motivation"
                            type="textarea"
                            placeholder="考公考编/上海落户/体验/想去港大港中文/申请不到好的也没必要出去了/不管怎么样都要出去/地区的选择/专业的考虑等"
                            :rows="3"
                            maxlength="500"
                            class="edit-textarea"
                          />
                        </div>
                      
                                              <div>
                          <label class="block text-xs text-gray-600 mb-1">紧迫性</label>
                          <el-input
                            v-model="feedbackForm.urgency"
                            type="textarea"
                            placeholder="最早什么时候能做决定，最晚什么时候做决定，原因是什么"
                            :rows="3"
                            maxlength="500"
                            class="edit-textarea"
                          />
                        </div>
                      
                                              <div>
                          <label class="block text-xs text-gray-600 mb-1">对比</label>
                          <el-input
                            v-model="feedbackForm.comparison"
                            type="textarea"
                            placeholder="线上线下，机构价格，机构名称，别的机构好在哪/不好在哪，学生还要再对比多久"
                            :rows="3"
                            maxlength="500"
                            class="edit-textarea"
                          />
                        </div>
                      
                                              <div>
                          <label class="block text-xs text-gray-600 mb-1">父母</label>
                          <el-input
                            v-model="feedbackForm.parents"
                            type="textarea"
                            placeholder="父母主要/次要做决定，父母关心的问题，有没有熟人机构问题"
                            :rows="3"
                            maxlength="500"
                            class="edit-textarea"
                          />
                        </div>
                    </div>
                  </div>
                </div>
                
                <!-- 无反馈信息时的显示 -->
                <div v-else class="text-center py-8">
                  <div class="text-gray-400 mb-2">
                    <span class="material-icons-outlined text-3xl">feedback</span>
                  </div>
                  <p class="text-sm text-gray-500">暂无客户反馈信息</p>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="handleFillFeedback(selectedCustomer)"
                    class="mt-3"
                  >
                    填写反馈
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div> <!-- 组织身份内容结束 -->
      </div>
    </div>

    <!-- 转为签约客户对话框 -->
    <el-dialog
      v-model="contractDialogVisible"
      :title="`转为签约客户 - ${selectedCustomer?.customer_name || selectedCustomer?.wechat_name || '未知客户'}`"
      width="600px"
      class="simple-dialog compact-dialog"
      @close="handleCancelSign"
    >
      <div class="simple-content" v-if="selectedCustomer">
        <!-- 客户信息展示 -->
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-12 h-12 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] font-medium">
            {{ getCustomerInitials(selectedCustomer.customer_name || selectedCustomer.wechat_name) }}
          </div>
          <div>
            <div class="font-medium text-gray-900">{{ selectedCustomer.customer_name || selectedCustomer.wechat_name || '未知客户' }}</div>
            <div class="text-sm text-gray-500">{{ selectedCustomer.wechat_name }} | {{ selectedCustomer.phone }}</div>
          </div>
        </div>

        <!-- 签约表单 -->
        <el-form :model="contractForm" label-width="100px" class="space-y-6">
          <!-- 附加条款 -->
          <el-form-item label="附加条款" required>
            <el-input
              v-model="contractForm.additionalTerms"
              type="textarea"
              :rows="6"
              placeholder="请输入附加条款内容，例如：服务范围、时间节点、特殊要求等"
              maxlength="1000"
              show-word-limit
              resize="none"
            />
            <div class="text-xs text-gray-500 mt-1">
              请详细描述与该客户签约的特殊条款和要求
            </div>
          </el-form-item>

          <!-- 签约备注 -->
          <el-form-item label="签约备注">
            <el-input
              v-model="contractForm.contractNotes"
              type="textarea"
              :rows="3"
              placeholder="请输入签约相关备注信息（可选）"
              maxlength="500"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </el-form>

        <!-- 确认信息 -->
        <div class="bg-blue-50 p-4 rounded-lg mt-6">
          <div class="flex items-start space-x-2">
            <span class="material-icons-outlined text-blue-600 text-sm">info</span>
            <div class="text-sm text-blue-800">
              <div class="font-medium mb-1">签约确认</div>
              <div>确认将该客户转为签约客户后，客户信息将流转到签约客户管理模块，同时附加条款将作为合同补充内容保存。</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对话框按钮 -->
      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="handleCancelSign">取消</el-button>
          <el-button type="primary" @click="handleConfirmSign">
            确认签约
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getValidCustomerList, 
  getValidCustomerDetail,
  updateValidCustomer, 
  convertToSignedCustomer,
  archiveValidCustomer,
  getValidCustomerStatistics,
  getOrganizationMembersForCRM,
  INTENT_LEVEL_OPTIONS,
  ASSIGNMENT_STATUS_OPTIONS
} from '@/api/crm'
import { useAuthStore } from '@/stores/auth'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'

// 初始化状态管理
const authStore = useAuthStore()
const { isOrganizationOwner } = useOrganizationPermissions()

// 响应式数据
const searchKeyword = ref('')
const intentLevelFilter = ref('')
const salesStaffFilter = ref('')
const statusFilter = ref('')
const loading = ref(false)
const cardClickLoading = ref(false)
const saving = ref(false)

// 计算属性：是否有权限查看所有客户（系统管理员或组织owner）
const canViewAllCustomers = computed(() => {
  return authStore.user?.role === 'admin' || authStore.user?.role === 'dev' || isOrganizationOwner.value
})

// 视图模式控制 - 初始化时将在mounted中根据权限设置
const showOnlyMyCustomers = ref(true)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const feedbackDialogVisible = ref(false)
const previewOverlayVisible = ref(false)
const contractDialogVisible = ref(false) // 转为签约客户对话框
const selectedCustomer = ref(null)
const isEditMode = ref(false)

// 已移除editForm，现在只使用feedbackForm进行反馈信息编辑

// 表单数据
const feedbackForm = ref({
  // 客户基本信息
  customerName: '',
  
  // 原有反馈字段
  budget: '',
  motivation: '',
  urgency: '',
  comparison: '',
  parents: '',
  
  // 院校背景信息
  education: {
    university: '',
    major: '',
    grade: '',
    gpaValue: '',
    gpaScale: '100', // 默认为百分制
    language: ''
  },
  
  // 申请意向信息
  application: {
    expectedEntryTime: '',
    intendedRegion: '',
    intendedUniversities: '',
    intendedMajor: '',
    notes: ''
  }
})

// 签约表单数据
const contractForm = ref({
  additionalTerms: '', // 附加条款
  contractNotes: ''    // 签约备注
})

// 数据状态
const validCustomers = ref([])
const totalCustomers = ref(0)
const statistics = ref({
  total: 0,
  intent_level_distribution: {
    high: 0,
    medium: 0,
    low: 0,
    unknown: 0
  },
  assignment_distribution: {
    assigned: 0,
    unassigned: 0
  }
})
const organizationMembers = ref([])

// 组织ID（从当前身份信息获取）
const organizationId = computed(() => {
  return authStore.currentIdentity?.organization_id || null
})

// 检查是否为组织身份
const isOrganizationIdentity = computed(() => {
  return authStore.currentIdentity?.identity_type === 'organization' && organizationId.value
})

// API函数
// 获取有效客户列表 - 学习市场资源的平滑切换方式
const fetchValidCustomers = async () => {
  // 检查是否为组织身份，个人身份不调用API
  if (!isOrganizationIdentity.value) {
    return
  }
  
  try {
    loading.value = true
    const params = {
      organization_id: organizationId.value,
      page: currentPage.value,
      size: pageSize.value
    }

    // 应用筛选条件
    if (searchKeyword.value) params.search_keyword = searchKeyword.value
    if (salesStaffFilter.value) params.sales_staff_user_id = salesStaffFilter.value

    // 状态筛选：根据看板卡片点击进行筛选（优先级高于手动筛选）
    if (statusFilter.value) {
      switch (statusFilter.value) {
        case 'invalid':
          params.is_valid = false // 只显示无效客户
          break
        case 'high_intent':
          params.intent_level = 'high' // 高意向客户
          break
        case 'following':
          params.assignment_status = 'assigned' // 已分配（跟进中）的客户
          break
        case 'pending':
          params.assignment_status = 'unassigned' // 未分配（待签约）的客户
          break
        // 空字符串表示显示所有客户，不添加额外筛选条件
      }
    } else {
      // 如果没有看板卡片筛选，则应用手动筛选
      if (intentLevelFilter.value) params.intent_level = intentLevelFilter.value
    }

    // 权限控制：只有有权限的用户且选择显示所有客户时才传递show_all=true
    if (canViewAllCustomers.value && !showOnlyMyCustomers.value) {
      params.show_all = true
    }

    // 添加时间戳避免缓存问题
    params._t = Date.now()
    
    // 调试日志已移除
    
    const response = await getValidCustomerList(params)
    validCustomers.value = response.items || []
    totalCustomers.value = response.total || 0
    

    
    // 获取客户列表后立即更新统计数据 - 学习市场资源的方法
    await fetchStatistics()
  } catch (error) {
    console.error('获取有效客户失败:', error)
    ElMessage.error('获取有效客户失败')
    // 出错时重置数据
    validCustomers.value = []
    totalCustomers.value = 0
  } finally {
    loading.value = false
  }
}

// 获取统计数据 - 修复版本，确保与客户列表数据完全一致
const fetchStatistics = async () => {
  // 检查是否为组织身份，个人身份不调用API
  if (!isOrganizationIdentity.value) {
    return
  }
  
  try {
    // 基于当前查看视角和筛选条件获取统计数据
    const params = {
      organization_id: organizationId.value
    }
    
    // 应用当前筛选条件，确保统计数据与显示数据一致
    if (searchKeyword.value) params.search_keyword = searchKeyword.value
    if (salesStaffFilter.value) params.sales_staff_user_id = salesStaffFilter.value

    // 状态筛选：与fetchValidCustomers保持完全一致的逻辑
    if (statusFilter.value) {
      switch (statusFilter.value) {
        case 'invalid':
          params.is_valid = false // 只显示无效客户
          break
        case 'high_intent':
          params.intent_level = 'high' // 高意向客户
          break
        case 'following':
          params.assignment_status = 'assigned' // 已分配（跟进中）的客户
          break
        case 'pending':
          params.assignment_status = 'unassigned' // 未分配（待签约）的客户
          break
        // 空字符串表示显示所有客户，不添加额外筛选条件
      }
    } else {
      // 如果没有看板卡片筛选，则应用手动筛选
      if (intentLevelFilter.value) params.intent_level = intentLevelFilter.value
    }

    // 权限控制：与fetchValidCustomers保持一致
    if (canViewAllCustomers.value && !showOnlyMyCustomers.value) {
      params.show_all = true
    }
    
    // 添加时间戳避免缓存问题
    params._t = Date.now()
    
    // 调试日志已移除
    
    const response = await getValidCustomerStatistics(params)
    
    // 使用服务端返回的统计数据，确保数据一致性
    statistics.value = {
      total: response.total || 0,
      intent_level_distribution: response.intent_level_distribution || {
        high: 0,
        medium: 0,
        low: 0,
        unknown: 0
      },
      assignment_distribution: response.assignment_distribution || {
        assigned: 0,
        unassigned: 0
      },
      invalid_count: response.invalid_count || 0
    }
    
    // 统计数据更新完成
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 失败时重置为空数据，避免显示错误信息
    statistics.value = {
      total: 0,
      intent_level_distribution: { high: 0, medium: 0, low: 0, unknown: 0 },
      assignment_distribution: { assigned: 0, unassigned: 0 },
      invalid_count: 0
    }
  }
}

// 获取组织成员列表
const fetchOrganizationMembers = async () => {
  // 检查是否为组织身份，个人身份不调用API
  if (!isOrganizationIdentity.value) {
    return
  }
  
  try {
    const response = await getOrganizationMembersForCRM(organizationId.value)
    organizationMembers.value = response.members || []
  } catch (error) {
    console.error('获取组织成员失败:', error)
    // 个人身份时不需要显示错误消息
    if (isOrganizationIdentity.value) {
      ElMessage.error('获取组织成员失败')
    }
  }
}

// 根据用户ID获取人员姓名 - 优化版本，使用缓存Map
const getStaffName = (userId) => {
  const staffName = staffNameMap.value.get(parseInt(userId))
  return staffName || `用户${userId}`
}



// 切换加载状态
const toggleLoading = ref(false)

// 切换显示所有客户 - 修复版本，确保数据同步
const showAllCustomers = async () => {
  if (!canViewAllCustomers.value) {
    console.warn('用户无权限查看所有客户')
    return
  }
  
  if (toggleLoading.value) return // 防止重复点击
  
  try {
    toggleLoading.value = true
    
    // 立即更新UI状态，给用户即时反馈
    showOnlyMyCustomers.value = false
    currentPage.value = 1 // 重置到第一页
    
    // 清除可能影响数据的筛选状态
    statusFilter.value = ''
    
    if (process.env.NODE_ENV === 'development') {
      console.log('切换到显示所有客户')
    }
    
    // 直接更新客户列表（统计数据会在fetchValidCustomers中自动更新）
    await fetchValidCustomers()
  } catch (error) {
    // 如果失败，回滚状态
    showOnlyMyCustomers.value = true
    console.error('切换到所有客户失败:', error)
    ElMessage.error('切换视图失败，请重试')
  } finally {
    toggleLoading.value = false
  }
}

// 切换显示我的客户 - 修复版本，确保数据同步
const showMyCustomers = async () => {
  if (toggleLoading.value) return // 防止重复点击
  
  try {
    toggleLoading.value = true
    
    // 立即更新UI状态，给用户即时反馈
    showOnlyMyCustomers.value = true
    currentPage.value = 1 // 重置到第一页
    
    // 清除可能影响数据的筛选状态
    statusFilter.value = ''
    
    if (process.env.NODE_ENV === 'development') {
      console.log('切换到显示我的客户')
    }
    
    // 直接更新客户列表（统计数据会在fetchValidCustomers中自动更新）
    await fetchValidCustomers()
  } catch (error) {
    // 如果失败，回滚状态
    showOnlyMyCustomers.value = false
    console.error('切换到我的客户失败:', error)
    ElMessage.error('切换视图失败，请重试')
  } finally {
    toggleLoading.value = false
  }
}

// 计算属性：已筛选的客户列表（API已筛选，这里直接返回）
const filteredCustomers = computed(() => {
  return validCustomers.value
})

// 计算属性：总页数 - 优化计算
const totalPages = computed(() => {
  if (totalCustomers.value <= 0 || pageSize.value <= 0) return 1
  return Math.ceil(totalCustomers.value / pageSize.value)
})

// 计算属性：当前页的客户列表（API已分页，直接返回）
const paginatedCustomers = computed(() => {
  return validCustomers.value
})

// 缓存组织成员映射，避免重复计算
const staffNameMap = computed(() => {
  const map = new Map()
  organizationMembers.value.forEach(member => {
    map.set(member.user_id, member.display_name)
  })
  return map
})

// 方法：获取客户名称首字母
const getCustomerInitials = (name) => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

// 缓存映射，避免重复计算
const INTENT_LEVEL_LABELS = {
  high: '高意向',
  medium: '中意向',
  low: '低意向'
}

const INTENT_LEVEL_CLASSES = {
  high: 'bg-red-50 text-red-700 border border-red-200',
  medium: 'bg-yellow-50 text-yellow-700 border border-yellow-200',
  low: 'bg-gray-50 text-gray-700 border border-gray-200'
}

const STATUS_LABELS = {
  assigned: '已分配',
  unassigned: '未分配',
  following: '跟进中',
  pending: '待签约',
  signed: '已签约'
}

const STATUS_CLASSES = {
  assigned: 'bg-green-100 text-green-800',
  unassigned: 'bg-gray-100 text-gray-800',
  following: 'bg-blue-100 text-blue-800',
  pending: 'bg-yellow-100 text-yellow-800',
  signed: 'bg-green-100 text-green-800'
}

// 方法：获取意向等级标签 - 优化版本
const getIntentLevelLabel = (level) => {
  return INTENT_LEVEL_LABELS[level] || '未知'
}

// 方法：获取意向等级样式 - 优化版本
const getIntentLevelClass = (level) => {
  return INTENT_LEVEL_CLASSES[level] || 'bg-gray-50 text-gray-700 border border-gray-200'
}

// 方法：获取状态标签 - 优化版本
const getStatusLabel = (status) => {
  return STATUS_LABELS[status] || '未知'
}

// 方法：获取状态样式 - 优化版本
const getStatusClass = (status) => {
  return STATUS_CLASSES[status] || 'bg-gray-100 text-gray-800'
}
  
  // 格式化时间为 YYYY-MM-DD HH:mm 格式
  const formatDateTime = (timeString) => {
    if (!timeString || timeString === '-') return '-'
    
    try {
      const date = new Date(timeString)
      if (isNaN(date.getTime())) return '-'
      
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      return '-'
    }
  }
  
// 重置筛选 - 优化版本
const handleResetFilter = () => {
  searchKeyword.value = ''
  intentLevelFilter.value = ''
  salesStaffFilter.value = ''
  statusFilter.value = ''
  currentPage.value = 1
  // 重新获取数据，fetchValidCustomers内部会自动更新统计数据
  fetchValidCustomers()
}

// 状态卡片点击处理 - 优化版本
const handleStatusCardClick = async (status) => {
  // 防止快速重复点击
  if (cardClickLoading.value) return
  
  cardClickLoading.value = true
  
  try {
    // 如果点击的是当前已选中的卡片，则取消筛选
    if (statusFilter.value === status) {
      statusFilter.value = ''
    } else {
      // 否则设置新的筛选条件
      statusFilter.value = status
      // 清除其他可能冲突的筛选条件
      if (status === 'high_intent') {
        intentLevelFilter.value = ''
      }
    }
    currentPage.value = 1
    
    // 立即刷新数据，fetchValidCustomers内部会自动更新统计数据
    await fetchValidCustomers()
  } finally {
    // 200ms后恢复点击
    setTimeout(() => {
      cardClickLoading.value = false
    }, 200)
  }
}

// 分页处理
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}



// 事件处理：填写反馈
const handleFillFeedback = (customer) => {
  selectedCustomer.value = customer
  feedbackForm.value = customer.feedback ? { ...customer.feedback } : {
    // 客户基本信息
    customerName: customer.customer_name || '',
    
    // 原有反馈字段
    budget: '',
    motivation: '',
    urgency: '',
    comparison: '',
    parents: '',
    
    // 院校背景信息
    education: {
      university: '',
      major: '',
      grade: '',
      gpa: '',
      language: ''
    },
    
    // 申请意向信息
    application: {
      expectedEntryTime: '',
      intendedRegion: '',
      intendedUniversities: '',
      intendedMajor: '',
      notes: ''
    }
  }
  feedbackDialogVisible.value = true
}

// 事件处理：查看反馈
const handleViewFeedback = (customer) => {
  selectedCustomer.value = customer
  feedbackForm.value = customer.feedback ? { ...customer.feedback } : {
    // 客户基本信息
    customerName: customer.customer_name || '',
    
    // 原有反馈字段
    budget: '',
    motivation: '',
    urgency: '',
    comparison: '',
    parents: '',
    
    // 院校背景信息
    education: {
      university: '',
      major: '',
      grade: '',
      gpa: '',
      language: ''
    },
    
    // 申请意向信息
    application: {
      expectedEntryTime: '',
      intendedRegion: '',
      intendedUniversities: '',
      intendedMajor: '',
      notes: ''
    }
  }
  feedbackDialogVisible.value = true
}

// 事件处理：保存反馈
const handleSaveFeedback = async () => {
  try {
    saving.value = true
    
    const formData = {
      // 客户基本信息
      customer_name: feedbackForm.value.customerName,
      
      // 反馈信息
      feedback_budget: feedbackForm.value.budget,
      feedback_motivation: feedbackForm.value.motivation,
      feedback_urgency: feedbackForm.value.urgency,
      feedback_comparison: feedbackForm.value.comparison,
      feedback_parents: feedbackForm.value.parents,
      
      // 院校背景信息
      current_school: feedbackForm.value.education?.university,
      current_major: feedbackForm.value.education?.major,
      current_grade: feedbackForm.value.education?.grade,
      gpa_value: feedbackForm.value.education?.gpaValue,
      gpa_scale: feedbackForm.value.education?.gpaScale,
      
      // 申请意向信息
      intended_enrollment_date: feedbackForm.value.application?.expectedEntryTime,
      intent_regions: feedbackForm.value.application?.intendedRegion ? [feedbackForm.value.application.intendedRegion] : [],
      intent_schools: feedbackForm.value.application?.intendedUniversities ? 
        feedbackForm.value.application.intendedUniversities.split('，').map(s => s.trim()).filter(s => s) : [],
      intent_majors: feedbackForm.value.application?.intendedMajor ?
        feedbackForm.value.application.intendedMajor.split('，').map(s => s.trim()).filter(s => s) : []
    }
    
    await updateValidCustomer(selectedCustomer.value.id, formData, organizationId.value)
    
    ElMessage.success('客户反馈保存成功')
    feedbackDialogVisible.value = false
    
    // 重新获取数据，fetchValidCustomers内部会自动更新统计数据
    await fetchValidCustomers()
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 事件处理：客户卡片点击
const handleCustomerCardClick = async (customer) => {
  // 若组织成员为空则先加载，确保姓名可解析
  if (organizationMembers.value.length === 0) await fetchOrganizationMembers()
  selectedCustomer.value = customer
  isEditMode.value = false
  previewOverlayVisible.value = true
}

// 事件处理：编辑客户
const handleEditCustomer = () => {
  if (selectedCustomer.value) {
    // 直接从数据库字段获取数据，确保数据持久化
    feedbackForm.value = {
      // 客户基本信息
      customerName: selectedCustomer.value.customer_name || '',
      
      // 原有反馈字段（直接从数据库字段获取）
      budget: selectedCustomer.value.feedback_budget || '',
      motivation: selectedCustomer.value.feedback_motivation || '',
      urgency: selectedCustomer.value.feedback_urgency || '',
      comparison: selectedCustomer.value.feedback_comparison || '',
      parents: selectedCustomer.value.feedback_parents || '',
      
      // 院校背景信息（从数据库字段获取，保证数据持久化）
      education: {
        university: selectedCustomer.value.current_school || '',
        major: selectedCustomer.value.current_major || '',
        grade: selectedCustomer.value.current_grade || '',
        gpaValue: selectedCustomer.value.gpa_value || '',
        gpaScale: selectedCustomer.value.gpa_scale || '100',
        language: '' // 语言成绩暂时设为空，因为数据库中是JSONB格式
      },
      
      // 申请意向信息（从数据库字段获取，保证数据持久化）
      application: {
        expectedEntryTime: selectedCustomer.value.intended_enrollment_date || '',
        intendedRegion: (selectedCustomer.value.intent_regions && selectedCustomer.value.intent_regions.length > 0) 
          ? selectedCustomer.value.intent_regions[0] 
          : '',
        intendedUniversities: (selectedCustomer.value.intent_schools && selectedCustomer.value.intent_schools.length > 0) 
          ? selectedCustomer.value.intent_schools.join('，') 
          : '',
        intendedMajor: (selectedCustomer.value.intent_majors && selectedCustomer.value.intent_majors.length > 0) 
          ? selectedCustomer.value.intent_majors.join('，') 
          : '',
        notes: '' // 备注暂时设为空
      }
    }
    isEditMode.value = true
  }
}

// 事件处理：取消编辑
const handleCancelEdit = () => {
  isEditMode.value = false
  // 重置反馈表单数据
  feedbackForm.value = {
    // 客户基本信息
    customerName: '',
    
    budget: '',
    motivation: '',
    urgency: '',
    comparison: '',
    parents: '',
    education: {
      university: '',
      major: '',
      grade: '',
      gpa: '',
      language: ''
    },
    application: {
      expectedEntryTime: '',
      intendedRegion: '',
      intendedUniversities: '',
      intendedMajor: '',
      notes: ''
    }
  }
}

// 事件处理：保存编辑
const handleSaveEdit = async () => {
  try {
    saving.value = true
    
    const formData = {
      // 客户基本信息
      customer_name: feedbackForm.value.customerName,
      
      // 反馈信息
      feedback_budget: feedbackForm.value.budget,
      feedback_motivation: feedbackForm.value.motivation,
      feedback_urgency: feedbackForm.value.urgency,
      feedback_comparison: feedbackForm.value.comparison,
      feedback_parents: feedbackForm.value.parents,
      
      // 院校背景信息
      current_school: feedbackForm.value.education?.university,
      current_major: feedbackForm.value.education?.major,
      current_grade: feedbackForm.value.education?.grade,
      gpa_value: feedbackForm.value.education?.gpaValue,
      gpa_scale: feedbackForm.value.education?.gpaScale,
      
      // 申请意向信息
      intended_enrollment_date: feedbackForm.value.application?.expectedEntryTime,
      intent_regions: feedbackForm.value.application?.intendedRegion ? [feedbackForm.value.application.intendedRegion] : [],
      intent_schools: feedbackForm.value.application?.intendedUniversities ? 
        feedbackForm.value.application.intendedUniversities.split('，').map(s => s.trim()).filter(s => s) : [],
      intent_majors: feedbackForm.value.application?.intendedMajor ?
        feedbackForm.value.application.intendedMajor.split('，').map(s => s.trim()).filter(s => s) : []
    }
    
    await updateValidCustomer(selectedCustomer.value.id, formData, organizationId.value)
    
    ElMessage.success('客户信息保存成功')
    isEditMode.value = false
    
    // 立即更新当前选中客户的数据，实现悬浮框实时数据同步
    if (selectedCustomer.value) {
      // 更新selectedCustomer的数据以实现实时显示
      Object.assign(selectedCustomer.value, {
        customer_name: formData.customer_name,
        current_school: formData.current_school,
        current_major: formData.current_major,
        current_grade: formData.current_grade,
        gpa_value: formData.gpa_value,
        gpa_scale: formData.gpa_scale,
        intended_enrollment_date: formData.intended_enrollment_date,
        intent_regions: formData.intent_regions,
        intent_schools: formData.intent_schools,
        intent_majors: formData.intent_majors,
        // 更新反馈信息到feedback对象中（为了保持数据结构一致性）
        feedback: {
          ...selectedCustomer.value.feedback,
          budget: formData.feedback_budget,
          motivation: formData.feedback_motivation,
          urgency: formData.feedback_urgency,
          comparison: formData.feedback_comparison,
          parents: formData.feedback_parents,
          education: {
            university: formData.current_school,
            major: formData.current_major,
            grade: formData.current_grade,
            gpaValue: formData.gpa_value,
            gpaScale: formData.gpa_scale,
            language: feedbackForm.value.education?.language || ''
          },
          application: {
            expectedEntryTime: formData.intended_enrollment_date,
            intendedRegion: feedbackForm.value.application?.intendedRegion,
            intendedUniversities: feedbackForm.value.application?.intendedUniversities,
            intendedMajor: feedbackForm.value.application?.intendedMajor,
            notes: feedbackForm.value.application?.notes
          }
        }
      })
    }
    
    // 重新获取数据以刷新列表显示，fetchValidCustomers内部会自动更新统计数据
    await fetchValidCustomers()
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 事件处理：客户操作
const handleCustomerAction = async (command, customer) => {
  switch (command) {
    case 'edit':
      // 在预览框中启用编辑模式
      selectedCustomer.value = customer
      previewOverlayVisible.value = true
      break
    case 'sign':
      await handleSignCustomer(customer)
      break
    case 'delete':
      await handleDeleteCustomer(customer)
      break
  }
}

// 事件处理：转为签约客户
const handleSignCustomer = async (customer) => {
  try {
    // 设置选中的客户
    selectedCustomer.value = customer
    // 重置表单
    contractForm.value = {
      additionalTerms: '',
      contractNotes: ''
    }
    // 显示签约对话框
    contractDialogVisible.value = true
  } catch (error) {
    console.error('打开签约对话框失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

// 确认签约
const handleConfirmSign = async () => {
  try {
    // 这里可以添加表单验证
    if (!contractForm.value.additionalTerms.trim()) {
      ElMessage.warning('请输入附加条款内容')
      return
    }

    // 执行签约操作
    const customer = selectedCustomer.value
    
    // 更新客户状态
    customer.status = 'signed'
    
    // 这里可以发送API请求保存附加条款等信息
    console.log('签约信息：', {
      customer: customer,
      additionalTerms: contractForm.value.additionalTerms,
      contractNotes: contractForm.value.contractNotes
    })
    
    // 关闭对话框
    contractDialogVisible.value = false
    
    ElMessage.success(`客户 "${customer.customer_name || customer.wechat_name || '该客户'}" 已成功转为签约客户`)

  } catch (error) {
    console.error('签约失败:', error)
    ElMessage.error('签约失败，请重试')
  }
}

// 取消签约
const handleCancelSign = () => {
  contractDialogVisible.value = false
  contractForm.value = {
    additionalTerms: '',
    contractNotes: ''
  }
}

// 事件处理：删除客户
const handleDeleteCustomer = async (customer) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户 "${customer.customer_name || customer.wechat_name || '该客户'}" 吗？\n\n删除后将无法恢复。`,
      '删除客户确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    const index = validCustomers.value.findIndex(c => c.id === customer.id)
    if (index > -1) {
      validCustomers.value.splice(index, 1)
      ElMessage.success(`已成功删除客户 "${customer.customer_name || customer.wechat_name || '该客户'}"`)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 监听权限变化，更新默认显示模式
watch(canViewAllCustomers, (newValue) => {
  if (!newValue) {
    // 如果失去权限，强制显示我的客户
    showOnlyMyCustomers.value = true
  }
}, { immediate: true })



// 监听筛选条件变化，重新获取数据 - 优化版本，减少API调用
watch([searchKeyword, intentLevelFilter, salesStaffFilter, statusFilter], () => {
  currentPage.value = 1
  // fetchValidCustomers内部会自动更新统计数据
  fetchValidCustomers()
}, { debounce: 300 })

// 监听分页变化 - 分页时无需更新统计数据
watch([currentPage, pageSize], () => {
  fetchValidCustomers()
})

// 组件挂载 - 优化版本，提升初始化性能
onMounted(async () => {
  // 初始化数据
  console.log('有效客户页面加载完成')
  
  try {
    // 根据权限设置默认显示模式
    if (canViewAllCustomers.value) {
      showOnlyMyCustomers.value = false // owner默认显示所有客户
      console.log('检测到权限用户，默认显示所有客户')
    }
    
    // 并行加载组织成员和客户数据，优化初始化性能
    const [membersPromise, customersPromise] = await Promise.allSettled([
      fetchOrganizationMembers(),
      fetchValidCustomers() // fetchValidCustomers内部会自动更新统计数据
    ])
    
    // 检查加载结果
    if (membersPromise.status === 'rejected') {
      console.warn('组织成员加载失败:', membersPromise.reason)
    }
    if (customersPromise.status === 'rejected') {
      console.error('客户数据加载失败:', customersPromise.reason)
    }
  } catch (error) {
    console.error('页面初始化失败:', error)
  }
})

// 关闭对话框
const handleCloseFeedbackDialog = () => {
  feedbackDialogVisible.value = false
  selectedCustomer.value = null
}

// 关闭预览框
const handleClosePreviewOverlay = () => {
  previewOverlayVisible.value = false
  isEditMode.value = false
  selectedCustomer.value = null
}

// 处理无效客户状态变更
const handleInvalidCustomerChange = async (customer) => {
  try {
    // 切换客资有效性（取反逻辑）
    const newIsValid = !customer.is_valid
    
    // 调用真实的更新API
    const updateData = {
      is_valid: newIsValid
    }
    await updateValidCustomer(customer.id, updateData, organizationId.value)
    
    // 更新本地状态
    customer.is_valid = newIsValid
    
    const statusText = newIsValid ? '有效' : '无效'
    ElMessage.success(`客户 "${customer.customer_name || customer.wechat_name || '该客户'}" 已标记为${statusText}客资`)
    
    // 重新获取数据以反映最新状态，fetchValidCustomers内部会自动更新统计数据
    await fetchValidCustomers()
    
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('客资状态更新失败，请重试')
    // 不需要回滚状态，因为本地状态还没有改变
  }
}
</script>

<style scoped>
/* CRM 页面样式 - 遵循 CRM 系统设计规范 */
.crm-page {
  @apply p-6;
}

/* Pro Card 样式 */
.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.pro-card-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.pro-card-title {
  @apply flex items-center text-lg font-medium text-gray-900;
}

.pro-card-title .icon {
  @apply mr-2 text-gray-500;
}

.pro-card-body {
  @apply p-6;
}

/* 按钮样式 - 使用系统紫色主题 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

.add-customer-button,
.feedback-button,
.edit-customer-button,
.save-customer-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.add-customer-button:hover,
.feedback-button:hover,
.edit-customer-button:hover,
.save-customer-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 简洁对话框样式 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  .el-button--primary {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
  }
}

/* 输入框和选择框紫色主题 */
:deep(.el-input .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-input .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

:deep(.el-select) {
  --el-color-primary: #4F46E5;
}

/* 客户操作下拉框样式 */
.action-dropdown-trigger {
  color: #6B7280 !important;
  padding: 4px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.action-dropdown-trigger:hover {
  color: #4F46E5 !important;
  background-color: #F3F4F6 !important;
}

/* 表单分区样式 */
.form-section-title {
  @apply text-base font-semibold text-gray-800 border-b border-gray-200 pb-2 mb-4;
}

.form-section {
  @apply p-4 bg-gray-50 rounded-lg border border-gray-200;
}

/* 编辑模式简约但清晰样式 */
:deep(.edit-input .el-input__wrapper) {
  border: 1px solid #E5E7EB !important;
  border-radius: 4px !important;
  background-color: #F8FAFC !important;
  box-shadow: none !important;
  padding: 8px 12px !important;
  transition: all 0.2s ease !important;
  height: 38px !important;
  min-height: 38px !important;
}

:deep(.edit-input .el-input__wrapper:hover) {
  border-color: #D1D5DB !important;
  background-color: #FFFFFF !important;
}

:deep(.edit-input .el-input__wrapper.is-focus) {
  border-color: #4F46E5 !important;
  background-color: #FFFFFF !important;
  box-shadow: none !important;
}

:deep(.edit-input .el-input__inner) {
  color: #374151 !important;
  font-size: 14px !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

:deep(.edit-input .el-input__inner::placeholder) {
  color: #9CA3AF !important;
  font-size: 14px !important;
}

:deep(.edit-select .el-select__wrapper) {
  border: 1px solid #E5E7EB !important;
  border-radius: 4px !important;
  background-color: #F8FAFC !important;
  box-shadow: none !important;
  padding: 8px 12px !important;
  transition: all 0.2s ease !important;
  height: 38px !important;
  min-height: 38px !important;
}

:deep(.edit-select .el-select__wrapper:hover) {
  border-color: #D1D5DB !important;
  background-color: #FFFFFF !important;
}

:deep(.edit-select .el-select__wrapper.is-focused) {
  border-color: #4F46E5 !important;
  background-color: #FFFFFF !important;
  box-shadow: none !important;
}

:deep(.edit-select .el-select__placeholder) {
  color: #9CA3AF !important;
  font-size: 14px !important;
}

:deep(.edit-select .el-input .el-input__inner) {
  color: #374151 !important;
  font-size: 14px !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

:deep(.edit-textarea .el-textarea__inner) {
  border: 1px solid #E5E7EB !important;
  border-radius: 4px !important;
  background-color: #F8FAFC !important;
  box-shadow: none !important;
  padding: 8px 12px !important;
  transition: all 0.2s ease !important;
  resize: vertical !important;
  color: #374151 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  outline: none !important;
}

:deep(.edit-textarea .el-textarea__inner:hover) {
  border-color: #D1D5DB !important;
  background-color: #FFFFFF !important;
}

:deep(.edit-textarea .el-textarea__inner:focus) {
  border-color: #4F46E5 !important;
  background-color: #FFFFFF !important;
  box-shadow: none !important;
}

:deep(.edit-textarea .el-textarea__inner::placeholder) {
  color: #9CA3AF !important;
  font-size: 14px !important;
}

/* 隐藏字数计数器 */
:deep(.el-input__count) {
  display: none !important;
}

/* 下拉菜单样式 */
:deep(.customer-dropdown-menu) {
  border-radius: 7px !important;
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.13) !important;
  border: 1px solid #E5E7EB !important;
  padding: 3px 0 !important;
  min-width: 130px !important;
}

:deep(.dropdown-menu-item) {
  padding: 7px 14px !important;
  margin: 1px 3px !important;
  border-radius: 5px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

:deep(.dropdown-menu-item:hover) {
  background-color: #F9FAFB !important;
}

:deep(.dropdown-menu-item.delete-item:hover) {
  background-color: #FEF2F2 !important;
}

/* 分割线样式 */
:deep(.dropdown-menu-item.el-dropdown-menu__item--divided) {
  border-top: 1px solid #E5E7EB !important;
  margin-top: 5px !important;
  padding-top: 9px !important;
}

/* 客户列表过渡动画 */
.customer-list-enter-active,
.customer-list-leave-active {
  transition: opacity 0.2s ease;
}

.customer-list-enter-from,
.customer-list-leave-to {
  opacity: 0;
}

.customer-list-enter-to,
.customer-list-leave-from {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .crm-page {
    @apply p-4;
  }
}

@media (max-width: 768px) {
  .crm-page {
    @apply p-3;
  }
}
</style>