<template>
  <div class="crm-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span>CRM系统</span>
        <span class="mx-2">/</span>
      <span class="text-gray-900">签约客户</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">签约客户管理</h2>
    </div>

    <!-- 主要内容卡片 -->
    <div class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">verified</span>
          进度跟踪
        </div>
        <div class="flex items-center gap-3">
          <!-- 移除添加客户按钮 -->
        </div>
      </div>

      <div class="pro-card-body">
        <!-- 进度跟踪看板 -->
        <div class="space-y-4 mb-8">
          <!-- 第一排：总客户数 + 4个进度卡片 -->
          <div class="grid grid-cols-5 gap-4">
            <!-- 总签约客户数卡片 -->
            <div 
              @click="handleProgressCardClick('')"
              :class="[
                'progress-card cursor-pointer rounded-xl p-4 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-cyan-50 to-blue-50',
                progressFilter === '' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-cyan-100/50'
              ]"
            >
              <div class="flex items-center space-x-2 mb-2">
                <span class="material-icons-outlined text-sm text-cyan-600">people_alt</span>
                <span class="text-xs font-medium text-slate-700">总签约客户</span>
              </div>
              <div class="text-2xl font-bold text-slate-800">{{ statistics.total }}</div>
            </div>
            
            <!-- 前4个进度卡片 -->
            <div 
              v-for="(step, index) in progressSteps.slice(0, 4)" 
              :key="step.key"
              @click="handleProgressCardClick(step.key)"
              :class="[
                'progress-card cursor-pointer rounded-xl p-4 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105',
                getProgressCardStyle(index + 1),
                progressFilter === step.key ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : ''
              ]"
            >
              <div class="flex items-center space-x-2 mb-2">
                <span class="material-icons-outlined text-sm" :class="getProgressCardIconColor(index + 1)">
                  {{ getProgressCardIcon(step.key) }}
                </span>
                <span class="text-xs font-medium text-slate-700">{{ step.label }}</span>
              </div>
              <div class="text-2xl font-bold text-slate-800">{{ getProgressStatistic(step.key) }}</div>
            </div>
          </div>
          
          <!-- 第二排：5个进度卡片 -->
          <div class="grid grid-cols-5 gap-4">
            <div 
              v-for="(step, index) in progressSteps.slice(4, 9)" 
              :key="step.key"
              @click="handleProgressCardClick(step.key)"
              :class="[
                'progress-card cursor-pointer rounded-xl p-4 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105',
                getProgressCardStyle(index + 5),
                progressFilter === step.key ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : ''
              ]"
            >
              <div class="flex items-center space-x-2 mb-2">
                <span class="material-icons-outlined text-sm" :class="getProgressCardIconColor(index + 5)">
                  {{ getProgressCardIcon(step.key) }}
                </span>
                <span class="text-xs font-medium text-slate-700">{{ step.label }}</span>
              </div>
              <div class="text-2xl font-bold text-slate-800">{{ getProgressStatistic(step.key) }}</div>
            </div>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="border-t border-gray-200 my-8"></div>

        <!-- 筛选区域 -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索姓名、电话"
            clearable
            class="w-full sm:w-64"
            size="default"
          >
            <template #prefix>
              <span class="material-icons-outlined text-gray-400 text-sm">search</span>
            </template>
          </el-input>

          <el-select 
            v-model="progressFilter" 
            placeholder="申请进度" 
            clearable 
            size="default"
            class="w-full sm:w-48"
          >
            <el-option label="全部进度" value="" />
            <el-option label="签约完成" value="signed_completed" />
            <el-option label="确定选校" value="school_selection" />
            <el-option label="材料收集" value="material_collection" />
            <el-option label="文书写作" value="document_writing" />
            <el-option label="文书定稿" value="document_finalization" />
            <el-option label="申请递交" value="application_submission" />
            <el-option label="获得Offer" value="offer_received" />
            <el-option label="签证申请" value="visa_application" />
            <el-option label="完成" value="completed" />
          </el-select>

          <el-select 
            v-model="serviceStaffFilter" 
            placeholder="服务人员" 
            clearable 
            size="default"
            class="w-full sm:w-48"
          >
            <el-option label="全部人员" value="" />
            <el-option label="pocky" value="pocky" />
          </el-select>

          <el-select 
            v-model="regionFilter" 
            placeholder="申请地区" 
            clearable 
            size="default"
            class="w-full sm:w-48"
          >
            <el-option label="全部地区" value="" />
            <el-option label="香港" value="香港" />
            <el-option label="英国" value="英国" />
            <el-option label="美国" value="美国" />
            <el-option label="澳洲" value="澳洲" />
            <el-option label="新加坡" value="新加坡" />
          </el-select>
          
          <el-button 
            @click="handleResetFilter"
            class="border-gray-300 text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
          >
            <span class="material-icons-outlined text-sm mr-1">refresh</span>
            重置
          </el-button>
        </div>

        <!-- 客户列表 -->
        <Transition
          name="customer-list"
          mode="out-in"
          appear
        >
          <div 
            :key="progressFilter + searchKeyword + serviceStaffFilter"
            class="customer-list-wrapper space-y-6"
          >
            <div
              v-for="customer in paginatedCustomers"
              :key="customer.id"
              class="customer-card rounded-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-200"
            >
            <!-- 第一行：客户基本信息 + 时间信息 -->
            <div class="flex items-center justify-between mb-4">
              <!-- 左侧：头像和基本信息 -->
              <div class="flex items-center">
                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center text-white mr-4 text-lg font-bold shadow-md">
                  {{ getCustomerInitials(customer.name) }}
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">{{ customer.name }}</h3>
                  <div class="text-sm text-gray-600 flex items-center gap-4">
                    <span>申请地区：{{ customer.targetRegion }}</span>
                    <span>入学年份：26Fall</span>
                    <span>申请方向：{{ customer.targetMajor }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 右侧：时间信息 -->
              <div class="text-sm text-gray-600 flex items-center gap-6">
                <span>签约时间：{{ customer.signedDate }}</span>
                <span>最新更新：{{ customer.lastUpdate }}</span>
                <span>服务时长：{{ getServiceDuration(customer.signedDate) }}天</span>
              </div>
            </div>

            <!-- 第二行：申请进度 -->
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm font-medium text-gray-700">申请进度</span>
              <div class="flex items-center gap-3">
                <span class="text-sm text-gray-600">{{ getProgressStatusLabel(customer.currentProgress) }}</span>
                <span class="inline-flex items-center justify-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 min-w-[50px]">
                  {{ getNewProgressPercentage(customer.progressStatus).toFixed(0) }}%
                </span>
              </div>
            </div>

            <!-- 第三行：进度条 -->
            <div class="mb-6">
              <!-- 自定义进度条 -->
              <div class="progress-wrapper">
                <!-- 进度步骤容器 -->
                <div class="progress-steps-wrapper">
                  <div
                    v-for="(step, index) in progressSteps"
                    :key="step.key"
                    class="progress-step-item"
                  >
                    <!-- 节点圆圈 -->
                    <div 
                      class="step-node"
                      :class="getNewStepCircleClass(customer.progressStatus, step.key)"
                    >
                      <span 
                        v-if="customer.progressStatus?.[step.key] === 'completed'"
                        class="material-icons-outlined"
                        style="font-size: 10px; line-height: 12px; font-weight: 900;"
                      >
                        check
                      </span>
                      <span 
                        v-else-if="customer.progressStatus?.[step.key] === 'in_progress'"
                        class="font-bold"
                        style="font-size: 8px; line-height: 12px; font-weight: 900;"
                      >
                        —
                      </span>
                    </div>
                    <!-- 步骤标签 -->
                    <span 
                      class="step-label-new"
                      :class="getNewStepLabelClass(customer.progressStatus, step.key)"
                    >
                      {{ step.label }}
                    </span>
                  </div>
                </div>
                
                <!-- 进度条轨道 -->
                <div class="progress-track-wrapper">
                  <div class="progress-track-bg">
                    <div 
                      class="progress-fill-new"
                      :class="getNewProgressBarClass(customer.progressStatus)"
                      :style="{ width: getNewProgressPercentage(customer.progressStatus) + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第四行：服务团队信息 + 操作按钮 -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
              <div class="text-sm text-gray-600 flex items-center gap-4">
                <span>服务团队</span>
                <div class="flex items-center gap-2">
                  <span>顾问：</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ customer.serviceTeam.marketingStaff }}
                  </span>
                </div>
                <div class="flex items-center gap-2">
                  <span>文书：</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ customer.serviceTeam.documentWriter }}
                  </span>
                </div>
                <div class="flex items-center gap-2">
                  <span>递交：</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ customer.serviceTeam.submissionStaff }}
                  </span>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <button @click="handleShowMaterials(customer)" class="px-3 py-1.5 text-sm bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-200 rounded-md transition-all duration-200 hover:shadow-sm">申请材料</button>
                <button @click="handleShowSchoolBook(customer)" class="px-3 py-1.5 text-sm bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-200 rounded-md transition-all duration-200 hover:shadow-sm">定校书</button>
                <button @click="handleShowAdditionalTerms(customer)" class="px-3 py-1.5 text-sm bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-200 rounded-md transition-all duration-200 hover:shadow-sm">附加条款</button>
                <button @click="handleShowOfferManagement(customer)" class="px-3 py-1.5 text-sm bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-200 rounded-md transition-all duration-200 hover:shadow-sm">Offer管理</button>
                <button class="px-3 py-1.5 text-sm bg-gray-50 text-gray-700 hover:bg-gray-100 border border-gray-200 rounded-md transition-all duration-200 hover:shadow-sm">备注信息</button>
                <el-dropdown @command="(command) => handleCustomerAction(command, customer)" trigger="click" class="customer-management-dropdown">
                  <el-button link size="small" class="action-dropdown-trigger">
                    <span class="material-icons-outlined text-lg">more_vert</span>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu class="management-dropdown-menu">
                      <el-dropdown-item command="edit" class="dropdown-menu-item">
                        <div class="flex items-center">
                          <span class="material-icons-outlined text-sm mr-2.5 text-green-600">edit</span>
                          <span class="text-sm font-medium text-gray-700">编辑信息</span>
                        </div>
                      </el-dropdown-item>
                      <el-dropdown-item command="update_progress" class="dropdown-menu-item">
                        <div class="flex items-center">
                          <span class="material-icons-outlined text-sm mr-2.5 text-blue-600">update</span>
                          <span class="text-sm font-medium text-gray-700">更新进度</span>
                        </div>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

            <!-- 空状态 -->
            <div v-if="paginatedCustomers.length === 0" class="text-center py-12">
              <div class="text-gray-400 mb-2">
                <span class="material-icons-outlined text-4xl">people_alt</span>
              </div>
              <p class="text-gray-500">暂无客户数据</p>
            </div>
          </div>
        </Transition>

        <!-- 分页 -->
        <div class="flex justify-between items-center mt-8" v-if="filteredCustomers.length > 0">
          <div class="text-sm text-gray-500">
            共 {{ filteredCustomers.length }} 位客户
          </div>
          <el-pagination
            v-if="totalPages > 1"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="filteredCustomers.length"
            layout="prev, pager, next"
            @current-change="handleCurrentChange"
            class="!mt-0"
          />
        </div>
      </div>
    </div>

    <!-- 学校详情对话框 -->
    <el-dialog
      v-model="schoolsDialogVisible"
      title="申请学校详情"
      width="600px"
      class="simple-dialog"
      @close="handleCloseSchoolsDialog"
    >
      <div class="simple-content" v-if="selectedCustomer">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-12 h-12 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] font-medium">
            {{ getCustomerInitials(selectedCustomer.name) }}
          </div>
          <div>
            <div class="font-medium text-gray-900">{{ selectedCustomer.name }}</div>
            <div class="text-sm text-gray-500">{{ selectedCustomer.targetRegion }} | {{ selectedCustomer.targetMajor }}</div>
          </div>
        </div>
        
        <div class="space-y-4">
          <h4 class="text-lg font-medium text-gray-900 mb-4">目标申请学校 ({{ selectedCustomer.targetSchools.length }} 所)</h4>
          <div class="grid grid-cols-1 gap-3">
            <div
              v-for="(school, index) in selectedCustomer.targetSchools"
              :key="index"
              class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200"
            >
              <div class="flex items-center space-x-3">
                <span class="material-icons-outlined text-blue-600">school</span>
                <div>
                  <div class="font-medium text-gray-900">{{ school }}</div>
                  <div class="text-sm text-gray-500">{{ selectedCustomer.targetRegion }}</div>
                </div>
              </div>
              <div class="text-sm text-gray-400">
                #{{ index + 1 }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="schoolsDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 更新进度对话框 -->
    <el-dialog
      v-model="updateProgressDialogVisible"
      title="更新客户进度"
      width="800px"
      class="simple-dialog compact-dialog"
      @close="handleCloseUpdateProgressDialog"
    >
      <div class="simple-content" v-if="selectedCustomer">
        <!-- 客户信息卡片 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 mb-6 border border-blue-100">
          <div class="flex items-center space-x-4">
            <div class="w-14 h-14 rounded-full bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center text-white text-xl font-bold shadow-lg">
              {{ getCustomerInitials(selectedCustomer.name) }}
            </div>
            <div class="flex-1">
              <h3 class="text-xl font-bold text-gray-900 mb-1">{{ selectedCustomer.name }}</h3>
              <div class="flex items-center space-x-3 text-sm text-gray-600">
                <span class="flex items-center">
                  <span class="material-icons-outlined text-sm mr-1">location_on</span>
                  申请地区：{{ selectedCustomer.targetRegion }}
                </span>
                <span class="flex items-center">
                  <span class="material-icons-outlined text-sm mr-1">school</span>
                  入学年份：26Fall
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 当前进度状态 -->
        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
            <span class="material-icons-outlined text-sm mr-2">timeline</span>
            当前申请进度
          </h4>
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 rounded-full bg-blue-500"></div>
                <span class="font-medium text-gray-900">{{ getProgressStatusLabel(selectedCustomer.currentProgress) }}</span>
              </div>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {{ getProgressPercentage(selectedCustomer.currentProgress).toFixed(0) }}% 完成
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: getProgressPercentage(selectedCustomer.currentProgress) + '%' }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 更新进度表单 -->
        <div class="space-y-6">
          <div class="mb-6">
            <div class="flex items-center justify-between mb-4">
              <label class="block text-sm font-medium text-gray-900">
                <span class="flex items-center">
                  <span class="material-icons-outlined text-sm mr-2">tune</span>
                  设置各环节状态
                </span>
              </label>
              <el-button 
                type="text" 
                @click="progressStepsExpanded = !progressStepsExpanded"
                class="text-blue-600 hover:text-blue-800"
                size="small"
              >
                <span class="flex items-center">
                  <span class="material-icons-outlined text-sm mr-1" :class="{ 'rotate-180': progressStepsExpanded }">
                    expand_more
                  </span>
                  {{ progressStepsExpanded ? '收起详情' : '展开详情' }}
                </span>
              </el-button>
            </div>
            
            <!-- 快速状态概览 -->
            <div v-if="!progressStepsExpanded" class="mb-4">
              <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div class="text-sm text-gray-600 mb-3 flex items-center">
                  <span class="material-icons-outlined text-sm mr-2">visibility</span>
                  当前进度概览
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div 
                    v-for="step in progressSteps.slice(0, -1)" 
                    :key="step.key"
                    class="flex items-center justify-between p-2 bg-white rounded border border-gray-100"
                  >
                    <span class="text-sm text-gray-700 font-medium">{{ step.label }}</span>
                    <span :class="getStepStatusBadgeClass(selectedCustomer.progressStatus?.[step.key] || 'not_started')" class="text-xs">
                      {{ getStepStatusLabel(selectedCustomer.progressStatus?.[step.key] || 'not_started') }}
                    </span>
                  </div>
                </div>
                <div class="text-xs text-gray-500 mt-3 p-2 bg-blue-50 rounded border-l-2 border-blue-300">
                  <span class="material-icons-outlined text-xs mr-1">info</span>
                  点击"展开详情"可修改各环节状态
                </div>
              </div>
            </div>
            
            <!-- 详细的进度环节状态设置 -->
            <div v-if="progressStepsExpanded" class="space-y-4">
              <div 
                v-for="(step, index) in progressSteps.slice(0, -1)" 
                :key="step.key"
                class="bg-gray-50 rounded-lg p-4 border border-gray-200"
              >
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center space-x-3">
                    <span class="material-icons-outlined text-sm text-gray-600">
                      {{ getProgressCardIcon(step.key) }}
                    </span>
                    <span class="font-medium text-gray-900">{{ step.label }}</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">当前:</span>
                    <span :class="getStepStatusBadgeClass(selectedCustomer.progressStatus?.[step.key] || 'not_started')">
                      {{ getStepStatusLabel(selectedCustomer.progressStatus?.[step.key] || 'not_started') }}
                    </span>
                  </div>
                </div>
                
                <el-radio-group 
                  v-model="progressForm.stepStatuses[step.key]" 
                  class="w-full flex space-x-4"
                  size="small"
                >
                  <el-radio value="not_started" class="radio-option">
                    <span class="flex items-center">
                      <span class="w-2 h-2 rounded-full bg-gray-400 mr-2"></span>
                      未开始
                    </span>
                  </el-radio>
                  <el-radio value="in_progress" class="radio-option">
                    <span class="flex items-center">
                      <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
                      进行中
                    </span>
                  </el-radio>
                  <el-radio value="completed" class="radio-option">
                    <span class="flex items-center">
                      <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                      已完成
                    </span>
                  </el-radio>
                </el-radio-group>
              </div>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-900 mb-3">
              <span class="flex items-center">
                <span class="material-icons-outlined text-sm mr-2">note_add</span>
                更新备注
              </span>
            </label>
            <el-input
              v-model="progressForm.note"
              type="textarea"
              placeholder="请输入更新备注"
              :rows="4"
              maxlength="500"
              show-word-limit
            />
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-between items-center">
          <div></div>
          <div class="flex space-x-3">
            <el-button 
              @click="updateProgressDialogVisible = false"
              :disabled="updating"
              size="default"
            >
              取消
            </el-button>
            <el-button 
              type="primary" 
              @click="handleConfirmUpdateProgress"
              :loading="updating"
              :disabled="!hasProgressChanges"
              size="default"
            >
              <span v-if="updating" class="flex items-center">
                <span class="material-icons-outlined text-sm mr-1 animate-spin">sync</span>
                更新中...
              </span>
              <span v-else class="flex items-center">
                <span class="material-icons-outlined text-sm mr-1">update</span>
                确认更新进度
              </span>
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 申请材料对话框 -->
    <el-dialog
      v-model="materialsDialogVisible"
      title="申请材料"
      width="800px"
      class="simple-dialog compact-dialog"
      @close="handleCloseMaterialsDialog"
    >
      <div class="simple-content" v-if="selectedCustomer">
        <!-- 客户信息卡片 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 mb-6 border border-blue-100">
          <div class="flex items-center space-x-4">
            <div class="w-14 h-14 rounded-full bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center text-white text-xl font-bold shadow-lg">
              {{ getCustomerInitials(selectedCustomer.name) }}
            </div>
            <div class="flex-1">
              <h3 class="text-xl font-bold text-gray-900 mb-1">{{ selectedCustomer.name }}</h3>
              <div class="flex items-center space-x-3 text-sm text-gray-600">
                <span class="flex items-center">
                  <span class="material-icons-outlined text-sm mr-1">location_on</span>
                  申请地区：{{ selectedCustomer.targetRegion }}
                </span>
                <span class="flex items-center">
                  <span class="material-icons-outlined text-sm mr-1">school</span>
                  入学年份：26Fall
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 申请材料网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div 
            v-for="category in materialCategories" 
            :key="category.key"
            class="material-category-card bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200"
          >
            <!-- 分类标题 -->
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center mr-3">
                <span class="material-icons-outlined text-blue-600 text-lg">{{ category.icon }}</span>
              </div>
              <div class="flex-1">
                <h4 class="text-sm font-medium text-gray-900">{{ category.name }}</h4>
                <p class="text-xs text-gray-500">{{ getMaterialCount(category.key) }} 个文件</p>
              </div>
            </div>

            <!-- 文件列表 -->
            <div class="space-y-2">
              <div 
                v-for="file in getMaterialsByCategory(category.key)" 
                :key="file.id"
                class="flex items-center p-2 bg-gray-50 rounded-md"
              >
                <span class="material-icons-outlined text-gray-400 mr-2 text-sm">{{ getFileIcon(file.type) }}</span>
                <div class="flex-1 min-w-0">
                  <p class="text-xs font-medium text-gray-900 truncate">{{ file.name }}</p>
                  <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }} • {{ file.uploadDate }}</p>
                </div>
              </div>
              
              <!-- 无文件状态 -->
              <div v-if="getMaterialsByCategory(category.key).length === 0" class="text-center py-4">
                <p class="text-xs text-gray-400">暂无文件</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="materialsDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 定校书对话框 -->
    <el-dialog
      v-model="schoolBookDialogVisible"
      title="定校书"
      width="900px"
      class="simple-dialog compact-dialog"
      @close="handleCloseSchoolBookDialog"
    >
      <div class="simple-content" v-if="selectedCustomer">
        <!-- 客户信息卡片 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 mb-6 border border-blue-100">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-14 h-14 rounded-full bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center text-white text-xl font-bold shadow-lg">
                {{ getCustomerInitials(selectedCustomer.name) }}
              </div>
              <div class="flex-1">
                <h3 class="text-xl font-bold text-gray-900 mb-1">{{ selectedCustomer.name }}</h3>
                <div class="flex items-center space-x-3 text-sm text-gray-600">
                  <span class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1">location_on</span>
                    申请地区：{{ selectedCustomer.targetRegion }}
                  </span>
                  <span class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1">school</span>
                    入学年份：26Fall
                  </span>
                </div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-indigo-600">{{ selectedCustomer.schoolPrograms?.length || 0 }}</div>
              <div class="text-sm text-gray-500">所学校</div>
            </div>
          </div>
        </div>

        <!-- 学校项目列表 -->
        <div class="space-y-4">
          <div
            v-for="program in selectedCustomer.schoolPrograms"
            :key="program.id"
            class="school-program-card bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200"
          >
            <div class="flex items-start space-x-4">
              <!-- 学校Logo -->
              <div class="w-16 h-16 rounded-lg bg-white border border-gray-200 flex items-center justify-center flex-shrink-0 shadow-sm">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  <span class="material-icons-outlined text-gray-400 text-lg">school</span>
                </div>
              </div>

              <!-- 学校和项目信息 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between mb-3">
                  <div class="flex-1">
                    <h4 class="text-lg font-semibold text-gray-900 mb-1">
                      {{ program.schoolName }} - {{ program.programName }}
                    </h4>
                    <p class="text-sm text-gray-600 mb-2">
                      {{ program.schoolEnglishName }} - {{ program.programEnglishName }}
                    </p>
                  </div>
                  
                  <!-- QS排名标签 -->
                  <div class="flex items-center space-x-2 flex-shrink-0 ml-4">
                    <div class="bg-amber-100 text-amber-800 px-3 py-1 rounded-full text-sm font-medium">
                      QS {{ program.qsRanking }}
                    </div>
                  </div>
                </div>

                <!-- 底部信息和操作 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span class="flex items-center">
                      <span class="material-icons-outlined text-sm mr-1">location_on</span>
                      {{ program.region }}
                    </span>
                    <span class="flex items-center">
                      <span class="material-icons-outlined text-sm mr-1">calendar_today</span>
                      添加于 {{ program.addedDate }}
                    </span>
                    <span class="flex items-center">
                      <div class="w-2 h-2 rounded-full mr-2" :class="getProgramTypeColor(program.type)"></div>
                      {{ getProgramTypeLabel(program.type) }}
                    </span>
                  </div>
                  
                  <div class="flex items-center space-x-2">
                    <el-button 
                      link 
                      size="small" 
                      @click="handleOpenWebsite(program.websiteUrl)"
                      class="text-blue-600 hover:text-blue-800"
                    >
                      <span class="material-icons-outlined text-sm mr-1">open_in_new</span>
                      查看官网
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!selectedCustomer.schoolPrograms || selectedCustomer.schoolPrograms.length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <span class="material-icons-outlined text-6xl">school</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无定校信息</h3>
            <p class="text-gray-500">该客户尚未添加任何学校项目</p>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="schoolBookDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 附加条款对话框 -->
    <el-dialog
      v-model="additionalTermsDialogVisible"
      title="附加条款"
      width="700px"
      class="simple-dialog compact-dialog"
      @close="handleCloseAdditionalTermsDialog"
    >
      <div class="simple-content" v-if="selectedCustomer">
        <!-- 客户信息卡片 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 mb-6 border border-blue-100">
          <div class="flex items-center space-x-4">
            <div class="w-14 h-14 rounded-full bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center text-white text-xl font-bold shadow-lg">
              {{ getCustomerInitials(selectedCustomer.name) }}
            </div>
            <div class="flex-1">
              <h3 class="text-xl font-bold text-gray-900 mb-1">{{ selectedCustomer.name }}</h3>
              <div class="flex items-center space-x-3 text-sm text-gray-600">
                <span class="flex items-center">
                  <span class="material-icons-outlined text-sm mr-1">location_on</span>
                  申请地区：{{ selectedCustomer.targetRegion }}
                </span>
                <span class="flex items-center">
                  <span class="material-icons-outlined text-sm mr-1">school</span>
                  入学年份：26Fall
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 附加条款内容 -->
        <div v-if="selectedCustomer.additionalTerms && selectedCustomer.additionalTerms.content" class="space-y-4">
          <!-- 条款信息头部 -->
          <div class="flex items-center justify-between mb-4 pb-3 border-b border-gray-200">
            <div class="flex items-center space-x-2">
              <span class="material-icons-outlined text-blue-600">description</span>
              <h4 class="text-lg font-semibold text-gray-900">签约附加条款</h4>
            </div>
            <div class="text-right text-sm text-gray-500">
              <div>创建人：{{ selectedCustomer.additionalTerms.createdBy }}</div>
              <div>创建时间：{{ selectedCustomer.additionalTerms.createdDate }}</div>
              <div v-if="selectedCustomer.additionalTerms.lastModified !== selectedCustomer.additionalTerms.createdDate">
                最后修改：{{ selectedCustomer.additionalTerms.lastModified }}
              </div>
            </div>
          </div>

          <!-- 条款内容 -->
          <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <div class="prose prose-sm max-w-none">
              <pre class="whitespace-pre-wrap text-sm text-gray-800 leading-relaxed font-sans">{{ selectedCustomer.additionalTerms.content }}</pre>
            </div>
          </div>

          <!-- 重要提醒 -->
          <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div class="flex items-start space-x-3">
              <span class="material-icons-outlined text-amber-600 mt-0.5">info</span>
              <div>
                <h5 class="font-medium text-amber-800 mb-1">服务老师注意事项</h5>
                <p class="text-sm text-amber-700">
                  以上条款为销售人员在签约时与客户约定的特殊条款，请在服务过程中严格按照条款执行。
                  如有疑问或需要调整，请及时与销售顾问沟通确认。
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 无条款状态 -->
        <div v-else class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <span class="material-icons-outlined text-6xl">description</span>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无附加条款</h3>
          <p class="text-gray-500">该客户签约时未设置特殊附加条款</p>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="additionalTermsDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Offer管理对话框 -->
    <el-dialog
      v-model="offerManagementDialogVisible"
      title="Offer管理"
      width="1000px"
      class="simple-dialog compact-dialog"
      @close="handleCloseOfferManagementDialog"
    >
      <div class="simple-content" v-if="selectedCustomer">
        <!-- 客户信息卡片 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 mb-6 border border-blue-100">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-14 h-14 rounded-full bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center text-white text-xl font-bold shadow-lg">
                {{ getCustomerInitials(selectedCustomer.name) }}
              </div>
              <div class="flex-1">
                <h3 class="text-xl font-bold text-gray-900 mb-1">{{ selectedCustomer.name }}</h3>
                <div class="flex items-center space-x-3 text-sm text-gray-600">
                  <span class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1">location_on</span>
                    申请地区：{{ selectedCustomer.targetRegion }}
                  </span>
                  <span class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1">school</span>
                    入学年份：26Fall
                  </span>
                </div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-indigo-600">{{ getOfferStatistics().total }}</div>
              <div class="text-sm text-gray-500">个申请项目</div>
            </div>
          </div>
        </div>

        <!-- 申请状态统计 -->
        <div class="grid grid-cols-4 gap-4 mb-6">
          <div class="bg-blue-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ getOfferStatistics().submitted }}</div>
            <div class="text-sm text-gray-600">已递交</div>
          </div>
          <div class="bg-orange-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-orange-600">{{ getOfferStatistics().interview }}</div>
            <div class="text-sm text-gray-600">获得面试</div>
          </div>
          <div class="bg-green-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600">{{ getOfferStatistics().offer }}</div>
            <div class="text-sm text-gray-600">获得Offer</div>
          </div>
          <div class="bg-gray-50 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-gray-600">{{ getOfferStatistics().pending }}</div>
            <div class="text-sm text-gray-600">等待结果</div>
          </div>
        </div>

        <!-- 学校项目列表 -->
        <div class="space-y-4">
          <div
            v-for="program in selectedCustomer.schoolPrograms"
            :key="program.id"
            class="offer-program-card bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200"
          >
            <div class="flex items-start space-x-6">
              <!-- 左侧：学校Logo和基本信息 -->
              <div class="flex-1">
                <div class="flex items-start space-x-4">
                  <!-- 学校Logo -->
                  <div class="w-16 h-16 rounded-lg bg-white border border-gray-200 flex items-center justify-center flex-shrink-0 shadow-sm">
                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <span class="material-icons-outlined text-gray-400 text-lg">school</span>
                    </div>
                  </div>

                  <!-- 学校和项目信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-3">
                      <div class="flex-1">
                        <h4 class="text-lg font-semibold text-gray-900 mb-1">
                          {{ program.schoolName }} - {{ program.programName }}
                        </h4>
                        <p class="text-sm text-gray-600 mb-2">
                          {{ program.schoolEnglishName }} - {{ program.programEnglishName }}
                        </p>
                        <div class="flex items-center space-x-3 text-sm text-gray-500">
                          <span class="flex items-center">
                            <span class="material-icons-outlined text-sm mr-1">location_on</span>
                            {{ program.region }}
                          </span>
                          <div class="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs font-medium">
                            QS {{ program.qsRanking }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧：申请状态和操作按钮 -->
              <div class="flex-shrink-0 w-80">
                <div class="bg-gray-50 rounded-lg p-4">
                  <h5 class="text-sm font-medium text-gray-900 mb-3">申请状态跟进</h5>
                  
                  <!-- 状态进度 -->
                  <div class="space-y-3">
                    <!-- 已递交状态 -->
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded-full flex items-center justify-center" 
                             :class="program.applicationStatus.submitted ? 'bg-blue-500' : 'bg-gray-300'">
                          <span v-if="program.applicationStatus.submitted" 
                                class="material-icons-outlined text-white text-xs">check</span>
                        </div>
                        <span class="text-sm" :class="program.applicationStatus.submitted ? 'text-gray-900 font-medium' : 'text-gray-500'">
                          已递交
                        </span>
                        <span v-if="program.applicationStatus.submitted && program.applicationStatus.submittedDate" 
                              class="text-xs text-gray-400">
                          ({{ program.applicationStatus.submittedDate }})
                        </span>
                      </div>
                      <el-button 
                        v-if="!program.applicationStatus.submitted"
                        size="small" 
                        type="primary"
                        @click="handleUpdateApplicationStatus(program.id, 'submitted')"
                        class="status-btn"
                      >
                        标记递交
                      </el-button>
                    </div>

                    <!-- 获得面试状态 -->
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded-full flex items-center justify-center" 
                             :class="program.applicationStatus.interview ? 'bg-orange-500' : 'bg-gray-300'">
                          <span v-if="program.applicationStatus.interview" 
                                class="material-icons-outlined text-white text-xs">check</span>
                        </div>
                        <span class="text-sm" :class="program.applicationStatus.interview ? 'text-gray-900 font-medium' : 'text-gray-500'">
                          获得面试
                        </span>
                        <span v-if="program.applicationStatus.interview && program.applicationStatus.interviewDate" 
                              class="text-xs text-gray-400">
                          ({{ program.applicationStatus.interviewDate }})
                        </span>
                      </div>
                      <el-button 
                        v-if="program.applicationStatus.submitted && !program.applicationStatus.interview"
                        size="small" 
                        type="warning"
                        @click="handleUpdateApplicationStatus(program.id, 'interview')"
                        class="status-btn"
                      >
                        标记面试
                      </el-button>
                    </div>

                    <!-- 获得Offer状态 -->
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded-full flex items-center justify-center" 
                             :class="program.applicationStatus.offer ? 'bg-green-500' : 'bg-gray-300'">
                          <span v-if="program.applicationStatus.offer" 
                                class="material-icons-outlined text-white text-xs">check</span>
                        </div>
                        <span class="text-sm" :class="program.applicationStatus.offer ? 'text-gray-900 font-medium' : 'text-gray-500'">
                          获得Offer
                        </span>
                        <span v-if="program.applicationStatus.offer && program.applicationStatus.offerDate" 
                              class="text-xs text-gray-400">
                          ({{ program.applicationStatus.offerDate }})
                        </span>
                      </div>
                      <el-button 
                        v-if="program.applicationStatus.submitted && !program.applicationStatus.offer"
                        size="small" 
                        type="success"
                        @click="handleUpdateApplicationStatus(program.id, 'offer')"
                        class="status-btn"
                      >
                        标记Offer
                      </el-button>
                    </div>

                    <!-- Offer详情 -->
                    <div v-if="program.applicationStatus.offer" class="mt-3 pt-3 border-t border-gray-200">
                      <div class="text-xs text-gray-600 space-y-1">
                        <div class="flex items-center justify-between">
                          <span>Offer类型：</span>
                          <span class="font-medium" :class="program.applicationStatus.offerType === 'unconditional' ? 'text-green-600' : 'text-orange-600'">
                            {{ program.applicationStatus.offerType === 'unconditional' ? '无条件' : '有条件' }}
                          </span>
                        </div>
                        <div v-if="program.applicationStatus.conditions && program.applicationStatus.conditions.length > 0">
                          <span>条件要求：</span>
                          <div class="mt-1 flex flex-wrap gap-1">
                            <span v-for="condition in program.applicationStatus.conditions" 
                                  :key="condition"
                                  class="inline-block bg-orange-100 text-orange-700 px-2 py-0.5 rounded text-xs">
                              {{ condition }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!selectedCustomer.schoolPrograms || selectedCustomer.schoolPrograms.length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <span class="material-icons-outlined text-6xl">school</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无申请项目</h3>
            <p class="text-gray-500">该客户尚未添加任何学校项目</p>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="offerManagementDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据

const searchKeyword = ref('')
const progressFilter = ref('')
const serviceStaffFilter = ref('')
const regionFilter = ref('')
const loading = ref(false)
const updating = ref(false)
const cardClickLoading = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(5)

// 对话框状态
const updateProgressDialogVisible = ref(false)
const schoolsDialogVisible = ref(false)
const materialsDialogVisible = ref(false)
const schoolBookDialogVisible = ref(false)
const additionalTermsDialogVisible = ref(false)
const offerManagementDialogVisible = ref(false)
const progressStepsExpanded = ref(false) // 控制进度环节是否展开
const selectedCustomer = ref(null)

// 表单数据
const progressForm = ref({
  currentProgress: '',
  note: '',
  // 新的进度状态设置
  stepStatuses: {}
})

// 进度步骤定义
const progressSteps = ref([
  { key: 'signed_completed', label: '签约完成' },
  { key: 'school_selection', label: '确定选校' },
  { key: 'material_collection', label: '材料收集' },
  { key: 'document_writing', label: '文书写作' },
  { key: 'document_finalization', label: '文书定稿' },
  { key: 'application_submission', label: '申请递交' },
  { key: 'offer_received', label: '获得Offer' },
  { key: 'visa_application', label: '签证申请' },
  { key: 'completed', label: '完成' }
])

// 进度状态定义
const progressStatuses = {
  NOT_STARTED: 'not_started',     // 未开始
  IN_PROGRESS: 'in_progress',     // 进行中
  COMPLETED: 'completed'          // 已完成
}

// 申请材料分类定义
const materialCategories = ref([
  { key: 'id_photo', name: '身份证照片', icon: 'person' },
  { key: 'passport_photo', name: '白底证件照（香港）', icon: 'portrait' },
  { key: 'passport_file', name: '护照文件', icon: 'flight' },
  { key: 'chsi_report', name: '学信网在线认证报告（中英文）', icon: 'verified' },
  { key: 'chsi_transcript', name: '学信网成绩单（中英文）', icon: 'assignment' },
  { key: 'enrollment_cert', name: '学校在读证明（中英文）', icon: 'school' },
  { key: 'transcript', name: '成绩单（中英文）', icon: 'grading' },
  { key: 'gpa_explanation', name: '绩点说明（中英文）', icon: 'info' },
  { key: 'grade_b_cert', name: '成绩B等级证明', icon: 'star' }
])

// 模拟数据
const customers = ref([
  {
    id: 1,
    name: '张小明',
    targetRegion: '中国香港',
    targetMajor: '计算机科学',
    targetSchools: ['香港大学', '香港科技大学', '香港中文大学'],
    serviceTeam: {
      marketingStaff: 'pocky',
      salesStaff: 'pocky',
      documentWriter: 'pocky',
      submissionStaff: 'pocky'
    },
    currentProgress: 'document_writing',
    // 新的详细进度状态
    progressStatus: {
      signed_completed: 'completed',
      school_selection: 'completed',
      material_collection: 'in_progress',
      document_writing: 'in_progress',
      document_finalization: 'not_started',
      application_submission: 'not_started',
      offer_received: 'not_started',
      visa_application: 'not_started',
      completed: 'not_started'
    },
    signedDate: '2025-01-01',
    lastUpdate: '2025-07-15',
    expectedCompletion: '2025-08-30',
    materials: [
      {
        id: '1',
        name: '身份证正面.jpg',
        category: 'id_photo',
        type: 'image/jpeg',
        size: 2048000,
        uploadDate: '2024-01-15'
      },
      {
        id: '2',
        name: '学信网认证报告.pdf',
        category: 'chsi_report',
        type: 'application/pdf',
        size: 1024000,
        uploadDate: '2024-01-14'
      }
    ],
    schoolPrograms: [
      {
        id: '1',
        schoolName: '香港大学',
        schoolEnglishName: 'The University of Hong Kong',
        programName: '金融科技金融学硕士',
        programEnglishName: 'Master of Finance in Financial Technology',
        qsRanking: 11,
        region: '中国香港',
        websiteUrl: 'https://www.hku.hk',
        logo: 'hku',
        type: 'reach',
        addedDate: '2024-01-10',
        applicationStatus: {
          submitted: true,
          submittedDate: '2024-12-01',
          interview: true,
          interviewDate: '2024-12-15',
          offer: true,
          offerDate: '2025-01-10',
          offerType: 'conditional',
          conditions: ['雅思7.0', '本科毕业证'],
          lastUpdated: '2025-01-10'
        }
      },
      {
        id: '2',
        schoolName: '香港科技大学',
        schoolEnglishName: 'The Hong Kong University of Science and Technology',
        programName: '计算机科学理学硕士',
        programEnglishName: 'Master of Science in Computer Science',
        qsRanking: 40,
        region: '中国香港',
        websiteUrl: 'https://www.ust.hk',
        logo: 'hkust',
        type: 'match',
        addedDate: '2024-01-12',
        applicationStatus: {
          submitted: true,
          submittedDate: '2024-11-20',
          interview: true,
          interviewDate: '2024-12-10',
          offer: false,
          lastUpdated: '2024-12-10'
        }
      },
      {
        id: '3',
        schoolName: '香港中文大学',
        schoolEnglishName: 'The Chinese University of Hong Kong',
        programName: '信息工程学理学硕士',
        programEnglishName: 'Master of Science in Information Engineering',
        qsRanking: 38,
        region: '中国香港',
        websiteUrl: 'https://www.cuhk.edu.hk',
        logo: 'cuhk',
        type: 'safety',
        addedDate: '2024-01-15',
        applicationStatus: {
          submitted: true,
          submittedDate: '2024-11-25',
          interview: false,
          offer: false,
          lastUpdated: '2024-11-25'
        }
      }
    ],
    additionalTerms: {
      content: `特别约定条款：

1. 服务时间安排
   - 文书修改不超过3轮，每轮修改时间不超过7个工作日
   - 申请递交时间为2024年12月1日前完成
   - 如遇特殊情况需延期，需提前7天通知

2. 费用相关
   - 如需增加申请学校（超过原定3所），每增加1所学校收费3000元
   - 签证申请服务费用另计，标准收费8000元
   - 所有第三方费用（申请费、邮寄费等）由客户自行承担

3. 责任约定  
   - 客户需在约定时间内提供完整、真实的申请材料
   - 如因客户材料不实导致的后果，由客户承担责任
   - 公司保证申请材料的专业性和时效性

4. 其他约定
   - 客户享有VIP服务，包括专属顾问对接和优先处理权
   - 如获得目标学校offer，客户需配合提供成功案例用于宣传
   - 服务期间如有争议，双方协商解决，协商不成可申请仲裁

备注：本条款由销售顾问pocky于签约时录入，服务老师请重点关注时间节点要求。`,
      createdBy: 'pocky',
      createdDate: '2025-01-01',
      lastModified: '2025-01-01'
    }
  },
  {
    id: 5,
    name: '陈思雨',
    targetRegion: '新加坡',
    targetMajor: '数据科学',
    targetSchools: ['新加坡国立大学', '南洋理工大学'],
    serviceTeam: {
      marketingStaff: 'pocky',
      salesStaff: 'pocky',
      documentWriter: 'pocky',
      submissionStaff: 'pocky'
    },
    currentProgress: 'signed_completed',
    // 新的详细进度状态
    progressStatus: {
      signed_completed: 'completed',
      school_selection: 'not_started',
      material_collection: 'not_started',
      document_writing: 'not_started',
      document_finalization: 'not_started',
      application_submission: 'not_started',
      offer_received: 'not_started',
      visa_application: 'not_started',
      completed: 'not_started'
    },
    signedDate: '2025-05-10',
    lastUpdate: '2025-07-18',
    expectedCompletion: '2025-08-15',
    materials: [],
    schoolPrograms: [],
    additionalTerms: null
  },
  {
    id: 6,
    name: '杨浩然',
    targetRegion: '加拿大',
    targetMajor: '人工智能',
    targetSchools: ['多伦多大学', '滑铁卢大学', '麦吉尔大学'],
    serviceTeam: {
      marketingStaff: 'pocky',
      salesStaff: 'pocky',
      documentWriter: 'pocky',
      submissionStaff: 'pocky'
    },
    currentProgress: 'school_selection',
    // 新的详细进度状态
    progressStatus: {
      signed_completed: 'completed',
      school_selection: 'in_progress',
      material_collection: 'not_started',
      document_writing: 'not_started',
      document_finalization: 'not_started',
      application_submission: 'not_started',
      offer_received: 'not_started',
      visa_application: 'not_started',
      completed: 'not_started'
    },
    signedDate: '2025-06-05',
    lastUpdate: '2025-07-17',
    expectedCompletion: '2025-08-30',
    materials: [],
    schoolPrograms: [],
    additionalTerms: {
      content: `加拿大申请特别条款：

1. 语言成绩要求
   - 雅思成绩需达到7.0分以上，单项不低于6.5分
   - 如首次未达标，公司协助安排一次免费重考指导

2. 签证申请
   - 包含学签申请全程服务
   - 协助准备资金证明和担保材料
   - 签证费用已包含在服务费中

3. 时间安排
   - 2025年1月15日前完成所有申请递交
   - 文书修改限3轮，每轮7个工作日内完成`,
      createdBy: 'pocky',
      createdDate: '2025-06-05',
      lastModified: '2025-06-05'
    }
  },
  {
    id: 2,
    name: '徐家棚',
    targetRegion: '英国',
    targetMajor: '商科',
    targetSchools: ['帝国理工学院', '伦敦政治经济学院'],
    serviceTeam: {
      marketingStaff: 'pocky',
      salesStaff: 'pocky',
      documentWriter: 'pocky',
      submissionStaff: 'pocky'
    },
    currentProgress: 'material_collection',
    // 新的详细进度状态
    progressStatus: {
      signed_completed: 'completed',
      school_selection: 'completed',
      material_collection: 'in_progress',
      document_writing: 'not_started',
      document_finalization: 'not_started',
      application_submission: 'not_started',
      offer_received: 'not_started',
      visa_application: 'not_started',
      completed: 'not_started'
    },
    signedDate: '2025-07-20',
    lastUpdate: '2025-07-20',
    expectedCompletion: '2025-08-15',
    materials: [],
    schoolPrograms: [],
    additionalTerms: null
  },
  {
    id: 3,
    name: '王强',
    targetRegion: '美国',
    targetMajor: '金融',
    targetSchools: ['哥伦比亚大学', '纽约大学'],
    serviceTeam: {
      marketingStaff: 'pocky',
      salesStaff: 'pocky',
      documentWriter: 'pocky',
      submissionStaff: 'pocky'
    },
    currentProgress: 'application_submission',
    // 新的详细进度状态
    progressStatus: {
      signed_completed: 'completed',
      school_selection: 'completed',
      material_collection: 'completed',
      document_writing: 'completed',
      document_finalization: 'completed',
      application_submission: 'in_progress',
      offer_received: 'not_started',
      visa_application: 'not_started',
      completed: 'not_started'
    },
    signedDate: '2025-03-15',
    lastUpdate: '2025-07-21',
    expectedCompletion: '2025-08-28',
    materials: [],
    schoolPrograms: [],
    additionalTerms: {
      content: `美国金融专业申请条款：

1. GMAT成绩要求
   - 目标GMAT成绩720分以上
   - 如未达标，提供一次免费GMAT培训课程

2. 实习经历要求  
   - 需提供至少2段金融相关实习经历
   - 公司可协助推荐实习机会

3. 申请时间安排
   - Round 1申请截止时间：2024年11月15日
   - 所有材料需提前2周准备完毕

4. 面试辅导
   - 包含3次一对一面试模拟训练
   - 提供常见面试问题题库`,
      createdBy: 'pocky',
      createdDate: '2025-03-15',
      lastModified: '2025-03-15'
    }
  },
  {
    id: 4,
    name: '赵小鹏',
    targetRegion: '澳洲',
    targetMajor: '工程',
    targetSchools: ['墨尔本大学', '悉尼大学'],
    serviceTeam: {
      marketingStaff: 'pocky',
      salesStaff: 'pocky',
      documentWriter: 'pocky',
      submissionStaff: 'pocky'
    },
    currentProgress: 'completed',
    // 新的详细进度状态
    progressStatus: {
      signed_completed: 'completed',
      school_selection: 'completed',
      material_collection: 'completed',
      document_writing: 'completed',
      document_finalization: 'completed',
      application_submission: 'completed',
      offer_received: 'completed',
      visa_application: 'completed',
      completed: 'completed'
    },
    signedDate: '2025-04-01',
    lastUpdate: '2025-07-22',
    expectedCompletion: '2025-08-28',
    materials: [],
    schoolPrograms: [],
    additionalTerms: {
      content: `澳洲工程专业申请条款（已完成）：

1. 学历认证
   - ✅ 已完成工程师协会EA认证
   - ✅ 学历等级评估已通过

2. 英语成绩
   - ✅ 雅思7.0分已达标
   - ✅ 单项成绩均满足要求

3. 签证申请
   - ✅ 学生签证已获批
   - ✅ 体检和保险已完成

4. 后续服务
   - ✅ 住宿安排已协助完成
   - ✅ 接机服务已预约

备注：该客户服务已全部完成，可作为成功案例参考。`,
      createdBy: 'pocky',
      createdDate: '2025-04-01',
      lastModified: '2025-07-22'
    }
  }
])

// 统计数据
const statistics = computed(() => {
  const total = customers.value.length
  const signedCompleted = customers.value.filter(c => c.currentProgress === 'signed_completed').length
  const schoolSelection = customers.value.filter(c => c.currentProgress === 'school_selection').length
  const materialCollection = customers.value.filter(c => c.currentProgress === 'material_collection').length
  const documentWriting = customers.value.filter(c => c.currentProgress === 'document_writing').length
  const documentFinalization = customers.value.filter(c => c.currentProgress === 'document_finalization').length
  const applicationSubmission = customers.value.filter(c => c.currentProgress === 'application_submission').length
  const offerReceived = customers.value.filter(c => c.currentProgress === 'offer_received').length
  const visaApplication = customers.value.filter(c => c.currentProgress === 'visa_application').length
  const completed = customers.value.filter(c => c.currentProgress === 'completed').length

  return {
    total,
    signedCompleted,
    schoolSelection,
    materialCollection,
    documentWriting,
    documentFinalization,
    applicationSubmission,
    offerReceived,
    visaApplication,
    completed
  }
})

// 计算属性：筛选后的客户列表
const filteredCustomers = computed(() => {
  let filtered = customers.value

  // 进度筛选
  if (progressFilter.value) {
    filtered = filtered.filter(customer => customer.currentProgress === progressFilter.value)
  }

  // 服务人员筛选
  if (serviceStaffFilter.value) {
    filtered = filtered.filter(customer => {
      const team = customer.serviceTeam
      return team.marketingStaff === serviceStaffFilter.value ||
             team.salesStaff === serviceStaffFilter.value ||
             team.documentWriter === serviceStaffFilter.value ||
             team.submissionStaff === serviceStaffFilter.value
    })
  }

  // 地区筛选
  if (regionFilter.value) {
    filtered = filtered.filter(customer => customer.targetRegion === regionFilter.value)
  }

  // 按状态和服务时长排序（已完成的放最后，其他按服务时长降序）
  filtered = filtered.sort((a, b) => {
    // 先按完成状态排序：未完成的在前，已完成的在后
    const isCompletedA = a.currentProgress === 'completed'
    const isCompletedB = b.currentProgress === 'completed'
    
    if (isCompletedA && !isCompletedB) return 1   // A已完成，B未完成 -> A排后面
    if (!isCompletedA && isCompletedB) return -1  // A未完成，B已完成 -> A排前面
    
    // 如果状态相同，再按服务时长排序（天数越长越靠前）
    const durationA = getServiceDuration(a.signedDate)
    const durationB = getServiceDuration(b.signedDate)
    return durationB - durationA // 降序排列，时长长的在前
  })

  return filtered
})

// 计算属性：总页数
const totalPages = computed(() => {
  return Math.ceil(filteredCustomers.value.length / pageSize.value)
})

// 计算属性：当前页的客户列表
const paginatedCustomers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredCustomers.value.slice(start, end)
})

// 方法：获取客户名称首字母
const getCustomerInitials = (name) => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

// 方法：计算服务时长（天数）
const getServiceDuration = (signedDate) => {
  if (!signedDate) return 0
  const signed = new Date(signedDate)
  const today = new Date()
  const diffTime = Math.abs(today - signed)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

// 方法：获取进度百分比
const getProgressPercentage = (currentProgress) => {
  const stepIndex = progressSteps.value.findIndex(step => step.key === currentProgress)
  if (stepIndex === -1) return 0
  return ((stepIndex + 1) / progressSteps.value.length) * 100
}

// 方法：获取进度状态标签
const getProgressStatusLabel = (currentProgress) => {
  if (currentProgress === 'completed') {
    return '已完成'
  }
  // 可以根据需要添加暂停服务的判断逻辑
  // if (customer.isPaused) {
  //   return '暂停服务'
  // }
  return '服务中'
}

// 方法：获取进度状态样式
const getProgressStatusClass = (currentProgress) => {
  // 简化颜色：过程中为蓝色，完成为绿色
  return currentProgress === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
}

// 方法：获取进度条样式
const getProgressBarClass = (currentProgress) => {
  // 过程中都是蓝色，完成为绿色
  if (currentProgress === 'completed') {
    return 'bg-green-500'
  }
  return 'bg-blue-500'
}

// 方法：判断步骤是否完成
const isStepCompleted = (currentProgress, stepKey, stepIndex) => {
  const currentIndex = progressSteps.value.findIndex(step => step.key === currentProgress)
  return stepIndex <= currentIndex
}

// 方法：获取步骤圆圈样式
const getStepCircleClass = (currentProgress, stepKey, stepIndex) => {
  const currentIndex = progressSteps.value.findIndex(step => step.key === currentProgress)
  
  if (currentProgress === 'completed' && stepIndex <= currentIndex) {
    // 全部完成时，所有步骤都是绿色
    return 'border-green-500 bg-green-500'
  } else if (stepIndex < currentIndex) {
    // 已完成的步骤（非全部完成状态）
    return 'border-blue-500 bg-blue-500'
  } else if (stepIndex === currentIndex) {
    // 当前步骤
    if (currentProgress === 'completed') {
      return 'border-green-500 bg-green-500'
    }
    return 'border-blue-500 bg-blue-500'
  } else {
    // 未完成的步骤
    return 'border-gray-300 bg-white'
  }
}

// 方法：获取步骤样式
const getStepClass = (currentProgress, stepKey, stepIndex) => {
  const currentIndex = progressSteps.value.findIndex(step => step.key === currentProgress)
  
  if (stepIndex <= currentIndex) {
    return 'step-active'
  } else {
    return 'step-inactive'
  }
}

// 方法：获取步骤标签样式
const getStepLabelClass = (currentProgress, stepKey, stepIndex) => {
  const currentIndex = progressSteps.value.findIndex(step => step.key === currentProgress)
  
  if (stepIndex <= currentIndex) {
    return 'text-gray-900 font-medium'
  } else {
    return 'text-gray-400'
  }
}

// 方法：获取进度步骤索引
const getProgressStepIndex = (progressKey) => {
  return progressSteps.value.findIndex(step => step.key === progressKey)
}

// 新增：获取步骤状态标签
const getStepStatusLabel = (status) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'in_progress':
      return '进行中'
    case 'not_started':
    default:
      return '未开始'
  }
}

// 新增：获取步骤状态徽章样式
const getStepStatusBadgeClass = (status) => {
  switch (status) {
    case 'completed':
      return 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800'
    case 'in_progress':
      return 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
    case 'not_started':
    default:
      return 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800'
  }
}

// 新增：检查是否有进度变化
const hasProgressChanges = computed(() => {
  if (!selectedCustomer.value || !selectedCustomer.value.progressStatus) return false
  
  return Object.keys(progressForm.value.stepStatuses).some(stepKey => {
    const currentStatus = selectedCustomer.value.progressStatus[stepKey] || 'not_started'
    const newStatus = progressForm.value.stepStatuses[stepKey]
    return newStatus && newStatus !== currentStatus
  })
})

// 新增：根据详细状态计算当前进度
const calculateCurrentProgress = (progressStatus) => {
  // 如果所有步骤都完成，返回completed
  const allStepsCompleted = progressSteps.value.slice(0, -1).every(step => 
    progressStatus[step.key] === 'completed'
  )
  
  if (allStepsCompleted) {
    return 'completed'
  }
  
  // 找到最后一个进行中或已完成的步骤
  let lastActiveStep = 'signed_completed'
  
  for (let i = progressSteps.value.length - 2; i >= 0; i--) {
    const step = progressSteps.value[i]
    const status = progressStatus[step.key]
    
    if (status === 'completed' || status === 'in_progress') {
      lastActiveStep = step.key
      break
    }
  }
  
  return lastActiveStep
}

// 新增：获取新的步骤圆圈样式
const getNewStepCircleClass = (progressStatus, stepKey) => {
  const status = progressStatus?.[stepKey] || 'not_started'
  
  switch (status) {
    case 'completed':
      return 'border-green-500 bg-green-500'
    case 'in_progress':
      return 'border-blue-500 bg-blue-500'
    case 'not_started':
    default:
      return 'border-gray-300 bg-white'
  }
}

// 新增：获取新的步骤标签样式
const getNewStepLabelClass = (progressStatus, stepKey) => {
  const status = progressStatus?.[stepKey] || 'not_started'
  
  switch (status) {
    case 'completed':
    case 'in_progress':
      return 'text-gray-900 font-medium'
    case 'not_started':
    default:
      return 'text-gray-400'
  }
}

// 新增：获取新的进度百分比
const getNewProgressPercentage = (progressStatus) => {
  if (!progressStatus) return 0
  
  // 计算进度：已完成步骤 + 进行中步骤 * 0.5
  let completedCount = 0
  let inProgressCount = 0
  const totalSteps = progressSteps.value.length - 1 // 排除 'completed' 步骤
  
  progressSteps.value.slice(0, -1).forEach(step => {
    const status = progressStatus[step.key]
    if (status === 'completed') {
      completedCount++
    } else if (status === 'in_progress') {
      inProgressCount++
    }
  })
  
  const progress = (completedCount + inProgressCount * 0.5) / totalSteps
  return Math.min(progress * 100, 100)
}

// 新增：获取新的进度条样式
const getNewProgressBarClass = (progressStatus) => {
  if (!progressStatus) return 'bg-gradient-to-r from-blue-500 to-indigo-600'
  
  // 检查是否所有步骤都完成
  const allCompleted = progressSteps.value.slice(0, -1).every(step => 
    progressStatus[step.key] === 'completed'
  )
  
  if (allCompleted) {
    return 'bg-gradient-to-r from-green-500 to-emerald-600'
  }
  
  return 'bg-gradient-to-r from-blue-500 to-indigo-600'
}

// 重置筛选
const handleResetFilter = () => {
  searchKeyword.value = ''
  progressFilter.value = ''
  serviceStaffFilter.value = ''
  regionFilter.value = ''
  currentPage.value = 1
}

// 分页处理
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 事件处理：添加客户
const handleAddCustomer = () => {
     ElMessage.info('添加客户功能开发中...')
}

// 事件处理：客户操作
const handleCustomerAction = async (command, customer) => {
  switch (command) {
    case 'edit':
             ElMessage.info('编辑信息功能开发中...')
      break
    case 'update_progress':
      handleUpdateProgress(customer)
      break
  }
}

// 事件处理：更新进度
const handleUpdateProgress = (customer) => {
  selectedCustomer.value = customer
  
  // 初始化表单，复制当前的进度状态
  const currentStepStatuses = {}
  progressSteps.value.forEach(step => {
    if (step.key !== 'completed') {
      currentStepStatuses[step.key] = customer.progressStatus?.[step.key] || 'not_started'
    }
  })
  
  progressForm.value = {
    currentProgress: customer.currentProgress,
    note: '',
    stepStatuses: currentStepStatuses
  }
  
  // 重置展开状态为收起
  progressStepsExpanded.value = false
  
  updateProgressDialogVisible.value = true
}

// 事件处理：确认更新进度
const handleConfirmUpdateProgress = async () => {
  try {
    updating.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新客户数据
    const customer = customers.value.find(c => c.id === selectedCustomer.value.id)
    if (customer) {
      // 更新进度状态
      if (!customer.progressStatus) {
        customer.progressStatus = {}
      }
      
      // 应用所有状态变更
      Object.keys(progressForm.value.stepStatuses).forEach(stepKey => {
        const newStatus = progressForm.value.stepStatuses[stepKey]
        if (newStatus) {
          customer.progressStatus[stepKey] = newStatus
        }
      })
      
      // 根据状态自动计算当前进度
      customer.currentProgress = calculateCurrentProgress(customer.progressStatus)
      customer.lastUpdate = new Date().toISOString().split('T')[0]
    }
    
    ElMessage.success('客户进度更新成功')
    updateProgressDialogVisible.value = false
    
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('更新失败，请重试')
  } finally {
    updating.value = false
  }
}

// 事件处理：显示学校详情
const handleShowSchools = (customer) => {
  selectedCustomer.value = customer
  schoolsDialogVisible.value = true
}

// 关闭学校详情对话框
const handleCloseSchoolsDialog = () => {
  schoolsDialogVisible.value = false
  selectedCustomer.value = null
}

// 关闭更新进度对话框
const handleCloseUpdateProgressDialog = () => {
  updateProgressDialogVisible.value = false
  selectedCustomer.value = null
}

// 进度相关的样式函数已整合到上方

// 进度卡片相关方法
// 获取进度卡片样式
const getProgressCardStyle = (index) => {
  const styles = [
    'bg-gradient-to-br from-emerald-50 to-green-50 border-emerald-100/50',
    'bg-gradient-to-br from-cyan-50 to-teal-50 border-cyan-100/50', 
    'bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-100/50',
    'bg-gradient-to-br from-purple-50 to-violet-50 border-purple-100/50',
    'bg-gradient-to-br from-indigo-50 to-blue-50 border-indigo-100/50',
    'bg-gradient-to-br from-orange-50 to-red-50 border-orange-100/50',
    'bg-gradient-to-br from-rose-50 to-pink-50 border-rose-100/50',
    'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100/50',
    'bg-gradient-to-br from-lime-50 to-green-50 border-lime-100/50'
  ]
  return styles[index] || styles[0]
}

// 获取进度卡片图标颜色
const getProgressCardIconColor = (index) => {
  const colors = [
    'text-emerald-600',
    'text-cyan-600', 
    'text-yellow-600',
    'text-purple-600',
    'text-indigo-600',
    'text-orange-600',
    'text-rose-600',
    'text-blue-600',
    'text-lime-600'
  ]
  return colors[index] || colors[0]
}

// 获取进度卡片图标
const getProgressCardIcon = (progressKey) => {
  const iconMap = {
    'signed_completed': 'verified',
    'school_selection': 'school',
    'material_collection': 'folder',
    'document_writing': 'edit',
    'document_finalization': 'description',
    'application_submission': 'send',
    'offer_received': 'emoji_events',
    'visa_application': 'flight_takeoff',
    'completed': 'check_circle'
  }
  return iconMap[progressKey] || 'circle'
}

// 获取进度统计数据
const getProgressStatistic = (progressKey) => {
  const statMap = {
    'signed_completed': statistics.value.signedCompleted,
    'school_selection': statistics.value.schoolSelection,
    'material_collection': statistics.value.materialCollection,
    'document_writing': statistics.value.documentWriting,
    'document_finalization': statistics.value.documentFinalization,
    'application_submission': statistics.value.applicationSubmission,
    'offer_received': statistics.value.offerReceived,
    'visa_application': statistics.value.visaApplication,
    'completed': statistics.value.completed
  }
  return statMap[progressKey] || 0
}

// 处理进度卡片点击
const handleProgressCardClick = (progressKey) => {
  // 防止快速重复点击
  if (cardClickLoading.value) return
  
  cardClickLoading.value = true
  
  // 如果点击的是当前已选中的卡片，则取消筛选
  if (progressFilter.value === progressKey) {
    progressFilter.value = ''
  } else {
    // 否则设置新的筛选条件
    progressFilter.value = progressKey
  }
  
  // 200ms后恢复点击
  setTimeout(() => {
    cardClickLoading.value = false
  }, 200)
}

// 申请材料相关方法
// 事件处理：显示申请材料
const handleShowMaterials = (customer) => {
  selectedCustomer.value = customer
  materialsDialogVisible.value = true
}

// 关闭申请材料对话框
const handleCloseMaterialsDialog = () => {
  materialsDialogVisible.value = false
  selectedCustomer.value = null
}

// 获取分类下的材料列表
const getMaterialsByCategory = (categoryKey) => {
  if (!selectedCustomer.value?.materials) return []
  return selectedCustomer.value.materials.filter(material => material.category === categoryKey)
}

// 获取分类下的材料数量
const getMaterialCount = (categoryKey) => {
  return getMaterialsByCategory(categoryKey).length
}

// 获取文件图标
const getFileIcon = (fileType) => {
  if (fileType.includes('image')) return 'image'
  if (fileType.includes('pdf')) return 'picture_as_pdf'
  if (fileType.includes('word') || fileType.includes('document')) return 'description'
  if (fileType.includes('zip') || fileType.includes('rar')) return 'archive'
  return 'attach_file'
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 定校书相关方法
// 事件处理：显示定校书
const handleShowSchoolBook = (customer) => {
  selectedCustomer.value = customer
  schoolBookDialogVisible.value = true
}

// 关闭定校书对话框
const handleCloseSchoolBookDialog = () => {
  schoolBookDialogVisible.value = false
  selectedCustomer.value = null
}

// 获取项目类型标签
const getProgramTypeLabel = (type) => {
  const typeMap = {
    'reach': '冲刺',
    'match': '匹配',
    'safety': '保底'
  }
  return typeMap[type] || '未知'
}

// 获取项目类型颜色
const getProgramTypeColor = (type) => {
  const colorMap = {
    'reach': 'bg-red-500',
    'match': 'bg-yellow-500', 
    'safety': 'bg-green-500'
  }
  return colorMap[type] || 'bg-gray-500'
}

// 打开学校官网
const handleOpenWebsite = (url) => {
  if (url) {
    window.open(url, '_blank')
  }
}

// 附加条款相关方法
// 事件处理：显示附加条款
const handleShowAdditionalTerms = (customer) => {
  selectedCustomer.value = customer
  additionalTermsDialogVisible.value = true
}

// 关闭附加条款对话框
const handleCloseAdditionalTermsDialog = () => {
  additionalTermsDialogVisible.value = false
  selectedCustomer.value = null
}

// Offer管理相关方法
// 事件处理：显示Offer管理
const handleShowOfferManagement = (customer) => {
  selectedCustomer.value = customer
  offerManagementDialogVisible.value = true
}

// 关闭Offer管理对话框
const handleCloseOfferManagementDialog = () => {
  offerManagementDialogVisible.value = false
  selectedCustomer.value = null
}

// 获取Offer统计数据
const getOfferStatistics = () => {
  if (!selectedCustomer.value?.schoolPrograms) {
    return { total: 0, submitted: 0, interview: 0, offer: 0, pending: 0 }
  }
  
  const programs = selectedCustomer.value.schoolPrograms
  const total = programs.length
  const submitted = programs.filter(p => p.applicationStatus.submitted).length
  const interview = programs.filter(p => p.applicationStatus.interview).length
  const offer = programs.filter(p => p.applicationStatus.offer).length
  const pending = submitted - offer
  
  return { total, submitted, interview, offer, pending }
}

// 更新申请状态
const handleUpdateApplicationStatus = async (programId, statusType) => {
  try {
    // 找到对应的项目
    const customer = selectedCustomer.value
    const programIndex = customer.schoolPrograms.findIndex(p => p.id === programId)
    
    if (programIndex === -1) return
    
    const program = customer.schoolPrograms[programIndex]
    const currentDate = new Date().toISOString().split('T')[0]
    
    // 根据状态类型更新相应字段
    switch (statusType) {
      case 'submitted':
        program.applicationStatus.submitted = true
        program.applicationStatus.submittedDate = currentDate
        break
      case 'interview':
        program.applicationStatus.interview = true
        program.applicationStatus.interviewDate = currentDate
        break
      case 'offer':
        program.applicationStatus.offer = true
        program.applicationStatus.offerDate = currentDate
        program.applicationStatus.offerType = 'conditional' // 默认为有条件offer
        program.applicationStatus.conditions = ['待确认具体条件']
        break
    }
    
    program.applicationStatus.lastUpdated = currentDate
    
    // 更新客户数据
    const customerIndex = customers.value.findIndex(c => c.id === customer.id)
    if (customerIndex !== -1) {
      customers.value[customerIndex].schoolPrograms[programIndex] = program
    }
    
    const statusLabels = {
      'submitted': '已递交',
      'interview': '获得面试',
      'offer': '获得Offer'
    }
    
    ElMessage.success(`已更新${program.schoolName}申请状态为：${statusLabels[statusType]}`)
    
  } catch (error) {
    console.error('更新申请状态失败:', error)
    ElMessage.error('更新申请状态失败，请重试')
  }
}

// 组件挂载
onMounted(() => {
  // 初始化数据
  console.log('客户管理页面加载完成')
})
</script>

<style scoped>
/* CRM 页面样式 - 遵循 CRM 系统设计规范 */
.crm-page {
  @apply p-6;
}

/* Pro Card 样式 */
.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.pro-card-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.pro-card-title {
  @apply flex items-center text-lg font-medium text-gray-900;
}

.pro-card-title .icon {
  @apply mr-2 text-gray-500;
}

.pro-card-body {
  @apply p-6;
}

/* 进度卡片样式 */
.progress-card {
  @apply cursor-pointer select-none;
}

.progress-card:hover {
  @apply scale-105;
}

.progress-card:active {
  @apply scale-95;
}

/* 客户列表过渡动画 */
.customer-list-enter-active,
.customer-list-leave-active {
  transition: opacity 0.2s ease;
}

.customer-list-enter-from,
.customer-list-leave-to {
  opacity: 0;
}

.customer-list-enter-to,
.customer-list-leave-from {
  opacity: 1;
}

/* 客户卡片样式 */
.customer-card {
  transition: all 0.3s ease;
}

.customer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 学校按钮样式 */
.schools-button {
  --el-button-text-color: #4F46E5 !important;
  --el-button-border-color: #4F46E5 !important;
  --el-button-bg-color: transparent !important;
  --el-button-hover-text-color: #4338CA !important;
  --el-button-hover-border-color: #4338CA !important;
  --el-button-hover-bg-color: #F0F0FF !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: auto !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

/* 进度条样式 */
.progress-wrapper {
  position: relative;
  @apply mt-4 mb-4;
  height: 50px;
}

.progress-steps-wrapper {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 10;
  height: 100%;
}

.progress-step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-node {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 15;
  transition: all 0.3s ease;
}

.step-label-new {
  font-size: 11px;
  margin-top: 8px;
  text-align: center;
  white-space: nowrap;
  max-width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.progress-track-wrapper {
  position: absolute;
  top: 6px;
  left: 0;
  right: 0;
  height: 2px;
  z-index: 5;
  padding: 0 6px;
}

.progress-track-bg {
  width: 100%;
  height: 100%;
  background-color: #e5e7eb;
  border-radius: 1px;
  position: relative;
}

.progress-fill-new {
  height: 100%;
  border-radius: 1px;
  transition: width 0.5s ease;
}

/* 客户卡片样式优化 */
.customer-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 - 使用系统紫色主题 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

.add-customer-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.add-customer-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 简洁对话框样式 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  .el-button--primary {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
  }
}

/* 输入框和选择框紫色主题 */
:deep(.el-input .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-input .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

:deep(.el-select) {
  --el-color-primary: #4F46E5;
}

/* 客户管理操作下拉框样式 */
.action-dropdown-trigger {
  color: #6B7280 !important;
  padding: 4px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.action-dropdown-trigger:hover {
  color: #4F46E5 !important;
  background-color: #F3F4F6 !important;
}

/* 下拉菜单样式 */
:deep(.management-dropdown-menu) {
  border-radius: 7px !important;
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.13) !important;
  border: 1px solid #E5E7EB !important;
  padding: 3px 0 !important;
  min-width: 130px !important;
}

:deep(.dropdown-menu-item) {
  padding: 7px 14px !important;
  margin: 1px 3px !important;
  border-radius: 5px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

:deep(.dropdown-menu-item:hover) {
  background-color: #F9FAFB !important;
}

/* 申请材料对话框样式 */
.material-category-card {
  transition: all 0.2s ease;
}

.material-category-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 定校书对话框样式 */
.school-program-card {
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
}

.school-program-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

/* Offer管理对话框样式 */
.offer-program-card {
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
}

.offer-program-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  border-color: #d1d5db;
}

.status-btn {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: 28px !important;
  min-width: 60px !important;
}

/* 客户卡片响应式设计 */
@media (max-width: 1024px) {
  .crm-page {
    @apply p-4;
  }
  
  .customer-card {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .crm-page {
    @apply p-3;
  }
  
  .customer-card {
    padding: 0.75rem;
  }
  
  /* 第一行布局调整 */
  .customer-card > div:first-child {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  /* 操作按钮行调整 */
  .customer-card > div:last-child {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  /* 服务团队信息移动端调整 */
  .customer-card > div:last-child > div:first-child {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  /* 操作按钮组调整 */
  .customer-card > div:last-child > div:last-child {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .customer-card > div:last-child > div:last-child button {
    font-size: 12px;
    padding: 0.25rem 0.5rem;
  }
  
  /* 进度条移动端调整 */
  .step-label-new {
    font-size: 10px;
    max-width: 50px;
  }
  
  .progress-wrapper {
    height: 45px;
  }
  
  .step-node {
    width: 10px;
    height: 10px;
  }
  
  /* 新增：进度状态单选按钮样式 */
  .el-radio {
    margin-right: 16px;
    white-space: nowrap;
  }
  
  .el-radio__label {
    padding-left: 8px;
    font-size: 14px;
  }
  
  /* 展开按钮旋转动画 */
  .material-icons-outlined {
    transition: transform 0.3s ease;
  }
  
  .rotate-180 {
    transform: rotate(180deg);
  }
  
  /* 进度条符号样式优化 */
  .step-node .material-icons-outlined {
    font-weight: 900 !important;
  }
  
  .step-node span {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>