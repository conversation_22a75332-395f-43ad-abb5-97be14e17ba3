<template>
  <div class="crm-system-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span class="text-gray-900">CRM系统</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">CRM客户关系管理系统</h2>
    </div>

    <!-- 个人身份：显示访问提示 -->
    <div v-if="!isOrganizationIdentity" class="pro-card">
      <div class="pro-card-body">
        <div class="text-center py-8">
          <div class="text-gray-400 mb-4">
            <span class="material-icons-outlined text-6xl">business_center</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">需要组织账号访问</h3>
          <p class="text-gray-600 mb-6">
            CRM系统功能仅限组织账号使用。请切换到组织身份或联系管理员获取组织账号权限。
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <el-button 
              type="primary" 
              @click="handleShowIdentityDialog"
              class="dialog-button-primary"
            >
              <span class="material-icons-outlined text-sm mr-2">switch_account</span>
              切换到组织身份
            </el-button>
            <el-button 
              @click="$router.push('/enterprise/accounts')"
              class="dialog-button-secondary"
            >
              <span class="material-icons-outlined text-sm mr-2">add_business</span>
              创建新组织
            </el-button>
          </div>
        </div>
        
        <!-- 功能预览 -->
        <div class="border-t border-gray-200 pt-6">
          <div class="mb-4">
            <h4 class="text-lg font-semibold text-gray-900 flex items-center">
              <span class="material-icons-outlined text-gray-600 mr-2">business_center</span>
              功能模块预览
            </h4>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- 市场资源 -->
            <div class="bg-gray-50 rounded-lg p-4 opacity-60">
              <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <span class="material-icons-outlined text-blue-600">campaign</span>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900">市场资源</h4>
                </div>
              </div>
              <p class="text-sm text-gray-600">管理市场线索和潜在客户</p>
            </div>

            <!-- 有效客户 -->
            <div class="bg-gray-50 rounded-lg p-4 opacity-60">
              <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <span class="material-icons-outlined text-green-600">people</span>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900">有效客户</h4>
                </div>
              </div>
              <p class="text-sm text-gray-600">管理已转化的有效客户</p>
            </div>

            <!-- 签约客户 -->
            <div class="bg-gray-50 rounded-lg p-4 opacity-60">
              <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <span class="material-icons-outlined text-purple-600">verified</span>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900">签约客户</h4>
                </div>
              </div>
              <p class="text-sm text-gray-600">管理已签约的正式客户</p>
            </div>

            <!-- 组织管理 -->
            <div class="bg-gray-50 rounded-lg p-4 opacity-60">
              <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                  <span class="material-icons-outlined text-indigo-600">corporate_fare</span>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900">组织管理</h4>
                </div>
              </div>
              <p class="text-sm text-gray-600">管理组织架构和成员</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 组织身份：显示功能模块 -->
    <div v-else class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">business_center</span>
          功能模块
        </div>
      </div>
      
      <div class="pro-card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 市场资源 -->
          <router-link 
            to="/crm/market-resources" 
            class="block bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100"
          >
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <span class="material-icons-outlined text-blue-600">campaign</span>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">市场资源</h4>
              </div>
            </div>
            <p class="text-sm text-gray-600">管理市场线索和潜在客户</p>
          </router-link>

          <!-- 有效客户 -->
          <router-link 
            to="/crm/valid-customers" 
            class="block bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100"
          >
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <span class="material-icons-outlined text-green-600">people</span>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">有效客户</h4>
              </div>
            </div>
            <p class="text-sm text-gray-600">管理已转化的有效客户</p>
          </router-link>

          <!-- 签约客户 -->
          <router-link 
            to="/crm/customer-management" 
            class="block bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100"
          >
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <span class="material-icons-outlined text-purple-600">verified</span>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">签约客户</h4>
              </div>
            </div>
            <p class="text-sm text-gray-600">管理已签约的正式客户</p>
          </router-link>

          <!-- 组织管理 -->
          <router-link 
            to="/crm/organization/departments" 
            class="block bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100"
          >
            <div class="flex items-center mb-3">
              <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                <span class="material-icons-outlined text-indigo-600">corporate_fare</span>
              </div>
              <div>
                <h4 class="font-medium text-gray-900">组织管理</h4>
              </div>
            </div>
            <p class="text-sm text-gray-600">管理组织架构和成员</p>
          </router-link>
        </div>
      </div>
    </div>
    
    <!-- 身份切换对话框 -->
    <el-dialog
      v-model="showIdentityDialog"
      title="选择工作身份"
      width="500px"
      class="simple-dialog compact-dialog"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      @close="showIdentityDialog = false"
    >
      <div class="simple-content">
        <div class="mb-6">
          <p class="text-sm text-slate-500 leading-6 text-center mb-6 font-medium">
            选择您要使用的工作身份，不同身份下的数据和权限完全独立
          </p>
          
          <!-- 身份选择卡片 -->
          <div class="space-y-3">
            <!-- 组织身份选项 -->
            <div 
              v-for="orgIdentity in authStore.availableIdentities?.filter(i => i.identity_type === 'organization')"
              :key="orgIdentity.organization_id"
              @click="handleSelectIdentity({ 
                identity_type: 'organization', 
                organization_id: orgIdentity.organization_id 
              })"
              :class="[
                'group relative bg-white rounded-2xl p-5 border-2 cursor-pointer transition-all duration-300',
                'hover:shadow-lg hover:border-indigo-300 hover:-translate-y-0.5',
                isSelectedIdentity('organization', orgIdentity.organization_id)
                  ? 'border-indigo-500 bg-gradient-to-r from-indigo-50 to-blue-50 shadow-md' 
                  : 'border-gray-200'
              ]"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div :class="[
                    'w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300',
                    isSelectedIdentity('organization', orgIdentity.organization_id)
                      ? 'bg-indigo-500 shadow-lg shadow-indigo-200' 
                      : 'bg-gray-400 group-hover:bg-indigo-400'
                  ]">
                    <span class="material-icons-outlined text-white text-lg">business</span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-3 mb-1">
                      <h3 class="text-lg font-semibold text-gray-900 truncate">{{ orgIdentity.organization_name }}</h3>
                      <span :class="[
                        'px-2.5 py-1 text-xs font-medium rounded-full flex-shrink-0 border',
                        orgIdentity.organization_role === 'owner' 
                          ? 'bg-amber-100 text-amber-700 border-amber-200' 
                          : 'bg-blue-100 text-blue-700 border-blue-200'
                      ]">
                        {{ orgIdentity.organization_role === 'owner' ? '管理员' : '成员' }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-600">
                      组织成员身份，享受团队协作功能
                    </p>
                  </div>
                </div>
                <div class="flex-shrink-0">
                  <div v-if="isSelectedIdentity('organization', orgIdentity.organization_id)" 
                       class="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                    <span class="material-icons-outlined text-white text-sm">check</span>
                  </div>
                  <div v-else class="w-6 h-6 border-2 border-gray-300 rounded-full group-hover:border-indigo-400 transition-colors duration-300"></div>
                </div>
              </div>
            </div>
            
            <!-- 无组织身份时的提示 -->
            <div 
              v-if="!authStore.availableIdentities?.some(i => i.identity_type === 'organization')"
              class="mt-3 px-4 py-6 border border-dashed border-gray-200 rounded-xl bg-gradient-to-br from-slate-50 to-gray-50"
            >
              <div class="text-center py-6">
                <div class="text-gray-400 mb-2">
                  <span class="material-icons-outlined text-4xl">business</span>
                </div>
                <p class="text-sm text-gray-500 mb-2">暂无组织身份</p>
                <p class="text-xs text-gray-400">创建或加入组织后即可使用组织身份</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-center">
          <el-button 
            type="primary" 
            @click="handleConfirmIdentitySelection"
            size="default"
            class="confirm-button"
            :disabled="!selectedIdentity"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

// 使用组织权限管理组合式函数
const { isOrganizationIdentity } = useOrganizationPermissions()

// 身份切换对话框状态
const showIdentityDialog = ref(false)
// 选中的待切换身份（先选择，后确认）
const selectedIdentity = ref(null)

// 当前身份信息
const currentIdentity = computed(() => authStore.currentIdentity)

// 处理显示身份切换对话框
const handleShowIdentityDialog = () => {
  // 打开时默认选中当前身份
  if (currentIdentity.value?.identity_type === 'organization') {
    selectedIdentity.value = {
      identity_type: 'organization',
      organization_id: currentIdentity.value.organization_id
    }
  } else {
    selectedIdentity.value = null
  }
  showIdentityDialog.value = true
}

// 先选择，再确认切换
const handleSelectIdentity = (identity) => {
  selectedIdentity.value = identity
}

const isSelectedIdentity = (type, organizationId = null) => {
  if (!selectedIdentity.value) return false
  if (type === 'personal') return selectedIdentity.value.identity_type === 'personal'
  return selectedIdentity.value.identity_type === 'organization' && selectedIdentity.value.organization_id === organizationId
}

const handleConfirmIdentitySelection = async () => {
  if (!selectedIdentity.value) return
  try {
    await authStore.switchUserIdentity(selectedIdentity.value)
    showIdentityDialog.value = false
    ElMessage.success('身份切换成功')
    // 角色切换成功后，自动跳转到总览界面
    router.push('/dashboard')
  } catch (error) {
    console.error('身份切换失败:', error)
    ElMessage.error('身份切换失败')
  }
}
</script>

<style scoped>
.pro-card {
  @apply bg-white rounded-xl border border-gray-200 shadow-sm;
}

.pro-card-body {
  @apply p-6;
}

.pro-card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.pro-card-title {
  @apply text-lg font-semibold text-gray-900 flex items-center;
}

.pro-card-title .icon {
  @apply mr-2 text-gray-600;
}

/* 按照悬浮框设计规范的按钮样式 */
:deep(.dialog-button-primary) {
  --el-button-bg-color: #4F46E5 !important;
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important;
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important;
  --el-button-active-border-color: #3730A3 !important;
  --el-button-text-color: #FFFFFF !important;
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: #FFFFFF !important;
  height: 32px !important;
  padding: 0 12px !important;
  font-size: 13px !important;
  line-height: 1 !important;
}

:deep(.dialog-button-secondary) {
  height: 32px !important;
  padding: 0 12px !important;
  font-size: 13px !important;
  line-height: 1 !important;
  --el-button-text-color: #374151 !important;
  --el-button-border-color: #D1D5DB !important;
  --el-button-bg-color: #FFFFFF !important;
  --el-button-hover-text-color: #4F46E5 !important;
  --el-button-hover-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #F8FAFF !important;
}

/* 简洁对话框样式 - 遵循设计标准 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 36px !important;
    padding: 0 20px !important;
    font-size: 14px !important;
    line-height: 1 !important;
    min-width: 80px !important;
  }

  /* 确认按钮特殊样式 - 更长的宽度 */
  .el-button.confirm-button {
    min-width: 120px !important;
    padding: 0 32px !important;
  }

  .el-button--primary:hover,
  .el-button[type="primary"]:hover {
    background-color: #4338CA !important;
    border-color: #4338CA !important;
    color: #FFFFFF !important;
  }

  .el-button--primary:active,
  .el-button[type="primary"]:active {
    background-color: #3730A3 !important;
    border-color: #3730A3 !important;
    color: #FFFFFF !important;
  }

  .el-button--primary:focus,
  .el-button[type="primary"]:focus {
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
  }

  .el-button--primary:disabled,
  .el-button[type="primary"]:disabled {
    background-color: #D1D5DB !important;
    border-color: #D1D5DB !important;
    color: #9CA3AF !important;
  }
}
</style>