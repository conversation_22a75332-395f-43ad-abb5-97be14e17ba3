# CRM系统模块说明

## 系统概述

全新的CRM系统采用现代化的客户管理架构，从市场资源获取到客户服务管理，提供专业的客户管理解决方案。系统设计遵循AccountManagement.vue的设计风格，确保界面一致性和用户体验。

## 核心模块

### 市场资源管理
- **目标**: 记录和管理所有市场获客信息
- **核心功能**: 客户信息记录、渠道跟踪、资源分配
- **页面路径**: `/crm/market-resources`

### 有效客户管理  
- **目标**: 深度了解客户需求，完善客户画像
- **核心功能**: 继承市场资源信息、客户反馈模板填写
- **页面路径**: `/crm/valid-customers`

### 客户管理（进度跟踪）
- **目标**: 跟踪客户申请进度，确保服务质量
- **核心功能**: 6阶段进度可视化、进度更新、完成时间预测、服务团队管理
- **页面路径**: `/crm/customer-management`

### 组织管理
- **目标**: 管理组织架构和部门人员
- **核心功能**: 部门管理、人员分配、权限控制
- **页面路径**: `/crm/organization/departments`

## 系统架构

### 路由结构
- **主路由**: `/crm` → 自动重定向到 `/crm/market-resources`
- **市场资源管理**:
  - `/crm/market-resources` - 市场资源列表和管理
- **有效客户管理**:
  - `/crm/valid-customers` - 有效客户列表和反馈管理
- **签约客户**:
  - `/crm/customer-management` - 签约客户申请进度跟踪
- **组织管理**:
  - `/crm/organization/departments` - 组织架构管理

### 页面组件

#### 1. 市场资源管理 (MarketResources.vue)
- **功能**: 记录和管理所有市场获客信息
- **核心字段**:
  - 添加日期、客户微信信息（微信名、微信号）
  - 客户电话、渠道来源（小红书/抖音/转介绍/自定义）
  - 市场部门人员、分配的销售部门人员
  - 咨询的留学项目、客户背景信息
- **特色功能**:
  - 资源分配功能（与销售人员绑定）
  - 批量分配操作
  - 多维度筛选（渠道、市场人员、分配状态）
  - 统计概览（总资源、待分配、已分配、本月新增）
- **目标用户**: 市场部门人员

#### 2. 有效客户管理 (ValidCustomers.vue)
- **功能**: 深度了解客户需求，完善客户画像
- **继承信息**: 市场资源的所有基础信息
- **客户反馈模板**（5个核心维度）:
  - **预算**: 中介预算、机构费用对比、学费生活费
  - **动机**: 考公考编/落户/体验/院校偏好等
  - **紧迫性**: 决定时间节点和原因分析
  - **对比**: 线上线下机构、价格、优劣势分析
  - **父母**: 决策角色、关心问题、熟人推荐情况
- **特色功能**:
  - 反馈完成状态追踪
  - 意向等级管理（高/中/低）
  - 客户状态流转（跟进中→待签约→已签约）
- **目标用户**: 销售顾问

#### 3. 签约客户（进度跟踪）(CustomerManagement.vue)
- **功能**: 跟踪签约客户的申请进度，确保服务质量
- **8阶段进度可视化**:
  - 签约完成 → 确定选校 → 材料收集 → 文书写作 → 文书定稿 → 申请递交 → 签证申请 → 完成
- **高级进度框特性**:
  - 符合系统风格的极简设计
  - 动态进度条和节点状态显示
  - 可视化进度百分比
  - 响应式设计，移动端适配
- **管理功能**:
  - 进度更新对话框
  - 多维度筛选（进度/顾问/地区）
  - 客户详细信息展示
  - 预计完成时间跟踪
- **服务团队管理**:
  - 市场人员、销售人员、文书老师、递交人员分类显示
  - 按服务人员筛选客户
  - 团队协作状态跟踪
- **目标用户**: 服务顾问、项目经理

#### 4. 组织管理 (DepartmentManagement.vue)
- **功能**: 管理组织架构和部门人员配置
- **核心功能**:
  - 部门结构管理
  - 人员角色分配
  - 权限控制设置
- **目标用户**: 管理员、HR人员

## 侧边栏菜单

### CRM系统分组
- **市场资源** - campaign图标 - `/crm/market-resources`
- **有效客户** - people图标 - `/crm/valid-customers`
- **签约客户** - verified图标 - `/crm/customer-management`
- **组织管理** - corporate_fare图标 - `/crm/organization/departments`

## 文件结构（全新重构）

```
crm/
├── 客户全生命周期管理
│   ├── MarketResources.vue       # 市场资源管理
│   ├── ValidCustomers.vue        # 有效客户管理
│   ├── SignedCustomers.vue       # 签约客户管理
│   └── CustomerManagement.vue    # 客户管理（进度跟踪）
├── 组织管理
│   └── organization/
│       └── DepartmentManagement.vue  # 组织架构管理
└── README.md                     # 系统说明文档
```

## 重构成果

### 删除的旧文件：
- ❌ `UnsignedStudents.vue` - 重构为MarketResources.vue
- ❌ `SignedStudents.vue` - 重构为SignedCustomers.vue
- ❌ `TaskManagement.vue` - 重构为CustomerManagement.vue
- ❌ `components/TaskList.vue` - 功能整合到新页面
- ❌ `styles/crm-theme.css` - 使用内联样式

### 设计优化：
- 采用AccountManagement.vue的高级极简风格
- 统一使用#4F46E5紫色主题
- Pro Card布局 + 横排卡片列表设计
- 响应式设计，移动端完美适配

### 功能提升：
- ✅ 完整的客户全生命周期管理
- ✅ 现代化的进度可视化组件
- ✅ 深度客户反馈模板系统
- ✅ 智能的资源分配和跟踪机制
- ✅ 集中功能管理，增强用户体验

## 🎨 设计规范

### 颜色系统
- **主色调**：Indigo (#6366f1)
- **成功色**：Green (#10b981) 
- **警告色**：Orange (#f59e0b)
- **危险色**：Red (#ef4444)
- **信息色**：Blue (#3b82f6)

### 组件样式
- **卡片阴影**：hover时轻微上浮效果
- **边框颜色**：左侧彩色边框表示优先级
- **渐变背景**：学生头像使用渐变色
- **圆角设计**：统一8px圆角

### 交互动效
- **悬停效果**：卡片阴影变化、颜色过渡
- **加载状态**：skeleton loading
- **过渡动画**：200ms缓动过渡

## 📊 状态管理

使用 Pinia 进行状态管理，位于 `@/stores/crm.js`：

```javascript
// 主要状态
- clients: []           // 学生列表（统一概念）
- currentClient: null   // 当前学生
- tasks: []            // 任务列表  
- taskStats: {}        // 任务统计
- clientNotes: []      // 学生备注

// 主要方法
- fetchClients()       // 获取学生列表
- fetchTasks()         // 获取任务列表
- createClient()       // 创建学生
- updateTask()         // 更新任务
```

## 🌐 API 接口

API 封装位于 `@/api/crm.js`，包含以下模块：

- **clientApi**：学生管理相关接口（统一概念）
- **taskApi**：任务管理相关接口
- **materialApi**：材料管理相关接口
- **schoolApi**：选校管理相关接口
- **documentApi**：文书管理相关接口
- **applicationApi**：网申进度相关接口
- **offerApi**：Offer管理相关接口
- **noteApi**：备注相关接口

## 🛣️ 路由配置

路由配置位于 `@/router/modules/crm.js`：

```
/crm                         # CRM首页（重定向到未签约学生）
├── /students/unsigned       # 未签约学生列表
├── /students/signed         # 签约学生列表（前期+后期服务通用）
├── /students/add            # 添加学生
├── /students/:id            # 学生详情
├── /students/:id/edit       # 编辑学生
├── /tasks                   # 任务管理
├── /tasks/add               # 添加任务
└── /tasks/:id/edit          # 编辑任务
```

## 🔧 开发指南

### 添加新功能

1. **新增页面组件**：
   ```vue
   <template>
     <!-- 页面内容 -->
   </template>
   
   <script setup>
   // 使用组合式API
   </script>
   ```

2. **更新路由配置**：
   ```javascript
   // 在 crm.js 路由文件中添加新路由
   ```

3. **添加API接口**：
   ```javascript
   // 在 @/api/crm.js 中添加相应API方法
   ```

4. **更新状态管理**：
   ```javascript
   // 在 @/stores/crm.js 中添加状态和方法
   ```

### 样式指南

- 使用 TailwindCSS 功能类优先
- 避免自定义CSS，除非必要
- 保持设计系统一致性
- 响应式设计考虑

### 代码规范

- 使用 Vue 3 组合式API
- TypeScript 类型提示
- ESLint 代码检查
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

## 🚧 待实现功能

以下组件功能可在现有页面中实现：

1. **MaterialsManagement** - 在StudentDetail.vue中作为标签页
2. **SchoolSelection** - 在StudentDetail.vue中作为标签页
3. **DocumentsManagement** - 在StudentDetail.vue中作为标签页
4. **ApplicationProgress** - 在StudentDetail.vue中作为标签页
5. **OffersManagement** - 在StudentDetail.vue中作为标签页

## 📱 移动端适配

系统采用响应式设计，主要断点：

- **手机**：< 768px
- **平板**：768px - 1024px  
- **桌面**：> 1024px

移动端优化：
- 卡片堆叠布局
- 触摸友好的按钮大小
- 简化的导航菜单
- 优化的表单输入

## 🧪 测试说明

目前使用模拟数据进行开发和测试，待后端API完成后需要：

1. 替换模拟数据为真实API调用
2. 添加错误处理和加载状态
3. 完善用户交互反馈
4. 添加单元测试和e2e测试

---

## 📞 联系方式

如有问题或建议，请联系开发团队。 