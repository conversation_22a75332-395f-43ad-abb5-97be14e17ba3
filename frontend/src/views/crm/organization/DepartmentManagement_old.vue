<template>
  <div class="crm-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span>CRM系统</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">组织管理</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">组织管理</h2>
    </div>

    <!-- 主要内容卡片 -->
    <div class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">corporate_fare</span>
          部门及成员
        </div>
        <div class="flex items-center gap-3">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleAddMember"
            class="main-action-button"
          >
            <span class="material-icons-outlined text-sm mr-1">person_add</span>
            添加成员
          </el-button>
        </div>
      </div>

      <div class="pro-card-body">
        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-xl p-5 border border-cyan-100/50 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-cyan-600">groups</span>
              <span class="text-sm font-medium text-slate-700">总成员数</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ totalMembersCount }}</div>
          </div>
          
          <div class="bg-gradient-to-br from-lime-50 to-green-50 rounded-xl p-5 border border-lime-100/50 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-lime-600">account_tree</span>
              <span class="text-sm font-medium text-slate-700">部门数量</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ totalDepartmentsCount }}</div>
          </div>
          
          <div class="bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl p-5 border border-violet-100/50 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-violet-600">verified_user</span>
              <span class="text-sm font-medium text-slate-700">活跃成员</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ activeMembersCount }}</div>
          </div>
          
          <div class="bg-gradient-to-br from-rose-50 to-pink-50 rounded-xl p-5 border border-rose-100/50 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-rose-600">person_off</span>
              <span class="text-sm font-medium text-slate-700">停用成员</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ disabledMembersCount }}</div>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="border-t border-gray-200 my-8"></div>
        
        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- 左侧部门树 -->
          <div class="lg:col-span-1">
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div class="mb-4">
                <div class="flex items-center justify-between mb-3">
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">{{ companyName }}</h4>
                    <div class="text-xs text-gray-500 mt-1">组织架构</div>
                  </div>
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="handleAddDepartment"
                    class="main-action-button"
                  >
                    <span class="material-icons-outlined text-sm mr-1">add</span>
                    新增
                  </el-button>
                </div>
              </div>
          
              <el-input
                v-model="deptSearchQuery"
                placeholder="搜索部门名"
                clearable
                class="mb-4"
                size="default"
                @input="handleDeptSearch"
              >
                <template #prefix>
                  <span class="material-icons-outlined text-gray-400">search</span>
                </template>
              </el-input>
          
              <el-tree
                :data="organizationTree"
                :props="{ children: 'children', label: 'name' }"
                :filter-node-method="filterDepartmentNode"
                :expand-on-click-node="false"
                :default-expand-all="true"
                ref="deptTreeRef"
                @node-click="handleDeptNodeClick"
                class="dept-tree"
                node-key="id"
                :highlight-current="true"
              >
                <template #default="{ node, data }">
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center flex-1">
                      <span class="material-icons-outlined mr-2" :class="data.isCompany ? 'text-indigo-600' : 'text-blue-600'">
                        {{ data.isCompany ? 'corporate_fare' : 'business' }}
                      </span>
                      <span class="flex-1 text-sm" :class="data.isCompany ? 'text-gray-900 font-medium' : 'text-gray-900'">{{ data.name }}</span>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 ml-1">{{ data.memberCount || 0 }}</span>
                    </div>
                    <el-dropdown 
                      v-if="!data.isCompany"
                      @command="handleDeptAction"
                      trigger="click"
                      class="resource-action-dropdown"
                      @click.stop
                    >
                      <el-button link size="small" class="action-dropdown-trigger">
                        <span class="material-icons-outlined text-lg">more_vert</span>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu class="resource-dropdown-menu">
                          <el-dropdown-item :command="{action: 'edit', dept: data}" class="dropdown-menu-item">
                            <div class="flex items-center">
                              <span class="material-icons-outlined text-sm mr-2.5 text-green-600">edit</span>
                              <span class="text-sm font-medium text-gray-700">编辑部门</span>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item :command="{action: 'addSub', dept: data}" class="dropdown-menu-item">
                            <div class="flex items-center">
                              <span class="material-icons-outlined text-sm mr-2.5 text-blue-600">add</span>
                              <span class="text-sm font-medium text-gray-700">添加子部门</span>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item :command="{action: 'delete', dept: data}" class="dropdown-menu-item">
                            <div class="flex items-center">
                              <span class="material-icons-outlined text-sm mr-2.5 text-red-600">delete</span>
                              <span class="text-sm font-medium text-gray-700">删除部门</span>
                            </div>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-tree>
            </div>
          </div>

          <!-- 右侧成员列表 -->
          <div class="lg:col-span-3">
            <!-- 筛选区域 -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6">
              <el-input
                v-model="memberSearchQuery"
                placeholder="搜索成员姓名、手机号"
                size="default"
                class="w-full sm:w-64"
                clearable
                @input="handleMemberSearch"
              >
                <template #prefix>
                  <span class="material-icons-outlined text-gray-400">search</span>
                </template>
              </el-input>

              <el-select 
                v-model="memberStatusFilter" 
                placeholder="账号状态" 
                clearable 
                size="default"
                class="w-full sm:w-48"
                @change="handleMemberSearch"
              >
                <el-option label="全部" value="" />
                <el-option label="正常" value="normal" />
                <el-option label="停用" value="disabled" />
              </el-select>

              <el-button 
                @click="handleResetMemberFilter"
                class="border-gray-300 text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
              >
                <span class="material-icons-outlined text-sm mr-1">refresh</span>
                重置
              </el-button>
            </div>



            <!-- 成员列表表头 -->
            <div class="hidden md:flex items-center bg-gray-100 rounded-lg px-4 py-3 mb-4 text-sm font-medium text-gray-600 min-h-[60px]">
              <div class="flex-1 min-w-0 grid grid-cols-6 gap-4 items-center">
                <div class="col-span-1 flex items-center">姓名</div>
                <div class="col-span-1 flex items-center justify-center">账号状态</div>
                <div class="col-span-1 flex items-center justify-center">手机号码</div>
                <div class="col-span-1 flex items-center">部门</div>
                <div class="col-span-1 flex items-center justify-center">账号类型</div>
                <div class="col-span-1 flex items-center justify-center">操作</div>
              </div>
            </div>

            <!-- 成员列表 -->
            <transition name="member-list" mode="out-in">
              <div :key="currentDepartment?.id || 'all'" v-loading="loading">
                <!-- 有成员时显示列表 -->
                <div v-if="filteredMembers.length > 0" class="space-y-4">
                  <div
                    v-for="member in paginatedMembers"
                    :key="member.id"
                    class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100 cursor-pointer"
                    @click="handleViewMemberDetail(member)"
                  >
                <div class="flex items-center min-h-[60px]">
                  <div class="flex-1 min-w-0 grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
                    <!-- 姓名列 -->
                    <div class="col-span-12 md:col-span-1 flex items-center">
                      <div class="md:hidden text-xs font-medium text-gray-600 mb-1">姓名</div>
                      <div class="flex items-center w-full">
                        <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 font-medium mr-3 text-sm">
                          {{ getMemberInitials(member.name) }}
                        </div>
                        <div>
                          <div class="font-semibold text-gray-900">{{ member.name }}</div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 账号状态列 -->
                    <div class="col-span-12 md:col-span-1 flex items-center justify-center">
                      <div class="md:hidden text-xs font-medium text-gray-600 mb-1">账号状态</div>
                      <span v-if="member.status === 'normal'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                        正常
                      </span>
                      <span v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                        停用
                      </span>
                    </div>
                    
                    <!-- 手机号码列 -->
                    <div class="col-span-12 md:col-span-1 flex items-center justify-center">
                      <div class="md:hidden text-xs font-medium text-gray-600 mb-1">手机号码</div>
                      <div class="text-sm text-gray-900">{{ member.phone }}</div>
                    </div>
                    
                    <!-- 部门列 -->
                    <div class="col-span-12 md:col-span-1 flex items-center">
                      <div class="md:hidden text-xs font-medium text-gray-600 mb-1">部门</div>
                      <div class="flex flex-wrap gap-1">
                        <span v-for="dept in member.departments" :key="dept.id" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                          {{ dept.name }}
                        </span>
                        <span v-if="!member.departments || member.departments.length === 0" class="text-gray-400 text-sm">
                          未分配
                        </span>
                      </div>
                    </div>
                    
                    <!-- 账号类型列 -->
                    <div class="col-span-12 md:col-span-1 flex items-center justify-center">
                      <div class="md:hidden text-xs font-medium text-gray-600 mb-1">账号类型</div>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-50 text-purple-700 border border-purple-200">
                        员工
                      </span>
                    </div>
                    
                    <!-- 操作列 -->
                    <div class="col-span-12 md:col-span-1 flex items-center justify-center" @click.stop>
                      <el-dropdown trigger="click" class="resource-action-dropdown">
                        <el-button link size="small" class="action-dropdown-trigger">
                          <span class="material-icons-outlined text-lg">more_vert</span>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu class="resource-dropdown-menu">
                            <el-dropdown-item @click="handleChangeMemberDepartment(member)" class="dropdown-menu-item">
                              <div class="flex items-center">
                                <span class="material-icons-outlined text-sm mr-2.5 text-blue-600">swap_horiz</span>
                                <span class="text-sm font-medium text-gray-700">变更部门</span>
                              </div>
                            </el-dropdown-item>
                            <el-dropdown-item @click="handleMemberResignation(member)" class="dropdown-menu-item">
                              <div class="flex items-center">
                                <span class="material-icons-outlined text-sm mr-2.5 text-orange-600">exit_to_app</span>
                                <span class="text-sm font-medium text-gray-700">操作离职（资源转移）</span>
                              </div>
                            </el-dropdown-item>
                            <el-dropdown-item @click="handleToggleMemberStatus(member)" class="dropdown-menu-item">
                              <div class="flex items-center">
                                <span class="material-icons-outlined text-sm mr-2.5 text-red-600">{{ member.status === 'normal' ? 'pause_circle' : 'play_circle' }}</span>
                                <span class="text-sm font-medium text-gray-700">
                                  {{ member.status === 'normal' ? '暂停账号' : '恢复账号' }}
                                </span>
                              </div>
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
                  </div>
                </div>
                
                <!-- 空状态 -->
                <div v-else class="text-center py-12">
                  <div class="text-gray-400 mb-2">
                    <span class="material-icons-outlined text-4xl">groups</span>
                  </div>
                  <p class="text-gray-500">{{ memberSearchQuery ? '未找到匹配的成员' : '暂无成员数据' }}</p>
                  <el-button type="primary" @click="handleAddMember" class="main-action-button mt-4">添加第一个成员</el-button>
                </div>
              </div>
            </transition>

            <!-- 分页 -->
            <div class="flex justify-between items-center mt-6" v-if="total > 0">
              <div class="text-sm text-gray-500">
                共 {{ total }} 条记录
              </div>
              <el-pagination
                v-if="totalPages > 1"
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="total"
                layout="prev, pager, next"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                class="!mt-0"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 部门表单对话框 -->
    <el-dialog
      v-model="departmentDialogVisible"
      :title="isDeptEditMode ? '编辑部门' : '新增部门'"
      width="600px"
      destroy-on-close
      class="simple-dialog compact-dialog"
    >
      <div class="simple-content">
        <el-form
        ref="departmentFormRef"
        :model="departmentFormData"
        :rules="departmentFormRules"
        label-width="100px"
        size="default"
      >
        <el-form-item label="部门名称" prop="name" required>
          <el-input v-model="departmentFormData.name" placeholder="请输入部门名称" clearable />
        </el-form-item>
        <el-form-item label="部门编码" prop="code" required>
          <el-input v-model="departmentFormData.code" placeholder="请输入部门编码" clearable />
        </el-form-item>
        <el-form-item label="上级部门" prop="parentId">
          <el-tree-select
            v-model="departmentFormData.parentId"
            :data="departmentTree"
            :props="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择上级部门"
            clearable
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="部门负责人" prop="manager">
          <el-input v-model="departmentFormData.manager" placeholder="请输入负责人姓名" clearable />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="departmentFormData.sort" :min="1" :max="999" class="w-full" />
        </el-form-item>
        <el-form-item label="部门描述" prop="description">
          <el-input
            v-model="departmentFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入部门描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="departmentDialogVisible = false">
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleDepartmentFormSubmit"
            :loading="departmentFormSubmitting"
          >
            {{ isDeptEditMode ? '更新部门' : '创建部门' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 成员表单对话框 -->
    <el-dialog
      v-model="memberDialogVisible"
      :title="isMemberEditMode ? '编辑成员' : '添加成员'"
      width="700px"
      destroy-on-close
      class="simple-dialog compact-dialog"
    >
      <div class="simple-content">
        <el-form
          ref="memberFormRef"
          :model="memberFormData"
          :rules="memberFormRules"
          label-width="100px"
          size="default"
        >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <el-form-item label="姓名" prop="name" required>
            <el-input v-model="memberFormData.name" placeholder="请输入成员姓名" clearable />
          </el-form-item>
          <el-form-item label="手机号码" prop="phone" required>
            <el-input v-model="memberFormData.phone" placeholder="请输入手机号码" clearable />
          </el-form-item>
          
          <el-form-item label="岗位/职位" prop="position">
            <el-input v-model="memberFormData.position" placeholder="请输入岗位职位" clearable />
          </el-form-item>
        </div>
        <el-form-item label="所属部门" prop="departments">
          <el-tree-select
            v-model="memberFormData.departments"
            :data="departmentTree"
            :props="{ children: 'children', label: 'name', value: 'id' }"
            multiple
            placeholder="请选择所属部门（可多选）"
            clearable
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="账号状态" prop="status">
          <el-radio-group v-model="memberFormData.status">
            <el-radio value="normal">正常</el-radio>
            <el-radio value="disabled">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="memberDialogVisible = false">
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleMemberFormSubmit"
            :loading="memberFormSubmitting"
          >
            {{ isMemberEditMode ? '更新成员' : '添加成员' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 成员详情对话框 -->
    <el-dialog
      v-model="memberDetailDialogVisible"
      title="成员详情"
      width="600px"
      destroy-on-close
      class="simple-dialog compact-dialog"
    >
      <div v-if="selectedMemberDetail" class="simple-content">
        <!-- 基本信息 -->
        <div class="mb-6">
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center mb-4">
              <div class="w-16 h-16 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 font-medium mr-4 text-xl">
                {{ getMemberInitials(selectedMemberDetail.name) }}
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">{{ selectedMemberDetail.name }}</h3>
                <div class="mt-2">
                  <span v-if="selectedMemberDetail.status === 'normal'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                    正常
                  </span>
                  <span v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                    停用
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 联系信息 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-900 mb-3">联系信息</label>
          <div class="bg-white border border-gray-200 rounded-lg p-4 space-y-3">
            <div class="flex items-center">
              <span class="text-sm font-medium text-gray-600 w-20">手机号：</span>
              <span class="text-sm text-gray-900">{{ selectedMemberDetail.phone }}</span>
            </div>

          </div>
        </div>

        <!-- 所属部门 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-900 mb-3">所属部门</label>
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <div class="flex flex-wrap gap-2" v-if="selectedMemberDetail.departments && selectedMemberDetail.departments.length > 0">
              <span v-for="dept in selectedMemberDetail.departments" :key="dept.id" class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200">
                {{ dept.name }}
              </span>
            </div>
            <div v-else class="text-gray-500 text-sm">
              未分配部门
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="memberDetailDialogVisible = false">
            关闭
          </el-button>
          <el-button 
            type="primary" 
            @click="handleEditFromDetail"
          >
            编辑信息
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 变更部门对话框 -->
    <el-dialog
      v-model="batchChangeDeptDialogVisible"
      title="变更部门"
      width="500px"
      destroy-on-close
      class="simple-dialog compact-dialog"
    >
      <div class="simple-content">
        <div class="mb-6" v-if="selectedMemberForDeptChange">
          <p class="text-sm text-gray-600 leading-6 mb-4">
            将为成员 <span class="font-medium text-gray-900">{{ selectedMemberForDeptChange.name }}</span> 变更部门：
          </p>
        </div>
        
        <el-form label-width="100px">
        <el-form-item label="目标部门" required>
          <el-tree-select
            v-model="batchChangeDeptForm.targetDepartment"
            :data="departmentTree"
            :props="{ children: 'children', label: 'name', value: 'id' }"
            placeholder="请选择目标部门"
            clearable
            class="w-full"
          />
        </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="batchChangeDeptDialogVisible = false">
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleConfirmBatchChangeDept"
            :disabled="!batchChangeDeptForm.targetDepartment"
          >
            确认变更
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Search, OfficeBuilding, Edit, Delete, MoreFilled, Lock, Unlock, Switch
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)

// 公司信息
const companyName = ref('英派瑞克') // 以后从数据库获取

// 部门相关
const deptSearchQuery = ref('')
const departmentTree = ref([])
const currentDepartment = ref(null)
const selectedDepartmentId = ref(null)

// 成员相关
const memberSearchQuery = ref('')
const memberStatusFilter = ref('')
const members = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

// 表单对话框相关
const departmentDialogVisible = ref(false)
const memberDialogVisible = ref(false)
const memberDetailDialogVisible = ref(false)
const batchChangeDeptDialogVisible = ref(false)
const selectedMemberDetail = ref(null)
const selectedMemberForDeptChange = ref(null)

const departmentFormData = ref({
  name: '',
  code: '',
  parentId: null,
  manager: '',
  description: '',
  sort: 1
})

const memberFormData = ref({
  name: '',
  phone: '',
  position: '',
  departments: [],
  status: 'normal'
})

const batchChangeDeptForm = ref({
  targetDepartment: null
})

const isDeptEditMode = ref(false)
const isMemberEditMode = ref(false)
const departmentFormSubmitting = ref(false)
const memberFormSubmitting = ref(false)
const departmentFormRef = ref()
const memberFormRef = ref()

// 工具函数：获取部门及其所有子部门的ID
const getAllDepartmentIds = (dept, allDepts = []) => {
  const ids = [dept.id]
  
  // 递归查找所有子部门
  const findChildrenIds = (deptList) => {
    deptList.forEach(d => {
      if (d.children && d.children.length > 0) {
        d.children.forEach(child => {
          ids.push(child.id)
          if (child.children && child.children.length > 0) {
            findChildrenIds([child])
          }
        })
      }
    })
  }
  
  // 从当前部门开始查找子部门
  if (dept.children && dept.children.length > 0) {
    findChildrenIds([dept])
  }
  
  return ids
}

// 组织树计算属性
const organizationTree = computed(() => {
  if (departmentTree.value.length === 0) return []
  
  // 计算所有成员数量
  const totalMembers = members.value.filter(member => member.status !== 'deleted').length
  
  return [{
    id: 'company-root',
    name: companyName.value,
    isCompany: true,
    memberCount: totalMembers,
    children: departmentTree.value
  }]
})

// 统计计算属性
const totalMembersCount = computed(() => {
  return members.value.filter(member => member.status !== 'deleted').length
})

const totalDepartmentsCount = computed(() => {
  const countDepartments = (depts) => {
    let count = 0
    depts.forEach(dept => {
      count++
      if (dept.children && dept.children.length > 0) {
        count += countDepartments(dept.children)
      }
    })
    return count
  }
  return countDepartments(departmentTree.value)
})

const activeMembersCount = computed(() => {
  return members.value.filter(member => member.status === 'normal').length
})

const disabledMembersCount = computed(() => {
  return members.value.filter(member => member.status === 'disabled').length
})

// 筛选后的成员列表
const filteredMembers = computed(() => {
  let filtered = members.value

  // 按部门筛选
  if (currentDepartment.value) {
    // 获取当前部门及其所有子部门的ID
    const departmentIds = getAllDepartmentIds(currentDepartment.value)
    
    filtered = filtered.filter(member => 
      member.departments && member.departments.some(dept => departmentIds.includes(dept.id))
    )
  }

  // 按关键词搜索
  if (memberSearchQuery.value) {
    const query = memberSearchQuery.value.toLowerCase()
    filtered = filtered.filter(member => 
      member.name.toLowerCase().includes(query) ||
      member.phone.includes(query)
    )
  }

  // 按状态筛选
  if (memberStatusFilter.value) {
    filtered = filtered.filter(member => member.status === memberStatusFilter.value)
  }

  // 排除已删除的成员
  filtered = filtered.filter(member => member.status !== 'deleted')

  return filtered
})

const total = computed(() => {
  return filteredMembers.value.length
})

const totalPages = computed(() => {
  return Math.ceil(total.value / pageSize.value)
})

const paginatedMembers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredMembers.value.slice(start, end)
})

// 表单验证规则
const departmentFormRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入部门编码', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9_-]+$/, message: '部门编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ]
}

const memberFormRules = {
  name: [
    { required: true, message: '请输入成员姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 部门树相关方法
const filterDepartmentNode = (value, data) => {
  if (!value) return true
  return data.name.includes(value)
}

const handleDeptSearch = () => {
  // TODO: 实现部门搜索逻辑
}

const handleDeptNodeClick = (dept) => {
  // 如果点击的是公司根节点，显示所有成员
  if (dept.isCompany) {
    currentDepartment.value = null
    selectedDepartmentId.value = 'company-root'
  } else {
    currentDepartment.value = dept
    selectedDepartmentId.value = dept.id
  }
  currentPage.value = 1
}

const handleDeptAction = async ({ action, dept }) => {
  if (action === 'edit') {
    openDepartmentDialog(dept)
  } else if (action === 'addSub') {
    openDepartmentDialog(null, dept)
  } else if (action === 'delete') {
    try {
      await ElMessageBox.confirm(
        `确定要删除部门 ${dept.name} 吗？删除后该部门下的成员将转移到上级部门。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // TODO: 调用删除API
      ElMessage.success('删除成功')
      loadDepartments()
    } catch (error) {
      // 用户取消删除
    }
  }
}

// 成员相关方法
const getMemberInitials = (name) => {
  if (!name) return 'N'
  const names = name.split(' ')
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase()
  }
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase()
}

const getMemberStatusTagType = (status) => {
  const statusMap = {
    normal: 'success',
    disabled: 'warning'
  }
  return statusMap[status] || 'info'
}

const getMemberStatusLabel = (status) => {
  const statusMap = {
    normal: '正常',
    disabled: '停用'
  }
  return statusMap[status] || '未知'
}

const handleMemberSearch = () => {
  currentPage.value = 1
}

const handleResetMemberFilter = () => {
  memberSearchQuery.value = ''
  memberStatusFilter.value = ''
  currentDepartment.value = null
}



// 操作方法
const handleAddDepartment = () => {
  openDepartmentDialog()
}

const handleAddMember = () => {
  openMemberDialog()
}

const handleViewMemberDetail = (member) => {
  selectedMemberDetail.value = member
  memberDetailDialogVisible.value = true
}

const handleEditMember = (member) => {
  openMemberDialog(member)
}

const handleEditFromDetail = () => {
  if (selectedMemberDetail.value) {
    memberDetailDialogVisible.value = false
    handleEditMember(selectedMemberDetail.value)
  }
}

const handleToggleMemberStatus = async (member) => {
  const action = member.status === 'normal' ? '停用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}成员 ${member.name} 的账号吗？`,
      `${action}确认`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'custom-confirm-btn',
        cancelButtonClass: 'custom-cancel-btn',
        customClass: 'custom-message-box'
      }
    )
    
    // TODO: 调用API
    ElMessage.success(`${action}成功`)
    loadMembers()
  } catch (error) {
    // 用户取消
  }
}

const handleChangeMemberDepartment = async (member) => {
  try {
    // 设置要变更的成员
    selectedMemberForDeptChange.value = member
    batchChangeDeptForm.value.targetDepartment = null
    batchChangeDeptDialogVisible.value = true
  } catch (error) {
    console.error('变更部门失败:', error)
  }
}

const handleMemberResignation = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要为成员 ${member.name} 办理离职手续吗？此操作将：\n\n• 停用该成员账号\n• 将其负责的客户资源转移给其他成员\n• 移除其部门分配\n\n请确认操作`,
      '离职确认',
      {
        confirmButtonText: '确认离职',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'custom-confirm-btn',
        cancelButtonClass: 'custom-cancel-btn',
        customClass: 'custom-message-box'
      }
    )
    
    // TODO: 调用离职API - 包含资源转移逻辑
    ElMessage.success('离职手续办理完成，相关资源已转移')
    loadMembers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('办理离职失败:', error)
      ElMessage.error('离职手续办理失败，请重试')
    }
  }
}

const handleDeleteMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除成员 ${member.name} 吗？此操作将永久移除成员，请确认！`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'custom-confirm-btn',
        cancelButtonClass: 'custom-cancel-btn',
        customClass: 'custom-message-box'
      }
    )
    
    // TODO: 调用删除API
    ElMessage.success('删除成功')
    loadMembers()
  } catch (error) {
    // 用户取消
  }
}



const handleConfirmBatchChangeDept = async () => {
  try {
    if (!selectedMemberForDeptChange.value) {
      ElMessage.error('未选择要变更的成员')
      return
    }
    
    // TODO: 调用单个成员变更部门API
    ElMessage.success('部门变更成功')
    batchChangeDeptDialogVisible.value = false
    selectedMemberForDeptChange.value = null
    loadMembers()
  } catch (error) {
    ElMessage.error('变更失败，请重试')
  }
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

// 对话框处理函数
const openDepartmentDialog = (department = null, parentDepartment = null) => {
  isDeptEditMode.value = department !== null
  
  if (department) {
    Object.keys(departmentFormData.value).forEach(key => {
      if (department.hasOwnProperty(key)) {
        departmentFormData.value[key] = department[key]
      }
    })
  } else {
    resetDepartmentForm()
    if (parentDepartment) {
      departmentFormData.value.parentId = parentDepartment.id
    }
  }
  
  departmentDialogVisible.value = true
}

const openMemberDialog = (member = null, viewOnly = false) => {
  isMemberEditMode.value = member !== null && !viewOnly
  
  if (member) {
    Object.keys(memberFormData.value).forEach(key => {
      if (member.hasOwnProperty(key)) {
        memberFormData.value[key] = member[key]
      }
    })
    // 处理部门数据
    if (member.departments) {
      memberFormData.value.departments = member.departments.map(dept => dept.id)
    }
  } else {
    resetMemberForm()
  }
  
  memberDialogVisible.value = true
}

const resetDepartmentForm = () => {
  departmentFormData.value = {
    name: '',
    code: '',
    parentId: null,
    manager: '',
    description: '',
    sort: 1
  }
}

const resetMemberForm = () => {
  memberFormData.value = {
    name: '',
    phone: '',
    position: '',
    departments: [],
    status: 'normal'
  }
}

const handleDepartmentFormSubmit = async () => {
  try {
    await departmentFormRef.value.validate()
    departmentFormSubmitting.value = true

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(isDeptEditMode.value ? '部门更新成功' : '部门创建成功')
    departmentDialogVisible.value = false
    loadDepartments()
    loadMembers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(isDeptEditMode.value ? '更新失败，请重试' : '创建失败，请重试')
    }
  } finally {
    departmentFormSubmitting.value = false
  }
}

const handleMemberFormSubmit = async () => {
  try {
    await memberFormRef.value.validate()
    memberFormSubmitting.value = true

    // TODO: 调用API保存数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success(isMemberEditMode.value ? '成员更新成功' : '成员添加成功')
    memberDialogVisible.value = false
    loadMembers()
    loadDepartments() // 刷新部门成员数量
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(isMemberEditMode.value ? '更新失败，请重试' : '添加失败，请重试')
    }
  } finally {
    memberFormSubmitting.value = false
  }
}

// 数据加载
// 加载公司信息
const loadCompanyInfo = async () => {
  try {
    // TODO: 从API获取公司信息
    // const response = await api.getCompanyInfo()
    // companyName.value = response.data.name
  } catch (error) {
    ElMessage.error('加载公司信息失败')
  }
}

const loadDepartments = async () => {
  try {
    // TODO: 实际API调用
    departmentTree.value = [
      {
        id: 1,
        name: '市场部',
        code: 'MARKETING',
        manager: '张三',
        memberCount: 4,
        children: []
      },
      {
        id: 2,
        name: '销售部',
        code: 'SALES',
        manager: '李四',
        memberCount: 6,
        children: []
      },
      {
        id: 3,
        name: '后端部门',
        code: 'BACKEND',
        manager: '王五',
        memberCount: 8,
        children: [
          {
            id: 4,
            name: '文书部',
            code: 'WRITING',
            manager: '赵六',
            memberCount: 5,
            children: []
          },
          {
            id: 5,
            name: '网申递交部',
            code: 'APPLICATION',
            manager: '刘七',
            memberCount: 3,
            children: []
          }
        ]
      }
    ]
  } catch (error) {
    ElMessage.error('加载部门数据失败')
  }
}

const loadMembers = async () => {
  try {
    loading.value = true
    // TODO: 实际API调用
    const membersData = [
      // 市场部成员 (4人)
      {
        id: 1,
        name: '张三',
        phone: '13812345678',
        position: '市场部经理',
        status: 'normal',
        departments: [{ id: 1, name: '市场部' }]
      },
      {
        id: 2,
        name: '陈小明',
        phone: '13887654321',
        position: '市场专员',
        status: 'normal',
        departments: [{ id: 1, name: '市场部' }]
      },
      {
        id: 3,
        name: '王丽娜',
        phone: '13698765432',
        position: '市场推广',
        status: 'normal',
        departments: [{ id: 1, name: '市场部' }]
      },
      {
        id: 4,
        name: '刘建华',
        phone: '13712345678',
        position: '市场分析师',
        status: 'normal',
        departments: [{ id: 1, name: '市场部' }]
      },
      
      // 销售部成员 (6人)
      {
        id: 5,
        name: '李四',
        phone: '13798765432',
        position: '销售部经理',
        status: 'normal',
        departments: [{ id: 2, name: '销售部' }]
      },
      {
        id: 6,
        name: '赵敏',
        phone: '13756789012',
        position: '销售顾问',
        status: 'normal',
        departments: [{ id: 2, name: '销售部' }]
      },
      {
        id: 7,
        name: '孙晓东',
        phone: '13634567890',
        position: '销售专员',
        status: 'normal',
        departments: [{ id: 2, name: '销售部' }]
      },
      {
        id: 8,
        name: '周雅琴',
        phone: '13612345678',
        position: '销售顾问',
        status: 'normal',
        departments: [{ id: 2, name: '销售部' }]
      },
      {
        id: 9,
        name: '吴志强',
        phone: '13587654321',
        position: '销售经理',
        status: 'normal',
        departments: [{ id: 2, name: '销售部' }]
      },
      {
        id: 10,
        name: '郑美玲',
        phone: '13523456789',
        position: '销售助理',
        status: 'disabled',
        departments: [{ id: 2, name: '销售部' }]
      },
      
      // 后端部门 - 文书部成员 (5人)
      {
        id: 11,
        name: '赵六',
        phone: '13445678901',
        position: '文书部经理',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      {
        id: 12,
        name: '钱小芳',
        phone: '13367890123',
        position: '文书顾问',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      {
        id: 13,
        name: '孙大伟',
        phone: '13289012345',
        position: '文书编辑',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      {
        id: 14,
        name: '李雪梅',
        phone: '13201234567',
        position: '文书专员',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      {
        id: 15,
        name: '王晓峰',
        phone: '13156789012',
        position: '文书助理',
        status: 'normal',
        departments: [{ id: 4, name: '文书部' }]
      },
      
      // 后端部门 - 网申递交部成员 (3人)
      {
        id: 16,
        name: '刘七',
        phone: '13078901234',
        position: '网申部经理',
        status: 'normal',
        departments: [{ id: 5, name: '网申递交部' }]
      },
      {
        id: 17,
        name: '何晓军',
        phone: '13934567890',
        position: '网申专员',
        status: 'normal',
        departments: [{ id: 5, name: '网申递交部' }]
      },
      {
        id: 18,
        name: '林美珊',
        phone: '13856789012',
        position: '递交助理',
        status: 'normal',
        departments: [{ id: 5, name: '网申递交部' }]
      }
    ]
    
    members.value = membersData
  } catch (error) {
    ElMessage.error('加载成员数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadCompanyInfo()
  loadDepartments()
  loadMembers()
})
</script>

<style scoped>
/* CRM页面基础样式 */
.crm-page {
  @apply p-6;
}

/* Pro Card 样式 */
.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.pro-card-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.pro-card-title {
  @apply flex items-center text-lg font-medium text-gray-900;
}

.pro-card-title .icon {
  @apply mr-2 text-gray-500;
}

.pro-card-body {
  @apply p-6;
}

/* 主要操作按钮 */
.main-action-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.main-action-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 触发按钮 */
.action-dropdown-trigger {
  color: #6B7280 !important;
  padding: 4px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  width: 32px !important;
  height: 32px !important;
}

.action-dropdown-trigger:hover {
  color: #4F46E5 !important;
  background-color: #F3F4F6 !important;
}

/* 下拉菜单 */
:deep(.resource-dropdown-menu) {
  border-radius: 7px !important;
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.13) !important;
  border: 1px solid #E5E7EB !important;
  padding: 3px 0 !important;
  min-width: 130px !important;
}

/* 菜单项 */
:deep(.dropdown-menu-item) {
  padding: 7px 14px !important;
  margin: 1px 3px !important;
  border-radius: 5px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

:deep(.dropdown-menu-item:hover) {
  background-color: #F9FAFB !important;
}

/* 部门树样式 */
.dept-tree :deep(.el-tree-node__content) {
  height: 36px;
  padding: 0 8px;
  border-radius: 6px;
  margin: 1px 0;
}

.dept-tree :deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

.dept-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #eef2ff;
  color: #4F46E5;
}

/* 页面级别的Element Plus主色调覆盖 */
.crm-page {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #7C3AED !important;
  --el-color-primary-light-5: #A855F7 !important;
  --el-color-primary-light-7: #C084FC !important;
  --el-color-primary-light-8: #DDD6FE !important;
  --el-color-primary-light-9: #EDE9FE !important;
  --el-color-primary-dark-2: #3730A3 !important;
}

/* 输入框紫色主题 */
:deep(.el-input .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-input .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

/* 选择框紫色主题 */
:deep(.el-select) {
  --el-color-primary: #4F46E5;
}

/* 下拉选项样式 */
:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #EEF2FF !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
}

/* 简单对话框样式 */
:deep(.simple-dialog) {
  border-radius: 12px !important;
}

/* 简洁对话框样式 - 无分割线整体页面，上下紧凑布局 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
  }

  /* 次要按钮样式 */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
  }
}

/* 成员列表动画效果 */
.member-list-enter-active,
.member-list-leave-active {
  transition: opacity 0.3s ease;
}

.member-list-enter-from,
.member-list-leave-to {
  opacity: 0;
}

/* 部门树选中效果 */
.dept-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #eef2ff !important;
  color: #4F46E5 !important;
  border-radius: 6px;
  font-weight: 500;
}

.dept-tree :deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dept-tree :deep(.el-tree-node__content) {
  height: 36px;
  padding: 0 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

@media (max-width: 1024px) {
  .crm-page {
    @apply p-4;
  }
}

@media (max-width: 768px) {
  .crm-page {
    @apply p-3;
  }
}</style> 