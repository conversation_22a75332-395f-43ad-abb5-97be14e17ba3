<template>
  <div class="market-resources-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-4">
      <nav class="flex text-sm text-gray-500">
        <span>CRM系统</span>
        <span class="mx-2">/</span>
        <span class="text-gray-900">市场资源</span>
      </nav>
    </div>

    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-semibold text-gray-900">市场资源管理</h2>
    </div>

    <!-- 主要内容卡片 -->
    <div class="pro-card">
      <div class="pro-card-header">
        <div class="pro-card-title">
          <span class="material-icons-outlined icon">campaign</span>
          市场资源列表
        </div>
                  <div class="flex items-center gap-3">
            <!-- 切换按钮（仅组织owner可见） -->
            <div v-if="canViewAllResources">
              <el-button 
                size="small" 
                type="primary"
                @click="showOnlyMyResources ? showAllResources() : showMyResources()"
                :loading="toggleLoading"
                :disabled="toggleLoading"
              >
                <span v-if="!toggleLoading" class="material-icons-outlined text-sm mr-1">
                  {{ showOnlyMyResources ? 'visibility' : 'person' }}
                </span>
                {{ showOnlyMyResources ? '查看所有市场资源' : '查看我创建的资源' }}
              </el-button>
            </div>
            
            <el-button 
              type="primary" 
              size="small" 
              @click="handleAddResource"
              class="add-resource-button"
            >
              <span class="material-icons-outlined text-sm mr-1">add</span>
              添加市场资源
            </el-button>
          </div>
      </div>

      <div class="pro-card-body">
        <!-- 个人身份访问提示 -->
        <div v-if="!isOrganizationIdentity" class="text-center py-16">
          <div class="max-w-md mx-auto">
            <div class="mb-6">
              <span class="material-icons-outlined text-6xl text-gray-300">business</span>
            </div>
            <h3 class="text-xl font-medium text-gray-900 mb-4">需要组织账号访问</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              CRM系统功能仅限组织账号使用。请切换到组织身份或联系管理员获取组织账号权限。
            </p>
            <div class="space-y-3">
              <el-button type="primary" @click="$router.push('/account/profile')">
                <span class="material-icons-outlined text-sm mr-1">switch_account</span>
                切换身份
              </el-button>
              <div class="text-sm text-gray-500">
                如需创建组织账号，请前往账户管理页面
              </div>
            </div>
          </div>
        </div>

        <!-- 组织身份内容 -->
        <div v-else>
        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div 
            @click="handleStatusCardClick('')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-cyan-50 to-blue-50',
              statusFilter === '' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-cyan-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-cyan-600">campaign</span>
              <span class="text-sm font-medium text-slate-700">总市场资源</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.total }}</div>
          </div>
          
          <div 
            @click="handleStatusCardClick('unassigned')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-rose-50 to-pink-50',
              statusFilter === 'unassigned' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-rose-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-rose-600">schedule</span>
              <span class="text-sm font-medium text-slate-700">待分配</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.unassigned }}</div>
          </div>
          
          <div 
            @click="handleStatusCardClick('assigned')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-lime-50 to-green-50',
              statusFilter === 'assigned' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-lime-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-lime-600">assignment_turned_in</span>
              <span class="text-sm font-medium text-slate-700">已分配</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.assigned }}</div>
          </div>
          
          <div 
            @click="handleStatusCardClick('invalid')"
            :class="[
              'cursor-pointer rounded-xl p-5 border shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 bg-gradient-to-br from-red-50 to-orange-50',
              statusFilter === 'invalid' ? 'ring-2 ring-blue-500 ring-opacity-50 border-blue-200' : 'border-red-100/50'
            ]"
          >
            <div class="flex items-center space-x-2 mb-3">
              <span class="material-icons-outlined text-red-600">block</span>
              <span class="text-sm font-medium text-slate-700">无效资源</span>
            </div>
            <div class="text-3xl font-bold text-slate-800">{{ statistics.invalid }}</div>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="border-t border-gray-200 my-8"></div>

        <!-- 筛选区域 -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6">
          <el-input
            v-model="searchQuery"
            placeholder="请输入姓名/微信号/电话搜索"
            size="default"
            class="w-full sm:w-64"
            @input="handleSearch"
            clearable
          >
            <template #prefix>
              <span class="material-icons-outlined text-gray-400">search</span>
            </template>
          </el-input>

          <el-select 
            v-model="channelFilter" 
            placeholder="渠道来源" 
            clearable 
            size="default"
            class="w-full sm:w-48"
          >
            <el-option label="全部渠道" value="" />
            <el-option label="小红书" value="小红书" />
            <el-option label="抖音" value="抖音" />
            <el-option label="转介绍" value="转介绍" />
            <el-option label="其他" value="其他" />
          </el-select>

          <el-select 
            v-model="marketStaffFilter" 
            placeholder="市场人员" 
            clearable 
            size="default"
            class="w-full sm:w-48"
            :loading="loadingOrgMembers"
          >
            <el-option label="全部人员" value="" />
            <el-option 
              v-for="member in organizationMembers" 
              :key="member.user_id"
              :label="member.display_name" 
              :value="member.user_id" 
            >
              <span class="flex items-center justify-between w-full">
                <span>{{ member.display_name }}</span>
                <span class="text-xs text-gray-500">{{ member.organization_role }}</span>
              </span>
            </el-option>
          </el-select>

          <el-select 
            v-model="assignmentStatusFilter" 
            placeholder="分配状态" 
            clearable 
            size="default"
            class="w-full sm:w-48"
          >
            <el-option label="全部状态" value="" />
            <el-option label="未分配" value="unassigned" />
            <el-option label="已分配" value="assigned" />
          </el-select>
          
          <el-button 
            @click="handleResetFilter"
            class="border-gray-300 text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
          >
            <span class="material-icons-outlined text-sm mr-1">refresh</span>
            重置
          </el-button>
        </div>

        <!-- 表头 -->
        <div class="hidden md:flex items-center bg-gray-100 rounded-lg px-4 py-3 mb-4 text-sm font-medium text-gray-600 min-h-[60px]">
          <div class="flex-1 min-w-0 grid grid-cols-11 gap-3 items-center">
            <div class="col-span-1 flex items-center">微信名称</div>
            <div class="col-span-2 flex items-center justify-center">微信账号/电话</div>
            <div class="col-span-1 flex items-center">渠道来源</div>
            <div class="col-span-1 flex items-center">市场人员</div>
            <div class="col-span-1 flex items-center">销售人员</div>
            <div class="col-span-1 flex items-center">留学项目</div>
            <div class="col-span-1 flex items-center">添加日期</div>
            <div class="col-span-1 flex items-center">分配状态</div>
            <div class="col-span-1 flex items-center justify-center">有效状态</div>
            <div class="col-span-1 flex items-center justify-center">操作</div>
          </div>
        </div>

        <!-- 市场资源横排卡片列表 -->
        <transition 
          name="resource-list"
          mode="out-in"
          appear
        >
          <div 
            :key="statusFilter + searchQuery + channelFilter + marketStaffFilter + salesStaffFilter + assignmentStatusFilter"
            class="resource-list-wrapper space-y-4"
          >
          <div
            v-for="resource in paginatedResources"
            :key="resource.id"
            class="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:bg-gray-100 cursor-pointer"
            @click="handleViewResourceDetail(resource)"
          >
            <div class="flex items-center min-h-[60px]">
              <!-- 内容区域 -->
              <div class="flex-1 min-w-0 grid grid-cols-1 md:grid-cols-11 gap-3 items-center">
                <!-- 微信名称列 -->
                <div class="col-span-12 md:col-span-1 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">微信名称</div>
                    <div class="text-sm text-gray-900">{{ resource.wechat_name || '-' }}</div>
                  </div>
                </div>

                <!-- 微信账号/电话列 -->
                <div class="col-span-12 md:col-span-2 flex items-center justify-center">
                  <div class="w-full text-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">微信账号/电话</div>
                    <div class="space-y-1">
                      <div class="text-sm text-gray-600">{{ resource.wechat_id || '-' }}</div>
                      <div class="text-sm text-gray-900">{{ resource.phone || '-' }}</div>
                    </div>
                  </div>
                </div>

                <!-- 渠道来源列 -->
                <div class="col-span-12 md:col-span-1 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">渠道来源</div>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                      {{ resource.channel_source || '-' }}
                    </span>
                  </div>
                </div>

                <!-- 市场人员列 -->
                <div class="col-span-12 md:col-span-1 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">市场人员</div>
                    <div v-if="resource.market_staff_user_id">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-50 text-purple-700 border border-purple-200">
                        {{ getStaffName(resource.market_staff_user_id) }}
                      </span>
                    </div>
                    <span v-else class="text-sm text-gray-400">-</span>
                  </div>
                </div>

                <!-- 销售人员列 -->
                <div class="col-span-12 md:col-span-1 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">销售人员</div>
                    <div v-if="resource.sales_staff_user_id">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                        {{ getStaffName(resource.sales_staff_user_id) }}
                      </span>
                    </div>
                    <span v-else class="text-sm text-gray-400">-</span>
                  </div>
                </div>

                <!-- 留学项目列 -->
                <div class="col-span-12 md:col-span-1 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">留学项目</div>
                    <div class="text-sm text-gray-900">{{ resource.study_project || '-' }}</div>
                  </div>
                </div>

                <!-- 添加日期列 -->
                <div class="col-span-12 md:col-span-1 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">添加日期</div>
                    <div class="text-sm text-gray-600">{{ resource.add_date }}</div>
                  </div>
                </div>

                <!-- 分配状态列 -->
                <div class="col-span-12 md:col-span-1 flex items-center">
                  <div class="w-full">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">分配状态</div>
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="getAssignmentStatusClass(resource.assignment_status)"
                    >
                      {{ getAssignmentStatusLabel(resource.assignment_status) }}
                    </span>
                  </div>
                </div>

                <!-- 有效状态列 -->
                <div class="col-span-12 md:col-span-1 flex items-center justify-center">
                  <div class="w-full flex justify-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">有效状态</div>
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="resource.is_valid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                    >
                      {{ resource.is_valid ? '有效' : '无效' }}
                    </span>
                  </div>
                </div>

                <!-- 操作列 -->
                <div class="col-span-12 md:col-span-1 flex items-center justify-center" @click.stop>
                  <div class="w-full flex justify-center">
                    <div class="md:hidden text-xs font-medium text-gray-600 mb-1">操作</div>
                    <el-dropdown @command="(command) => handleResourceAction(command, resource)" trigger="click" class="resource-action-dropdown">
                      <el-button link size="small" class="action-dropdown-trigger">
                        <span class="material-icons-outlined text-lg">more_vert</span>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu class="resource-dropdown-menu">
                          <el-dropdown-item command="assign" class="dropdown-menu-item">
                            <div class="flex items-center">
                              <span class="material-icons-outlined text-sm mr-2.5 text-blue-600">assignment</span>
                              <span class="text-sm font-medium text-gray-700">分配资源</span>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item command="edit" class="dropdown-menu-item">
                            <div class="flex items-center">
                              <span class="material-icons-outlined text-sm mr-2.5 text-green-600">edit</span>
                              <span class="text-sm font-medium text-gray-700">编辑信息</span>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item command="delete" class="dropdown-menu-item delete-item" divided>
                            <div class="flex items-center">
                              <span class="material-icons-outlined text-sm mr-2.5 text-red-600">delete</span>
                              <span class="text-sm font-medium text-red-600">删除资源</span>
                            </div>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="paginatedResources.length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-2">
              <span class="material-icons-outlined text-4xl">campaign</span>
            </div>
            <p class="text-gray-500">{{ searchQuery ? '未找到匹配的市场资源' : '暂无市场资源数据' }}</p>
          </div>
          </div>
        </transition>

        <!-- 分页 -->
        <div class="flex justify-between items-center mt-6" v-if="totalResources > 0">
          <div class="text-sm text-gray-500">
            共 {{ totalResources }} 条市场资源
          </div>
          <el-pagination
            v-if="totalPages > 1"
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="totalResources"
            layout="prev, pager, next"
            @current-change="handleCurrentChange"
            class="!mt-0"
          />
        </div>
      </div>
    </div>

    <!-- 添加/编辑市场资源对话框 -->
    <el-dialog
      v-model="addResourceDialogVisible"
      :title="isEditMode ? '编辑市场资源' : '添加市场资源'"
      width="600px"
      class="simple-dialog compact-dialog"
      @close="handleCloseAddResourceDialog"
    >
      <div class="simple-content">
        <el-form :model="resourceForm" label-width="120px" class="space-y-4">
          <el-form-item label="添加日期" required>
            <el-date-picker
              v-model="resourceForm.addDate"
              type="date"
              placeholder="选择添加日期"
              class="w-full"
            />
          </el-form-item>
          
          <el-form-item label="客户微信名" required>
            <el-input
              v-model="resourceForm.wechatName"
              placeholder="请输入客户微信名"
              maxlength="50"
            />
          </el-form-item>
          
          <el-form-item label="客户微信号" required>
            <el-input
              v-model="resourceForm.wechatId"
              placeholder="请输入客户微信号"
              maxlength="50"
            />
          </el-form-item>
          
          <el-form-item label="客户电话">
            <el-input
              v-model="resourceForm.phone"
              placeholder="请输入客户电话"
              maxlength="20"
            />
          </el-form-item>
          
          <el-form-item label="渠道来源" required>
            <el-select
              v-model="resourceForm.channelSource"
              placeholder="请选择渠道来源"
              allow-create
              filterable
              class="w-full"
            >
              <el-option label="小红书" value="小红书" />
              <el-option label="抖音" value="抖音" />
              <el-option label="转介绍" value="转介绍" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="市场部门人员" required>
            <el-select
              v-model="resourceForm.marketStaff"
              placeholder="请选择市场部门人员"
              class="w-full"
              :loading="loadingOrgMembers"
              clearable
            >
              <el-option 
                v-for="member in organizationMembers" 
                :key="member.user_id"
                :label="member.display_name" 
                :value="member.user_id"
              >
                <span class="flex items-center justify-between w-full">
                  <span>{{ member.display_name }}</span>
                  <span class="text-xs text-gray-500">{{ member.organization_role }}</span>
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="留学项目" required>
            <el-input
              v-model="resourceForm.studyProject"
              placeholder="例如：香港、英国、美国等"
              maxlength="100"
            />
          </el-form-item>
          
          <el-form-item label="客户背景">
            <el-input
              v-model="resourceForm.customerBackground"
              type="textarea"
              placeholder="客户的教育背景、工作经历等信息"
              :rows="3"
              maxlength="500"
            />
          </el-form-item>
          
          <el-form-item label="市场补充">
            <el-input
              v-model="resourceForm.marketSupplement"
              type="textarea"
              placeholder="市场相关的补充信息、备注等"
              :rows="3"
              maxlength="500"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <el-button @click="addResourceDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSaveResource"
            :loading="saving"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配资源对话框 -->
    <el-dialog
      v-model="assignResourceDialogVisible"
      title="分配资源"
      width="500px"
      class="simple-dialog compact-dialog"
      @close="handleCloseAssignResourceDialog"
    >
      <div class="simple-content" v-if="selectedResource">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-12 h-12 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] font-medium">
            {{ getResourceInitials(selectedResource.wechat_name || selectedResource.customer_name) }}
          </div>
          <div>
            <div class="font-medium text-gray-900">{{ selectedResource.wechat_name || selectedResource.customer_name || '未知资源' }}</div>
            <div class="text-sm text-gray-500">{{ selectedResource.wechat_name || '未设置微信名' }} | {{ selectedResource.wechat_id || '未设置微信号' }}</div>
          </div>
        </div>
        
        <el-form :model="assignmentForm" label-width="100px">
          <el-form-item label="销售人员" required>
            <el-select
              v-model="assignmentForm.salesStaff"
              placeholder="请选择销售部门人员"
              class="w-full"
              :loading="loadingOrgMembers"
              clearable
            >
              <el-option 
                v-for="member in organizationMembers" 
                :key="member.user_id"
                :label="member.display_name" 
                :value="member.user_id"
              >
                <span class="flex items-center justify-between w-full">
                  <span>{{ member.display_name }}</span>
                  <span class="text-xs text-gray-500">{{ member.organization_role }}</span>
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignResourceDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirmAssignment"
            :loading="assigning"
          >
            确认分配
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 资源预览悬浮框 -->
    <div 
      v-if="resourceDetailDialogVisible && selectedDetailResource"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click.self="handleCloseResourceDetailDialog"
    >
      <div class="bg-white rounded-xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden">
        <!-- 预览框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-blue-50">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 rounded-full bg-[#4F46E5] bg-opacity-10 flex items-center justify-center text-[#4F46E5] font-bold text-lg">
              {{ getResourceInitials(selectedDetailResource.wechat_name || selectedDetailResource.customer_name) }}
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900">{{ selectedDetailResource.wechat_name || selectedDetailResource.customer_name || '未知资源' }}</h3>
              <p class="text-sm text-gray-600">{{ selectedDetailResource.channel_source }} · {{ selectedDetailResource.assignment_status === 'assigned' ? '已分配' : '待分配' }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <el-button 
              type="primary" 
              size="default"
              @click="handleEditFromDetail"
              class="detail-action-button"
            >
              <span class="material-icons-outlined text-sm mr-1">edit</span>
              编辑信息
            </el-button>
            <el-button 
              type="primary" 
              size="default"
              @click="handleAssignFromDetail"
              class="detail-action-button"
            >
              <span class="material-icons-outlined text-sm mr-1">assignment</span>
              分配资源
            </el-button>
            <button 
              @click="handleCloseResourceDetailDialog"
              class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"
            >
              <span class="material-icons-outlined text-sm">close</span>
            </button>
          </div>
        </div>

        <!-- 预览框内容 -->
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 基本信息 -->
            <div class="col-span-1 lg:col-span-1 space-y-6">

              <!-- 联系信息 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">contact_phone</span>
                  联系信息
                </h4>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">微信名称</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedDetailResource.wechat_name || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">微信账号</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedDetailResource.wechat_id || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">联系电话</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedDetailResource.phone || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">客户姓名</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedDetailResource.customer_name || '-' }}</span>
                  </div>
                </div>
              </div>

              <!-- 渠道信息 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">campaign</span>
                  渠道信息
                </h4>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">渠道来源</span>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                      {{ selectedDetailResource.channel_source || '-' }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">市场人员</span>
                    <span 
                      v-if="selectedDetailResource.market_staff_user_id"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-50 text-purple-700 border border-purple-200"
                    >
                      {{ getStaffName(selectedDetailResource.market_staff_user_id) }}
                    </span>
                    <span v-else class="text-sm text-gray-400">-</span>
                  </div>
                </div>
              </div>

              <!-- 项目信息 -->
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">school</span>
                  项目信息
                </h4>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">留学项目</span>
                    <span class="text-sm font-medium text-gray-900">{{ selectedDetailResource.study_project || '-' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">分配状态</span>
                    <span 
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="getAssignmentStatusClass(selectedDetailResource.assignment_status)"
                    >
                      {{ getAssignmentStatusLabel(selectedDetailResource.assignment_status) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 详细信息 -->
            <div class="col-span-1 lg:col-span-1 space-y-6">

              <!-- 分配信息 -->
              <div class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">assignment</span>
                  分配信息
                </h4>
                <div class="space-y-3">
                  <div class="flex justify-between items-start">
                    <span class="text-sm text-gray-600">销售人员</span>
                    <!-- 处理多种可能的字段名：sales_staff_user_ids (数组) 或 sales_staff_user_id (单个) -->
                    <div class="flex flex-wrap gap-1 justify-end" v-if="(selectedDetailResource.sales_staff_user_ids && selectedDetailResource.sales_staff_user_ids.length > 0) || selectedDetailResource.sales_staff_user_id">
                      <!-- 如果是数组 -->
                      <template v-if="selectedDetailResource.sales_staff_user_ids && selectedDetailResource.sales_staff_user_ids.length > 0">
                        <span 
                          v-for="staffId in selectedDetailResource.sales_staff_user_ids" 
                          :key="staffId"
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200"
                        >
                          {{ getStaffName(staffId) }}
                        </span>
                      </template>
                      <!-- 如果是单个 -->
                      <template v-else-if="selectedDetailResource.sales_staff_user_id">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200">
                          {{ getStaffName(selectedDetailResource.sales_staff_user_id) }}
                        </span>
                      </template>
                    </div>
                    <span v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200">
                      未分配
                    </span>
                  </div>
                </div>
              </div>

              <!-- 客户背景 -->
              <div class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">person</span>
                  客户背景
                </h4>
                <p class="text-sm text-gray-700 leading-relaxed">
                  {{ selectedDetailResource.customer_background || '暂无客户背景信息' }}
                </p>
              </div>

              <!-- 市场补充 -->
              <div class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
                <h4 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                  <span class="material-icons-outlined text-indigo-600 mr-2">info</span>
                  市场补充
                </h4>
                <p class="text-sm text-gray-700 leading-relaxed">
                  {{ selectedDetailResource.market_supplement || '暂无市场补充信息' }}
                </p>
              </div>
            </div>
          </div>
        </div>
        </div> <!-- 组织身份内容结束 -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getCRMClientList, 
  createCRMClient, 
  updateCRMClient, 
  deleteCRMClient, 
  getCRMStatistics,
  assignStaffToClient,
  removeStaffFromClient,
  getOrganizationMembersForCRM,
  CHANNEL_SOURCE_OPTIONS,
  ASSIGNMENT_STATUS_OPTIONS,
  LIFECYCLE_STATUS
} from '@/api/crm'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import { useAuthStore } from '@/stores/auth'

// 初始化权限管理
const { isOrganizationOwner } = useOrganizationPermissions()
const authStore = useAuthStore()

// 计算属性：是否有权限查看所有资源（组织owner）
const canViewAllResources = computed(() => {
  return isOrganizationOwner.value
})

// 响应式数据
const searchQuery = ref('')
const channelFilter = ref('')
const marketStaffFilter = ref('')
const salesStaffFilter = ref('')
const assignmentStatusFilter = ref('')
const statusFilter = ref('')
const loading = ref(false)
const cardClickLoading = ref(false)
const saving = ref(false)

// 视图模式控制 - 默认显示我创建的资源，组织owner可以切换
const showOnlyMyResources = ref(true)
const toggleLoading = ref(false)
const assigning = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框状态
const addResourceDialogVisible = ref(false)
const assignResourceDialogVisible = ref(false)
const resourceDetailDialogVisible = ref(false)
const selectedResource = ref(null)
const selectedDetailResource = ref(null)
const editingResource = ref(null) // 当前正在编辑的资源
const isEditMode = ref(false)

// 表单数据
const resourceForm = ref({
  addDate: new Date(),
  customerName: '',
  wechatName: '',
  wechatId: '',
  phone: '',
  channelSource: '',
  marketStaff: null, // 单选
  studyProject: '',
  customerBackground: '',
  marketSupplement: ''
})

const assignmentForm = ref({
  salesStaff: null // 单选
})

// 数据状态
const marketResources = ref([])
const totalResources = ref(0)
const statistics = ref({
  total: 0,
  unassigned: 0,
  assigned: 0,
  invalid: 0
})

// 组织成员数据
const organizationMembers = ref([])
const loadingOrgMembers = ref(false)

// 组织ID（从当前身份信息获取）
const organizationId = computed(() => {
  return authStore.currentIdentity?.organization_id || null
})

// 检查是否为组织身份
const isOrganizationIdentity = computed(() => {
  return authStore.currentIdentity?.identity_type === 'organization' && organizationId.value
})

// API函数
// 获取CRM客户列表  
const fetchMarketResources = async () => {
  // 检查是否为组织身份，个人身份不调用API
  if (!isOrganizationIdentity.value) {
    return
  }
  
  try {
    loading.value = true
    const params = {
      organization_id: organizationId.value,
      page: currentPage.value,
      size: pageSize.value
      // 市场资源界面显示所有活跃客户（lead + valid），排除已签约和已归档
    }

    // 应用筛选条件
    if (searchQuery.value) params.search_keyword = searchQuery.value
    if (channelFilter.value) params.channel_source = channelFilter.value
    if (salesStaffFilter.value) params.sales_staff_user_id = salesStaffFilter.value
    
    // 权限控制：根据用户权限和视角决定过滤逻辑
    if (canViewAllResources.value) {
      // 组织owner可以查看所有资源或自己创建的资源
      if (showOnlyMyResources.value) {
        // 显示我创建的资源，按市场人员筛选
        params.market_staff_user_id = authStore.user?.id
      } else {
        // 显示所有资源，应用手动市场人员筛选
        if (marketStaffFilter.value) params.market_staff_user_id = marketStaffFilter.value
      }
    } else {
      // 普通成员只能看自己创建的资源
      params.market_staff_user_id = authStore.user?.id
    }
    
    // 状态筛选：根据看板卡片点击进行筛选（优先级高于手动筛选）
    if (statusFilter.value) {
      switch (statusFilter.value) {
        case 'unassigned':
          params.assignment_status = 'unassigned' // 未分配（待分配）
          break
        case 'assigned':
          params.assignment_status = 'assigned' // 已分配
          break
        case 'invalid':
          params.is_valid = false // 无效资源
          break
        // 空字符串表示显示所有资源，不添加额外筛选条件
      }
    } else {
      // 如果没有看板卡片筛选，则应用手动筛选
      if (assignmentStatusFilter.value) params.assignment_status = assignmentStatusFilter.value
    }
    
    // 过滤掉已签约和已归档的客户
    params.exclude_lifecycle_status = 'signed,archived'

    const response = await getCRMClientList(params)
    marketResources.value = response.items || []
    totalResources.value = response.total || 0
    
    // 获取资源后立即更新统计数据
    await fetchStatistics()
  } catch (error) {
    console.error('获取市场资源失败:', error)
    ElMessage.error('获取市场资源失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  // 检查是否为组织身份，个人身份不调用API
  if (!isOrganizationIdentity.value) {
    return
  }
  
  try {
    const response = await getCRMStatistics(organizationId.value)
    
    // 根据用户权限和视角重新计算统计数据
    if (showOnlyMyResources.value || !canViewAllResources.value) {
      // 显示我创建的资源模式，基于当前资源列表重新计算统计
      const myResources = marketResources.value
      const recalculatedStats = {
        total: myResources.length,
        unassigned: myResources.filter(r => r.assignment_status === 'unassigned').length,
        assigned: myResources.filter(r => r.assignment_status === 'assigned').length,
        invalid: myResources.filter(r => r.is_valid === false).length
      }
      statistics.value = recalculatedStats
    } else {
      // 组织owner显示全局统计数据
      statistics.value = response
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 计算属性：已筛选的资源列表（API已筛选，这里直接返回）
const filteredResources = computed(() => {
  return marketResources.value
})

// 计算属性：总页数
const totalPages = computed(() => {
  return Math.ceil(totalResources.value / pageSize.value)
})

// 计算属性：当前页的资源列表（API已分页，直接返回）
const paginatedResources = computed(() => {
  return marketResources.value
})

// 方法：获取资源名称首字母
const getResourceInitials = (name) => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

// 方法：获取分配状态标签
const getAssignmentStatusLabel = (status) => {
  const statusMap = {
    unassigned: '未分配',
    assigned: '已分配'
  }
  return statusMap[status] || '未知'
}

// 方法：获取分配状态样式
const getAssignmentStatusClass = (status) => {
  const classMap = {
    unassigned: 'bg-yellow-100 text-yellow-800',
    assigned: 'bg-green-100 text-green-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
}

// 切换显示所有资源
const showAllResources = async () => {
  if (!canViewAllResources.value) {
    console.warn('用户无权限查看所有资源')
    return
  }
  
  if (toggleLoading.value) return // 防止重复点击
  
  // 立即更新UI状态，给用户即时反馈
  showOnlyMyResources.value = false
  currentPage.value = 1 // 重置到第一页
  
  try {
    toggleLoading.value = true
    if (process.env.NODE_ENV === 'development') {
      console.log('切换到显示所有资源')
    }
    // 更新资源列表（统计数据会在fetchMarketResources中自动更新）
    await fetchMarketResources()
  } catch (error) {
    // 如果失败，回滚状态
    showOnlyMyResources.value = true
    throw error
  } finally {
    toggleLoading.value = false
  }
}

// 切换显示我的资源
const showMyResources = async () => {
  if (toggleLoading.value) return // 防止重复点击
  
  // 立即更新UI状态，给用户即时反馈
  showOnlyMyResources.value = true
  currentPage.value = 1 // 重置到第一页
  
  try {
    toggleLoading.value = true
    if (process.env.NODE_ENV === 'development') {
      console.log('切换到显示我的资源')
    }
    // 更新资源列表（统计数据会在fetchMarketResources中自动更新）
    await fetchMarketResources()
  } catch (error) {
    // 如果失败，回滚状态
    showOnlyMyResources.value = false
    throw error
  } finally {
    toggleLoading.value = false
  }
}

// 重置筛选
const handleResetFilter = () => {
  channelFilter.value = ''
  marketStaffFilter.value = ''
  assignmentStatusFilter.value = ''
  salesStaffFilter.value = ''
  statusFilter.value = ''
  searchQuery.value = ''
  currentPage.value = 1
  // 重新获取数据
  fetchMarketResources()
}

// 状态卡片点击处理
const handleStatusCardClick = async (status) => {
  // 防止快速重复点击
  if (cardClickLoading.value) return
  
  cardClickLoading.value = true
  
  try {
    // 如果点击的是当前已选中的卡片，则取消筛选
    if (statusFilter.value === status) {
      statusFilter.value = ''
    } else {
      // 否则设置新的筛选条件
      statusFilter.value = status
      // 清除其他可能冲突的筛选条件
      if (status === 'assigned' || status === 'unassigned') {
        assignmentStatusFilter.value = ''
      }
    }
    currentPage.value = 1
    
    // 立即刷新数据
    await fetchMarketResources()
  } finally {
    // 200ms后恢复点击
    setTimeout(() => {
      cardClickLoading.value = false
    }, 200)
  }
}

// 分页处理
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

// 事件处理：添加市场资源
const handleAddResource = async () => {
  isEditMode.value = false
  resourceForm.value = {
    addDate: new Date(),
    customerName: '',
    wechatName: '',
    wechatId: '',
    phone: '',
    channelSource: '',
    marketStaff: null, // 单选
    studyProject: '',
    customerBackground: '',
    marketSupplement: ''
  }
  
  // 获取组织成员数据（如果还没有获取过）
  if (organizationMembers.value.length === 0) {
    await fetchOrganizationMembers()
  }
  
  addResourceDialogVisible.value = true
}

// 事件处理：保存资源
const handleSaveResource = async () => {
  // 验证必填字段：添加日期、客户微信名、客户微信号、渠道来源、市场部门人员、留学项目
  const requiredFields = [
    { field: 'addDate', name: '添加日期' },
    { field: 'wechatName', name: '客户微信名' },
    { field: 'wechatId', name: '客户微信号' },
    { field: 'channelSource', name: '渠道来源' },
    { field: 'marketStaff', name: '市场部门人员', isArray: false },
    { field: 'studyProject', name: '留学项目' }
  ]
  
  const missingFields = requiredFields.filter(({ field, isArray }) => {
    if (isArray) {
      return !resourceForm.value[field] || resourceForm.value[field].length === 0
    }
    return !resourceForm.value[field]
  })
  
  if (missingFields.length > 0) {
    const missingFieldNames = missingFields.map(({ name }) => name).join('、')
    ElMessage.warning(`请填写必填项：${missingFieldNames}`)
    return
  }

  try {
    saving.value = true
    
    const formData = {
      organization_id: parseInt(organizationId.value),
      add_date: resourceForm.value.addDate instanceof Date 
        ? resourceForm.value.addDate.toISOString().split('T')[0] 
        : resourceForm.value.addDate,
      customer_name: resourceForm.value.customerName || null,
      wechat_name: resourceForm.value.wechatName,
      wechat_id: resourceForm.value.wechatId,
      phone: resourceForm.value.phone || null,
      channel_source: resourceForm.value.channelSource,
      study_project: resourceForm.value.studyProject,
      customer_background: resourceForm.value.customerBackground || null,
      market_supplement: resourceForm.value.marketSupplement || null,
      is_valid: true
    }
    
    // 只在新建模式下设置这些状态字段，编辑模式下保持原有状态
    if (!isEditMode.value) {
      formData.lifecycle_status = LIFECYCLE_STATUS.LEAD
      formData.assignment_status = 'unassigned'
    }
    
    // 调试输出
    console.log('准备发送的formData:', formData)
    console.log('resourceForm值:', resourceForm.value)
    
    // 验证关键字段
    if (!formData.wechat_name || !formData.wechat_id || !formData.channel_source || !formData.study_project) {
      console.error('关键字段缺失:', {
        wechat_name: formData.wechat_name,
        wechat_id: formData.wechat_id,
        channel_source: formData.channel_source,
        study_project: formData.study_project
      })
      ElMessage.error('请填写所有必填字段')
      return
    }
    
    let createdClientId = null
    let originalMarketStaff = null
    
    if (isEditMode.value && editingResource.value) {
      // 更新模式
      originalMarketStaff = editingResource.value.market_staff_user_id // 记录原始的市场人员
      await updateCRMClient(editingResource.value.id, formData, organizationId.value)
      createdClientId = editingResource.value.id
      ElMessage.success('市场资源更新成功')
    } else {
      // 新建模式
      console.log('发送到API的最终数据:', formData)
      
      // 确保所有字段都是有效的JSON序列化值
      const cleanFormData = {
        ...formData,
        organization_id: parseInt(formData.organization_id),
        add_date: formData.add_date,
        customer_name: formData.customer_name || null,
        wechat_name: String(formData.wechat_name),
        wechat_id: String(formData.wechat_id),
        phone: formData.phone || null,
        channel_source: String(formData.channel_source),
        study_project: String(formData.study_project),
        customer_background: formData.customer_background || null,
        market_supplement: formData.market_supplement || null,
        lifecycle_status: String(formData.lifecycle_status),
        assignment_status: String(formData.assignment_status),
        is_valid: Boolean(formData.is_valid)
      }
      
      console.log('清理后的数据:', cleanFormData)
      const response = await createCRMClient(cleanFormData)
      createdClientId = response.id
      ElMessage.success('市场资源添加成功')
    }
    
    // 处理市场人员分配
    if (resourceForm.value.marketStaff && createdClientId) {
      // 确保类型一致性，将ID转换为数字
      const currentMarketStaffId = isEditMode.value 
        ? editingResource.value?.market_staff_user_id
        : null
      const newMarketStaffId = parseInt(resourceForm.value.marketStaff)
      
      const marketStaffChanged = !isEditMode.value || currentMarketStaffId !== newMarketStaffId
      
      console.log('市场人员分配检查:', {
        isEditMode: isEditMode.value,
        currentMarketStaffId,
        newMarketStaffId,
        marketStaffChanged
      })
      
      if (marketStaffChanged) {
        try {
          // 在编辑模式下，需要先移除原有的人员分配
          if (isEditMode.value && currentMarketStaffId) {
            console.log('需要移除的市场人员:', currentMarketStaffId)
            await removeStaffFromClient(createdClientId, {
              user_id: currentMarketStaffId,
              role: 'market',
              organization_id: organizationId.value
            })
            console.log('成功移除原市场人员')
          }
          
          // 分配新的市场人员
          console.log('分配新的市场人员:', newMarketStaffId)
          await assignStaffToClient(createdClientId, {
            user_id: newMarketStaffId,
            role: 'market',
            organization_id: organizationId.value
          })
          console.log('成功分配市场人员')
        } catch (error) {
          console.error('处理市场人员分配失败:', error)
          
          // 处理具体的错误信息
          if (error?.response?.data?.detail?.includes('已被分配')) {
            ElMessage.warning('选中的市场人员中有已经分配到此客户的人员')
          } else {
            ElMessage.error('处理市场人员分配时发生错误')
          }
          // 不影响主流程，只记录错误
        }
      } else {
        console.log('市场人员没有变化，跳过分配')
      }
    }
    
    addResourceDialogVisible.value = false
    isEditMode.value = false
    editingResource.value = null
    
    // 重新获取数据
    await fetchMarketResources()
    await fetchStatistics()
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 获取组织成员列表
const fetchOrganizationMembers = async () => {
  // 检查是否为组织身份，个人身份不调用API
  if (!isOrganizationIdentity.value) {
    return
  }
  
  try {
    loadingOrgMembers.value = true
    const response = await getOrganizationMembersForCRM(organizationId.value)
    organizationMembers.value = response.members || []
  } catch (error) {
    console.error('获取组织成员失败:', error)
    // 个人身份时不需要显示错误消息
    if (isOrganizationIdentity.value) {
      ElMessage.error('获取组织成员失败')
    }
  } finally {
    loadingOrgMembers.value = false
  }
}

// 根据用户ID获取人员姓名
const getStaffName = (userId) => {
  const member = organizationMembers.value.find(m => m.user_id === parseInt(userId))
  return member ? member.display_name : `用户${userId}`
}

// 市场人员选择变更（单选模式无需特殊处理）

// 销售人员选择变更（单选模式无需特殊处理）

// 事件处理：分配资源
const handleAssignResource = async (resource) => {
  selectedResource.value = resource
  assignmentForm.value = {
    salesStaff: null // 单选初始化
  }
  
  // 获取组织成员数据（如果还没有获取过）
  if (organizationMembers.value.length === 0) {
    await fetchOrganizationMembers()
  }
  
  assignResourceDialogVisible.value = true
}

// 事件处理：确认分配（单选）
const handleConfirmAssignment = async () => {
  if (!assignmentForm.value.salesStaff) {
    ElMessage.warning('请选择销售人员')
    return
  }

  // 获取当前和新的销售人员分配（确保类型一致性）
  const currentSalesStaffId = selectedResource.value.sales_staff_user_id
  const newSalesStaffId = parseInt(assignmentForm.value.salesStaff)
  
  console.log('销售人员分配检查:', {
    currentSalesStaffId,
    newSalesStaffId
  })

  try {
    assigning.value = true
    
    // 先移除原有的销售人员分配
    if (currentSalesStaffId && currentSalesStaffId !== newSalesStaffId) {
      console.log('需要移除的销售人员:', currentSalesStaffId)
      await removeStaffFromClient(selectedResource.value.id, {
        user_id: currentSalesStaffId,
        role: 'sales',
        organization_id: organizationId.value
      })
      console.log('成功移除原销售人员')
    }
    
    // 分配新的销售人员
    if (currentSalesStaffId !== newSalesStaffId) {
      console.log('分配新的销售人员:', newSalesStaffId)
      await assignStaffToClient(selectedResource.value.id, {
        user_id: newSalesStaffId,
        role: 'sales', // 角色为销售人员
        organization_id: organizationId.value
      })
      console.log('成功分配销售人员')
    }
    
    // 更新资源状态为已分配
    const updateData = {
      assignment_status: 'assigned'
    }
    await updateCRMClient(selectedResource.value.id, updateData, organizationId.value)
    
    ElMessage.success('销售人员分配更新成功')
    assignResourceDialogVisible.value = false
    
    // 重新获取数据以反映最新状态
    await fetchMarketResources()
    await fetchStatistics()
    
  } catch (error) {
    console.error('分配失败:', error)
    
    // 处理具体的错误信息
    if (error?.response?.data?.detail?.includes('已被分配')) {
      ElMessage.error('该用户已被分配到此客户的相同角色')
    } else {
      ElMessage.error('分配失败，请重试')
    }
  } finally {
    assigning.value = false
  }
}



// 事件处理：查看资源详情
const handleViewResource = (resource) => {
  handleViewResourceDetail(resource)
}

// 事件处理：查看资源详细信息
const handleViewResourceDetail = (resource) => {
  selectedDetailResource.value = resource
  resourceDetailDialogVisible.value = true
}

// 事件处理：从详情页分配资源
const handleAssignFromDetail = () => {
  resourceDetailDialogVisible.value = false
  handleAssignResource(selectedDetailResource.value)
}

// 将数据库格式转换为表单格式
const mapDatabaseToForm = (dbData) => {
  return {
    addDate: dbData.add_date ? new Date(dbData.add_date) : new Date(),
    customerName: dbData.customer_name || '',
    wechatName: dbData.wechat_name || '',
    wechatId: dbData.wechat_id || '',
    phone: dbData.phone || '',
    channelSource: dbData.channel_source || '',
    marketStaff: dbData.market_staff_user_id || null, // 单选格式
    studyProject: dbData.study_project || '',
    customerBackground: dbData.customer_background || '',
    marketSupplement: dbData.market_supplement || ''
  }
}

// 事件处理：从详情页编辑资源
const handleEditFromDetail = async () => {
  resourceDetailDialogVisible.value = false
  isEditMode.value = true
  editingResource.value = selectedDetailResource.value
  resourceForm.value = mapDatabaseToForm(selectedDetailResource.value)
  
  // 获取组织成员数据（如果还没有获取过）
  if (organizationMembers.value.length === 0) {
    await fetchOrganizationMembers()
  }
  
  addResourceDialogVisible.value = true
}

// 事件处理：资源操作
const handleResourceAction = async (command, resource) => {
  switch (command) {
    case 'assign':
      // 分配资源
      handleAssignResource(resource)
      break
    case 'edit':
      // 编辑资源信息
      isEditMode.value = true
      editingResource.value = resource
      resourceForm.value = mapDatabaseToForm(resource)
      
      // 获取组织成员数据（如果还没有获取过）
      if (organizationMembers.value.length === 0) {
        await fetchOrganizationMembers()
      }
      
      addResourceDialogVisible.value = true
      break
    case 'delete':
      await handleDeleteResource(resource)
      break
  }
}

// 事件处理：删除资源
const handleDeleteResource = async (resource) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除市场资源 "${resource.customer_name || resource.wechat_name}" 吗？\n\n删除后将无法恢复。`,
      '删除资源确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    await deleteCRMClient(resource.id, organizationId.value)
    ElMessage.success(`已成功删除资源 "${resource.customer_name || resource.wechat_name}"`)
    
    // 重新获取数据
    await fetchMarketResources()
    await fetchStatistics()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 关闭对话框
const handleCloseAddResourceDialog = () => {
  addResourceDialogVisible.value = false
  isEditMode.value = false
  editingResource.value = null
}

const handleCloseAssignResourceDialog = () => {
  assignResourceDialogVisible.value = false
  selectedResource.value = null
}

const handleCloseResourceDetailDialog = () => {
  resourceDetailDialogVisible.value = false
  selectedDetailResource.value = null
}

// 监听筛选条件变化，重新获取数据
watch([searchQuery, channelFilter, assignmentStatusFilter, marketStaffFilter, salesStaffFilter, statusFilter], () => {
  currentPage.value = 1
  fetchMarketResources()
}, { debounce: 300 })

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchMarketResources()
})

// 监听权限变化，更新默认显示模式 - 与ValidCustomers.vue保持一致
watch(canViewAllResources, (newValue) => {
  if (!newValue) {
    // 如果失去权限，强制显示我的资源
    showOnlyMyResources.value = true
  }
}, { immediate: true })

// 组件挂载
onMounted(async () => {
  // 初始化数据
  console.log('市场资源页面加载完成')
  
  try {
    // 根据权限设置默认显示模式 - 与ValidCustomers.vue保持一致
    if (canViewAllResources.value) {
      showOnlyMyResources.value = false // owner默认显示所有资源
      console.log('检测到权限用户，默认显示所有市场资源')
    }
    
    // 并行加载数据
    await Promise.all([
      fetchMarketResources(),
      fetchStatistics(),
      fetchOrganizationMembers() // 预加载组织成员数据
    ])
  } catch (error) {
    console.error('市场资源页面初始化失败:', error)
  }
  
  // 强制设置Element Plus主色调为紫色
  const setElementPlusTheme = () => {
    const root = document.documentElement
    root.style.setProperty('--el-color-primary', '#4F46E5')
    root.style.setProperty('--el-color-primary-light-3', '#7C3AED')
    root.style.setProperty('--el-color-primary-light-5', '#A855F7')
    root.style.setProperty('--el-color-primary-light-7', '#C084FC')
    root.style.setProperty('--el-color-primary-light-8', '#DDD6FE')
    root.style.setProperty('--el-color-primary-light-9', '#EDE9FE')
    root.style.setProperty('--el-color-primary-dark-2', '#3730A3')
  }
  
  setElementPlusTheme()
  
  // 监听DOM变化，确保下拉框出现时也应用主题
  const observer = new MutationObserver(() => {
    setElementPlusTheme()
  })
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
})
</script>

<style scoped>
/* 市场资源页面样式 - 与AccountManagement.vue保持一致 */
.market-resources-page {
  @apply p-6;
}

/* Pro Card 样式 */
.pro-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.pro-card-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.pro-card-title {
  @apply flex items-center text-lg font-medium text-gray-900;
}

.pro-card-title .icon {
  @apply mr-2 text-gray-500;
}

.pro-card-body {
  @apply p-6;
}

/* 按钮样式 - 使用系统紫色主题 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

.add-resource-button,
.detail-action-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.add-resource-button:hover,
.detail-action-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 按钮样式 - 使用系统紫色主题 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

.add-resource-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.add-resource-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

.assign-resource-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
}

.assign-resource-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  color: white !important;
}

/* 简洁对话框样式 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
  }

  /* 次要按钮样式（取消按钮） */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
    color: #374151 !important;
    border-color: #D1D5DB !important;
    background-color: #FFFFFF !important;
  }

  /* 次要按钮悬停状态 */
  .el-button:not(.el-button--primary):hover {
    color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    background-color: #F8FAFF !important;
  }
}

/* 详情页面按钮样式 */
.detail-action-button {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
  font-size: 13px !important;
  height: 32px !important;
  padding: 0 12px !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2) !important;
  transition: all 0.2s ease !important;
}

.detail-action-button:hover {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3) !important;
  transform: translateY(-1px) !important;
}

.detail-action-button:active {
  background-color: #3730A3 !important;
  border-color: #3730A3 !important;
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2) !important;
}

/* 资源操作下拉框样式 */
.action-dropdown-trigger {
  color: #6B7280 !important;
  padding: 4px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.action-dropdown-trigger:hover {
  color: #4F46E5 !important;
  background-color: #F3F4F6 !important;
}

/* 下拉菜单样式 */
:deep(.resource-dropdown-menu) {
  border-radius: 7px !important;
  box-shadow: 0 3px 16px rgba(0, 0, 0, 0.13) !important;
  border: 1px solid #E5E7EB !important;
  padding: 3px 0 !important;
  min-width: 130px !important;
}

:deep(.dropdown-menu-item) {
  padding: 7px 14px !important;
  margin: 1px 3px !important;
  border-radius: 5px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
}

:deep(.dropdown-menu-item:hover) {
  background-color: #F9FAFB !important;
}

:deep(.dropdown-menu-item.delete-item:hover) {
  background-color: #FEF2F2 !important;
}

/* 分割线样式 */
:deep(.dropdown-menu-item.el-dropdown-menu__item--divided) {
  border-top: 1px solid #E5E7EB !important;
  margin-top: 5px !important;
  padding-top: 9px !important;
}

/* 全局下拉菜单项样式覆盖 */
:global(.el-dropdown-menu .el-dropdown-menu__item) {
  padding: 7px 14px !important;
  font-size: 13px !important;
  line-height: 1.35 !important;
  color: #374151 !important;
}

:global(.el-dropdown-menu .el-dropdown-menu__item:hover) {
  background-color: #F9FAFB !important;
  color: #374151 !important;
}

:global(.el-dropdown-menu .el-dropdown-menu__item.delete-item:hover) {
  background-color: #FEF2F2 !important;
}

/* 输入框和选择框紫色主题 */
:deep(.el-input .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-input .el-input__wrapper.is-focus) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px #4F46E5 inset;
}

/* 文本域紫色主题 */
:deep(.el-textarea .el-textarea__inner) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-textarea .el-textarea__inner:focus) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

:deep(.el-textarea .el-textarea__inner:hover) {
  border-color: #6366F1 !important;
}

/* 页面级别的Element Plus主色调覆盖 */
.market-resources-page {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #7C3AED !important;
  --el-color-primary-light-5: #A855F7 !important;
  --el-color-primary-light-7: #C084FC !important;
  --el-color-primary-light-8: #DDD6FE !important;
  --el-color-primary-light-9: #EDE9FE !important;
  --el-color-primary-dark-2: #3730A3 !important;
}

/* 选择框紫色主题 */
:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-select .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  border-color: #6366F1 !important;
}

/* 下拉选项样式 - 更强制的全局覆盖 */
:deep(.el-select-dropdown) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #EEF2FF !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected::after) {
  color: #FFFFFF !important;
}

/* 全局下拉框选项样式覆盖 */
:global(.el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #EEF2FF !important;
  color: #4F46E5 !important;
}

:global(.el-select-dropdown .el-select-dropdown__item.selected) {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
}

:global(.el-select-dropdown .el-select-dropdown__item.selected::after) {
  color: #FFFFFF !important;
}

/* Element Plus 选择框主色调全局覆盖 */
:global(.el-popper.is-light .el-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #EEF2FF !important;
  color: #4F46E5 !important;
}

:global(.el-popper.is-light .el-select-dropdown .el-select-dropdown__item.selected) {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
}

/* 多选标签样式 */
:deep(.el-tag) {
  --el-tag-bg-color: #EEF2FF;
  --el-tag-text-color: #4F46E5;
  --el-tag-border-color: #C7D2FE;
}

:deep(.el-tag.el-tag--info) {
  background-color: #EEF2FF !important;
  color: #4F46E5 !important;
  border-color: #C7D2FE !important;
}

:deep(.el-tag .el-tag__close) {
  color: #4F46E5 !important;
}

:deep(.el-tag .el-tag__close:hover) {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
}

/* 日期选择器紫色主题 */
:deep(.el-date-editor .el-input__wrapper) {
  --el-input-focus-border-color: #4F46E5;
  --el-input-hover-border-color: #6366F1;
}

:deep(.el-date-editor .el-input__wrapper.is-focus) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}



/* 资源列表过渡动画 */
.resource-list-enter-active,
.resource-list-leave-active {
  transition: opacity 0.2s ease;
}

.resource-list-enter-from,
.resource-list-leave-to {
  opacity: 0;
}

.resource-list-enter-to,
.resource-list-leave-from {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .market-resources-page {
    @apply p-4;
  }
  
  .crm-filter-section {
    @apply p-4;
  }
}

@media (max-width: 768px) {
  .market-resources-page {
    @apply p-3;
  }
}
</style>