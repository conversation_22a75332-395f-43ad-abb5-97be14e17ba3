<template>
  <div class="ai-reducer-page bg-gray-50 max-w-7xl mx-auto p-6">
      <!-- 页面标题区域 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">TunshuEdu降AI工具</h1>
        <p class="text-gray-600 text-lg">
          TunshuEdu独家推出拟人化AI文书优化工具，精准清理痕迹、优化内容，为你的留学申请文书保驾护航。
        </p>
      </div>

      <!-- 主要内容区域 -->
      <div class="bg-white rounded-2xl shadow-lg p-8">
        <!-- 输入区域 -->
        <div class="mb-6">
          <!-- 输入标题 -->
          <div class="mb-4">
            <h2 class="text-lg font-semibold text-gray-800">
              输入内容 
              <span class="text-sm font-normal text-gray-500">(仅限英文)</span>
            </h2>
          </div>

          <!-- 文本输入框 -->
          <div class="relative">
            <textarea
              v-model="inputText"
              placeholder="请在此输入或粘贴需要处理的文本..."
              class="w-full h-96 p-4 border border-gray-200 rounded-xl focus:border-blue-500 resize-none text-base leading-relaxed"
              :disabled="isProcessing"
            ></textarea>
            
            <!-- 字数统计 -->
            <div class="absolute bottom-3 right-3 text-xs" :class="getCharCountColor(inputText.length)">
              {{ inputText.length }} 字符
              <span v-if="inputText.length < 500" class="ml-1">(至少需要500字符)</span>
              <span v-else-if="inputText.length > 20000" class="ml-1">(超出20000字符限制)</span>
            </div>
          </div>
        </div>

        <!-- 前后对比结果显示 -->
        <div v-if="beforeAfterComparison" class="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-6 mb-8">
          <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-800">降AI率前后对比</h3>
          </div>

          <!-- 对比统计 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-red-500">{{ beforeAfterComparison.before.aiRate }}%</div>
              <div class="text-sm text-gray-600">降AI前</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold" :class="beforeAfterComparison.improvement > 0 ? 'text-green-500' : 'text-yellow-500'">
                {{ beforeAfterComparison.improvement > 0 ? '-' : '' }}{{ Math.abs(beforeAfterComparison.improvement) }}%
              </div>
              <div class="text-sm text-gray-600">变化</div>
            </div>
            <div class="bg-white rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-green-500">{{ beforeAfterComparison.after.aiRate }}%</div>
              <div class="text-sm text-gray-600">降AI后</div>
            </div>
          </div>

          <!-- 文本对比 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 原文 -->
            <div class="bg-white rounded-lg overflow-hidden">
              <div class="bg-red-50 px-4 py-2 border-b">
                <h4 class="font-medium text-red-700">原文 (AI率: {{ beforeAfterComparison.before.aiRate }}%)</h4>
              </div>
              <div class="p-4 max-h-64 overflow-y-auto">
                <div class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap" v-html="beforeAfterComparison.before.highlighted_html"></div>
              </div>
            </div>

            <!-- 降AI后文本 -->
            <div class="bg-white rounded-lg overflow-hidden">
              <div class="bg-green-50 px-4 py-2 border-b">
                <h4 class="font-medium text-green-700">降AI后 (AI率: {{ beforeAfterComparison.after.aiRate }}%)</h4>
              </div>
              <div class="p-4 max-h-64 overflow-y-auto">
                <div class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap" v-html="beforeAfterComparison.after.highlighted_html"></div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="mt-6 flex justify-center space-x-4">
            <button
              @click="handleCopyResult"
              class="px-6 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm transition-colors flex items-center"
            >
              <span class="material-icons-outlined text-sm mr-1">content_copy</span>
              复制降AI后文本
            </button>
            <button
              @click="handleUseReducedText"
              class="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm transition-colors flex items-center"
            >
              <span class="material-icons-outlined text-sm mr-1">input</span>
              使用降AI后文本
            </button>
          </div>
        </div>

        <!-- 简单降AI率结果显示（兼容旧逻辑） -->
        <div v-else-if="reducedText && !beforeAfterComparison" class="bg-green-50 rounded-xl p-6 mb-8">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">降AI率结果</h3>
          <div class="space-y-3">
            <div class="bg-white rounded-lg p-4 max-h-64 overflow-y-auto">
              <div class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">{{ reducedText }}</div>
            </div>
            <button
              @click="handleCopyResult"
              class="w-full px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm transition-colors"
            >
              复制结果
            </button>
          </div>
        </div>

        <!-- AI检测结果看板 -->
        <div v-if="detectionResult" class="mt-8 pt-6 border-t border-gray-200">
          <!-- 结果头部 -->
          <div class="mb-6">
            <h2 class="text-2xl font-semibold text-gray-800">AI率检测结果</h2>
          </div>

          <!-- 仪表盘和高亮文本 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- 左侧仪表盘 -->
            <div class="md:col-span-1 flex flex-col items-center justify-center bg-gray-50 rounded-xl p-6">
              <svg width="250" height="160" viewBox="0 0 250 160" class="mx-auto">
                <path d="M 25 140 A 100 100 0 0 1 225 140" stroke="#e5e7eb" stroke-width="18" fill="none" />
                <path :d="getArcPath(detectionResult.aiRate)" :stroke="getArcColor(detectionResult.aiRate)" stroke-width="18" fill="none" stroke-linecap="round" />
              </svg>
              <div class="text-center -mt-8">
                <div class="text-5xl font-bold" :class="getAiRateColor(detectionResult.aiRate)">
                  {{ detectionResult.aiRate }}%
                </div>
                <div class="text-lg text-gray-600">AI Generated</div>
              </div>
              <p class="text-center text-sm text-gray-500 mt-4" :class="getAiRateColor(detectionResult.aiRate)">
                {{ getAiRateMessage(detectionResult.aiRate) }}
              </p>
            </div>

            <!-- 右侧高亮文本 -->
            <div class="md:col-span-2">
              <div class="border rounded-lg overflow-hidden h-96">
                <div class="bg-gray-50 px-4 py-2 border-b">
                  <h3 class="font-medium text-gray-700">文本分析 (AI内容已高亮)</h3>
                </div>
                <div class="p-4 overflow-y-auto h-full bg-white" v-html="detectionResult.highlighted_html"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="mt-8 pt-6 border-t border-gray-200">
          <!-- 当前步骤显示 -->
          <div v-if="currentStep" class="mb-4 p-3 bg-blue-50 rounded-lg">
            <div class="flex items-center text-blue-700">
              <span class="material-icons-outlined text-sm mr-2 animate-spin">refresh</span>
              <span class="text-sm font-medium">{{ currentStep }}</span>
            </div>
          </div>

          <div class="flex justify-between items-center">
            <!-- 左侧信息 -->
            <div class="text-sm text-gray-500 flex items-center">
              <span class="material-icons-outlined mr-1" style="font-size: 14px;">info</span>
              建议输入内容不超过20000字符以获得最佳效果
            </div>

            <!-- 右侧操作按钮 -->
            <div class="flex items-center space-x-4">
              <!-- 模型版本选择 -->
              <div class="text-sm text-gray-600">
                TunshuEdu降重模型 V1.0.0
              </div>

              <!-- 操作按钮 -->
              <div class="flex space-x-3">
                <button
                  @click="handleReduceAI"
                  :disabled="!isValidCharCount || isProcessing"
                  class="px-6 py-2 text-white rounded-lg text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  style="background-color: #4F46E5;"
                  onmouseover="this.style.backgroundColor='#3730A3'"
                  onmouseout="this.style.backgroundColor='#4F46E5'"
                >
                  <span v-if="isReducing" class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1 animate-spin">refresh</span>
                    {{ currentStep || '降AI率处理中...' }}
                  </span>
                  <span v-else>
                    一键降AI率
                  </span>
                </button>

                <button
                  @click="handleDetectAI"
                  :disabled="!isValidCharCount || isProcessing"
                  class="px-6 py-2 text-white rounded-lg text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  style="background-color: #4F46E5;"
                  onmouseover="this.style.backgroundColor='#3730A3'"
                  onmouseout="this.style.backgroundColor='#4F46E5'"
                >
                  <span v-if="isDetecting" class="flex items-center">
                    <span class="material-icons-outlined text-sm mr-1 animate-spin">refresh</span>
                    检测中...
                  </span>
                  <span v-else>检测AI率</span>
                </button>

                <!-- 清除缓存按钮 -->
                <button
                  v-if="hasValidCache"
                  @click="clearCache"
                  :disabled="isProcessing"
                  class="px-4 py-2 border border-gray-300 text-gray-600 hover:bg-gray-50 rounded-lg text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="清除缓存的检测结果"
                >
                  <span class="material-icons-outlined text-sm">clear</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>


  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { detectAIContent, humanizeAIContent } from '@/api/aidetection'
import { ElMessage } from 'element-plus'

// 响应式数据
const inputText = ref('')
const detectionResult = ref(null)
const reducedText = ref('')
const isDetecting = ref(false)
const isReducing = ref(false)

// 新增：缓存和对比相关数据
const cachedDetectionResult = ref(null)
const lastDetectedText = ref('')
const beforeAfterComparison = ref(null)
const currentStep = ref('')

// 计算属性
const isProcessing = computed(() => isDetecting.value || isReducing.value)

// 检查字符数量是否符合要求
const isValidCharCount = computed(() => {
  const length = inputText.value.trim().length
  return length >= 500 && length <= 20000
})

// 字符数量显示颜色
const getCharCountColor = (length) => {
  if (length < 500) return 'text-red-500'
  if (length > 20000) return 'text-red-500'
  if (length >= 500 && length <= 3000) return 'text-green-500'
  return 'text-yellow-500'
}

// 检查是否有有效的缓存检测结果
const hasValidCache = computed(() => {
  return cachedDetectionResult.value &&
         lastDetectedText.value === inputText.value.trim() &&
         inputText.value.trim().length > 0
})

// 监听输入文本变化，清除缓存
watch(inputText, (newText, oldText) => {
  if (newText.trim() !== oldText?.trim()) {
    clearCache()
  }
})

// 清除缓存
const clearCache = () => {
  cachedDetectionResult.value = null
  lastDetectedText.value = ''
  beforeAfterComparison.value = null
}

// AI率颜色判断
const getAiRateColor = (rate) => {
  if (rate >= 70) return 'text-red-500'
  if (rate >= 40) return 'text-yellow-500'
  return 'text-green-500'
}

// SVG仪表盘相关方法
const getArcPath = (rate) => {
  const angle = (rate / 100) * 180;
  const radian = (angle * Math.PI) / 180;
  const centerX = 125;
  const centerY = 140;
  const radius = 100;
  const x = centerX + radius * Math.cos(Math.PI - radian);
  const y = centerY - radius * Math.sin(Math.PI - radian);
  const largeArc = angle > 90 ? 1 : 0;
  return `M 25 140 A 100 100 0 ${largeArc} 1 ${x} ${y}`;
};

const getArcColor = (rate) => {
  if (rate >= 70) return '#ef4444' // 红色
  if (rate >= 40) return '#f59e0b' // 黄色
  return '#10b981' // 绿色
}



const getAiRateMessage = (rate) => {
  if (rate >= 70) return 'GPTZERO显示AI痕迹较明显，建议进行降AI率处理。'
  if (rate >= 40) return 'GPTZERO显示可能有AI痕迹。'
  return 'GPTZERO显示内容原创性较高。'
}

// 检测AI率
const handleDetectAI = async () => {
  const trimmedText = inputText.value.trim();
  
  if (!trimmedText) {
    ElMessage.warning('请输入要检测的文本内容');
    return;
  }
  
  if (trimmedText.length < 500) {
    ElMessage.warning('文本长度至少需要500个字符才能进行检测');
    return;
  }
  
  if (trimmedText.length > 20000) {
    ElMessage.warning('文本长度超过20000字符限制，请缩短后再试');
    return;
  }

  isDetecting.value = true;
  currentStep.value = '正在检测AI率...';

  try {
    // AI检测使用纯文本格式（保持原有逻辑）
    const response = await detectAIContent(inputText.value.trim(), 'text');
    if (response && response.result) {
      const result = response.result;
      const detectionData = {
        aiRate: Math.round(result.fake_percentage),
        highlighted_html: result.highlighted_html,
        highlighted_sentences: result.highlighted_sentences || [],
      };

      // 更新显示结果
      detectionResult.value = detectionData;

      // 更新缓存
      cachedDetectionResult.value = detectionData;
      lastDetectedText.value = inputText.value.trim();

      ElMessage.success(`检测完成！AI率: ${detectionData.aiRate}%`);
    } else {
      throw new Error('API响应格式异常');
    }
  } catch (error) {
    console.error('AI检测失败:', error);
    ElMessage.error('AI检测失败，请稍后重试');
  } finally {
    isDetecting.value = false;
    currentStep.value = '';
  }
};

// 智能降AI率处理
const handleReduceAI = async () => {
  const trimmedText = inputText.value.trim();
  
  if (!trimmedText) {
    ElMessage.warning('请输入要处理的文本内容');
    return;
  }
  
  if (trimmedText.length < 500) {
    ElMessage.warning('文本长度至少需要500个字符才能进行降AI率处理');
    return;
  }
  
  if (trimmedText.length > 20000) {
    ElMessage.warning('文本长度超过20000字符限制，请缩短后再试');
    return;
  }

  isReducing.value = true;
  let beforeDetection = null;
  let afterDetection = null;

  try {
    // 步骤1：获取降AI前的检测结果
    if (hasValidCache.value) {
      // 使用缓存的检测结果
      beforeDetection = cachedDetectionResult.value;
      currentStep.value = '使用已缓存的检测结果...';
    } else {
      // 执行新的检测
      currentStep.value = '正在检测原文AI率...';
      const response = await detectAIContent(inputText.value.trim(), 'text');
      if (response && response.result) {
        const result = response.result;
        beforeDetection = {
          aiRate: Math.round(result.fake_percentage),
          highlighted_html: result.highlighted_html,
          highlighted_sentences: result.highlighted_sentences || [],
        };

        // 更新缓存
        cachedDetectionResult.value = beforeDetection;
        lastDetectedText.value = inputText.value.trim();
      } else {
        throw new Error('原文AI检测失败');
      }
    }

    // 步骤2：执行降AI率处理
    currentStep.value = '正在执行降AI率处理...';
    const text = inputText.value.trim();
    const hasMarkdownSyntax = (
      text.includes('**') ||
      text.includes('###') ||
      text.includes('##') ||
      text.includes('# ') ||
      text.includes('- ') ||
      text.includes('* ') ||
      text.includes('1. ') ||
      text.includes('`') ||
      text.includes('> ') ||
      text.includes('[') && text.includes('](') ||
      text.includes('___') ||
      text.includes('---')
    );
    const format = hasMarkdownSyntax ? 'markdown' : 'text';

    const payload = {
      text: text,
      format: format,
      language: 'english',
    };
    if (beforeDetection?.highlighted_sentences?.length) {
      payload.highlighted_sentences = beforeDetection.highlighted_sentences;
    }

    const resp = await humanizeAIContent(payload);
    const humanizedText = resp?.humanized_text || '';
    if (!humanizedText) throw new Error('后端未返回改写文本');

    reducedText.value = humanizedText;

    // 步骤3：检测降AI后的文本
    currentStep.value = '正在检测降AI后文本的AI率...';
    const afterResponse = await detectAIContent(humanizedText, 'text');
    if (afterResponse && afterResponse.result) {
      const afterResult = afterResponse.result;
      afterDetection = {
        aiRate: Math.round(afterResult.fake_percentage),
        highlighted_html: afterResult.highlighted_html,
        highlighted_sentences: afterResult.highlighted_sentences || [],
      };
    } else {
      throw new Error('降AI后文本检测失败');
    }

    // 步骤4：设置前后对比数据
    beforeAfterComparison.value = {
      before: beforeDetection,
      after: afterDetection,
      originalText: text,
      humanizedText: humanizedText,
      improvement: beforeDetection.aiRate - afterDetection.aiRate
    };

    // 清除单独的检测结果，显示对比结果
    detectionResult.value = null;

    const improvementText = beforeAfterComparison.value.improvement > 0
      ? `AI率降低了${beforeAfterComparison.value.improvement}%`
      : '未能有效降低AI率';

    ElMessage.success(`降AI率处理完成！${improvementText} ${format === 'markdown' ? '(已保持Markdown格式)' : ''}`);

  } catch (error) {
    console.error('降AI率失败:', error);
    ElMessage.error(`处理失败：${error.message || '请稍后重试'}`);
  } finally {
    isReducing.value = false;
    currentStep.value = '';
  }
};

// 复制结果
const handleCopyResult = async () => {
  try {
    const textToCopy = beforeAfterComparison.value
      ? beforeAfterComparison.value.humanizedText
      : reducedText.value;

    await navigator.clipboard.writeText(textToCopy);
    ElMessage.success('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败，请手动复制');
  }
}

// 使用降AI后的文本替换输入框内容
const handleUseReducedText = () => {
  if (beforeAfterComparison.value?.humanizedText) {
    inputText.value = beforeAfterComparison.value.humanizedText;
    // 清除对比结果和缓存，因为输入内容已改变
    beforeAfterComparison.value = null;
    clearCache();
    ElMessage.success('已将降AI后文本填入输入框');
  }
}
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 浮窗动画 */
.fixed.inset-0 {
  animation: fadeIn 0.3s ease-out;
}

.fixed.inset-0 > div {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0; 
    transform: scale(0.9) translateY(-20px); 
  }
  to { 
    opacity: 1; 
    transform: scale(1) translateY(0); 
  }
}

/* 新增高亮样式 */
:deep(.ai-highlight) {
  background-color: #fef08a; /* 黄色高亮 */
  padding: 0.1em 0.2em;
  border-radius: 3px;
}
</style> 