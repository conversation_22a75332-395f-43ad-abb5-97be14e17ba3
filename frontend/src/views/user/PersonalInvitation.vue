<template>
  <div class="max-w-5xl mx-auto p-6">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900">邀请好友</h2>
      <p class="text-gray-500 mt-1">邀请好友注册，双方获得3天试用套餐</p>
    </div>

    <!-- 邀请统计看板 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 mb-8">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">邀请数据</h3>
        
        <div v-if="statsLoading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>

        <div v-else class="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="text-3xl font-bold text-primary mb-1">{{ statsData?.total_invitations || 0 }}</div>
            <div class="text-sm text-gray-600">总邀请数</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600 mb-1">{{ statsData?.successful_invitations || 0 }}</div>
            <div class="text-sm text-gray-600">成功邀请</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-orange-600 mb-1">{{ statsData?.pending_rewards || 0 }}</div>
            <div class="text-sm text-gray-600">待发放奖励</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600 mb-1">{{ statsData?.total_rewards || 0 }}</div>
            <div class="text-sm text-gray-600">已获得奖励</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 邀请码卡片 -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 mb-8">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">我的邀请码</h3>
        
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>

        <div v-else class="space-y-6">
          <!-- 邀请码 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">邀请码</label>
            <div class="flex items-center space-x-3">
              <div class="flex-1 px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg font-mono text-lg tracking-wider text-center font-semibold">
                {{ invitationData?.invitation_code || '加载中...' }}
              </div>
              <button
                @click="handleCopyCode"
                class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors font-medium"
              >
                复制
              </button>
            </div>
          </div>

          <!-- 邀请链接 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">邀请链接</label>
            <div class="flex items-center space-x-3">
              <div class="flex-1 px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-sm break-all text-gray-700 text-center">
                {{ invitationData?.invite_link || '加载中...' }}
              </div>
              <button
                @click="handleCopyLink"
                class="px-6 py-3 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors font-medium"
              >
                复制
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 奖励说明 -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">奖励机制</h3>
      <div class="grid md:grid-cols-2 gap-4 text-sm text-gray-700">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
          好友注册成功，双方获得3天试用套餐
        </div>
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
          试用套餐包含20积分
        </div>
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
          邀请码永久有效，可重复使用
        </div>
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
          新奖励将延长现有套餐有效期
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getMyPersonalInvitationLink, getPersonalInvitationStats } from '@/api/invitation'

// 响应式数据
const loading = ref(true)
const statsLoading = ref(true)
const invitationData = ref(null)
const statsData = ref(null)

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    loadInvitationData(),
    loadStatsData()
  ])
})

// 加载邀请数据
const loadInvitationData = async () => {
  try {
    loading.value = true
    const response = await getMyPersonalInvitationLink()
    invitationData.value = response
  } catch (error) {
    console.error('获取邀请链接失败:', error)

    let message = '获取邀请链接失败'
    if (error.response?.status === 403) {
      message = '此功能只能在个人身份下使用，请切换到个人身份'
    } else if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatsData = async () => {
  try {
    statsLoading.value = true
    const response = await getPersonalInvitationStats()
    statsData.value = response
  } catch (error) {
    console.error('获取邀请统计失败:', error)

    let message = '获取邀请统计失败'
    if (error.response?.status === 403) {
      message = '此功能只能在个人身份下使用，请切换到个人身份'
    } else if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)
  } finally {
    statsLoading.value = false
  }
}

// 复制邀请码
const handleCopyCode = async () => {
  if (!invitationData.value?.invitation_code) return
  
  try {
    await navigator.clipboard.writeText(invitationData.value.invitation_code)
    ElMessage.success('邀请码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 复制邀请链接
const handleCopyLink = async () => {
  if (!invitationData.value?.invite_link) return
  
  try {
    await navigator.clipboard.writeText(invitationData.value.invite_link)
    ElMessage.success('邀请链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}
</script>

<style scoped>
/* Primary 颜色样式支持 */
.bg-primary {
  background-color: var(--primary, #4F46E5) !important;
}

.hover\\:bg-primary-dark:hover {
  background-color: var(--primary-dark, #4338CA) !important;
}

.text-primary {
  color: var(--primary, #4F46E5) !important;
}

.border-primary {
  border-color: var(--primary, #4F46E5) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid.grid-cols-2.md\\:grid-cols-4 {
    @apply grid-cols-1 gap-4;
  }

  .flex.space-x-3 {
    @apply flex-col space-x-0 space-y-3;
  }

  .grid.md\\:grid-cols-2 {
    @apply grid-cols-1;
  }
}
</style>
