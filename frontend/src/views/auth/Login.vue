<template>
  <div class="login-page min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex">
        <!-- 左侧图片区域 -->
    <div class="hidden lg:flex lg:flex-col lg:items-center lg:justify-center lg:w-2/5 relative overflow-hidden" style="background-color: #F0F0FF;">
      <!-- 背景装饰 -->
      <div class="absolute inset-0">
        <!-- 网格背景 -->
        <div class="absolute inset-0 tech-grid-light opacity-30"></div>
        
        <!-- 浮动几何元素 -->
        <div class="absolute top-1/4 left-1/4 w-24 h-24 tech-hexagon-light opacity-20 animate-float-slow"></div>
        <div class="absolute top-3/4 right-1/4 w-16 h-16 tech-circle-light opacity-15 animate-float-medium"></div>
        <div class="absolute bottom-1/4 left-1/3 w-12 h-12 tech-triangle-light opacity-25 animate-float-fast"></div>
        
        <!-- 光晕效果 -->
        <div class="absolute top-0 left-0 w-96 h-96 bg-gradient-radial from-indigo-200/20 to-transparent blur-3xl"></div>
        <div class="absolute bottom-0 right-0 w-96 h-96 bg-gradient-radial from-purple-200/15 to-transparent blur-3xl"></div>
      </div>
      
            <!-- 内容区域 -->
      <div class="relative z-10 flex flex-col items-center justify-center w-full h-full p-8 text-gray-800">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
          <img 
            src="@/assets/Logo_TunshuEdu_W.svg" 
            alt="TUNSHU EDU" 
            class="h-12 mx-auto mb-3"
          />
          <p class="text-xl text-gray-600">留学服务智能专家</p>
        </div>
        
        <!-- 主图 -->
        <div class="mb-8 flex justify-center">
          <img 
            src="@/assets/login.png" 
            alt="留学选校" 
            class="max-w-sm w-full h-auto object-contain filter drop-shadow-xl"
          />
        </div>
        
        <!-- 描述文字 -->
        <div class="text-center max-w-md">
          <p class="text-lg text-gray-600 leading-relaxed">
            智能分析学生背景，精准匹配全球院校，让留学申请更简单高效
          </p>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="w-full lg:w-3/5 flex items-center justify-center p-8">
      <div class="w-full max-w-md">
        <!-- 移动端Logo -->
        <div class="lg:hidden text-center mb-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-2">TUNSHU EDU</h1>
          <p class="text-gray-600">留学选校智能专家</p>
        </div>

        <!-- 登录卡片 -->
        <div class="bg-white rounded-2xl shadow-xl p-8 space-y-6 border border-gray-100">
          <!-- 登录界面 -->
          <div v-if="!showInviteInterface && !showIdentitySelection">
            <!-- 欢迎标题 -->
            <div class="text-center">
              <h2 class="text-2xl font-bold text-gray-800 mb-2">欢迎回来</h2>
            </div>



          <!-- 邀请加载状态 -->
          <div v-if="invitationLoading" class="mb-6">
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div class="flex items-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-3"></div>
                <span class="text-sm text-gray-600">正在验证邀请信息...</span>
              </div>
            </div>
          </div>

          <!-- 微信扫码登录 -->
          <div class="mb-6">
            <WeChatQRLogin
              @login-success="handleLoginSuccess"
            />
          </div>

          <!-- 开发环境登录选项 -->
          <div v-if="isDevelopment" class="mb-6">
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">开发环境</span>
              </div>
            </div>

            <!-- 开发环境登录模式切换 -->
            <div class="mt-6 mb-4">
              <div class="flex rounded-lg bg-gray-100 p-1">
                <button
                  @click="loginMode = 'dev'"
                  :class="[
                    'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-all',
                    loginMode === 'dev'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  ]"
                >
                  快速登录
                </button>
                <button
                  @click="loginMode = 'password'"
                  :class="[
                    'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-all',
                    loginMode === 'password'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  ]"
                >
                  账号密码
                </button>
              </div>
            </div>

            <!-- 快速登录按钮 -->
            <div v-if="loginMode === 'dev'" class="mt-4">
              <button
                @click="handleDevLogin"
                :disabled="devLoginLoading"
                class="w-full py-3 px-4 rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] active:translate-y-[0px] transition-all duration-200 font-medium bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="!devLoginLoading" class="flex items-center justify-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  开发登录
                </span>
                <span v-else class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  登录中...
                </span>
              </button>
              <p class="text-xs text-gray-500 text-center mt-2">
                使用开发账号快速登录（仅开发环境可见）
              </p>
            </div>

            <!-- 密码登录表单 -->
            <div v-else-if="loginMode === 'password'" class="mt-4">
              <form @submit.prevent="handlePasswordLogin" class="space-y-5" novalidate>
                <div class="space-y-4">
                  <div class="group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <div class="relative">
                      <input
                        v-model="form.username"
                        type="text"
                        placeholder="请输入用户名"
                        class="w-full px-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-indigo-600 focus:ring-2 focus:ring-indigo-600/20 transition-all duration-200 group-hover:bg-gray-100"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500/20': errorMessage }"
                        @input="errorMessage = ''"
                      />
                    </div>
                  </div>

                  <div class="group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <div class="relative">
                      <input
                        v-model="form.password"
                        type="password"
                        placeholder="请输入密码"
                        class="w-full px-4 py-3 rounded-xl bg-gray-50 border border-gray-200 focus:border-indigo-600 focus:ring-2 focus:ring-indigo-600/20 transition-all duration-200 group-hover:bg-gray-100"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500/20': errorMessage }"
                        @input="errorMessage = ''"
                      />
                    </div>
                    <transition name="error-fade">
                      <div v-if="errorMessage" class="mt-2 text-sm text-red-600 flex items-center">
                        <svg class="w-4 h-4 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                        {{ errorMessage }}
                      </div>
                    </transition>
                  </div>
                </div>

                <div class="flex items-start space-x-2 text-sm text-gray-600">
                  <input
                    id="agree-terms"
                    v-model="agreeTerms"
                    type="checkbox"
                    class="mt-0.5 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label for="agree-terms" class="flex-1 leading-relaxed">
                    我已阅读并同意
                    <a href="#" @click.prevent="showTermsModal = true" class="text-indigo-600 hover:text-indigo-700 underline">《服务条款》</a>
                    和
                    <a href="#" @click.prevent="showPrivacyModal = true" class="text-indigo-600 hover:text-indigo-700 underline">《隐私政策》</a>
                  </label>
                </div>

                <button
                  type="button"
                  @click="handlePasswordLogin"
                  class="w-full py-3 px-4 rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] active:translate-y-[0px] transition-all duration-200 font-medium"
                  :class="agreeTerms ? 'button-gradient text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'"
                  :disabled="loading || (!form.username || !form.password) || !agreeTerms"
                >
                  <span v-if="!loading">密码登录</span>
                  <span v-else class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    登录中...
                  </span>
                </button>
              </form>
              <p class="text-xs text-gray-500 text-center mt-2">
                使用账号密码登录（仅开发环境可见）
              </p>
            </div>
          </div>

          <!-- 邀请信息显示 -->
          <div v-if="hasInvitation && invitationInfo" class="mt-6">
            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-100 rounded-xl px-4 py-3 shadow-sm">
              <div class="flex items-center justify-center text-center">
                <span class="material-icons-outlined text-indigo-600 mr-2 flex-shrink-0 text-base">mail</span>
                <span class="text-sm text-gray-700">
                  您被邀请加入组织 <span class="font-semibold text-indigo-700">{{ invitationInfo.organization_name }}</span>，登录后将自动加入
                </span>
              </div>
            </div>
          </div>

          <!-- 个人邀请信息显示 -->
          <div v-if="hasPersonalInvite && personalInviteInfo" class="mt-6">
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100 rounded-xl px-4 py-3 shadow-sm">
              <div class="flex items-center justify-center text-center">
                <span class="material-icons-outlined text-green-600 mr-2 flex-shrink-0 text-base">card_giftcard</span>
                <span class="text-sm text-gray-700">
                  通过邀请码注册可获得 <span class="font-semibold text-green-700">{{ personalInviteInfo.trial_days || 3 }} 天试用套餐</span>
                </span>
              </div>
            </div>
          </div>
          </div>

          <!-- 邀请界面 -->
          <div v-else-if="showInviteInterface">
            <!-- 加载状态 -->
            <div v-if="invitationLoading" class="text-center py-8">
              <div class="flex items-center justify-center mb-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
              </div>
              <p class="text-gray-600">正在验证邀请信息...</p>
            </div>

            <!-- 邀请详情 -->
            <div v-else-if="invitationInfo && !inviteError">
              <!-- 头部 -->
              <div class="text-center mb-6">
                <div class="mx-auto w-16 h-16 bg-gradient-to-br from-indigo-100 to-blue-100 rounded-2xl flex items-center justify-center mb-4">
                  <span class="material-icons-outlined text-indigo-600 text-3xl">group_add</span>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                  邀请您加入 {{ invitationInfo.organization_name }}
                </h2>
                <p class="text-gray-600">
                  {{ invitationInfo.invited_by_organization_username || invitationInfo.invited_by_nickname || invitationInfo.invited_by_username }} 邀请您加入团队
                </p>
              </div>

              <!-- 邀请信息卡片 -->
              <div class="bg-gradient-to-r from-gray-50 to-indigo-50 rounded-2xl p-5 mb-6 border border-gray-200">
                <div class="space-y-3">
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">组织名称</span>
                    <span class="text-sm font-semibold text-gray-900">{{ invitationInfo.organization_name }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">邀请人</span>
                    <span class="text-sm font-semibold text-gray-900">{{ invitationInfo.invited_by_organization_username || invitationInfo.invited_by_nickname || invitationInfo.invited_by_username }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">预设用户名</span>
                    <span class="text-sm font-semibold text-gray-900">{{ invitationInfo.organization_username }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">有效期至</span>
                    <span class="text-sm font-semibold text-gray-900">{{ formatInviteDate(invitationInfo.expires_at) }}</span>
                  </div>
                </div>
              </div>

              <!-- 用户名输入 -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  组织内显示名称
                </label>
                <el-input
                  v-model="organizationUsername"
                  placeholder="请输入您的真名或在公司中的名称"
                  size="large"
                  class="w-full"
                  :disabled="acceptingInvite"
                />
                <p class="text-xs text-gray-500 mt-2">
                  此名称将作为您在组织内的显示名称，建议使用真实姓名或工作中常用的称呼
                </p>
              </div>

              <!-- 操作按钮 -->
              <div class="space-y-3">
                <button
                  @click="handleAcceptInvitation"
                  :disabled="acceptingInvite || !organizationUsername.trim()"
                  class="w-full py-4 px-6 rounded-2xl font-semibold text-lg transition-all duration-300 transform text-white shadow-lg hover:shadow-xl hover:-translate-y-1 active:translate-y-0 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  style="background-color: #4F46E5;"
                >
                  <span v-if="!acceptingInvite" class="flex items-center justify-center">
                    <span>加入组织</span>
                    <span class="material-icons-outlined ml-2 text-xl">arrow_forward</span>
                  </span>
                                     <span v-else class="flex items-center justify-center">
                     <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                       <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                       <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                     </svg>
                     处理中...
                   </span>
                </button>
                
                <button
                  @click="handleDeclineInvitation"
                  class="w-full py-3 px-4 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-2xl transition-all duration-300"
                >
                  拒绝邀请
                </button>
              </div>

              <!-- 说明文字 -->
              <p class="text-xs text-gray-500 text-center mt-4">
                点击"加入组织"将自动完成身份验证并加入团队
              </p>
            </div>

            <!-- 错误状态 -->
            <div v-else class="text-center py-8">
              <div class="mx-auto w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center mb-4">
                <span class="material-icons-outlined text-red-600 text-3xl">error</span>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-2">邀请无效</h3>
              <p class="text-gray-600 mb-6">{{ inviteError }}</p>
              <button
                @click="backToLogin"
                class="px-6 py-3 rounded-2xl font-medium transition-all duration-300 text-white"
                style="background-color: #4F46E5;"
              >
                返回登录
              </button>
            </div>
          </div>

          <!-- 身份选择界面 -->
          <div v-else-if="showIdentitySelection">
            <!-- 标题区域 -->
            <div class="text-center mb-6">
              <h2 class="text-2xl font-bold text-gray-900 mb-2">选择账号类型</h2>
              <p class="text-gray-600 text-sm">
                检测到您的账号关联了组织，请选择要使用的账号类型
              </p>
            </div>

            <!-- 身份选择卡片 -->
            <div class="space-y-3">
              <!-- 个人账号选项 -->
              <div 
                @click="handleSelectIdentity('personal')"
                :class="[
                  'group relative bg-white rounded-2xl p-5 border-2 cursor-pointer transition-all duration-300',
                  'hover:shadow-lg hover:border-indigo-300 hover:-translate-y-0.5',
                  selectedIdentity === 'personal' 
                    ? 'border-indigo-500 bg-gradient-to-r from-indigo-50 to-blue-50 shadow-md' 
                    : 'border-gray-200'
                ]"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div :class="[
                      'w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300',
                      selectedIdentity === 'personal' 
                        ? 'bg-indigo-500 shadow-lg shadow-indigo-200' 
                        : 'bg-gray-400 group-hover:bg-indigo-400'
                    ]">
                      <span class="material-icons-outlined text-white text-lg">person</span>
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-900 mb-1">个人账号</h3>
                      <p class="text-sm text-gray-600">
                        独立使用，创建和管理组织
                      </p>
                    </div>
                  </div>
                  <div class="flex-shrink-0">
                    <div v-if="selectedIdentity === 'personal'" 
                         class="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                      <span class="material-icons-outlined text-white text-sm">check</span>
                    </div>
                    <div v-else class="w-6 h-6 border-2 border-gray-300 rounded-full group-hover:border-indigo-400 transition-colors duration-300"></div>
                  </div>
                </div>
              </div>

              <!-- 组织账号选项 -->
              <div 
                v-for="org in availableOrganizations" 
                :key="org.id"
                @click="handleSelectIdentity('organization', org)"
                :class="[
                  'group relative bg-white rounded-2xl p-5 border-2 cursor-pointer transition-all duration-300',
                  'hover:shadow-lg hover:border-indigo-300 hover:-translate-y-0.5',
                  selectedIdentity === 'organization' && selectedOrganization?.id === org.id
                    ? 'border-indigo-500 bg-gradient-to-r from-indigo-50 to-blue-50 shadow-md' 
                    : 'border-gray-200'
                ]"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div :class="[
                      'w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300',
                      selectedIdentity === 'organization' && selectedOrganization?.id === org.id
                        ? 'bg-indigo-500 shadow-lg shadow-indigo-200' 
                        : 'bg-gray-400 group-hover:bg-indigo-400'
                    ]">
                      <span class="material-icons-outlined text-white text-lg">business</span>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center gap-3 mb-1">
                        <h3 class="text-lg font-semibold text-gray-900 truncate">{{ org.name }}</h3>
                        <span :class="[
                          'px-2.5 py-1 text-xs font-medium rounded-full flex-shrink-0 border',
                          org.role === 'owner' 
                            ? 'bg-amber-100 text-amber-700 border-amber-200' 
                            : 'bg-blue-100 text-blue-700 border-blue-200'
                        ]">
                          {{ org.role === 'owner' ? '管理员' : '成员' }}
                        </span>
                      </div>
                      <p class="text-sm text-gray-600">
                        组织成员身份，享受团队协作功能
                      </p>
                    </div>
                  </div>
                  <div class="flex-shrink-0">
                    <div v-if="selectedIdentity === 'organization' && selectedOrganization?.id === org.id" 
                         class="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                      <span class="material-icons-outlined text-white text-sm">check</span>
                    </div>
                    <div v-else class="w-6 h-6 border-2 border-gray-300 rounded-full group-hover:border-indigo-400 transition-colors duration-300"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 确认按钮 -->
            <div class="mt-8">
              <button
                @click="handleConfirmIdentity"
                :disabled="!selectedIdentity || identityLoading"
                class="w-full py-4 px-6 rounded-2xl font-semibold text-lg transition-all duration-300 transform"
                :class="selectedIdentity && !identityLoading 
                  ? 'text-white shadow-lg hover:shadow-xl hover:-translate-y-1 active:translate-y-0' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'"
                :style="selectedIdentity && !identityLoading ? 'background-color: #4F46E5;' : ''"
              >
                <span v-if="!identityLoading">确认选择</span>
                <span v-else class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  处理中...
                </span>
              </button>
            </div>

            <!-- 返回登录按钮 -->
            <div class="mt-4 text-center">
              <button
                @click="backToLogin"
                class="text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                ← 返回登录
              </button>
            </div>
          </div>
          
          <!-- 调试信息 -->
          <div v-if="showDebugInfo" class="mt-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
            <h3 class="text-sm font-medium text-gray-700 mb-2">调试信息</h3>
            <div class="text-xs text-gray-600 space-y-1">
              <p>API URL: {{ apiUrl }}</p>
              <p>Token状态: {{ authStore.token ? '已存在' : '不存在' }}</p>
              <p>错误信息: {{ errorMessage || '无' }}</p>
            </div>
            <div class="mt-2">
              <button 
                @click="clearAuthData" 
                class="text-xs text-red-600 hover:text-red-700 transition-colors"
              >
                清除认证数据
              </button>
            </div>
          </div>
        </div>
        
        <!-- 微信登录说明 -->
        <div class="mt-6 text-center">
          <p class="text-sm text-gray-600">
            使用微信扫码即可登录或注册
          </p>
        </div>

        <!-- 版权信息 -->
        <div class="mt-4 text-center text-xs text-gray-500">
          <span>© 2025 囤鼠科技教育平台</span>
        </div>
      </div>
    </div>

    <!-- 服务条款模态框 -->
    <transition name="modal">
      <div v-if="showTermsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <transition name="modal-content">
          <div v-if="showTermsModal" class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">服务条款</h2>
              <button
                @click="showTermsModal = false"
                class="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6 overflow-y-auto max-h-[60vh]">
              <div class="space-y-4 text-sm text-gray-700 leading-relaxed">
                <h4 class="text-base font-medium text-gray-800 mb-3">用户协议</h4>
                <p class="mb-4">欢迎签署本《用户协议》（以下简称"本协议"）并使用TunshuEdu智能留学选校平台（定义见下文）服务。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">相关定义</h4>
                <ul class="space-y-2 ml-4">
                  <li><strong>TunshuEdu智能留学选校平台：</strong> 指 囤鼠科技（四川）有限公司（以下简称"囤鼠科技"）运营的智能留学选校SAAS产品。</li>
                  <li><strong>TunshuEdu服务提供者：</strong> 指提供TunshuEdu智能留学选校平台互联网信息及软件技术服务的 囤鼠科技（四川）有限公司。</li>
                  <li><strong>用户或您：</strong> 指使用TunshuEdu智能留学选校平台产品或服务的个人。</li>
                </ul>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">协议适用范围</h4>
                <p class="mb-4">本协议由您和囤鼠科技签署，自您确认接受之日起或自您使用TunshuEdu智能留学选校平台之发生之时起生效。除非另有明确规定，平台新增的任何功能、新产品、新服务，均无条件地适用本协议。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">账户与使用说明</h4>
                <p class="mb-4">在您使用TunshuEdu智能留学选校平台服务时，需要您先行进行用户注册和实名认证。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">用户资格说明</h4>
                <p class="mb-4">请确认在您开始注册使用TunshuEdu智能留学选校平台服务前，您已具备中华人民共和国法律法规规定的与您行为相适应的民事行为能力。若您不具备前述与您行为相适应的民事行为能力而进行用户注册，则您及您的监护人应依照法律规定承担因此而导致的一切后果。</p>
              </div>
            </div>

            <!-- 模态框底部 -->
            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                @click="handleAcceptTerms"
                class="px-6 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 transition-colors"
              >
                我已阅读
              </button>
            </div>
          </div>
        </transition>
      </div>
    </transition>

    <!-- 隐私政策模态框 -->
    <transition name="modal">
      <div v-if="showPrivacyModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <transition name="modal-content">
          <div v-if="showPrivacyModal" class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">隐私政策</h2>
              <button
                @click="showPrivacyModal = false"
                class="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6 overflow-y-auto max-h-[60vh]">
              <div class="space-y-4 text-sm text-gray-700 leading-relaxed">
                <p class="text-gray-600 mb-4">协议发布及生效日期：2025年7月15日起</p>

                <p class="mb-4">以下协议详细介绍了 囤鼠科技（四川）有限公司（以下简称"我们"）会如何通过TunshuEdu智能留学选校平台（以下简称"本产品"）收集、使用、共享、存储和保护您的个人信息，以及您对此所拥有的权利。在您开始使用我们的产品和服务前，请务必仔细先行阅读该隐私政策并理解本政策，请您充分理解和同意后再开始使用。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">用户信息收集</h4>
                <p class="mb-4">为了确保能够向您提供服务，我们仅会收集对于下列服务的个人信息：</p>

                <ul class="list-disc list-inside space-y-2 ml-4">
                  <li><strong>账户信息：</strong> 当您申请使用平台时，需要向我们提供的手机号码及设置的密码等信息，为了确保您能够成功访问您的服务，我们将记录此信息。</li>
                  <li><strong>对话信息：</strong> 为了确保服务的质量和稳定性，在您使用平台服务时，我们将收集您在产品交互过程中输入的内容本身相关信息。</li>
                  <li><strong>反馈信息：</strong> 为了确保服务的质量和稳定性，在您使用平台服务时，我们将收集您对生成的内容和反馈信息。</li>
                  <li><strong>投诉建议信息：</strong> 为了更好的处理您的投诉和建议，我们将需要您在提交投诉建议时所提供的相关信息。</li>
                  <li><strong>设备信息及个人用户标识：</strong> 当您使用我们的服务时，可能会涉及到第三方 SDK 的使用，这些 SDK 可能会请求获取您的设备权限，以便在不同的手机设备配置三方平台上登录和保护功能。</li>
                </ul>
              </div>
            </div>

            <!-- 模态框底部 -->
            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                @click="handleAcceptPrivacy"
                class="px-6 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 transition-colors"
              >
                我已阅读
              </button>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { clearAuth } from '@/utils/auth'
import WeChatQRLogin from '@/components/auth/WeChatQRLogin.vue'
import { getInvitationByToken, acceptInvitationByToken, getUserInvitationDetail, joinOrganization, validatePersonalInvitationCode, processPersonalInvitationReward } from '@/api/invitation'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const loading = ref(false)
const errorMessage = ref('')
const showDebugInfo = ref(false)
const apiUrl = ref(import.meta.env.VITE_API_URL || '未设置')

// 身份选择相关状态
const showIdentitySelection = ref(false)
const selectedIdentity = ref('')
const selectedOrganization = ref(null)
const availableOrganizations = ref([])
const identityLoading = ref(false)

// 开发环境检测
const isDevelopment = ref(import.meta.env.VITE_DEV_MODE === 'true' || import.meta.env.DEV)

// 开发登录状态
const devLoginLoading = ref(false)

// 登录模式：开发环境默认为快速登录
const loginMode = ref('dev')

const form = reactive({
  username: '',
  password: ''
})

// 服务条款同意
const agreeTerms = ref(false)

// 服务条款模态框
const showTermsModal = ref(false)

// 隐私政策模态框
const showPrivacyModal = ref(false)

// 邀请相关状态
const invitationInfo = ref(null)
const hasInvitation = ref(false)
const invitationLoading = ref(false)
const showInviteInterface = ref(false)
const acceptingInvite = ref(false)
const inviteError = ref('')
const organizationUsername = ref('')

// 个人邀请相关状态
const personalInviteCode = ref('')
const personalInviteInfo = ref(null)
const hasPersonalInvite = ref(false)

// 本地存储工具 - 统一跨环境处理邀请参数在微信回调跳转时丢失的问题
const savePendingOrgJoin = (data) => {
  try {
    localStorage.setItem('pending_org_join', JSON.stringify(data))
  } catch (_) {}
}

const getPendingOrgJoin = () => {
  try {
    const raw = localStorage.getItem('pending_org_join')
    return raw ? JSON.parse(raw) : null
  } catch (_) { return null }
}

const clearPendingOrgJoin = () => {
  try { localStorage.removeItem('pending_org_join') } catch (_) {}
}

const savePendingPersonalInvite = (code) => {
  try { localStorage.setItem('pending_personal_invite', String(code || '')) } catch (_) {}
}

const getPendingPersonalInvite = () => {
  try { return localStorage.getItem('pending_personal_invite') || '' } catch (_) { return '' }
}

const clearPendingPersonalInvite = () => {
  try { localStorage.removeItem('pending_personal_invite') } catch (_) {}
}

// 处理服务条款接受
const handleAcceptTerms = () => {
  agreeTerms.value = true
  showTermsModal.value = false
}

// 处理隐私政策接受
const handleAcceptPrivacy = () => {
  agreeTerms.value = true
  showPrivacyModal.value = false
}

// 清除认证数据
const clearAuthData = () => {
  clearAuth()
  ElMessage({
    message: '认证数据已清除',
    type: 'success'
  })
  // 重新载入页面以清除所有状态
  setTimeout(() => {
    location.reload()
  }, 1000)
}

onMounted(async () => {
  // 在控制台打印环境信息
  console.log('环境信息:')
  console.log('- API URL:', apiUrl.value)
  console.log('- Token:', authStore.token ? '存在' : '不存在')
  console.log('- 用户:', authStore.user ? '已登录' : '未登录')

  // 检查是否是直接邀请链接
  if (route.query.token || route.query.invite_token || (route.query.code && route.query.org)) {
    console.log('检测到组织邀请链接')
    showInviteInterface.value = true
    invitationLoading.value = true
    await fetchInvitationDetails()
    return
  }

  // 检查个人邀请参数
  if (route.query.invite) {
    console.log('检测到个人邀请码:', route.query.invite)
    hasInvitation.value = true
    invitationLoading.value = true
    // 持久化个人邀请码，避免微信回调重定向后参数丢失
    savePendingPersonalInvite(route.query.invite)
    await validatePersonalInvite()
  }

  // 检查邀请参数（登录后的邀请处理）
  await checkInvitationParams()

  // 微信回调处理交给 WeChatQRLogin 组件处理，避免重复处理
})

// 检查邀请参数
const checkInvitationParams = async () => {
  const inviteToken = route.query.invite_token
  const code = route.query.code
  const org = route.query.org

  // 支持旧的invite_token参数（向后兼容）
  if (inviteToken) {
    console.log('检测到旧版邀请参数:', { inviteToken })
    hasInvitation.value = true
    invitationLoading.value = true

    try {
      // 获取邀请详情
      const response = await getInvitationByToken(inviteToken)
      invitationInfo.value = response
      console.log('邀请信息:', invitationInfo.value)

      // 显示邀请提示
      ElMessage({
        message: `您被邀请加入组织：${invitationInfo.value.organization_name}`,
        type: 'info',
        duration: 5000
      })
    } catch (error) {
      console.error('获取邀请信息失败:', error)

      let message = '邀请链接无效或已过期'
      if (error.response?.data?.detail) {
        message = error.response.data.detail
      }

      ElMessage({
        message,
        type: 'warning',
        duration: 5000
      })
    } finally {
      invitationLoading.value = false
    }
  }
  // 支持新的code和org参数
  else if (code && org) {
    console.log('检测到新版邀请参数:', { code, org })
    hasInvitation.value = true
    invitationLoading.value = true

    try {
      // 获取邀请详情
      const response = await getUserInvitationDetail(code, parseInt(org))
      invitationInfo.value = response
      console.log('邀请信息:', invitationInfo.value)

      // 显示邀请提示
      ElMessage({
        message: `您被邀请加入组织：${invitationInfo.value.organization_name}`,
        type: 'info',
        duration: 5000
      })
    } catch (error) {
      console.error('获取邀请信息失败:', error)

      let message = '邀请链接无效或已过期'
      if (error.response?.data?.detail) {
        message = error.response.data.detail
      }

      ElMessage({
        message,
        type: 'warning',
        duration: 5000
      })
    } finally {
      invitationLoading.value = false
    }
  }
}

// 清除错误信息的方法
const clearError = () => {
  errorMessage.value = ''
}

// 处理登录成功
const handleLoginSuccess = async (userData) => {
  console.log('登录成功，准备跳转', userData)

  // 优先读取本地暂存的组织邀请（解决生产环境微信回调导致URL参数丢失的问题）
  const pendingOrg = getPendingOrgJoin()
  // 如果有邀请，先处理邀请接受（优先使用本地暂存，其次使用URL参数）
  if (pendingOrg || hasInvitation.value) {
    try {
      console.log('处理邀请接受...')
      let response

      if (pendingOrg) {
        // 本地暂存支持两种格式：旧版 invite_token 或 新版 code+org+username
        if (pendingOrg.invite_token) {
          response = await acceptInvitationByToken(pendingOrg.invite_token)
        } else if (pendingOrg.invitation_code && pendingOrg.organization_id && pendingOrg.organization_username) {
          response = await joinOrganization({
            invitation_code: pendingOrg.invitation_code,
            organization_id: Number(pendingOrg.organization_id),
            organization_username: pendingOrg.organization_username
          })
        }
      } else {
        // 回退到URL参数（开发环境或未暂存的场景）
        if (route.query.invite_token) {
          response = await acceptInvitationByToken(route.query.invite_token)
        } else if (route.query.code && route.query.org && route.query.username) {
          response = await joinOrganization({
            invitation_code: route.query.code,
            organization_id: parseInt(route.query.org),
            organization_username: decodeURIComponent(route.query.username)
          })
        }
      }

      if (response) {
        ElMessage({
          message: `成功加入组织：${response.organization_name}`,
          type: 'success',
          duration: 5000
        })

        // 清除暂存
        clearPendingOrgJoin()

        // 清除URL中的邀请参数
        const newQuery = { ...route.query }
        delete newQuery.invite_token
        delete newQuery.code
        delete newQuery.org
        delete newQuery.username

        // 跳转到组织页面或dashboard
        const redirectPath = '/dashboard'
        await router.replace({ path: redirectPath, query: newQuery })
        return
      }

    } catch (error) {
      console.error('接受邀请失败:', error)

      let message = '加入组织失败'
      if (error.response?.data?.detail) {
        message = error.response.data.detail
      }

      ElMessage({
        message,
        type: 'error',
        duration: 5000
      })

      // 即使邀请失败，也继续正常登录流程
    }
  }

  // 处理个人邀请奖励
  const pendingPersonal = getPendingPersonalInvite()
  if ((hasPersonalInvite.value && personalInviteCode.value) || pendingPersonal) {
    try {
      console.log('处理个人邀请奖励...')
      
      // 检查是否是新注册用户（优先使用后端返回的 is_new_user，其次回退到时间相等判定）
      const isNewUser = userData?.is_new_user ?? (
        userData?.login_type === 'wechat' &&
        userData?.created_at && userData?.last_login &&
        new Date(userData.created_at).getTime() === new Date(userData.last_login).getTime()
      )

      if (isNewUser) {
        console.log('检测到新用户通过微信注册，处理邀请奖励')
        // 对于新用户，调用个人邀请奖励API
        const response = await processPersonalInvitationReward(pendingPersonal || personalInviteCode.value)
        
        ElMessage({
          message: response.message || `欢迎使用！您已通过邀请码获得 ${personalInviteInfo.value?.trial_days || 3} 天试用套餐`,
          type: 'success',
          duration: 5000
        })
      } else {
        console.log('现有用户登录，跳过邀请奖励处理')
        ElMessage({
          message: '欢迎回来！',
          type: 'success',
          duration: 3000
        })
      }

      // 清除URL中的个人邀请参数
      const newQuery = { ...route.query }
      delete newQuery.invite

      // 清除暂存
      clearPendingPersonalInvite()

      // 跳转时保留清理后的参数
      const redirectPath = '/dashboard'
      await router.replace({ path: redirectPath, query: newQuery })
      return

    } catch (error) {
      console.error('处理个人邀请奖励失败:', error)
      
      // 显示失败消息，但继续正常登录流程
      ElMessage({
        message: '邀请奖励处理失败，但登录成功',
        type: 'warning',
        duration: 3000
      })
    }
  }

  // 检查用户是否有组织关联，决定是否需要身份选择
  try {
    console.log('检查用户身份信息...')
    const identities = await authStore.fetchUserIdentities()
    
    // 过滤出组织身份
    const organizationIdentities = identities.available_identities.filter(
      identity => identity.identity_type === 'organization'
    )

    // 如果用户有组织关联，且没有明确的重定向路径，显示身份选择界面
    if (organizationIdentities.length > 0 && !route.query.redirect) {
      console.log('用户有组织关联，显示身份选择界面')
      availableOrganizations.value = organizationIdentities.map(identity => ({
        id: identity.organization_id,
        name: identity.organization_name,
        role: identity.organization_role
      }))
      showIdentitySelection.value = true
      return
    }

  } catch (error) {
    console.error('获取用户身份信息失败:', error)
    // 如果获取身份信息失败，继续正常流程
  }

  // 获取重定向URL（如果存在）
  const redirectPath = route.query.redirect || '/dashboard'
  console.log('跳转到:', redirectPath)
  await router.push(redirectPath)
}

// 身份选择相关方法
const handleSelectIdentity = (identityType, organization = null) => {
  selectedIdentity.value = identityType
  selectedOrganization.value = organization
}

const handleConfirmIdentity = async () => {
  if (!selectedIdentity.value) {
    ElMessage.warning('请选择账号类型')
    return
  }

  identityLoading.value = true
  
  try {
    const identityData = {
      identity_type: selectedIdentity.value
    }

    // 如果选择组织身份，添加组织ID
    if (selectedIdentity.value === 'organization' && selectedOrganization.value) {
      identityData.organization_id = selectedOrganization.value.id
    }

    // 切换身份
    await authStore.switchUserIdentity(identityData)
    
    // 获取重定向URL（如果存在）
    const redirectPath = route.query.redirect || '/dashboard'
    console.log('跳转到:', redirectPath)
    await router.push(redirectPath)

  } catch (error) {
    console.error('切换身份失败:', error)
    
    let message = '切换身份失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }
    
    ElMessage.error(message)
  } finally {
    identityLoading.value = false
  }
}

const backToLogin = () => {
  showIdentitySelection.value = false
  showInviteInterface.value = false
  selectedIdentity.value = ''
  selectedOrganization.value = null
  availableOrganizations.value = []
  invitationInfo.value = null
  inviteError.value = ''
}

// 邀请相关方法
const formatInviteDate = (dateString) => {
  if (!dateString) return '未设置'
  
  const date = new Date(dateString)
  if (isNaN(date.getTime())) {
    return '无效日期'
  }
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleAcceptInvitation = async () => {
  // 验证用户名
  if (!organizationUsername.value.trim()) {
    ElMessage.warning('请输入您的组织内显示名称')
    return
  }

  acceptingInvite.value = true

  try {
    // 获取邀请参数
    const inviteToken = route.query.token || route.query.invite_token
    const code = route.query.code
    const org = route.query.org
    const username = encodeURIComponent(organizationUsername.value.trim())

    // 暂存邀请加入数据，避免微信回调跳转导致参数丢失
    if (code && org) {
      savePendingOrgJoin({
        invitation_code: code,
        organization_id: Number(org),
        organization_username: decodeURIComponent(username)
      })
    } else if (inviteToken) {
      savePendingOrgJoin({ invite_token: inviteToken, organization_username: decodeURIComponent(username) })
    }

    // 切换到登录界面，并设置邀请信息用于后续登录处理
    showInviteInterface.value = false
    hasInvitation.value = true

    // 根据参数格式更新URL，并包含用户名
    if (code && org) {
      // 新格式：使用code和org参数
      await router.replace(`/login?code=${code}&org=${org}&username=${username}`)
    } else if (inviteToken) {
      // 旧格式：使用invite_token参数
      await router.replace(`/login?invite_token=${inviteToken}&username=${username}`)
    }

    ElMessage({
      message: '请完成登录以加入组织',
      type: 'info',
      duration: 3000
    })

  } catch (error) {
    console.error('处理邀请接受失败:', error)
    ElMessage.error('处理失败，请重试')
  } finally {
    acceptingInvite.value = false
  }
}

const handleDeclineInvitation = () => {
  ElMessage({
    message: '您已拒绝加入该组织',
    type: 'info'
  })
  
  // 返回登录界面
  backToLogin()
}

const fetchInvitationDetails = async () => {
  const token = route.query.token || route.query.invite_token
  const code = route.query.code
  const org = route.query.org

  try {
    let response

    // 处理新格式的邀请参数 (code + org)
    if (code && org) {
      console.log('使用新格式邀请参数:', { code, org })
      response = await getUserInvitationDetail(code, parseInt(org))
    }
    // 处理旧格式的邀请参数 (token)
    else if (token) {
      console.log('使用旧格式邀请参数:', { token })
      response = await getInvitationByToken(token)
    }
    else {
      inviteError.value = '邀请链接无效，缺少必要参数'
      invitationLoading.value = false
      return
    }

    invitationInfo.value = response

    // 检查邀请是否有效
    if (response.is_valid === false) {
      inviteError.value = '邀请已过期或已失效'
    }
  } catch (err) {
    console.error('获取邀请信息失败:', err)

    let message = '邀请链接无效或已过期'
    if (err.response?.data?.detail) {
      message = err.response.data.detail
    }
    inviteError.value = message
  } finally {
    invitationLoading.value = false
  }
}

// 验证个人邀请码
const validatePersonalInvite = async () => {
  const inviteCode = route.query.invite
  if (!inviteCode) {
    invitationLoading.value = false
    return
  }

  personalInviteCode.value = inviteCode

  try {
    console.log('验证个人邀请码:', inviteCode)
    const response = await validatePersonalInvitationCode(inviteCode)
    
    if (response.is_valid) {
      hasPersonalInvite.value = true
      personalInviteInfo.value = response
      
      ElMessage({
        message: `通过邀请码注册可获得 ${response.trial_days || 3} 天试用套餐`,
        type: 'info',
        duration: 5000
      })
    } else {
      ElMessage({
        message: response.message || '邀请码无效或已过期',
        type: 'warning',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('验证个人邀请码失败:', error)
    ElMessage({
      message: '邀请码验证失败',
      type: 'warning',
      duration: 3000
    })
  } finally {
    invitationLoading.value = false
  }
}

// 处理开发登录
const handleDevLogin = async () => {
  if (!isDevelopment.value) {
    ElMessage.error('开发登录仅在开发环境可用')
    return
  }

  devLoginLoading.value = true
  errorMessage.value = ''

  try {
    console.log('正在尝试开发登录...')

    // 使用预设的开发账号
    const devCredentials = {
      username: 'dev',
      password: 'dev123'
    }

    await authStore.login(devCredentials.username, devCredentials.password)
    console.log('开发登录成功，准备跳转')

    // ElMessage.success('开发登录成功！')

    // 调用统一的登录成功处理函数
    await handleLoginSuccess()
  } catch (error) {
    console.error('开发登录失败:', error)

    // 如果是用户不存在的错误，提示创建开发用户
    if (error.response?.status === 401) {
      ElMessage.error('开发账号不存在，请联系管理员创建开发用户（用户名: dev, 密码: dev123）')
    } else {
      // 提取错误信息
      let message = '开发登录失败'
      if (error.response?.data?.detail) {
        message = error.response.data.detail
      } else if (error.response?.data?.error) {
        message = error.response.data.error
      } else if (error.response?.data?.message) {
        message = error.response.data.message
      } else if (error.message) {
        message = error.message
      }

      ElMessage.error(message)
    }
  } finally {
    devLoginLoading.value = false
  }
}

// 处理密码登录
const handlePasswordLogin = async (event) => {
  // 阻止表单默认提交行为
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  if (!form.username || !form.password) {
    errorMessage.value = '请输入用户名和密码'
    return false
  }

  if (!agreeTerms.value) {
    errorMessage.value = '请同意服务条款和隐私政策'
    return false
  }

  loading.value = true
  errorMessage.value = ''
  
  try {
    console.log('正在尝试密码登录...', { username: form.username })
    await authStore.login(form.username, form.password)
    console.log('密码登录成功，准备跳转')

    // ElMessage.success('登录成功！')

    // 调用统一的登录成功处理函数
    await handleLoginSuccess()
  } catch (error) {
    console.error('密码登录失败:', error)
    
    // 提取错误信息
    let message = '账号或密码错误'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    } else if (error.response?.data?.error) {
      message = error.response.data.error
    } else if (error.response?.data?.message) {
      message = error.response.data.message
    } else if (error.message) {
      message = error.message
    }
    
    // 显示错误消息在密码框下方
    errorMessage.value = message
  } finally {
    loading.value = false
  }
  
  return false
}
</script>

<style scoped>
/* 网格背景 */
.tech-grid {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 30s linear infinite;
}

/* 浅色背景的网格 */
.tech-grid-light {
  background-image: 
    linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 30s linear infinite;
}

/* 几何图形 */
.tech-hexagon {
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
  filter: blur(0.5px);
}

.tech-circle {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
  border-radius: 50%;
  filter: blur(1px);
}

.tech-triangle {
  background: linear-gradient(60deg, rgba(255, 255, 255, 0.06), transparent);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  filter: blur(0.8px);
}

/* 浅色背景的几何图形 */
.tech-hexagon-light {
  background: linear-gradient(45deg, rgba(99, 102, 241, 0.15), rgba(67, 56, 202, 0.08));
  clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
  filter: blur(0.5px);
}

.tech-circle-light {
  background: radial-gradient(circle, rgba(139, 92, 246, 0.12) 0%, transparent 70%);
  border-radius: 50%;
  filter: blur(1px);
}

.tech-triangle-light {
  background: linear-gradient(60deg, rgba(79, 70, 229, 0.1), transparent);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  filter: blur(0.8px);
}

/* 径向渐变工具类 */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-from), var(--tw-gradient-to));
}

/* 动画定义 */
@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(-3deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(8deg); }
}

/* 动画应用类 */
.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 6s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

/* 渐变样式 */
.button-gradient {
  background: linear-gradient(135deg, #4F46E5, #7C3AED);
}

/* 输入框样式优化 */
input::placeholder {
  color: #9CA3AF;
}

/* 错误提示动画 */
.error-fade-enter-active,
.error-fade-leave-active {
  transition: all 0.3s ease;
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 模态框过渡动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

/* 禁用状态的按钮样式 */
.cursor-not-allowed:hover {
  transform: none !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* 文本截断样式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>