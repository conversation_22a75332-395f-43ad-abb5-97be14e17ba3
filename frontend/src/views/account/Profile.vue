<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-6 py-8">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">{{ pageTitle }}</h1>
      </div>

      <!-- 基础信息卡片 -->
      <div class="pro-card mb-6">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">
              {{ isOrganizationAccount ? 'business' : 'person' }}
            </span>
            {{ isOrganizationAccount ? '组织基础信息' : '个人基础信息' }}
          </div>
        </div>
        <div class="pro-card-body">
        <div class="space-y-6">
          <!-- 头像区域 -->
          <div class="flex items-center space-x-6">
            <UserAvatar
              :avatar-url="authStore.user?.avatar_url"
              :display-name="authStore.user?.nickname || authStore.user?.username"
              size="extra-large"
              class="w-20 h-20"
            />
            <div class="flex-1">
              <div class="text-lg font-medium text-gray-900 mb-1">
                {{ authStore.user?.nickname || authStore.user?.username || '未设置昵称' }}
              </div>
              <div class="space-y-1">
                <div class="text-sm text-gray-600">用户ID：{{ displayUserId }}</div>
                <div v-if="isOrganizationAccount" class="text-sm text-gray-600">
                  <span class="inline-flex items-center">
                    <span class="material-icons-outlined text-sm mr-1">business</span>
                    组织：{{ organizationName }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 昵称编辑 -->
          <div class="flex items-center space-x-4">
            <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">昵称：</label>
            <div class="flex-1 flex items-center space-x-3">
              <el-input
                v-model="profileForm.nickname"
                placeholder="请输入昵称"
                class="flex-1 max-w-xs"
              />
              <el-button
                type="primary"
                size="small"
                @click="handleSaveNickname"
                :loading="savingNickname"
              >
                保存
              </el-button>
            </div>
          </div>



          </div>
        </div>
      </div>

      <!-- 机构信息卡片 -->
      <div class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">business</span>
            机构信息
          </div>
        </div>
        <div class="pro-card-body">
          <!-- 组织身份时显示机构信息编辑 -->
          <div v-if="isOrganizationAccount" class="space-y-6">
            <!-- 组织名称 -->
            <div class="flex items-center space-x-4">
              <label class="text-sm font-medium text-gray-700 w-20 flex-shrink-0">组织名称：</label>
              <div class="flex-1 flex items-center space-x-3">
                <el-input
                  v-model="organizationForm.companyName"
                  placeholder="请输入组织名称"
                  class="flex-1 max-w-xs"
                />
                <el-button
                  type="primary"
                  size="small"
                  @click="handleSaveOrganization"
                  :loading="savingOrganization"
                >
                  保存
                </el-button>
              </div>
            </div>

            <!-- 企业logo -->
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-1 w-20 flex-shrink-0">
                <label class="text-sm font-medium text-gray-700">企业logo：</label>
                <el-tooltip
                  :content="isOrganizationOwner ? '建议尺寸：200×60px，支持PNG/JPG/SVG格式，最大2MB。上传后将自动保存并替换系统左侧导航栏顶部的\'TunshuEdu\'标识' : '只有组织管理员可以上传企业logo'"
                  placement="top"
                  effect="dark"
                >
                  <i class="material-icons text-sm text-gray-400 cursor-help">info</i>
                </el-tooltip>
              </div>
              <div class="flex-1">
                <div class="flex items-center gap-4">
                  <div
                    :class="[
                      'company-logo-upload border-2 border-dashed rounded-lg text-center transition-colors flex items-center justify-center overflow-hidden bg-white',
                      isOrganizationOwner 
                        ? 'border-gray-300 hover:border-purple-400 cursor-pointer' 
                        : 'border-gray-200 cursor-not-allowed opacity-60'
                    ]"
                    @click="isOrganizationOwner ? handleClickLogoUpload() : null"
                  >
                    <template v-if="logoPreview">
                      <img :src="logoPreview" alt="组织Logo" class="max-h-full max-w-full object-contain" />
                    </template>
                    <template v-else>
                      <div class="flex flex-col items-center p-4">
                        <i class="material-icons text-4xl text-gray-400">add</i>
                        <div class="text-sm text-gray-500 mt-2">
                          {{ isOrganizationOwner ? '点击上传企业logo' : '仅管理员可上传' }}
                        </div>
                      </div>
                    </template>
                  </div>
                  <input 
                    ref="fileInput" 
                    type="file" 
                    accept="image/png,image/jpeg,image/jpg,image/svg+xml" 
                    class="hidden" 
                    @change="handleFileChange"
                    :disabled="!isOrganizationOwner"
                  />
                  <div class="min-w-[160px]">
                    <div v-if="uploadProgress > 0 && uploadProgress < 100" class="w-40">
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-600 h-2 rounded-full transition-all" :style="{ width: `${uploadProgress}%` }"></div>
                      </div>
                      <div class="text-xs text-gray-500 mt-1">上传中 {{ uploadProgress }}%</div>
                    </div>
                    <div v-if="uploadError" class="text-xs text-red-600 mt-1">{{ uploadError }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 个人身份时显示提示信息 -->
          <div v-else class="text-center py-12">
            <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-4">
              <span class="material-icons-outlined text-gray-400 text-2xl">business</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">组织功能</h3>
            <p class="text-gray-500 text-sm max-w-md mx-auto">
              要使用组织功能，您需要切换到组织身份或创建新的组织。组织功能包括团队管理、企业资料编辑等。
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { updateProfile } from '@/api/auth'
import { updateOrganization, uploadOrganizationLogo, getOrganizationDetail } from '@/api/organizations'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import UserAvatar from '@/components/common/UserAvatar.vue'
import { mapUserIdForDisplay } from '@/utils/userIdMapper'

const authStore = useAuthStore()
const { isOrganizationOwner } = useOrganizationPermissions()

// 响应式数据
const savingNickname = ref(false)

const savingOrganization = ref(false)

const profileForm = ref({
  username: '',
  nickname: '',
  email: '',
  createdAt: null
})

const organizationForm = ref({
  companyName: '',
  companyLogo: ''
})



// 判断是否为组织账号
const isOrganizationAccount = computed(() => {
  // 检查 currentIdentity 中的身份类型
  const identityType = authStore.currentIdentity?.identity_type
  const organizationId = authStore.currentIdentity?.organization_id
  
  // 如果身份类型是 organization，则为组织账号
  return identityType === 'organization' && organizationId
})

// 组织名称
const organizationName = computed(() => {
  // 从 currentIdentity 中获取组织名称
  const orgName = authStore.currentIdentity?.organization_name || 
                  authStore.currentIdentity?.organization?.name ||
                  authStore.user?.organization?.name ||
                  authStore.user?.organization_name
  
  return orgName || '未知组织'
})

// 计算显示的用户ID
const displayUserId = computed(() => {
  return mapUserIdForDisplay(authStore.user?.id || '28865')
})

// 动态页面标题
const pageTitle = computed(() => {
  return isOrganizationAccount.value ? '组织资料' : '个人资料'
})



// 初始化用户信息
const initUserProfile = () => {
  if (authStore.user) {
    profileForm.value = {
      username: authStore.user.username || '',
      nickname: authStore.user.nickname || '',
      email: authStore.user.email || '',
      createdAt: authStore.user.createdAt || new Date()
    }
  }
  
  // 初始化组织信息
  if (isOrganizationAccount.value) {
    organizationForm.value = {
      companyName: organizationName.value || '',
      companyLogo: ''
    }
  }
}

// 监听用户信息变化，同步更新表单
watch(() => authStore.user, (newUser) => {
  if (newUser) {
    profileForm.value.nickname = newUser.nickname || ''
    profileForm.value.email = newUser.email || ''
  }
}, { deep: true })



// 保存昵称
const handleSaveNickname = async () => {
  if (!profileForm.value.nickname.trim()) {
    ElMessage.warning('请输入昵称')
    return
  }

  try {
    savingNickname.value = true

    const response = await updateProfile({
      nickname: profileForm.value.nickname.trim()
    })

    // 更新本地用户信息
    if (authStore.user) {
      authStore.user.nickname = profileForm.value.nickname.trim()
    }

    ElMessage.success('昵称保存成功')
  } catch (error) {
    console.error('保存昵称失败:', error)
    ElMessage.error('保存昵称失败，请稍后重试')
  } finally {
    savingNickname.value = false
  }
}





// 保存机构信息
const handleSaveOrganization = async () => {
  if (!organizationForm.value.companyName.trim()) {
    ElMessage.warning('请输入组织名称')
    return
  }

  try {
    savingOrganization.value = true

    // 获取当前组织ID
    const organizationId = authStore.currentIdentity?.organization_id
    if (!organizationId) {
      ElMessage.error('未找到组织信息')
      return
    }

    // 调用更新组织API
    const updateData = {
      name: organizationForm.value.companyName.trim()
    }
    
    const response = await updateOrganization(organizationId, updateData)
    
    // 更新本地组织信息
    if (authStore.currentIdentity) {
      authStore.currentIdentity.organization_name = organizationForm.value.companyName.trim()
    }

    // 重新获取用户身份列表，以更新身份切换对话框中的组织名称
    try {
      await authStore.fetchUserIdentities()
    } catch (error) {
      console.warn('更新身份信息失败:', error)
      // 不影响主要流程，只是身份切换对话框可能不会立即显示最新名称
    }

    ElMessage.success('组织信息保存成功')
  } catch (error) {
    console.error('保存组织信息失败:', error)
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error('保存组织信息失败，请稍后重试')
    }
  } finally {
    savingOrganization.value = false
  }
}

// 上传相关状态
const uploadProgress = ref(0)
const uploadError = ref('')
const logoPreview = ref('')
const fileInput = ref(null)
const handleClickLogoUpload = () => { fileInput.value && fileInput.value.click() }

const validateFile = (file) => {
  const allowed = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml']
  const maxSize = 2 * 1024 * 1024
  if (!allowed.includes(file.type)) return '仅支持 PNG/JPG/JPEG/SVG 格式'
  if (file.size > maxSize) return '图片大小不能超过 2MB'
  return ''
}

const handleFileChange = async (e) => {
  // 检查权限
  if (!isOrganizationOwner.value) {
    ElMessage.warning('只有组织管理员可以上传企业logo')
    return
  }

  const files = e.target.files
  if (!files || !files[0]) return
  const file = files[0]
  uploadError.value = validateFile(file)
  if (uploadError.value) return

  // 本地预览
  logoPreview.value = URL.createObjectURL(file)

  // 执行上传
  const organizationId = authStore.currentIdentity?.organization_id
  if (!organizationId) {
    uploadError.value = '未找到组织信息'; return
  }
  try {
    uploadProgress.value = 1
    const res = await uploadOrganizationLogo(organizationId, file, (p) => uploadProgress.value = p)
    if (res?.logo_url) {
      // 更新预览为新的logo_url
      logoPreview.value = res.logo_url
      
      // 刷新身份信息，带回logo_url
      try { 
        await authStore.fetchUserIdentities()
        await authStore.fetchCurrentIdentity()
      } catch {}
      
      // 触发侧边栏logo刷新 - 通过全局事件
      window.dispatchEvent(new CustomEvent('organization-logo-updated', { 
        detail: { logo_url: res.logo_url } 
      }))
      
      ElMessage.success('Logo 上传成功，侧边栏已更新')
    } else {
      uploadError.value = '上传失败'
    }
  } catch (err) {
    uploadError.value = err?.response?.data?.detail || '上传失败，请稍后重试'
  } finally {
    setTimeout(() => uploadProgress.value = 0, 400)
  }
}



// 初始化企业logo预览
const initOrganizationLogo = async () => {
  if (!authStore.currentIdentity?.organization_id) return
  
  try {
    const response = await getOrganizationDetail(authStore.currentIdentity.organization_id)
    if (response.logo_url) {
      logoPreview.value = response.logo_url
    }
  } catch (error) {
    console.warn('获取组织logo失败:', error)
  }
}

// 组件挂载时初始化数据
onMounted(async () => {
  try {
    // 确保获取当前身份信息
    await authStore.fetchCurrentIdentity()
    // 初始化用户资料
    initUserProfile()
    // 如果是组织身份，初始化企业logo预览
    if (authStore.currentIdentity?.identity_type === 'organization') {
      await initOrganizationLogo()
    }
  } catch (error) {
    console.error('获取身份信息失败:', error)
    // 即使获取身份信息失败，也尝试初始化用户资料
    initUserProfile()
  }
})
</script>

<style scoped>

/* 覆盖Element Plus主题变量 */
:deep(:root) {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important;
}

/* 按钮样式统一 - 紫色主题 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5 !important;
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important;
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important;
  --el-button-active-border-color: #3730A3 !important;
  --el-button-text-color: #FFFFFF !important;
  border: none !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  transform: translateY(-1px);
}

:deep(.el-button--primary:active) {
  transform: translateY(0);
}



/* 输入框样式 - 去掉阴影 */
:deep(.el-input__wrapper) {
  box-shadow: none !important;
  border: 1px solid #D1D5DB;
  transition: border-color 0.3s ease;
}

:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
  box-shadow: none !important;
}

:deep(.el-input__wrapper:hover) {
  border-color: #6366F1 !important;
}



/* 企业logo上传区域样式 */
.company-logo-upload {
  width: 200px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.company-logo-upload:hover {
  border-color: #4F46E5 !important;
  background-color: #F9FAFB;
}



/* 头像容器样式 */
:deep(.user-avatar) {
  border: 3px solid #E5E7EB;
}

/* 编辑按钮样式 */
button {
  outline: none;
  border: none;
}

button:focus {
  outline: none;
  box-shadow: none;
}



/* 响应式设计 */
@media (max-width: 768px) {

  .flex.items-center.space-x-4 {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .flex.items-center.space-x-4 > .w-16,
  .flex.items-center.space-x-4 > .w-20 {
    width: auto;
    margin-bottom: 0.5rem;
  }

  .flex-1.flex.items-center.space-x-3 {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
}

/* 强制覆盖任何可能的蓝色或其他颜色样式 */
:deep(.el-button[type="primary"]),
:deep(button.el-button--primary),
:deep(.el-button.el-button--primary) {
  --el-button-bg-color: #4F46E5 !important;
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important;
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important;
  --el-button-active-border-color: #3730A3 !important;
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

:deep(.el-button[type="primary"]:hover),
:deep(button.el-button--primary:hover),
:deep(.el-button.el-button--primary:hover) {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
}

:deep(.el-button[type="primary"]:active),
:deep(button.el-button--primary:active),
:deep(.el-button.el-button--primary:active) {
  background-color: #3730A3 !important;
  border-color: #3730A3 !important;
}

/* Material Icons 确保正确显示 */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
</style>