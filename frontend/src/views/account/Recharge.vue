<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 套餐选择页面 -->
    <div class="container mx-auto px-6 py-12 pb-24">
      <!-- 页面标题 -->
      <div class="mb-12">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">选择最适合你的方案</h1>
        <p class="text-gray-600 mb-6">灵活的定价方案，满足你的不同需求</p>

        <!-- 使用兑换码按钮 -->
        <div class="flex justify-end items-center">
          <button
            @click="showRedeemCodeDialog = true"
            class="flex items-center text-sm hover:opacity-80 transition-opacity"
            style="color: #4F46E5;"
          >
            <span class="material-icons-outlined text-sm mr-1">card_giftcard</span>
            使用兑换码
          </button>
        </div>
      </div>



      <!-- 选校规划标签和提示 -->
      <div class="flex justify-between items-center mb-12">
        <div class="text-2xl font-bold text-gray-800 border-b-2 border-gray-200 pb-2">
          选校规划
        </div>
        <div class="text-gray-500 text-sm">
          *所有版本需按年付费
        </div>
      </div>

      <!-- 所有套餐 -->
      <div class="flex flex-wrap justify-center gap-6 max-w-6xl mx-auto">

        <!-- 个人标准版 -->
        <div class="bg-white rounded-3xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 flex flex-col w-80 min-h-[420px]">
          <h3 class="text-lg font-bold mb-4" style="color: #4F46E5;">个人标准版</h3>

          <div class="mb-6">
            <div class="flex items-baseline">
              <span class="text-3xl font-bold" style="color: #4F46E5;">90元</span>
              <span class="text-gray-500 ml-2">人/月</span>
            </div>
          </div>

          <div class="space-y-3 mb-8 flex-grow">
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>不限次数选校方案匹配</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>500积分（约100次智能选校）</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>100次数据方案导出</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>定校书不限项目个数</span>
            </div>
          </div>

          <button
            class="w-full text-white py-3 rounded-xl font-medium transition-all mt-auto hover:opacity-90"
            style="background-color: #4F46E5;"
            @click="selectPackage('personal_standard')"
          >
            选择套餐
          </button>
        </div>

        <!-- 个人专业版 -->
        <div class="bg-white rounded-3xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 flex flex-col w-80 min-h-[420px]">
          <h3 class="text-lg font-bold mb-4" style="color: #4F46E5;">个人专业版</h3>

          <div class="mb-6">
            <div class="flex items-baseline">
              <span class="text-3xl font-bold" style="color: #4F46E5;">140元</span>
              <span class="text-gray-500 ml-2">人/月</span>
            </div>
          </div>

          <div class="space-y-3 mb-8 flex-grow">
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>不限次数选校方案匹配</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>1000积分（约200次智能选校）</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>500次数据方案导出</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>会员内部群</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>定校书不限项目个数</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>支持自定义logo</span>
            </div>
          </div>

          <button
            class="w-full text-white py-3 rounded-xl font-medium transition-all mt-auto hover:opacity-90"
            style="background-color: #4F46E5;"
            @click="selectPackage('personal_professional')"
          >
            选择套餐
          </button>
        </div>

        <!-- 商业旗舰版 -->
        <div class="bg-white rounded-3xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 flex flex-col relative w-80 min-h-[420px]">
          <!-- 20%off标签 -->
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-gradient-to-r from-orange-400 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full">
              20%OFF
            </span>
          </div>

          <h3 class="text-lg font-bold mb-4" style="color: #4F46E5;">商业旗舰版</h3>

          <div class="mb-6">
            <div class="flex flex-col">
              <!-- 原价（删除线） -->
              <div class="flex items-baseline">
                <span class="text-xl text-gray-400 line-through">160元</span>
                <span class="text-gray-400 ml-1 text-sm">人/月</span>
              </div>
              <!-- 折后价格 -->
              <div class="flex items-baseline mt-1">
                <span class="text-3xl font-bold" style="color: #4F46E5;">128元</span>
                <span class="text-gray-500 ml-2">人/月</span>
                <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full ml-2 font-medium">
                  限时优惠
                </span>
              </div>
            </div>
          </div>

          <div class="space-y-3 mb-8 flex-grow">
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>含个人专业版所有功能</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>不限次数方案导出</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>快速响应专属业务群</span>
            </div>
            <div class="flex items-center text-sm">
              <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
              </span>
              <span>多账号购买优惠</span>
            </div>

            <div class="rounded-lg p-3 mt-4" style="background-color: rgba(79, 70, 229, 0.1);">
              <div class="text-center text-sm font-medium" style="color: #4F46E5;">
                5人及以上享20%off优惠
              </div>
            </div>
          </div>

          <button
            class="w-full text-white py-3 rounded-xl font-medium transition-all mt-auto hover:opacity-90"
            style="background-color: #4F46E5;"
            @click="handleBusinessFlagshipSelection"
          >
            选择套餐
          </button>
        </div>


      </div>

      <!-- 智能积分部分 -->
      <div class="mt-16">
        <!-- 标题 -->
        <div class="flex justify-between items-center mb-12">
          <div class="text-2xl font-bold text-gray-800 border-b-2 border-gray-200 pb-2">
            智能积分
          </div>
        </div>

        <!-- 积分充值档位 -->
        <div class="flex flex-wrap justify-center gap-6 max-w-6xl mx-auto">
          <!-- 动态加载的积分档位 -->
          <div 
            v-for="bundle in displayCreditBundles" 
            :key="bundle.id || bundle.credits"
            class="bg-white rounded-3xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 flex flex-col w-80 min-h-[420px]"
          >
            <h3 class="text-lg font-bold mb-4" style="color: #4F46E5;">{{ bundle.name }}</h3>
            
            <div class="mb-6">
              <div class="flex items-baseline">
                <span class="text-3xl font-bold" style="color: #4F46E5;">{{ bundle.amount }}元</span>
              </div>
              <p class="text-sm text-gray-600 mt-2">{{ bundle.description }}</p>
              <div v-if="bundle.original_amount && bundle.original_amount !== bundle.amount" class="text-sm text-gray-500 mt-2">
                <span class="line-through">原价：{{ bundle.original_amount }}元</span>
              </div>
            </div>

            <div class="space-y-3 mb-8 flex-grow">
              <div 
                v-for="feature in (bundle.features || ['支持智能建档', '支持智能文书', '支持智能选校', '支持PDF定校表导出', '支持查AI率', '支持降AI率'])" 
                :key="feature"
                class="flex items-center text-sm"
              >
                <span class="w-4 h-4 rounded-full flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
                  <span class="material-icons-outlined" style="color: #4F46E5; font-size: 10px;">check</span>
                </span>
                <span>{{ feature }}</span>
              </div>
            </div>

            <button
              class="w-full text-white py-3 rounded-xl font-medium transition-all mt-auto hover:opacity-90"
              style="background-color: #4F46E5;"
              @click="selectCreditBundle(bundle.amount, bundle.credits)"
              :disabled="isLoadingCreditBundles"
            >
              <span v-if="isLoadingCreditBundles">加载中...</span>
              <span v-else>立即支付</span>
            </button>
          </div>

          <!-- 加载中状态 -->
          <div v-if="isLoadingCreditBundles && displayCreditBundles.length === 0" class="flex justify-center items-center w-full">
            <div class="text-gray-500">加载积分档位中...</div>
          </div>
        </div>
      </div>

    </div>

    <!-- 兑换码浮窗 -->
    <div
      v-if="showRedeemCodeDialog"
      class="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-50"
      @click="closeRedeemCodeDialog"
    >
      <div
        class="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md mx-4"
        @click.stop
      >
        <!-- 标题区域 -->
        <div class="flex items-center mb-6">
          <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3" style="background-color: rgba(79, 70, 229, 0.1);">
            <span class="material-icons-outlined text-lg" style="color: #4F46E5;">card_giftcard</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900">输入兑换码</h3>
        </div>

        <!-- 描述文字 -->
        <p class="text-gray-600 text-sm mb-6">
          输入您获得的兑换码，即可获得相应的套餐或积分奖励。兑换码可通过参与活动、联系客服及邀请好友等方式获得。
        </p>

        <!-- 兑换码输入区域 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            <span class="text-red-500">*</span> 兑换码
          </label>
          <input
            v-model="redeemCode"
            type="text"
            placeholder="请输入兑换码"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg outline-none transition-all"
            :class="{
              'border-red-300': redeemCodeError,
              'focus:ring-2 focus:border-transparent': !redeemCodeError
            }"
            style="--tw-ring-color: #4F46E5;"
            @input="clearRedeemCodeError"
            @focus="$event.target.style.borderColor = '#4F46E5'; $event.target.style.boxShadow = '0 0 0 2px rgba(79, 70, 229, 0.2)'"
            @blur="$event.target.style.borderColor = ''; $event.target.style.boxShadow = ''"
          />
          <p v-if="redeemCodeError" class="text-red-500 text-sm mt-2">{{ redeemCodeError }}</p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex space-x-3">
          <button
            @click="closeRedeemCodeDialog"
            class="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            取消
          </button>
          <button
            @click="handleRedeemCode"
            :disabled="!redeemCode.trim() || isRedeeming"
            class="flex-1 px-4 py-3 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            :class="isRedeeming ? 'bg-gray-400' : 'hover:bg-opacity-90'"
            style="background-color: #4F46E5;"
          >
            <span v-if="isRedeeming">兑换中...</span>
            <span v-else>立即兑换</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 支付信息确认区域 - 固定在页面底部，不遮挡侧边栏 -->
    <transition name="fade">
      <div v-if="selectedPackageType"
           class="fixed bottom-0 right-0 z-40 bg-white border-t border-gray-200 shadow-lg transition-all duration-300"
           :class="sidebarStore.isCollapsed ? 'left-16' : 'left-56'">
        <div class="p-4 border-t border-gray-100 flex items-center justify-end" style="height: 80px;">
            <!-- 信息内容区域 -->
            <div class="flex items-center space-x-8 mr-6">
              <!-- 账号数量调整 - 体验套餐、个人标准版、个人专业版不显示 -->
              <div v-if="selectedPackageType !== 'personal_standard' && selectedPackageType !== 'personal_professional'" class="flex items-center space-x-3" style="min-height: 48px;">
                <div class="text-center">
                  <div class="text-xs text-gray-500 leading-none">账号数量</div>
                </div>
                <div class="flex items-center space-x-2">
                  <button
                    type="button"
                    @click="decreaseQuantity"
                    class="w-6 h-6 flex items-center justify-center rounded border border-gray-300 hover:bg-gray-50 text-gray-600"
                    :disabled="accountQuantity <= getMinQuantity()"
                  >
                    <span class="text-sm">-</span>
                  </button>
                  <div class="w-8 text-center">
                    <span class="text-sm font-bold" style="color: #4F46E5;">{{ accountQuantity }}</span>
                  </div>
                  <button
                    type="button"
                    @click="increaseQuantity"
                    class="w-6 h-6 flex items-center justify-center rounded border border-gray-300 hover:bg-gray-50 text-gray-600"
                    :disabled="accountQuantity >= 99"
                  >
                    <span class="text-sm">+</span>
                  </button>
                </div>
              </div>

              <!-- 分隔线 - 仅在有账号数量选择时显示 -->
              <div v-if="selectedPackageType !== 'personal_standard' && selectedPackageType !== 'personal_professional'" class="w-px h-8 bg-gray-200"></div>

              <!-- 价格信息 -->
              <div class="text-center flex flex-col justify-center" style="min-height: 48px;">
                <div class="text-xs text-gray-500 leading-none mb-1">套餐总价</div>
                <div class="text-base font-bold" style="color: #4F46E5;">
                  ¥{{ getTotalPrice(selectedPackageType) }}{{ getPriceUnit(selectedPackageType) }}
                </div>
                <!-- 显示原价（如果有优惠） -->
                <div v-if="showDiscountPrice(selectedPackageType)" class="text-xs text-gray-400 line-through mt-1">
                  ¥{{ getOriginalPrice(selectedPackageType) }}{{ getPriceUnit(selectedPackageType) }}
                </div>
              </div>

              <!-- 分隔线 -->
              <div class="w-px h-8 bg-gray-200"></div>

              <!-- 套餐名称 -->
              <div class="text-center flex flex-col justify-center" style="min-height: 48px;">
                <div class="text-xs text-gray-500 leading-none mb-1">套餐类型</div>
                <div class="text-sm font-semibold text-gray-800">
                  {{ getPackageName(selectedPackageType) }}
                </div>
              </div>
            </div>

            <!-- 立即购买按钮 -->
            <button
              class="px-4 py-3 text-white rounded-lg font-medium transition-all hover:opacity-90 hover:scale-105"
              style="background-color: #4F46E5;"
              @click="confirmPurchase"
            >
              立即购买
            </button>
        </div>
      </div>
    </transition>

    <!-- 商业旗舰版身份检查模态框 -->
    <el-dialog
      v-model="showBusinessIdentityModal"
      title="选择套餐身份"
      width="500px"
      class="simple-dialog compact-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      :show-close="true"
      align-center
    >
      <div class="simple-content">
        <!-- 身份选择提示 -->
        <div class="mb-6">
          <p class="text-sm text-gray-600 leading-6">
            商业旗舰版需要使用<strong class="text-gray-900">组织身份</strong>购买。
            请选择以下操作：
          </p>
        </div>

        <!-- 操作选项 -->
        <div class="space-y-3">
          <!-- 创建组织选项 -->
          <div 
            v-if="!hasOrganizations"
            class="p-4 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer"
            @click="handleCreateOrganization"
          >
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="material-icons-outlined text-blue-600">add_business</span>
              </div>
              <div class="flex-1">
                <h4 class="font-medium text-gray-900 mb-1">创建新组织</h4>
                <p class="text-sm text-gray-600 leading-6">创建您的组织账户，享受品牌定制服务</p>
              </div>
              <span class="material-icons-outlined text-gray-400">arrow_forward_ios</span>
            </div>
          </div>

          <!-- 切换到组织身份选项 -->
          <div 
            v-if="hasOrganizations"
            class="p-4 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer"
            @click="handleSwitchToOrganization"
          >
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <span class="material-icons-outlined text-green-600">switch_account</span>
              </div>
              <div class="flex-1">
                <h4 class="font-medium text-gray-900 mb-1">切换到组织身份</h4>
                <p class="text-sm text-gray-600 leading-6">选择您的组织身份来购买商业套餐</p>
              </div>
              <span class="material-icons-outlined text-gray-400">arrow_forward_ios</span>
            </div>
          </div>

          <!-- 创建新组织选项（当已有组织时） -->
          <div 
            v-if="hasOrganizations"
            class="p-4 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer"
            @click="handleCreateOrganization"
          >
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="material-icons-outlined text-blue-600">add_business</span>
              </div>
              <div class="flex-1">
                <h4 class="font-medium text-gray-900 mb-1">创建新组织</h4>
                <p class="text-sm text-gray-600 leading-6">创建另一个组织账户</p>
              </div>
              <span class="material-icons-outlined text-gray-400">arrow_forward_ios</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 创建组织对话框 -->
    <el-dialog
      v-model="showCreateOrganizationDialog"
      title="创建组织"
      width="500px"
      class="simple-dialog compact-dialog"
      @close="handleCloseCreateOrganizationDialog"
    >
      <div class="simple-content">
        <div class="mb-6">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-900 mb-3">组织名称</label>
            <el-input
              v-model="organizationForm.name"
              placeholder="请输入组织名称"
              maxlength="100"
              show-word-limit
              @keyup.enter="handleConfirmCreateOrganization"
            />
          </div>
          <p class="text-sm text-gray-600 leading-6">
            组织名称将作为您团队的标识，创建后可以邀请成员加入
          </p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button @click="showCreateOrganizationDialog = false">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="handleConfirmCreateOrganization"
            :loading="createOrganizationLoading"
            :disabled="!organizationForm.name.trim()"
          >
            {{ createOrganizationLoading ? '创建中...' : '创建组织' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 组织身份选择对话框 -->
    <el-dialog
      v-model="showOrganizationSelectionDialog"
      title="选择工作身份"
      width="500px"
      class="simple-dialog compact-dialog"
      @close="handleCloseOrganizationSelectionDialog"
    >
      <div class="simple-content">
        <div class="mb-6">
          <p class="text-sm text-slate-500 leading-6 text-center mb-6 font-medium">
            选择您要使用的工作身份，不同身份下的数据和权限完全独立
          </p>
          
          <!-- 身份选择卡片 -->
          <div class="space-y-3">
            <!-- 组织身份选项 -->
            <div 
              v-for="org in availableOrganizations"
              :key="org.id"
              @click="handleDirectOrganizationSelection(org.id)"
              :class="[
                'group relative bg-white rounded-2xl p-5 border-2 cursor-pointer transition-all duration-300',
                'hover:shadow-lg hover:border-indigo-300 hover:-translate-y-0.5',
                'border-gray-200'
              ]"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 bg-gray-400 group-hover:bg-indigo-400">
                    <span class="material-icons-outlined text-white text-lg">business</span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-3 mb-1">
                      <h3 class="text-lg font-semibold text-gray-900 truncate">{{ org.name }}</h3>
                      <span :class="[
                        'px-2.5 py-1 text-xs font-medium rounded-full flex-shrink-0 border',
                        org.role === 'owner' 
                          ? 'bg-amber-100 text-amber-700 border-amber-200' 
                          : 'bg-blue-100 text-blue-700 border-blue-200'
                      ]">
                        {{ org.role === 'owner' ? '管理员' : '成员' }}
                      </span>
                    </div>
                    <p class="text-sm text-gray-600">
                      组织成员身份，享受团队协作功能
                    </p>
                  </div>
                </div>
                <div class="flex-shrink-0">
                  <div class="w-6 h-6 border-2 border-gray-300 rounded-full group-hover:border-indigo-400 transition-colors duration-300"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCreditBalance, redeemCode as redeemCodeAPI, getCreditBundles, createCreditRechargeOrder } from '@/api/account'
import { useSidebarStore } from '@/stores/sidebar'
import { useAuthStore } from '@/stores/auth'
import { useOrganizationPermissions } from '@/composables/useOrganizationPermissions'
import { createOrganization } from '@/api/organizations'

const router = useRouter()
const sidebarStore = useSidebarStore()
const authStore = useAuthStore()
const { isPersonalIdentity, isOrganizationIdentity } = useOrganizationPermissions()

// 响应式数据
const selectedPackageType = ref('')
const currentBalance = ref(null)
const accountQuantity = ref(1) // 账户数量

// 兑换码相关数据
const showRedeemCodeDialog = ref(false)
const redeemCode = ref('')
const redeemCodeError = ref('')
const isRedeeming = ref(false)

// 积分充值相关数据
const creditBundles = ref([])
const isLoadingCreditBundles = ref(false)

// 身份检查模态框
const showBusinessIdentityModal = ref(false)
const showCreateOrganizationDialog = ref(false)
const showOrganizationSelectionDialog = ref(false)
const createOrganizationLoading = ref(false)
const switchIdentityLoading = ref(false)
const selectedOrganizationId = ref(null)

// 组织表单数据
const organizationForm = ref({
  name: ''
})

// 默认积分档位（作为备选）
const defaultCreditBundles = [
  {
    id: '200-credits',
    name: '体验积分包',
    description: '200积分，可写约6～7篇文书',
    amount: '99',
    credits: 200,
    features: ['支持智能建档', '支持智能文书', '支持智能选校', '支持PDF定校表导出', '支持查AI率', '支持降AI率']
  },
  {
    id: '1100-credits',
    name: '基础积分包',
    description: '1100积分，可写约28～38篇文书',
    amount: '499',
    credits: 1100,
    features: ['支持智能建档', '支持智能文书', '支持智能选校', '支持PDF定校表导出', '支持查AI率', '支持降AI率']
  },
  {
    id: '2300-credits',
    name: '进阶积分包',
    description: '2300积分，可写约58～77篇文书',
    amount: '999',
    credits: 2300,
    features: ['支持智能建档', '支持智能文书', '支持智能选校', '支持PDF定校表导出', '支持查AI率', '支持降AI率']
  },
  {
    id: '4800-credits',
    name: '专业积分包',
    description: '4800积分，可写约120～160篇文书',
    amount: '1999',
    credits: 4800,
    features: ['支持智能建档', '支持智能文书', '支持智能选校', '支持PDF定校表导出', '支持查AI率', '支持降AI率']
  },
  {
    id: '15000-credits',
    name: '企业积分包',
    description: '15000积分，可写约375～500篇文书',
    amount: '5999',
    credits: 15000,
    features: ['支持智能建档', '支持智能文书', '支持智能选校', '支持PDF定校表导出', '支持查AI率', '支持降AI率']
  }
]

// 计算属性：显示的积分档位
const displayCreditBundles = computed(() => {
  // 如果从后端获取到了数据，使用后端数据
  if (creditBundles.value.length > 0) {
    return creditBundles.value
  }
  // 否则使用默认数据
  return defaultCreditBundles
})

// 计算属性：是否有组织身份
const hasOrganizations = computed(() => {
  return authStore.availableIdentities?.some(identity => 
    identity.identity_type === 'organization'
  ) || false
})

// 计算属性：可用的组织列表
const availableOrganizations = computed(() => {
  return authStore.availableIdentities?.filter(identity => 
    identity.identity_type === 'organization'
  ).map(identity => ({
    id: identity.organization_id,
    name: identity.organization_name,
    role: identity.organization_role
  })) || []
})

// 套餐信息
const packageInfo = {
  personal_standard: {
    name: '个人标准版',
    price: 90,
    credits: '500',
    description: '人/月'
  },
  personal_professional: {
    name: '个人专业版',
    price: 140,
    credits: '1000',
    description: '人/月'
  },
  business_flagship: {
    name: '商业旗舰版',
    price: 160,
    credits: '不限',
    description: '人/月'
  }
}



// 处理商业旗舰版选择
const handleBusinessFlagshipSelection = () => {
  // 检查当前身份类型
  if (isPersonalIdentity.value) {
    // 个人身份，显示身份检查模态框
    showBusinessIdentityModal.value = true
  } else if (isOrganizationIdentity.value) {
    // 组织身份，直接选择套餐
    selectPackage('business_flagship')
  } else {
    // 未知身份状态，提示用户
    ElMessage.warning('请先登录或刷新页面')
  }
}

// 身份检查模态框成功回调
const handleBusinessIdentitySuccess = () => {
  // 身份切换成功后，直接选择商业旗舰版套餐
  selectPackage('business_flagship')
}

// 创建组织
const handleCreateOrganization = () => {
  organizationForm.value = { name: '' }
  showCreateOrganizationDialog.value = true
}

const handleCloseCreateOrganizationDialog = () => {
  organizationForm.value = { name: '' }
  showCreateOrganizationDialog.value = false
}

const handleConfirmCreateOrganization = async () => {
  if (!organizationForm.value.name.trim()) {
    ElMessage.warning('请输入组织名称')
    return
  }

  try {
    createOrganizationLoading.value = true

    const response = await createOrganization({
      name: organizationForm.value.name.trim()
    })

    ElMessage.success('组织创建成功！')
    showCreateOrganizationDialog.value = false

    // 刷新用户身份信息
    await authStore.fetchUserIdentities()

    // 切换到新创建的组织身份
    await authStore.switchUserIdentity({
      identity_type: 'organization',
      organization_id: response.id
    })

    ElMessage.success(`已切换到组织：${response.name}`)
    
    // 关闭主对话框并选择套餐
    showBusinessIdentityModal.value = false
    selectPackage('business_flagship')

  } catch (error) {
    console.error('创建组织失败:', error)

    let message = '创建组织失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)
  } finally {
    createOrganizationLoading.value = false
  }
}

// 切换到组织身份
const handleSwitchToOrganization = () => {
  if (availableOrganizations.value.length === 1) {
    // 如果只有一个组织，直接切换
    selectedOrganizationId.value = availableOrganizations.value[0].id
    handleConfirmOrganizationSelection()
  } else {
    // 如果有多个组织，显示选择对话框
    selectedOrganizationId.value = null
    showOrganizationSelectionDialog.value = true
  }
}

const handleCloseOrganizationSelectionDialog = () => {
  selectedOrganizationId.value = null
  showOrganizationSelectionDialog.value = false
}

const handleConfirmOrganizationSelection = async () => {
  if (!selectedOrganizationId.value) {
    ElMessage.warning('请选择组织')
    return
  }

  try {
    switchIdentityLoading.value = true

    // 切换到选中的组织身份
    await authStore.switchUserIdentity({
      identity_type: 'organization',
      organization_id: selectedOrganizationId.value
    })

    const selectedOrg = availableOrganizations.value.find(org => org.id === selectedOrganizationId.value)
    ElMessage.success(`已切换到组织：${selectedOrg?.name}`)
    
    // 关闭所有对话框并选择套餐
    showOrganizationSelectionDialog.value = false
    showBusinessIdentityModal.value = false
    selectPackage('business_flagship')

  } catch (error) {
    console.error('切换身份失败:', error)

    let message = '切换身份失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)
  } finally {
    switchIdentityLoading.value = false
  }
}

// 直接切换组织身份（点击即切换）
const handleDirectOrganizationSelection = async (organizationId) => {
  try {
    switchIdentityLoading.value = true

    // 切换到选中的组织身份
    await authStore.switchUserIdentity({
      identity_type: 'organization',
      organization_id: organizationId
    })

    const selectedOrg = availableOrganizations.value.find(org => org.id === organizationId)
    ElMessage.success(`已切换到组织：${selectedOrg?.name}`)
    
    // 关闭所有对话框并选择套餐
    showOrganizationSelectionDialog.value = false
    showBusinessIdentityModal.value = false
    selectPackage('business_flagship')

  } catch (error) {
    console.error('切换身份失败:', error)

    let message = '切换身份失败，请重试'
    if (error.response?.data?.detail) {
      message = error.response.data.detail
    }

    ElMessage.error(message)
  } finally {
    switchIdentityLoading.value = false
  }
}



// 选择套餐（仅显示确认区域，不跳转）
const selectPackage = (packageType) => {
  selectedPackageType.value = packageType
  // 根据套餐类型设置购买数量
  if (packageType === 'personal_standard' || packageType === 'personal_professional') {
    accountQuantity.value = 1 // 个人标准版、个人专业版固定1个用户
  } else {
    accountQuantity.value = getMinQuantity()
  }
  // 滚动到确认区域
  nextTick(() => {
    const confirmationElement = document.querySelector('.fixed.bottom-0')
    if (confirmationElement) {
      confirmationElement.scrollIntoView({ behavior: 'smooth', block: 'end' })
    }
  })
}

// 确认购买并跳转到付款页面
const confirmPurchase = () => {
  if (!selectedPackageType.value) return
  
  // 生成新的订单号
  const orderNo = generateOrderNo()
  const createTime = new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })

  // 跳转到付款页面
  router.push({
    name: 'Payment',
    params: { orderNo },
    query: {
      package: selectedPackageType.value,
      userCount: accountQuantity.value,
      createTime: createTime
    }
  })
}

// 获取套餐年费价格
const getPackageYearlyPrice = (packageType) => {
  const packageData = packageInfo[packageType]
  if (!packageData) return 0

  // 体验套餐直接返回原价，不按年计算
  if (packageData.isTrialPackage) {
    return Number(packageData.price)
  }

  const monthlyPrice = packageData.price || 0
  return monthlyPrice * 12
}

// 获取总价格（根据账户数量）
const getTotalPrice = (packageType) => {
  const packageData = packageInfo[packageType]
  if (!packageData) return 0

  // 体验套餐特殊处理
  if (packageData.isTrialPackage) {
    return Number(packageData.price)
  }

  const yearlyPrice = getPackageYearlyPrice(packageType)
  let totalPrice = yearlyPrice * accountQuantity.value

  // 商业旗舰版5人及以上享20%off优惠
  if (packageType === 'business_flagship' && accountQuantity.value >= 5) {
    totalPrice = totalPrice * 0.8
  }

  return Math.round(totalPrice)
}

// 获取原价格（未打折前）
const getOriginalPrice = (packageType) => {
  const packageData = packageInfo[packageType]
  if (!packageData) return 0

  // 体验套餐特殊处理
  if (packageData.isTrialPackage) {
    return Number(packageData.price)
  }

  const yearlyPrice = getPackageYearlyPrice(packageType)
  return yearlyPrice * accountQuantity.value
}

// 是否显示优惠价格
const showDiscountPrice = (packageType) => {
  return packageType === 'business_flagship' && accountQuantity.value >= 5
}

// 获取价格单位
const getPriceUnit = (packageType) => {
  const packageData = packageInfo[packageType]
  if (!packageData) return '/年'

  // 试用版显示特殊单位
  if (packageData.isTrialPackage) {
    return ''
  }

  return '/年'
}

// 增加账户数量
const increaseQuantity = (event) => {
  // 阻止默认行为和事件冒泡
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  
  // 个人标准版、个人专业版不允许增加数量
  if (selectedPackageType.value === 'personal_standard' || 
      selectedPackageType.value === 'personal_professional') {
    return
  }
  if (accountQuantity.value < 99) { // 设置最大限制
    accountQuantity.value++
  }
}

// 减少账户数量
const decreaseQuantity = (event) => {
  // 阻止默认行为和事件冒泡
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  
  // 个人标准版、个人专业版不允许减少数量
  if (selectedPackageType.value === 'personal_standard' || 
      selectedPackageType.value === 'personal_professional') {
    return
  }
  const minQuantity = getMinQuantity()
  if (accountQuantity.value > minQuantity) {
    accountQuantity.value--
  }
}

// 获取套餐名称
const getPackageName = (packageType) => {
  return packageInfo[packageType]?.name || '未知套餐'
}

// 获取最小购买数量
const getMinQuantity = () => {
  // 个人标准版、个人专业版固定1人，其他套餐1人起购
  if (selectedPackageType.value === 'personal_standard' || 
      selectedPackageType.value === 'personal_professional') {
    return 1
  }
  return 1
}



// 生成订单号
const generateOrderNo = () => {
  return 'ORD-' + Date.now() + '-' + Math.random().toString(36).substring(2, 8).toUpperCase()
}

// 积分充值相关方法
const selectCreditBundle = async (amount, credits) => {
  try {
    ElMessage.info('正在创建积分充值订单...')
    
    // 创建积分充值订单
    const response = await createCreditRechargeOrder({
      amount: amount,
      payment_method: 'alipay'
    })

    if (response.success) {
      ElMessage.success('订单创建成功！正在跳转到支付页面...')

      // 如果有支付URL，直接跳转到支付宝支付页面
      if (response.payment_url) {
        // 在新窗口打开支付页面
        window.open(response.payment_url, '_blank')

        // 跳转到支付结果等待页面或订单详情页面
        setTimeout(() => {
          router.push(`/account/orders?order_no=${response.order.order_no}`)
        }, 1000)
      } else {
        // 如果没有支付URL，跳转到订单详情页面
        setTimeout(() => {
          router.push(`/account/orders?order_no=${response.order.order_no}`)
        }, 1000)
      }
    } else {
      ElMessage.error(response.message || '订单创建失败，请稍后重试')
    }

  } catch (error) {
    console.error('积分充值失败:', error)

    // 处理不同类型的错误
    if (error.response?.status === 401) {
      ElMessage.error('请先登录后再进行充值')
      router.push('/login')
    } else if (error.response?.status === 400) {
      ElMessage.error(error.response.data?.detail || '请求参数错误')
    } else if (error.response?.status === 500) {
      ElMessage.error('服务器内部错误，请稍后重试')
    } else {
      ElMessage.error('网络错误，请检查网络连接后重试')
    }
  }
}

// 获取积分充值档位
const fetchCreditBundles = async () => {
  try {
    isLoadingCreditBundles.value = true
    const response = await getCreditBundles()
    
    if (response.success || response.length) {
      const backendBundles = response.bundles || response || []
      
      // 将后端数据转换为我们需要的格式
      creditBundles.value = backendBundles.map(bundle => {
        const packageName = getPackageNameByAmount(bundle.amount)
        const description = getDescriptionByCredits(bundle.credits)
        return {
          ...bundle,
          name: packageName,
          description: description,
          features: ['支持智能建档', '支持智能文书', '支持智能选校', '支持PDF定校表导出', '支持查AI率', '支持降AI率']
        }
      })
    }
  } catch (error) {
    console.error('获取积分充值档位失败:', error)
    // 静默处理错误，使用硬编码的档位作为备选
  } finally {
    isLoadingCreditBundles.value = false
  }
}

// 根据金额获取套餐名称
const getPackageNameByAmount = (amount) => {
  const amountNum = parseFloat(amount)
  if (amountNum <= 99) return '体验积分包'
  if (amountNum <= 499) return '基础积分包'
  if (amountNum <= 999) return '进阶积分包'
  if (amountNum <= 1999) return '专业积分包'
  return '企业积分包'
}

// 根据积分数量获取描述
const getDescriptionByCredits = (credits) => {
  if (credits <= 200) return '200积分，可写约6～7篇文书'
  if (credits <= 1100) return '1100积分，可写约28～38篇文书'
  if (credits <= 2300) return '2300积分，可写约58～77篇文书'
  if (credits <= 4800) return '4800积分，可写约120～160篇文书'
  if (credits <= 15000) return '15000积分，可写约375～500篇文书'
  // 对于其他积分数量，动态计算（大致按1积分=0.025-0.033篇文书）
  const minCount = Math.floor(credits * 0.025)
  const maxCount = Math.floor(credits * 0.033)
  return `${credits}积分，可写约${minCount}～${maxCount}篇文书`
}

// 兑换码相关方法
const closeRedeemCodeDialog = () => {
  showRedeemCodeDialog.value = false
  redeemCode.value = ''
  redeemCodeError.value = ''
}

const clearRedeemCodeError = () => {
  redeemCodeError.value = ''
}

const handleRedeemCode = async () => {
  if (!redeemCode.value.trim()) {
    redeemCodeError.value = '请输入兑换码'
    return
  }

  isRedeeming.value = true
  redeemCodeError.value = ''

  try {
    const response = await redeemCodeAPI(redeemCode.value.trim())

    if (response.success) {
      ElMessage.success(response.message || '兑换成功！积分已到账')
      closeRedeemCodeDialog()

      // 刷新余额
      await fetchCurrentBalance()

      // 如果兑换的是套餐，发送全局事件通知套餐状态变化
      if (response.reward_type === 'package' || response.package_name) {
        // 清除当前套餐选择状态，隐藏购买栏
        selectedPackageType.value = ''

        window.dispatchEvent(new CustomEvent('packagePurchased', {
          detail: {
            source: 'redeem_code',
            redeemCode: redeemCode.value.trim(),
            packageName: response.package_name,
            creditsReceived: response.credits_received,
            timestamp: Date.now()
          }
        }))
        console.log('兑换码套餐兑换成功，已发送全局事件')
      }
    } else {
      redeemCodeError.value = response.message || '兑换失败，请检查兑换码是否正确'
    }

  } catch (error) {
    console.error('兑换码兑换失败:', error)

    // 处理不同类型的错误
    if (error.response && error.response.data && error.response.data.detail) {
      redeemCodeError.value = error.response.data.detail
    } else if (error.message) {
      redeemCodeError.value = error.message
    } else {
      redeemCodeError.value = '兑换失败，请检查兑换码是否正确'
    }
  } finally {
    isRedeeming.value = false
  }
}



// 获取当前积分余额
const fetchCurrentBalance = async () => {
  try {
    const response = await getCreditBalance()
    if (response.success) {
      currentBalance.value = response.balance || 0
    }
  } catch (error) {
    console.error('获取当前余额失败:', error)
    // 不显示错误，因为这不是关键功能
  }
}



// 组件挂载时获取数据
onMounted(() => {
  console.log('充值页面已加载')
  fetchCurrentBalance()
  fetchCreditBundles()
  
  // 检查是否是支付成功返回
  const route = router.currentRoute.value
  if (route.query.success === 'true') {
    ElMessage.success('支付成功！账户余额已更新')
    // 清除URL参数
    router.replace('/account/recharge')
  }
})
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 容器样式 */
.container {
  max-width: 1200px;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.grid > div {
  animation: slideInUp 0.5s ease-out forwards;
}

/* 覆盖Element Plus主题变量 */
:deep(:root) {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important;
}

/* 按钮样式统一 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5 !important;
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important;
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important;
  --el-button-active-border-color: #3730A3 !important;
  --el-button-text-color: #FFFFFF !important;
}

/* 输入框焦点状态 */
:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2) !important;
}

/* 复选框样式 */
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

/* 套餐卡片悬停效果 */
.bg-white.rounded-3xl:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(79, 70, 229, 0.15);
}

/* 按钮悬停效果增强 */
button:hover {
  transform: scale(1.02);
  transition: all 0.2s ease;
}

button:active {
  transform: scale(0.98);
}

/* 价格数字特殊效果 */
.text-3xl.font-bold[style*="color: #4F46E5"] {
  text-shadow: 0 1px 2px rgba(79, 70, 229, 0.1);
}

/* 功能列表图标动画 */
.material-icons-outlined {
  transition: transform 0.2s ease;
}

.flex.items-center:hover .material-icons-outlined {
  transform: scale(1.1);
}

/* 支付方式选择卡片增强 */
.border-2.rounded-lg.p-4 {
  transition: all 0.3s ease;
}

.border-2.rounded-lg.p-4:hover {
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}

/* 支付倒计时样式优化 */
.bg-orange-100 {
  background: linear-gradient(135deg, #FED7AA 0%, #FDBA74 100%);
  border: 1px solid #FB923C;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 数量调整按钮样式 */
.fixed.bottom-0 button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.fixed.bottom-0 button:disabled:hover {
  background-color: inherit;
  transform: none;
}

/* 简洁对话框样式 - 遵循悬浮框设计规范 */
:deep(.simple-dialog.compact-dialog) {
  .el-dialog {
    border-radius: 8px;
  }
  
  .el-dialog__header {
    background: #ffffff;
    border-bottom: none;
    padding: 16px 32px 8px;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0;
    line-height: 1.2;
  }
  
  .el-dialog__body {
    padding: 8px 32px 8px;
    background: #ffffff;
  }
  
  .el-dialog__footer {
    padding: 12px 32px 16px;
    background: #ffffff;
    border-top: none;
  }

  /* 关闭按钮样式 */
  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-dialog__close {
    color: #6B7280 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  .el-dialog__close:hover {
    color: #4F46E5 !important;
  }

  /* 主要按钮样式 */
  .el-button--primary,
  .el-button[type="primary"] {
    --el-button-bg-color: #4F46E5 !important;
    --el-button-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #4338CA !important;
    --el-button-hover-border-color: #4338CA !important;
    --el-button-active-bg-color: #3730A3 !important;
    --el-button-active-border-color: #3730A3 !important;
    --el-button-text-color: #FFFFFF !important;
    background-color: #4F46E5 !important;
    border-color: #4F46E5 !important;
    color: #FFFFFF !important;
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
  }

  /* 次要按钮样式 */
  .el-button:not(.el-button--primary) {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    line-height: 1 !important;
    --el-button-text-color: #374151 !important;
    --el-button-border-color: #D1D5DB !important;
    --el-button-bg-color: #FFFFFF !important;
    --el-button-hover-text-color: #4F46E5 !important;
    --el-button-hover-border-color: #4F46E5 !important;
    --el-button-hover-bg-color: #F8FAFF !important;
  }
}

.compact-dialog .simple-content {
  padding: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .text-4xl {
    font-size: 2rem;
  }

  .text-3xl {
    font-size: 1.5rem;
  }

  /* 移动端按钮样式调整 */
  button {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }

  /* 底部固定窗口移动端适配 */
  .fixed.bottom-0 {
    left: 0 !important; /* 移动端覆盖整个宽度 */
  }

  .fixed.bottom-0 > div {
    padding: 1rem !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: 1rem;
  }

  .fixed.bottom-0 .flex.items-center.space-x-6 {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.75rem;
    margin-right: 0 !important;
  }

  .fixed.bottom-0 .w-px.h-8 {
    display: none;
  }

  /* 移动端账户数量调整器 */
  .fixed.bottom-0 .flex.items-center.space-x-2 {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  /* 移动端按钮样式调整 */
  .fixed.bottom-0 button:last-child {
    width: 100%;
    max-width: 200px;
  }
}


</style>
