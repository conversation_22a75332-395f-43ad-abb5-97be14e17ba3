<template>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-6 py-8">
      <div class="max-w-4xl mx-auto">
        <!-- 返回按钮 -->
        <button
          @click="handleGoBack"
          class="flex items-center mb-6 text-gray-600 hover:opacity-80 transition-opacity text-sm"
          style="color: #4F46E5;"
        >
          <span class="material-icons-outlined mr-1" style="font-size: 16px;">arrow_back</span>
          返回套餐选择
        </button>

        <!-- 确认订单标题 -->
        <div class="text-center mb-8">
          <h1 class="text-2xl font-bold text-gray-900 mb-4">确认订单</h1>
          <div class="text-4xl font-bold mb-2" style="color: #4F46E5;">¥{{ packageInfo.price || 0 }}</div>
          <p class="text-sm text-gray-600 mb-4">套餐价格</p>

          <!-- 支付剩余时间 -->
          <div class="bg-orange-100 text-orange-700 px-4 py-2 rounded-lg inline-block text-sm">
            支付剩余时间：{{ paymentTimeLeft }}
          </div>
        </div>

        <!-- 订单信息和支付方式 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 订单信息 -->
          <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">订单信息</h3>

            <!-- 调试信息 (开发环境) -->
            <div v-if="$route.query.debug" class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded text-xs">
              <div><strong>调试信息:</strong></div>
              <div>orderNo: {{ orderNo }}</div>
              <div>packageType: {{ packageType }}</div>
              <div>userCount: {{ userCount }}</div>
              <div>createTime: {{ createTime }}</div>
              <div>packageInfo: {{ JSON.stringify(packageInfo, null, 2) }}</div>
            </div>

            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-gray-600 text-sm">订单号</span>
                <div class="flex items-center">
                  <span class="font-mono text-sm text-gray-900">{{ orderNo || '生成中...' }}</span>
                  <button class="ml-2 p-1 text-gray-400 hover:opacity-80 transition-opacity" @click="copyOrderNo">
                    <span class="material-icons-outlined" style="font-size: 12px;">content_copy</span>
                  </button>
                </div>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-gray-600 text-sm">商品名称</span>
                <span class="text-gray-900 text-sm font-medium">{{ packageInfo.name || '加载中...' }}</span>
              </div>



              <div class="flex justify-between items-center">
                <span class="text-gray-600 text-sm">用户数量</span>
                <span class="text-gray-900 text-sm">{{ userCount || 1 }}人</span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-gray-600 text-sm">订单类型</span>
                <span class="text-gray-900 text-sm">{{ getOrderType() }}</span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-gray-600 text-sm">订单金额</span>
                <span class="text-gray-900 text-sm font-medium">¥{{ packageInfo.price || 0 }}</span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-gray-600 text-sm">下单时间</span>
                <span class="text-gray-900 text-sm">{{ createTime || '刚刚' }}</span>
              </div>

              <div class="flex justify-between items-center">
                <span class="text-gray-600 text-sm">订单状态</span>
                <span class="text-gray-900 text-sm">待支付</span>
              </div>
            </div>
          </div>

          <!-- 支付方式 -->
          <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold text-gray-900">订单总额</h3>
              <div class="text-2xl font-bold" style="color: #4F46E5;">¥{{ packageInfo.price || 0 }}</div>
            </div>

            <div class="mb-4">
              <h4 class="text-sm font-medium text-gray-900 mb-3">选择支付方式</h4>
              
              <!-- 支付宝支付 -->
              <div
                :class="[
                  'border rounded-lg p-3 cursor-pointer transition-all mb-2',
                  paymentMethod === 'alipay'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                ]"
                @click="paymentMethod = 'alipay'"
              >
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded flex items-center justify-center mr-3">
                    <img :src="AlipayLogo" alt="支付宝" class="w-6 h-6 object-contain" />
                  </div>
                  <div class="flex-1">
                    <div class="font-medium text-sm">支付宝</div>
                  </div>
                  <div v-if="paymentMethod === 'alipay'" class="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                    <span class="material-icons-outlined text-white" style="font-size: 10px;">check</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 服务协议 -->
            <div class="mb-4">
              <label class="flex items-start cursor-pointer">
                <input type="checkbox" v-model="agreeTerms" class="mr-2 mt-1 w-4 h-4 rounded border-gray-300" style="accent-color: #4F46E5;">
                <span class="text-sm text-gray-600">
                  我已阅读并同意
                  <button
                    @click="showServiceAgreementModal = true"
                    class="text-sm transition-colors underline"
                    style="color: #4F46E5;"
                    @mouseover="$event.target.style.color = '#3730A3'"
                    @mouseleave="$event.target.style.color = '#4F46E5'"
                    type="button"
                  >
                    《囤鼠教育充值服务协议》
                  </button>
                </span>
              </label>
            </div>

            <!-- 确认支付按钮 -->
            <button
              :disabled="!paymentMethod || !agreeTerms || loading"
              :class="[
                'w-full py-3 rounded-lg font-medium text-white transition-all',
                !paymentMethod || !agreeTerms || loading
                  ? 'bg-gray-300 cursor-not-allowed'
                  : ''
              ]"
              :style="!paymentMethod || !agreeTerms || loading ? '' : 'background-color: #4F46E5;'"
              @mouseover="(!paymentMethod || !agreeTerms || loading) ? null : ($event.target.style.backgroundColor = '#4338CA')"
              @mouseout="(!paymentMethod || !agreeTerms || loading) ? null : ($event.target.style.backgroundColor = '#4F46E5')"
              @click="confirmPayment"
            >
              <span v-if="loading">处理中...</span>
              <span v-else>确认支付 ¥{{ packageInfo.price || 0 }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 囤鼠教育充值服务协议模态框 -->
    <transition name="modal">
      <div v-if="showServiceAgreementModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <transition name="modal-content">
          <div v-if="showServiceAgreementModal" class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <!-- 模态框头部 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">囤鼠教育充值服务协议</h2>
              <button
                @click="showServiceAgreementModal = false"
                class="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-6 overflow-y-auto max-h-[60vh]">
              <div class="space-y-4 text-sm text-gray-700 leading-relaxed">
                <h4 class="text-base font-medium text-gray-800 mb-3">囤鼠科技《用户协议》</h4>
                <p class="mb-4">欢迎使用 TunshuEdu 系统！请在使用本服务前，仔细阅读并理解本协议。点击"同意"或继续使用，即表示你已阅读、理解并同意遵守以下条款：</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">实名认证与账号安全</h4>
                <p class="mb-4">所有账户需确保通过实名认证，请妥善保管你的登录信息，并对账号下的所有行为负责，账号仅限本人使用。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">用户资格</h4>
                <p class="mb-4">请确认在你开始注册程序使用 TunshuEdu 系统前，你已具备中华人民共和国法律规定的与你行为相适应的民事行为能力。若你不具备前述与你行为相适应的民事行为能力而进行用户注册，则你及你的监护人应依照法律规定承担因此而导致的一切后果。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">服务变更</h4>
                <p class="mb-4">我们有权根据运营需要，对服务内容进行调整和更新。除非另有明确规定，平台新增的任何新功能、新产品、新服务，均无条件地适用本协议。</p>

                <h4 class="text-base font-medium text-gray-800 mb-3 mt-6">联系我们</h4>
                <p class="mb-4">如有疑问，请发送邮件至 <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a>。</p>
              </div>
            </div>

            <!-- 模态框底部 -->
            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                @click="showServiceAgreementModal = false"
                class="px-6 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 transition-colors"
              >
                我已阅读
              </button>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { createPaymentOrder } from '@/api/account'
import AlipayLogo from '@/assets/Alipay_logo.svg'

const route = useRoute()
const router = useRouter()

// 响应式数据
const paymentMethod = ref('alipay')
const agreeTerms = ref(true)
const showServiceAgreementModal = ref(false)
const loading = ref(false)
const paymentTimeLeft = ref('14:50')

// 从路由参数获取订单信息
const orderNo = ref('')
const packageType = ref('')
const userCount = ref(1)
const createTime = ref('')

// 套餐信息映射
const packageInfoMap = {
  personal_standard: {
    name: '个人标准版',
    price: 1080, // 90 * 12
    credits: '500',
    description: '人/年'
  },
  personal_professional: {
    name: '个人专业版',
    price: 1680, // 140 * 12
    credits: '1000',
    description: '人/年'
  },
  business_flagship: {
    name: '商业旗舰版',
    price: 1920, // 160 * 12
    credits: '不限',
    description: '人/年'
  }
}

// 计算属性
const packageInfo = computed(() => {
  const baseInfo = packageInfoMap[packageType.value] || packageInfoMap.personal_standard

  // 体验套餐特殊处理
  if (baseInfo.isTrialPackage) {
    return {
      ...baseInfo,
      price: baseInfo.price,
      unitPrice: baseInfo.price,
      userCount: 1,
      discountApplied: false,
      originalPrice: baseInfo.price
    }
  }

  // 计算实际价格（考虑用户数量和批量折扣）
  let totalPrice = baseInfo.price * userCount.value
  let unitPrice = baseInfo.price
  let discountApplied = false

  // 商业旗舰版5人及以上享20%off优惠
  if (packageType.value === 'business_flagship' && userCount.value >= 5) {
    totalPrice = totalPrice * 0.8
    unitPrice = unitPrice * 0.8
    discountApplied = true
  }

  return {
    ...baseInfo,
    price: Math.round(totalPrice),
    unitPrice: Math.round(unitPrice),
    userCount: userCount.value,
    discountApplied: discountApplied,
    originalPrice: baseInfo.price * userCount.value
  }
})

// 获取订单类型
const getOrderType = () => {
  return '年付套餐'
}

// 返回上一页
const handleGoBack = () => {
  router.push('/account/recharge')
}

// 复制订单号
const copyOrderNo = async () => {
  try {
    await navigator.clipboard.writeText(orderNo.value)
    ElMessage.success('订单号已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 确认支付
const confirmPayment = async () => {
  if (!paymentMethod.value || !agreeTerms.value) {
    ElMessage.warning('请选择支付方式并同意服务协议')
    return
  }

  try {
    loading.value = true

    // 调用后端创建支付订单API
    const response = await createPaymentOrder({
      amount: packageInfo.value.price,
      payment_method: paymentMethod.value,
      package_id: packageType.value,
      user_count: userCount.value,
      remark: `购买${packageInfo.value.name}套餐 (${userCount.value}人)`
    })

    if (response.success) {
      ElMessage.success('订单创建成功！正在跳转到支付页面...')

      // 如果有支付URL，直接跳转到支付宝支付页面
      if (response.payment_url) {
        // 在新窗口打开支付页面
        window.open(response.payment_url, '_blank')

        // 跳转到支付结果等待页面
        setTimeout(() => {
          router.push(`/account/payment-result?order_no=${response.order.order_no}`)
        }, 1000)
      } else {
        // 如果没有支付URL，跳转到订单详情页面
        setTimeout(() => {
          router.push(`/account/orders?order_no=${response.order.order_no}`)
        }, 1000)
      }
    } else {
      ElMessage.error(response.message || '订单创建失败，请稍后重试')
    }

  } catch (error) {
    console.error('支付失败:', error)

    // 处理不同类型的错误
    if (error.response?.status === 401) {
      ElMessage.error('请先登录后再进行支付')
      router.push('/login')
    } else if (error.response?.status === 400) {
      ElMessage.error(error.response.data?.detail || '请求参数错误')
    } else if (error.response?.status === 500) {
      ElMessage.error('服务器内部错误，请稍后重试')
    } else {
      ElMessage.error('网络错误，请检查网络连接后重试')
    }
  } finally {
    loading.value = false
  }
}

// 生成订单号
const generateOrderNo = () => {
  return 'ORD-' + Date.now() + '-' + Math.random().toString(36).substring(2, 8).toUpperCase()
}

// 启动支付倒计时
const startPaymentTimer = () => {
  let minutes = 14
  let seconds = 50

  const timer = setInterval(() => {
    if (seconds === 0) {
      if (minutes === 0) {
        clearInterval(timer)
        paymentTimeLeft.value = '00:00'
        // 移除支付超时提示
        return
      }
      minutes--
      seconds = 59
    } else {
      seconds--
    }

    paymentTimeLeft.value = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }, 1000)
}

// 组件挂载时初始化
onMounted(() => {
  // 从路由参数获取订单信息
  orderNo.value = route.params.orderNo || generateOrderNo()
  packageType.value = route.query.package || 'personal_standard'
  userCount.value = parseInt(route.query.userCount) || 1
  createTime.value = route.query.createTime || new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })

  // 调试信息
  console.log('付款页面初始化数据:', {
    orderNo: orderNo.value,
    packageType: packageType.value,
    userCount: userCount.value,
    createTime: createTime.value,
    routeParams: route.params,
    routeQuery: route.query,
    packageInfo: packageInfo.value
  })

  // 启动倒计时
  startPaymentTimer()
})
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 覆盖Element Plus主题变量 */
:deep(:root) {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important;
}

/* 按钮样式统一 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5 !important;
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important;
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important;
  --el-button-active-border-color: #3730A3 !important;
  --el-button-text-color: #FFFFFF !important;
}

/* 复选框样式 */
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

/* 卡片悬停效果 */
.bg-white.rounded-2xl:hover {
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}

/* 按钮悬停效果 */
button:hover {
  transition: all 0.2s ease;
}

/* 支付倒计时脉冲动画 */
.bg-orange-100 {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .text-4xl {
    font-size: 2rem;
  }

  .text-2xl {
    font-size: 1.5rem;
  }
}

/* 模态框过渡动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}
</style> 