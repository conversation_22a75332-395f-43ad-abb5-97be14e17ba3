<template>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-6 py-8">
      <div class="max-w-6xl mx-auto">
        <!-- 页面标题 -->
        <div class="mb-8">
          <h1 class="text-2xl font-bold text-gray-900">我的订单</h1>
          <p class="text-gray-600 mt-2">查看您的充值订单记录</p>
        </div>

        <!-- 筛选器 -->
        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100 mb-6">
          <div class="flex flex-wrap gap-4 items-center">
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700">订单状态：</label>
              <select
                v-model="filterStatus"
                @change="loadOrders"
                class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2"
                style="focus:ring-color: #4F46E5;"
              >
                <option value="">全部</option>
                <option value="pending">待支付</option>
                <option value="paid">已支付</option>
                <option value="failed">支付失败</option>
                <option value="cancelled">已取消</option>
              </select>
            </div>
            
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700">订单号：</label>
              <input
                v-model="searchOrderNo"
                @keyup.enter="loadOrders"
                placeholder="输入订单号搜索"
                class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2"
                style="focus:ring-color: #4F46E5;"
              >
            </div>
            
            <button
              @click="loadOrders"
              class="px-4 py-1 text-white rounded-md text-sm transition-colors"
              style="background-color: #4F46E5;"
              @mouseover="$event.target.style.backgroundColor = '#4338CA'"
              @mouseout="$event.target.style.backgroundColor = '#4F46E5'"
            >
              搜索
            </button>
          </div>
        </div>

        <!-- 订单列表 -->
        <div v-if="loading" class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2" style="border-color: #4F46E5;"></div>
          <p class="text-gray-600 mt-2">加载中...</p>
        </div>

        <div v-else-if="orders.length === 0" class="bg-white rounded-lg p-8 shadow-sm border border-gray-100 text-center">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="material-icons-outlined text-gray-400" style="font-size: 32px;">receipt_long</span>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无订单</h3>
          <p class="text-gray-600 mb-4">您还没有任何充值订单</p>
          <button
            @click="goToRecharge"
            class="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            立即充值
          </button>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="order in orders"
            :key="order.id"
            class="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
          >
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <!-- 订单基本信息 -->
              <div class="flex-1">
                <div class="flex items-center space-x-4 mb-2">
                  <h3 class="font-medium text-gray-900">订单号：{{ order.order_no }}</h3>
                  <span :class="getStatusClass(order.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ getStatusText(order.status) }}
                  </span>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div>
                    <span class="font-medium">支付金额：</span>
                    <span class="text-gray-900 font-medium">¥{{ order.amount }}</span>
                  </div>
                  <div>
                    <span class="font-medium">支付方式：</span>
                    <span>{{ getPaymentMethodName(order.payment_method) }}</span>
                  </div>
                  <div>
                    <span class="font-medium">创建时间：</span>
                    <span>{{ formatDate(order.created_at) }}</span>
                  </div>
                </div>
                
                <div v-if="order.payment_time" class="text-sm text-gray-600 mt-1">
                  <span class="font-medium">支付时间：</span>
                  <span>{{ formatDate(order.payment_time) }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex flex-wrap gap-2 mt-4 lg:mt-0">
                <button
                  v-if="order.status === 'pending'"
                  @click="payOrder(order)"
                  class="px-4 py-2 text-white rounded-md text-sm transition-colors"
                  style="background-color: #4F46E5;"
                  @mouseover="$event.target.style.backgroundColor = '#4338CA'"
                  @mouseout="$event.target.style.backgroundColor = '#4F46E5'"
                >
                  立即支付
                </button>

                <button
                  v-if="order.status === 'pending'"
                  @click="cancelOrderHandler(order)"
                  :disabled="order.cancelling"
                  class="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {{ order.cancelling ? '取消中...' : '取消订单' }}
                </button>

                <button
                  @click="viewOrderDetail(order)"
                  class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md text-sm hover:bg-gray-300 transition-colors"
                >
                  查看详情
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="mt-8 flex justify-center">
          <div class="flex space-x-2">
            <button
              v-for="page in totalPages"
              :key="page"
              @click="currentPage = page; loadOrders()"
              :class="[
                'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                page === currentPage
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              ]"
            >
              {{ page }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserOrders, cancelOrder } from '@/api/account'

const route = useRoute()
const router = useRouter()

// 响应式数据
const orders = ref([])
const loading = ref(false)
const filterStatus = ref('')
const searchOrderNo = ref('')
const currentPage = ref(1)
const totalPages = ref(1)
const pageSize = 10

// 获取支付方式名称
const getPaymentMethodName = (method) => {
  const methods = {
    'alipay': '支付宝',
    'wechat': '微信支付'
  }
  return methods[method] || method
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'failed': '支付失败',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return statusMap[status] || status
}

// 获取状态样式
const getStatusClass = (status) => {
  const classMap = {
    'pending': 'bg-orange-100 text-orange-800',
    'paid': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'cancelled': 'bg-gray-100 text-gray-800',
    'refunded': 'bg-blue-100 text-blue-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载订单列表
const loadOrders = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      page_size: pageSize
    }
    
    if (filterStatus.value) {
      params.status = filterStatus.value
    }
    
    if (searchOrderNo.value) {
      params.order_no = searchOrderNo.value
    }
    
    const response = await getUserOrders(params)
    
    if (response.success) {
      orders.value = response.orders || []
      totalPages.value = Math.ceil((response.total || 0) / pageSize)
    } else {
      ElMessage.error(response.message || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 支付订单
const payOrder = (order) => {
  router.push(`/account/payment/${order.order_no}`)
}

// 取消订单
const cancelOrderHandler = async (order) => {
  try {
    // 确认对话框
    const confirmed = await ElMessageBox.confirm(
      `确定要取消订单 ${order.order_no} 吗？取消后将无法恢复。`,
      '确认取消订单',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '我再想想',
        type: 'warning',
        confirmButtonClass: 'custom-confirm-btn',
        cancelButtonClass: 'custom-cancel-btn',
        customClass: 'custom-message-box'
      }
    )

    if (!confirmed) return

    // 设置取消状态
    order.cancelling = true

    // 调用取消订单API
    const response = await cancelOrder(order.order_no)

    if (response.success) {
      ElMessage.success('订单取消成功')

      // 更新本地订单状态
      order.status = 'cancelled'
      order.cancelling = false

      // 重新加载订单列表以确保数据同步
      await loadOrders()
    } else {
      ElMessage.error(response.message || '取消订单失败')
      order.cancelling = false
    }

  } catch (error) {
    console.error('取消订单失败:', error)
    order.cancelling = false

    // 处理不同类型的错误
    if (error === 'cancel') {
      // 用户取消了确认对话框，不显示错误消息
      return
    } else if (error.response?.status === 400) {
      ElMessage.error(error.response.data?.detail || '订单状态不允许取消')
    } else if (error.response?.status === 404) {
      ElMessage.error('订单不存在')
    } else if (error.response?.status === 403) {
      ElMessage.error('无权取消此订单')
    } else {
      ElMessage.error('取消订单失败，请稍后重试')
    }
  }
}

// 查看订单详情
const viewOrderDetail = (order) => {
  router.push(`/account/payment-result?order_no=${order.order_no}`)
}

// 跳转到充值页面
const goToRecharge = () => {
  router.push('/account/recharge')
}

// 组件挂载时初始化
onMounted(() => {
  // 如果URL中有订单号参数，自动填入搜索框
  if (route.query.order_no) {
    searchOrderNo.value = route.query.order_no
  }
  
  loadOrders()
})
</script>

<style scoped>
/* 动画效果 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 按钮悬停效果 */
button:hover {
  transition: all 0.2s ease;
}

/* 卡片悬停效果 */
.bg-white.rounded-lg:hover {
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}

/* 自定义确认对话框样式 */
:deep(.custom-message-box) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.custom-message-box .el-message-box__header) {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 24px;
}

:deep(.custom-message-box .el-message-box__title) {
  color: #1f2937;
  font-weight: 600;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 24px;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 16px 24px;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

/* 确认按钮样式 */
:deep(.custom-confirm-btn) {
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  color: white !important;
}

:deep(.custom-confirm-btn:hover) {
  background-color: #4338CA !important;
  border-color: #4338CA !important;
}

/* 取消按钮样式 */
:deep(.custom-cancel-btn) {
  background-color: #f3f4f6 !important;
  border-color: #d1d5db !important;
  color: #374151 !important;
}

:deep(.custom-cancel-btn:hover) {
  background-color: #e5e7eb !important;
  border-color: #9ca3af !important;
  color: #111827 !important;
}
</style>
