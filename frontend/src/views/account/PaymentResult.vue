<template>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 py-6">
      <div class="max-w-lg mx-auto">
        <!-- 支付状态卡片 -->
        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100 text-center">
          <!-- 状态图标 -->
          <div class="mb-4">
            <div v-if="paymentStatus === 'success'" class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <span class="material-icons-outlined text-green-600" style="font-size: 24px;">check_circle</span>
            </div>
            <div v-else-if="paymentStatus === 'failed'" class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <span class="material-icons-outlined text-red-600" style="font-size: 24px;">error</span>
            </div>
            <div v-else class="w-12 h-12 rounded-full flex items-center justify-center mx-auto" style="background-color: rgba(79, 70, 229, 0.1);">
              <span class="material-icons-outlined animate-spin" style="font-size: 24px; color: #4F46E5;">refresh</span>
            </div>
          </div>

          <!-- 状态标题 -->
          <h1 class="text-xl font-bold text-gray-900 mb-3">
            <span v-if="paymentStatus === 'success'">支付成功</span>
            <span v-else-if="paymentStatus === 'failed'">支付失败</span>
            <span v-else>支付处理中</span>
          </h1>

          <!-- 状态描述 -->
          <p class="text-gray-600 mb-4 text-sm">
            <span v-if="paymentStatus === 'success'">恭喜您！支付已成功完成，积分已充值到您的账户。</span>
            <span v-else-if="paymentStatus === 'failed'">很抱歉，支付处理失败，请稍后重试或联系客服。</span>
            <span v-else>正在处理您的支付，请稍候...</span>
          </p>

          <!-- 订单信息 -->
          <div v-if="orderInfo" class="bg-gray-50 rounded-lg p-3 mb-4 text-left">
            <h3 class="text-xs font-medium text-gray-900 mb-2">订单信息</h3>
            <div class="space-y-1.5 text-xs">
              <div class="flex justify-between">
                <span class="text-gray-600">订单号</span>
                <span class="font-mono text-gray-900">{{ orderInfo.order_no }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">支付金额</span>
                <span class="font-medium text-gray-900">¥{{ orderInfo.amount }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">支付方式</span>
                <span class="text-gray-900">{{ getPaymentMethodName(orderInfo.payment_method) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">订单状态</span>
                <span
                  :class="getStatusClass(orderInfo.status)"
                  :style="orderInfo.status === 'refunded' ? 'color: #4F46E5;' : ''"
                >{{ getStatusText(orderInfo.status) }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col gap-2 justify-center">
            <button
              v-if="paymentStatus === 'success'"
              @click="goToAccount"
              class="px-4 py-2 text-white rounded-lg font-medium transition-colors text-sm"
              style="background-color: #4F46E5;"
              @mouseover="$event.target.style.backgroundColor = '#4338CA'"
              @mouseout="$event.target.style.backgroundColor = '#4F46E5'"
            >
              查看账户余额
            </button>

            <button
              v-if="paymentStatus === 'failed'"
              @click="retryPayment"
              class="px-4 py-2 text-white rounded-lg font-medium transition-colors text-sm"
              style="background-color: #4F46E5;"
              @mouseover="$event.target.style.backgroundColor = '#4338CA'"
              @mouseout="$event.target.style.backgroundColor = '#4F46E5'"
            >
              重新支付
            </button>

            <button
              v-if="paymentStatus === 'pending'"
              @click="checkPaymentStatus"
              :disabled="checking"
              class="px-4 py-2 text-white rounded-lg font-medium transition-colors disabled:opacity-50 text-sm"
              style="background-color: #4F46E5;"
              @mouseover="!checking && ($event.target.style.backgroundColor = '#4338CA')"
              @mouseout="!checking && ($event.target.style.backgroundColor = '#4F46E5')"
            >
              {{ checking ? '查询中...' : '刷新状态' }}
            </button>

            <button
              @click="goToOrders"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors text-sm"
            >
              查看订单
            </button>
          </div>
        </div>

        <!-- 自动刷新提示 -->
        <div v-if="paymentStatus === 'pending'" class="mt-3 text-center">
          <p class="text-xs text-gray-500">
            系统将每隔 {{ refreshInterval }} 秒自动查询支付状态
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { queryPaymentStatus } from '@/api/account'

const route = useRoute()
const router = useRouter()

// 响应式数据
const paymentStatus = ref('pending') // pending, success, failed
const orderInfo = ref(null)
const checking = ref(false)
const refreshInterval = ref(5)
let statusTimer = null

// 获取支付方式名称
const getPaymentMethodName = (method) => {
  const methods = {
    'alipay': '支付宝',
    'wechat': '微信支付'
  }
  return methods[method] || method
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'failed': '支付失败',
    'cancelled': '已取消',
    'refunded': '已退款'
  }
  return statusMap[status] || status
}

// 获取状态样式
const getStatusClass = (status) => {
  const classMap = {
    'pending': 'text-orange-600',
    'paid': 'text-green-600',
    'failed': 'text-red-600',
    'cancelled': 'text-gray-600',
    'refunded': ''  // 使用内联样式设置紫色
  }
  return classMap[status] || 'text-gray-600'
}

// 查询支付状态
const checkPaymentStatus = async () => {
  const orderNo = route.query.order_no
  if (!orderNo) {
    ElMessage.error('缺少订单号')
    return
  }

  try {
    checking.value = true
    const response = await queryPaymentStatus(orderNo)
    
    if (response.success && response.order) {
      orderInfo.value = response.order
      
      // 根据订单状态更新支付状态
      if (response.order.status === 'paid') {
        paymentStatus.value = 'success'
        clearInterval(statusTimer)
        ElMessage.success('支付成功！')

        // 发送全局事件通知套餐购买成功
        window.dispatchEvent(new CustomEvent('packagePurchased', {
          detail: {
            orderId: response.order.id,
            packageId: response.order.package_id,
            timestamp: Date.now()
          }
        }))
      } else if (response.order.status === 'failed' || response.order.status === 'cancelled') {
        paymentStatus.value = 'failed'
        clearInterval(statusTimer)
      } else {
        paymentStatus.value = 'pending'
      }
    }
  } catch (error) {
    console.error('查询支付状态失败:', error)
    ElMessage.error('查询支付状态失败')
  } finally {
    checking.value = false
  }
}

// 跳转到账户页面
const goToAccount = () => {
  router.push('/account/recharge')
}

// 重新支付
const retryPayment = () => {
  router.push('/account/recharge')
}

// 跳转到订单页面
const goToOrders = () => {
  router.push('/account/orders')
}

// 启动自动刷新
const startAutoRefresh = () => {
  statusTimer = setInterval(() => {
    if (paymentStatus.value === 'pending') {
      checkPaymentStatus()
    }
  }, refreshInterval.value * 1000)
}

// 组件挂载时初始化
onMounted(() => {
  // 立即查询一次支付状态
  checkPaymentStatus()
  
  // 启动自动刷新
  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer)
  }
})
</script>

<style scoped>
/* 动画效果 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 按钮悬停效果 */
button:hover {
  transition: all 0.2s ease;
}

/* 状态卡片悬停效果 */
.bg-white.rounded-lg:hover {
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}
</style>
