<template>
  <div class="min-h-screen p-6" style="background: linear-gradient(to bottom right, rgba(79, 70, 229, 0.05), rgba(79, 70, 229, 0.1));">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">订单状态查询</h1>
        <p class="text-xl text-gray-600">查询您的支付订单状态，确认积分到账情况</p>
      </div>

      <!-- 订单状态查询组件 -->
      <OrderStatusQuery
        @back="goToRecharge"
        @goToDashboard="goToDashboard"
        @paymentSuccess="handlePaymentSuccess"
      />

      <!-- 返回按钮 -->
      <div class="text-center mt-8">
        <router-link
          to="/account/recharge"
          class="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200"
        >
          <span class="material-icons-outlined text-sm mr-2">arrow_back</span>
          返回充值页面
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import OrderStatusQuery from '@/components/account/OrderStatusQuery.vue'

const router = useRouter()

// 跳转到充值页面
const goToRecharge = () => {
  router.push('/account/recharge')
}

// 跳转到Dashboard
const goToDashboard = () => {
  router.push('/dashboard')
}

// 处理支付成功事件
const handlePaymentSuccess = (order) => {
  ElMessage.success(`支付成功！${order.credits}积分已充值到您的账户`)
  
  // 可以在这里触发全局状态更新
  // 比如更新用户积分余额等
}
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
</style>
