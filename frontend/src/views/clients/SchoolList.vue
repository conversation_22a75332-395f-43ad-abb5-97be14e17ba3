<template>
  <div class="school-list-container">
    <!-- 定校书主体内容 -->
    <div class="grid grid-cols-12 gap-6">
      <!-- 主要内容：专业列表 -->
      <div class="col-span-9">
        <div class="bg-white rounded-lg border border-gray-200">
          <div class="pro-card-header">
            <div class="pro-card-title">
              <span class="material-icons-outlined icon">school</span>
              <h4 class="text-base font-medium text-gray-700">目标专业</h4>
              <span class="text-sm text-gray-500 ml-2">共 {{ totalProgramsCount }} 个专业</span>
            </div>
          </div>
          
          <div class="p-4">
            <template v-if="schoolProgramsByRegion && Object.keys(schoolProgramsByRegion).length > 0">
              <div v-for="(programs, region) in schoolProgramsByRegion" :key="region" class="mb-8 last:mb-0">
                <!-- 地区标题 -->
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center">
                    <span class="text-lg font-semibold text-gray-800 mr-3 region-title">{{ region }}</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {{ programs.length }} 个专业
                    </span>
                  </div>
                  <!-- 收起/展开按钮 -->
                  <button
                    type="button"
                    @click="toggleRegionCollapse(region)"
                    class="group flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:shadow-md transform hover:scale-105 active:scale-95"
                    :class="[
                      collapsedRegions[region] 
                        ? 'text-gray-500 bg-gray-50 hover:bg-gray-100 hover:text-gray-700 shadow-sm' 
                        : 'text-gray-600 bg-white border border-gray-200 hover:border-gray-300 hover:text-gray-800 hover:bg-gray-50 shadow-sm'
                    ]"
                    :title="collapsedRegions[region] ? '展开' + region + '的专业' : '收起' + region + '的专业'"
                  >
                    <span class="material-icons-outlined text-lg transition-all duration-300 group-hover:scale-110"
                          :class="{ 'rotate-180': collapsedRegions[region] }">
                      expand_more
                    </span>
                    <span class="ml-1.5 transition-all duration-300 transform"
                          :class="{ 'translate-x-0.5': collapsedRegions[region] }">
                      {{ collapsedRegions[region] ? '展开' : '收起' }}
                    </span>
                  </button>
                </div>
                
                <!-- 专业卡片网格 - 使用 Vue transition 实现流畅动画 -->
                <transition 
                  name="region-expand"
                  @enter="onEnter"
                  @after-enter="onAfterEnter"
                  @leave="onLeave"
                  @after-leave="onAfterLeave"
                >
                  <div 
                    v-show="!collapsedRegions[region]"
                    class="space-y-4 region-content"
                  >
                    <transition-group 
                      name="program-list"
                      tag="div"
                      class="space-y-4"
                      appear
                    >
                      <div
                        v-for="program in programs"
                        :key="program.id"
                        class="school-card"
                      >
                        <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 hover:border-[#4F46E5]/20">
                          <!-- 卡片头部 -->
                          <div class="px-4 py-3 bg-gradient-to-r from-gray-50 via-white to-gray-50 border-b border-gray-100">
                            <div class="flex items-start">
                              <!-- 学校Logo区域 -->
                              <div class="flex-shrink-0 w-14 h-14 bg-white rounded-lg flex items-center justify-center overflow-hidden mr-4 border border-gray-100 shadow-sm">
                                <img 
                                  :src="getSchoolLogo(program.school_name_cn)" 
                                  :alt="program.school_name_cn + ' Logo'"
                                  class="w-full h-full object-contain"
                                  @error="handleLogoError"
                                  style="background-color: #f8fafc;"
                                />
                              </div>

                              <!-- 学校名称和基本信息 -->
                              <div class="flex-grow">
                                <h3 class="text-base font-semibold text-gray-800">{{ program.school_name_cn }} - {{ program.program_name_cn }}</h3>
                                <p class="text-xs text-gray-500 mt-0.5">{{ program.school_name_en }}{{ program.program_name_en ? ' - ' + program.program_name_en : '' }}</p>
                              </div>

                              <!-- 排名徽章 -->
                              <div class="flex items-center ml-auto self-center">
                                <div class="ranking-badge">
                                  <div class="ranking-label">QS</div>
                                  <div class="ranking-number">{{ program.school_qs_rank || 'N/A' }}</div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 操作按钮区域 -->
                          <div class="px-4 py-2.5 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                            <a 
                              v-if="program.program_website" 
                              :href="program.program_website" 
                              target="_blank" 
                              class="text-[#4F46E5] text-xs flex items-center group hover:text-[#4338CA] transition-colors"
                            >
                              <span class="material-icons-outlined mr-1 !text-sm">open_in_new</span>
                              <span class="group-hover:underline">查看官网</span>
                            </a>
                            <div v-else class="text-xs text-gray-400">官网信息待查询</div>
                            
                            <div class="flex items-center space-x-2">
                              <el-button
                                type="danger"
                                @click="emit('remove-school-program', program.id)"
                                class="!bg-red-500 !border-red-500 hover:!bg-red-600 !h-7 !text-xs !rounded-md !px-2.5 !transition-colors"
                              >
                                <span class="material-icons-outlined mr-1 !text-xs">delete</span>
                                <span class="!text-xs">删除</span>
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </transition-group>
                  </div>
                </transition>

                <!-- 收起状态的简略信息 -->
                <transition name="summary-fade" mode="out-in">
                  <div 
                    v-show="collapsedRegions[region]"
                    key="summary"
                    class="region-collapsed-summary bg-gray-50 rounded-lg p-4 border border-gray-200"
                  >
                    <div class="text-sm text-gray-600">
                      <span>已收起 {{ programs.length }} 个{{ region }}专业</span>
                    </div>
                  </div>
                </transition>
              </div>
            </template>
            
            <!-- 空状态 -->
            <div v-else class="text-center py-16">
              <div class="text-gray-400 mb-4">
                <span class="material-icons-outlined text-6xl">school</span>
              </div>
              <h5 class="text-lg font-medium text-gray-700 mb-2">暂无目标专业</h5>
              <p class="text-sm text-gray-500">请使用右侧的"添加专业"按钮来添加目标专业</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧：定校书操作 -->
      <div class="col-span-3">
        <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
          <!-- 卡片头部 -->
          <div class="px-4 py-3 border-b border-gray-200">
            <div class="flex items-center">
              <h4 class="text-base font-medium text-gray-700">定校书管理</h4>
              <el-tooltip content="定校书将包含所有已添加的目标专业信息，包括学校排名、专业详情、申请要求等。" placement="top">
                <span class="material-icons-outlined text-gray-400 text-sm ml-1 cursor-help">info</span>
              </el-tooltip>
            </div>
          </div>
          
          <!-- 卡片内容 -->
          <div class="p-4">
            <!-- 统计信息区域 -->
            <div class="bg-gray-50 rounded-lg p-6 text-center mb-4">
              <div class="text-3xl font-bold text-[#4F46E5] mb-2">{{ totalProgramsCount }}</div>
              <div class="text-sm text-gray-600 mb-4">目标专业总数</div>
              
              <!-- 地区统计 -->
              <div v-if="Object.keys(schoolProgramsByRegion).length > 0" class="space-y-2">
                <div v-for="(programs, region) in schoolProgramsByRegion" :key="region"
                     class="flex justify-between items-center text-xs text-gray-600">
                  <span class="flex items-center">
                    {{ region }}
                  </span>
                  <span class="font-medium text-gray-800">{{ programs.length }} 个</span>
                </div>
              </div>
            </div>
            
            <!-- 操作按钮区域 -->
            <div class="space-y-2">
              <!-- 添加专业按钮 -->
              <button 
                type="button"
                class="w-full bg-[#4F46E5] hover:bg-[#4338CA] text-white font-medium py-2 px-3 rounded-md transition-colors duration-200 flex items-center justify-center text-sm"
                @click="emit('add-school-program')"
              >
                <span class="material-icons-outlined text-xs mr-1">add</span>
                添加专业
              </button>
              
              <!-- 导出定校书按钮 -->
              <button 
                type="button"
                class="w-full font-medium py-2 px-3 rounded-md transition-colors duration-200 flex items-center justify-center text-sm"
                :class="totalProgramsCount === 0 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                  : 'bg-[#4F46E5] hover:bg-[#4338CA] text-white'"
                :disabled="totalProgramsCount === 0"
                @click="emit('download-school-book')"
              >
                <span class="material-icons-outlined text-xs mr-1">download</span>
                导出定校书
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getProgramList, 
  getProgramCategories,
  getProgramDirections,
  getProgramCount
} from '@/api/programs'
import { getSchoolLogo as getSchoolLogoFallback } from '@/utils/schoolLogos'
import { useSchoolLogosStore } from '@/stores/schoolLogos'
import FloatingLabelInput from '@/components/common/FloatingLabelInput.vue'

// 类型定义
interface SchoolProgram {
  id: string
  school_name_cn: string
  school_name_en: string
  school_region: string
  school_qs_rank?: number | string | null
  program_name_cn: string
  program_name_en: string
  program_direction?: string
  degree?: string
  program_duration?: string
  program_website?: string
  program_tuition?: string
  faculty?: string
  created_at?: string
}

// 专业分类和方向的类型定义
interface ProgramCategory {
  name: string
}

interface ProgramDirection {
  name: string
}

// Props
const props = defineProps<{
  clientData: {
    name: string
  }
  isNewClient: boolean
  schoolPrograms: SchoolProgram[]
}>()

// 事件定义
const emit = defineEmits<{
  'add-school-program': []
  'select-program': [program: SchoolProgram]
  'remove-school-program': [programId: string]
  'view-program-details': [program: SchoolProgram]
  'download-school-book': []
}>()

// 搜索相关状态
const searchLoading = ref(false)
const searchResults = ref<SchoolProgram[]>([])
const categories = ref<ProgramCategory[]>([])
const directions = ref<ProgramDirection[]>([])

// 使用全局logo缓存store
const logoStore = useSchoolLogosStore()

// 搜索分页
const searchPagination = ref({
  currentPage: 1,
  pageSize: 12,
  total: 0
})

// 搜索防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// 可用地区列表
const availableRegions = ref<string[]>([
  '中国香港', '新加坡', '英国', '美国', '澳大利亚', '中国澳门', '马来西亚'
])

// 筛选状态
const selectedCategory = ref('')

// 搜索表单
const searchForm = reactive({
  keyword: '',
  regions: [] as string[], // 明确指定为字符串数组
  program_category: '',
  program_directions: [] as string[] // 明确指定为字符串数组
})

// 地区名称已直接使用中文，无需映射

// 计算属性
const schoolProgramsByRegion = computed(() => {
  const grouped: Record<string, SchoolProgram[]> = {}
  props.schoolPrograms.forEach(program => {
    const region = program.school_region || '其他'
    if (!grouped[region]) {
      grouped[region] = []
    }
    grouped[region].push(program)
  })

  // 对每个地区内的专业进行排序：先按QS排名排序，再按添加时间排序
  Object.keys(grouped).forEach(region => {
    grouped[region].sort((a, b) => {
      // 首先按QS排名排序（数字越小排名越高，无排名的排在后面）
      const rankA = getRankNumber(a.school_qs_rank) || 999999
      const rankB = getRankNumber(b.school_qs_rank) || 999999

      if (rankA !== rankB) {
        return rankA - rankB
      }

      // QS排名相同时，按添加时间排序（先添加的在前）
      const timeA = new Date(a.created_at || 0).getTime()
      const timeB = new Date(b.created_at || 0).getTime()
      return timeA - timeB
    })
  })

  return grouped
})

const totalProgramsCount = computed(() => {
  return props.schoolPrograms.length
})

// 智能检测是否所有地区都已收起
const allRegionsCollapsed = computed(() => {
  const regions = Object.keys(schoolProgramsByRegion.value)
  if (regions.length === 0) return false
  return regions.every(region => collapsedRegions.value[region])
})

// 智能检测是否有地区被收起
const hasCollapsedRegions = computed(() => {
  return Object.values(collapsedRegions.value).some(collapsed => collapsed)
})

// 计算属性：根据选中的专业大类筛选专业方向，并将"其他"选项排在最后
const filteredDirections = computed(() => {
  if (!selectedCategory.value) return []
  
  // 根据专业大类筛选相关的专业方向
  return directions.value.filter((direction: ProgramDirection) => {
    // 根据实际专业方向数据建立精确的映射关系
    const categoryKeywords: Record<string, string[]> = {
      '商科': [
        '金工金数', '金融', '商业分析', '经济', '会计', '市场营销', '信息系统', 
        '管理', '人力资源管理', '供应链管理', '创业与创新', '房地产', 
        '旅游酒店管理', '工商管理', '其他商科'
      ],
      '工科': [
        '计算机', '电气电子', '机械工程', '材料', '化工', '生物工程', 
        '土木工程', '工程管理', '环境工程', '工业工程', '能源', 
        '航空工程', '地球科学', '交通运输', '海洋技术', '食品科学', '其他工科'
      ],
      '理科': [
        '物理', '化学', '数学', '生物', '数据科学', '其他理科'
      ],
      '社科': [
        '教育', '建筑', '法律', '社会学与社工', '国际关系', '哲学', '历史', 
        '公共政策与事务', '艺术', '公共卫生', '心理学', '体育', '药学', '医学', 
        '新闻', '影视', '文化', '媒体与传播', '新媒体', '媒介与社会', 
        '科学传播', '策略传播', '媒体产业', '语言', '其他社科'
      ]
    }
    
    const keywords = categoryKeywords[selectedCategory.value] || []
    return keywords.includes(direction.name)
  }).sort((a, b) => {
    // 将"其他"选项排在最后
    const aIsOther = a.name.startsWith('其他')
    const bIsOther = b.name.startsWith('其他')
    
    if (aIsOther && !bIsOther) return 1
    if (!aIsOther && bIsOther) return -1
    return 0
  })
})

// 计算属性：判断是否有活跃的筛选器
const hasActiveFilters = computed(() => {
  return searchForm.regions.length > 0 || 
         selectedCategory.value !== '' || 
         searchForm.program_directions.length > 0
})

// 工具函数：将QS排名转换为数字
const getRankNumber = (rank: number | string | null | undefined): number | null => {
  if (!rank) return null
  if (typeof rank === 'number') return rank
  const str = String(rank)
  if (str === 'null' || str === '' || str === 'undefined') return null
  const num = parseInt(str)
  return isNaN(num) ? null : num
}

// 方法
const isAlreadySelected = (programId: string) => {
  return props.schoolPrograms.some(p => p.id === programId)
}

// 获取学校Logo
const getSchoolLogo = (schoolNameCn: string) => {
  // 使用全局logo store
  return logoStore.getSchoolLogo(schoolNameCn)
}

// 处理logo加载错误
const handleLogoError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = logoStore.getSchoolLogoFallback('')
}

// 智能关键词生成函数
const generateSmartKeywords = (keyword: string) => {
  const smartKeywords = new Set([keyword])
  const lowerKeyword = keyword.toLowerCase()
  
  // 专业领域关键词映射
  const fieldMappings: Record<string, string[]> = {
    // 商科类
    '商': ['商科', '商业', '管理', '工商管理'],
    '金融': ['金融', '财务', '经济', '银行', 'Finance'],
    '会计': ['会计', '财务', 'Accounting'],
    '管理': ['管理', '工商管理', 'Management', 'MBA'],
    '市场': ['市场营销', '营销', 'Marketing'],
    '经济': ['经济', '经济学', 'Economics'],
    
    // 工科类
    '计算机': ['计算机', '软件', '编程', 'Computer Science', 'CS'],
    '软件': ['软件', '计算机', '编程', 'Software'],
    '电子': ['电子', '电气', '电子工程', 'Electronic'],
    '机械': ['机械', '机械工程', 'Mechanical'],
    '土木': ['土木', '建筑', 'Civil Engineering'],
    
    // 理科类
    '数学': ['数学', '统计', 'Mathematics', 'Statistics'],
    '物理': ['物理', '物理学', 'Physics'],
    '化学': ['化学', '化学工程', 'Chemistry'],
    '生物': ['生物', '生命科学', 'Biology'],
    
    // 社科类
    '心理': ['心理学', '心理', 'Psychology'],
    '教育': ['教育', '教育学', 'Education'],
    '法律': ['法律', '法学', 'Law'],
    '传媒': ['传媒', '新闻', '传播', 'Media', 'Communication'],
    
    // 学校简称和全称
    '哈佛': ['哈佛大学', 'Harvard'],
    '斯坦福': ['斯坦福大学', 'Stanford'],
    '麻省理工': ['麻省理工学院', 'MIT'],
    '牛津': ['牛津大学', 'Oxford'],
    '剑桥': ['剑桥大学', 'Cambridge'],
    '清华': ['清华大学', 'Tsinghua'],
    '北大': ['北京大学', 'Peking University'],
    '港大': ['香港大学', 'University of Hong Kong', 'HKU'],
    '港科大': ['香港科技大学', 'HKUST'],
    '中大': ['香港中文大学', 'CUHK'],
    '新国立': ['新加坡国立大学', 'NUS'],
    '南洋理工': ['南洋理工大学', 'NTU']
  }
  
  // 查找匹配的关键词
  for (const [key, synonyms] of Object.entries(fieldMappings)) {
    if (lowerKeyword.includes(key.toLowerCase()) || 
        synonyms.some(syn => lowerKeyword.includes(syn.toLowerCase()))) {
      synonyms.forEach(syn => smartKeywords.add(syn))
    }
  }
  
  // 处理常见的简写
  const abbreviations: Record<string, string> = {
    'cs': '计算机',
    'ai': '人工智能',
    'ml': '机器学习',
    'mba': '工商管理',
    'phd': '博士',
    'msc': '硕士',
    'ba': '学士'
  }
  
  if (abbreviations[lowerKeyword]) {
    smartKeywords.add(abbreviations[lowerKeyword])
  }
  
  return Array.from(smartKeywords)
}

// 相关性评分函数
const calculateRelevanceScore = (program: SchoolProgram, keyword: string) => {
  let score = 0
  const lowerKeyword = keyword.toLowerCase()
  
  // 精确匹配得分最高
  if (program.school_name_cn?.toLowerCase().includes(lowerKeyword)) score += 100
  if (program.program_name_cn?.toLowerCase().includes(lowerKeyword)) score += 90
  if (program.program_direction?.toLowerCase().includes(lowerKeyword)) score += 80
  
  // 英文匹配
  if (program.school_name_en?.toLowerCase().includes(lowerKeyword)) score += 95
  if (program.program_name_en?.toLowerCase().includes(lowerKeyword)) score += 85
  
  // 开头匹配加分
  if (program.school_name_cn?.toLowerCase().startsWith(lowerKeyword)) score += 50
  if (program.program_name_cn?.toLowerCase().startsWith(lowerKeyword)) score += 40
  
  // 完全匹配超高分
  if (program.school_name_cn?.toLowerCase() === lowerKeyword) score += 200
  if (program.program_name_cn?.toLowerCase() === lowerKeyword) score += 180
  
  // QS排名加分（排名越好加分越多）
  const rank = getRankNumber(program.school_qs_rank)
  if (rank) {
    if (rank <= 50) score += 30
    else if (rank <= 100) score += 20
    else if (rank <= 200) score += 10
  }
  
  return score
}

// Logo预加载现在由全局store处理

// 获取真实的总数量
const getRealTotalCount = async (searchParams: Record<string, any>) => {
  try {
    // 移除分页参数，获取所有匹配的专业
    const countParams = { ...searchParams }
    delete countParams.limit
    delete countParams.offset
    
    // 设置一个大的limit来获取所有数据进行计数
    countParams.limit = 100000 // 设置足够大的数值来获取所有数据
    
    const allResults = await getProgramList(countParams)
    const results = Array.isArray(allResults) ? allResults : []
    
    return results.length
  } catch (error) {
    console.warn('获取总数失败，使用估算值:', error)
    return null
  }
}

// 执行搜索
const handleSearch = async (resetPage = false) => {
  // 如果是重新搜索，重置页面
  if (resetPage) {
    searchPagination.value.currentPage = 1
  }
  
  searchLoading.value = true
  
  try {
    // 构建基础查询参数
    const baseParams: Record<string, any> = {
      limit: searchPagination.value.pageSize,
      offset: (searchPagination.value.currentPage - 1) * searchPagination.value.pageSize
    }

    // 构建筛选参数
    const filterParams: Record<string, any> = {}
    
    // 处理地区筛选（直接使用地区名称）
    if (searchForm.regions.length > 0) {
      filterParams.region = searchForm.regions.length === 1 ? searchForm.regions[0] : searchForm.regions
    }
    
    // 处理专业方向筛选（多选）
    if (searchForm.program_directions.length > 0) {
      filterParams.program_direction = searchForm.program_directions.length === 1 
        ? searchForm.program_directions[0] 
        : searchForm.program_directions
    }

    let results: SchoolProgram[] = []
    let total = 0
    
    // 如果有关键词搜索
    if (searchForm.keyword && searchForm.keyword.trim()) {
      const keyword = searchForm.keyword.trim()
      
      // 优化策略：限制搜索范围，提高查询效率
      const searchLimit = Math.max(searchPagination.value.pageSize * 3, 50) // 最少50条，最多是页面大小的3倍
      
      // 智能关键词扩展（限制数量）
      const smartKeywords = generateSmartKeywords(keyword).slice(0, 3) // 只取前3个智能关键词
      const allKeywords = [keyword, ...smartKeywords.filter(k => k !== keyword)].slice(0, 4) // 最多4个关键词
      
      // 构建精简的搜索Promise数组
      const searchPromises: Promise<any>[] = []
      
      // 主要关键词（原始输入）使用较大的限制
      const mainKeywordLimit = searchLimit
      const otherKeywordLimit = Math.floor(searchLimit / 2) // 其他关键词使用较小的限制
      
      for (let i = 0; i < allKeywords.length; i++) {
        const searchKeyword = allKeywords[i]
        const currentLimit = i === 0 ? mainKeywordLimit : otherKeywordLimit
        
        // 学校中文名搜索（优先级最高）
        searchPromises.push(
          getProgramList({ 
            ...filterParams, 
            school_name: searchKeyword,
            limit: currentLimit,
            offset: 0 // 搜索时总是从0开始
          })
        )
        
        // 专业中文名搜索
        searchPromises.push(
          getProgramList({ 
            ...filterParams, 
            program_name: searchKeyword,
            limit: currentLimit,
            offset: 0
          })
        )
        
        // 只对主关键词进行英文搜索（减少查询数量）
        if (i === 0 && /[a-zA-Z]/.test(searchKeyword)) {
          searchPromises.push(
            // 学校英文名搜索
            getProgramList({ 
              ...filterParams, 
              school_name_en: searchKeyword,
              limit: currentLimit,
              offset: 0
            }),
            // 专业英文名搜索
            getProgramList({ 
              ...filterParams, 
              program_name_en: searchKeyword,
              limit: currentLimit,
              offset: 0
            })
          )
        }
        
        // 只对主关键词且没有指定专业方向时进行专业方向搜索
        if (i === 0 && !filterParams.program_direction) {
          searchPromises.push(
            getProgramList({ 
              ...filterParams, 
              program_direction: searchKeyword,
              limit: currentLimit,
              offset: 0
            })
          )
        }
      }
      
      // 并行执行所有搜索
      const allSearchResults = await Promise.all(searchPromises)
      
      // 合并结果并去重
      const combinedResults = allSearchResults.flat() as SchoolProgram[]
      const uniqueResults = combinedResults.filter((program: SchoolProgram, index: number, self: SchoolProgram[]) => 
        index === self.findIndex((p: SchoolProgram) => p.id === program.id)
      )
      
      // 智能相关性排序
      const scoredResults = uniqueResults.map((program: SchoolProgram) => ({
        ...program,
        relevanceScore: calculateRelevanceScore(program, keyword)
      }))
      
      // 按相关性和QS排名排序
      const sortedResults = scoredResults.sort((a, b) => {
        // 先按相关性分数排序
        if (a.relevanceScore !== b.relevanceScore) {
          return b.relevanceScore - a.relevanceScore
        }
        
        // 相关性相同时按QS排名排序
        const aRank = getRankNumber(a.school_qs_rank)
        const bRank = getRankNumber(b.school_qs_rank)
        
        if (aRank && !bRank) return -1
        if (!aRank && bRank) return 1
        
        if (aRank && bRank) {
          if (aRank !== bRank) return aRank - bRank
        }
        
        // 最后按学校名称排序
        return (a.school_name_cn || '').localeCompare(b.school_name_cn || '')
      })
      
      // 应用分页（前端分页，因为需要相关性排序）
      const startIndex = (searchPagination.value.currentPage - 1) * searchPagination.value.pageSize
      const endIndex = startIndex + searchPagination.value.pageSize
      results = sortedResults.slice(startIndex, endIndex)
      
      // 设置总数（使用实际搜索结果数，避免复杂的count查询）
      total = sortedResults.length
      
      // 如果结果数量达到了搜索限制，说明可能还有更多结果
      if (total >= searchLimit * allKeywords.length * 0.8) {
        // 保守估计，显示"约XXX+条结果"
        total = Math.floor(total * 1.2)
      }
      
    } else if (searchForm.regions.length > 0 || searchForm.program_directions.length > 0) {
      // 只有筛选条件，没有关键词
      const queryParams = { ...baseParams, ...filterParams }
      
      // 直接查询（后端分页）
      results = await getProgramList(queryParams) as SchoolProgram[]
      results = Array.isArray(results) ? results : []
      
      // 获取筛选后的总数
      try {
        const countResult = await getProgramCount(filterParams)
        total = countResult.total
      } catch (error) {
        console.warn('获取筛选总数失败，使用默认值:', error)
        total = results.length
      }
      
    } else {
      // 没有任何搜索和筛选条件，显示所有数据（后端分页）
      results = await getProgramList(baseParams) as SchoolProgram[]
      results = Array.isArray(results) ? results : []
      
      // 获取总数
      try {
        const countResult = await getProgramCount({})
        total = countResult.total
      } catch (error) {
        console.warn('获取总数失败，使用默认值:', error)
        total = results.length
      }
    }
    
    // 设置结果
    searchResults.value = results
    searchPagination.value.total = total
    
    // 预加载当前页面学校的Logo
    if (searchResults.value.length > 0) {
      logoStore.preloadSchoolLogos(searchResults.value)
    }
    
  } catch (error) {
    console.error('搜索专业失败:', error)
    ElMessage.error('搜索专业失败')
    searchResults.value = []
    searchPagination.value.total = 0
  } finally {
    searchLoading.value = false
  }
}

// 防抖搜索
const handleSearchDebounced = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    handleSearch(true) // 重置页面并重新搜索
  }, 500)
}

// 清除搜索
const handleClearSearch = () => {
  handleSearch(true)
}

// 分页变化处理
const handlePageChange = (page: number) => {
  searchPagination.value.currentPage = page
  handleSearch(false) // 获取新页面数据
}

const handlePageSizeChange = (size: number) => {
  searchPagination.value.pageSize = size
  searchPagination.value.currentPage = 1
  handleSearch(true) // 重新搜索获取数据
}

// 选择专业
const handleSelectProgram = (program: SchoolProgram) => {
  if (isAlreadySelected(program.id)) {
    return
  }
  
  emit('select-program', program)
}

// 加载专业大类列表
const loadCategories = async () => {
  try {
    const result = await getProgramCategories()
    
    // 确保result是数组且每个元素都有name属性
    if (!Array.isArray(result)) {
      console.error('专业大类数据格式错误:', result)
      categories.value = []
      return
    }
    
    // 过滤并验证数据
    categories.value = result.filter((category: any) => 
      category && 
      category.name && 
      typeof category.name === 'string'
    ) as ProgramCategory[]
  } catch (error) {
    console.error('加载专业大类失败:', error)
    categories.value = []
  }
}

// 加载专业方向列表
const loadDirections = async () => {
  try {
    const result = await getProgramDirections()
    
    // 确保result是数组且每个元素都有name属性
    if (!Array.isArray(result)) {
      console.error('专业方向数据格式错误:', result)
      directions.value = []
      return
    }
    
    // 过滤并验证数据
    directions.value = result.filter((direction: any) => 
      direction && 
      direction.name && 
      typeof direction.name === 'string'
    ) as ProgramDirection[]
  } catch (error) {
    console.error('加载专业方向失败:', error)
    directions.value = []
  }
}



// 处理地区点击
const handleRegionClick = (region: string) => {
  if (region === '') {
    // 点击"不限"，清空所有选择
    searchForm.regions = []
  } else {
    // 切换选择状态
    if (searchForm.regions.includes(region)) {
      // 如果已选中，则取消选择
      searchForm.regions = searchForm.regions.filter(r => r !== region)
    } else {
      // 如果未选中，则添加到选择列表
      searchForm.regions.push(region)
    }
  }
  handleFilterChange()
}

// 处理专业大类点击
const handleCategoryClick = (category: string) => {
  if (selectedCategory.value === category) {
    // 如果已选中，则取消选择
    selectedCategory.value = ''
    searchForm.program_category = ''
  } else {
    // 选择新的大类
    selectedCategory.value = category
    searchForm.program_category = category
    searchForm.program_directions = [] // 清空专业方向选择
  }
  handleFilterChange()
}

// 处理专业大类下拉选择变化
const handleCategoryChange = (category: string | null) => {
  selectedCategory.value = category || ''
  searchForm.program_category = category || ''
  searchForm.program_directions = [] // 清空专业方向选择
  handleFilterChange()
}

// 处理地区移除
const handleRegionRemove = (region: string) => {
  searchForm.regions = searchForm.regions.filter(r => r !== region)
  handleFilterChange()
}

// 处理专业大类移除
const handleCategoryRemove = () => {
  selectedCategory.value = ''
  searchForm.program_category = ''
  searchForm.program_directions = [] // 清空专业方向选择
  handleFilterChange()
}

// 处理专业方向点击
const handleDirectionClick = (direction: string) => {
  if (searchForm.program_directions.includes(direction)) {
    // 如果已选中，则取消选择
    searchForm.program_directions = searchForm.program_directions.filter(d => d !== direction)
  } else {
    // 选择新的方向
    searchForm.program_directions.push(direction)
  }
  handleFilterChange()
}

// 处理删除已选择的专业方向
const handleDirectionRemove = (direction: string) => {
  searchForm.program_directions = searchForm.program_directions.filter(d => d !== direction)
  handleFilterChange()
}

// 处理筛选变化
const handleFilterChange = () => {
  searchPagination.value.currentPage = 1
  handleSearch(true) // 重置到第一页
}

// 重置筛选条件
const handleResetFilters = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'program_directions' || key === 'regions') {
      (searchForm as any)[key] = []
    } else {
      (searchForm as any)[key] = ''
    }
  })
  selectedCategory.value = ''
  handleSearch(true)
}

// 收起/展开逻辑
const collapsedRegions = ref<Record<string, boolean>>({})
const animatingRegions = ref<Record<string, boolean>>({})

const toggleRegionCollapse = (region: string) => {
  // 防止在动画进行时重复触发
  if (animatingRegions.value[region]) return
  
  animatingRegions.value[region] = true
  collapsedRegions.value[region] = !collapsedRegions.value[region]
  
  // 动画完成后重置状态
  setTimeout(() => {
    animatingRegions.value[region] = false
  }, 400) // 与 CSS transition duration 保持一致
}

// 全部展开/收起逻辑
const toggleAllRegions = () => {
  const regions = Object.keys(schoolProgramsByRegion.value)
  const targetState = !allRegionsCollapsed.value
  
  // 确保所有地区都有状态记录
  regions.forEach(region => {
    collapsedRegions.value[region] = targetState
  })
}

// 监听地区变化，为新地区初始化状态
watch(() => schoolProgramsByRegion.value, (newRegions) => {
  Object.keys(newRegions).forEach(region => {
    if (!(region in collapsedRegions.value)) {
      collapsedRegions.value[region] = false // 默认展开
    }
  })
}, { immediate: true })

// Vue transition 钩子函数，用于流畅的展开/收起动画
const onEnter = (el: HTMLElement) => {
  el.style.height = '0'
  el.style.overflow = 'hidden'
  el.offsetHeight // 强制重排
  el.style.height = el.scrollHeight + 'px'
}

const onAfterEnter = (el: HTMLElement) => {
  el.style.height = ''
  el.style.overflow = ''
}

const onLeave = (el: HTMLElement) => {
  el.style.height = el.scrollHeight + 'px'
  el.style.overflow = 'hidden'
  el.offsetHeight // 强制重排
  el.style.height = '0'
}

const onAfterLeave = (el: HTMLElement) => {
  el.style.height = ''
  el.style.overflow = ''
}

</script>

<style lang="postcss" scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 20px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* 学校卡片样式 */
.school-card {
  @apply transition-all duration-300;
}

.school-card:hover {
  @apply transform -translate-y-0.5 shadow-lg;
}

/* 排名徽章样式 */
.ranking-badge {
  @apply flex items-center justify-center gap-2 px-3 py-2 rounded-xl border shadow-sm transition-all duration-300 min-w-20 h-9;
  border: 1px solid rgba(251, 191, 36, 0.4);
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  backdrop-filter: blur(8px);
}

.ranking-badge:hover {
  @apply transform -translate-y-0.5 scale-105;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
  border-color: rgba(251, 191, 36, 0.6);
}

.ranking-label {
  @apply text-xs font-semibold text-slate-600 uppercase tracking-wide;
  font-size: 0.6rem;
  line-height: 1;
}

.ranking-number {
  @apply text-sm font-bold text-slate-700;
  line-height: 1;
  font-feature-settings: 'tnum';
}

/* 添加按钮的统一样式 */
.add-btn {
  @apply text-white font-medium;
  background-color: #4F46E5;
  border-color: #4F46E5;
}

.add-btn:hover {
  background-color: #4338CA;
  border-color: #4338CA;
}

/* 图标按钮样式 */
:deep(.icon-btn) {
  padding: 6px !important;
  min-width: unset !important;
  width: 32px !important;
  height: 32px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.icon-btn.is-link) {
  padding: 2px !important;
  min-width: unset !important;
  width: 24px !important;
  height: 24px !important;
}

:deep(.icon-btn .material-icons-outlined) {
  margin: 0 !important;
}

/* 地区标题样式 */
.region-title {
  position: relative;
}

.region-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #4F46E5, #818CF8);
  border-radius: 1px;
}

/* 统计信息卡片 */
.stats-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 淡紫色禁用按钮样式 */
:deep(.disabled-light-purple.el-button--primary.is-disabled) {
  background-color: #C7D2FE !important;
  border-color: #C7D2FE !important;
  color: #6366F1 !important;
}

:deep(.disabled-light-purple.el-button--primary.is-disabled:hover) {
  background-color: #C7D2FE !important;
  border-color: #C7D2FE !important;
  color: #6366F1 !important;
}

/* 项目数据库风格样式 */
.program-database-container {
  @apply space-y-6;
}

/* === 新的简洁搜索筛选样式 === */
.search-filter-card {
  @apply bg-white rounded-lg border border-gray-200 shadow-sm p-6;
}

/* 搜索部分 */
.search-section {
  @apply mb-6;
}

.search-container {
  @apply relative;
}

.search-input {
  @apply w-full;
}

:deep(.search-input .floating-input-wrapper) {
  border-radius: 0.5rem;
  border-width: 1px;
}

:deep(.search-input .floating-input-wrapper.focused) {
  border-color: #4F46E5;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-tip {
  display: flex;
  align-items: flex-start;
  gap: 0.375rem;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #9CA3AF;
  line-height: 1.4;
}

.search-tip .material-icons-outlined {
  font-size: 14px;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

/* 筛选器容器 */
.filters-container {
  @apply flex flex-wrap items-end gap-4 mb-4;
}

.filter-item {
  @apply flex flex-col min-w-0;
  flex: 1;
  min-width: 160px;
}

.filter-label {
  @apply flex items-center text-sm font-medium text-gray-700 mb-2;
}

.filter-label .material-icons-outlined {
  @apply text-[#4F46E5] mr-1;
  font-size: 18px;
}

.filter-select {
  @apply w-full;
}

:deep(.filter-select .el-input__wrapper) {
  @apply border-gray-300 rounded-md;
}

:deep(.filter-select.is-focus .el-input__wrapper) {
  @apply border-[#4F46E5] ring-1 ring-[#4F46E5] ring-opacity-20;
}

/* 地区选项样式 */
.region-option {
  @apply flex items-center;
}

.region-flag {
  @apply mr-2;
}

/* 重置按钮 */
.filter-actions {
  @apply flex items-end;
}

.reset-btn {
  @apply text-gray-600 border-gray-300 px-4 py-2 text-sm;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.375rem !important;
}

.reset-btn:hover {
  @apply text-[#4F46E5] border-[#4F46E5] bg-purple-50;
}

/* 活跃筛选标签 */
.active-filters {
  @apply border-t border-gray-200 pt-4 mt-4;
}

.active-filters-label {
  @apply text-sm font-medium text-gray-700 mb-3;
}

.active-filters-tags {
  @apply flex flex-wrap gap-2;
}

.active-filter-tag {
  @apply text-xs font-medium;
}

:deep(.active-filter-tag.el-tag--info) {
  @apply bg-blue-100 text-blue-800 border-blue-200;
}

:deep(.active-filter-tag.el-tag--warning) {
  @apply bg-amber-100 text-amber-800 border-amber-200;
}

:deep(.active-filter-tag.el-tag--success) {
  @apply bg-green-100 text-green-800 border-green-200;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .filters-container {
    @apply flex-col items-stretch;
  }
  
  .filter-item {
    @apply w-full min-w-full;
  }
  
  .filter-actions {
    @apply justify-center mt-4;
  }
}

@media (max-width: 1024px) {
  .search-filter-card {
    @apply p-4;
  }
  
  .filters-container {
    @apply gap-3;
  }
  
  .filter-item {
    min-width: 140px;
  }
}

/* 结果区域样式 */
.pro-card-header {
  @apply px-4 py-3 border-b border-gray-200;
}

.pro-card-body {
  @apply p-4;
}

.result-info {
  @apply text-sm text-gray-700 flex items-center;
}

.result-info .material-icons-outlined {
  @apply text-sm mr-2;
}

.result-info strong {
  @apply text-[#4F46E5] mx-1;
}

.loading-container {
  @apply py-8;
}

.skeleton-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.skeleton-card {
  @apply bg-white rounded-lg border border-gray-200 overflow-hidden;
}

.skeleton-content {
  @apply p-4 space-y-3;
}

.cards-container {
  @apply w-full;
}

.cards-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.program-card {
  @apply relative cursor-pointer;
}

.program-card.card-disabled {
  @apply opacity-75 cursor-not-allowed;
}

.hover-scale:hover {
  @apply transform scale-105;
}

.transition-standard {
  @apply transition-all duration-300;
}

.card-header {
  @apply p-4 border-b border-gray-100 bg-gray-50;
}

.school-info-section {
  @apply flex items-center justify-between mb-3;
}

.school-logo-container {
  @apply w-12 h-12 bg-white rounded-lg flex items-center justify-center overflow-hidden border border-gray-200 mr-3;
}

.school-logo {
  @apply w-full h-full object-contain;
}

.school-content {
  @apply flex-1;
}

.school-name {
  @apply text-sm font-semibold text-gray-900 mb-1;
}

.school-meta {
  @apply flex items-center space-x-2;
}

.region {
  @apply inline-block px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded;
}

.qs-rank {
  @apply text-xs text-gray-500;
}

.degree-badge {
  @apply inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded-md font-medium;
}

.card-body {
  @apply p-4;
}

.program-title-section {
  @apply mb-4;
}

.program-name {
  @apply text-base font-semibold text-gray-900 mb-1;
}

.program-name-en {
  @apply text-sm text-gray-600;
}

.program-meta-grid {
  @apply space-y-2 mb-4;
}

.meta-item {
  @apply flex justify-between items-center;
}

.meta-label {
  @apply text-xs text-gray-500;
}

.meta-value {
  @apply text-xs text-gray-900 font-medium;
}

.info-tags {
  @apply flex flex-wrap gap-2;
}

.info-tag {
  @apply flex items-center space-x-1 px-2 py-1 rounded-md text-xs;
}

.info-tag.tuition {
  @apply bg-yellow-100 text-yellow-800;
}

.info-tag .material-icons-outlined {
  @apply text-xs;
}

.card-footer {
  @apply p-4 border-t border-gray-100 bg-gray-50 flex justify-between items-center;
}

.btn-website {
  @apply text-gray-600 border-gray-300;
}

.btn-website:hover {
  @apply text-gray-800 border-gray-400 bg-gray-50;
}

.btn-add {
  @apply bg-[#4F46E5] border-[#4F46E5];
}

.btn-add:hover {
  @apply bg-[#4338CA] border-[#4338CA];
}

.btn-add:disabled {
  @apply bg-gray-300 border-gray-300 text-gray-500 cursor-not-allowed;
}

.added-overlay {
  @apply absolute inset-0 bg-green-500 bg-opacity-90 flex flex-col items-center justify-center text-white rounded-lg;
}

.added-overlay .material-icons-outlined {
  @apply text-2xl mb-2;
}

.empty-state {
  @apply py-16 text-center;
}

.empty-content {
  @apply text-center;
}

.empty-icon {
  @apply text-4xl text-gray-400 mb-4;
}

.empty-content h3 {
  @apply text-lg font-medium text-gray-700 mb-2;
}

.empty-content p {
  @apply text-sm text-gray-500;
}

.pagination-container {
  @apply mt-8 border-t border-gray-200 pt-6;
}

.pagination-wrapper {
  @apply flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0;
}

.pagination-info {
  @apply text-sm text-gray-600;
}

.custom-pagination {
  @apply flex justify-center md:justify-end;
}

/* Element Plus 对话框样式覆盖 */
:deep(.program-database-dialog) {
  --el-dialog-padding-primary: 0;
}

:deep(.program-database-dialog .el-dialog__header) {
  @apply px-6 py-4 border-b border-gray-200;
}

:deep(.program-database-dialog .el-dialog__body) {
  @apply p-6;
}

:deep(.program-database-dialog .el-dialog__footer) {
  @apply px-6 py-4 border-t border-gray-200;
}

/* 自定义对话框样式 */
.custom-dialog :deep(.el-dialog__header) {
  padding: 16px 24px;
  margin: 0;
  border-bottom: 1px solid #f1f5f9;
}

.custom-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
}

.custom-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.custom-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #f1f5f9;
}

/* 取消按钮样式 */
.cancel-btn {
  @apply text-gray-700 border-gray-300;
}

.cancel-btn:hover {
  @apply bg-gray-50 text-gray-900;
}

/* Element Plus 紫色主题覆盖 */
:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5;
  --el-button-border-color: #4F46E5;
  --el-button-hover-bg-color: #4338CA;
  --el-button-hover-border-color: #4338CA;
  --el-button-active-bg-color: #3730A3;
  --el-button-active-border-color: #3730A3;
}

:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important;
}

:deep(.el-select.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important;
}

/* Vue transition 动画样式 - 流畅的展开/收起效果 */
.region-expand-enter-active,
.region-expand-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: top;
}

.region-expand-enter-from {
  opacity: 0;
  transform: translateY(-8px) scaleY(0.95);
}

.region-expand-leave-to {
  opacity: 0;
  transform: translateY(-8px) scaleY(0.95);
}

/* 专业卡片列表动画 */
.program-list-move,
.program-list-enter-active,
.program-list-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.program-list-enter-from {
  opacity: 0;
  transform: translateY(10px) scale(0.98);
}

.program-list-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.98);
}

.program-list-leave-active {
  position: absolute;
  right: 0;
  left: 0;
}

/* 简略信息淡入淡出动画 */
.summary-fade-enter-active,
.summary-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.summary-fade-enter-from {
  opacity: 0;
  transform: translateY(8px) scale(0.98);
}

.summary-fade-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.98);
}

/* 收起按钮的流畅旋转动画 */
.rotate-180 {
  transform: rotate(180deg);
}

.group .material-icons-outlined {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
}

/* 按钮点击时的弹性动画 */
@keyframes button-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.group:active {
  animation: button-bounce 0.2s ease-out;
}

/* 文字平移动画的微调 */
.translate-x-0\.5 {
  transform: translateX(2px);
}

/* 为学校卡片添加悬浮时的微妙动画增强 */
.school-card {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.school-card:hover {
  transform: translateY(-2px) scale(1.005);
}

/* 地区内容容器的基础样式 */
.region-content {
  will-change: height, opacity, transform;
}
</style> 