<template>
  <div class="client-list-page max-w-7xl mx-auto">
    <!-- 面包屑导航 -->
    <div class="mb-6">
      <!-- 如果 Breadcrumb 组件不存在，请创建或注释此行 -->
      <!-- <Breadcrumb /> -->
    </div>

    <!-- 页面标题 -->
    <div class="mb-4">
      <h2 class="text-xl font-medium">客户档案</h2>
      <p class="mt-1 text-sm text-gray-500">服务中 - 所有当前服务的客户都在这里哦</p>
    </div>

    <!-- 标签页切换 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex gap-4">
        <button
          type="button"
          class="px-4 py-2 rounded-full text-sm transition-all duration-300 transform hover:scale-105 active:scale-95 status-tab-btn"
          :class="[
            activeTab === 'active'
              ? 'bg-primary text-white shadow-lg'
              : 'text-gray-600 hover:bg-gray-100 hover:shadow-md'
          ]"
          @click="handleTabSwitch('active')"
        >
          服务中
        </button>
        <button
          type="button"
          class="px-4 py-2 rounded-full text-sm transition-all duration-300 transform hover:scale-105 active:scale-95 status-tab-btn"
          :class="[
            activeTab === 'archived'
              ? 'bg-primary text-white shadow-lg'
              : 'text-gray-600 hover:bg-gray-100 hover:shadow-md'
          ]"
          @click="handleTabSwitch('archived')"
        >
          已归档
        </button>
      </div>
      
      <!-- 创建客户按钮 -->
      <button
        type="button"
        class="px-4 py-2 rounded-full text-sm transition-all duration-300 transform hover:scale-105 active:scale-95 bg-primary text-white hover:bg-primary-dark"
        @click="handleCreateClient"
      >
        + 创建客户
      </button>
    </div>

    <!-- 搜索和操作区 -->
    <div class="flex justify-between items-center mb-4">
      <div class="flex gap-3">
        <div class="relative">
          <el-input
            v-model="searchQuery"
            placeholder="填写客户名称"
            class="w-60"
            clearable
            @input="handleSearch"
            @clear="handleClearSearch"
            :suffix-icon="searching ? 'Loading' : ''"
          >
            <template #prefix>
              <span class="material-icons-outlined text-gray-400">search</span>
            </template>
          </el-input>

          <!-- 快速定校书提示 -->
          <div
            v-if="showQuickSchoolBookOption"
            class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 p-3"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <span class="material-icons-outlined text-[#4F46E5] mr-2">school</span>
                <span class="text-sm text-gray-700">为 "{{ searchQuery.trim() }}" 创建快速定校书？</span>
              </div>
              <div class="flex gap-2">
                <el-button size="small" @click="hideQuickSchoolBookOption">取消</el-button>
                <el-button size="small" type="primary" @click="createQuickSchoolBook">创建</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户列表 -->
    <div class="bg-white rounded-lg">
      <el-table
        :data="clientList"
        style="width: 100%"
        :header-cell-style="{
          background: '#fff',
          color: '#909399',
          fontWeight: 400,
          fontSize: '13px',
          height: '48px'
        }"
        :cell-style="{
          fontSize: '14px',
          color: '#606266'
        }"
        @row-click="handleView"
        class="client-table"
      >
        <el-table-column label="客户" min-width="240">
          <template #default="{ row }">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-primary bg-opacity-10 flex items-center justify-center text-primary mr-3">
                {{ row.name?.charAt(0)?.toUpperCase() || '?' }}
              </div>
              <div>
                <div class="font-medium">{{ row.name }}</div>
                <div class="text-sm text-gray-500">{{ row.school || '暂无学校信息' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="居住城市" min-width="120" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="{ row }">
            {{ genderMap[row.gender] }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" min-width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="updatedAt" label="修改时间" min-width="180">
          <template #default="{ row }">
            {{ formatRelativeTime(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-4" @click.stop>
              <el-dropdown trigger="click">
                <el-button
                  class="icon-btn bg-gray-100 hover:bg-gray-200 transition-colors !border-0"
                >
                  <span class="material-icons-outlined text-gray-500">more_horiz</span>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleView(row)" class="view-item">
                      <i class="el-icon-view mr-1"></i>查看
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleArchive(row)" class="archive-item">
                      <i class="el-icon-folder mr-1"></i>{{ row.is_archived ? '取消归档' : '归档' }}
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="handleDelete(row)" class="delete-item">
                      <i class="el-icon-delete mr-1"></i>删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-end py-0 px-6 -mt-2 pb-4">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next"
          @current-change="handleCurrentChange"
          background
          class="pagination-custom"
        />
      </div>
    </div>

    <!-- 创建客户弹窗 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建客户"
      width="520px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div class="px-2">
        <el-form ref="createFormRef" :model="createForm" :rules="createFormRules" label-width="80px">
          <el-form-item label="昵称" prop="nickname" :required="!createForm.enableAI">
            <el-input 
              v-model="createForm.nickname" 
              :placeholder="createForm.enableAI ? '智能建档将自动识别姓名（可选）' : '请输入客户昵称'" 
            />
          </el-form-item>
          <el-form-item label="智能建档">
            <div class="flex items-center gap-3">
              <el-switch v-model="createForm.enableAI" />
              <button
                v-if="createForm.enableAI"
                type="button"
                @click="handleDownloadTemplate"
                class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium flex items-center"
              >
                <span class="material-icons-outlined text-sm mr-1">download</span>
                下载模板
              </button>
            </div>
          </el-form-item>

          <!-- 优化的文件上传区域 -->
          <div v-if="createForm.enableAI" class="mt-4">
            <!-- 上传区域 -->
            <div 
              class="upload-area"
              :class="{
                'upload-area--dragover': isDragOver,
                'upload-area--has-file': uploadedFiles.length > 0,
                'upload-area--error': uploadError
              }"
              @dragover.prevent="handleDragOver"
              @dragleave.prevent="handleDragLeave"
              @drop.prevent="handleDrop"
              @click="triggerFileInput"
            >
              <!-- 隐藏的文件输入 -->
              <input
                ref="fileInputRef"
                type="file"
                accept=".pdf,.docx,.txt"
                @change="handleFileSelect"
                class="hidden"
              />
              
              <!-- 上传状态显示 -->
              <div v-if="uploadedFiles.length === 0" class="upload-content">
                <div class="upload-icon-container">
                  <div class="upload-icon-wrapper">
                    <span class="material-icons-outlined upload-icon">cloud_upload</span>
                    <div class="upload-icon-pulse"></div>
                  </div>
                </div>
                
                <div class="upload-text">
                  <h4 class="upload-title">
                    {{ isDragOver ? '松开鼠标即可上传' : '拖拽简历文件到此处' }}
                  </h4>
                  <p class="upload-subtitle">
                    或者 <span class="upload-link">点击选择文件</span>
                  </p>
                </div>
                
                <div class="upload-info">
                  <div class="upload-formats">
                    <span class="format-tag">PDF</span>
                    <span class="format-tag">DOCX</span>
                    <span class="format-tag">TXT</span>
                  </div>
                  <p class="upload-limit">文件大小不超过 10MB</p>
                </div>
                
                <div class="upload-tip">
                  <span class="material-icons-outlined text-xs mr-1">lightbulb</span>
                  没有素材？点击上方"下载模板"获取模板
                </div>
              </div>

              <!-- 文件预览区域 -->
              <div v-else class="file-preview">
                <div class="file-preview-content">
                  <div class="file-icon-container">
                    <span class="material-icons-outlined file-icon" :class="getFileIconClass(uploadedFiles[0])">
                      {{ getFileIcon(uploadedFiles[0]) }}
                    </span>
                  </div>
                  
                  <div class="file-details">
                    <div class="file-name">{{ uploadedFiles[0].name }}</div>
                    <div class="file-meta">
                      <span class="file-size">{{ formatFileSize(uploadedFiles[0].size) }}</span>
                      <span class="file-status">
                        <span class="material-icons-outlined text-xs mr-1 text-green-500">check_circle</span>
                        已选择
                      </span>
                    </div>
                  </div>
                  
                  <button
                    type="button"
                    @click.stop="handleFileRemove"
                    class="remove-file-btn"
                    title="移除文件"
                  >
                    <span class="material-icons-outlined">close</span>
                  </button>
                </div>
                
                <div class="file-preview-footer">
                  <div class="flex items-center text-xs text-blue-600">
                    <span class="material-icons-outlined text-xs mr-1">smart_toy</span>
                    点击"确认创建"开始智能建档分析
                  </div>
                </div>
              </div>
            </div>

            <!-- 错误提示 -->
            <div v-if="uploadError" class="upload-error">
              <span class="material-icons-outlined text-sm mr-2">error</span>
              {{ uploadError }}
            </div>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmCreate" :loading="creating">
            确认创建
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="p-4">
        <p class="text-gray-600">确定要删除该客户档案吗？此操作不可恢复。</p>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="handleConfirmDelete" :loading="deleting">
            确认删除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 归档确认对话框 -->
    <el-dialog
      v-model="archiveDialogVisible"
      :title="selectedClient?.is_archived ? '确认取消归档' : '确认归档'"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="p-4">
        <p class="text-gray-600">{{ selectedClient?.is_archived ? '确定要取消归档该客户吗？' : '确定要归档该客户吗？' }}</p>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="archiveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmArchive" :loading="archiving">
            确认{{ selectedClient?.is_archived ? '取消归档' : '归档' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 智能建档处理状态弹窗 -->
    <el-dialog
      v-model="processingDialogVisible"
      title="智能建档处理中"
      width="500px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="processing-dialog"
    >
      <div class="py-4">
        <div class="flex flex-col items-center justify-center">
          <!-- 状态图标 -->
          <div class="w-16 h-16 rounded-full flex items-center justify-center mb-4"
               :class="[
                 processingStatus === 'uploading' || processingStatus === 'processing' 
                   ? 'bg-primary text-white' 
                   : processingStatus === 'success'
                   ? 'bg-green-500 text-white'
                   : processingStatus === 'error'
                   ? 'bg-red-500 text-white'
                   : 'bg-primary bg-opacity-10 text-primary'
               ]">
            <span v-if="processingStatus === 'uploading'" class="material-icons-outlined text-2xl animate-spin">sync</span>
            <span v-else-if="processingStatus === 'processing'" class="material-icons-outlined text-2xl animate-spin">auto_awesome</span>
            <span v-else-if="processingStatus === 'success'" class="material-icons-outlined text-2xl">check_circle</span>
            <span v-else-if="processingStatus === 'error'" class="material-icons-outlined text-2xl">error</span>
          </div>

          <!-- 状态标题 -->
          <h3 class="text-lg font-medium text-gray-800 mb-2">{{ getStatusTitle }}</h3>

          <!-- 状态描述 -->
          <p class="text-gray-600 text-center mb-4">{{ getStatusDescription }}</p>

          <!-- 进度指示器 -->
          <div v-if="['uploading', 'processing'].includes(processingStatus)" class="w-full max-w-xs mb-4">
            <el-progress
              :percentage="processingProgress"
              :stroke-width="8"
              :show-text="false"
              color="#6366f1"
            ></el-progress>
            <div class="text-sm text-gray-500 text-center mt-2">{{ processingProgressText }}</div>
          </div>

        </div>
      </div>

      <!-- 按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <el-button
            v-if="processingStatus === 'error'"
            @click="processingDialogVisible = false"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 快速定校书浮窗 -->
    <!-- 右侧定校书按钮 -->
    <div
      v-if="quickClient && clientPrograms.size > 0"
      class="fixed right-0 z-40"
      :style="{ top: `calc(50% - ${64}px)` }"
    >
      <button
        @click="toggleSchoolBookSidebar"
        class="school-book-btn bg-white border border-gray-200 rounded-l-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group active:scale-95 active:shadow-md"
      >
        <div class="flex flex-col items-center justify-center py-6 px-3 space-y-2">
          <span class="material-icons-outlined text-[#4F46E5] text-xl group-hover:scale-110 transition-transform duration-300">
            school
          </span>
          <div class="flex flex-col items-center space-y-1">
            <span class="text-xs font-medium text-gray-700 group-hover:text-[#4F46E5] transition-colors duration-300 whitespace-nowrap">
              查看
            </span>
            <span class="text-xs font-medium text-gray-700 group-hover:text-[#4F46E5] transition-colors duration-300 whitespace-nowrap">
              定校书
            </span>
          </div>
          <div class="w-5 h-5 bg-[#4F46E5] text-white rounded-full flex items-center justify-center text-xs font-bold group-hover:scale-110 transition-transform duration-300">
            {{ clientPrograms.size }}
          </div>
        </div>
      </button>
    </div>

    <!-- 右侧定校书侧边栏 -->
    <transition
      name="sidebar-slide"
      @enter="onSidebarEnter"
      @after-enter="onSidebarAfterEnter"
      @leave="onSidebarLeave"
      @after-leave="onSidebarAfterLeave"
    >
      <div
        v-if="quickClient && showSchoolBookSidebar"
        class="fixed right-0 w-96 bg-white border-l border-gray-200 shadow-xl z-50 flex flex-col sidebar-container"
        :style="{ top: '64px', height: 'calc(100vh - 64px)' }"
      >
        <!-- 左侧拉回按钮 -->
        <button
          @click="toggleSchoolBookSidebar"
          class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-full bg-white border border-gray-200 border-r-0 rounded-l-lg shadow-lg hover:shadow-xl transition-all duration-300 p-2 z-10 hover:bg-gray-50 active:scale-95 active:shadow-md group"
          style="border-top-right-radius: 0; border-bottom-right-radius: 0;"
          title="收起定校书"
        >
          <span class="material-icons-outlined text-gray-600 text-lg group-hover:scale-110 transition-transform duration-300">chevron_right</span>
        </button>

        <!-- 侧边栏头部 -->
        <div class="flex-shrink-0 px-6 py-5 bg-gradient-to-r from-[#4F46E5]/5 to-[#7C3AED]/5 border-b border-gray-100">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-11 h-11 rounded-full bg-[#4F46E5] text-white flex items-center justify-center text-base font-semibold mr-4">
                {{ quickClient.name?.charAt(0)?.toUpperCase() || '?' }}
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">{{ quickClient.name }}的定校书</h3>
                <p class="text-xs text-gray-500 mt-1">{{ clientPrograms.size }} 个院校项目</p>
              </div>
            </div>
            <button
              @click="toggleSchoolBookSidebar"
              class="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-all duration-300 active:scale-95 active:bg-gray-200 group"
            >
              <span class="material-icons-outlined text-lg group-hover:scale-110 transition-transform duration-300">close</span>
            </button>
          </div>
        </div>

        <!-- 侧边栏内容 -->
        <div class="flex-1 overflow-y-auto school-book-sidebar" style="max-height: calc(100vh - 64px - 120px - 120px);">
          <div class="p-6">
            <div v-if="clientProgramsList.length === 0" class="text-center py-8">
              <div class="text-gray-400 mb-4">
                <span class="material-icons-outlined text-4xl">school</span>
              </div>
              <h5 class="text-base font-medium text-gray-700 mb-2">暂无目标专业</h5>
              <p class="text-sm text-gray-500 mb-4">前往选校匹配页面添加目标专业</p>
              <el-button
                type="primary"
                @click="goToSchoolAssistant"
                size="small"
              >
                <span class="material-icons-outlined text-sm mr-1">add</span>
                前往选校匹配
              </el-button>
            </div>

            <transition-group
              v-else
              name="program-item"
              tag="div"
              class="space-y-3"
              @enter="onProgramEnter"
              @leave="onProgramLeave"
            >
              <div
                v-for="(program, index) in clientProgramsList"
                :key="program.id"
                class="bg-white rounded-xl border border-gray-100 hover:border-gray-200 hover:shadow-sm transition-all duration-300 p-4 program-item"
                :data-index="index"
              >
                <div class="flex items-start justify-between">
                  <!-- 学校Logo -->
                  <div class="flex-shrink-0 mr-3 flex items-center" style="height: 52px;">
                    <img
                      v-if="program.school_logo_url"
                      :src="program.school_logo_url"
                      :alt="program.school_name_cn"
                      class="w-12 h-12 object-contain rounded-lg border border-gray-100"
                      @error="$event.target.style.display='none'"
                    />
                    <div
                      v-else
                      class="w-12 h-12 bg-gradient-to-br from-[#4F46E5] to-[#7C3AED] rounded-lg flex items-center justify-center text-white text-sm font-bold"
                    >
                      {{ program.school_name_cn?.charAt(0) || '?' }}
                    </div>
                  </div>

                  <!-- 专业信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between">
                      <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-semibold text-gray-800 truncate">{{ program.school_name_cn }}</h4>
                        <p class="text-xs text-gray-600 mt-1 line-clamp-2">{{ program.program_name_cn }}</p>
                        <div class="flex items-center mt-2 space-x-2">
                          <span v-if="program.school_qs_rank" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-[#4F46E5]/10 text-[#4F46E5]">
                            QS #{{ program.school_qs_rank }}
                          </span>
                          <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                            {{ program.school_region }}
                          </span>
                        </div>
                      </div>

                      <!-- 移除按钮 -->
                      <button
                        @click="removeFromSchoolBook(program.program_id || program.id)"
                        class="ml-2 p-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-all duration-200 active:scale-95 group"
                        title="从定校书中移除"
                      >
                        <span class="material-icons-outlined text-sm group-hover:scale-110 transition-transform duration-200">close</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </transition-group>
          </div>
        </div>

        <!-- 侧边栏底部操作 -->
        <div class="flex-shrink-0 px-6 py-4 bg-gray-50 border-t border-gray-100">
          <div class="flex gap-3">
            <el-button
              size="small"
              @click="clearQuickClientCache"
              class="flex-1"
            >
              <span class="material-icons-outlined text-sm mr-1">clear</span>
              清空定校书
            </el-button>
            <el-button
              size="small"
              type="primary"
              :loading="isExporting"
              @click="handleExportSchoolBook"
              class="flex-1"
            >
              <span class="material-icons-outlined text-sm mr-1">download</span>
              导出Excel
            </el-button>
          </div>
        </div>
      </div>
    </transition>

    <!-- 侧边栏遮罩层 -->
    <transition name="overlay-fade">
      <div
        v-if="showSchoolBookSidebar"
        @click="toggleSchoolBookSidebar"
        class="fixed inset-0 bg-black bg-opacity-25 z-40 overlay-backdrop"
      ></div>
    </transition>
  </div>
</template>

<script setup lang="ts">
/// <reference types="vite/client" />
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
// 导入API函数
import {
  getClientList,
  addClient,
  deleteClient,
  toggleClientArchive,
  extractBackgroundFromFile,
  addClientProgram,
  removeClientProgram,
  getClientPrograms,
  exportClientSchoolBook
} from '@/api/client'
// 如果 Breadcrumb 组件不存在，请创建或注释此行
// import Breadcrumb from '@/components/common/Breadcrumb.vue'

// 配置 dayjs 支持时区转换和相对时间显示
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(relativeTime)
dayjs.locale('zh-cn') // 使用中文

// 设置默认时区为北京时间
dayjs.tz.setDefault('Asia/Shanghai')

// 路由实例和状态管理
const router = useRouter()
const authStore = useAuthStore()

// 客户数据类型定义
interface Client {
  id_hashed: string
  name: string
  location: string
  gender: 'male' | 'female' | 'unknown'
  serviceType: 'undergraduate' | 'master'
  is_archived: boolean
  createdAt: string
  updatedAt: string
  phone?: string
  email?: string
}

// 解析后的客户信息类型
interface ParsedClientInfo {
  name?: string
  gender?: string
  dob?: string
  phone?: string
  email?: string
  location?: string
  education?: Array<{
    school: string
    major: string
    period: string
    degree?: string
    gpa?: string
  }>
  academic?: Array<{
    title?: string
    institution?: string
    date?: string
    description?: string
  }>
  work?: Array<{
    company?: string
    position?: string
    period?: string
    description?: string
  }>
  activities?: Array<{
    name?: string
    role?: string
    period?: string
    description?: string
  }>
  skills?: Array<{
    languageproficiency?: string
    [key: string]: any
  }>
  awards?: Array<{
    name?: string
    level?: string
    date?: string
    description?: string
  }>
}

// 状态定义
const activeTab = ref('active')
const searchQuery = ref('')
const filterService = ref('')
const currentPage = ref(1)
const pageSize = ref(15) // 固定每页显示15条
const total = ref(0)
const clientList = ref<Client[]>([])
const createDialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const archiveDialogVisible = ref(false) // 新增归档对话框状态
const creating = ref(false)
const deleting = ref(false)
const archiving = ref(false) // 新增归档中状态
const selectedClient = ref<Client | null>(null)
const processingDialogVisible = ref(false)
const processingStatus = ref('uploading') // 'uploading', 'processing', 'success', 'error'
const processingProgress = ref(0)
const createdClientId = ref<string | null>(null)
const searching = ref(false) // 添加搜索状态指示变量
const isInitialLoading = ref(true) // 初始加载状态

// 快速定校书相关状态
const showQuickSchoolBookOption = ref(false)
const quickClient = ref(null) // 临时客户信息
const showSchoolBookSidebar = ref(false)
const clientPrograms = ref(new Set()) // 存储客户已有的专业ID集合
const clientProgramsList = ref([]) // 客户专业列表
const isExporting = ref(false)

// 表单数据
const createForm = ref({
  nickname: '',
  enableAI: false
})

// 表单验证规则
const createFormRules = {
  nickname: [
    { 
      required: true, 
      message: '请输入客户昵称',
      trigger: []  // 空数组表示不自动触发验证
    },
    { 
      min: 1, 
      max: 50, 
      message: '昵称长度应在1-50个字符之间',
      trigger: []  // 空数组表示不自动触发验证
    }
  ]
}

// 表单引用
const createFormRef = ref()

// 文件上传相关状态
const uploadedFiles = ref<File[]>([])
const parsedClientInfo = ref<ParsedClientInfo | null>(null)
const uploading = ref(false)
const isDragOver = ref(false)
const uploadError = ref('')
const fileInputRef = ref<HTMLInputElement | null>(null)
const processing = ref(false)

// 映射表
const genderMap = {
  male: '男',
  female: '女',
  unknown: '未知'
}

const serviceTypeMap = {
  undergraduate: '本科申请',
  master: '硕士申请'
}

// 日期格式化（创建时间 YYYY-MM-DD HH:mm）
const formatDateTime = (date: string) => {
  if (!date) return '暂无记录'
  // 由于后端已经存储为北京时间，直接格式化即可，不需要时区转换
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 相对时间格式化（修改时间 几天前、几小时前等）
const formatRelativeTime = (date: string) => {
  if (!date) return '暂无更新'

  // 由于后端已经存储为北京时间，直接格式化即可，不需要时区转换
  const updateTime = dayjs(date)
  const now = dayjs()

  // 计算时间差（分钟）
  const diffMinutes = now.diff(updateTime, 'minute')

  // 根据时间差显示不同格式
  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (now.diff(updateTime, 'hour') < 24) {
    return `${now.diff(updateTime, 'hour')}小时前`
  } else if (now.diff(updateTime, 'day') < 30) {
    return `${now.diff(updateTime, 'day')}天前`
  } else {
    return formatDateTime(date)
  }
}

// 文件移除处理
const handleFileRemove = () => {
  uploadedFiles.value = []
  parsedClientInfo.value = null
}

// 下载智能建档模板（存放于 public/templates 目录）
const handleDownloadTemplate = () => {
  // 你可以根据需要提供多个模板；这里示例下载“客户智能建档模板.docx”
  const link = document.createElement('a')
  link.href = '/templates/客户信息采集表.docx'
  link.download = '客户信息采集表.docx'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 文件大小格式化
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 智能背景提取函数
const handleIntelligentExtraction = async (file: File) => {
  try {
    // 显示处理状态弹窗
    processingDialogVisible.value = true
    processingStatus.value = 'uploading'
    processingProgress.value = 20
    
    // 关闭创建对话框
    createDialogVisible.value = false
    
    // 更新状态为AI处理中
    processingStatus.value = 'processing'
    processingProgress.value = 40
    
    // 模拟进度增长
    const progressInterval = setInterval(() => {
      if (processingProgress.value < 90) {
        processingProgress.value += 5
      }
    }, 2000)
    
    try {
      // 调用新的背景提取API
      const result = await extractBackgroundFromFile(file)
      
      // 清除进度定时器
      clearInterval(progressInterval)
      processingProgress.value = 100
      
      // 处理成功
      processingStatus.value = 'success'
      
      // 检查返回的client_id
      if (result.client_id) {
        createdClientId.value = result.client_id
        
        // 刷新客户列表
        await fetchClients()
        
        ElMessage.success('智能建档成功！')
      } else {
        throw new Error('未返回客户ID')
      }
      
    } catch (error: any) {
      clearInterval(progressInterval)
      console.error('智能建档失败:', error)
      
      processingStatus.value = 'error'
      
      // 错误处理
      let errorMessage = '智能建档失败，请稍后重试'
      
      if (error.response) {
        const { status, data } = error.response
        switch (status) {
          case 400:
            errorMessage = '文件格式不支持或内容无法解析'
            break
          case 413:
            errorMessage = '文件太大，请上传小于10MB的文件'
            break
          case 500:
            errorMessage = 'AI解析失败，请检查文件内容或稍后重试'
            break
          default:
            errorMessage = data.detail || errorMessage
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络状态'
      }
      
      ElMessage.error(errorMessage)
    }
  } catch (error) {
    console.error('Unexpected error:', error)
    processingStatus.value = 'error'
    ElMessage.error('处理失败，请稍后重试')
  }
}



// 创建一个防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null
  return function(...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...args)
    }, delay) as unknown as number
  }
}

// 使用防抖函数包装搜索处理逻辑
const debouncedSearch = debounce(() => {
  currentPage.value = 1 // 搜索时重置为第一页
  fetchClients()
}, 500) // 500ms的延迟

const handleSearch = () => {
  // 设置搜索状态
  searching.value = true
  // 调用防抖处理函数
  debouncedSearch()

  // 如果有输入内容，立即显示快速定校书选项
  if (searchQuery.value.trim()) {
    showQuickSchoolBookOption.value = true
  } else {
    showQuickSchoolBookOption.value = false
  }
}

const handleClearSearch = () => {
  searchQuery.value = ''
  hideQuickSchoolBookOption()
  clearQuickClientCache()
  handleSearch()
}

// 检查是否显示快速定校书选项
const checkQuickSchoolBookOption = () => {
  // 有输入内容就显示快速定校书选项
  showQuickSchoolBookOption.value = searchQuery.value.trim().length > 0
}

const handleFilter = () => {
  currentPage.value = 1 // 筛选时重置为第一页
  fetchClients()
}

const handleCurrentChange = async (val: number) => {
  currentPage.value = val
  await fetchClients() // 确保等待数据加载完成
}

const handleCreateClient = () => {
  // 重置表单和状态
  createForm.value.nickname = ''
  createForm.value.enableAI = false
  uploadedFiles.value = []
  parsedClientInfo.value = null
  createDialogVisible.value = true
}

// 新的文件处理逻辑
const processFile = (file: File) => {
  // 清除之前的错误
  uploadError.value = ''
  
  // 文件格式验证
  const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
  if (!allowedTypes.includes(file.type)) {
    uploadError.value = '请上传 PDF、DOCX 或 TXT 格式的文件'
    return false
  }
  
  // 文件大小验证（10MB）
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    uploadError.value = '文件大小不能超过 10MB'
    return false
  }
  
  uploadedFiles.value = [file]
  ElMessage.success('文件已选择，点击确认创建开始智能建档')
  return true
}

// 文件选择处理
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    processFile(target.files[0])
  }
  // 清空 input 值，允许重复选择相同文件
  target.value = ''
}

// 拖拽处理
const handleDragOver = () => {
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

// 触发文件选择
const triggerFileInput = () => {
  if (uploadedFiles.value.length === 0) {
    fileInputRef.value?.click()
  }
}

// 获取文件图标
const getFileIcon = (file: File) => {
  if (file.type.includes('pdf')) return 'picture_as_pdf'
  if (file.type.includes('word') || file.type.includes('document')) return 'description'
  return 'attach_file'
}

// 获取文件图标样式类
const getFileIconClass = (file: File) => {
  if (file.type.includes('pdf')) return 'text-red-500'
  if (file.type.includes('word') || file.type.includes('document')) return 'text-blue-500'
  return 'text-gray-500'
}

// 兼容原有的文件变化处理（保持向后兼容）
const handleFileChange = (file: any, fileList: any[]) => {
  if (file && file.raw) {
    processFile(file.raw)
  }
}

const handleConfirmCreate = async () => {
  // 如果没有启用智能建档，需要验证表单
  if (!createForm.value.enableAI) {
    try {
      await createFormRef.value.validate()
    } catch (error) {
      // 表单验证失败，会自动显示中文错误信息
      return
    }
  }

  // 如果启用智能建档，但没有上传文件，提示错误
  if (createForm.value.enableAI && uploadedFiles.value.length === 0) {
    ElMessage.warning('请上传至少一个文件用于智能建档');
    return;
  }

  creating.value = true;
  try {
    if (createForm.value.enableAI && uploadedFiles.value.length > 0) {
      // 智能建档模式 - 现在开始处理文件
      await handleIntelligentExtraction(uploadedFiles.value[0]);
      return;
    } else {
      // 手动创建客户模式
      const clientData = {
        name: createForm.value.nickname || '未命名客户',
        gender: 'unknown',
        location: '未知',
        service_type: 'undergraduate'
      };

      try {
        // 调用API创建客户
        const response = await addClient(clientData);

        // 添加到客户列表
        const newClient = {
          id_hashed: response.id_hashed,
          name: response.name,
          location: response.location || '未知',
          gender: response.gender === 'male' ? 'male' : (response.gender === 'female' ? 'female' : 'unknown'),
          serviceType: response.service_type || 'undergraduate',
          phone: response.phone,
          email: response.email,
          is_archived: response.is_archived,
          createdAt: response.created_at,
          updatedAt: response.updated_at || response.created_at
        };

        clientList.value.unshift(newClient as Client);
        total.value += 1;

        ElMessage.success('客户创建成功');
        createDialogVisible.value = false;

        // 重置表单
        createForm.value.nickname = '';
        createForm.value.enableAI = false;
        uploadedFiles.value = [];
        parsedClientInfo.value = null;
      } catch (error) {
        console.error('创建客户失败:', error);
        ElMessage.error('创建客户失败');
        throw error;
      }
    }
  } catch (error) {
    console.error(error);
  } finally {
    creating.value = false;
  }
}

const handleView = (row: any) => {
  router.push(`/clients/${row.id_hashed}`)
}

const handleArchive = (row: any) => {
  selectedClient.value = row
  archiveDialogVisible.value = true
}

const handleConfirmArchive = async () => {
  if (!selectedClient.value) return
  archiving.value = true
  try {
    // 调用API切换客户归档状态
    await toggleClientArchive(selectedClient.value.id_hashed)

    // 重新加载客户列表
    fetchClients()
    archiveDialogVisible.value = false
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    archiving.value = false
  }
}

const handleDelete = (row: any) => {
  selectedClient.value = row
  deleteDialogVisible.value = true
}

const handleConfirmDelete = async () => {
  if (!selectedClient.value) return
  deleting.value = true
  try {
    // 调用API删除客户
    await deleteClient(selectedClient.value.id_hashed)

    // 从列表中移除该客户
    clientList.value = clientList.value.filter(client => client.id_hashed !== selectedClient.value?.id_hashed)
    total.value = Math.max(0, total.value - 1)

    // ElMessage.success('删除成功')
    deleteDialogVisible.value = false
  } catch (error) {
    console.error('删除客户失败:', error)

    // ElMessage.error('删除失败')
  } finally {
    deleting.value = false
  }
}

const fetchClients = async (silent = false) => {
  try {
    // 设置搜索状态（静默模式下不显示搜索状态）
    if (!silent) {
      searching.value = true
    }
    
    // 固定每页显示的条数为15
    const fixedPageSize = 15;

    // 构建查询参数
    const params: {
      page: number;
      per_page: number;
      search?: string;
      service_type?: string;
      is_archived: boolean;
      sort?: string;
    } = {
      page: currentPage.value,
      per_page: fixedPageSize,
      sort: '-updated_at', // 使用负号表示降序，确保最新更新的在最前面
      is_archived: activeTab.value === 'archived' // 根据当前标签页设置是否获取已归档客户
    }

    // 如果有搜索关键词，添加到参数中
    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    // 如果有服务类型筛选，添加到参数中
    if (filterService.value) {
      params.service_type = filterService.value
    }

    try {
      // 调用API获取客户列表
      const response = await getClientList(params)

      // 更新客户列表和总数
      if (Array.isArray(response)) {
        // 如果是数组，说明是所有数据
        const sortedData = [...response].sort((a, b) => {
          const dateA = dayjs.utc(a.updated_at || a.created_at)
          const dateB = dayjs.utc(b.updated_at || b.created_at)
          return dateB.valueOf() - dateA.valueOf() // 降序排序，最新的在前面
        })

        total.value = sortedData.length
        const start = (currentPage.value - 1) * fixedPageSize
        clientList.value = sortedData.slice(start, start + fixedPageSize).map((client: any) => ({
          id_hashed: client.id_hashed,
          name: client.name,
          location: client.location || '未知',
          gender: client.gender === 'male' ? 'male' : (client.gender === 'female' ? 'female' : 'unknown'),
          serviceType: client.service_type || 'undergraduate',
          phone: client.phone,
          email: client.email,
          school: client.school || '暂无学校信息',
          is_archived: client.is_archived,
          createdAt: client.created_at,
          updatedAt: client.updated_at || client.created_at
        }))
      } else if (response.items && Array.isArray(response.items)) {
        // 如果是分页对象
        total.value = response.total || response.items.length

        // 确保数据按更新时间降序排序
        const sortedItems = [...response.items].sort((a, b) => {
          const dateA = dayjs.utc(a.updated_at || a.created_at)
          const dateB = dayjs.utc(b.updated_at || b.created_at)
          return dateB.valueOf() - dateA.valueOf() // 降序排序，最新的在前面
        })

        clientList.value = sortedItems.map((client: any) => ({
          id_hashed: client.id_hashed,
          name: client.name,
          location: client.location || '未知',
          gender: client.gender === 'male' ? 'male' : (client.gender === 'female' ? 'female' : 'unknown'),
          serviceType: client.service_type || 'undergraduate',
          phone: client.phone,
          email: client.email,
          school: client.school || '暂无学校信息',
          is_archived: client.is_archived,
          createdAt: client.created_at,
          updatedAt: client.updated_at || client.created_at
        }))
      } else {
        console.error('API返回的数据格式不正确:', response)
        clientList.value = []
        total.value = 0
      }

      // 根据当前标签页筛选数据
      if (activeTab.value === 'archived') {
        // 注意：后端API需要支持归档状态筛选
        // 可以通过添加参数 params.archived = true 实现
      }
    } catch (error) {
      if (!silent) {
        console.error('API调用失败:', error)
      }

      // 显示错误并清空列表
      clientList.value = []
      total.value = 0
      throw error
    } finally {
      // 重置搜索状态
      if (!silent) {
        searching.value = false
      }
      // 检查是否显示快速定校书选项
      checkQuickSchoolBookOption()
    }
  } catch (error) {
    if (!silent) {
      // ElMessage.error('获取客户列表失败')
      console.error(error)
      // 确保错误时也重置搜索状态
      searching.value = false
    }
    // 检查是否显示快速定校书选项
    checkQuickSchoolBookOption()
  }
}

// 监听标签页变化
watch(() => activeTab.value, () => {
  currentPage.value = 1 // 切换标签时重置页码
  fetchClients()
})

// 生命周期钩子
onMounted(async () => {
  try {
    // 并行加载客户数据和快速客户缓存
    await fetchClients()

    // 加载快速客户缓存
    const cachedClient = loadQuickClientCache()
    if (cachedClient) {
      quickClient.value = cachedClient
      // 如果有缓存的客户且有专业，显示定校书按钮
      if (clientProgramsList.value.length > 0) {
        // 不自动显示浮窗，让用户手动打开
      }
    }
    
    console.log('客户列表界面数据初始化完成')
  } catch (error) {
    console.error('客户列表界面初始化失败:', error)
  } finally {
    // 初始加载完成
    isInitialLoading.value = false
  }
})

// 页面卸载时清理缓存
onUnmounted(() => {
  // 清除快速客户缓存
  clearQuickClientCache()
})

// 监听身份变化，自动刷新数据
watch(
  () => authStore.currentIdentity,
  async (newIdentity, oldIdentity) => {
    // 如果有新的身份信息，就刷新数据
    if (newIdentity) {
      // 检查是否是真正的身份变化
      const isIdentityChange = oldIdentity && newIdentity && 
        (newIdentity.identity_type !== oldIdentity.identity_type || 
         newIdentity.organization_id !== oldIdentity.organization_id);
      
      if (isIdentityChange) {
        console.log('客户列表检测到身份切换，自动刷新数据:', {
          old: oldIdentity,
          new: newIdentity
        });
        
        // 身份切换时静默刷新数据
        try {
          await fetchClients(true); // 静默刷新
        } catch (error) {
          console.warn('客户列表身份切换后数据刷新失败:', error);
        }
      }
    }
  },
  { deep: true } // 深度监听对象变化
);

// 自动刷新数据的方法
const refreshAllData = async (silent = true) => {
  try {
    await fetchClients(silent);
    
    if (!silent) {
      console.log('客户列表数据刷新完成');
    }
  } catch (error) {
    if (!silent) {
      console.error('客户列表数据刷新失败:', error);
    }
  }
};

// 监听分页变化
watch(currentPage, async (newPage) => {
  if (newPage > 0) {
    await fetchClients()
  }
})

// 监听搜索和筛选条件变化
watch([searchQuery, filterService], () => {
  currentPage.value = 1 // 重置到第一页
  fetchClients()
})

// 快速定校书相关方法
const hideQuickSchoolBookOption = () => {
  showQuickSchoolBookOption.value = false
}

const createQuickSchoolBook = () => {
  const clientName = searchQuery.value.trim()
  if (!clientName) {
    ElMessage.warning('请输入客户名称')
    return
  }

  // 创建临时客户对象
  const tempClient = {
    id_hashed: `temp_${Date.now()}`,
    name: clientName,
    isTemporary: true,
    created_at: new Date().toISOString()
  }

  // 设置为当前选中客户
  quickClient.value = tempClient

  // 缓存客户信息
  saveQuickClientCache(tempClient)

  // 隐藏选项提示
  hideQuickSchoolBookOption()

  // 显示定校书浮窗
  showSchoolBookSidebar.value = true

  // 初始化客户专业列表
  clientPrograms.value.clear()
  clientProgramsList.value = []

  ElMessage.success(`已为 ${clientName} 创建快速定校书`)
}

// 缓存管理
const saveQuickClientCache = (client) => {
  try {
    sessionStorage.setItem('quick_client_info', JSON.stringify(client))
  } catch (error) {
    console.error('保存客户缓存失败:', error)
  }
}

const loadQuickClientCache = () => {
  try {
    const cached = sessionStorage.getItem('quick_client_info')
    if (cached) {
      const client = JSON.parse(cached)
      quickClient.value = client

      // 加载专业列表缓存
      const programsCache = sessionStorage.getItem('quick_client_programs')
      if (programsCache) {
        clientProgramsList.value = JSON.parse(programsCache)
        clientPrograms.value = new Set(clientProgramsList.value.map(p => p.program_id || p.id))
      }

      return client
    }
  } catch (error) {
    console.error('加载客户缓存失败:', error)
  }
  return null
}

const clearQuickClientCache = () => {
  try {
    sessionStorage.removeItem('quick_client_info')
    sessionStorage.removeItem('quick_client_programs')
    quickClient.value = null
    clientPrograms.value.clear()
    clientProgramsList.value = []
    showSchoolBookSidebar.value = false
  } catch (error) {
    console.error('清除客户缓存失败:', error)
  }
}

const saveQuickProgramsCache = () => {
  try {
    sessionStorage.setItem('quick_client_programs', JSON.stringify(clientProgramsList.value))
  } catch (error) {
    console.error('保存专业缓存失败:', error)
  }
}

// 定校书浮窗相关方法
const isToggling = ref(false); // 防止快速点击的状态

const toggleSchoolBookSidebar = () => {
  // 防止快速点击导致的DOM访问错误
  if (isToggling.value) {
    return;
  }

  isToggling.value = true;

  // 使用nextTick确保DOM更新完成
  nextTick(() => {
    showSchoolBookSidebar.value = !showSchoolBookSidebar.value;

    // 延迟重置切换状态，避免动画期间的重复操作
    setTimeout(() => {
      isToggling.value = false;
    }, 300); // 与transition动画时间保持一致
  });
}

// 从定校书中移除专业
const removeFromSchoolBook = async (programId) => {
  if (!quickClient.value) return

  try {
    // 从集合中移除
    clientPrograms.value.delete(programId)

    // 从列表中移除
    clientProgramsList.value = clientProgramsList.value.filter(p =>
      (p.program_id || p.id) !== programId
    )

    // 更新缓存
    saveQuickProgramsCache()

    ElMessage.success('已从定校书中移除该专业')
  } catch (error) {
    console.error('移除专业失败:', error)
    ElMessage.error('移除失败，请稍后重试')
  }
}

// 导出定校书Excel文件
const handleExportSchoolBook = async () => {
  if (clientProgramsList.value.length === 0) {
    ElMessage.warning('请先添加目标专业，再导出定校书')
    return
  }

  if (!quickClient.value) {
    ElMessage.error('请先创建客户信息')
    return
  }

  try {
    isExporting.value = true

    // 对于临时客户，我们需要特殊处理
    // 这里可以调用一个特殊的API或者在前端生成Excel
    ElMessage.info('正在生成Excel文件...')

    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 创建文件名
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '')
    const filename = `${quickClient.value.name || '客户'}_定校书_${timestamp}.xlsx`

    ElMessage.success(`定校书Excel文件已生成：${filename}`)

  } catch (error) {
    console.error('导出定校书失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    isExporting.value = false
  }
}

// 侧边栏动画处理方法
const onSidebarEnter = (el) => {
  el.style.transform = 'translateX(100%)';
  el.style.opacity = '0';
  el.offsetHeight; // 强制重排
  el.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
  el.style.transform = 'translateX(0)';
  el.style.opacity = '1';
};

const onSidebarAfterEnter = (el) => {
  el.style.transition = '';
};

const onSidebarLeave = (el) => {
  el.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
  el.style.transform = 'translateX(100%)';
  el.style.opacity = '0';
};

const onSidebarAfterLeave = (el) => {
  el.style.transition = '';
  el.style.transform = '';
  el.style.opacity = '';
};

// 项目列表动画处理方法
const onProgramEnter = (el) => {
  const index = parseInt(el.dataset.index) || 0;
  el.style.opacity = '0';
  el.style.transform = 'translateX(-20px) scale(0.95)';
  el.style.transition = 'none';

  setTimeout(() => {
    el.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    el.style.opacity = '1';
    el.style.transform = 'translateX(0) scale(1)';
  }, index * 50);
};

const onProgramLeave = (el) => {
  el.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
  el.style.opacity = '0';
  el.style.transform = 'translateX(20px) scale(0.95)';
  el.style.height = el.offsetHeight + 'px';

  setTimeout(() => {
    el.style.height = '0';
    el.style.marginBottom = '0';
    el.style.paddingTop = '0';
    el.style.paddingBottom = '0';
  }, 150);
};

// 跳转到选校匹配页面
const goToSchoolAssistant = () => {
  // 保存当前快速客户信息到sessionStorage，以便在选校匹配页面使用
  if (quickClient.value) {
    saveQuickClientCache(quickClient.value)
  }

  // 跳转到选校匹配页面
  router.push('/school-assistant')
}

// 状态文字计算属性
const getStatusTitle = computed(() => {
  switch(processingStatus.value) {
    case 'uploading': return '正在上传文件';
    case 'processing': return '智能分析中';
    case 'success': return '智能建档成功';
    case 'error': return '处理出错';
    default: return '处理中';
  }
})

const getStatusDescription = computed(() => {
  switch(processingStatus.value) {
    case 'uploading': return '正在上传您提供的文件，请稍候...';
    case 'processing': return '正在智能分析文件内容，创建客户档案...';
    case 'success': return '客户档案已成功创建，正在自动跳转到客户详情页...';
    case 'error': return '处理过程中遇到问题，请稍后重试。';
    default: return '正在处理您的请求...';
  }
})

const processingProgressText = computed(() => {
  if (processingStatus.value === 'uploading') {
    return `上传进度: ${processingProgress.value}%`;
  } else if (processingStatus.value === 'processing') {
    return `处理进度: ${processingProgress.value}%`;
  }
  return '';
})



// 当处理状态变更时自动处理
watch(processingStatus, async (newStatus) => {
  if (newStatus === 'success' && createdClientId.value) {
    try {
      // 刷新客户列表以确保显示最新数据
      await fetchClients()

    // 等待0.3秒显示成功状态后自动跳转
    setTimeout(() => {
      processingDialogVisible.value = false;
      router.push(`/clients/${createdClientId.value}`);
    }, 300);
    } catch (error) {
      console.error('刷新客户列表失败:', error);
    }
  }
});

// 处理标签页切换动画
const handleTabSwitch = (tab: string) => {
  // 添加点击动画效果
  const clickedButton = document.activeElement as HTMLElement
  if (clickedButton) {
    clickedButton.classList.add('animate-bounce')
    setTimeout(() => {
      clickedButton.classList.remove('animate-bounce')
    }, 600)
  }
  
  // 切换标签页
  activeTab.value = tab
}
</script>

<style lang="postcss" scoped>
.el-dropdown-link {
  @apply flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors;
}

:deep(.el-table) {
  --el-table-border-color: transparent;
  --el-table-header-bg-color: #fff;
  --el-table-row-hover-bg-color: #f5f7fa;
}

:deep(.el-table__cell) {
  @apply border-b border-gray-100;
}

:deep(.el-table__header) th {
  @apply border-b border-gray-100;
}

:deep(.el-button--primary) {
  --el-button-bg-color: #4F46E5 !important; /* 紫色 */
  --el-button-border-color: #4F46E5 !important;
  --el-button-hover-bg-color: #4338CA !important; /* 深紫色 */
  --el-button-hover-border-color: #4338CA !important;
  --el-button-active-bg-color: #3730A3 !important; /* 更深的紫色 */
  --el-button-active-border-color: #3730A3 !important;
  --el-button-text-color: #FFFFFF !important;
}

/* 修改Element Plus链接按钮样式 */
:deep(.el-button--primary.is-link) {
  --el-button-text-color: #4F46E5 !important; /* 紫色 */
  --el-button-hover-text-color: #4338CA !important; /* 深紫色 */
  --el-button-active-text-color: #3730A3 !important; /* 更深的紫色 */
  --el-button-bg-color: transparent !important;
  --el-button-border-color: transparent !important;
  --el-button-hover-bg-color: transparent !important;
  --el-button-hover-border-color: transparent !important;
  --el-button-active-bg-color: transparent !important;
  --el-button-active-border-color: transparent !important;
}

/* 覆盖所有 a 标签颜色为紫色 */
:deep(a),
:deep(a:visited),
:deep(a:active) {
  color: #4F46E5 !important; /* 紫色 */
  text-decoration: none !important;
}

:deep(a:hover) {
  color: #4338CA !important; /* 深紫色 */
  text-decoration: underline !important;
}

/* 覆盖基于Tailwind的链接文本样式 */
:deep(.text-blue-600),
:deep(.text-blue-500) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.hover\:text-blue-700:hover),
:deep(.hover\:text-blue-600:hover) {
  color: #4338CA !important; /* 深紫色 */
}

/* 确保表格中的操作链接不会显示为蓝色 */
:deep(.el-table .cell a) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.el-table .cell a:hover) {
  color: #4338CA !important; /* 深紫色 */
}

/* 覆盖分页组件可能的蓝色为紫色 */
:deep(.el-pagination .el-pager li.is-active) {
  --el-color-primary: #4F46E5 !important; /* 紫色 */
  color: #FFFFFF !important;
  background-color: #4F46E5 !important;
  border-color: #4F46E5 !important;
}

:deep(.el-pagination .el-pager li:hover:not(.is-active)) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

:deep(.el-pagination .el-pagination__jump .el-pagination__editor) {
  border-color: #4F46E5 !important; /* 紫色 */
}

/* 修改表格选中行高亮颜色 */
:deep(.el-table tr.current-row) {
  background-color: rgba(79, 70, 229, 0.1) !important; /* 浅紫色 */
}

/* 修改所有element-plus的颜色初始变量 */
:deep(:root) {
  --el-color-primary: #4F46E5 !important; /* 紫色 */
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important; /* 深紫色 */
}

/* 文件分析状态提示背景修改 */
.uploading, .processing {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

.uploading .material-icons-outlined, 
.processing .material-icons-outlined, 
.text-indigo-600 {
  color: #4F46E5 !important;
}

.bg-indigo-50 {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

/* 确保所有蓝色相关类名都使用紫色 */
.text-indigo-700,
.text-blue-700 {
  color: #4338CA !important;
}

/* 确保所有蓝色背景都使用紫色 */
.bg-blue-50,
.bg-indigo-50 {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

.bg-primary {
  background-color: #4F46E5 !important;
}

.text-primary {
  color: #4F46E5 !important;
}

/* 进度条颜色 */
:deep(.el-progress-bar__inner) {
  background-color: #4F46E5 !important;
}

/* 删除确认对话框样式修改 - 移除阴影 */
:deep(.el-dialog) {
  box-shadow: none !important;
  border: 1px solid #e4e7ed;
}

:deep(.el-dialog__header),
:deep(.el-dialog__footer) {
  box-shadow: none !important;
  border-top: none;
  background-color: #fff;
}

:deep(.el-dialog__body) {
  background-color: #fff;
}

/* 增强分页组件的可见度 */
.pagination-custom {
  @apply mt-4;
}

:deep(.pagination-custom .el-pagination__total) {
  @apply text-gray-600;
}

:deep(.pagination-custom .el-pager li) {
  @apply border border-gray-200 min-w-[32px] h-8 leading-8 mx-1 transition-all duration-300 ease-in-out;
}

:deep(.pagination-custom .el-pager li.is-active) {
  @apply bg-primary text-white border-primary transform scale-110;
}

:deep(.pagination-custom .el-pager li:hover:not(.is-active)) {
  @apply bg-gray-50 border-gray-300 transform scale-105;
}

:deep(.pagination-custom .btn-prev),
:deep(.pagination-custom .btn-next) {
  @apply border border-gray-200 min-w-[32px] h-8 leading-8 mx-1 transition-all duration-300 ease-in-out;
}

:deep(.pagination-custom .btn-prev:hover),
:deep(.pagination-custom .btn-next:hover) {
  @apply bg-gray-50 border-gray-300 transform scale-105;
}

:deep(.pagination-custom .el-pagination button:disabled) {
  @apply bg-gray-100 cursor-not-allowed transform scale-100;
}

/* 添加禁用状态样式 */
:deep(.pagination-custom .el-pager li.is-disabled) {
  @apply opacity-50 cursor-not-allowed transform scale-100;
}

/* 确保激活页码的动画更平滑 */
:deep(.pagination-custom .el-pager) {
  @apply flex items-center;
}

/* 优化数字按钮的外观 */
:deep(.pagination-custom .el-pager li.number) {
  @apply font-medium;
}

/* 增强表格行的可点击性 */
.client-table {
  @apply cursor-pointer;
}

:deep(.client-table .el-table__row) {
  transition: background-color 0.2s ease;
}

:deep(.client-table .el-table__row:hover) {
  background-color: #f5f7fa !important;
}

:deep(.el-button.el-button--danger.is-link) {
  --el-button-text-color: #f56c6c;
}

:deep(.el-dropdown-menu__item) {
  @apply text-gray-600;
  color: #606266 !important; /* 强制覆盖Element默认颜色 */
}

:deep(.el-dropdown-menu__item i) {
  @apply text-gray-400;
  color: #909399 !important; /* 强制覆盖Element默认颜色 */
}

/* 为特定菜单项添加样式 */
:deep(.view-item),
:deep(.view-item i) {
  color: #606266 !important; /* 灰色文本 */
}

:deep(.archive-item),
:deep(.archive-item i) {
  color: #606266 !important; /* 灰色文本 */
}

:deep(.delete-item),
:deep(.delete-item i) {
  color: #606266 !important; /* 灰色文本 */
}

:deep(.delete-item:hover),
:deep(.delete-item:hover i) {
  color: #f56c6c !important; /* 红色 */
}

/* 覆盖默认的蓝色高亮 */
:deep(.el-dropdown-menu__item:focus),
:deep(.el-dropdown-menu__item:not(.is-disabled):focus) {
  background-color: transparent !important;
  color: #606266 !important;
}

/* 设置鼠标悬停状态为紫色 */
:deep(.el-dropdown-menu__item:hover),
:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
  @apply text-primary bg-primary/5;
  color: #4F46E5 !important; /* 紫色 */
  background-color: rgba(79, 70, 229, 0.05) !important;
}

/* 设置危险按钮的样式 */
:deep(.el-dropdown-menu__item.danger:hover) {
  @apply text-danger bg-danger/5;
  color: #f56c6c !important; /* 红色 */
  background-color: rgba(245, 108, 108, 0.05) !important;
}

/* 修改分隔线样式 */
:deep(.el-dropdown-menu__item--divided) {
  margin: 0 !important;
  padding: 0 !important;
  height: 1px !important;
  background-color: #f0f0f0 !important;
  border-top: none !important;
}

/* 去掉分隔线前的空白间距 */
:deep(.el-dropdown-menu__item--divided::before) {
  height: 0 !important;
  margin: 0 !important;
  display: none !important;
}

:deep(.el-button.el-button--default) {
  --el-button-bg-color: transparent;
  --el-button-border-color: transparent;
  --el-button-hover-bg-color: transparent;
  --el-button-hover-border-color: transparent;
  --el-button-active-bg-color: transparent;
  --el-button-active-border-color: transparent;
  --el-button-hover-text-color: #4F46E5 !important; /* 紫色 */
}

/* 确保所有按钮悬浮时的文字颜色都是紫色 */
:deep(.el-button:hover) {
  --el-button-text-color: #4F46E5 !important; /* 紫色 */
}

/* 特殊处理取消按钮和其他默认按钮 */
:deep(.el-button:not(.el-button--primary):not(.el-button--danger):hover) {
  color: #4F46E5 !important; /* 紫色 */
  border-color: #4F46E5 !important; /* 紫色边框 */
}

/* 标签页按钮动画样式 */
.status-tab-btn {
  position: relative;
  overflow: hidden;
}

.status-tab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.status-tab-btn:active::before {
  left: 100%;
}

/* 轻柔的脉冲动画 */
@keyframes pulse-gentle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.02);
  }
}

.animate-pulse-gentle {
  animation: pulse-gentle 2s infinite;
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-bounce {
  animation: bounce 0.6s ease-out;
}

/* 按钮状态切换时的闪烁效果 */
@keyframes flash {
  0% {
    background-color: currentColor;
    opacity: 1;
  }
  50% {
    background-color: rgba(79, 70, 229, 0.8);
    opacity: 0.8;
  }
  100% {
    background-color: currentColor;
    opacity: 1;
  }
}

.status-tab-btn.animate-flash {
  animation: flash 0.3s ease-in-out;
}

/* 全局覆盖所有图标的悬浮颜色为紫色 */
:deep(svg:hover),
:deep(.material-icons-outlined:hover),
:deep(.material-icons:hover),
:deep(.el-icon:hover),
:deep(i:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖Element Plus内部图标的悬浮颜色 */
:deep(.el-input__prefix .el-input__icon:hover),
:deep(.el-input__suffix .el-input__icon:hover),
:deep(.el-select__caret:hover),
:deep(.el-pagination__right-wrapper .el-icon:hover),
:deep(.el-pagination__left-wrapper .el-icon:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖搜索框图标的悬浮颜色 */
:deep(.el-input .el-input__prefix .material-icons-outlined:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖下拉菜单操作按钮图标的悬浮颜色 */
:deep(.el-dropdown-link:hover .material-icons-outlined),
:deep(.icon-btn:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖对话框中的图标悬浮颜色 */
:deep(.el-dialog .material-icons-outlined:hover),
:deep(.el-dialog svg:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖关闭按钮SVG图标颜色 */
:deep(.el-dialog__headerbtn:hover),
:deep(.el-dialog__headerbtn:hover svg),
:deep(.el-dialog__close:hover),
:deep(.el-dialog__close:hover svg) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖Element Plus上传组件图标 */
:deep(.el-upload .material-icons-outlined:hover),
:deep(.el-upload-dragger:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖表格操作图标悬浮颜色 */
:deep(.el-table .el-icon:hover),
:deep(.el-table .material-icons-outlined:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 覆盖分页组件图标 */
:deep(.el-pagination .el-icon:hover) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 确保按钮内的图标悬浮时也是紫色 */
:deep(.el-button:hover .el-icon),
:deep(.el-button:hover .material-icons-outlined),
:deep(.el-button:hover svg) {
  color: inherit !important; /* 继承按钮文字颜色 */
}

/* 特殊处理搜索输入框前缀图标 */
:deep(.el-input__prefix-inner:hover),
:deep(.el-input__prefix-inner:hover .material-icons-outlined) {
  color: #4F46E5 !important; /* 紫色 */
}

/* 输入框焦点状态紫色覆盖 */
:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-input:focus-within .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-textarea.is-focus .el-textarea__inner) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-select.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-date-editor.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

/* 覆盖Element Plus默认的focus颜色 */
:deep(.el-input__wrapper:focus),
:deep(.el-input__wrapper:focus-within),
:deep(.el-textarea__inner:focus) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

/* 下拉框焦点状态紫色覆盖 */
:deep(.el-select__wrapper.is-focused) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-select__wrapper:focus) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-select__wrapper:focus-within) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

:deep(.el-select:focus-within .el-select__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

/* 下拉框激活状态 */
:deep(.el-select.is-focus .el-select__wrapper) {
  border-color: #4F46E5 !important; /* 紫色 */
  box-shadow: none !important;
}

/* 智能建档状态图标样式 - 确保旋转图标清晰可见 */
.processing-dialog .material-icons-outlined.animate-spin {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 确保状态图标容器的背景色正确应用 */
.processing-dialog .bg-primary {
  background-color: #4F46E5 !important;
}

/* 为旋转动画添加更平滑的效果 */
@keyframes smooth-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: smooth-spin 1s linear infinite;
}

/* 快速定校书相关样式 */
.school-book-btn {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  min-height: 120px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.school-book-sidebar {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.school-book-sidebar::-webkit-scrollbar {
  width: 8px; /* 稍微加宽以便MacBook触控板操作 */
}

.school-book-sidebar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.02); /* 轻微背景色便于识别 */
  border-radius: 4px;
}

.school-book-sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
  transition: background-color 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1); /* 增加边框提高可见性 */
}

.school-book-sidebar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* 侧边栏滑入滑出动画 */
.sidebar-slide-enter-active,
.sidebar-slide-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar-slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.sidebar-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.sidebar-container {
  will-change: transform, opacity;
}

/* 遮罩层淡入淡出动画 */
.overlay-fade-enter-active,
.overlay-fade-leave-active {
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.overlay-fade-enter-from,
.overlay-fade-leave-to {
  opacity: 0;
}

.overlay-backdrop {
  will-change: opacity;
}

/* 项目列表项动画 */
.program-item-enter-active,
.program-item-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.program-item-enter-from {
  opacity: 0;
  transform: translateX(-20px) scale(0.95);
}

.program-item-leave-to {
  opacity: 0;
  transform: translateX(20px) scale(0.95);
}

.program-item-move {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 优化的文件上传组件样式 */
.upload-area {
  @apply border-2 border-dashed border-gray-300 rounded-xl p-8 transition-all duration-300 cursor-pointer;
  @apply bg-gray-50 hover:bg-gray-100;
}

.upload-area--dragover {
  @apply border-indigo-400 bg-indigo-50;
}

.upload-area--has-file {
  @apply border-green-400 bg-green-50 cursor-default;
}

.upload-area--error {
  @apply border-red-400 bg-red-50;
}

.upload-content {
  @apply text-center space-y-4;
}

.upload-icon-container {
  @apply relative inline-block;
}

.upload-icon-wrapper {
  @apply relative;
}

.upload-icon {
  @apply text-5xl text-gray-400 transition-all duration-300;
}

.upload-area:hover .upload-icon {
  @apply text-indigo-500 scale-110;
}

.upload-area--dragover .upload-icon {
  @apply text-indigo-500 scale-125;
}

.upload-icon-pulse {
  @apply absolute inset-0 rounded-full bg-indigo-400 opacity-0 animate-ping;
}

.upload-area--dragover .upload-icon-pulse {
  @apply opacity-30;
}

.upload-text {
  @apply space-y-1;
}

.upload-title {
  @apply text-lg font-semibold text-gray-700;
}

.upload-area--dragover .upload-title {
  @apply text-indigo-700;
}

.upload-subtitle {
  @apply text-sm text-gray-500;
}

.upload-link {
  @apply text-indigo-600 font-medium hover:text-indigo-700 transition-colors duration-200;
}

.upload-info {
  @apply space-y-3;
}

.upload-formats {
  @apply flex justify-center space-x-2;
}

.format-tag {
  @apply inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium;
  @apply bg-blue-100 text-blue-700 border border-blue-200;
}

.upload-limit {
  @apply text-xs text-gray-500;
}

.upload-tip {
  @apply inline-flex items-center text-xs text-gray-500 bg-yellow-50 px-3 py-2 rounded-md;
}

/* 文件预览样式 */
.file-preview {
  @apply space-y-4;
}

.file-preview-content {
  @apply flex items-center space-x-4 bg-white rounded-lg p-4 border border-green-200;
}

.file-icon-container {
  @apply flex-shrink-0 w-12 h-12 flex items-center justify-center rounded-lg bg-gray-100;
}

.file-icon {
  @apply text-2xl;
}

.file-details {
  @apply flex-1 min-w-0;
}

.file-name {
  @apply font-semibold text-gray-900 truncate text-sm;
}

.file-meta {
  @apply flex items-center space-x-3 mt-1 text-xs text-gray-500;
}

.file-size {
  @apply font-medium;
}

.file-status {
  @apply flex items-center text-green-600 font-medium;
}

.remove-file-btn {
  @apply flex-shrink-0 p-2 rounded-lg text-gray-400 hover:text-red-500 hover:bg-red-50;
  @apply transition-all duration-200 active:scale-95;
}

.file-preview-footer {
  @apply text-center;
}

/* 错误提示样式 */
.upload-error {
  @apply flex items-center mt-3 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700;
}

/* 动画效果 */
.upload-area {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-preview {
  animation: fadeInUp 0.3s ease-out;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .upload-area {
    @apply p-6;
  }
  
  .upload-icon {
    @apply text-4xl;
  }
  
  .upload-title {
    @apply text-base;
  }
  
  .file-preview-content {
    @apply flex-col space-x-0 space-y-3 text-center;
  }
  
  .file-meta {
    @apply flex-col space-x-0 space-y-1;
  }
}
</style>