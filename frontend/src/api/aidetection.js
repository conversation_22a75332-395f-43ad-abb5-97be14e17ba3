/**
 * AI检测API客户端
 */
import request from '@/utils/request'

const AI_DETECTION_BASE = '/api/ai-detection'

/**
 * 单文档AI检测
 * @param {string} text - 要检测的文本
 * @param {string} format - 文本格式 ('text' 或 'markdown')
 * @returns {Promise} 检测结果
 */
export const detectAIContent = async (text, format = 'text') => {
  return request({
    url: `${AI_DETECTION_BASE}/detect`,
    method: 'post',
    data: { 
      text,
      format 
    }
  })
}

/**
 * 批量文档AI检测
 * @param {Array<string>} texts - 要检测的文本数组
 * @returns {Promise} 批量检测结果
 */
export const detectAIContentBatch = async (texts) => {
  return request({
    url: `${AI_DETECTION_BASE}/detect-batch`,
    method: 'post',
    data: { texts }
  })
}

/**
 * 批量检测统计分析
 * @param {Array<string>} texts - 要分析的文本数组
 * @returns {Promise} 统计分析结果
 */
export const analyzeAIContentBatch = async (texts) => {
  return request({
    url: `${AI_DETECTION_BASE}/analyze-batch`,
    method: 'post',
    data: { texts }
  })
}

/**
 * 获取AI检测服务健康状态
 * @returns {Promise} 健康状态
 */
export const getAIDetectionHealth = async () => {
  return request({
    url: `${AI_DETECTION_BASE}/health`,
    method: 'get'
  })
}

/**
 * 获取AI检测服务配置信息
 * @returns {Promise} 配置信息
 */
export const getAIDetectionConfig = async () => {
  return request({
    url: `${AI_DETECTION_BASE}/config`,
    method: 'get'
  })
}

/**
 * 获取AI检测API信息
 * @returns {Promise} API信息
 */
export const getAIDetectionInfo = async () => {
  return request({
    url: `${AI_DETECTION_BASE}/`,
    method: 'get'
  })
} 

/**
 * AI降重（人性化改写）
 * @param {Object} payload
 * @param {string} payload.text - 原始文本
 * @param {string} [payload.format] - 文本格式 ('text' 或 'markdown')
 * @param {Array<object>} [payload.highlighted_sentences] - 可选：带位置的高亮句子
 * @param {Array<string>} [payload.highlighted_sentences_raw] - 可选：原始高亮句子字符串
 */
export const humanizeAIContent = async (payload) => {
  // 如果没有指定format，根据文本内容自动判断
  if (!payload.format) {
    const text = payload.text || '';
    const hasMarkdownSyntax = (
      text.includes('**') || 
      text.includes('###') || 
      text.includes('##') || 
      text.includes('# ') ||
      text.includes('- ') ||
      text.includes('* ') ||
      text.includes('1. ') ||
      text.includes('`') ||
      text.includes('> ') ||
      text.includes('[') && text.includes('](') ||
      text.includes('___') ||
      text.includes('---')
    )
    payload.format = hasMarkdownSyntax ? 'markdown' : 'text'
  }
  
  return request({
    url: `${AI_DETECTION_BASE}/humanize`,
    method: 'post',
    data: payload
  })
}