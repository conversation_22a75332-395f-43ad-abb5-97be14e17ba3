import request from '@/utils/request'
import { setAuth, clearAuth } from '@/utils/auth'

export const login = async (username, password) => {
  const params = new URLSearchParams();
  params.append('username', username);
  params.append('password', password);

  try {
    const response = await request.post('/api/auth/login', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }
    });

    if (response.access_token) {
      setAuth(response.access_token, response.refresh_token, response.user)
    }
    return response;
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
}

export const register = async (username, email, password, invitation_code = null) => {
  const data = {
    username,
    email,
    password
  }

  // 如果有邀请码，添加到请求数据中
  if (invitation_code) {
    data.invitation_code = invitation_code
  }

  const response = await request.post('/api/auth/register', data)
  if (response.access_token) {
    setAuth(response.access_token, response.refresh_token, response.user)
  }
  return response
}

export const logout = () => {
  clearAuth()
  window.location.href = '/login'
}

// 微信登录相关API
export const getWeChatLoginUrl = async () => {
  try {
    const response = await request.get('/api/auth/wechat/login-url')
    return response
  } catch (error) {
    console.error('获取微信登录URL失败:', error)
    throw error
  }
}

export const getWeChatQRConfig = async () => {
  try {
    const response = await request.get('/api/auth/wechat/qr-config')
    return response
  } catch (error) {
    console.error('获取微信二维码配置失败:', error)
    throw error
  }
}

export const weChatCallback = async (code, state) => {
  try {
    const response = await request.post('/api/auth/wechat/callback', {
      code,
      state
    })
    
    // 处理登录成功的逻辑
    if (response.access_token) {
      setAuth(response.access_token, response.refresh_token, response.user)
    }
    
    return response
  } catch (error) {
    console.error('微信登录回调处理失败:', error)
    throw error
  }
}

export const getCurrentUser = async (params = {}) => {
  return request.get('/api/auth/me', { params })
}

export const updateProfile = async (data) => {
  try {
    const response = await request.put('/api/auth/update-profile', data)

    // 如果更新成功，同时更新本地存储的用户信息
    if (response) {
      // 获取当前存储的用户信息
      const currentUser = JSON.parse(localStorage.getItem('user') || 'null')

      if (currentUser) {
        // 更新用户信息
        const updatedUser = { ...currentUser }

        // 更新昵称
        if (data.nickname !== undefined) {
          updatedUser.nickname = data.nickname
        }

        // 更新手机号
        if (data.phone !== undefined) {
          updatedUser.phone = data.phone
        }



        // 更新邮箱
        if (data.email !== undefined) {
          updatedUser.email = data.email
        }

        // 更新其他字段
        if (response.updated_at) {
          updatedUser.updated_at = response.updated_at
        }

        // 保存更新后的用户信息
        localStorage.setItem('user', JSON.stringify(updatedUser))
      }
    }

    return response
  } catch (error) {
    throw error
  }
}

export const refreshToken = async (tokenFromCaller) => {
  // 支持从调用方传入 refresh_token；未传则回退到本地存储
  const refresh_token = tokenFromCaller || localStorage.getItem('refresh_token')
  if (!refresh_token) {
    throw new Error('No refresh token')
  }

  try {
    // 使用原始axios实例发送请求，避免进入request拦截器循环
    const axios = require('axios').default
    // 获取API基础URL，开发环境为空使用相对路径，生产环境使用完整URL
    const baseURL = import.meta.env.VITE_API_URL || ''

    const response = await axios({
      method: 'post',
      url: `${baseURL}/api/auth/refresh-token`,
      headers: {
        'Content-Type': 'application/json'
      },
      data: {
        refresh_token: refresh_token
      }
    })

    // 更新本地存储的token
    if (response.data && response.data.access_token) {
      localStorage.setItem('token', response.data.access_token)
      // 如果返回了新的refresh_token，也要更新
      if (response.data.refresh_token) {
        localStorage.setItem('refresh_token', response.data.refresh_token)
      }
      
      // 🔥 修复：token刷新后重新同步身份信息
      try {
        // 解析新token中的身份信息并更新localStorage
        const tokenPayload = JSON.parse(atob(response.data.access_token.split('.')[1]))
        const currentIdentity = {
          identity_type: tokenPayload.identity_type || 'personal',
          organization_id: tokenPayload.organization_id || null,
          organization_role: tokenPayload.organization_role || null
        }
        
        // 更新身份信息到localStorage
        localStorage.setItem('currentIdentity', JSON.stringify(currentIdentity))
      } catch (identityError) {
        console.warn('解析token身份信息失败:', identityError)
      }
      
      return response.data
    } else {
      throw new Error('刷新令牌响应无效')
    }
  } catch (error) {
    // 不在此处触发登出，交由调用方（拦截器/Token管理器）决定如何处理
    throw error
  }
}

export const changePassword = async (oldPassword, newPassword) => {
  return request.post('/api/auth/change-password', {
    old_password: oldPassword,
    new_password: newPassword
  })
}

export const forgotPassword = async (email) => {
  return request.post('/api/auth/forgot-password', {
    email
  })
}