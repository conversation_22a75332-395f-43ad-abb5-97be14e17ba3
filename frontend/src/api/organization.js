import request from '@/utils/request'

// ===============================
// 用户管理 API
// ===============================

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {string} params.search - 搜索关键词
 * @param {string} params.status - 用户状态
 * @param {string} params.type - 用户类型
 * @param {number} params.departmentId - 部门ID
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} 用户列表响应
 */
export const getUserList = (params) => {
  return request({
    url: '/api/organization/users',
    method: 'get',
    params
  })
}

/**
 * 创建用户
 * @param {Object} userData - 用户数据
 * @param {string} userData.username - 用户名
 * @param {string} userData.name - 姓名
 * @param {string} userData.email - 邮箱
 * @param {string} userData.phone - 电话
 * @param {number} userData.departmentId - 部门ID
 * @param {string} userData.type - 用户类型
 * @param {string} userData.status - 状态
 * @returns {Promise} 创建响应
 */
export const createUser = (userData) => {
  return request({
    url: '/api/organization/users',
    method: 'post',
    data: userData
  })
}

/**
 * 更新用户
 * @param {number} userId - 用户ID
 * @param {Object} userData - 用户数据
 * @returns {Promise} 更新响应
 */
export const updateUser = (userId, userData) => {
  return request({
    url: `/api/organization/users/${userId}`,
    method: 'put',
    data: userData
  })
}

/**
 * 删除用户
 * @param {number} userId - 用户ID
 * @returns {Promise} 删除响应
 */
export const deleteUser = (userId) => {
  return request({
    url: `/api/organization/users/${userId}`,
    method: 'delete'
  })
}

/**
 * 批量删除用户
 * @param {Array<number>} userIds - 用户ID数组
 * @returns {Promise} 删除响应
 */
export const batchDeleteUsers = (userIds) => {
  return request({
    url: '/api/organization/users/batch-delete',
    method: 'post',
    data: { userIds }
  })
}

/**
 * 重置用户密码
 * @param {number} userId - 用户ID
 * @returns {Promise} 重置响应
 */
export const resetUserPassword = (userId) => {
  return request({
    url: `/api/organization/users/${userId}/reset-password`,
    method: 'post'
  })
}

/**
 * 批量重置密码
 * @param {Array<number>} userIds - 用户ID数组
 * @returns {Promise} 重置响应
 */
export const batchResetPassword = (userIds) => {
  return request({
    url: '/api/organization/users/batch-reset-password',
    method: 'post',
    data: { userIds }
  })
}

/**
 * 导出用户数据
 * @param {Object} params - 导出参数
 * @returns {Promise} 导出响应
 */
export const exportUsers = (params) => {
  return request({
    url: '/api/organization/users/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ===============================
// 部门管理 API
// ===============================

/**
 * 获取部门树
 * @returns {Promise} 部门树响应
 */
export const getDepartmentTree = () => {
  return request({
    url: '/api/organization/departments/tree',
    method: 'get'
  })
}

/**
 * 获取部门列表
 * @param {Object} params - 查询参数
 * @param {string} params.search - 搜索关键词
 * @param {string} params.status - 部门状态
 * @returns {Promise} 部门列表响应
 */
export const getDepartmentList = (params) => {
  return request({
    url: '/api/organization/departments',
    method: 'get',
    params
  })
}

/**
 * 创建部门
 * @param {Object} deptData - 部门数据
 * @param {string} deptData.name - 部门名称
 * @param {string} deptData.code - 部门编码
 * @param {number} deptData.parentId - 父级部门ID
 * @param {string} deptData.manager - 负责人
 * @param {string} deptData.phone - 联系电话
 * @param {string} deptData.email - 邮箱
 * @param {string} deptData.status - 状态
 * @param {number} deptData.sort - 排序
 * @returns {Promise} 创建响应
 */
export const createDepartment = (deptData) => {
  return request({
    url: '/api/organization/departments',
    method: 'post',
    data: deptData
  })
}

/**
 * 更新部门
 * @param {number} deptId - 部门ID
 * @param {Object} deptData - 部门数据
 * @returns {Promise} 更新响应
 */
export const updateDepartment = (deptId, deptData) => {
  return request({
    url: `/api/organization/departments/${deptId}`,
    method: 'put',
    data: deptData
  })
}

/**
 * 删除部门
 * @param {number} deptId - 部门ID
 * @returns {Promise} 删除响应
 */
export const deleteDepartment = (deptId) => {
  return request({
    url: `/api/organization/departments/${deptId}`,
    method: 'delete'
  })
}

/**
 * 导入部门数据
 * @param {FormData} formData - 包含文件的表单数据
 * @returns {Promise} 导入响应
 */
export const importDepartments = (formData) => {
  return request({
    url: '/api/organization/departments/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出部门数据
 * @param {Object} params - 导出参数
 * @returns {Promise} 导出响应
 */
export const exportDepartments = (params) => {
  return request({
    url: '/api/organization/departments/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ===============================
// 岗位管理 API
// ===============================

/**
 * 获取岗位列表
 * @param {Object} params - 查询参数
 * @param {string} params.search - 搜索关键词
 * @param {string} params.status - 岗位状态
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} 岗位列表响应
 */
export const getPositionList = (params) => {
  return request({
    url: '/api/organization/positions',
    method: 'get',
    params
  })
}

/**
 * 创建岗位
 * @param {Object} positionData - 岗位数据
 * @param {string} positionData.code - 岗位编码
 * @param {string} positionData.name - 岗位名称
 * @param {string} positionData.status - 状态
 * @param {number} positionData.sort - 排序
 * @param {string} positionData.description - 描述
 * @returns {Promise} 创建响应
 */
export const createPosition = (positionData) => {
  return request({
    url: '/api/organization/positions',
    method: 'post',
    data: positionData
  })
}

/**
 * 更新岗位
 * @param {number} positionId - 岗位ID
 * @param {Object} positionData - 岗位数据
 * @returns {Promise} 更新响应
 */
export const updatePosition = (positionId, positionData) => {
  return request({
    url: `/api/organization/positions/${positionId}`,
    method: 'put',
    data: positionData
  })
}

/**
 * 删除岗位
 * @param {number} positionId - 岗位ID
 * @returns {Promise} 删除响应
 */
export const deletePosition = (positionId) => {
  return request({
    url: `/api/organization/positions/${positionId}`,
    method: 'delete'
  })
}

/**
 * 切换岗位状态
 * @param {number} positionId - 岗位ID
 * @param {string} status - 新状态
 * @returns {Promise} 切换响应
 */
export const togglePositionStatus = (positionId, status) => {
  return request({
    url: `/api/organization/positions/${positionId}/status`,
    method: 'patch',
    data: { status }
  })
}

/**
 * 批量启用岗位
 * @param {Array<number>} positionIds - 岗位ID数组
 * @returns {Promise} 启用响应
 */
export const batchEnablePositions = (positionIds) => {
  return request({
    url: '/api/organization/positions/batch-enable',
    method: 'post',
    data: { positionIds }
  })
}

/**
 * 批量停用岗位
 * @param {Array<number>} positionIds - 岗位ID数组
 * @returns {Promise} 停用响应
 */
export const batchDisablePositions = (positionIds) => {
  return request({
    url: '/api/organization/positions/batch-disable',
    method: 'post',
    data: { positionIds }
  })
}

/**
 * 批量删除岗位
 * @param {Array<number>} positionIds - 岗位ID数组
 * @returns {Promise} 删除响应
 */
export const batchDeletePositions = (positionIds) => {
  return request({
    url: '/api/organization/positions/batch-delete',
    method: 'post',
    data: { positionIds }
  })
}

/**
 * 导出岗位数据
 * @param {Object} params - 导出参数
 * @returns {Promise} 导出响应
 */
export const exportPositions = (params) => {
  return request({
    url: '/api/organization/positions/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ===============================
// 通用 API
// ===============================

/**
 * 获取组织统计信息
 * @returns {Promise} 统计信息响应
 */
export const getOrganizationStats = () => {
  return request({
    url: '/api/organization/stats',
    method: 'get'
  })
} 