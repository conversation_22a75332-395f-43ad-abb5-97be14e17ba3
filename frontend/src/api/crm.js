/**
 * CRM系统API接口
 * 包含市场资源和有效客户管理的所有API调用
 */

import request from '@/utils/request'

// ======================== CRM客户管理 ========================

/**
 * 获取CRM客户列表
 * @param {Object} params - 查询参数
 * @param {number} params.organization_id - 组织ID
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.size=20] - 每页大小
 * @param {string} [params.search_keyword] - 搜索关键词
 * @param {string} [params.channel_source] - 渠道来源
 * @param {string} [params.assignment_status] - 分配状态
 * @param {boolean} [params.is_valid] - 是否有效
 * @param {string} [params.intent_level] - 意向等级
 * @param {string} [params.lifecycle_status] - 生命周期状态
 * @param {number} [params.market_staff_user_id] - 市场人员ID
 * @param {number} [params.sales_staff_user_id] - 销售人员ID
 * @param {string} [params.start_date] - 开始日期
 * @param {string} [params.end_date] - 结束日期
 * @returns {Promise}
 */
export function getCRMClientList(params) {
  return request({
    url: '/api/crm/clients/',
    method: 'get',
    params
  })
}

/**
 * 获取CRM客户详情
 * @param {number} clientId - 客户ID
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function getCRMClientDetail(clientId, organizationId) {
  return request({
    url: `/api/crm/clients/${clientId}`,
    method: 'get',
    params: { organization_id: organizationId }
  })
}

/**
 * 创建CRM客户
 * @param {Object} data - 客户数据
 * @param {number} data.organization_id - 组织ID
 * @param {string} data.add_date - 添加日期
 * @param {string} [data.customer_name] - 客户姓名
 * @param {string} data.wechat_name - 微信名称
 * @param {string} data.wechat_id - 微信账号
 * @param {string} [data.phone] - 电话号码
 * @param {string} data.channel_source - 渠道来源
 * @param {string} data.study_project - 咨询项目
 * @param {string} [data.customer_background] - 客户背景
 * @param {string} [data.market_supplement] - 市场补充
 * @param {string} [data.assignment_status] - 分配状态
 * @param {boolean} [data.is_valid] - 是否有效客户
 * @param {string} [data.intent_level] - 意向等级
 * @param {string} [data.feedback_budget] - 预算反馈
 * @param {string} [data.feedback_motivation] - 动机反馈
 * @param {string} [data.feedback_urgency] - 紧迫性反馈
 * @param {string} [data.feedback_comparison] - 对比反馈
 * @param {string} [data.feedback_parents] - 父母反馈
 * @param {string} [data.current_school] - 当前院校
 * @param {string} [data.current_major] - 当前专业
 * @param {string} [data.current_grade] - 当前年级
 * @param {string} [data.gpa_value] - GPA数值
 * @param {string} [data.gpa_scale] - GPA满分
 * @param {Array} [data.language_scores] - 语言成绩
 * @param {string} [data.intended_enrollment_date] - 预期入学时间
 * @param {Array} [data.intent_regions] - 意向地区
 * @param {Array} [data.intent_schools] - 意向院校
 * @param {Array} [data.intent_majors] - 意向专业
 * @param {string} [data.lifecycle_status] - 生命周期状态
 * @returns {Promise}
 */
export function createCRMClient(data) {
  return request({
    url: '/api/crm/clients/',
    method: 'post',
    data
  })
}

/**
 * 更新CRM客户
 * @param {number} clientId - 客户ID
 * @param {Object} data - 更新数据
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function updateCRMClient(clientId, data, organizationId) {
  return request({
    url: `/api/crm/clients/${clientId}`,
    method: 'put',
    data,
    params: { organization_id: organizationId }
  })
}

/**
 * 删除CRM客户
 * @param {number} clientId - 客户ID
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function deleteCRMClient(clientId, organizationId) {
  return request({
    url: `/api/crm/clients/${clientId}`,
    method: 'delete',
    params: { organization_id: organizationId }
  })
}

/**
 * 获取CRM统计信息
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function getCRMStatistics(organizationId) {
  return request({
    url: '/api/crm/clients/statistics/overview',
    method: 'get',
    params: { organization_id: organizationId }
  })
}

/**
 * 获取组织成员列表（用于CRM分配）
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function getOrganizationMembersForCRM(organizationId) {
  return request({
    url: `/api/crm/organization/${organizationId}/members`,
    method: 'get'
  })
}

// ======================== 有效客户管理 ========================

/**
 * 获取有效客户列表
 * @param {Object} params - 查询参数
 * @param {number} params.organization_id - 组织ID
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.size=20] - 每页大小
 * @param {string} [params.search_keyword] - 搜索关键词
 * @param {string} [params.intent_level] - 意向等级
 * @param {string} [params.assignment_status] - 分配状态
 * @param {number} [params.market_staff_user_id] - 市场人员ID
 * @param {number} [params.sales_staff_user_id] - 销售人员ID
 * @param {string} [params.start_date] - 开始日期
 * @param {string} [params.end_date] - 结束日期
 * @returns {Promise}
 */
export function getValidCustomerList(params) {
  return request({
    url: '/api/crm/valid-customers/',
    method: 'get',
    params
  })
}

/**
 * 获取有效客户详情
 * @param {number} clientId - 客户ID
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function getValidCustomerDetail(clientId, organizationId) {
  return request({
    url: `/api/crm/valid-customers/${clientId}`,
    method: 'get',
    params: { organization_id: organizationId }
  })
}

/**
 * 更新有效客户
 * @param {number} clientId - 客户ID
 * @param {Object} data - 更新数据
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function updateValidCustomer(clientId, data, organizationId) {
  return request({
    url: `/api/crm/valid-customers/${clientId}`,
    method: 'put',
    data,
    params: { organization_id: organizationId }
  })
}

/**
 * 转为签约客户
 * @param {number} clientId - 客户ID
 * @param {number} organizationId - 组织ID
 * @param {number} [convertedClientId] - 转签后的客户ID
 * @returns {Promise}
 */
export function convertToSignedCustomer(clientId, organizationId, convertedClientId) {
  const params = { organization_id: organizationId }
  if (convertedClientId) {
    params.converted_client_id = convertedClientId
  }
  return request({
    url: `/api/crm/valid-customers/${clientId}/convert-to-signed`,
    method: 'post',
    params
  })
}

/**
 * 归档有效客户
 * @param {number} clientId - 客户ID
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function archiveValidCustomer(clientId, organizationId) {
  return request({
    url: `/api/crm/valid-customers/${clientId}/archive`,
    method: 'post',
    params: { organization_id: organizationId }
  })
}

/**
 * 获取有效客户统计信息
 * @param {Object} params - 查询参数
 * @param {number} params.organization_id - 组织ID
 * @param {boolean} [params.show_all] - 是否显示所有客户（仅管理员或组织owner）
 * @returns {Promise}
 */
export function getValidCustomerStatistics(params) {
  return request({
    url: '/api/crm/valid-customers/statistics/overview',
    method: 'get',
    params
  })
}

// ======================== CRM人员分配管理 ========================

/**
 * 为客户分配人员
 * @param {number} clientId - 客户ID
 * @param {Object} params - 分配参数
 * @param {number} params.user_id - 用户ID
 * @param {string} params.role - 角色 (market/sales/document_writer/submission)
 * @param {number} params.organization_id - 组织ID
 * @returns {Promise}
 */
export function assignStaffToClient(clientId, params) {
  return request({
    url: `/api/crm/clients/${clientId}/staff`,
    method: 'post',
    params
  })
}

/**
 * 移除客户人员分配
 * @param {number} clientId - 客户ID
 * @param {Object} params - 移除参数
 * @param {number} params.user_id - 用户ID
 * @param {string} params.role - 角色
 * @param {number} params.organization_id - 组织ID
 * @returns {Promise}
 */
export function removeStaffFromClient(clientId, params) {
  return request({
    url: `/api/crm/clients/${clientId}/staff`,
    method: 'delete',
    params
  })
}

/**
 * 获取客户人员分配列表
 * @param {number} clientId - 客户ID
 * @param {number} organizationId - 组织ID
 * @returns {Promise}
 */
export function getClientStaff(clientId, organizationId) {
  return request({
    url: `/api/crm/clients/${clientId}/staff`,
    method: 'get',
    params: { organization_id: organizationId }
  })
}

// ======================== 常用枚举和常量 ========================

/**
 * 分配状态枚举
 */
export const ASSIGNMENT_STATUS = {
  UNASSIGNED: 'unassigned',
  ASSIGNED: 'assigned'
}

/**
 * 意向等级枚举
 */
export const INTENT_LEVEL = {
  HIGH: 'high',
  MEDIUM: 'medium', 
  LOW: 'low'
}

/**
 * 生命周期状态枚举
 */
export const LIFECYCLE_STATUS = {
  LEAD: 'lead',
  VALID: 'valid',
  SIGNED: 'signed',
  ARCHIVED: 'archived'
}

/**
 * 人员角色枚举
 */
export const STAFF_ROLE = {
  MARKET: 'market',
  SALES: 'sales',
  DOCUMENT_WRITER: 'document_writer',
  SUBMISSION: 'submission'
}

/**
 * 渠道来源选项（前端使用）
 */
export const CHANNEL_SOURCE_OPTIONS = [
  { label: '小红书', value: '小红书' },
  { label: '抖音', value: '抖音' },
  { label: '转介绍', value: '转介绍' },
  { label: '其他', value: '其他' }
]

/**
 * 意向等级选项（前端使用）
 */
export const INTENT_LEVEL_OPTIONS = [
  { label: '高意向', value: 'high' },
  { label: '中意向', value: 'medium' },
  { label: '低意向', value: 'low' }
]

/**
 * 分配状态选项（前端使用）
 */
export const ASSIGNMENT_STATUS_OPTIONS = [
  { label: '未分配', value: 'unassigned' },
  { label: '已分配', value: 'assigned' }
]

/**
 * 生命周期状态选项（前端使用）
 */
export const LIFECYCLE_STATUS_OPTIONS = [
  { label: '线索', value: 'lead' },
  { label: '有效客户', value: 'valid' },
  { label: '已签约', value: 'signed' },
  { label: '已归档', value: 'archived' }
]