/**
 * 账户管理相关API
 */

import request from '@/utils/request'

/**
 * 获取充值套餐列表
 */
export const getRechargePackages = async () => {
  try {
    const response = await request.get('/api/credit-payment/packages')
    // 后端直接返回套餐数组，包装成统一格式
    return {
      success: true,
      packages: response
    }
  } catch (error) {
    console.error('获取充值套餐失败:', error)
    throw error
  }
}

/**
 * 创建支付订单
 * @param {Object} orderData - 订单数据
 * @param {number} orderData.amount - 支付金额
 * @param {string} orderData.payment_method - 支付方式 (alipay/wechat)
 * @param {string} orderData.package_id - 套餐ID（可选）
 * @param {number} orderData.user_count - 用户数量（默认为1）
 * @param {string} orderData.remark - 备注信息（可选）
 */
export const createPaymentOrder = async (orderData) => {
  try {
    const response = await request.post('/api/credit-payment/payment/create', orderData)
    return response
  } catch (error) {
    console.error('创建支付订单失败:', error)
    throw error
  }
}

/**
 * 查询支付状态
 * @param {string} orderNo - 订单号
 */
export const queryPaymentStatus = async (orderNo) => {
  try {
    const response = await request.post('/api/credit-payment/payment/query', { order_no: orderNo })
    return response
  } catch (error) {
    console.error('查询支付状态失败:', error)
    throw error
  }
}

/**
 * 获取用户订单列表
 * @param {Object} params - 查询参数
 * @param {string} params.status - 订单状态
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页数量
 */
export const getUserOrders = async (params = {}) => {
  try {
    const response = await request.get('/api/credit-payment/orders', { params })
    return response
  } catch (error) {
    console.error('获取用户订单失败:', error)
    throw error
  }
}

/**
 * 取消订单（关闭交易）
 * @param {string} orderNo - 订单号
 */
export const cancelOrder = async (orderNo) => {
  try {
    const response = await request.post(`/api/credit-payment/payment/close-trade/${orderNo}`)
    return response
  } catch (error) {
    console.error('取消订单失败:', error)
    throw error
  }
}

/**
 * 获取用户积分余额
 */
export const getCreditBalance = async () => {
  try {
    const response = await request.get('/api/credit-payment/credit/balance')
    return response
  } catch (error) {
    console.error('获取积分余额失败:', error)
    throw error
  }
}

/**
 * 获取用户积分账户信息
 */
export const getCreditAccount = async () => {
  try {
    const response = await request.get('/api/credit-payment/credit/account')
    return response
  } catch (error) {
    console.error('获取积分账户信息失败:', error)
    throw error
  }
}

/**
 * 获取积分交易记录
 * @param {Object} params - 查询参数
 * @param {string} params.transaction_type - 交易类型 (recharge/consumption/refund)
 * @param {string} params.service_type - 服务类型 (ai_selection/ai_writing/ai_matching)
 * @param {string} params.start_date - 开始时间(ISO格式)
 * @param {string} params.end_date - 结束时间(ISO格式)
 * @param {number} params.page - 页码 (默认1)
 * @param {number} params.page_size - 每页数量 (默认20,最大100)
 */
export const getCreditTransactions = async (params = {}) => {
  try {
    const response = await request.get('/api/credit-payment/transactions', { params })
    return response
  } catch (error) {
    console.error('获取积分交易记录失败:', error)
    throw error
  }
}

/**
 * 兑换码兑换
 * @param {string} redeemCode - 兑换码
 */
export const redeemCode = async (redeemCode) => {
  try {
    const response = await request.post('/api/credit-payment/redeem-code', {
      redeem_code: redeemCode
    })
    return response
  } catch (error) {
    console.error('兑换码兑换失败:', error)
    throw error
  }
}

/**
 * 获取积分充值档位
 */
export const getCreditBundles = async () => {
  try {
    const response = await request.get('/api/credit-payment/credit-bundles')
    return response
  } catch (error) {
    console.error('获取积分充值档位失败:', error)
    throw error
  }
}

/**
 * 创建积分充值订单
 * @param {Object} orderData - 订单数据
 * @param {string} orderData.amount - 充值金额
 * @param {string} orderData.payment_method - 支付方式 (alipay/wechat)
 */
export const createCreditRechargeOrder = async (orderData) => {
  try {
    const response = await request.post('/api/credit-payment/payment/create', orderData)
    return response
  } catch (error) {
    console.error('创建积分充值订单失败:', error)
    throw error
  }
}

/**
 * 模拟支付成功（仅开发/测试环境使用）
 * @param {string} orderNo - 订单号
 */
export const mockPayment = async (orderNo) => {
  try {
    const response = await request.get(`/api/credit-payment/mock-payment/${orderNo}`)
    return response
  } catch (error) {
    console.error('模拟支付失败:', error)
    throw error
  }
}