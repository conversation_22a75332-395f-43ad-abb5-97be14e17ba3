import request from '@/utils/request'

/**
 * AI写作 - CV相关API
 */

// 注意：CV模块的客户列表已统一使用主客户API (/api/clients/)
// 客户模块数据获取仍使用CV专用接口，因为需要特定的数据格式

// 流式生成CV
export const generateCVStream = (data) => {
  const token = localStorage.getItem('token')
  return fetch('/api/ai-writing/cv/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  })
}

// 保存CV内容
export const saveCVContent = (data) => {
  return request({
    url: '/api/ai-writing/cv/save',
    method: 'post',
    data
  })
}

// 删除CV文档
export const deleteCVDocument = (documentId) => {
  return request({
    url: `/api/ai-writing/cv/documents/${documentId}`,
    method: 'delete'
  })
}

/**
 * AI写作 - PS相关API
 */

// 注意：PS模块的客户列表已统一使用主客户API (/api/clients/)

// 获取客户详细模块数据
export const getPSClientModules = (clientId) => {
  return request({
    url: `/api/ai-writing/ps/clients/${clientId}/modules`,
    method: 'get'
  })
}

// 解析PS申请动机文档
export const parsePSDocument = (formData) => {
  return request({
    url: '/api/ai-writing/ps/parse',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 流式生成PS
export const generatePSStream = (data) => {
  const token = localStorage.getItem('token')

  return fetch('/api/ai-writing/ps/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  })
}

// 保存PS内容
export const savePSContent = (data) => {
  return request({
    url: '/api/ai-writing/ps/save',
    method: 'post',
    data
  })
}

// 获取客户CV版本列表
export const getClientCVsForPS = (clientId) => {
  return request({
    url: `/api/ai-writing/ps/client-cvs/${clientId}`,
    method: 'get'
  })
}

// 获取PS文档详情
export const getPSDocument = (documentId) => {
  return request({
    url: `/api/ai-writing/ps/documents/${documentId}`,
    method: 'get'
  })
}

// 删除PS文档
export const deletePSDocument = (documentId) => {
  return request({
    url: `/api/ai-writing/ps/documents/${documentId}`,
    method: 'delete'
  })
}

/**
 * AI写作 - RL相关API
 */

/**
 * 解析推荐信模板文件 (.docx)
 * @param {FormData} formData 包含文件的表单数据
 * @returns {Promise<any>}
 */
export function parseRLTemplate(data) {
  return request({
    url: '/api/ai-writing/rl/parse',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 流式生成推荐信
 * @param {object} data 请求体
 * @returns {Promise<Response>} 返回原始的 Fetch API Response 对象用于流式读取
 */
export function generateRLStream(data) {
  const token = localStorage.getItem('token'); // 假设token存储在localStorage

  return fetch('/api/ai-writing/rl/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  });
}

export function saveRL(data) {
  return request({
    url: '/api/ai-writing/rl/save',
    method: 'post',
    data
  });
}

// 获取RL文档详情
export const getRLDocument = (documentId) => {
  return request({
    url: `/api/ai-writing/rl/documents/${documentId}`,
    method: 'get'
  })
}

// 删除RL文档
export const deleteRLDocument = (documentId) => {
  return request({
    url: `/api/ai-writing/rl/documents/${documentId}`,
    method: 'delete'
  })
}