import request from '@/utils/request'

export function getDashboardData() {
  return request({
    url: '/dashboard/overview',
    method: 'get'
  })
}

export function getDashboardStatistics() {
  return request({
    url: '/dashboard/statistics',
    method: 'get'
  })
}

export function getClientTrend(params) {
  return request({
    url: '/dashboard/client-trend',
    method: 'get',
    params
  })
}

export function getPlanningDistribution() {
  return request({
    url: '/dashboard/planning-distribution',
    method: 'get'
  })
}

export function getRecentActivities(params) {
  return request({
    url: '/dashboard/recent-activities',
    method: 'get',
    params
  })
} 