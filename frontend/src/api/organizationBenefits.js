import request from '@/utils/request'

/**
 * 组织权益分配相关API
 */

// ===============================
// 套餐分配管理 API
// ===============================

/**
 * 获取可分配的套餐列表
 * @param {number} organizationId - 组织ID
 * @returns {Promise} 可分配套餐列表
 */
export const getAvailablePackages = (organizationId) => {
  return request({
    url: `/api/organization-benefits/available-packages/${organizationId}`,
    method: 'get'
  })
}

/**
 * 分配套餐给成员
 * @param {Object} allocationData - 分配数据
 * @param {number} allocationData.organization_id - 组织ID
 * @param {number} allocationData.allocated_user_id - 分配给的用户ID
 * @param {string} allocationData.package_id - 套餐ID
 * @returns {Promise} 分配结果
 */
export const allocatePackage = (allocationData) => {
  return request({
    url: '/api/organization-benefits/allocate',
    method: 'post',
    data: allocationData
  })
}

/**
 * 回收成员的套餐
 * @param {Object} revocationData - 回收数据
 * @param {number} revocationData.organization_id - 组织ID
 * @param {number} revocationData.allocated_user_id - 回收的用户ID
 * @param {string} revocationData.package_id - 套餐ID
 * @returns {Promise} 回收结果
 */
export const revokePackage = (revocationData) => {
  return request({
    url: '/api/organization-benefits/revoke',
    method: 'post',
    data: revocationData
  })
}

/**
 * 获取组织的套餐分配列表
 * @param {number} organizationId - 组织ID
 * @param {Object} params - 查询参数
 * @param {string} params.status_filter - 状态过滤：active/revoked
 * @returns {Promise} 分配列表
 */
export const getOrganizationAllocations = (organizationId, params = {}) => {
  return request({
    url: `/api/organization-benefits/allocations/${organizationId}`,
    method: 'get',
    params
  })
}

/**
 * 获取当前用户被分配的套餐列表
 * @param {number} organizationId - 组织ID
 * @returns {Promise} 用户分配列表
 */
export const getMyAllocatedPackages = (organizationId) => {
  return request({
    url: `/api/organization-benefits/my-allocations/${organizationId}`,
    method: 'get'
  })
}

// ===============================
// 辅助函数
// ===============================

/**
 * 格式化套餐名称
 * @param {string} packageId - 套餐ID
 * @returns {string} 格式化后的套餐名称
 */
export const formatPackageName = (packageId) => {
  const packageNames = {
    'personal_standard': '个人标准版',
    'personal_premium': '个人高级版',
    'organization_basic': '组织基础版',
    'organization_standard': '组织标准版',
    'organization_premium': '组织高级版',
    'business_flagship': '商业旗舰版',
    'business_premium': '商业高级版',
    'business_standard': '商业标准版',
    'enterprise_flagship': '企业旗舰版',
    'enterprise_premium': '企业高级版'
  }
  
  return packageNames[packageId] || packageId
}

/**
 * 获取套餐详细信息（包含图标和颜色）
 * @param {string} packageId - 套餐ID
 * @returns {Object} 套餐信息对象
 */
export const getPackageInfo = (packageId) => {
  const packageInfo = {
    'personal_standard': {
      name: '个人标准版',
      icon: 'person',
      color: '#4F46E5',
      bgColor: '#EEF2FF'
    },
    'personal_premium': {
      name: '个人高级版',
      icon: 'person_add',
      color: '#7C3AED',
      bgColor: '#F3E8FF'
    },
    'organization_basic': {
      name: '组织基础版',
      icon: 'groups',
      color: '#059669',
      bgColor: '#ECFDF5'
    },
    'organization_standard': {
      name: '组织标准版',
      icon: 'business',
      color: '#DC2626',
      bgColor: '#FEF2F2'
    },
    'organization_premium': {
      name: '组织高级版',
      icon: 'business_center',
      color: '#EA580C',
      bgColor: '#FFF7ED'
    },
    'business_flagship': {
      name: '商业旗舰版',
      icon: 'rocket_launch',
      color: '#C2410C',
      bgColor: '#FFF7ED'
    },
    'business_premium': {
      name: '商业高级版',
      icon: 'trending_up',
      color: '#7C2D12',
      bgColor: '#FEF2F2'
    },
    'business_standard': {
      name: '商业标准版',
      icon: 'business',
      color: '#92400E',
      bgColor: '#FFFBEB'
    },
    'enterprise_flagship': {
      name: '企业旗舰版',
      icon: 'domain',
      color: '#1E40AF',
      bgColor: '#EFF6FF'
    },
    'enterprise_premium': {
      name: '企业高级版',
      icon: 'apartment',
      color: '#1D4ED8',
      bgColor: '#F0F9FF'
    }
  }
  
  return packageInfo[packageId] || {
    name: packageId,
    icon: 'inventory_2',
    color: '#6B7280',
    bgColor: '#F9FAFB'
  }
}

/**
 * 格式化分配状态
 * @param {string} status - 状态
 * @returns {Object} 格式化后的状态信息
 */
export const formatAllocationStatus = (status) => {
  const statusMap = {
    'active': {
      label: '已分配',
      type: 'success',
      color: '#67C23A'
    },
    'revoked': {
      label: '已回收',
      type: 'info',
      color: '#909399'
    }
  }
  
  return statusMap[status] || {
    label: status,
    type: 'info',
    color: '#909399'
  }
}

/**
 * 验证分配权限
 * @param {Object} currentIdentity - 当前身份信息
 * @param {number} organizationId - 组织ID
 * @returns {Object} 权限验证结果
 */
export const validateAllocationPermission = (currentIdentity, organizationId) => {
  // 检查是否为组织身份
  if (currentIdentity?.identity_type !== 'organization') {
    return {
      canAllocate: false,
      reason: '请切换到组织身份后再执行分配操作'
    }
  }
  
  // 检查组织ID是否匹配
  if (currentIdentity?.organization_id !== organizationId) {
    return {
      canAllocate: false,
      reason: '请切换到正确的组织身份后再执行分配操作'
    }
  }
  
  // 检查是否为组织管理员
  if (currentIdentity?.organization_role !== 'owner') {
    return {
      canAllocate: false,
      reason: '只有组织管理员可以执行套餐分配操作'
    }
  }
  
  return {
    canAllocate: true,
    reason: null
  }
}
