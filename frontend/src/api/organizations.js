import request from '@/utils/request'

/**
 * 组织管理相关API
 */

// ===============================
// 组织管理 API
// ===============================

/**
 * 创建组织（仅个人身份）
 * @param {Object} organizationData - 组织数据
 * @param {string} organizationData.name - 组织名称
 * @param {string} organizationData.description - 组织描述
 * @returns {Promise} 创建的组织信息
 */
export const createOrganization = (organizationData) => {
  return request({
    url: '/api/organizations/',
    method: 'post',
    data: organizationData
  })
}

/**
 * 获取用户组织列表
 * @param {Object} params - 查询参数
 * @param {boolean} params.include_stats - 是否包含统计信息
 * @returns {Promise} 组织列表
 */
export const getUserOrganizations = (params = {}) => {
  return request({
    url: '/api/organizations/',
    method: 'get',
    params
  })
}

/**
 * 获取组织详情
 * @param {number} organizationId - 组织ID
 * @returns {Promise} 组织详细信息
 */
export const getOrganizationDetail = (organizationId) => {
  return request({
    url: `/api/organizations/${organizationId}`,
    method: 'get'
  })
}

/**
 * 更新组织信息（仅Owner）
 * @param {number} organizationId - 组织ID
 * @param {Object} organizationData - 组织更新数据
 * @param {string} organizationData.name - 组织名称
 * @param {string} organizationData.description - 组织描述
 * @returns {Promise} 更新后的组织信息
 */
export const updateOrganization = (organizationId, organizationData) => {
  return request({
    url: `/api/organizations/${organizationId}`,
    method: 'put',
    data: organizationData
  })
}

/**
 * 上传组织Logo（仅Owner）
 * @param {number} organizationId - 组织ID
 * @param {File} file - 图片文件（PNG/JPG/JPEG/SVG），最大2MB
 * @param {(progress:number)=>void} onProgress - 进度回调（0-100）
 * @returns {Promise<{success:boolean, logo_url:string}>}
 */
export const uploadOrganizationLogo = (organizationId, file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: `/api/organizations/${organizationId}/logo`,
    method: 'post',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: (event) => {
      if (onProgress && event.total) {
        const percent = Math.round((event.loaded * 100) / event.total)
        onProgress(percent)
      }
    }
  })
}

/**
 * 删除组织（仅Owner）
 * @param {number} organizationId - 组织ID
 * @returns {Promise} 删除结果
 */
export const deleteOrganization = (organizationId) => {
  return request({
    url: `/api/organizations/${organizationId}`,
    method: 'delete'
  })
}

// ===============================
// 组织成员管理 API
// ===============================

/**
 * 获取组织成员列表
 * @param {number} organizationId - 组织ID
 * @param {Object} params - 查询参数
 * @param {number} params.skip - 分页起始位置
 * @param {number} params.limit - 每页数量
 * @param {string} params.search - 搜索关键词
 * @returns {Promise} 成员列表
 */
export const getOrganizationMembers = (organizationId, params = {}) => {
  return request({
    url: `/api/organizations/${organizationId}/members`,
    method: 'get',
    params
  })
}

/**
 * 更新组织成员信息（仅Owner）
 * @param {number} organizationId - 组织ID
 * @param {number} userId - 用户ID
 * @param {Object} memberData - 成员更新数据
 * @param {string} memberData.organization_username - 组织用户名
 * @param {boolean} memberData.is_active - 是否激活
 * @returns {Promise} 更新后的成员信息
 */
export const updateOrganizationMember = (organizationId, userId, memberData) => {
  return request({
    url: `/api/organizations/${organizationId}/members/${userId}`,
    method: 'put',
    data: memberData
  })
}

/**
 * 移除组织成员（仅Owner）
 * @param {number} organizationId - 组织ID
 * @param {number} userId - 用户ID
 * @returns {Promise} 移除结果
 */
export const removeOrganizationMember = (organizationId, userId) => {
  return request({
    url: `/api/organizations/${organizationId}/members/${userId}`,
    method: 'delete'
  })
}

// ===============================
// 身份管理 API
// ===============================

/**
 * 获取用户身份列表
 * @returns {Promise} 用户身份信息
 */
export const getUserIdentities = () => {
  return request({
    url: '/api/auth/identities',
    method: 'get'
  })
}

/**
 * 切换用户身份
 * @param {Object} switchRequest - 切换身份请求
 * @param {string} switchRequest.identity_type - 身份类型：personal/organization
 * @param {number} switchRequest.organization_id - 组织ID（切换到组织身份时必需）
 * @returns {Promise} 新的访问令牌
 */
export const switchIdentity = (switchRequest) => {
  return request({
    url: '/api/auth/switch-identity',
    method: 'post',
    data: switchRequest
  })
}

/**
 * 获取当前身份信息
 * @returns {Promise} 当前身份信息
 */
export const getCurrentIdentity = () => {
  return request({
    url: '/api/auth/current-identity',
    method: 'get'
  })
} 