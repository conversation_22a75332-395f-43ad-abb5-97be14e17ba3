import request from '@/utils/request'

/**
 * 组织邀请相关API
 */

// ===============================
// 旧的邀请API（已废弃，保留用于向后兼容）
// ===============================

/**
 * 创建邀请（已废弃）
 * @deprecated 请使用新的用户邀请码功能
 */
export const createInvitation = (organizationId, invitationData) => {
  return request({
    url: `/api/invitations/organizations/${organizationId}`,
    method: 'post',
    data: invitationData
  })
}

/**
 * 获取组织邀请列表（已废弃）
 * @deprecated 新的邀请模式不再需要邀请列表
 */
export const getOrganizationInvitations = (organizationId, params = {}) => {
  return request({
    url: `/api/invitations/organizations/${organizationId}`,
    method: 'get',
    params
  })
}

/**
 * 取消邀请（已废弃）
 * @deprecated 新的邀请模式不再需要取消邀请
 */
export const cancelInvitation = (organizationId, invitationId) => {
  return request({
    url: `/api/invitations/organizations/${organizationId}/${invitationId}`,
    method: 'delete'
  })
}

/**
 * 获取邀请详情
 * @param {string} invitationCode - 邀请码
 * @returns {Promise} 邀请详情
 */
export const getInvitationDetail = (invitationCode) => {
  return request({
    url: `/api/invitations/${invitationCode}`,
    method: 'get'
  })
}

/**
 * 接受邀请
 * @param {string} invitationCode - 邀请码
 * @returns {Promise} 接受结果
 */
export const acceptInvitation = (invitationCode) => {
  return request({
    url: `/api/invitations/${invitationCode}/accept`,
    method: 'post'
  })
}

/**
 * 根据邀请token获取邀请详情（用于登录页面）
 * @param {string} inviteToken - 邀请token（invitation_code）
 * @returns {Promise} 邀请详情
 */
export const getInvitationByToken = (inviteToken) => {
  return getInvitationDetail(inviteToken)
}

/**
 * 接受邀请（使用token）
 * @param {string} inviteToken - 邀请token（invitation_code）
 * @returns {Promise} 接受结果
 */
export const acceptInvitationByToken = (inviteToken) => {
  return acceptInvitation(inviteToken)
}

// ===============================
// 新的用户邀请码相关API
// ===============================

/**
 * 生成新的动态邀请链接（24小时有效）
 * @param {number} organizationId - 组织ID
 * @returns {Promise} 邀请链接信息
 */
export const getMyInvitationLink = (organizationId) => {
  return request({
    url: `/api/invitations/my-link/${organizationId}`,
    method: 'get'
  })
}

/**
 * 获取组织当前有效的邀请链接列表
 * @param {number} organizationId - 组织ID
 * @returns {Promise} 有效邀请链接列表
 */
export const getActiveInvitationLinks = (organizationId) => {
  return request({
    url: `/api/invitations/active-links/${organizationId}`,
    method: 'get'
  })
}

/**
 * 撤销指定的邀请链接
 * @param {number} invitationId - 邀请记录ID
 * @returns {Promise} 撤销结果
 */
export const revokeInvitationLink = (invitationId) => {
  return request({
    url: `/api/invitations/revoke/${invitationId}`,
    method: 'post'
  })
}

/**
 * 通过用户邀请码获取邀请详情
 * @param {string} code - 用户邀请码
 * @param {number} orgId - 组织ID
 * @returns {Promise} 邀请详情
 */
export const getUserInvitationDetail = (code, orgId) => {
  return request({
    url: `/api/invitations/user-invite-detail`,
    method: 'get',
    params: { code, org: orgId }
  })
}

/**
 * 通过用户邀请码加入组织
 * @param {Object} joinData - 加入组织数据
 * @param {string} joinData.invitation_code - 邀请码
 * @param {number} joinData.organization_id - 组织ID
 * @param {string} joinData.organization_username - 组织内用户名
 * @returns {Promise} 加入结果
 */
export const joinOrganization = (joinData) => {
  return request({
    url: `/api/invitations/join`,
    method: 'post',
    data: joinData
  })
}

// ===============================
// 个人邀请码相关API
// ===============================

/**
 * 获取个人邀请链接
 * @returns {Promise} 个人邀请链接信息
 */
export const getMyPersonalInvitationLink = () => {
  return request({
    url: `/api/invitations/personal/my-link`,
    method: 'get'
  })
}

/**
 * 获取个人邀请统计
 * @returns {Promise} 邀请统计信息
 */
export const getPersonalInvitationStats = () => {
  return request({
    url: `/api/invitations/personal/stats`,
    method: 'get'
  })
}

/**
 * 验证个人邀请码
 * @param {string} invitationCode - 邀请码
 * @returns {Promise} 验证结果
 */
export const validatePersonalInvitationCode = (invitationCode) => {
  return request({
    url: `/api/invitations/personal/validate`,
    method: 'post',
    data: { invitation_code: invitationCode }
  })
}

/**
 * 处理登录后的个人邀请奖励
 * @param {string} invitationCode - 邀请码
 * @returns {Promise} 处理结果
 */
export const processPersonalInvitationReward = (invitationCode) => {
  return request({
    url: `/api/invitations/personal/process-reward`,
    method: 'post',
    data: { invitation_code: invitationCode }
  })
}
