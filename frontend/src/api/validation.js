import request from '@/utils/request'

/**
 * 智能选校验证API服务
 * 提供套餐验证、积分验证和预验证功能
 */

// API基础路径
const API_BASE = '/api/ai-selection/validation'

/**
 * 套餐验证接口
 * 检查用户是否已购买选校套餐
 * 触发时机：用户点击"选校匹配"功能时立即调用
 */
export function validatePackageAccess(debug = false) {
  return request({
    url: `${API_BASE}/package-access-validation`,
    method: 'get',
    params: debug ? { debug: true } : {}
  })
}

/**
 * 套餐状态查询接口
 * 获取详细的套餐状态和过期信息
 */
export function getPackageStatus(debug = false) {
  return request({
    url: `${API_BASE}/package-status`,
    method: 'get',
    params: debug ? { debug: true } : {}
  })
}

/**
 * 积分状态查询接口
 * 检查用户积分余额，判断是否可以使用AI智能匹配
 */
export function getCreditStatus() {
  return request({
    url: `${API_BASE}/credit-status`,
    method: 'get'
  })
}

/**
 * 智能选校预验证接口
 * 用户点击"开始匹配"前的最终验证
 * 作为保险机制，全面检查用户的套餐状态、积分余额和有效期
 */
export function preValidateAISelection() {
  return request({
    url: `${API_BASE}/pre-validation`,
    method: 'post'
  })
}

/**
 * 获取AI选校功能要求说明
 */
export function getAISelectionRequirements() {
  return request({
    url: `${API_BASE}/ai-selection-requirements`,
    method: 'get'
  })
}

/**
 * 智能选校验证服务类
 * 封装所有验证相关的业务逻辑
 */
export class AISelectionValidationService {
  // 缓存验证结果，避免重复请求
  static _cache = {
    packageAccess: null,
    timestamp: 0,
    ttl: 30000 // 缓存30秒
  }

  /**
   * 清除验证缓存
   * 用于套餐购买完成后强制重新验证
   */
  static clearCache() {
    this._cache.packageAccess = null
    this._cache.timestamp = 0
  }
  /**
   * 检查用户是否可以访问选校匹配功能
   * @param {boolean} useCache 是否使用缓存
   * @returns {Promise<Object>} 验证结果
   */
  static async checkAccess(useCache = true) {
    // 检查缓存
    if (useCache && this._cache.packageAccess) {
      const now = Date.now()
      if (now - this._cache.timestamp < this._cache.ttl) {
        return this._cache.packageAccess
      }
    }

    try {
      const response = await validatePackageAccess()
      const result = {
        success: true,
        canAccess: response.data.can_access,
        reason: response.data.reason,
        message: response.data.message,
        packageStatus: response.data.package_status
      }

      // 缓存结果
      this._cache.packageAccess = result
      this._cache.timestamp = Date.now()

      return result
    } catch (error) {
      console.error('套餐验证失败:', error)
      const result = {
        success: false,
        canAccess: false,
        reason: 'network_error',
        message: '网络错误，请稍后重试',
        packageStatus: null
      }

      // 不缓存错误结果
      return result
    }
  }

  /**
   * 获取用户完整状态信息
   * @returns {Promise<Object>} 用户状态
   */
  static async getUserStatus() {
    try {
      const [packageRes, creditRes] = await Promise.all([
        getPackageStatus(),
        getCreditStatus()
      ])

      return {
        success: true,
        package: packageRes.data,
        credit: creditRes.data
      }
    } catch (error) {
      console.error('获取用户状态失败:', error)
      return {
        success: false,
        package: null,
        credit: null
      }
    }
  }

  /**
   * 预验证AI选校功能
   * @returns {Promise<Object>} 验证结果
   */
  static async preValidate() {
    try {
      const response = await preValidateAISelection()
      return {
        success: true,
        canProceed: response.can_proceed,
        reason: response.data?.reason,
        message: response.data?.message || response.message,
        action: response.action
      }
    } catch (error) {
      console.error('预验证失败:', error)
      return {
        success: false,
        canProceed: false,
        reason: 'network_error',
        message: '网络错误，请稍后重试',
        action: null
      }
    }
  }

  /**
   * 检查用户是否可以使用硬筛选模式
   * @param {Object} packageStatus 套餐状态
   * @returns {boolean} 是否可以使用
   */
  static canUseHardFilter(packageStatus) {
    return packageStatus?.has_package && !packageStatus?.is_expired
  }

  /**
   * 检查用户是否可以使用AI智能匹配
   * @param {Object} packageStatus 套餐状态
   * @param {Object} creditStatus 积分状态
   * @returns {boolean} 是否可以使用
   */
  static canUseAISelection(packageStatus, creditStatus) {
    return this.canUseHardFilter(packageStatus) && 
           creditStatus?.sufficient_for_ai_selection
  }

  /**
   * 获取验证失败的处理建议
   * @param {string} reason 失败原因
   * @returns {Object} 处理建议
   */
  static getActionSuggestion(reason) {
    const suggestions = {
      'no_package': {
        title: '未订阅套餐',
        message: '请先购买套餐后使用智能选校功能',
        actionText: '去购买',
        actionUrl: '/account/recharge'
      },
      'package_expired': {
        title: '套餐已过期',
        message: '您的套餐已过期，请续费后继续使用',
        actionText: '立即续费',
        actionUrl: '/account/recharge'
      },
      'insufficient_credits': {
        title: '积分不足',
        message: 'AI智能匹配需要5积分，请充值后使用',
        actionText: '充值积分',
        actionUrl: '/account/recharge'
      },
      'network_error': {
        title: '网络错误',
        message: '网络连接异常，请检查网络后重试',
        actionText: '重试',
        actionUrl: null
      }
    }

    return suggestions[reason] || {
      title: '验证失败',
      message: '验证过程中出现问题，请稍后重试',
      actionText: '重试',
      actionUrl: null
    }
  }
}

export default {
  validatePackageAccess,
  getPackageStatus,
  getCreditStatus,
  preValidateAISelection,
  getAISelectionRequirements,
  AISelectionValidationService
}
