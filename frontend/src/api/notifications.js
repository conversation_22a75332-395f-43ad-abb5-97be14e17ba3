import request from '@/utils/request'

/**
 * 通知相关API接口
 * 
 * 提供与后端FastAPI通知服务的交互功能
 */

/**
 * 获取用户通知列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.pageSize - 每页数量，默认20
 * @param {string} params.type - 通知类型过滤
 * @returns {Promise} API响应
 */
export const getNotifications = (params = {}) => {
  return request({
    url: '/api/notifications',
    method: 'get',
    params: {
      page: 1,
      page_size: 20,
      ...params
    }
  })
}

/**
 * 获取通知数量
 * @returns {Promise} API响应
 */
export const getNotificationCount = () => {
  return request({
    url: '/api/notifications/count',
    method: 'get'
  })
}

/**
 * 获取通知详情
 * @param {string} notificationId - 通知ID
 * @returns {Promise} API响应
 */
export const getNotificationDetail = (notificationId) => {
  return request({
    url: `/api/notifications/${notificationId}`,
    method: 'get'
  })
}

/**
 * 创建系统通知（管理员功能）
 * @param {Object} notification - 通知内容
 * @param {string} notification.title - 通知标题
 * @param {string} notification.content - 通知内容
 * @param {string} notification.type - 通知类型
 * @param {string} notification.priority - 优先级
 * @returns {Promise} API响应
 */
export const createSystemNotification = (notification) => {
  return request({
    url: '/api/admin/notifications',
    method: 'post',
    data: notification
  })
}

/**
 * 获取系统通知列表（管理员功能）
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export const getSystemNotifications = (params = {}) => {
  return request({
    url: '/api/admin/notifications',
    method: 'get',
    params
  })
} 