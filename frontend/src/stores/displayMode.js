import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useDisplayModeStore = defineStore('displayMode', () => {
  // 从 localStorage 恢复显示模式，默认为完整版
  const getSavedMode = () => {
    try {
      return localStorage.getItem('displayMode') || 'full'
    } catch (error) {
      console.warn('读取显示模式缓存失败:', error)
      return 'full'
    }
  }
  
  const mode = ref(getSavedMode())
  
  // 监听模式变化，自动保存到 localStorage
  watch(mode, (newMode) => {
    try {
      localStorage.setItem('displayMode', newMode)
      console.log(`显示模式已保存为：${newMode === 'full' ? '完整版' : '纯净版'}`)
    } catch (error) {
      console.warn('保存显示模式缓存失败:', error)
    }
  }, { immediate: false })
  
  // 切换显示模式
  const toggleMode = () => {
    mode.value = mode.value === 'full' ? 'clean' : 'full'
  }
  
  // 设置为完整版
  const setFullMode = () => {
    mode.value = 'full'
    console.log('显示模式已切换为：完整版')
  }

  // 设置为纯净版
  const setCleanMode = () => {
    mode.value = 'clean'
    console.log('显示模式已切换为：纯净版')
  }

  // 重置为默认模式（完整版）- 只在必要时使用，不影响用户选择
  const resetToDefault = () => {
    mode.value = 'full'
    console.log('显示模式已重置为默认：完整版')
  }
  
  // 判断是否为纯净版
  const isCleanMode = () => mode.value === 'clean'
  
  // 判断是否为完整版
  const isFullMode = () => mode.value === 'full'
  
  return {
    mode,
    toggleMode,
    setFullMode,
    setCleanMode,
    resetToDefault,
    isCleanMode,
    isFullMode
  }
})