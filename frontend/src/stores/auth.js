import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login as loginApi, logout as logoutApi, getCurrentUser, updateProfile, changePassword } from '@/api/auth'
import * as authAPI from '@/api/auth'
import { getUserIdentities, switchIdentity, getCurrentIdentity } from '@/api/organizations'
import tokenRefreshManager from '@/utils/tokenRefresh'

/**
 * 认证状态管理 Store
 *
 * 负责处理用户的认证状态，包括：
 * - 登录/注册/登出
 * - Token 管理
 * - 用户信息管理
 * - 认证状态检查
 * - 用户资料更新
 */
export const useAuthStore = defineStore('auth', () => {
  // 存储认证令牌，从本地存储中恢复
  const token = ref(localStorage.getItem('token') || '')

  // 存储用户信息，从本地存储中恢复
  const user = ref(JSON.parse(localStorage.getItem('user') || 'null'))

  // 加载状态标志
  const loading = ref(false)

  // 错误信息
  const error = ref(null)

  // 是否为开发环境
  const isDev = process.env.NODE_ENV !== 'production'

  // 身份管理状态
  const currentIdentity = ref(JSON.parse(localStorage.getItem('currentIdentity') || 'null'))
  const availableIdentities = ref(JSON.parse(localStorage.getItem('availableIdentities') || '[]'))
  const refreshToken = ref(localStorage.getItem('refresh_token') || '')

  /**
   * 设置认证令牌
   * @param {string|null} newToken - 新的认证令牌，null 表示清除
   */
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      // 存储令牌到本地存储
      localStorage.setItem('token', newToken)
    } else {
      // 从本地存储中移除令牌
      localStorage.removeItem('token')
    }
  }

  /**
   * 设置用户信息
   * @param {Object|null} newUser - 新的用户信息，null 表示清除
   */
  const setUser = (newUser) => {
    user.value = newUser
    if (newUser) {
      // 将用户信息序列化后存储到本地存储
      localStorage.setItem('user', JSON.stringify(newUser))
    } else {
      // 从本地存储中移除用户信息
      localStorage.removeItem('user')
    }
  }

  /**
   * 设置tokens（访问令牌和刷新令牌）
   * @param {string} accessToken - 访问令牌
   * @param {string} refreshTokenValue - 刷新令牌
   */
  const setTokens = (accessToken, refreshTokenValue) => {
    setToken(accessToken)
    if (refreshTokenValue) {
      refreshToken.value = refreshTokenValue
      localStorage.setItem('refresh_token', refreshTokenValue)
    }
  }

  /**
   * 设置当前身份信息
   * @param {Object|null} identity - 身份信息，null 表示清除
   */
  const setCurrentIdentity = (identity) => {
    currentIdentity.value = identity
    if (identity) {
      localStorage.setItem('currentIdentity', JSON.stringify(identity))
    } else {
      localStorage.removeItem('currentIdentity')
    }
  }

  /**
   * 设置可用身份列表
   * @param {Array} identities - 身份列表
   */
  const setAvailableIdentities = (identities) => {
    availableIdentities.value = identities || []
    localStorage.setItem('availableIdentities', JSON.stringify(identities || []))
  }

  /**
   * 用户登录
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @returns {Promise<Object>} 登录响应
   */
  const login = async (username, password) => {
    // 设置加载状态和清除错误
    loading.value = true
    error.value = null

    try {
      // 调用登录API
      const response = await loginApi(username, password)

      // 保存访问令牌
      setToken(response.access_token)

      // 同时保存刷新令牌（用于令牌过期时刷新）
      if (response.refresh_token) {
        localStorage.setItem('refresh_token', response.refresh_token)
      }

      // 确保用户信息中包含昵称
      if (response.user) {
        // 保存用户信息
        setUser(response.user)
        
        // 登录成功后获取身份信息
        try {
          await fetchUserIdentities()
        } catch (identityError) {
          // 身份信息获取失败不影响登录流程
          console.warn('获取身份信息失败:', identityError)
        }
        
        // 启动token自动刷新管理器
        tokenRefreshManager.init()
      }

      return response
    } catch (err) {
      // 保存错误信息
      error.value = err.message

      if (isDev) {
        console.error('登录失败:', err)
      }

      throw err
    } finally {
      // 无论成功失败都结束加载状态
      loading.value = false
    }
  }



  /**
   * 用户登出
   * 执行登出流程，包括：
   * 1. 调用后端登出API
   * 2. 显示登出动画
   * 3. 清除本地存储的认证信息
   * 4. 重定向到登录页
   */
  const logout = () => {
    if (isDev) {
      console.log('执行登出流程')
    }

    // 先执行后端登出方法
    try {
      logoutApi()
    } catch (err) {
      if (isDev) {
        console.warn('后端登出API调用失败:', err)
      }
      // 即使后端API失败，仍继续前端登出流程
    }

    /**
     * 创建登出过渡动画
     * @returns {HTMLElement} 创建的覆盖层元素
     */
    const createLogoutAnimation = () => {
      // 创建全屏覆盖层
      const overlay = document.createElement('div')
      overlay.style.position = 'fixed'
      overlay.style.top = '0'
      overlay.style.left = '0'
      overlay.style.width = '100vw'
      overlay.style.height = '100vh'
      overlay.style.backgroundColor = '#f9fafb'
      overlay.style.zIndex = '9999'
      overlay.style.opacity = '0'
      overlay.style.transition = 'opacity 0.5s ease'

      // 创建Logo容器
      const logoContainer = document.createElement('div')
      logoContainer.style.position = 'absolute'
      logoContainer.style.top = '50%'
      logoContainer.style.left = '50%'
      logoContainer.style.transform = 'translate(-50%, -50%)'
      logoContainer.style.display = 'flex'
      logoContainer.style.flexDirection = 'column'
      logoContainer.style.alignItems = 'center'

      // 创建Logo
      const logo = document.createElement('div')
      logo.style.width = '80px'
      logo.style.height = '80px'
      logo.style.borderRadius = '12px'
      logo.style.background = 'linear-gradient(to right, #4F46E5, #818CF8)'
      logo.style.display = 'flex'
      logo.style.alignItems = 'center'
      logo.style.justifyContent = 'center'
      logo.style.color = 'white'
      logo.style.fontWeight = 'bold'
      logo.style.fontSize = '24px'
      logo.style.marginBottom = '20px'
      logo.style.boxShadow = '0 10px 15px -3px rgba(79, 70, 229, 0.2)'
      logo.style.animation = 'pulse 2s infinite'
      logo.innerHTML = '<span>Tu</span>'

      // 添加动画样式
      const style = document.createElement('style')
      style.textContent = `
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `
      document.head.appendChild(style)

      // 创建加载指示器
      const spinner = document.createElement('div')
      spinner.style.width = '40px'
      spinner.style.height = '40px'
      spinner.style.border = '3px solid #e6e8eb'
      spinner.style.borderTopColor = '#4F46E5'
      spinner.style.borderRadius = '50%'
      spinner.style.animation = 'spin 1s linear infinite'
      spinner.style.marginTop = '20px'

      // 创建文字提示
      const text = document.createElement('div')
      text.style.color = '#4F46E5'
      text.style.fontWeight = '500'
      text.style.marginTop = '20px'
      text.style.fontSize = '16px'
      text.textContent = '正在退出登录...'

      // 组合元素
      logoContainer.appendChild(logo)
      logoContainer.appendChild(spinner)
      logoContainer.appendChild(text)
      overlay.appendChild(logoContainer)
      document.body.appendChild(overlay)

      // 触发渐变效果
      setTimeout(() => {
        overlay.style.opacity = '1'
      }, 10)

      return overlay
    }

    // 执行动画并在完成后清除状态
    createLogoutAnimation()

    // 延迟清除令牌和用户信息，确保动画有足够时间显示
    setTimeout(() => {
      // 停止token自动刷新管理器
      tokenRefreshManager.destroy()
      
      // 清除认证状态
      setToken(null)
      setUser(null)
      setCurrentIdentity(null)
      setAvailableIdentities([])
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('username')

      // 清理自动登出相关的存储
      localStorage.removeItem('auto_logout_sync')
      localStorage.removeItem('last_activity_time')

      // 设置微信二维码强制刷新标记
      localStorage.setItem('wechat_qr_force_refresh', 'true')

      if (isDev) {
        console.log('已清除所有认证信息')
      }

      // 跳转到登录页
      window.location.href = '/login'
    }, 1000)
  }

  /**
   * 检查用户认证状态
   *
   * 验证流程：
   * 1. 检查是否有token
   * 2. 使用token获取当前用户信息
   * 3. 如果token过期，尝试使用refresh token
   * 4. 如果所有尝试都失败，执行登出
   *
   * @returns {Promise<boolean>} 认证状态，true表示已认证，false表示未认证
   */
  const checkAuth = async () => {
    // 检查是否有token
    if (!token.value) {
      if (isDev) {
        console.warn('[认证状态] 无token，未认证状态')
      }
      return false
    }

    try {
      if (isDev) {
        console.log('[认证状态] 正在验证用户认证状态...')
      }

      // 获取当前用户信息
      const currentUser = await getCurrentUser()

      // 确保获取到了有效的用户数据
      if (!currentUser || !currentUser.id) {
        if (isDev) {
          console.error('[认证状态] 获取用户数据失败:', currentUser)
        }
        throw new Error('无法获取用户数据')
      }

      if (isDev) {
        console.log('[认证状态] 认证成功')
      }

      // 更新用户信息
      setUser(currentUser)
      return true
    } catch (err) {
      if (isDev) {
        console.error('[认证状态] 认证验证失败:', err)
      }

      // 如果是401错误，可能是token过期，尝试使用refresh token
      if (err.response?.status === 401) {
        if (isDev) {
          console.log('[认证状态] 尝试使用refresh token...')
        }

        const refresh_token = localStorage.getItem('refresh_token')

        if (refresh_token) {
          try {
            // 动态导入刷新token方法
            const { refreshToken } = await import('@/api/auth')
            const result = await refreshToken()

            if (result && result.access_token) {
              if (isDev) {
                console.log('[认证状态] refresh token成功，重新验证...')
              }

              // 设置新的token
              setToken(result.access_token)

              // 使用新token重新获取用户信息
              const userData = await getCurrentUser()
              if (userData && userData.id) {
                setUser(userData)
                
                // 🔥 修复：token刷新后重新获取身份信息
                try {
                  await fetchUserIdentities()
                } catch (identityError) {
                  console.warn('token刷新后获取身份信息失败:', identityError)
                }
                
                return true
              }
            }
          } catch (refreshErr) {
            if (isDev) {
              console.error('[认证状态] refresh token失败:', refreshErr)
            }
            
            // 🔥 修复：区分网络错误和认证错误
            const isNetworkError = refreshErr.code === 'NETWORK_ERROR' || 
                                  refreshErr.message?.includes('Network Error') ||
                                  !navigator.onLine
            
            if (isNetworkError) {
              // 网络错误时不执行登出，返回token存在状态
              if (isDev) {
                console.log('[认证状态] 网络错误，保持当前登录状态')
              }
              return !!token.value
            }
          }
        }
      }

      // 🔥 修复：对于网络错误，不执行登出
      const isNetworkError = err.code === 'NETWORK_ERROR' || 
                            err.message?.includes('Network Error') ||
                            !navigator.onLine ||
                            err.response?.status >= 500
      
      if (isNetworkError) {
        if (isDev) {
          console.log('[认证状态] 网络错误，保持当前登录状态')
        }
        return !!token.value
      }

      // 只有在明确的认证错误时才执行登出
      logout()
      return false
    }
  }

  /**
   * 更新用户资料
   * @param {Object} data - 要更新的用户资料数据
   * @returns {Promise<Object>} 更新响应
   */
  const updateUserProfile = async (data) => {
    loading.value = true
    error.value = null

    try {
      // 调用更新资料API
      const response = await updateProfile(data)

      // 更新本地用户信息
      if (user.value) {
        // 创建一个新对象，避免直接修改原对象
        const updatedUser = { ...user.value }

        // 更新昵称
        if (data.nickname !== undefined) {
          updatedUser.nickname = data.nickname
        }

        // 更新手机号
        if (data.phone !== undefined) {
          updatedUser.phone = data.phone
        }



        // 更新邮箱
        if (data.email !== undefined) {
          updatedUser.email = data.email
        }

        // 更新时间戳
        if (response.updated_at) {
          updatedUser.updated_at = response.updated_at
        }

        // 保存更新后的用户信息
        setUser(updatedUser)
      }

      return response
    } catch (err) {
      error.value = err.message

      if (isDev) {
        console.error('用户资料更新失败:', err)
      }

      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新用户密码
   * @param {string} oldPassword - 旧密码
   * @param {string} newPassword - 新密码
   * @returns {Promise<Object>} 更新响应
   */
  const updateUserPassword = async (oldPassword, newPassword) => {
    loading.value = true
    error.value = null

    try {
      // 调用修改密码API
      const response = await changePassword(oldPassword, newPassword)

      if (isDev) {
        console.log('密码修改成功')
      }

      return response
    } catch (err) {
      error.value = err.message

      if (isDev) {
        console.error('密码修改失败:', err)
      }

      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取用户名首字母（用于头像显示）
   * @returns {string} 用户名首字母（大写）
   */
  const userInitial = () => {
    if (!user.value) return '?'
    const name = user.value.nickname || user.value.username
    return name ? name.charAt(0).toUpperCase() : '?'
  }

  /**
   * 重置认证状态
   * 用于测试或特殊场景下清除状态
   */
  const resetState = () => {
    setToken(null)
    setUser(null)
    setCurrentIdentity(null)
    setAvailableIdentities([])
    refreshToken.value = ''
    localStorage.removeItem('refresh_token')
    loading.value = false
    error.value = null
  }

  // ===============================
  // 身份管理方法
  // ===============================

  /**
   * 获取用户身份信息
   * @returns {Promise<Object>} 身份信息响应
   */
  const fetchUserIdentities = async () => {
    try {
      loading.value = true
      error.value = null

      const response = await getUserIdentities()
      
      // 更新身份信息
      setCurrentIdentity(response.current_identity)
      setAvailableIdentities(response.available_identities)

      return response
    } catch (err) {
      error.value = err.message || '获取身份信息失败'
      
      // 如果是认证失败，不抛出错误，静默处理
      if (err.response?.status === 401) {
        console.warn('身份信息获取失败：未认证')
        return null
      }
      
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换用户身份
   * @param {Object} switchRequest - 切换身份请求
   * @param {string} switchRequest.identity_type - 身份类型：personal/organization
   * @param {number} switchRequest.organization_id - 组织ID（切换到组织身份时必需）
   * @returns {Promise<Object>} 新的访问令牌
   */
  const switchUserIdentity = async (switchRequest) => {
    try {
      loading.value = true
      error.value = null

      const response = await switchIdentity(switchRequest)
      
      // 更新tokens
      setTokens(response.access_token, response.refresh_token)

      // 刷新身份信息
      await fetchUserIdentities()
      
      // 身份切换成功后，强制重置显示模式为完整版
      try {
        const { useDisplayModeStore } = await import('./displayMode')
        const displayModeStore = useDisplayModeStore()
        displayModeStore.setFullMode()
        console.log('身份切换成功，已重置显示模式为完整版')
      } catch (error) {
        console.warn('重置显示模式失败:', error)
      }
      
      // 重新启动token自动刷新管理器
      tokenRefreshManager.destroy()
      tokenRefreshManager.init()

      return response
    } catch (err) {
      error.value = err.message || '切换身份失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取当前身份信息
   * @returns {Promise<Object>} 当前身份信息
   */
  const fetchCurrentIdentity = async () => {
    try {
      const response = await getCurrentIdentity()
      setCurrentIdentity(response)
      return response
    } catch (err) {
      error.value = err.message || '获取当前身份失败'
      throw err
    }
  }

  /**
   * 获取微信登录URL
   * @returns {Promise<Object>} 包含微信登录URL和state的响应
   */
  const getWeChatLogin = async () => {
    try {
      loading.value = true
      error.value = null

      const response = await authAPI.getWeChatLoginUrl()

      loading.value = false
      return response
    } catch (err) {
      loading.value = false
      error.value = err.message || '获取微信登录URL失败'
      throw err
    }
  }

  /**
   * 获取微信内嵌二维码配置
   * @returns {Promise<Object>} 包含内嵌二维码配置参数的响应
   */
  const getWeChatQRConfig = async () => {
    try {
      loading.value = true
      error.value = null

      const response = await authAPI.getWeChatQRConfig()

      loading.value = false
      return response
    } catch (err) {
      loading.value = false
      error.value = err.message || '获取微信二维码配置失败'
      throw err
    }
  }

  /**
   * 处理微信登录回调
   * @param {string} code - 微信授权码
   * @param {string} state - 状态参数
   * @returns {Promise<Object>} 登录响应
   */
  const handleWeChatCallback = async (code, state) => {
    try {
      loading.value = true
      error.value = null

      const response = await authAPI.weChatCallback(code, state)

      if (response.access_token) {
        setToken(response.access_token)
        setUser(response.user)
        
        // 保存刷新令牌
        if (response.refresh_token) {
          refreshToken.value = response.refresh_token
          localStorage.setItem('refresh_token', response.refresh_token)
        }
        
        // 获取身份信息
        try {
          await fetchUserIdentities()
        } catch (identityError) {
          // 身份信息获取失败不影响登录流程
          console.warn('获取身份信息失败:', identityError)
        }
        
        // 启动token自动刷新管理器
        tokenRefreshManager.init()
      }

      loading.value = false
      return response
    } catch (err) {
      loading.value = false
      error.value = err.message || '微信登录失败'
      throw err
    }
  }

  // 返回store的公共API
  return {
    // 状态
    token,
    user,
    loading,
    error,
    // 身份管理状态
    currentIdentity,
    availableIdentities,
    refreshToken,

    // 方法
    login,
    logout,
    checkAuth,
    updateUserProfile,
    updateUserPassword,
    userInitial,
    resetState,
    getWeChatLogin,
    getWeChatQRConfig,
    handleWeChatCallback,
    // 辅助方法
    setTokens,
    setCurrentIdentity,
    setAvailableIdentities,
    // 身份管理方法
    fetchUserIdentities,
    switchUserIdentity,
    fetchCurrentIdentity
  }
})