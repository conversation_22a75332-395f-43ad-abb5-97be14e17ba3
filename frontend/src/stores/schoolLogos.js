import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '@/utils/request'

export const useSchoolLogosStore = defineStore('schoolLogos', () => {
  // 全局的学校logo缓存
  const logoCache = ref(new Map())
  // 进行中请求去重：school_name_cn -> Promise<string|null>
  const inFlightRequests = ref(new Map())
  // 预加载去重：已尝试预加载的学校集合（避免短时间重复）
  const preloadedSchools = ref(new Set())
  // 批量请求并发去重锁：避免同一批次名称被重复批量请求
  const batchLockedSchools = ref(new Set())

  // 获取学校logo
  const getLogoFromCache = (schoolNameCn) => {
    return logoCache.value.get(schoolNameCn)
  }

  // 缓存学校logo
  const setLogoCache = (schoolNameCn, logoUrl) => {
    logoCache.value.set(schoolNameCn, logoUrl)
  }

  // 检查缓存中是否存在logo
  const hasLogoInCache = (schoolNameCn) => {
    return logoCache.value.has(schoolNameCn)
  }

  // 清除缓存
  const clearCache = () => {
    logoCache.value.clear()
  }

  // 从数据库获取学校Logo URL的方法
  const fetchSchoolLogoFromDB = async (schoolNameCn) => {
    if (!schoolNameCn) return null

    // 命中缓存直接返回
    if (hasLogoInCache(schoolNameCn)) return getLogoFromCache(schoolNameCn)

    // 如果已通过批量预加载尝试过，则不再触发单次查询，避免重复请求
    if (preloadedSchools.value.has(schoolNameCn)) return null

    // 进行中请求去重：复用同一个Promise
    if (inFlightRequests.value.has(schoolNameCn)) {
      try {
        return await inFlightRequests.value.get(schoolNameCn)
      } catch (e) {
        return null
      }
    }

    const p = (async () => {
      try {
        // 统一使用 axios 封装的 request，继承全局拦截器
        const schools = await request({
          url: '/api/ai-selection/data/abroad-schools',
          method: 'get',
          params: { school_name: schoolNameCn, limit: 1 }
        })

        const school = Array.isArray(schools)
          ? schools.find(s => s.school_name_cn === schoolNameCn)
          : null

        const logoUrl = school?.school_logo_url || null
        if (logoUrl) setLogoCache(schoolNameCn, logoUrl)
        return logoUrl
      } catch (error) {
        console.warn(`获取学校logo失败: ${schoolNameCn}`, error)
        return null
      } finally {
        // 请求结束后清理 in-flight 记录
        inFlightRequests.value.delete(schoolNameCn)
      }
    })()

    inFlightRequests.value.set(schoolNameCn, p)
    return await p
  }

  // 批量获取学校Logo（一次请求最多200个）
  const fetchSchoolLogosBatch = async (schoolNames) => {
    const names = Array.isArray(schoolNames) ? schoolNames.filter(Boolean) : []
    if (names.length === 0) return []
    try {
      const result = await request({
        url: '/api/ai-selection/data/abroad-schools/logos',
        method: 'post',
        data: { school_names: names.slice(0, 200) }
      })
      // 期望返回：[{ name, logo_url }]
      return Array.isArray(result) ? result : []
    } catch (e) {
      console.warn('批量获取学校logo失败，将降级到单个请求', e)
      return null
    }
  }

  // 批量预加载学校Logo
  const preloadSchoolLogos = async (schools) => {
    if (!Array.isArray(schools) || schools.length === 0) return

    // 提取所有唯一学校名并过滤：已缓存 / 已在飞行中 / 已预加载 的跳过
    const allNames = [...new Set(
      schools.map(s => s.school_name_cn || s.学校中文名).filter(Boolean)
    )]

    const namesToLoad = allNames.filter(name =>
      !hasLogoInCache(name) &&
      !inFlightRequests.value.has(name) &&
      !preloadedSchools.value.has(name) &&
      !batchLockedSchools.value.has(name)
    )

    if (namesToLoad.length === 0) return

    // 批量并发锁，防止并行触发重复批量请求
    namesToLoad.forEach(n => batchLockedSchools.value.add(n))

    // 先尝试批量接口
    const batchResult = await fetchSchoolLogosBatch(namesToLoad)
    if (batchResult) {
      batchResult.forEach(item => {
        const name = item?.name
        const logoUrl = item?.logo_url || null
        if (!name) return
        batchLockedSchools.value.delete(name)
        preloadedSchools.value.add(name)
        if (logoUrl) setLogoCache(name, logoUrl)
      })
      // 批量成功后不对缺失项触发单项请求，直接用本地fallback，避免再次并发洪峰
      return
    }

    // 降级策略：仅当批量调用失败时，回退到单个查询（并发受 in-flight 控制）
    const targets = namesToLoad
    if (targets.length === 0) return

    const tasks = targets.map(async (name) => {
      try {
        const logoUrl = await fetchSchoolLogoFromDB(name)
        preloadedSchools.value.add(name)
        if (logoUrl) setLogoCache(name, logoUrl)
      } catch (e) {
        preloadedSchools.value.add(name)
        console.warn(`预加载logo失败: ${name}`, e)
      } finally {
        batchLockedSchools.value.delete(name)
      }
    })
    await Promise.allSettled(tasks)
  }

  // Fallback logo获取逻辑
  const getSchoolLogoFallback = (schoolNameCn) => {
    // 学校域名映射
    const domainMapping = {
      '麻省理工学院': 'mit.edu',
      '新加坡国立大学': 'nus.edu.sg',
      '香港大学': 'hku.hk',
      '帝国理工学院': 'imperial.ac.uk',
      '伦敦大学学院': 'ucl.ac.uk',
      '哈佛大学': 'harvard.edu',
      '斯坦福大学': 'stanford.edu',
      '剑桥大学': 'cam.ac.uk',
      '牛津大学': 'ox.ac.uk',
      '清华大学': 'tsinghua.edu.cn',
      '北京大学': 'pku.edu.cn',
      '复旦大学': 'fudan.edu.cn',
      '上海交通大学': 'sjtu.edu.cn',
      '浙江大学': 'zju.edu.cn',
      '南京大学': 'nju.edu.cn',
      '中国人民大学': 'ruc.edu.cn',
      '北京航空航天大学': 'buaa.edu.cn',
      '同济大学': 'tongji.edu.cn',
      '天津大学': 'tju.edu.cn',
      '华中科技大学': 'hust.edu.cn',
      '西安交通大学': 'xjtu.edu.cn',
      '中山大学': 'sysu.edu.cn',
      '哈尔滨工业大学': 'hit.edu.cn',
      '武汉大学': 'whu.edu.cn',
      '东南大学': 'seu.edu.cn',
      '中南大学': 'csu.edu.cn',
      '大连理工大学': 'dlut.edu.cn',
      '北京理工大学': 'bit.edu.cn',
      '厦门大学': 'xmu.edu.cn'
    }

    // 首先检查是否有预定义的域名映射
    if (domainMapping[schoolNameCn]) {
      const domain = domainMapping[schoolNameCn]
      // 使用Google的favicon服务，更可靠
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=128`
    }

    // 生成美观的文字Logo（最终fallback）
    const colors = [
      { bg: '4F46E5', color: 'FFFFFF' }, // 紫色
      { bg: '059669', color: 'FFFFFF' }, // 绿色
      { bg: 'DC2626', color: 'FFFFFF' }, // 红色
      { bg: '2563EB', color: 'FFFFFF' }, // 蓝色
      { bg: 'F59E0B', color: 'FFFFFF' }, // 橙色
      { bg: '7C2D12', color: 'FFFFFF' }, // 棕色
      { bg: '581C87', color: 'FFFFFF' }, // 深紫色
      { bg: '0F766E', color: 'FFFFFF' }  // 青色
    ]
    
    // 使用学校名称的第一个字符来选择颜色
    const firstChar = (schoolNameCn || 'U').charAt(0)
    const colorIndex = firstChar.charCodeAt(0) % colors.length
    const { bg, color } = colors[colorIndex]
    
    // 使用学校名称的前两个字符（如果是中文）或第一个字符（如果是英文）
    let initials = schoolNameCn || 'U'
    if (/[\u4e00-\u9fa5]/.test(initials)) {
      // 中文：取前两个字符
      initials = initials.substring(0, 2)
    } else {
      // 英文：取第一个字符
      initials = initials.charAt(0).toUpperCase()
    }
    
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${bg}&color=${color}&size=128&font-size=0.6&bold=true&format=svg`
  }

  // 获取学校Logo的统一方法
  const getSchoolLogo = (schoolNameCn) => {
    // 方案1: 优先使用缓存中的logo URL
    if (schoolNameCn && hasLogoInCache(schoolNameCn)) {
      const logoUrl = getLogoFromCache(schoolNameCn)
      if (logoUrl && logoUrl.trim() !== '') {
        return logoUrl
      }
    }

    // 不在此触发单个请求，避免并发风暴；统一依赖批量预加载

    // 方案3: 立即返回fallback逻辑
    return getSchoolLogoFallback(schoolNameCn)
  }

  return {
    logoCache,
    getLogoFromCache,
    setLogoCache,
    hasLogoInCache,
    clearCache,
    fetchSchoolLogoFromDB,
    preloadSchoolLogos,
    getSchoolLogoFallback,
    getSchoolLogo
  }
}) 