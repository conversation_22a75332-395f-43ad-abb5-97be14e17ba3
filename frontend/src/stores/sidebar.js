import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useSidebarStore = defineStore('sidebar', () => {
  // 从 localStorage 读取初始状态
  const getInitialCollapsedState = () => {
    const saved = localStorage.getItem('sidebar-collapsed')
    return saved !== null ? JSON.parse(saved) : false
  }

  const getInitialViewMode = () => {
    const saved = localStorage.getItem('sidebar-view-mode')
    return saved !== null ? saved : 'dev'
  }

  // 侧边栏收缩状态 - 立即从 localStorage 初始化
  const isCollapsed = ref(getInitialCollapsedState())

  // 视图模式：'dev' 开发版视角，'production' 上线版视角 - 立即从 localStorage 初始化
  const viewMode = ref(getInitialViewMode())
  
  // 切换侧边栏收缩状态
  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value
    // 保存状态到localStorage
    localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed.value))
  }
  
  // 设置侧边栏收缩状态
  const setCollapsed = (collapsed) => {
    isCollapsed.value = collapsed
    localStorage.setItem('sidebar-collapsed', JSON.stringify(collapsed))
  }
  
  // 切换视图模式
  const toggleViewMode = () => {
    viewMode.value = viewMode.value === 'dev' ? 'production' : 'dev'
    // 保存状态到localStorage
    localStorage.setItem('sidebar-view-mode', viewMode.value)
  }

  // 设置视图模式
  const setViewMode = (mode) => {
    viewMode.value = mode
    localStorage.setItem('sidebar-view-mode', mode)
  }

  // 检查当前是否为开发版视角
  const isDevMode = computed(() => viewMode.value === 'dev')

  // 检查当前是否为上线版视角
  const isProductionMode = computed(() => viewMode.value === 'production')

  // 缓存用户信息的计算属性
  const userInfo = computed(() => {
    try {
      const userStr = localStorage.getItem('user')
      if (!userStr) return null

      return JSON.parse(userStr)
    } catch (error) {
      console.warn('解析用户信息时出错:', error)
      return null
    }
  })

  // 检查用户是否有权限切换视图模式
  const canSwitchViewMode = computed(() => {
    const user = userInfo.value
    if (!user) return false

    const userRole = user.role || 'user'
    // 只有admin和dev用户可以切换视图模式
    return userRole === 'admin' || userRole === 'dev'
  })

  // 获取实际的测试功能显示状态（结合用户权限和视图模式）
  const shouldShowTestFeatures = computed(() => {
    const user = userInfo.value
    if (!user) return false

    const hasTestPermission = user.has_test_access || false

    // 如果用户没有测试权限，永远不显示测试功能
    if (!hasTestPermission) {
      return false
    }

    // 如果用户有测试权限，根据当前视图模式决定
    return isDevMode.value
  })

  // 重新初始化侧边栏状态（从localStorage读取）
  // 注意：状态已经在创建时初始化，这个函数主要用于强制重新读取
  const initializeSidebar = () => {
    // 重新读取收缩状态
    const savedCollapseState = localStorage.getItem('sidebar-collapsed')
    if (savedCollapseState !== null) {
      isCollapsed.value = JSON.parse(savedCollapseState)
    }

    // 重新读取视图模式状态
    const savedViewMode = localStorage.getItem('sidebar-view-mode')
    if (savedViewMode !== null) {
      viewMode.value = savedViewMode
    }
  }
  
  return {
    // 收缩状态
    isCollapsed,
    toggleCollapse,
    setCollapsed,

    // 视图模式
    viewMode,
    toggleViewMode,
    setViewMode,
    isDevMode,
    isProductionMode,
    canSwitchViewMode,
    shouldShowTestFeatures,

    // 初始化
    initializeSidebar
  }
})
