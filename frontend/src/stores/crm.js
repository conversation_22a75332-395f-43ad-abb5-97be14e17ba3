import { defineStore } from 'pinia'
import { clientApi, taskApi, noteApi } from '@/api/crm'

export const useCRMStore = defineStore('crm', {
  state: () => ({
    // 客户相关状态
    clients: [],
    currentClient: null,
    clientsLoading: false,
    clientsTotal: 0,
    
    // 任务相关状态
    tasks: [],
    currentTask: null,
    tasksLoading: false,
    tasksTotal: 0,
    taskStats: {
      total: 0,
      urgent: 0,
      todayDue: 0,
      completed: 0
    },
    
    // 备注相关状态
    clientNotes: [],
    notesLoading: false,
    
    // 筛选和搜索状态
    clientFilters: {
      searchQuery: '',
      statusFilter: '',
      currentPage: 1,
      pageSize: 20
    },
    
    taskFilters: {
      searchQuery: '',
      priorityFilter: '',
      stageFilter: '',
      sortBy: 'dueDate',
      currentPage: 1,
      pageSize: 20
    }
  }),

  getters: {
    // 获取当前页面的客户列表
    getCurrentPageClients: (state) => {
      return state.clients
    },
    
    // 获取特定阶段的任务
    getTasksByStage: (state) => (stage) => {
      return state.tasks.filter(task => task.stage === stage)
    },
    
    // 获取紧急任务
    getUrgentTasks: (state) => {
      return state.tasks.filter(task => task.priority === 'urgent')
    },
    
    // 获取今日到期任务
    getTodayDueTasks: (state) => {
      const today = new Date().toDateString()
      return state.tasks.filter(task => 
        new Date(task.due_date).toDateString() === today
      )
    },
    
    // 获取已完成任务
    getCompletedTasks: (state) => {
      return state.tasks.filter(task => task.status === 'completed')
    },
    
    // 获取客户总数
    getClientCount: (state) => {
      return state.clients.length
    },
    
    // 获取任务统计
    getTaskStatistics: (state) => {
      return {
        total: state.tasks.length,
        urgent: state.tasks.filter(task => task.priority === 'urgent').length,
        todayDue: state.tasks.filter(task => {
          const today = new Date().toDateString()
          return new Date(task.due_date).toDateString() === today
        }).length,
        completed: state.tasks.filter(task => task.status === 'completed').length
      }
    }
  },

  actions: {
    // 客户相关操作
    async fetchClients(params = {}) {
      try {
        this.clientsLoading = true
        const mergedParams = { ...this.clientFilters, ...params }
        const response = await clientApi.getClients(mergedParams)
        
        this.clients = response.data.clients || []
        this.clientsTotal = response.data.total || 0
        
        return response
      } catch (error) {
        console.error('获取客户列表失败:', error)
        throw error
      } finally {
        this.clientsLoading = false
      }
    },

    async fetchClient(id) {
      try {
        const response = await clientApi.getClient(id)
        this.currentClient = response.data
        return response
      } catch (error) {
        console.error('获取客户详情失败:', error)
        throw error
      }
    },

    async createClient(clientData) {
      try {
        const response = await clientApi.createClient(clientData)
        // 添加到本地状态
        this.clients.unshift(response.data)
        this.clientsTotal += 1
        return response
      } catch (error) {
        console.error('创建客户失败:', error)
        throw error
      }
    },

    async updateClient(id, clientData) {
      try {
        const response = await clientApi.updateClient(id, clientData)
        
        // 更新本地状态
        const index = this.clients.findIndex(client => client.id === id)
        if (index !== -1) {
          this.clients[index] = { ...this.clients[index], ...response.data }
        }
        
        // 更新当前客户
        if (this.currentClient && this.currentClient.id === id) {
          this.currentClient = { ...this.currentClient, ...response.data }
        }
        
        return response
      } catch (error) {
        console.error('更新客户失败:', error)
        throw error
      }
    },

    async deleteClient(id) {
      try {
        await clientApi.deleteClient(id)
        
        // 从本地状态移除
        this.clients = this.clients.filter(client => client.id !== id)
        this.clientsTotal -= 1
        
        // 清除当前客户（如果是被删除的客户）
        if (this.currentClient && this.currentClient.id === id) {
          this.currentClient = null
        }
      } catch (error) {
        console.error('删除客户失败:', error)
        throw error
      }
    },

    // 任务相关操作
    async fetchTasks(params = {}) {
      try {
        this.tasksLoading = true
        const mergedParams = { ...this.taskFilters, ...params }
        const response = await taskApi.getTasks(mergedParams)
        
        this.tasks = response.data.tasks || []
        this.tasksTotal = response.data.total || 0
        
        // 更新任务统计
        this.updateTaskStats()
        
        return response
      } catch (error) {
        console.error('获取任务列表失败:', error)
        throw error
      } finally {
        this.tasksLoading = false
      }
    },

    async fetchTask(id) {
      try {
        const response = await taskApi.getTask(id)
        this.currentTask = response.data
        return response
      } catch (error) {
        console.error('获取任务详情失败:', error)
        throw error
      }
    },

    async createTask(taskData) {
      try {
        const response = await taskApi.createTask(taskData)
        
        // 添加到本地状态
        this.tasks.unshift(response.data)
        this.tasksTotal += 1
        this.updateTaskStats()
        
        return response
      } catch (error) {
        console.error('创建任务失败:', error)
        throw error
      }
    },

    async updateTask(id, taskData) {
      try {
        const response = await taskApi.updateTask(id, taskData)
        
        // 更新本地状态
        const index = this.tasks.findIndex(task => task.id === id)
        if (index !== -1) {
          this.tasks[index] = { ...this.tasks[index], ...response.data }
        }
        
        // 更新当前任务
        if (this.currentTask && this.currentTask.id === id) {
          this.currentTask = { ...this.currentTask, ...response.data }
        }
        
        this.updateTaskStats()
        return response
      } catch (error) {
        console.error('更新任务失败:', error)
        throw error
      }
    },

    async deleteTask(id) {
      try {
        await taskApi.deleteTask(id)
        
        // 从本地状态移除
        this.tasks = this.tasks.filter(task => task.id !== id)
        this.tasksTotal -= 1
        this.updateTaskStats()
        
        // 清除当前任务（如果是被删除的任务）
        if (this.currentTask && this.currentTask.id === id) {
          this.currentTask = null
        }
      } catch (error) {
        console.error('删除任务失败:', error)
        throw error
      }
    },

    async updateTaskStatus(id, status) {
      try {
        const response = await taskApi.updateTaskStatus(id, status)
        
        // 更新本地状态
        const index = this.tasks.findIndex(task => task.id === id)
        if (index !== -1) {
          this.tasks[index].status = status
        }
        
        this.updateTaskStats()
        return response
      } catch (error) {
        console.error('更新任务状态失败:', error)
        throw error
      }
    },

    // 备注相关操作
    async fetchClientNotes(clientId) {
      try {
        this.notesLoading = true
        const response = await noteApi.getClientNotes(clientId)
        this.clientNotes = response.data.notes || []
        return response
      } catch (error) {
        console.error('获取客户备注失败:', error)
        throw error
      } finally {
        this.notesLoading = false
      }
    },

    async addClientNote(clientId, content) {
      try {
        const response = await noteApi.addNote(clientId, content)
        
        // 添加到本地状态
        this.clientNotes.unshift(response.data)
        
        return response
      } catch (error) {
        console.error('添加备注失败:', error)
        throw error
      }
    },

    async deleteClientNote(noteId) {
      try {
        await noteApi.deleteNote(noteId)
        
        // 从本地状态移除
        this.clientNotes = this.clientNotes.filter(note => note.id !== noteId)
      } catch (error) {
        console.error('删除备注失败:', error)
        throw error
      }
    },

    // 筛选和搜索操作
    updateClientFilters(filters) {
      this.clientFilters = { ...this.clientFilters, ...filters }
    },

    updateTaskFilters(filters) {
      this.taskFilters = { ...this.taskFilters, ...filters }
    },

    resetClientFilters() {
      this.clientFilters = {
        searchQuery: '',
        statusFilter: '',
        currentPage: 1,
        pageSize: 20
      }
    },

    resetTaskFilters() {
      this.taskFilters = {
        searchQuery: '',
        priorityFilter: '',
        stageFilter: '',
        sortBy: 'dueDate',
        currentPage: 1,
        pageSize: 20
      }
    },

    // 内部辅助方法
    updateTaskStats() {
      this.taskStats = this.getTaskStatistics
    },

    // 清除状态
    clearCurrentClient() {
      this.currentClient = null
    },

    clearCurrentTask() {
      this.currentTask = null
    },

    clearClientNotes() {
      this.clientNotes = []
    },

    // 重置所有状态
    resetStore() {
      this.clients = []
      this.currentClient = null
      this.clientsLoading = false
      this.clientsTotal = 0
      
      this.tasks = []
      this.currentTask = null
      this.tasksLoading = false
      this.tasksTotal = 0
      this.taskStats = {
        total: 0,
        urgent: 0,
        todayDue: 0,
        completed: 0
      }
      
      this.clientNotes = []
      this.notesLoading = false
      
      this.resetClientFilters()
      this.resetTaskFilters()
    }
  }
}) 