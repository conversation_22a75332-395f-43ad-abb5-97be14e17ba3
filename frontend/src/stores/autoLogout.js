/**
 * 自动登出状态管理 Store
 * 
 * 负责管理自动登出功能的状态和配置
 */

import { defineStore } from 'pinia'
import { ref } from 'vue'
import autoLogoutManager from '@/utils/autoLogout'
import { useAuthStore } from './auth'
import { useRouter } from 'vue-router'

export const useAutoLogoutStore = defineStore('autoLogout', () => {
  // 是否启用自动登出
  const isEnabled = ref(true)
  
  // 是否已初始化
  const isInitialized = ref(false)
  
  // 🔥 移除警告状态，提升用户体验
  
  /**
   * 初始化自动登出功能
   */
  const initialize = () => {
    if (isInitialized.value || !isEnabled.value) {
      return
    }
    
    const authStore = useAuthStore()
    
    // 定义登出回调
    const handleAutoLogout = async () => {
      try {
        // 调用auth store的登出方法
        await authStore.logout()
        
        // 重定向到登录页
        const router = useRouter()
        router.push('/login')
      } catch (error) {
        // 即使登出API失败，也要清理本地状态
        authStore.resetState()
        
        // 强制重定向到登录页
        window.location.href = '/login'
      }
    }
    
    // 🔥 移除警告回调，完全静默处理
    const handleWarning = () => {
      // 完全静默，不做任何处理
    }
    
    // 初始化自动登出管理器
    autoLogoutManager.init(handleAutoLogout, handleWarning)
    
    isInitialized.value = true
  }
  
  /**
   * 启用自动登出
   */
  const enable = () => {
    isEnabled.value = true
    if (!isInitialized.value) {
      initialize()
    }
    // 静默启用
  }
  
  /**
   * 禁用自动登出
   */
  const disable = () => {
    isEnabled.value = false
    if (isInitialized.value) {
      autoLogoutManager.stopListening()
    }
    // 静默禁用
  }
  
  /**
   * 重置计时器（用户活动时调用）
   */
  const resetTimer = () => {
    if (isEnabled.value && isInitialized.value) {
      autoLogoutManager.resetTimer()
    }
  }
  

  
  /**
   * 手动触发登出
   */
  const triggerLogout = () => {
    if (isInitialized.value) {
      autoLogoutManager.executeLogout()
    }
  }
  
  /**
   * 销毁自动登出功能
   */
  const destroy = () => {
    if (isInitialized.value) {
      autoLogoutManager.destroy()
      isInitialized.value = false
    }
    // 静默销毁
  }
  

  


  return {
    // 状态
    isEnabled,
    isInitialized,

    // 方法
    initialize,
    enable,
    disable,
    resetTimer,
    triggerLogout,
    destroy
  }
})

/**
 * 自动登出组合式函数
 * 提供便捷的自动登出功能接口
 */
export const useAutoLogout = () => {
  const autoLogoutStore = useAutoLogoutStore()
  
  return {
    ...autoLogoutStore,
    
    /**
     * 在组件中启用自动登出
     */
    enableInComponent() {
      autoLogoutStore.enable()
      
      // 返回清理函数
      return () => {
        autoLogoutStore.disable()
      }
    },
    
    /**
     * 用户活动时调用
     */
    onUserActivity() {
      autoLogoutStore.resetTimer()
    }
  }
}
