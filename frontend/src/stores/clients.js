import { defineStore } from 'pinia'
import { ref } from 'vue'
import clientApi from '@/api/clients'

/**
 * 客户数据管理 Store
 *
 * 负责处理客户相关的数据操作，包括：
 * - 获取客户列表
 * - 获取单个客户详情
 * - 创建/更新/删除客户
 * - 搜索客户
 */
export const useClientStore = defineStore('clients', () => {
  // 是否为开发环境
  const isDev = process.env.NODE_ENV !== 'production'

  // 客户列表
  const clients = ref([])

  // 当前选中的客户
  const currentClient = ref(null)

  // 加载状态标志
  const loading = ref(false)

  // 错误信息
  const error = ref(null)

  /**
   * 获取客户列表
   * @param {Object} params - 查询参数（分页、排序等）
   * @returns {Promise<Object>} 客户列表响应
   */
  const fetchClients = async (params) => {
    loading.value = true
    error.value = null

    try {
      // 调用获取客户列表API
      const response = await clientApi.getClients(params)

      // 更新客户列表
      clients.value = response.data

      if (isDev) {
        console.log(`获取到${response.data.length}个客户`)
      }

      return response
    } catch (err) {
      // 保存错误信息
      error.value = err.message

      if (isDev) {
        console.error('获取客户列表失败:', err)
      }

      throw err
    } finally {
      // 无论成功失败都结束加载状态
      loading.value = false
    }
  }

  /**
   * 获取单个客户详情
   * @param {string|number} id - 客户ID
   * @returns {Promise<Object>} 客户详情响应
   */
  const fetchClientById = async (id) => {
    loading.value = true
    error.value = null

    try {
      // 调用获取客户详情API
      const response = await clientApi.getClientById(id)

      // 更新当前客户
      currentClient.value = response.data

      if (isDev) {
        console.log('获取客户详情成功:', response.data.name || response.data.id)
      }

      return response
    } catch (err) {
      // 保存错误信息
      error.value = err.message

      if (isDev) {
        console.error('获取客户详情失败:', err)
      }

      throw err
    } finally {
      // 无论成功失败都结束加载状态
      loading.value = false
    }
  }

  /**
   * 创建新客户
   * @param {Object} data - 客户数据
   * @returns {Promise<Object>} 创建响应
   */
  const createClient = async (data) => {
    loading.value = true
    error.value = null

    try {
      // 调用创建客户API
      const response = await clientApi.createClient(data)

      // 将新客户添加到列表
      clients.value.push(response.data)

      if (isDev) {
        console.log('创建客户成功:', response.data.name || response.data.id)
      }

      return response
    } catch (err) {
      // 保存错误信息
      error.value = err.message

      if (isDev) {
        console.error('创建客户失败:', err)
      }

      throw err
    } finally {
      // 无论成功失败都结束加载状态
      loading.value = false
    }
  }

  /**
   * 更新客户信息
   * @param {string|number} id - 客户ID
   * @param {Object} data - 更新的客户数据
   * @returns {Promise<Object>} 更新响应
   */
  const updateClient = async (id, data) => {
    loading.value = true
    error.value = null

    try {
      // 调用更新客户API
      const response = await clientApi.updateClient(id, data)

      // 更新客户列表中的对应客户
      const index = clients.value.findIndex(client => client.id === id)
      if (index !== -1) {
        clients.value[index] = response.data
      }

      // 如果当前选中的客户就是被更新的客户，也更新它
      if (currentClient.value && currentClient.value.id === id) {
        currentClient.value = response.data
      }

      if (isDev) {
        console.log('更新客户成功:', response.data.name || response.data.id)
      }

      return response
    } catch (err) {
      // 保存错误信息
      error.value = err.message

      if (isDev) {
        console.error('更新客户失败:', err)
      }

      throw err
    } finally {
      // 无论成功失败都结束加载状态
      loading.value = false
    }
  }

  /**
   * 删除客户
   * @param {string|number} id - 客户ID
   * @returns {Promise<void>}
   */
  const deleteClient = async (id) => {
    loading.value = true
    error.value = null

    try {
      // 调用删除客户API
      await clientApi.deleteClient(id)

      // 从客户列表中移除该客户
      clients.value = clients.value.filter(client => client.id !== id)

      // 如果当前选中的客户就是被删除的客户，清除它
      if (currentClient.value && currentClient.value.id === id) {
        currentClient.value = null
      }

      if (isDev) {
        console.log('删除客户成功:', id)
      }
    } catch (err) {
      // 保存错误信息
      error.value = err.message

      if (isDev) {
        console.error('删除客户失败:', err)
      }

      throw err
    } finally {
      // 无论成功失败都结束加载状态
      loading.value = false
    }
  }

  /**
   * 搜索客户
   * @param {string} keyword - 搜索关键词
   * @returns {Promise<Object>} 搜索结果响应
   */
  const searchClients = async (keyword) => {
    loading.value = true
    error.value = null

    try {
      // 调用搜索客户API
      const response = await clientApi.searchClients(keyword)

      // 更新客户列表为搜索结果
      clients.value = response.data

      if (isDev) {
        console.log(`搜索客户成功，找到${response.data.length}个结果`)
      }

      return response
    } catch (err) {
      // 保存错误信息
      error.value = err.message

      if (isDev) {
        console.error('搜索客户失败:', err)
      }

      throw err
    } finally {
      // 无论成功失败都结束加载状态
      loading.value = false
    }
  }

  /**
   * 重置客户状态
   * 用于测试或特殊场景下清除状态
   */
  const resetState = () => {
    clients.value = []
    currentClient.value = null
    loading.value = false
    error.value = null

    if (isDev) {
      console.log('客户状态已重置')
    }
  }

  // 返回store的公共API
  return {
    // 状态
    clients,
    currentClient,
    loading,
    error,

    // 方法
    fetchClients,
    fetchClientById,
    createClient,
    updateClient,
    deleteClient,
    searchClients,
    resetState
  }
})