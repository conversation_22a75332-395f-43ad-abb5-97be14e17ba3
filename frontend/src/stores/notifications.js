import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  getNotifications, 
  getNotificationCount
} from '@/api/notifications'

/**
 * 通知状态管理 Store
 * 
 * 负责处理系统通知，包括：
 * - 系统公告管理
 * - 更新通知
 * - 本地已读状态管理
 * - 通知计数
 */
export const useNotificationsStore = defineStore('notifications', () => {
  // 通知列表
  const notifications = ref([])
  
  // 加载状态
  const loading = ref(false)
  
  // 通知面板显示状态
  const showPanel = ref(false)

  // 计算通知总数
  const totalCount = computed(() => {
    return notifications.value.length
  })

  // 计算未读通知数量
  const unreadCount = computed(() => {
    return notifications.value.filter(n => !n.isRead).length
  })

  // 计算是否有未读通知
  const hasUnread = computed(() => {
    return unreadCount.value > 0
  })

  /**
   * 初始化通知数据
   */
  const initNotifications = async () => {
    try {
      console.log('开始初始化通知数据...')
      loading.value = true
      await fetchNotifications()
      await fetchNotificationCount()
      console.log('通知数据初始化完成')
    } catch (error) {
      console.error('初始化通知数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取通知列表
   */
  const fetchNotifications = async (page = 1, pageSize = 20) => {
    try {
      console.log('正在获取通知列表...')
      const response = await getNotifications({ page, page_size: pageSize })
      console.log('API响应:', response)
      
      if (response && response.notifications) {
        // 从本地存储获取已读状态
        const readNotifications = JSON.parse(localStorage.getItem('readNotifications') || '[]')
        
        notifications.value = response.notifications.map(n => ({
          id: n.id,
          title: n.title,
          content: n.content,
          type: n.type,
          priority: n.priority,
          isRead: readNotifications.includes(n.id.toString()), // 本地检查已读状态
          readAt: null, // 系统通知不跟踪具体读取时间
          createdAt: n.created_at,
          actionData: n.action_data
        }))
        console.log('成功加载通知数据:', notifications.value.length, '条')
        console.log('未读通知数量:', unreadCount.value, '条')
      }
    } catch (error) {
      console.error('获取通知列表失败:', error)
      throw error
    }
  }

  /**
   * 获取通知总数
   */
  const fetchNotificationCount = async () => {
    try {
      const response = await getNotificationCount()
      if (response && response.total_count) {
        console.log('系统通知总数:', response.total_count)
      }
    } catch (error) {
      console.error('获取通知数量失败:', error)
      throw error
    }
  }

  /**
   * 标记通知为已读（本地存储）
   */
  const markAsRead = async (notificationId) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification && !notification.isRead) {
      notification.isRead = true
      notification.readAt = new Date().toISOString()
      
      // 保存到localStorage
      const readNotifications = JSON.parse(localStorage.getItem('readNotifications') || '[]')
      const idStr = notificationId.toString()
      if (!readNotifications.includes(idStr)) {
        readNotifications.push(idStr)
        localStorage.setItem('readNotifications', JSON.stringify(readNotifications))
        console.log('标记通知为已读:', notificationId)
      }
    }
  }

  /**
   * 标记所有通知为已读（本地存储）
   */
  const markAllAsRead = async () => {
    const readNotifications = JSON.parse(localStorage.getItem('readNotifications') || '[]')
    
    notifications.value.forEach(notification => {
      if (!notification.isRead) {
        notification.isRead = true
        notification.readAt = new Date().toISOString()
        const idStr = notification.id.toString()
        if (!readNotifications.includes(idStr)) {
          readNotifications.push(idStr)
        }
      }
    })
    
    localStorage.setItem('readNotifications', JSON.stringify(readNotifications))
    console.log('标记所有通知为已读')
  }

  /**
   * 切换通知面板显示状态
   */
  const togglePanel = () => {
    showPanel.value = !showPanel.value
  }

  /**
   * 关闭通知面板
   */
  const closePanel = () => {
    showPanel.value = false
  }

  /**
   * 获取通知类型的显示信息
   */
  const getNotificationTypeInfo = (type) => {
    const typeMap = {
      maintenance: { 
        icon: 'build_circle', 
        color: 'text-amber-600', 
        bgColor: 'bg-amber-50',
        borderColor: 'border-amber-200'
      },
      feature: { 
        icon: 'new_releases', 
        color: 'text-primary', 
        bgColor: 'bg-indigo-50',
        borderColor: 'border-indigo-200'
      },
      security: { 
        icon: 'security', 
        color: 'text-red-600', 
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200'
      },
      promotion: { 
        icon: 'local_offer', 
        color: 'text-green-600', 
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200'
      },
      general: { 
        icon: 'info', 
        color: 'text-gray-600', 
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200'
      }
    }
    return typeMap[type] || typeMap.general
  }

  /**
   * 格式化时间显示
   */
  const formatTime = (timeString) => {
    const date = new Date(timeString)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 60) {
      return diffMins <= 1 ? '刚刚' : `${diffMins}分钟前`
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'numeric',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  /**
   * 获取通知优先级显示文本
   */
  const getPriorityText = (priority) => {
    const priorityMap = {
      high: '重要',
      medium: '一般',
      low: '普通'
    }
    return priorityMap[priority] || ''
  }

  /**
   * 获取通知优先级样式
   */
  const getPriorityStyle = (priority) => {
    const styleMap = {
      high: 'bg-red-100 text-red-600 border-red-200',
      medium: 'bg-amber-100 text-amber-600 border-amber-200',
      low: 'bg-gray-100 text-gray-600 border-gray-200'
    }
    return styleMap[priority] || styleMap.low
  }

  return {
    // 状态
    notifications,
    loading,
    showPanel,
    
    // 计算属性
    totalCount,
    unreadCount,
    hasUnread,
    
    // 方法
    initNotifications,
    fetchNotifications,
    fetchNotificationCount,
    markAsRead,
    markAllAsRead,
    togglePanel,
    closePanel,
    getNotificationTypeInfo,
    formatTime,
    getPriorityText,
    getPriorityStyle
  }
}) 