import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getCurrentUser } from '@/api/auth'
import { useAuthStore } from './auth'
import {
  hasRolePermission,
  getRolePermissions,
  hasTestFeatureAccess,
  PERMISSIONS,
  ROLES
} from '@/utils/permissions'

/**
 * 用户信息管理 Store
 *
 * 负责处理当前登录用户的信息，包括：
 * - 获取用户详细信息
 * - 缓存用户信息
 * - 提供用户信息状态
 */
export const useUserStore = defineStore('user', () => {
  // 是否为开发环境
  const isDevEnv = process.env.NODE_ENV !== 'production'

  // 用户详细信息
  const userInfo = ref({})

  // 加载状态标志
  const loading = ref(false)

  // 错误信息
  const error = ref(null)

  // 引用认证Store
  const authStore = useAuthStore()

  /**
   * 获取当前用户信息
   * 从后端API获取当前登录用户的详细信息
   * @param {boolean} [forceRefresh=false] - 是否强制刷新，忽略缓存
   * @returns {Promise<Object|null>} 用户信息或null（如果获取失败）
   */
  const fetchUserInfo = async (forceRefresh = false) => {
    // 如果没有token，则不进行API请求
    if (!authStore.token) {
      return null
    }

    // 如果已有用户信息且不是强制刷新，直接返回缓存的用户信息
    if (!forceRefresh && Object.keys(userInfo.value).length > 0) {
      return userInfo.value
    }

    // 设置加载状态
    loading.value = true
    error.value = null

    // 创建一个AbortController用于超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时

    try {
      // 调用获取当前用户API，添加超时控制和缓存破坏参数
      const data = await getCurrentUser({
        signal: controller.signal,
        _t: new Date().getTime() // 添加时间戳防止缓存
      })

      // 更新用户信息
      userInfo.value = data

      // 同时更新authStore中的用户信息，确保两边保持一致
      if (data && authStore.user) {
        // 创建一个新对象，合并现有用户信息和新获取的信息
        const updatedUser = { ...authStore.user, ...data }

        // 特别确保nickname字段被正确更新
        if (data.nickname) {
          updatedUser.nickname = data.nickname
        }

        // 更新authStore中的用户信息
        authStore.setUser(updatedUser)
      }

      return data
    } catch (err) {
      // 区分超时错误和其他错误
      if (err.name === 'AbortError') {
        console.warn('获取用户信息请求超时')
        // 如果有缓存的用户信息，在超时时返回缓存
        if (Object.keys(userInfo.value).length > 0) {
          return userInfo.value
        }
      }

      // 保存错误信息
      error.value = err.message
      return null
    } finally {
      // 清除超时计时器
      clearTimeout(timeoutId)
      // 无论成功失败都结束加载状态
      loading.value = false
    }
  }

  /**
   * 重置用户信息
   * 用于登出或切换用户时清除状态
   */
  const resetUserInfo = () => {
    userInfo.value = {}
    loading.value = false
    error.value = null

    if (isDevEnv) {
      console.log('用户信息已重置')
    }
  }

  /**
   * 获取用户角色
   * @returns {string} 用户角色，如果未获取到则返回'user'
   */
  const getUserRole = () => {
    return userInfo.value?.role || 'user'
  }

  /**
   * 检查用户是否有特定权限
   * @param {string} permission - 权限标识
   * @returns {boolean} 是否有权限
   */
  const hasPermission = (permission) => {
    const role = getUserRole()

    // 使用权限工具函数检查角色权限
    return hasRolePermission(role, permission)
  }

  /**
   * 获取用户的所有权限列表
   * @returns {string[]} 权限列表
   */
  const getUserPermissions = () => {
    const role = getUserRole()
    return getRolePermissions(role)
  }

  /**
   * 检查用户是否有测试功能访问权限
   * @returns {boolean} 是否有测试功能权限
   */
  const hasTestAccess = () => {
    const role = getUserRole()
    return hasTestFeatureAccess(role)
  }

  /**
   * 检查是否为管理员
   * @returns {boolean} 是否为管理员
   */
  const isAdmin = () => {
    return getUserRole() === ROLES.ADMIN
  }

  /**
   * 检查是否为开发者
   * @returns {boolean} 是否为开发者
   */
  const isDev = () => {
    return getUserRole() === ROLES.DEV
  }

  /**
   * 检查是否为普通用户
   * @returns {boolean} 是否为普通用户
   */
  const isNormalUser = () => {
    return getUserRole() === ROLES.USER
  }

  // 计算属性：响应式权限检查
  const permissions = computed(() => ({
    // 测试功能权限
    testFeatures: hasTestAccess(),

    // 角色检查
    isAdmin: isAdmin(),
    isDev: isDev(),
    isUser: isNormalUser(),

    // 具体功能权限
    canAccessAdminPanel: hasPermission(PERMISSIONS.ADMIN_PANEL),
    canManageUsers: hasPermission(PERMISSIONS.USER_MANAGEMENT),
    canUseDevTools: hasPermission(PERMISSIONS.DEV_TOOLS),
    canManageClients: hasPermission(PERMISSIONS.CLIENT_MANAGEMENT),
    canUseWritingTools: hasPermission(PERMISSIONS.WRITING_TOOLS),
    canUseAITools: hasPermission(PERMISSIONS.AI_TOOLS),
    canUseCRM: hasPermission(PERMISSIONS.CRM_SYSTEM),
    canUseSchoolMatching: hasPermission(PERMISSIONS.SCHOOL_MATCHING),
    canExportDocuments: hasPermission(PERMISSIONS.EXPORT_DOCUMENTS)
  }))

  // 返回store的公共API
  return {
    // 状态
    userInfo,
    loading,
    error,

    // 计算属性
    permissions,

    // 方法
    fetchUserInfo,
    resetUserInfo,
    getUserRole,
    hasPermission,
    getUserPermissions,
    hasTestAccess,
    isAdmin,
    isDev,
    isNormalUser
  }
})