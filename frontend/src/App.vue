<script setup>
// 路由视图组件，用于显示当前路由对应的组件
</script>

<template>
  <router-view v-slot="{ Component }">
    <transition name="global-fade" mode="out-in" :duration="{ enter: 150, leave: 100 }">
      <component :is="Component" />
    </transition>
  </router-view>
</template>

<style>
/* 全局样式可以在这里添加 */

/* 全局路由切换动画 - 优化过渡效果 */
.global-fade-enter-active,
.global-fade-leave-active {
  transition: opacity 0.15s ease;
}

.global-fade-enter-from,
.global-fade-leave-to {
  opacity: 0;
}
</style>
