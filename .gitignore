# Data
案例数据/
专业数据/
院校数据/
项目数据/
data_processing/*.csv
data_processing/*.xlsx
data_processing/merged_cases.txt
data_processing/tunshu_data
data_processing/tunshu_data_env

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.venv
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# VSCode generated files
.vscode/extensions.json

# Environment variables
venv
.env
.env.*
!.env.example

# Build output
.cache/
.temp/
.tmp/
.turbo/

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/
.pnp/
.pnp.js

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Nuxt
.nuxt/
.output/

# Test/Demo files
**/streaming_demo.html
**/extraction_demo.html
**/test_cv_selection.html
**/test_rl_generation.html
**/test_ps_generation.html
**/test_alipay_integration.html
**/test_credit_payment.html
**/test_ai_selection_validation.html

# Redis
*.rdb

# Augment rules
.augment/rules/

# Uploaded assets (keep dir, ignore files)
backend/static/uploads/**
!backend/static/uploads/org_logos/
backend/static/uploads/org_logos/*