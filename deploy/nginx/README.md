# Nginx 反向代理配置

## 概述

本配置文件用于为 TunshuEdu 项目提供 Nginx 反向代理服务，支持：
- 前端应用（Vue 3 + Vite）运行在 3000 端口
- 后端 API（FastAPI）运行在 8000 端口
- 域名：tunshuedu.com
- SSL/HTTPS 支持
- API 限流保护
- 静态资源缓存优化

## 配置特性

### 1. 反向代理配置
- **前端路由**: `https://tunshuedu.com/` → `localhost:3000`
- **API路由**: `https://tunshuedu.com/api/` → `localhost:8000`
- **WebSocket支持**: `https://tunshuedu.com/ws/` → `localhost:8000`

### 2. 安全特性
- SSL/TLS 加密（TLS 1.2 & 1.3）
- 安全头部设置
- CORS 跨域支持
- API 限流保护（10次/秒，登录5次/分钟）

### 3. 性能优化
- Gzip 压缩
- 静态资源缓存（1年）
- HTTP/2 支持
- Keep-alive 连接

## 部署步骤

### 1. 准备工作

确保您的服务器已安装 Nginx：

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
# 或者
sudo dnf install nginx
```

### 2. SSL 证书配置

#### 方式一：使用 Let's Encrypt（推荐）

```bash
# 安装 certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d tunshuedu.com -d www.tunshuedu.com

# 自动更新证书
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 方式二：使用自有证书

修改 `nginx.conf` 中的 SSL 证书路径：

```nginx
ssl_certificate /path/to/your/certificate.crt;
ssl_certificate_key /path/to/your/private.key;
```

### 3. 部署配置文件

```bash
# 备份原配置
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup

# 复制新配置
sudo cp nginx.conf /etc/nginx/nginx.conf

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 4. 启动应用服务

确保您的前端和后端服务正在运行：

```bash
# 启动后端 (在 backend 目录)
cd backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000

# 启动前端 (在 frontend 目录)
cd frontend
npm run dev -- --host 0.0.0.0 --port 3000
```

### 5. Docker 部署示例

如果使用 Docker，可以参考以下 docker-compose.yml：

```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx/nginx.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    restart: unless-stopped

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://...
    restart: unless-stopped
```

## 配置说明

### 关键配置项

1. **Upstream 服务器**
   ```nginx
   upstream frontend {
       server localhost:3000;
   }
   
   upstream backend {
       server localhost:8000;
   }
   ```

2. **API 路由配置**
   ```nginx
   location /api/ {
       proxy_pass http://backend;
       # 其他代理设置...
   }
   ```

3. **前端路由配置**
   ```nginx
   location / {
       proxy_pass http://frontend;
       try_files $uri $uri/ @fallback;
   }
   ```

### 限流配置

- API 通用限制：10 请求/秒，突发 20 个
- 登录接口限制：5 请求/分钟，突发 5 个

### CORS 配置

已配置完整的 CORS 支持，包括：
- 预检请求处理
- 允许的方法和头部
- 凭证支持

## 故障排除

### 1. 检查配置语法
```bash
sudo nginx -t
```

### 2. 查看错误日志
```bash
sudo tail -f /var/log/nginx/error.log
```

### 3. 查看访问日志
```bash
sudo tail -f /var/log/nginx/access.log
```

### 4. 检查服务状态
```bash
sudo systemctl status nginx
```

### 5. 常见问题

#### 502 Bad Gateway
- 检查后端服务是否运行在正确端口
- 检查防火墙设置
- 验证 upstream 配置

#### SSL 证书问题
- 确认证书路径正确
- 检查证书有效期
- 验证域名匹配

#### CORS 错误
- 检查 API 路径是否正确
- 确认 CORS 头部设置
- 验证预检请求处理

## 监控和维护

### 1. 日志轮转
```bash
sudo logrotate -f /etc/logrotate.d/nginx
```

### 2. 性能监控
```bash
# 查看连接状态
sudo ss -tulpn | grep nginx

# 监控资源使用
htop
```

### 3. 定期维护
- 定期更新 SSL 证书
- 监控日志文件大小
- 检查配置文件更新
- 备份配置文件

## 开发环境配置

如果只需要 HTTP 访问（开发环境），可以取消注释配置文件末尾的 HTTP 服务器配置，并注释掉 HTTPS 相关配置。

## 联系信息

如有问题，请参考：
- Nginx 官方文档：https://nginx.org/en/docs/
- Let's Encrypt 文档：https://letsencrypt.org/docs/ 