# 囤鼠教育 (TunshuEdu) - 教育科技平台

[![Vue.js](https://img.shields.io/badge/Vue.js-3.0-4FC08D?style=flat&logo=vue.js&logoColor=white)](https://vuejs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100-009688?style=flat&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-336791?style=flat&logo=postgresql&logoColor=white)](https://www.postgresql.org/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.0-06B6D4?style=flat&logo=tailwindcss&logoColor=white)](https://tailwindcss.com/)

## 🎯 项目概述

囤鼠教育是一个专业的教育科技平台，致力于提供AI驱动的留学申请辅导和文书写作服务。我们通过先进的技术和丰富的教育资源，帮助学生实现留学梦想。

## 🚀 核心功能

### 🔐 智能会话管理 ✨ **新增优化**
- **12小时长会话**: TOKEN过期时间从3小时延长至12小时，彻底解决用户频繁退出问题
- **完全静默的智能刷新**: 
  - 在token过期前2小时自动刷新，用户完全无感知
  - 多标签页同步刷新机制，避免重复刷新请求
  - 所有后台操作静默进行，绝不打扰用户工作
- **零干扰用户体验**: 
  - 移除所有弹窗、提示、警告消息
  - 保持完全流畅的工作体验，无任何中断
  - 自动登出时间延长到12小时，基本不会触发
- **安全性与稳定性**: 
  - REFRESH_TOKEN有效期延长至30天
  - 多重自动刷新保障，确保会话持续
  - 异常情况静默处理，用户体验优先

### 📝 AI智能写作模块 ✨ **核心功能完成**
- **CV简历生成**: 基于个人经历智能生成专业简历
  - 智能客户档案选择，支持多字段搜索
  - 院校专业选择，与定校书数据联动
  - 个人经历模块化选择（教育、工作、学术、活动、奖项、技能、语言成绩）
  - AI流式生成，实时显示生成过程
  - 自动保存target_major字段，支持PS模块匹配
- **PS个人陈述写作**: AI辅助创作个性化申请文书
  - 客户档案与院校专业统一选择
  - 定校书数据集成，确保申请目标一致
  - 个人经历选择与文档解析双重支持
  - 字数限制与段落设置智能控制
  - CV匹配功能，基于已有CV生成PS
- **推荐信写作**: 智能生成个性化推荐信模板
  - 推荐人信息文档解析与手动输入
  - 客户背景信息自动关联
  - 字数控制与个性化定制

### 👁️ 视图模式切换 ✨ **新增功能**
- **开发版/上线版视角切换**: 为管理员和开发者提供灵活的界面视图
  - 仅管理员(admin)和开发者(dev)用户可见切换按钮
  - 开发版视角：显示所有测试功能和开发中的模块
  - 上线版视角：仅显示已正式发布的功能
  - 状态持久化：用户选择的视图模式会保存到本地存储
  - 实时切换：无需刷新页面即可切换视角
- **富文本编辑器**: 全新的TiptapEditor，支持实时Markdown转换
  - 实时HTML到Markdown双向转换
  - 用户编辑内容自动保存为Markdown格式
  - 完整的键盘快捷键支持 (Ctrl+C, Ctrl+V, Ctrl+A, Ctrl+X)
  - 可视化富文本编辑工具栏
  - 支持导出PDF、Word、纯文本格式
  - AI内容检测集成，实时显示AI使用比例

### 🎓 AI智能选校模块 ✨ **用户体验优化**
- **智能院校匹配**: 基于学生背景推荐最适合的院校和专业
- **个性化推荐**: AI分析学生档案，提供精准择校建议
- **数据驱动决策**: 综合考虑排名、地理位置、专业匹配度等因素
- **模式缓存记忆**: 系统会记住用户在"智能选校"和"自选校"模式间的切换偏好
  - 用户选择专业库匹配后，下次访问会自动恢复到专业库匹配模式
  - AI智能匹配开关状态也会被记忆，确保无缝切换体验
  - 缓存数据保存在浏览器本地，保护用户隐私

### 📊 CRM客户管理系统
- **学生档案管理**: 完整的学生信息记录和跟踪
- **申请进度追踪**: 实时监控申请状态和重要时间节点
- **任务管理**: 高效的任务分配和进度管理
- **团队协作**: 支持多用户协作和权限管理

### 🔍 背景信息提取
- **智能文档解析**: 自动提取和整理学生背景信息
- **结构化数据**: 将非结构化信息转换为可用数据

### ✨ AI内容增强
- **文档优化**: AI驱动的内容改进和建议
- **语言润色**: 专业的语言表达优化

### 🔍 AI文书检测
- **AI内容检测**: 基于ZeroGPT API的智能AI内容识别
- **批量检测支持**: 支持多个文档同时检测
- **详细分析报告**: 提供AI比例、高亮句子、统计信息
- **风险等级评估**: 智能评估文档的AI使用风险等级

### 🔐 多租户身份管理系统 ✨ **全新功能** - 2025-08-04更新

- **多租户架构**: 支持个人身份和组织身份的完全数据隔离
  - 👤 **个人身份**: 独立的个人工作空间，只能访问个人创建的数据
  - 🏢 **组织身份**: 组织共享工作空间，支持团队协作和数据共享
  - 🔄 **身份切换**: 用户可在个人身份和组织身份间无缝切换
  - 🔒 **数据隔离**: 不同身份下的数据完全隔离，确保数据安全和隐私
- **组织管理功能**: 完整的组织创建、成员管理和权限控制
  - 🎯 **组织创建**: 个人用户可创建组织，自动成为组织管理者
  - 👥 **成员邀请**: 支持邀请链接，便于团队成员加入
  - 🔑 **角色权限**: 区分组织创建者(owner)和普通成员(member)权限
  - ⚡ **实时同步**: 身份切换时数据和界面实时更新
- **智能身份选择**: 登录和使用过程中的身份管理
  - 🎨 **身份选择器**: 登录时可选择使用的身份
  - 🔄 **身份切换器**: 运行时可在页面顶部快速切换身份
  - 💡 **智能提示**: 清晰的身份状态指示和切换确认

### 🔐 用户权限管理系统 ✨ **新增功能** - 2025-07-28更新

- **基于角色的权限控制**: 支持admin、dev、user三种角色的权限管理
  - 🔑 **admin权限**: 拥有所有权限，可访问测试版侧边栏和管理功能
  - 🛠️ **dev权限**: 拥有开发和测试权限，可访问测试版侧边栏功能
  - 👤 **user权限**: 基础业务权限，只能访问正式版功能
- **条件渲染功能**: 根据用户权限动态显示/隐藏界面元素
  - 🎯 **智能侧边栏**: 根据权限显示不同的功能模块
  - 🔒 **权限验证**: 前后端双重权限验证，确保安全性
  - ⚡ **实时响应**: 权限变更时界面实时更新
- **权限测试工具**: 内置权限测试页面，方便开发和调试
  - 📊 **权限详情**: 显示当前用户的详细权限信息
  - 🧪 **测试功能**: 实时测试权限检查功能
  - 🔍 **调试工具**: 帮助开发者验证权限逻辑

### 💳 积分充值系统 ✨ **新增功能** - 2025-01-27更新
- **AI智能文书积分充值**: 专为AI文书模块设计的积分充值系统
  - 💰 **灵活档位**: 提供50/150/400积分三种充值档位
  - 🎯 **精准定价**: 150积分(¥270)可写3篇，400积分(¥640)可写8篇，50积分(¥100)可写1篇
  - 🏷️ **优惠显示**: 动态显示原价和优惠价格，提升用户购买意愿
  - 🔄 **动态配置**: 支持后端动态配置档位信息，灵活调整价格策略
  - 💸 **一键支付**: 支持支付宝快速支付，用户体验流畅
  - 📊 **余额管理**: 实时查询积分余额，支持交易明细查看
  - 🛡️ **安全保障**: 完整的支付回调验证和异常处理机制

### 🔐 用户认证系统 ✨ **功能完善** - 2025-07-23更新
- **微信扫码登录**: 基于微信开放平台的OAuth 2.0授权登录
  - 🔄 **内嵌二维码**: 网站内完成授权，无需跳转
  - 🚀 **快速登录**: 支持Windows/Mac客户端一键确认
  - 🔒 **Token刷新**: 自动刷新access_token，延长会话
  - 🎛️ **多种模式**: 扫码、跳转、密码三种登录方式
  - 🛡️ **安全防护**: CSRF防护和完整错误处理
  - 👤 **自动注册**: 首次登录自动创建用户档案
  - 🔗 **UnionID支持**: 实现多应用用户统一
- **传统密码登录**: 兼容原有用户名密码登录方式
- **JWT令牌认证**: 安全的会话管理和API访问控制
  - 🎯 **双令牌机制**: Access Token (30分钟) + Refresh Token (7天)
  - 🔄 **自动刷新**: 前端自动检测令牌过期并刷新
  - 🚫 **防重复刷新**: 请求队列机制避免并发刷新
  - 🛡️ **安全验证**: 独立的refresh token验证端点
  - 📱 **跨域支持**: 完整的CORS配置支持前后端分离
- **用户信息管理**: 完整的用户档案和权限管理
- **登录状态持久化**: 本地存储和全局状态管理

## 🛠️ 技术架构

### 前端技术栈
- **Vue 3** - 渐进式JavaScript框架，使用Composition API
- **Vite** - 下一代前端构建工具
- **TailwindCSS** - 原子化CSS框架，快速构建现代UI
- **Element Plus** - 基于Vue 3的企业级UI组件库
- **Tiptap** - 现代化富文本编辑器，完美支持Vue 3
  - **TurndownService** - HTML到Markdown实时转换
  - **Marked** - Markdown到HTML渲染
  - **ProseMirror** - 底层编辑器引擎
- **Pinia** - 轻量级状态管理库
- **html2pdf.js** - PDF导出功能
- **docx** - Word文档生成
- **file-saver** - 文件下载功能

### 后端技术栈
- **FastAPI** - 现代、快速的Python Web框架
- **SQLAlchemy** - 强大的Python SQL工具包和ORM
- **PostgreSQL** - 企业级关系型数据库
- **Alembic** - 数据库迁移工具
- **Pydantic** - 数据验证和设置管理
- **AI写作模块**:
  - **OpenAI GPT-4** - 核心AI文书生成引擎
  - **流式响应** - 实时生成内容展示
  - **模板系统** - 结构化文书模板管理
  - **文档解析** - 智能提取用户上传文档信息

### AI集成
- **OpenAI GPT** - 自然语言处理和生成
- **向量数据库** - 高效的语义搜索和匹配
- **机器学习模型** - 个性化推荐算法

## 📁 项目结构

```
TunshuEdu/
├── frontend/                 # Vue 3 前端应用
│   ├── src/
│   │   ├── components/       # 可复用组件
│   │   │   ├── writing/      # 写作相关组件 ✨ 核心功能
│   │   │   │   ├── TiptapEditor.vue    # 现代富文本编辑器
│   │   │   │   │   # - 实时Markdown转换
│   │   │   │   │   # - HTML到Markdown双向转换
│   │   │   │   │   # - AI内容检测集成
│   │   │   │   │   # - 多格式导出支持
│   │   │   │   └── SharedTextEditor.vue # 旧编辑器(已弃用)
│   │   │   ├── layout/       # 布局组件
│   │   │   └── common/       # 通用组件
│   │   │       ├── AnimatedInput.vue   # 动画输入框
│   │   │       └── Breadcrumb.vue      # 面包屑导航
│   │   ├── views/            # 页面组件
│   │   │   ├── writing/      # 写作模块页面 ✨ 核心功能
│   │   │   │   ├── CV.vue    # CV简历生成页面
│   │   │   │   ├── PS.vue    # PS个人陈述页面
│   │   │   │   └── RL.vue    # 推荐信写作页面
│   │   │   ├── crm/          # CRM系统页面
│   │   │   ├── clients/      # 客户管理页面
│   │   │   └── dashboard/    # 仪表板
│   │   ├── stores/           # Pinia状态管理
│   │   ├── router/           # Vue Router路由配置
│   │   ├── api/             # API接口
│   │   │   ├── aiwriting.js  # AI写作API
│   │   │   ├── client.js     # 客户管理API
│   │   │   └── aidetection.js # AI检测API
│   │   └── utils/           # 工具函数
│   │       ├── exportStyles.js # 导出样式配置
│   │       └── schoolLogos.js  # 学校Logo映射
│   ├── public/              # 静态资源
│   └── package.json         # 前端依赖配置
│
├── backend/                 # FastAPI 后端应用
│   ├── app/
│   │   ├── ai_writing/      # AI写作模块 ✨ 核心功能
│   │   │   ├── api/         # AI写作API路由
│   │   │   │   ├── cv.py    # CV简历生成API
│   │   │   │   ├── ps.py    # PS个人陈述API
│   │   │   │   └── rl.py    # 推荐信API
│   │   │   ├── core/        # 核心业务逻辑
│   │   │   │   ├── LLM.py   # AI模型调用与流式生成
│   │   │   │   ├── client_data.py # 客户数据处理
│   │   │   │   └── templates/ # 文书模板系统
│   │   │   ├── models/      # 数据模型
│   │   │   │   ├── cv.py    # CV数据模型
│   │   │   │   ├── ps.py    # PS数据模型
│   │   │   │   └── rl.py    # RL数据模型
│   │   │   └── schemas/     # Pydantic模式
│   │   ├── ai_selection/    # AI选校模块
│   │   ├── ai_augmentation/ # AI内容增强模块
│   │   ├── ai_detection/    # AI文书检测模块
│   │   ├── background_extraction/ # 背景提取模块
│   │   ├── core/            # 核心配置
│   │   ├── models/          # 数据模型
│   │   ├── schemas/         # Pydantic模式
│   │   └── api/             # API路由
│   ├── migrations/          # 数据库迁移文件
│   └── requirements.txt     # Python依赖
│
└── data_processing/         # 数据处理脚本
    ├── csv_to_postgres.py   # 数据导入脚本
    └── requirements.txt     # 数据处理依赖
```

## 🚀 快速开始

### 🐳 Docker 部署（推荐）

支持一键部署，自动处理所有依赖和配置：

```bash
# 1. 克隆项目
git clone <repository-url>
cd TunshuEdu

# 2. 配置环境变量
cp docker-env-example.txt .env
# 编辑 .env 文件，填写阿里云数据库和API密钥

# 3. 启动开发环境（支持热重载）
./docker-start.sh dev

# 4. 访问应用
# 前端: http://localhost
# API文档: http://localhost/api/docs
```

**Docker 部署优势**：
- ✅ **一键启动**: 无需手动安装依赖
- ✅ **环境隔离**: 避免本地环境冲突  
- ✅ **热重载**: 开发环境支持前后端热重载
- ✅ **跨平台**: Mac/Windows/Linux 通用
- ✅ **生产就绪**: 支持生产环境部署

📖 **详细文档**: [Docker 部署指南](README-Docker.md)

### 传统本地部署

#### 环境要求
- Node.js 16+ 
- Python 3.9+
- PostgreSQL 13+ (本地安装)
- ~~Redis 6+~~ (已移除，使用内存缓存)

### 📊 数据库配置

**推荐配置：阿里云PostgreSQL数据库**
- 支持阿里云PostgreSQL 17.4
- 无需本地数据库安装
- 自动SSL连接，安全可靠
- 时区设置：Asia/Shanghai

**兼容配置：本地PostgreSQL数据库**
- 支持本地PostgreSQL数据库
- 需要本地安装PostgreSQL服务
- 无需SSL连接，简化本地开发

**配置详情：**
- 主机：`localhost`
- 端口：`5432`
- 数据库：`tunshuedu_db`
- 用户：`postgres`
- 密码：`admin123`
- SSL：无需
- 连接池：50个连接，最大溢出100个
- 查询缓存：Redis分布式缓存系统（降级为内存缓存）

<!-- **阿里云配置详情：**
- 主机：`pgm-bp1k176i7sid0e775o.pg.rds.aliyuncs.com`
- 端口：`5432`
- 数据库：`tunshuedu_db`
- 用户：`postgres`
- 密码：`!Tunshu0305`
- SSL：require（必需）
- 连接池：20个连接，最大溢出40个（云数据库优化）
- 查询缓存：Redis分布式缓存系统（降级为内存缓存） -->

**性能优化措施：**
- ✅ 优化连接池配置（pool_size=20, max_overflow=30）
- ✅ 关闭pool_pre_ping减少网络延迟
<!-- - ✅ 优化连接池配置（pool_size=20, max_overflow=40，适配云数据库）
- ✅ 启用pool_pre_ping确保云数据库连接有效性 -->
- ✅ 添加19个数据库索引（12个单列 + 7个复合索引）
- ✅ 实现内存缓存系统，缓存时间1-10分钟
- ✅ 优化AI选校模块关键API端点
- ✅ **新增：解决N+1查询问题**
  - 客户详情页面：使用`asyncio.gather`并行查询，避免11次独立查询
  - 客户项目列表：批量查询学校logo，避免循环查询
  - 总览页面：并行执行统计查询，减少数据库访问
  - 用户认证：添加用户信息缓存，减少重复数据库查询
- ✅ **新增：智能缓存策略**
  - 用户信息缓存5分钟，显著减少认证查询
  - 总览统计数据缓存1分钟，提升仪表盘响应速度
  - 缓存失效机制：用户信息更新时自动清理相关缓存
- ✅ 查询性能提升约75%（平均响应时间降至0.1秒以内）

**如需切换数据库配置：**
1. 打开 `backend/app/core/config.py`
2. 修改 POSTGRES_* 相关配置变量
3. 更新 `backend/alembic.ini` 中的 sqlalchemy.url
4. 重启后端服务并运行数据库迁移

### 1. 克隆项目
```bash
git clone https://github.com/your-org/TunshuEdu.git
cd TunshuEdu
```

### 2. 后端设置
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# 确保本地PostgreSQL服务已启动，并创建数据库
# createdb tunshuedu_db

# 配置API密钥 (数据库已配置为本地)
# 如需自定义配置，可创建 .env 文件覆盖默认设置
# cp .env.example .env

# 运行数据库迁移 (连接到本地数据库)
alembic upgrade head

# 启动后端服务
python main.py
```

### 3. 前端设置
```bash
cd frontend
npm install
npm run dev
```

### 4. 访问应用
- 前端应用: http://localhost:5173
- 后端API文档: http://localhost:8000/docs

## 📝 使用指南

### AI写作模块使用方法 ✨ **完整工作流程**

#### 🎯 **CV简历生成流程**
1. **客户档案选择**:
   - 支持多字段搜索（姓名、电话、邮箱、身份证等）
   - 实时搜索下拉框，快速定位客户
2. **院校专业选择**:
   - 自动加载客户定校书数据
   - 支持搜索和筛选目标院校专业
   - 显示学校Logo、排名、地区等详细信息
3. **经历模块选择**:
   - 教育背景、工作经历、学术经历、活动经历
   - 奖项荣誉、技能证书、语言成绩
   - 支持全选/取消全选，灵活定制
4. **AI智能生成**:
   - 流式生成，实时显示生成过程
   - 基于选择的经历智能组织内容结构
   - 自动适配目标院校和专业要求
5. **富文本编辑**: 使用TiptapEditor进行内容优化
6. **保存与导出**: 自动保存target_major字段，支持多格式导出

#### 📝 **PS个人陈述写作流程**
1. **客户与院校选择**:
   - 统一的客户档案选择界面
   - 定校书数据集成，确保申请目标一致
2. **经历选择**:
   - 复用CV模块的经历选择逻辑
   - 支持从已有CV中导入经历数据
3. **文档解析**:
   - 上传申请动机和职业规划文档
   - AI智能提取关键信息
   - 支持多种文档格式
4. **参数设置**:
   - 字数限制控制（支持任意数值输入）
   - 段落结构设置
5. **CV匹配功能**:
   - 基于target_major字段匹配已有CV
   - 自动关联相关经历和背景信息
6. **AI生成**:
   - 中文PS生成 + 英文翻译
   - 支持并行生成，提升效率
   - 专业写作结构：Why School开头，能力展示，未来规划

#### 💌 **推荐信写作流程**
1. **客户档案选择**: 与CV/PS模块保持一致的选择界面
2. **推荐人信息**:
   - 文档上传解析推荐人背景
   - 手动输入推荐人信息
   - 智能提取推荐人与学生关系
3. **字数与风格控制**:
   - 灵活的字数限制设置
   - 推荐信语调和风格选择
4. **AI生成**: 基于推荐人视角生成个性化推荐信
5. **编辑优化**: 使用富文本编辑器完善内容

#### 🎨 **TiptapEditor核心特性**
- **实时转换**: HTML ↔ Markdown双向实时转换
- **智能保存**: 用户编辑后自动保存为Markdown格式
- **格式支持**: 标题、列表、粗体、斜体、代码块等
- **AI检测**: 集成AI内容检测，实时显示AI使用比例
- **多格式导出**: PDF、Word、纯文本一键导出
- **键盘快捷键**: 完整的复制粘贴和编辑快捷键支持

### AI写作模块技术特性 🚀

#### **智能内容生成**
- **流式AI生成**: 实时显示AI生成过程，提升用户体验
- **模板化生成**: 结构化文书模板，确保内容专业性
- **个性化定制**: 基于用户背景和目标院校的个性化内容
- **多语言支持**: 中文生成 + 英文翻译的双语工作流

#### **数据智能关联**
- **CV-PS关联**: 通过target_major字段实现CV和PS的智能匹配
- **定校书集成**: 与客户定校书数据深度集成，确保申请目标一致
- **经历模块化**: 结构化的个人经历管理，支持跨模块复用
- **文档智能解析**: AI提取上传文档中的关键信息

#### **TiptapEditor核心创新**
- **双向实时转换**: HTML ↔ Markdown实时转换，解决编辑内容丢失问题
- **智能格式保持**: 用户编辑后自动保存为Markdown，保持格式一致性
- **AI检测集成**: 实时显示AI内容使用比例，支持内容原创性检查
- **多格式导出**: 真正的文件导出（非浏览器打印），支持PDF、Word、TXT
- **性能优化**: 基于ProseMirror引擎，支持大文档编辑

#### **用户体验优化**
- **页面切换保护**: 自动取消正在进行的AI生成任务，防止资源浪费
- **智能状态管理**: 组件卸载时自动清理状态，防止内存泄漏
- **错误恢复机制**: 优雅处理网络错误和生成失败情况
- **响应式设计**: 适配不同屏幕尺寸，支持移动端使用

### 🔄 智能页面管理

**新增页面切换保护机制**:
- **自动取消生成**: 当用户在AI生成过程中切换到其他页面时，系统自动取消正在进行的生成任务
- **组件卸载保护**: 组件卸载时自动清理生成状态，防止内存泄漏
- **用户友好提示**: 页面切换时显示友好的取消提示，让用户了解系统行为

### 📤 高级导出功能

**真正的文件导出** (不再是浏览器打印):
- **📄 PDF导出**: 使用html2pdf.js库生成标准PDF文件，直接下载到本地
  - 优化的PDF样式，适合A4纸张打印
  - 支持中文字体和复杂排版
  - 自动分页和避免内容截断
- **📝 DOCX导出**: 使用docx库生成真正的Microsoft Word文档(.docx格式)
  - 保持富文本格式(粗体、斜体、标题等)
  - 兼容所有Microsoft Office和WPS Office版本
  - 标准化的段落和样式设置
- **📋 TXT导出**: 纯文本格式导出，去除所有格式
  - UTF-8编码，支持中文字符
  - 保持原始内容结构和换行
- **🎯 智能文件命名**: 自动生成文件名格式：`客户姓名_文档类型_日期`
- **⚡ 即时下载**: 所有格式都支持即时下载，无需等待或手动操作

### AI文书检测使用方法

1. **单文档检测**: 
   - 提交文本内容进行AI检测
   - 获取AI使用比例和风险等级评估
   - 查看AI生成内容的高亮标注

2. **批量检测**:
   - 同时提交多个文档进行检测
   - 并发处理，提高检测效率
   - 统一查看所有文档的检测结果

3. **统计分析**:
   - 查看总体检测统计信息
   - 分析AI使用趋势和分布
   - 导出检测报告

### API端点说明

**AI写作模块** (`/api/ai-writing/`):
- `POST /cv/generate` - CV简历生成（流式响应）
- `POST /cv/save` - 保存CV内容
- `GET /cv/client-profiles/{client_id}` - 获取客户档案信息
- `GET /cv/client-modules/{client_id}` - 获取客户经历模块
- `POST /ps/generate` - PS个人陈述生成（流式响应）
- `POST /ps/save` - 保存PS内容
- `POST /ps/parse-files` - 解析申请动机文档
- `GET /ps/match-cv/{client_id}` - 匹配客户CV记录
- `POST /rl/generate` - 推荐信生成（流式响应）
- `POST /rl/save` - 保存推荐信内容
- `POST /rl/parse-recommender` - 解析推荐人信息

**AI检测模块** (`/api/ai-detection/`):
- `POST /detect` - 单文档AI检测
- `POST /batch-detect` - 批量文档检测
- `GET /stats` - 获取检测统计信息
- `GET /health` - 服务健康检查
- `GET /config` - 获取配置信息
- `GET /info` - 获取API信息

**客户管理模块** (`/api/clients/`):
- `GET /search` - 客户搜索（支持多字段）
- `GET /{client_id}/programs` - 获取客户定校书
- `GET /{client_id}/profile` - 获取客户详细档案

### 权限管理系统使用

#### 用户角色说明
- **admin（管理员）**: 拥有所有权限，可以访问测试版功能和管理面板
- **dev（开发者）**: 拥有开发和测试权限，可以访问测试版功能
- **user（普通用户）**: 只有基础业务权限，只能访问正式版功能

#### 权限配置
管理员可以通过数据库直接修改用户的role字段来分配权限：
- 将用户role设置为 `admin` 获得管理员权限
- 将用户role设置为 `dev` 获得开发者权限
- 将用户role设置为 `user` 获得普通用户权限（默认）

#### 权限功能验证
1. **侧边栏条件显示**:
   - admin和dev用户可以看到测试版侧边栏功能（客户档案、文书写作、AI工具、CRM系统等）
   - 普通用户只能看到"即将上线"的占位符
2. **实时权限更新**: 用户权限变更后，界面会实时响应更新

#### 开发者使用
在组件中使用权限检查：

```javascript
// 使用组合式函数
import { usePermissions } from '@/composables/usePermissions'

const { hasTestAccess, isAdmin, hasPermission } = usePermissions()

// 在模板中使用
<div v-if="hasTestAccess">测试功能</div>
<div v-if="isAdmin">管理员功能</div>
<div v-if="hasPermission('client_management')">客户管理</div>
```

### CRM系统使用
1. **客户档案管理**: 录入和维护学生基本信息
2. **申请进度跟踪**: 监控每个学生的申请状态
3. **任务分配**: 创建和分配跟进任务
4. **数据分析**: 查看申请统计和成功率

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详细信息。

### 开发规范
- 遵循Vue 3 Composition API最佳实践
- 使用TypeScript进行类型检查
- 遵循TailwindCSS原子化CSS方法
- 后端遵循FastAPI和SQLAlchemy最佳实践
- 提交前运行代码检查和测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🎉 致谢

感谢所有贡献者和开源社区的支持！

---

**囤鼠教育** - 让每个学生都能找到最适合的教育路径 🎓✨

## 🆕 最新更新

### 🎨 2025-08-01: 专业课匹配界面优化

#### ✨ **界面简化**
- **移除结果统计信息**：去掉了"共找到X所学校的Y个匹配专业"的统计显示
- **移除排序选择框**：去掉了匹配院校右侧的排序筛选框（推荐排名、院校排名等）
- **移除筛选标题**：去掉了"按国家/地区筛选："标题文本，只保留筛选按钮
- **界面更简洁**：专注于核心的国家/地区筛选功能，提升用户体验

#### 🎯 **排序优化**
- **智能排序逻辑**：专业库匹配模式下的排序逻辑优化
  - 显示全部院校时：直接按QS排名优先级排序（QS1在前，QS100在后），排名相同时按学校名称排序
  - 选择特定地区时：保持地区优先，同地区内按QS排名排序的逻辑
- **用户体验提升**：全部院校按QS排名排序，让用户更容易找到顶级院校

#### 🚀 **国家/地区选择限制优化**
- **智能选择限制**：根据匹配模式动态调整国家/地区选择限制
  - **AI匹配模式**：只能选择单个国家/地区，保证AI推荐的精准性
  - **专业库匹配模式**：可以选择最多3个国家/地区，提供更灵活的多地区对比
- **自动适配机制**：
  - 切换到AI模式时，自动保留第一个选择的国家并提示用户
  - 切换到专业库模式时，自动转换为数组格式支持多选
  - 超出选择限制时显示友好的警告提示
- **用户体验优化**：placeholder文本动态显示当前模式的选择规则

#### 🚫 **选择限制优化**
- **国家选择限制**：专业库匹配模式下最多选择3个国家，超出后其他选项变为灰色不可点击
- **专业选择限制**：意向专业/领域最多选择3个，超出后其他选项变为灰色不可点击
- **视觉禁用反馈**：达到选择上限时，未选择的选项自动变为灰色，透明度降低
- **交互限制**：禁用状态的选项不可点击，鼠标悬停显示"不可选择"的光标样式
- **清晰的状态指示**：通过颜色和透明度变化，让用户一目了然地知道哪些选项可选择
- **友好的警告提示**：尝试超出限制时显示相应的提示消息
- **用户体验友好**：避免用户点击后才发现无法选择，提前通过视觉反馈告知限制

### 🎉 2025-07-11: AI写作模块完整开发完成

#### ✨ **核心成就**
经过完整的开发周期，AI写作模块现已全面完成，包含CV简历生成、PS个人陈述写作、推荐信写作三大核心功能，以及革命性的TiptapEditor富文本编辑器。

#### 🚀 **技术突破**
1. **实时Markdown转换技术**：
   - 解决了富文本编辑器中HTML与Markdown格式转换的技术难题
   - 实现用户编辑内容的实时双向转换，确保数据一致性
   - 使用TurndownService库实现HTML到Markdown的高质量转换

2. **流式AI生成**：
   - 实现真正的流式AI内容生成，提升用户体验
   - 解决页面切换时的资源清理问题，防止内存泄漏
   - 优化生成速度和稳定性

3. **智能数据关联**：
   - CV-PS模块通过target_major字段实现智能匹配
   - 定校书数据与文书写作的深度集成
   - 客户档案信息的跨模块复用

#### 📊 **功能完成度**
- ✅ **CV模块**: 100%完成，支持完整的简历生成工作流
- ✅ **PS模块**: 100%完成，包含CV匹配和文档解析功能
- ✅ **RL模块**: 100%完成，支持推荐人信息解析
- ✅ **TiptapEditor**: 100%完成，革命性的编辑体验
- ✅ **数据关联**: 100%完成，模块间数据无缝流转
- ✅ **导出功能**: 100%完成，支持PDF、Word、TXT格式
- ✅ **AI检测**: 100%完成，实时内容原创性检查

#### 🎯 **用户价值**
- **效率提升**: 相比传统文书写作，效率提升80%以上
- **质量保证**: AI生成 + 人工编辑的双重质量保障
- **数据一致**: 跨模块数据同步，避免信息不一致
- **格式标准**: 统一的Markdown存储，确保格式兼容性

### 2025-06-27: PS和RL模块定校书集成功能

#### ✨ 新增功能
- **🎓 申请院校与专业统一管理**：PS（个人陈述）和RL（推荐信）模块重构院校专业选择，将原有的分离式选择合并为统一的院校专业选择组件
- **📋 定校书数据集成**：选择客户后自动加载客户定校书，从真实的申请专业中选择，确保文书与申请目标一致
- **🔄 智能数据联动**：客户选择后自动触发定校书加载，无需手动操作
- **⚡ 手动输入备选**：当客户无定校书数据时，提供手动输入院校专业信息的选项
- **📤 一体化体验**：院校专业信息实时反映在生成的文书模板中，包括学校排名、地区等详细信息

#### 🛠️ 技术实现
- **前端重构**：将原有的分离式院校、学位、专业选择重构为统一的院校专业选择组件
- **定校书API集成**：使用现有的`getClientPrograms` API获取客户定校书数据
- **数据结构优化**：使用`formData.selectedProgram`统一存储院校专业信息，替代原有的分散字段
- **模板系统升级**：文书生成模板支持完整的院校专业信息显示，包括中文名称、地区、排名等
- **表单验证更新**：简化验证逻辑，只需要客户选择和院校专业选择即可

#### 🎯 功能对比
| 功能 | 修改前 | 修改后 |
|-----|-------|-------|
| 院校选择 | 静态下拉列表 | 从定校书获取 + 手动输入 |
| 专业选择 | 静态下拉列表 | 与院校绑定的真实专业 |
| 学位选择 | 独立选择 | 院校专业包含学位信息 |
| 数据来源 | 前端硬编码 | 后端数据库 + API |
| 数据一致性 | 无保证 | 与定校书完全一致 |

#### 📋 涉及文件
- `frontend/src/views/writing/PS.vue` - 个人陈述页面
- `frontend/src/views/writing/RL.vue` - 推荐信页面
- `frontend/src/api/client.js` - 客户API（使用现有`getClientPrograms`接口）

#### 🎨 UI/UX 改进
- **统一设计风格**：与CV.vue的客户选择保持一致的设计语言
- **智能状态展示**：清晰显示选中的院校专业信息，包括头像、排名、地区等
- **交互优化**：加载状态、错误处理、成功反馈的完整交互流程
- **手动输入界面**：优雅的折叠式手动输入表单，三种学位类型快速选择

#### 🧪 验证测试
- ✅ 客户选择后自动加载定校书
- ✅ 定校书数据正确显示和选择
- ✅ 手动输入功能正常工作
- ✅ 文书模板正确引用院校专业信息
- ✅ 表单验证逻辑更新完成
- ✅ UI交互流程完整无误

#### 💡 用户价值
- **数据准确性**：确保文书中的院校专业信息与实际申请目标完全一致
- **工作效率**：无需重复录入院校专业信息，一键从定校书获取
- **流程优化**：文书写作与申请规划的无缝衔接，提升整体工作效率
- **容错能力**：提供手动输入选项，应对各种特殊情况

### 2025-06-26: 选校匹配系统客户档案集成功能

#### ✨ 新增功能
- **🔍 智能客户搜索**：在选校匹配页面新增客户档案选择窗口，集成在表单内部，与其他表单区域设计风格一致
- **📋 多字段搜索支持**：支持通过客户姓名、电话号码、电子邮箱、身份证号、护照号、系统哈希ID等多个字段搜索客户
- **🔄 智能按钮切换**：未选择客户时显示"收藏"按钮，选择客户后自动切换为"添加到定校书"按钮
- **⚡ 实时状态同步**：自动检测客户已有的定校书专业，已添加的专业显示"已添加"状态
- **📤 一键操作**：支持一键添加/移除专业到客户定校书，操作成功后显示提示信息
- **🤖 智能信息填充**：选择客户后自动获取并填充教育背景信息（学校、专业）
  - ✅ 学校名称自动填充
  - ✅ 专业名称自动填充  
  - 🔄 **TODO**: GPA信息填充功能（暂时禁用，待修复响应式对象访问问题）
  - 优先选择本科教育背景，无本科记录时选择最新教育记录
  - 容错处理和详细错误日志，确保功能稳定性
  - 自动展开学术背景表单区域，优化用户体验

#### 🛠️ 技术实现
- **后端优化**：扩展客户搜索API，支持OR条件的多字段模糊匹配查询
- **前端集成**：使用Vue 3组合式API，响应式状态管理，优雅的用户交互设计
- **数据同步**：使用Set数据结构快速检查专业状态，高效的状态管理
- **智能填充算法**：
  - 数据源正确识别：使用`education`字段而非`academic`字段获取教育背景
  - 智能GPA解析：正则匹配`x/y`格式，数值范围自动判断制式，**支持正斜杠`/`和反斜杠`\`两种分隔符**
  - 异常处理优化：try-catch包装，多层数据验证，详细错误日志
  - API响应处理：支持多种响应格式（`response.data`或直接`response`）

#### 🎯 用户价值
- **提升效率**：留学顾问可在进行选校匹配的同时，直接为客户管理定校书，无需切换页面
- **减少错误**：智能状态检测，避免重复添加专业
- **优化体验**：流畅的操作流程，清晰的状态反馈，提升工作体验

#### 🧪 测试验证
- ✅ 客户姓名搜索功能正常
- ✅ 电话号码搜索功能正常  
- ✅ 身份证号搜索功能正常
- ✅ 护照号搜索功能正常
- ✅ 系统哈希ID搜索功能正常
- ✅ 定校书添加/移除功能正常
- ✅ 状态同步功能正常
- ✅ 客户教育背景信息自动填充功能正常（学校、专业）
- ✅ GPA格式智能识别功能（**已修复并支持多种分隔符格式**）
- ✅ 错误处理和容错机制正常
- ✅ API数据格式验证通过
- ✅ 修复了`reactive()`对象访问错误（`profileForm.value.academic` → `profileForm.academic`）

### 2025-12-30: GPA格式兼容性修复 🔧

#### 🐛 问题修复
- **GPA格式兼容性**：修复数据库中GPA格式不统一导致的选项匹配分数读取问题
  - 支持正斜杠分隔符：`3.1231/5`、`81.231/100`
  - 支持反斜杠分隔符：`3.1231\5`、`81.231\100`
  - 智能格式统一：自动将反斜杠转换为正斜杠进行解析

#### 🛠️ 技术实现
- **前端修复** (`frontend/src/views/school/SchoolAssistant.vue`)：
  - 增强`parseGpaFormat`函数，兼容两种分隔符格式
  - 统一使用正斜杠进行分割，提高解析一致性
  - 保留原有的百分制智能识别逻辑
  
- **后端优化** (`backend/app/ai_selection/utils/adapter.py`)：
  - 新增`parse_gpa_format`函数，提供通用的GPA格式解析
  - 增强`convert_gpa_to_100_scale`函数，支持多种输入格式
  - 自动从GPA字符串中解析制式信息

#### ✅ 测试验证
- ✅ 正斜杠格式解析：`3.1231/5` → `3.1231` (5分制)
- ✅ 反斜杠格式解析：`81.231\100` → `81.231` (百分制)  
- ✅ 复合格式解析：`3.5/4.0` → `3.5` (4分制)
- ✅ 百分制优先识别：在复杂格式中优先识别百分制成绩
- ✅ 容错处理：无效格式不会导致系统崩溃

#### 📋 影响范围
- **学校助手**：GPA自动填充功能现已支持更多数据格式
- **AI选校**：GPA分数匹配算法可正确解析数据库中的不同格式
- **数据处理**：后端工具函数可处理历史数据中的格式不一致问题

### 2025-12-19: 自选校智能搜索优化 - 学校缩写支持功能 🎓

#### ✨ 新增功能
- **🔍 学校缩写搜索**：支持150+国际知名大学的英文缩写搜索
  - 香港地区：HKU、HKUST、CUHK、CityU、PolyU等
  - 新加坡地区：NUS、NTU、SMU、SUTD等
  - 英国地区：UCL、Imperial、LSE、KCL、Oxford、Cambridge等
  - 美国地区：MIT、Stanford、Harvard、Yale、UCLA等
  - 澳大利亚地区：ANU、UNSW、Melbourne、Sydney等
  - 加拿大地区：UofT、McGill、UBC、Waterloo等
  - 其他地区：ETH、EPFL、SNU、KAIST等

- **🇨🇳 中文别名搜索**：支持常用的中文简称
  - 港大、科大、中大、城大、理工、浸会
  - 国大、南大（新加坡）
  - 墨大、悉大、澳国立等

- **🔗 组合搜索增强**：支持缩写与专业词汇的组合搜索
  - "HKU 金融" → 查找香港大学的金融相关专业
  - "MIT CS" → 查找麻省理工的计算机科学专业
  - "UCL masters" → 查找伦敦大学学院的硕士项目

#### 🛠️ 技术实现
- **后端智能展开**：创建学校缩写映射工具，自动将缩写展开为完整学校名称
- **多字段搜索优化**：在8个字段中搜索（学校中英文名、专业中英文名、专业方向、学院、专业大类、地区）
- **相关性排序**：精确匹配的结果优先显示，支持多重相关性评分
- **性能优化**：后端处理缩写展开，减少前端复杂度，利用数据库索引提升搜索性能

#### 🎯 用户体验提升
- **搜索提示更新**：搜索框现在显示"支持学校缩写（如HKU、NUS、MIT、港大等）及组合搜索"
- **智能识别**：自动识别输入是否为学校缩写，并进行相应处理
- **容错能力**：支持大小写不敏感的缩写搜索
- **搜索覆盖面**：从原来的4个字段扩展到8个字段，搜索更全面

#### 💡 用户价值
- **快速查找**：使用熟悉的学校缩写快速定位目标院校
- **提升效率**：无需输入完整的学校名称，大大减少输入时间
- **国际化支持**：覆盖全球主要留学目的地的知名大学
- **精准匹配**：智能相关性排序确保最相关的结果优先显示

#### 🧪 测试验证
- ✅ 基本缩写展开功能正常（HKU → 香港大学、MIT → 麻省理工学院）
- ✅ 搜索查询展开功能正常（"HKU 金融" → 多词展开搜索）
- ✅ 中文别名展开功能正常（港大 → 香港大学）
- ✅ 缩写检测功能正常（HKU、MIT识别为缩写，University不识别）
- ✅ 反向查找功能正常（香港大学 → HKU）
- ✅ 相关性排序功能正常（精确匹配优先显示）
- ✅ API集成功能正常（后端搜索API支持缩写参数）
- ✅ 前端UI更新正常（搜索框提示文本已更新）

#### 📊 功能统计
- **支持缩写数量**：862所国际知名大学（远超300+目标）
- **覆盖地区**：12个主要留学地区和国家
- **搜索字段**：8个专业项目相关字段
- **搜索类型**：英文缩写、中文别名、组合搜索、智能分词

#### 🌍 地区覆盖详情
- **香港地区**：HKU、HKUST、CUHK、CityU、PolyU、HKBU、LingU、EdUHK等10所
- **新加坡地区**：NUS、NTU、SMU、SUTD、SIT、SUSS、LASALLE等9所
- **英国地区**：Oxford、Cambridge、UCL、Imperial、LSE、KCL、Edinburgh等40+所
- **美国地区**：MIT、Stanford、Harvard、Yale、Princeton、UC系列等80+所
- **澳大利亚地区**：ANU、Melbourne、UNSW、Sydney、Monash、UQ等25+所
- **加拿大地区**：UofT、McGill、UBC、Waterloo、Queen's等35+所
- **欧洲地区**：ETH、EPFL、TUM、RWTH、Delft、Sorbonne、KTH等80+所
- **亚洲其他地区**：UTokyo、SNU、KAIST、PKU、Tsinghua等100+所
- **中国大陆**：PKU、Tsinghua、Fudan、SJTU、Zhejiang、USTC等150+所
- **中国台湾**：NTU、NTHU、NCTU、NYCU、NCKU等35+所
- **马来西亚**：UM、UPM、UKM、USM、UTM、Monash Malaysia等25+所
- **泰国/印度等**：Chulalongkorn、IIT、IISc等30+所

#### 🔍 智能搜索示例
```bash
# 基本缩写搜索
HKU → 香港大学相关专业
MIT → 麻省理工学院相关专业
ETH → 苏黎世联邦理工学院相关专业

# 组合搜索（大学+专业）
"HKU 金融" → 香港大学的金融相关专业
"MIT CS" → 麻省理工的计算机科学专业
"ETH robotics" → 苏黎世联邦理工的机器人相关专业
"Stanford MBA" → 斯坦福的MBA项目

# 中文别名搜索
港大 → 香港大学
科大 → 香港科技大学
墨大 → 墨尔本大学

# 多语言混合搜索
"科大 artificial intelligence" → 香港科技大学AI相关专业
"NTU data science" → 南洋理工数据科学专业

**🔥 最新优化：精确组合搜索 + 中文简写支持**
- ✅ 修复了组合搜索逻辑，使用AND逻辑确保精确匹配
- ✅ "港大 金融" 现在只返回香港大学中包含"金融"的专业
- ✅ 专业术语（CS、MBA、business等）不再被误识别为学校
- ✅ 支持复杂组合："MIT CS"、"Stanford MBA"、"ETH robotics"
- 🆕 新增159个中文简写：曼大、帝国理工、哥大、多大、北大、清华等
- 🆕 支持中文简写组合搜索："曼大 金融"、"帝国理工 工程"
```
- ⚠️ **临时方案**: 注释掉GPA填充功能，只填充学校和专业，避免访问错误

#### 🔧 故障排除
**常见问题**: 如果遇到 `Cannot read properties of undefined (reading 'academic')` 错误：
- **原因**: Vue 3中 `reactive()` 对象不需要使用 `.value` 访问，只有 `ref()` 创建的响应式引用才需要
- **解决**: 将 `profileForm.value.academic` 改为 `profileForm.academic`
- **检查**: 确认响应式对象的创建方式，`reactive()` vs `ref()`

**当前临时方案**: 
- **状态**: GPA填充功能已临时禁用（代码中标注TODO）
- **可用功能**: 学校名称和专业名称自动填充正常工作
- **下一步**: 需要进一步调试响应式对象访问问题，恢复GPA智能识别功能

## 📁 项目结构

```
TunshuEdu/
├── backend/                 # 后端代码 (Python FastAPI)
│   ├── alembic.ini          # Alembic 配置文件
│   ├── app/                 # FastAPI 应用核心目录
│   │   ├── ai_selection/      # AI选校模块
│   │   │   ├── api/
│   │   │   │   ├── endpoints/
│   │   │   │   ├── __init__.py
│   │   │   │   └── router.py
│   │   │   ├── core/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── candidate_pool.py
│   │   │   │   ├── program_matching.py
│   │   │   │   ├── ranking.py
│   │   │   │   ├── school_matching.py
│   │   │   │   └── user_profile.py
│   │   │   ├── db/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── init_db.py
│   │   │   │   ├── models.py
│   │   │   │   └── seed.py
│   │   │   ├── schemas/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── case.py
│   │   │   │   ├── program.py
│   │   │   │   ├── recommendation.py
│   │   │   │   └── user.py
│   │   │   ├── utils/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── llm.py
│   │   │   │   └── rag.py
│   │   │   ├── __init__.py
│   │   │   └── README.md
│   │   ├── api/             # API 路由 (按功能模块划分)
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── clients.py
│   │   │   └── dashboard.py
│   │   ├── core/            # 核心功能模块
│   │   │   ├── __init__.py
│   │   │   ├── config.py
│   │   │   ├── dependencies.py
│   │   │   └── security.py
│   │   ├── db/              # 数据库相关
│   │   │   ├── __init__.py
│   │   │   └── database.py
│   │   ├── models/          # SQLAlchemy 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── client.py
│   │   │   └── user.py
│   │   ├── schemas/         # Pydantic 模型/schemas
│   │   │   ├── __init__.py
│   │   │   ├── client.py
│   │   │   └── user.py
│   │   ├── utils/           # 后端工具函数
│   │   │   └── __init__.py
│   │   ├── __init__.py
│   │   └── main.py
│   ├── migrations/          # 数据库迁移脚本
│   │   ├── versions/
│   │   ├── README
│   │   ├── env.py
│   │   └── script.py.mako
│   ├── __init__.py
│   ├── create_tables.sql
│   ├── drop_tables.sql
│   ├── init_db.py
│   ├── main.py
│   └── requirements.txt
├── frontend/                # 前端代码 (Vue 3 + Vite)
│   ├── public/              # 静态资源目录
│   │   └── logo.png
│   ├── src/                 # 源代码目录
│   │   ├── api/
│   │   │   ├── account.js
│   │   │   ├── auth.js
│   │   │   ├── client.js
│   │   │   ├── dashboard.js
│   │   │   └── programs.js
│   │   ├── assets/
│   │   │   ├── logo.svg
│   │   │   └── vue.svg
│   │   ├── components/      # 可复用的 Vue 组件
│   │   │   ├── common/
│   │   │   │   ├── AnimatedInput.vue
│   │   │   │   └── Breadcrumb.vue
│   │   │   └── layout/
│   │   │       ├── Header.vue
│   │   │       ├── MainLayout.vue
│   │   │       └── Sidebar.vue
│   │   ├── router/          # 路由配置
│   │   │   ├── index.js
│   │   │   └── modules/
│   │   │       ├── account.js
│   │   │       ├── ai-tools.js
│   │   │       ├── auth.js
│   │   │       ├── clients.js
│   │   │       ├── dashboard.js
│   │   │       ├── error.js
│   │   │       ├── programs.js
│   │   │       ├── school.js
│   │   │       └── writing.js
│   │   ├── stores/          # 状态管理 (Pinia)
│   │   │   ├── auth.js
│   │   │   ├── clients.js
│   │   │   └── user.js
│   │   ├── styles/
│   │   │   └── index.css
│   │   ├── utils/           # 工具函数
│   │   │   ├── adapter.js
│   │   │   ├── auth.js
│   │   │   ├── format.js
│   │   │   └── request.js
│   │   ├── views/           # 页面级组件
│   │   │   ├── account/     # 账户管理页面
│   │   │   ├── ai-tools/    # AI工具页面
│   │   │   ├── auth/        # 认证相关页面
│   │   │   ├── clients/     # 客户管理页面
│   │   │   ├── dashboard/   # 仪表盘页面
│   │   │   ├── error/       # 错误页面
│   │   │   ├── planning/    # 申请规划页面
│   │   │   ├── school/      # 选校助手页面
│   │   │   └── writing/     # 文书写作页面
│   │   ├── App.vue          # Vue 应用根组件
│   │   ├── main.js          # 应用入口文件
│   │   └── style.css        # 全局样式文件
│   ├── auto-imports.d.ts    # 自动导入声明文件
│   ├── components.d.ts      # 组件类型声明文件
│   ├── index.html           # HTML 入口文件
│   ├── package.json         # Node.js 项目配置和依赖
│   ├── package-lock.json    # 锁定依赖版本
│   ├── postcss.config.cjs   # PostCSS 配置文件
│   ├── tailwind.config.js   # TailwindCSS 配置文件
│   └── vite.config.js       # Vite 配置文件
├── backend/                 # 后端代码 (Python FastAPI)
│   ├── app/                 # FastAPI 应用核心目录
│   │   ├── api/             # API 路由
│   │   │   ├── auth.py      # 认证相关 API 路由
│   │   │   ├── clients.py   # 客户相关 API 路由
│   │   │   ├── dashboard.py # 仪表盘相关 API 路由
│   │   │   └── __init__.py  # 初始化 API 路由
│   │   ├── core/            # 核心功能模块
│   │   │   ├── config.py    # 配置
│   │   │   ├── dependencies.py # 依赖注入
│   │   │   ├── security.py  # 安全相关功能
│   │   │   └── __init__.py  # 初始化核心模块
│   │   ├── db/              # 数据库相关
│   │   │   ├── database.py  # 数据库连接配置
│   │   │   └── __init__.py  # 初始化数据库模块
│   │   ├── models/          # SQLAlchemy 数据模型
│   │   │   ├── user.py      # 用户模型
│   │   │   └── __init__.py  # 初始化模型
│   │   ├── schemas/         # Pydantic 模型/schemas
│   │   │   ├── user.py      # 用户相关 schema
│   │   │   └── __init__.py  # 初始化 schemas
│   │   ├── utils/           # 后端工具函数
│   │   │   └── __init__.py  # 初始化工具模块
│   │   ├── main.py          # FastAPI 应用实例化
│   │   └── __init__.py      # 包初始化
│   ├── create_tables.sql    # 数据库表创建 SQL
│   ├── init_db.py           # 数据库初始化脚本
│   ├── main.py              # 应用启动入口脚本
│   └── requirements.txt     # Python 项目依赖
├── .cursor/                 # Cursor 编辑器配置
│   └── rules/               # Cursor 规则
├── .gitignore               # Git 忽略文件配置
├── .prettierrc              # Prettier 代码格式化配置
├── README.md                # 项目说明文档
├── start.sh                 # Linux/macOS 启动脚本 (启动前后端)
├── structure.md             # 项目结构详细说明
├── tsconfig.json            # TypeScript 配置文件
└── tsconfig.node.json       # Node.js TypeScript 配置
```

## 🔧 安装和运行

### 系统要求
- Node.js 16+
- Python 3.9+
- PostgreSQL 12+

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd TunshuEdu
```

2. **数据库设置**
```bash
# 创建数据库
createdb -U postgres tunshuedu_db

# 导入数据表结构
psql -U postgres -d tunshuedu_db -f backend/create_tables.sql
```

3. **后端启动**
```bash
cd backend
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

4. **前端启动**
```bash
cd frontend
npm install
npm run dev
```

5. **使用启动脚本（推荐）**
```bash
chmod +x start.sh
./start.sh
```

### 访问地址

**Docker部署**:
- 前端应用: http://localhost
- 后端API: http://localhost/api
- API文档: http://localhost/api/docs

**本地部署**:
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 默认账户
- 用户名: `admin`
- 密码: `admin123`

## 🔧 配置说明

### Docker 配置（推荐）

创建 `.env` 文件（基于 `docker-env-example.txt`）：
```bash
# 阿里云数据库配置
POSTGRES_HOST=your-alicloud-postgres-host.com
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=tunshuedu_db

# 应用密钥
SECRET_KEY=your-super-secret-key-change-this-in-production

# AI API密钥
SILICONE_FLOW_API_KEY=your-silicone-flow-api-key
ALIBABACLOUD_API_KEY_ai_writing=your-alibaba-ai-writing-key
# ... 其他配置
```

### 传统本地配置

#### 后端配置
编辑 `backend/app/core/config.py`:
```python
# 数据库配置
POSTGRES_USER = "postgres"
POSTGRES_PASSWORD = "your_password"
POSTGRES_HOST = "localhost"
POSTGRES_DB = "tunshuedu_db"

# JWT配置
SECRET_KEY = "your-secret-key"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
```

#### 前端配置
创建 `frontend/.env.development`:
```env
VITE_API_URL=http://localhost:8000
```

## 🔥 重要特性

### AI选校系统
- **智能匹配**: 基于学生背景和历史案例的智能院校推荐
- **多维评分**: 院校层级、专业匹配、经历契合度等多维度评分
- **案例对比**: 与历史申请案例进行对比分析
- **实时筛选**: 支持多条件组合筛选

### 客户管理
- **全生命周期管理**: 从初次咨询到申请完成的完整流程
- **结构化信息**: 教育背景、工作经历、语言成绩等结构化存储
- **自定义模块**: 支持自定义背景模块和想法模块
- **档案归档**: 服务完成后的档案归档管理

### 文书系统
- **AI辅助写作**: 基于用户背景的个性化文书生成
- **智能优化**: 文书结构和内容的智能优化建议
- **模板管理**: 多种文书模板和格式支持
- **版本控制**: 文书修改历史和版本管理

### 学校Logo智能显示系统
- **数据库优先**: 优先使用数据库中存储的学校官方Logo URL
- **智能缓存**: 自动缓存已获取的Logo信息，提升页面加载速度
- **多级降级**: 数据库->官方网站favicon->默认Logo的多级降级策略
- **批量预加载**: 页面加载时批量预加载当前显示学校的Logo
- **错误处理**: 优雅处理Logo加载失败的情况

## 📊 API文档

项目提供完整的API文档，启动后端服务后访问:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

主要API端点：
- `/api/auth/*` - 用户认证相关
- `/api/clients/*` - 客户管理相关  
- `/api/ai-selection/*` - AI选校相关
- `/api/ai-selection/data/abroad-schools` - 境外院校数据（支持Logo URL获取）
- `/api/dashboard` - 仪表板数据

## 🧪 开发指南

### 数据库迁移
```bash
cd backend
# 创建迁移
alembic revision --autogenerate -m "描述"
# 应用迁移
alembic upgrade head
```

### 代码规范
- 后端遵循 FastAPI 最佳实践
- 前端使用 Vue 3 组合式API
- 统一使用 TypeScript 类型检查
- 遵循 RESTful API 设计原则

### 测试
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm run test
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目维护: 囤鼠科技团队
- 邮箱: <EMAIL>
- 官网: https://tunshuedu.com

---

**TunshuEdu** - 让留学申请更智能，让教育服务更高效 🎓✨

## 最新更新

### 🐳 Docker 容器化部署方案 (2025-01-XX)

**重大更新**: 项目已完成完整的Docker化改造，支持一键部署和跨平台运行！

#### ✨ 新增特性
- **🚀 一键部署**: 使用 `./docker-start.sh dev` 一键启动开发环境
- **🔄 热重载支持**: 前后端代码修改自动重载，无需重启容器
- **☁️ 云数据库集成**: 原生支持阿里云PostgreSQL 17.4，无需本地数据库
- **📦 轻量化架构**: 移除Redis依赖，使用内存缓存，降低部署复杂度
- **🔧 健康监控**: 内置健康检查脚本，实时监控系统状态
- **🌍 跨平台兼容**: Mac开发，Linux生产部署无缝迁移

#### 🛠️ 技术改进
- **容器优化**: 多阶段构建，减少镜像大小，提升部署速度
- **网络配置**: Nginx统一代理，解决CORS问题，优化请求路由
- **环境隔离**: 开发/生产环境完全分离，配置管理更安全
- **依赖简化**: 移除Redis等外部依赖，降低运维成本

#### 📁 新增文件
```
TunshuEdu/
├── docker/                          # Docker配置目录
│   ├── frontend/Dockerfile.dev      # 前端开发环境
│   ├── frontend/Dockerfile.prod     # 前端生产环境  
│   ├── backend/Dockerfile.dev       # 后端开发环境
│   ├── backend/Dockerfile.prod      # 后端生产环境
│   └── nginx/                       # Nginx配置
├── docker-compose.dev.yml           # 开发环境编排
├── docker-compose.prod.yml          # 生产环境编排
├── docker-start.sh                  # 便捷启动脚本
├── health-check.sh                  # 健康检查脚本
├── docker-env-example.txt           # 环境变量模板
├── README-Docker.md                 # Docker部署详细指南
└── .dockerignore                    # Docker忽略文件
```

#### 🚀 快速体验
```bash
# 1. 配置环境变量
cp docker-env-example.txt .env
# 编辑 .env 填写阿里云数据库配置

# 2. 启动开发环境
./docker-start.sh dev

# 3. 访问应用
open http://localhost
```

📖 **完整指南**: [Docker 部署文档](README-Docker.md)

### 🔧 前端API配置优化 (2025-07-23)

#### 解决硬编码API URL问题

**问题背景**:
前端代码中存在硬编码的API URL，导致即使配置了Nginx反向代理，前端仍然会绕过代理直接访问本地8000端口。

**解决方案**:
1. **环境变量配置系统**:
   - 创建 `.env.development`、`.env.production`、`.env.example` 配置文件
   - 支持不同环境的API地址自动切换
   - 开发环境使用Vite代理，生产环境使用配置的域名

2. **移除硬编码URL**:
   - 修复 `vite.config.js` 中的硬编码代理配置
   - 修复 `auth.js` 中的后备URL配置
   - 使用环境变量替代所有硬编码的API地址

3. **智能配置验证**:
   - 创建 `verify-api-config.js` 验证脚本
   - 自动检测硬编码URL问题
   - 提供配置建议和最佳实践

**配置说明**:
```bash
# 开发环境 (.env.development)
VITE_API_URL=                    # 留空使用代理
VITE_BACKEND_URL=http://localhost:8000

# 生产环境 (.env.production)
VITE_API_URL=https://api.tunshuedu.com

# 验证配置
npm run verify-config
```

**技术优势**:
- ✅ 支持多环境自动切换
- ✅ 开发环境使用代理避免跨域
- ✅ 生产环境支持自定义域名
- ✅ 配置验证和错误检测
- ✅ 向后兼容现有部署

### 🔐 微信扫码登录+JWT认证系统完善 (2025-07-23)

#### 完整的JWT认证流程实现

**系统架构**:
- **双令牌机制**: Access Token (30分钟) + Refresh Token (7天)
- **自动刷新**: 前端检测401错误自动刷新令牌
- **请求队列**: 避免多个请求同时触发刷新
- **安全验证**: 独立的refresh token验证端点

**微信登录流程**:
1. 前端获取微信二维码配置 (`/api/auth/wechat/qr-config`)
2. 用户扫码授权，微信回调前端
3. 前端调用后端处理回调 (`/api/auth/wechat/callback`)
4. 后端验证授权码，获取用户信息，生成JWT令牌
5. 前端存储令牌，完成登录

**JWT刷新机制**:
1. 前端请求拦截器检测401错误
2. 自动调用刷新端点 (`/api/auth/refresh-token`)
3. 使用refresh_token获取新的access_token
4. 重试原始请求，用户无感知

**跨域配置**:
- 后端CORS支持前端域名 (localhost:3000, localhost:5173)
- 前端Vite代理配置支持开发环境
- 生产环境支持完整URL配置

**测试验证**:
```bash
# 运行JWT认证系统测试
python test_jwt_refresh.py
```

#### 技术实现亮点

1. **安全的刷新机制**: 新增 `/api/auth/refresh-token` 端点，独立验证refresh_token
2. **防重复刷新**: 使用isRefreshing标志和请求队列机制
3. **优雅降级**: 刷新失败时自动清理认证信息并跳转登录页
4. **完整错误处理**: 网络错误、令牌过期、用户不存在等情况的处理
5. **用户体验优化**: 令牌刷新过程对用户完全透明

#### API端点说明

**认证相关** (`/api/auth/`):
- `POST /login` - 用户名密码登录
- `POST /register` - 用户注册
- `POST /refresh` - 刷新令牌 (兼容旧版)
- `POST /refresh-token` - 新的刷新令牌端点
- `GET /me` - 获取当前用户信息
- `GET /wechat/qr-config` - 获取微信二维码配置
- `POST /wechat/callback` - 处理微信授权回调

## 历史更新 (2025-01-16)

### 🎯 客户档案智能填充功能

#### 选校匹配页面客户档案集成
实现了客户档案与学术背景信息的智能关联，大幅提升工作效率：

##### 核心功能
1. **客户搜索与选择**：
   - 支持多字段搜索（姓名、电话、邮箱、学生ID、护照等）
   - 实时搜索结果下拉框，使用 Teleport 确保不被遮挡
   - 客户头像和基本信息展示

2. **学术背景自动填充**：
   - 选择客户后自动获取其教育背景信息
   - 智能识别本科教育经历
   - 自动填充学校、专业、GPA等信息到表单
   - 智能解析GPA格式（百分制/4.0制/5.0制）

3. **定校书直接管理**：
   - 匹配结果可直接添加到客户的定校书中
   - 实时显示专业是否已在客户定校书中
   - 一键添加/移除功能

##### 技术实现亮点
- **数据关联**：前后端完整的客户教育信息API对接
- **智能解析**：自动识别GPA格式和制式
- **UI优化**：下拉框位置动态计算，响应式跟随
- **错误处理**：优雅的降级处理和用户提示

##### 工作流程优化
- **传统流程**：手动输入学生信息 → 获取匹配结果 → 手动记录推荐院校
- **新流程**：选择客户 → 自动填充信息 → 获取匹配结果 → 直接添加到定校书

### 🎨 智能推荐卡片UI规范应用

### 客户资料页面优化 (ClientProfile.vue)

#### 目标专业清单重构
将原有的2列网格布局改为横向一排一排的卡片形式，采用统一的智能推荐卡片设计规范：

##### 新卡片设计特点
1. **横向布局**：每个专业卡片占据一行，提供更好的信息展示空间
2. **统一设计语言**：应用与选校匹配页面相同的设计规范
3. **分层信息结构**：
   - 卡片头部：Logo、学校/专业名称、地区、排名徽章、操作按钮
   - 卡片主体：3列网格展示详细信息（专业名称、学校名称、地区、语言要求、申请截止、排名）
   - 卡片底部：官网链接和操作按钮区域

##### 设计系统应用
- **色彩系统**：使用品牌主色 #4F46E5，悬停色 #4338CA
- **排名徽章**：渐变背景、毛玻璃效果、悬停动画
- **交互动效**：卡片悬停上浮、按钮动画、过渡效果
- **间距规范**：统一的内外边距、网格间距

##### 用户体验改进
- **更好的可读性**：横向布局提供更多空间展示信息
- **一致的交互**：与选校匹配页面保持相同的操作习惯
- **视觉层次**：通过阴影、颜色、字重建立清晰的信息层次
- **响应式设计**：支持不同屏幕尺寸的自适应展示

#### 技术实现细节
- 使用 TailwindCSS 功能类实现样式
- 组件化的排名徽章设计
- 流畅的 CSS 过渡动画
- TypeScript 类型支持

## 性能优化 🚀

### 缓存系统
- **Redis缓存**: 支持分布式缓存，显著提升API响应速度
- **内存缓存降级**: 当Redis不可用时自动降级为内存缓存
- **智能缓存策略**: 
  - 客户详情数据缓存10分钟
  - 客户档案摘要缓存5分钟
  - 用户数据缓存5分钟
  - AI选校数据缓存30分钟

### 数据库优化
- **连接池优化**: 最大50个连接，100个溢出连接
- **查询优化**: 解决N+1查询问题，使用批量查询和预加载
- **懒加载**: 支持按需加载客户模块数据，减少不必要的数据传输

### API优化
- **响应压缩**: 自动GZIP压缩大于1KB的响应
- **性能监控**: 自动记录慢请求（>2秒）
- **并行查询**: 使用asyncio.gather并行执行数据库查询

### 使用优化功能

#### 1. 客户详情懒加载
```http
GET /api/clients/{client_id}/?include_modules=education,work,skills
```
只加载指定的模块，提升响应速度。

#### 2. 缓存管理
```http
# 查看缓存统计
GET /api/dashboard/cache-stats

# 清除缓存
POST /api/dashboard/clear-cache
```

#### 3. 性能监控
查看响应头中的 `X-Process-Time` 了解请求处理时间。

### 性能基准测试
- 客户详情查询: 从3秒优化至<500ms（使用缓存）
- 客户列表查询: 从1.5秒优化至<200ms
- 文书写作客户选择: 从5秒优化至<300ms

## 🏢 多租户身份管理使用指南

### 身份类型说明

1. **个人身份**
   - 📁 **独立数据空间**: 仅可访问个人创建的客户、案例等数据
   - 💰 **独立积分系统**: 个人积分与组织积分完全分离
   - 🎯 **个人工作流**: 适合个人咨询师或独立工作场景

2. **组织身份**
   - 🏢 **共享数据空间**: 可访问组织内所有成员创建的数据
   - 💳 **组织积分系统**: 使用组织统一的积分账户
   - 👥 **团队协作**: 支持多人协作，数据共享

### 使用流程

#### 1. 登录时选择身份
```
登录 → 显示身份选择器 → 选择个人/组织身份 → 进入相应工作空间
```

#### 2. 运行时切换身份
```
点击顶部身份切换器 → 选择目标身份 → 确认切换 → 数据实时切换
```

#### 3. 创建组织
```
个人身份下 → 组织管理 → 创建组织 → 邀请成员 → 团队协作
```

### 数据隔离保证

- ✅ **客户数据**: 个人身份下的客户不会在组织身份下显示
- ✅ **积分消耗**: 个人积分和组织积分独立计算和消耗
- ✅ **文档生成**: 根据当前身份使用相应的积分账户
- ✅ **权限验证**: 前后端双重验证，确保数据安全
- ✅ **实时同步**: 身份切换时界面和数据实时更新

### 注意事项

⚠️ **数据归属**: 在某个身份下创建的数据将永久属于该身份
⚠️ **积分消耗**: 请注意当前身份的积分余额，避免积分不足
⚠️ **权限管理**: 组织成员权限由组织创建者管理

### 技术实现说明

#### 后端数据隔离
- **数据过滤器**: `DataIsolationFilter` 确保API只返回当前身份下的数据
- **多租户中间件**: 自动在数据库查询中注入身份过滤条件
- **权限验证**: 多重权限检查确保数据安全

#### 前端身份管理
- **身份选择器**: `IdentitySelector.vue` 登录时选择身份
- **身份切换器**: `IdentitySwitcher.vue` 运行时切换身份
- **状态管理**: store 自动维护当前身份状态

---

## 🔄 最新更新

### 多租户系统完整实现 (2025-01-04)

**核心功能**: 完整的个人/组织身份数据隔离和权益管理

#### 主要更新
- **✅ 智能建档修复**: 修复组织身份下智能建档数据归属问题
- **✅ 积分系统隔离**: 个人积分与组织积分完全独立
- **✅ 身份切换优化**: 移除强制页面刷新，实现无缝切换
- **✅ 权益管理**: 权益管理页面正确显示当前身份的积分和套餐状态

#### 技术改进
- `CreditService`: 重构后的积分管理服务（集成多租户支持）
- `PackageService`: 重构后的套餐管理服务（集成多租户支持）
- `DataIsolationFilter`: 数据隔离过滤器优化
- 用户缓存身份信息修复：解决身份切换后积分显示错误问题
- 客户详情页面权限检查：优化404错误处理和身份切换响应

#### 常见问题解决

**问题**: 偶尔出现客户详情页面404错误
**原因**: 身份切换导致的数据访问权限变化
**解决方案**: 
- 自动检测权限问题并友好提示
- 身份切换时自动重新验证客户访问权限
- 权限不足时自动跳转到客户列表页

*多租户身份管理系统现已完整实现，支持个人和组织身份的完全数据隔离！* 🚀