# 🐳 囤鼠教育 Docker 部署指南

本文档详细介绍如何使用 Docker 部署囤鼠教育平台，支持开发和生产环境。

## 📋 系统要求

### 本地开发环境
- **操作系统**: macOS, Linux, Windows (WSL2)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 至少 4GB
- **存储**: 至少 10GB 可用空间

### 生产环境
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 至少 8GB
- **存储**: 至少 20GB 可用空间
- **数据库**: 阿里云 PostgreSQL 17.4

## 🏗️ 架构概述

### 开发环境架构
```
┌─────────────────────────────────────────────────────────────┐
│                     开发环境 (localhost)                      │
├─────────────────────────────────────────────────────────────┤
│  Nginx (端口80) ──┐                                         │
│                   ├── /api/* ──→ Backend (FastAPI:8000)     │
│                   └── /* ──────→ Frontend (Vite:3000)       │
├─────────────────────────────────────────────────────────────┤
│  🔄 热重载支持     📁 源码挂载     🛠️ 开发工具               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                      阿里云 PostgreSQL 17.4
```

### 生产环境架构
```
┌─────────────────────────────────────────────────────────────┐
│                   生产环境 (tunshuedu.com)                    │
├─────────────────────────────────────────────────────────────┤
│  Nginx (80/443) ──┐                                         │
│  + SSL证书        ├── /api/* ──→ Backend (FastAPI:8000)     │
│  + 静态文件缓存   └── /* ──────→ 静态文件 (/usr/share/...)   │
├─────────────────────────────────────────────────────────────┤
│  ⚡ 性能优化       🔒 SSL/TLS     📊 健康检查               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                      阿里云 PostgreSQL 17.4
```

## ⚡ 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd TunshuEdu
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（重要！）
vim .env
```

**必需配置项**：
```bash
# 阿里云数据库配置
POSTGRES_HOST=your-alicloud-postgres-host.com
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=tunshuedu_db

# 应用密钥
SECRET_KEY=your-super-secret-key-change-this-in-production

# AI API密钥
SILICONE_FLOW_API_KEY=your-silicone-flow-api-key
# ... 其他API密钥
```

### 3. 启动开发环境
```bash

docker-compose -f docker-compose.dev.yml up -d --build
```

### 4. 访问应用
- **前端应用**: http://localhost
- **后端API**: http://localhost/api
- **API文档**: http://localhost/api/docs

## 🛠️ 开发环境

### 特性
- ✅ **前端热重载**: Vite 开发服务器，代码变更自动刷新
- ✅ **后端热重载**: Uvicorn reload 模式，Python 代码变更自动重启
- ✅ **源码挂载**: 容器直接挂载源码目录，无需重建镜像
- ✅ **开发调试**: 详细日志输出，便于问题排查
- ✅ **代理配置**: Nginx 统一代理，避免 CORS 问题

### 开发工作流程
1. **启动环境**: `./docker-start.sh dev`
2. **代码开发**: 在本地编辑器中修改代码
3. **自动重载**: 保存后自动应用更改
4. **调试测试**: 通过浏览器或 API 客户端测试
5. **查看日志**: `./docker-start.sh logs`

### 常用命令
```bash
# 查看运行状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 重启单个服务
docker-compose -f docker-compose.dev.yml restart backend

# 进入容器调试
docker exec -it tunshuedu-backend-dev bash
```

## 🚀 生产环境

### 特性
- ✅ **性能优化**: 多进程 Uvicorn，静态文件缓存
- ✅ **安全加固**: 非 root 用户运行，安全头配置
- ✅ **SSL支持**: HTTPS 加密传输（需配置证书）
- ✅ **健康检查**: 自动故障恢复
- ✅ **跨平台**: Mac 开发，Linux 部署无缝迁移

### 部署到生产环境

#### 1. 准备服务器
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo apt install docker-compose-plugin -y
```

#### 2. 配置 SSL 证书（可选）
```bash
# 创建 SSL 目录
mkdir -p ssl

# 复制证书文件
cp your-certificate.crt ssl/certificate.crt
cp your-private.key ssl/private.key

# 确保权限正确
chmod 600 ssl/private.key
chmod 644 ssl/certificate.crt
```

#### 3. 配置生产环境变量
```bash
# 编辑 .env 文件
vim .env

# 确保生产环境配置
ENVIRONMENT=production
FRONTEND_URL=https://tunshuedu.com
WECHAT_REDIRECT_URI=https://tunshuedu.com/auth/wechat/callback
```

#### 4. 启动生产环境
```bash

docker-compose -f docker-compose.prod.yml up -d --build
```

#### 5. 验证部署
```bash
# 检查服务状态
docker-compose -f docker-compose.prod.yml ps

# 检查健康状态
curl http://localhost/health

# 查看日志
./docker-start.sh logs
```

## 🔧 配置说明

### 数据库配置

项目已配置为使用阿里云 PostgreSQL 17.4，无需本地数据库：

```bash
# .env 文件中的数据库配置
POSTGRES_HOST=your-alicloud-postgres-host.com
POSTGRES_PORT=5432
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=tunshuedu_db
```

### 缓存配置

为简化 Docker 部署，项目使用内存缓存替代 Redis：
- ✅ **轻量级**: 无需额外的 Redis 容器
- ✅ **开发友好**: 启动更快，资源占用更少
- ✅ **容器重启自动清理**: 避免缓存污染

### 网络配置

Docker 网络自动处理服务发现：
```yaml
networks:
  tunshuedu-network:
    driver: bridge
```

容器间通过服务名通信：
- `frontend:3000` - 前端服务
- `backend:8000` - 后端服务

## 📊 监控与日志

### 健康检查
生产环境包含自动健康检查：
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
```

### 日志管理
```bash
# 查看所有服务日志
./docker-start.sh logs

# 查看特定服务日志
docker-compose -f docker-compose.dev.yml logs frontend
docker-compose -f docker-compose.dev.yml logs backend
docker-compose -f docker-compose.dev.yml logs nginx

# 实时跟踪日志
docker-compose -f docker-compose.dev.yml logs -f backend
```

### 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看容器状态
docker-compose -f docker-compose.dev.yml ps
```

## 🔍 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
lsof -i :80
lsof -i :3000
lsof -i :8000

# 修改端口映射（如需要）
# 编辑 docker-compose.*.yml 文件中的 ports 配置
```

#### 2. 权限问题
```bash
# 确保脚本有执行权限
chmod +x docker-start.sh

# 确保 Docker 权限正确
sudo usermod -aG docker $USER
newgrp docker
```

#### 3. 网络问题
```bash
# 重建网络
docker network prune
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml up -d
```

#### 4. 数据库连接问题
```bash
# 检查数据库配置
grep POSTGRES .env

# 测试数据库连接
docker exec -it tunshuedu-backend-dev python -c "
from app.core.config import settings
print(f'数据库地址: {settings.POSTGRES_HOST}')
print(f'数据库: {settings.POSTGRES_DB}')
"
```

#### 5. 缓存问题
```bash
# 清理 Docker 缓存
./docker-start.sh cleanup

# 强制重建
docker-compose -f docker-compose.dev.yml up -d --build --force-recreate
```

### 日志分析
```bash
# 检查容器启动错误
docker-compose -f docker-compose.dev.yml logs backend | grep -i error

# 检查网络连接
docker exec -it tunshuedu-nginx-dev nginx -t

# 检查应用健康状态
curl -v http://localhost/health
curl -v http://localhost/api/test
```

## 🚀 性能优化

### 开发环境优化
- 使用 Docker 的构建缓存
- 合理配置 volume 挂载
- 限制日志输出大小

### 生产环境优化
- 多阶段构建减少镜像大小
- 使用非 root 用户运行
- 启用 Nginx gzip 压缩
- 配置静态文件缓存

## 📝 维护指南

### 定期维护
```bash
# 清理未使用的 Docker 资源
./docker-start.sh cleanup

# 更新镜像
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# 备份数据库（阿里云控制台操作）
# 备份配置文件
tar -czf config-backup.tar.gz .env docker/
```

### 版本升级
```bash
# 停止服务
./docker-start.sh stop

# 拉取新代码
git pull origin main

# 重新构建和启动
./docker-start.sh prod
```

## 🔐 安全建议

1. **环境变量安全**: 
   - 不要将 `.env` 文件提交到版本控制
   - 定期更换 API 密钥和数据库密码

2. **SSL证书**: 
   - 使用 Let's Encrypt 或购买 SSL 证书
   - 定期更新证书

3. **防火墙配置**:
   ```bash
   # 只开放必要端口
   sudo ufw allow 80/tcp
   sudo ufw allow 443/tcp
   sudo ufw enable
   ```

4. **定期更新**:
   - 定期更新 Docker 镜像
   - 关注安全补丁更新

## 📞 技术支持

遇到问题？查看以下资源：

1. **项目文档**: 查看主 README.md
2. **日志分析**: 使用 `./docker-start.sh logs` 查看详细日志
3. **社区支持**: 提交 GitHub Issue
4. **在线文档**: 访问 API 文档 http://localhost/api/docs

---

**囤鼠教育 Docker 部署方案** - 让部署更简单，让开发更高效 🚀 