# 前端生产环境 Dockerfile - 多阶段构建
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源（可选，加速国内下载）
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY frontend/package*.json ./

# 安装依赖（使用npm install来处理新依赖）
RUN npm install

# 复制源代码
COPY frontend/ ./

# 设置构建时的环境变量
ARG VITE_API_URL
ENV VITE_API_URL=$VITE_API_URL

# 构建生产版本
RUN npm run build

# 生产阶段 - 使用nginx服务静态文件
FROM nginx:alpine

# 复制构建产物到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# # 复制nginx配置
# COPY docker/nginx/nginx.prod.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"] 