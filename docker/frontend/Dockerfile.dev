# 前端开发环境 Dockerfile
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 设置npm镜像源（可选，加速国内下载）
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY frontend/package*.json ./

# 安装依赖
RUN npm install

# 复制源代码（开发模式下会被volume挂载覆盖）
COPY frontend/ ./

# 暴露端口
EXPOSE 3000

# 开发模式启动命令，支持热重载，并解决crypto.hash问题
CMD ["node", "--openssl-legacy-provider", "./node_modules/vite/bin/vite.js", "--host", "0.0.0.0"] 