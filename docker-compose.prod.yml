version: '3.8'

services:
  # 后端服务 - FastAPI 生产环境
  backend:
    build:
      context: .
      dockerfile: docker/backend/Dockerfile.prod
      # 确保Linux架构兼容性（Mac -> Linux部署）
      platforms:
        - linux/amd64
    container_name: tunshuedu-backend-prod
    restart: unless-stopped
    environment:
      # 数据库配置 - 阿里云PostgreSQL
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      
      # 应用配置
      - ENVIRONMENT=production
      - SECRET_KEY=${SECRET_KEY}
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - REFRESH_TOKEN_EXPIRE_DAYS=7
      
      # AI API配置
      - SILICONE_FLOW_API_KEY=${SILICONE_FLOW_API_KEY}
      - ALIBABACLOUD_API_KEY_ai_selection=${ALIBABACLOUD_API_KEY_ai_selection}
      - ALIBABACLOUD_API_KEY_bg_extraction=${ALIBABACLOUD_API_KEY_bg_extraction}
      - ALIBABACLOUD_API_KEY_ai_augmentation=${ALIBABACLOUD_API_KEY_ai_augmentation}
      - ALIBABACLOUD_API_KEY_ai_writing=${ALIBABACLOUD_API_KEY_ai_writing}
      - DOUBAO_API_KEY=${DOUBAO_API_KEY}
      - ZEROGPT_API_KEY=${ZEROGPT_API_KEY}
      
      # 微信登录配置
      - WECHAT_APP_ID=${WECHAT_APP_ID}
      - WECHAT_APP_SECRET=${WECHAT_APP_SECRET}
      - WECHAT_REDIRECT_URI=${WECHAT_REDIRECT_URI}
      
      # 前端URL
      - FRONTEND_URL=https://tunshuedu.com
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379/0
    networks:
      - tunshuedu-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      # 持久化并对外挂载组织Logo静态资源目录（宿主机 -> 容器）
      # 注意：后端代码将文件写入 /app/static/uploads/org_logos
      - ./backend/static/uploads/org_logos:/app/static/uploads/org_logos

  # 前端服务 - Vue 3 构建 + Nginx
  frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile.prod
      # 确保Linux架构兼容性（Mac -> Linux部署）
      platforms:
        - linux/amd64
      args:
        - VITE_API_URL=https://tunshuedu.com
    container_name: tunshuedu-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # SSL证书挂载
      - ./backend/keys/fullchain.pem:/etc/nginx/ssl/fullchain.pem:ro
      - ./backend/keys/privkey.key:/etc/nginx/ssl/privkey.key:ro
      - ./docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      # 门户网站静态文件挂载
      - /www/wwwroot/tunshuedu.com:/usr/share/nginx/portal:ro
    restart: unless-stopped
    networks:
      - tunshuedu-network
    depends_on:
      - backend
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存服务 - 生产环境
  redis:
    image: redis:7-alpine
    container_name: tunshuedu-redis-prod
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - tunshuedu-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3

# 网络配置
networks:
  tunshuedu-network:
    driver: bridge
    name: tunshuedu-prod-network

# 数据卷
volumes:
  nginx_cache:
    name: tunshuedu-nginx-cache-prod
  redis_data:
    name: tunshuedu-redis-data-prod 