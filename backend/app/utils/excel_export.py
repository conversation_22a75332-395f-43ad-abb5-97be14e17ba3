"""
定校书Excel导出工具类
实现专业的定校书Excel生成功能，包含数据转换、样式设置和文件生成
"""

import io
import logging
import re
from typing import List, Dict, Any, Optional
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo
from datetime import datetime

from app.schemas.client import SchoolBookExcelItem

logger = logging.getLogger(__name__)

class SchoolBookExcelExporter:
    """定校书Excel导出器"""
    
    def __init__(self):
        # 定义Excel样式
        self.header_font = Font(
            name='微软雅黑',
            size=12,
            bold=True,
            color='FFFFFF'
        )
        
        self.content_font = Font(
            name='微软雅黑',
            size=10,
            color='333333'
        )
        
        self.header_fill = PatternFill(
            start_color='4F46E5',
            end_color='4F46E5',
            fill_type='solid'
        )
        
        self.content_fill_1 = PatternFill(
            start_color='F8FAFC',
            end_color='F8FAFC',
            fill_type='solid'
        )
        
        self.content_fill_2 = PatternFill(
            start_color='FFFFFF',
            end_color='FFFFFF',
            fill_type='solid'
        )
        
        self.border = Border(
            left=Side(border_style='thin', color='E2E8F0'),
            right=Side(border_style='thin', color='E2E8F0'),
            top=Side(border_style='thin', color='E2E8F0'),
            bottom=Side(border_style='thin', color='E2E8F0')
        )
        
        self.center_alignment = Alignment(
            horizontal='center',
            vertical='center',
            wrap_text=True
        )
        
        self.left_alignment = Alignment(
            horizontal='left',
            vertical='center',
            wrap_text=True
        )

    def export_school_book(
        self,
        client_name: str,
        programs_data: List[Dict[str, Any]],
        output_format: str = "xlsx"
    ) -> io.BytesIO:
        """
        导出定校书Excel文件
        
        Args:
            client_name: 客户姓名
            programs_data: 专业项目数据列表
            output_format: 输出格式（默认为xlsx）
            
        Returns:
            io.BytesIO: Excel文件的字节流
        """
        logger.info(f"开始为客户 {client_name} 导出定校书，共 {len(programs_data)} 个专业")
        
        # 创建工作簿
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = f"{client_name} - 定校书"
        
        # 转换数据为Excel导出格式
        excel_data = self._convert_to_excel_format(programs_data)
        
        # 按地区分组数据
        grouped_data = self._group_by_region(excel_data)
        
        # 生成Excel内容
        self._generate_excel_content(worksheet, client_name, grouped_data)
        
        # 设置样式
        self._apply_styles(worksheet, len(excel_data))
        
        # 调整列宽
        self._adjust_column_widths(worksheet)
        
        # 保存到字节流
        excel_buffer = io.BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        
        logger.info(f"成功为客户 {client_name} 生成定校书Excel文件")
        return excel_buffer

    def _convert_to_excel_format(self, programs_data: List[Dict[str, Any]]) -> List[SchoolBookExcelItem]:
        """
        将程序数据转换为Excel导出格式
        
        Args:
            programs_data: 原始专业数据
            
        Returns:
            List[SchoolBookExcelItem]: 转换后的Excel数据
        """
        excel_items = []
        
        for program in programs_data:
            # 转换数据，确保字段对应
            excel_item = SchoolBookExcelItem(
                school_qs_rank=str(program.get('school_qs_rank', 'N/A')) if program.get('school_qs_rank') else 'N/A',
                school_name_cn=program.get('school_name_cn', '未知学校'),
                school_region=program.get('school_region', '未知地区'),
                program_tuition=program.get('program_tuition', '待查询'),
                program_duration=program.get('program_duration', '待查询'),
                program_name_cn=program.get('program_name_cn', '未知专业'),
                program_name_en=program.get('program_name_en', ''),
                language_requirements=self._extract_language_requirements(program),
                program_website=program.get('program_website', ''),
                application_time=self._extract_application_time(program)
            )
            excel_items.append(excel_item)
        
        return excel_items

    def _extract_language_requirements(self, program: Dict[str, Any]) -> str:
        """
        提取语言要求信息
        
        Args:
            program: 专业数据
            
        Returns:
            str: 语言要求字符串
        """
        # 尝试从不同字段提取语言要求
        language_req = (
            program.get('language_requirements') or
            program.get('admission_requirements') or
            program.get('requirements') or
            '待查询'
        )
        
        # 使用新的格式化方法
        return self._format_language_requirements(str(language_req))

    def _extract_application_time(self, program: Dict[str, Any]) -> str:
        """
        提取申请时间信息
        
        Args:
            program: 专业数据
            
        Returns:
            str: 申请时间字符串
        """
        app_time = (
            program.get('application_time') or
            program.get('application_deadline') or
            program.get('deadline') or
            '待查询'
        )
        
        # 使用新的格式化方法
        return self._format_application_time(str(app_time))

    def _group_by_region(self, excel_data: List[SchoolBookExcelItem]) -> Dict[str, List[SchoolBookExcelItem]]:
        """
        按地区分组数据
        
        Args:
            excel_data: Excel数据列表
            
        Returns:
            Dict[str, List[SchoolBookExcelItem]]: 按地区分组的数据
        """
        grouped = {}
        
        for item in excel_data:
            region = item.school_region or '其他'
            if region not in grouped:
                grouped[region] = []
            grouped[region].append(item)
        
        # 对每个地区内的数据按QS排名排序
        for region in grouped:
            grouped[region].sort(key=lambda x: self._get_qs_rank_sort_key(x.school_qs_rank))
        
        return grouped

    def _get_qs_rank_sort_key(self, qs_rank: Optional[str]) -> int:
        """
        获取QS排名的排序键值
        
        Args:
            qs_rank: QS排名字符串
            
        Returns:
            int: 用于排序的数值
        """
        if not qs_rank or qs_rank == 'N/A':
            return 999999
        
        try:
            # 尝试提取数字
            numbers = re.findall(r'\d+', str(qs_rank))
            if numbers:
                return int(numbers[0])
        except (ValueError, TypeError):
            pass
        
        return 999999

    def _clean_text(self, text: str) -> str:
        """
        清理文本，去除HTML标签、特殊符号和多余空格
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text or not isinstance(text, str):
            return text or ''
        
        # 去除HTML标签
        text = re.sub(r'<br\s*/?>', '\n', text)  # 将<br/>转换为换行符
        text = re.sub(r'<[^>]+>', '', text)  # 去除其他HTML标签
        
        # 去除特殊符号
        text = text.replace('⚠', '')
        text = text.replace('️', '')
        
        # 清理多余的空格和换行符
        text = re.sub(r'\s+', ' ', text)  # 多个空格合并为一个
        text = text.strip()
        
        return text

    def _format_language_requirements(self, text: str) -> str:
        """
        格式化语言要求文本
        
        Args:
            text: 原始语言要求文本
            
        Returns:
            str: 格式化后的语言要求
        """
        if not text or text == '待查询':
            return '待查询'
        
        # 先清理基本文本
        text = self._clean_text(text)
        
        # 检查是否所有考试都是"无要求"或"Optional"
        if self._is_no_requirement(text):
            return '无语言要求'
        
        if self._is_optional_requirement(text):
            return '语言要求可选'
        
        # 处理各种语言考试信息
        formatted_parts = []
        
        # 提取雅思信息（包括小分要求）
        ielts_info = self._extract_ielts_info(text)
        if ielts_info:
            formatted_parts.append(ielts_info)
        
        # 提取托福信息（包括小分要求）
        toefl_info = self._extract_toefl_info(text)
        if toefl_info:
            formatted_parts.append(toefl_info)
        
        # 提取PTE信息
        pte_info = self._extract_pte_info(text)
        if pte_info:
            formatted_parts.append(pte_info)
        
        # 提取多邻国信息
        duolingo_info = self._extract_duolingo_info(text)
        if duolingo_info:
            formatted_parts.append(duolingo_info)
        
        # 查找其他考试要求
        other_tests = self._extract_other_tests(text)
        if other_tests:
            formatted_parts.extend(other_tests)
        
        # 如果找到了格式化的内容，返回
        if formatted_parts:
            return ' | '.join(formatted_parts)
        
        # 如果没有找到格式化的内容，返回清理后的原文本
        cleaned_text = self._clean_language_text(text)
        return cleaned_text if cleaned_text else '待查询'

    def _extract_duolingo_info(self, text: str) -> str:
        """
        提取多邻国信息
        """
        # 查找多邻国总分
        total_score = None
        
        # 提取总分
        duolingo_patterns = [
            r'Duolingo.*?(\d+)',
            r'多邻国.*?(\d+)',
            r'DET.*?(\d+)'
        ]
        
        for pattern in duolingo_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                total_score = match.group(1)
                break
        
        if total_score:
            return f"多邻国: {total_score}"
        
        return None

    def _is_no_requirement(self, text: str) -> bool:
        """
        检查是否为无语言要求
        """
        # 检查是否包含多个"无要求"
        no_requirement_count = len(re.findall(r'总分要求:\s*无要求', text))
        
        # 如果有2个或以上的"无要求"，认为是无语言要求
        if no_requirement_count >= 2:
            return True
            
        # 检查是否包含关键的无要求短语（增加英文支持）
        no_requirement_phrases = [
            '无任何语言要求',
            '不需要语言成绩',
            '无语言要求',
            'No language requirement',
            'No English language requirements',
            'Language requirements waived',
            'No specific language requirements',
            'English proficiency not required'
        ]
        
        for phrase in no_requirement_phrases:
            if phrase.lower() in text.lower():
                return True
                
        return False

    def _is_optional_requirement(self, text: str) -> bool:
        """
        检查是否为可选语言要求
        """
        # 检查是否包含多个"Optional"
        optional_count = len(re.findall(r'总分要求:\s*Optional', text, re.IGNORECASE))
        
        # 如果有2个或以上的"Optional"，认为是可选要求
        if optional_count >= 2:
            return True
            
        # 检查是否包含关键的可选短语（增加英文支持）
        optional_phrases = [
            '语言成绩可选',
            '可选提交',
            '语言要求可选',
            'Optional language',
            'Language optional',
            'English proficiency test scores optional',
            'Language test optional',
            'Recommended but not required'
        ]
        
        for phrase in optional_phrases:
            if phrase.lower() in text.lower():
                return True
                
        return False

    def _extract_ielts_info(self, text: str) -> str:
        """
        提取雅思信息，包括总分和小分要求
        """
        # 查找雅思总分
        total_score = None
        sub_scores = None
        
        # 提取总分 - 支持更多英文格式
        total_patterns = [
            r'雅思.*?总分要求:\s*(\d+(?:\.\d+)?)',  # 中文格式
            r'IELTS.*?总分要求:\s*(\d+(?:\.\d+)?)',
            r'IELTS\s+(\d+(?:\.\d+)?)',  # IELTS 7.0
            r'IELTS:\s*(\d+(?:\.\d+)?)',  # IELTS: 7.0
            r'IELTS\s+(\d+(?:\.\d+)?)\s+overall',  # IELTS 7.0 overall
            r'IELTS\s+overall\s+(\d+(?:\.\d+)?)',  # IELTS overall 7.0
            r'雅思\s*(\d+(?:\.\d+)?)',  # 雅思7.0
        ]
        
        for pattern in total_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                total_score = match.group(1)
                break
        
        if not total_score:
            return None
        
        # 提取小分要求 - 支持更多英文格式
        sub_score_patterns = [
            r'雅思.*?小分要求:\s*([^|<]+)',  # 中文格式
            r'IELTS.*?小分要求:\s*([^|<]+)',
            r'小分要求:\s*([^|<]+)',  # 通用匹配
            r'minimum\s+(\d+(?:\.\d+)?)\s+in\s+each',  # minimum 6.0 in each
            r'(\d+(?:\.\d+)?)\s+in\s+each\s+component',  # 6.0 in each component
            r'(\d+(?:\.\d+)?)\s+each',  # 6.5 each
            r'\((\d+(?:\.\d+)?)\s+each\)',  # (6.5 each)
        ]
        
        for pattern in sub_score_patterns:
            sub_match = re.search(pattern, text, re.IGNORECASE)
            if sub_match:
                sub_text = sub_match.group(1).strip()
                # 检查是否有有效的小分要求
                if not re.search(r'无要求|/\s*$', sub_text) and sub_text:
                    # 如果匹配到的是单个数字（英文格式）
                    if re.match(r'^\d+(?:\.\d+)?$', sub_text):
                        sub_scores = f"各项{sub_text}"
                    else:
                        # 简化小分要求显示（中文格式）
                        if '听力:' in sub_text and '阅读:' in sub_text:
                            # 提取各项分数
                            scores = []
                            for skill in ['听力', '阅读', '写作', '口语']:
                                skill_match = re.search(f'{skill}:\s*(\d+(?:\.\d+)?)', sub_text)
                                if skill_match:
                                    scores.append(skill_match.group(1))
                            
                            # 如果四项分数相同，简化显示
                            if len(set(scores)) == 1 and len(scores) == 4:
                                sub_scores = f"各项{scores[0]}"
                            elif scores:
                                unique_scores = list(set(scores))
                                if len(unique_scores) == 1:
                                    sub_scores = f"各项{unique_scores[0]}"
                                else:
                                    sub_scores = f"小分{min(scores)}-{max(scores)}"
                break
        
        # 组合结果
        if sub_scores:
            return f"雅思: {total_score}({sub_scores})"
        else:
            return f"雅思: {total_score}"

    def _extract_toefl_info(self, text: str) -> str:
        """
        提取托福信息，包括总分和小分要求
        """
        # 查找托福总分
        total_score = None
        sub_scores = None
        
        # 提取总分 - 支持更多英文格式
        total_patterns = [
            r'托福.*?总分要求:\s*(\d+)',  # 中文格式
            r'TOEFL.*?总分要求:\s*(\d+)',
            r'TOEFL\s+(\d+)',  # TOEFL 100
            r'TOEFL:\s*(\d+)',  # TOEFL: 100
            r'TOEFL\s+iBT\s*:?\s*(\d+)',  # TOEFL iBT: 100
            r'托福\s*(\d+)',  # 托福100
        ]
        
        for pattern in total_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                total_score = match.group(1)
                break
        
        if not total_score:
            return None
        
        # 提取小分要求 - 支持更多英文格式
        sub_score_patterns = [
            r'托福.*?小分要求:\s*([^|<]+)',  # 中文格式
            r'TOEFL.*?小分要求:\s*([^|<]+)',
            r'小分要求:\s*([^|<]+)',  # 通用匹配
            r'minimum\s+(\d+)\s+in\s+each\s+section',  # minimum 20 in each section
            r'(\d+)\s+in\s+each\s+section',  # 20 in each section
            r'\(minimum\s+(\d+)\s+in\s+each\s+section\)',  # (minimum 20 in each section)
        ]
        
        for pattern in sub_score_patterns:
            sub_match = re.search(pattern, text, re.IGNORECASE)
            if sub_match:
                sub_text = sub_match.group(1).strip()
                # 检查是否有有效的小分要求
                if not re.search(r'无要求|/\s*$', sub_text) and sub_text:
                    # 如果匹配到的是单个数字（英文格式）
                    if re.match(r'^\d+$', sub_text):
                        sub_scores = f"各项{sub_text}"
                    else:
                        # 处理不同格式的小分要求
                        if 'L' in sub_text and 'W' in sub_text and 'R' in sub_text and 'S' in sub_text:
                            # L23,W23,R23,S23 格式
                            scores = re.findall(r'[LWRS](\d+)', sub_text)
                            if len(set(scores)) == 1 and len(scores) == 4:
                                sub_scores = f"各项{scores[0]}"
                            elif scores:
                                unique_scores = list(set(scores))
                                if len(unique_scores) == 1:
                                    sub_scores = f"各项{unique_scores[0]}"
                                else:
                                    sub_scores = f"小分{min(scores)}-{max(scores)}"
                        elif '听力:' in sub_text:
                            # 听力: 22; 阅读: 22; 格式
                            scores = []
                            for skill in ['听力', '阅读', '写作', '口语']:
                                skill_match = re.search(f'{skill}:\s*(\d+)', sub_text)
                                if skill_match:
                                    scores.append(skill_match.group(1))
                            
                            if len(set(scores)) == 1 and len(scores) == 4:
                                sub_scores = f"各项{scores[0]}"
                            elif scores:
                                unique_scores = list(set(scores))
                                if len(unique_scores) == 1:
                                    sub_scores = f"各项{unique_scores[0]}"
                                else:
                                    sub_scores = f"小分{min(scores)}-{max(scores)}"
                break
        
        # 组合结果
        if sub_scores:
            return f"托福: {total_score}({sub_scores})"
        else:
            return f"托福: {total_score}"

    def _extract_pte_info(self, text: str) -> str:
        """
        提取PTE信息，包括总分和小分要求
        """
        # 查找PTE总分
        total_score = None
        sub_scores = None
        
        # 提取总分 - 修复正则表达式
        total_patterns = [
            r'PTE.*?总分要求:\s*(\d+)',  # 跨越|符号
            r'PTE\s+(\d+)'
        ]
        
        for pattern in total_patterns:
            match = re.search(pattern, text)
            if match:
                total_score = match.group(1)
                break
        
        if not total_score:
            return None
        
        # 提取小分要求 - 改进正则表达式
        sub_score_patterns = [
            r'PTE.*?小分要求:\s*([^|<]+)',  # 跨越|符号
            r'小分要求:\s*([^|<]+)'  # 通用匹配
        ]
        
        for pattern in sub_score_patterns:
            sub_match = re.search(pattern, text)
            if sub_match:
                sub_text = sub_match.group(1).strip()
                # 检查是否有有效的小分要求
                if not re.search(r'无要求|/\s*$', sub_text) and sub_text:
                    # 提取各项分数
                    scores = []
                    for skill in ['听力', '阅读', '写作', '口语']:
                        skill_match = re.search(f'{skill}:\s*(\d+)', sub_text)
                        if skill_match:
                            scores.append(skill_match.group(1))
                    
                    # 如果四项分数相同，简化显示
                    if len(set(scores)) == 1 and len(scores) == 4:
                        sub_scores = f"各项{scores[0]}"
                    elif scores:
                        unique_scores = list(set(scores))
                        if len(unique_scores) == 1:
                            sub_scores = f"各项{unique_scores[0]}"
                        else:
                            sub_scores = f"小分{min(scores)}-{max(scores)}"
                break
        
        # 组合结果
        if sub_scores:
            return f"PTE: {total_score}({sub_scores})"
        else:
            return f"PTE: {total_score}"

    def _extract_other_tests(self, text: str) -> list:
        """
        提取其他考试要求（GMAT、GRE等）
        """
        other_tests = []
        
        # GMAT分数
        gmat_patterns = [
            r'GMAT[^|]*总分要求:\s*(\d+)',
            r'GMAT[^\d]*(\d+)'
        ]
        
        for pattern in gmat_patterns:
            match = re.search(pattern, text)
            if match:
                score = match.group(1)
                other_tests.append(f"GMAT: {score}")
                break
        
        # GRE分数
        gre_patterns = [
            r'GRE[^|]*总分要求:\s*(\d+)',
            r'GRE[^\d]*(\d+)'
        ]
        
        for pattern in gre_patterns:
            match = re.search(pattern, text)
            if match:
                score = match.group(1)
                other_tests.append(f"GRE: {score}")
                break
        
        return other_tests

    def _clean_language_text(self, text: str) -> str:
        """
        清理语言要求文本的最后处理
        """
        # 去除无效的小分要求
        cleaned_text = re.sub(r'小分要求:[^;|]*', '', text)
        cleaned_text = re.sub(r'听力:\s*/[;|\s]*', '', cleaned_text)
        cleaned_text = re.sub(r'阅读:\s*/[;|\s]*', '', cleaned_text) 
        cleaned_text = re.sub(r'写作:\s*/[;|\s]*', '', cleaned_text)
        cleaned_text = re.sub(r'口语:\s*/[;|\s]*', '', cleaned_text)
        
        # 清理无要求和Optional的重复信息
        cleaned_text = re.sub(r'总分要求:\s*无要求[^|]*', '无要求', cleaned_text)
        cleaned_text = re.sub(r'总分要求:\s*Optional[^|]*', 'Optional', cleaned_text, flags=re.IGNORECASE)
        
        # 清理分隔符
        cleaned_text = re.sub(r'\s*[;|]\s*', ' | ', cleaned_text)
        cleaned_text = cleaned_text.strip(' ;|')
        
        # 限制长度
        if len(cleaned_text) > 50:
            cleaned_text = cleaned_text[:47] + '...'
        
        return cleaned_text

    def _format_application_time(self, text: str) -> str:
        """
        格式化申请时间文本 - 增强对英文格式的支持
        
        Args:
            text: 原始申请时间文本
            
        Returns:
            str: 格式化后的申请时间
        """
        if not text or text == '待查询':
            return '待查询'
        
        # 先清理基本文本
        text = self._clean_text(text)
        
        # 处理英文格式的申请时间
        english_patterns = [
            r'Application\s+opens:\s*([^;]+);\s*Deadline:\s*([^;]+)',  # Application opens: date; Deadline: date
            r'Deadline:\s*([^;,]+)',  # Deadline: date
            r'Application\s+deadline:\s*([^;,]+)',  # Application deadline: date
            r'Early\s+decision\s+deadline:\s*([^;,]+)',  # Early decision deadline: date
            r'Regular\s+decision:\s*([^;,]+)',  # Regular decision: date
        ]
        
        for pattern in english_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if len(match.groups()) == 2:
                    # 有开放和截止时间
                    return f"开放: {match.group(1).strip()}\n截止: {match.group(2).strip()}"
                else:
                    # 只有截止时间
                    return f"截止: {match.group(1).strip()}"
        
        # 处理Round格式（英文）
        round_matches = re.findall(r'Round\s*(\d+):\s*([^;,]+)', text, re.IGNORECASE)
        if round_matches:
            formatted_rounds = []
            for round_num, date in round_matches:
                formatted_rounds.append(f"Round{round_num}: {date.strip()}")
            return '\n'.join(formatted_rounds)
        
        # 处理中文格式（原有逻辑）
        formatted_parts = []
        
        # 匹配年份入学信息 - 改进正则表达式
        year_sections = re.split(r'(\d{2}年[^:]*入学:)', text)
        
        # 重新组织数据：year_sections会是[前缀, 年份标题1, 内容1, 年份标题2, 内容2, ...]
        for i in range(1, len(year_sections), 2):
            if i + 1 < len(year_sections):
                year_title = year_sections[i].strip()
                year_content = year_sections[i + 1].strip()
                
                # 提取年份
                year_match = re.search(r'(\d{2})年', year_title)
                if year_match:
                    year = year_match.group(1)
                    year_info = f"20{year}年入学:"
                    
                    # 处理该年份的Round信息
                    # 分割内容，查找Round截止信息
                    parts = re.split(r'\s*\|\s*', year_content)
                    round_info = []
                    
                    for part in parts:
                        part = part.strip()
                        if 'Round' in part and '截止' in part:
                            # 保留Round信息，格式化为更简洁的形式
                            formatted_part = re.sub(r'Round\s*(\d+)', r'Round\1', part)
                            round_info.append(formatted_part)
                    
                    if round_info:
                        # 用换行符连接Round信息
                        year_info += '\n' + '\n'.join(round_info)
                        formatted_parts.append(year_info)
        
        # 如果没有找到格式化的内容，简化处理原文本
        if not formatted_parts:
            # 简化处理：将 | 替换为换行符，去除开放申请信息
            text = re.sub(r'开放申请\([^)]+\)\s*\|\s*', '', text)
            text = re.sub(r'Round\s*(\d+)', r'Round\1', text)
            text = text.replace('|', '\n')
            
            # 限制行数（最多8行）
            lines = text.split('\n')
            lines = [line.strip() for line in lines if line.strip()]  # 去除空行
            if len(lines) > 8:
                lines = lines[:8]
                lines[-1] += '...'
            
            return '\n'.join(lines) if lines else '待查询'
        
        return '\n\n'.join(formatted_parts[:2])  # 最多显示两年的信息，年份间用双换行分隔

    def _generate_excel_content(
        self, 
        worksheet, 
        client_name: str, 
        grouped_data: Dict[str, List[SchoolBookExcelItem]]
    ):
        """
        生成Excel内容
        
        Args:
            worksheet: Excel工作表
            client_name: 客户姓名
            grouped_data: 按地区分组的数据
        """
        current_row = 1
        
        # 添加标题
        title = f"{client_name} - 定校书"
        worksheet.merge_cells(f'A{current_row}:K{current_row}')  # 更新为K列
        title_cell = worksheet[f'A{current_row}']
        title_cell.value = title
        title_cell.font = Font(name='微软雅黑', size=16, bold=True, color='1F2937')
        title_cell.alignment = self.center_alignment
        title_cell.border = self.border
        # 增加标题行高
        worksheet.row_dimensions[current_row].height = 30
        current_row += 2

        # 添加生成时间
        timestamp = f"生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M')}"
        worksheet.merge_cells(f'A{current_row}:K{current_row}')  # 更新为K列
        time_cell = worksheet[f'A{current_row}']
        time_cell.value = timestamp
        time_cell.font = Font(name='微软雅黑', size=10, color='6B7280')
        time_cell.alignment = self.center_alignment
        time_cell.border = self.border
        current_row += 2
        
        # 表头定义，添加序号列
        headers = [
            '序号', 'QS排名', '学校中文名', '地区', '学费', '学制', '专业中文名', 
            '专业英文名', '语言要求', '项目网址', '申请时间'
        ]
        
        # 添加表头
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=current_row, column=col)
            cell.value = header
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.center_alignment
            cell.border = self.border
        
        current_row += 1
        
        # 添加数据行
        total_programs = 0
        for region, programs in grouped_data.items():
            for program in programs:
                total_programs += 1  # 先递增计数
                row_data = [
                    total_programs,  # 序号
                    program.school_qs_rank,
                    program.school_name_cn,
                    program.school_region,
                    program.program_tuition,
                    program.program_duration,
                    program.program_name_cn,
                    program.program_name_en,
                    program.language_requirements,
                    program.program_website,
                    program.application_time
                ]
                
                for col, value in enumerate(row_data, 1):
                    cell = worksheet.cell(row=current_row, column=col)
                    cell.value = value or ''
                    cell.font = self.content_font
                    cell.border = self.border
                    
                    # 所有内容都居中对齐
                    cell.alignment = self.center_alignment
                
                # 设置最小行高为120
                worksheet.row_dimensions[current_row].height = 120
                current_row += 1
        
        # 添加统计信息
        current_row += 1
        summary_text = f"总计：{total_programs} 个专业项目"
        worksheet.merge_cells(f'A{current_row}:K{current_row}')  # 更新为K列
        summary_cell = worksheet[f'A{current_row}']
        summary_cell.value = summary_text
        summary_cell.font = Font(name='微软雅黑', size=12, bold=True, color='4F46E5')
        summary_cell.alignment = self.center_alignment
        summary_cell.border = self.border

    def _apply_styles(self, worksheet, data_count: int):
        """
        应用样式到工作表
        
        Args:
            worksheet: Excel工作表
            data_count: 数据行数
        """
        # 为数据行添加交替背景色
        start_row = 6  # 数据开始行
        for row in range(start_row, start_row + data_count):
            fill = self.content_fill_1 if (row - start_row) % 2 == 0 else self.content_fill_2
            for col in range(1, 12):  # A到K列
                cell = worksheet.cell(row=row, column=col)
                if not cell.fill.start_color.rgb or cell.fill.start_color.rgb == '00000000':
                    cell.fill = fill
                # 确保所有数据单元格都有边框
                cell.border = self.border
        
        # 为合并单元格的范围添加边框
        self._apply_border_to_merged_cells(worksheet)

    def _apply_border_to_merged_cells(self, worksheet):
        """
        为合并单元格的范围添加边框
        """
        # 为标题行和时间行的合并单元格添加边框
        # 标题行 (第1行)
        for col in range(1, 12):  # A到K列
            cell = worksheet.cell(row=1, column=col)
            cell.border = self.border

        # 时间行 (第3行)
        for col in range(1, 12):  # A到K列
            cell = worksheet.cell(row=3, column=col)
            cell.border = self.border

        # 计算统计行的行号（根据数据行数动态计算）
        total_rows = worksheet.max_row
        summary_row = total_rows

        # 统计行边框
        for col in range(1, 12):  # A到K列
            cell = worksheet.cell(row=summary_row, column=col)
            cell.border = self.border

    def _adjust_column_widths(self, worksheet):
        """
        调整列宽
        
        Args:
            worksheet: Excel工作表
        """
        # 定义列宽（基于内容优化）
        column_widths = {
            'A': 8,    # 序号
            'B': 10,   # QS排名
            'C': 25,   # 学校中文名
            'D': 12,   # 地区
            'E': 20,   # 学费
            'F': 10,   # 学制
            'G': 30,   # 专业中文名
            'H': 35,   # 专业英文名
            'I': 35,   # 语言要求
            'J': 40,   # 项目网址
            'K': 40    # 申请时间 (加宽)
        }
        
        for column, width in column_widths.items():
            worksheet.column_dimensions[column].width = width 