"""
组织邀请管理 API 路由
提供邀请的创建、管理和接受功能
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import Annotated
import logging
from datetime import datetime, timedelta

from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.core.config import settings
from app.models.user import User
from app.models.organization import Organization, OrganizationMember, OrganizationInvitationCode, OrganizationInvitationUsage, PersonalInvitation
from app.models.credit_payment import PaymentOrder
from app.credit_payment.config import CreditPaymentConfig
from app.credit_payment.services.credit_service import CreditService
from app.credit_payment.services.payment_service import PaymentService
from app.schemas.organization import (
    UserInvitationLinkResponse,
    UserInvitationDetailResponse,
    JoinOrganizationRequest,
    JoinOrganizationResponse,
    PersonalInvitationLinkResponse,
    PersonalInvitationStatsResponse,
    ValidateInvitationCodeRequest,
    ValidateInvitationCodeResponse
)
from app.core.organization_permissions import (
    verify_organization_owner,
    check_organization_username_unique
)

# 创建路由器
router = APIRouter(prefix="/invitations", tags=["邀请管理"])

# 创建logger
logger = logging.getLogger(__name__)

# 类型别名
DBSession = Annotated[AsyncSession, Depends(get_db)]
CurrentUser = Annotated[User, Depends(get_current_user)]


# ===============================
# 新的用户邀请码相关API
# ===============================

@router.get("/my-link/{organization_id}", response_model=UserInvitationLinkResponse)
async def get_my_invitation_link(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID")
):
    """
    生成新的组织邀请链接（仅Owner可操作）
    每次调用都会生成一个新的24小时有效的邀请链接

    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_id: 组织ID

    Returns:
        UserInvitationLinkResponse: 邀请链接信息

    Raises:
        HTTPException: 权限不足或组织不存在
    """
    # 验证用户是组织Owner
    await verify_organization_owner(db, current_user, organization_id)

    # 获取组织信息
    org_result = await db.execute(
        select(Organization).where(Organization.id == organization_id)
    )
    organization = org_result.scalars().first()

    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="组织不存在"
        )

    try:
        # 检查是否已有有效的邀请码
        existing_invitation_result = await db.execute(
            select(OrganizationInvitationCode).where(
                OrganizationInvitationCode.organization_id == organization_id,
                OrganizationInvitationCode.status == "active"
            ).order_by(OrganizationInvitationCode.created_at.desc())
        )
        existing_invitation = existing_invitation_result.scalars().first()

        # 如果有有效的邀请码且未过期，返回现有的
        if existing_invitation and existing_invitation.is_valid():
            remaining_time = existing_invitation.get_remaining_time()
            invite_link = f"{settings.FRONTEND_URL}/invite?code={existing_invitation.invitation_code}&org={organization_id}"

            return UserInvitationLinkResponse(
                invitation_code=existing_invitation.invitation_code,
                organization_id=organization_id,
                organization_name=organization.name,
                invite_link=invite_link,
                expires_at=existing_invitation.expires_at.isoformat(),
                remaining_hours=int(remaining_time.total_seconds() / 3600),
                status=existing_invitation.status
            )

        # 如果现有邀请码已过期，更新其状态
        if existing_invitation and existing_invitation.is_expired():
            existing_invitation.status = "expired"

        # 生成新的邀请码
        invitation_code = OrganizationInvitationCode.generate_invitation_code()
        expires_at = OrganizationInvitationCode.get_default_expiry()

        new_invitation = OrganizationInvitationCode(
            organization_id=organization_id,
            invited_by_user_id=current_user.id,
            invitation_code=invitation_code,
            status="active",
            expires_at=expires_at
        )

        db.add(new_invitation)
        await db.commit()

        # 生成邀请链接
        invite_link = f"{settings.FRONTEND_URL}/invite?code={invitation_code}&org={organization_id}"

        return UserInvitationLinkResponse(
            invitation_code=invitation_code,
            organization_id=organization_id,
            organization_name=organization.name,
            invite_link=invite_link,
            expires_at=expires_at.isoformat(),
            remaining_hours=24,
            status="active"
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"生成邀请链接失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成邀请链接失败，请稍后重试"
        )


@router.get("/user-invite-detail", response_model=UserInvitationDetailResponse)
async def get_user_invitation_detail(
    db: DBSession,
    code: str = Query(..., description="邀请码"),
    org: int = Query(..., description="组织ID")
):
    """
    获取邀请详情（通过动态邀请码和组织ID）

    Args:
        db: 数据库会话
        code: 动态邀请码
        org: 组织ID

    Returns:
        UserInvitationDetailResponse: 邀请详情

    Raises:
        HTTPException: 邀请码无效、已过期或组织不存在
    """
    # 通过邀请码查找邀请记录
    invitation_result = await db.execute(
        select(OrganizationInvitationCode).where(
            OrganizationInvitationCode.invitation_code == code,
            OrganizationInvitationCode.organization_id == org
        )
    )
    invitation = invitation_result.scalars().first()

    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="邀请码无效"
        )

    # 检查邀请是否有效
    if not invitation.is_valid():
        if invitation.is_expired():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邀请链接已过期"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邀请链接已失效"
            )

    # 获取邀请人信息和其在组织中的用户名
    inviting_user_result = await db.execute(
        select(User).where(User.id == invitation.invited_by_user_id)
    )
    inviting_user = inviting_user_result.scalars().first()

    if not inviting_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="邀请人不存在"
        )

    # 获取邀请人在该组织中的成员信息
    inviter_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.user_id == invitation.invited_by_user_id,
            OrganizationMember.organization_id == org,
            OrganizationMember.is_active == True
        )
    )
    inviter_member = inviter_member_result.scalars().first()

    # 获取组织信息
    org_result = await db.execute(
        select(Organization).where(
            Organization.id == org,
            Organization.is_active == True
        )
    )
    organization = org_result.scalars().first()

    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="组织不存在"
        )

    return UserInvitationDetailResponse(
        invitation_code=code,
        invited_by_username=inviting_user.username,
        invited_by_nickname=inviting_user.nickname,
        invited_by_organization_username=inviter_member.organization_username if inviter_member else None,
        organization_id=organization.id,
        organization_name=organization.name,
        organization_description=organization.description
    )


@router.post("/join", response_model=JoinOrganizationResponse)
async def join_organization(
    db: DBSession,
    current_user: CurrentUser,
    join_data: JoinOrganizationRequest
):
    """
    通过动态邀请码加入组织

    Args:
        db: 数据库会话
        current_user: 当前用户
        join_data: 加入组织请求数据

    Returns:
        JoinOrganizationResponse: 加入结果

    Raises:
        HTTPException: 邀请码无效、已过期、用户已在组织中或其他错误
    """
    # 通过邀请码查找邀请记录
    invitation_result = await db.execute(
        select(OrganizationInvitationCode).where(
            OrganizationInvitationCode.invitation_code == join_data.invitation_code,
            OrganizationInvitationCode.organization_id == join_data.organization_id
        )
    )
    invitation = invitation_result.scalars().first()

    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="邀请码无效"
        )

    # 检查邀请是否有效
    if not invitation.is_valid():
        if invitation.is_expired():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邀请链接已过期"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邀请链接已失效"
            )

    # 获取组织信息
    org_result = await db.execute(
        select(Organization).where(
            Organization.id == join_data.organization_id,
            Organization.is_active == True
        )
    )
    organization = org_result.scalars().first()

    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="组织不存在"
        )

    # 检查组织用户名是否已存在
    is_unique = await check_organization_username_unique(
        db, join_data.organization_id, join_data.organization_username
    )
    if not is_unique:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="组织用户名已存在"
        )

    # 检查用户是否已在组织中
    existing_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == join_data.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    existing_member = existing_member_result.scalars().first()

    if existing_member:
        if existing_member.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="您已经是该组织的成员"
            )
        else:
            # 重新激活已存在的成员关系
            existing_member.is_active = True
            existing_member.organization_username = join_data.organization_username
            existing_member.joined_at = datetime.utcnow()

            await db.commit()

            return JoinOrganizationResponse(
                message="成功重新加入组织",
                organization_id=organization.id,
                organization_name=organization.name,
                organization_username=join_data.organization_username,
                role=existing_member.role
            )

    try:
        # 创建新的成员关系
        new_member = OrganizationMember(
            organization_id=join_data.organization_id,
            user_id=current_user.id,
            role="member",
            organization_username=join_data.organization_username
        )
        db.add(new_member)

        # 创建使用记录
        usage_record = OrganizationInvitationUsage(
            invitation_code_id=invitation.id,
            user_id=current_user.id,
            organization_id=join_data.organization_id,
            organization_username=join_data.organization_username,
            joined_at=datetime.utcnow()
        )
        db.add(usage_record)

        await db.commit()

        return JoinOrganizationResponse(
            message="成功加入组织",
            organization_id=organization.id,
            organization_name=organization.name,
            organization_username=join_data.organization_username,
            role="member"
        )

    except Exception as e:
        await db.rollback()
        logger.error(f"加入组织失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="加入组织失败，请稍后重试"
        )


# ===============================
# 组织邀请链接管理API
# ===============================

@router.get("/active-links/{organization_id}")
async def get_active_invitation_links(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID")
):
    """
    获取组织当前有效的邀请链接列表（仅Owner可操作）

    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_id: 组织ID

    Returns:
        list: 有效的邀请链接列表
    """
    # 验证用户是组织Owner
    await verify_organization_owner(db, current_user, organization_id)

    # 查询有效的邀请链接
    active_invitations_result = await db.execute(
        select(OrganizationInvitationCode).where(
            OrganizationInvitationCode.organization_id == organization_id,
            OrganizationInvitationCode.status == "active"
        ).order_by(OrganizationInvitationCode.created_at.desc())
    )
    active_invitations = active_invitations_result.scalars().all()

    # 过滤掉已过期的邀请
    valid_invitations = []
    for invitation in active_invitations:
        if invitation.is_valid():
            remaining_time = invitation.get_remaining_time()
            valid_invitations.append({
                "id": invitation.id,
                "invitation_code": invitation.invitation_code,
                "invite_link": f"{settings.FRONTEND_URL}/invite?code={invitation.invitation_code}&org={organization_id}",
                "created_at": invitation.created_at.isoformat(),
                "expires_at": invitation.expires_at.isoformat(),
                "remaining_hours": int(remaining_time.total_seconds() / 3600),
                "remaining_minutes": int((remaining_time.total_seconds() % 3600) / 60),
                "status": invitation.status
            })
        else:
            # 自动更新过期的邀请状态
            invitation.status = "expired"

    await db.commit()
    return {"active_invitations": valid_invitations}


@router.post("/revoke/{invitation_id}")
async def revoke_invitation_link(
    db: DBSession,
    current_user: CurrentUser,
    invitation_id: int = Path(..., description="邀请记录ID")
):
    """
    撤销指定的邀请链接（仅Owner可操作）

    Args:
        db: 数据库会话
        current_user: 当前用户
        invitation_id: 邀请记录ID

    Returns:
        dict: 撤销结果
    """
    # 查找邀请记录
    invitation_result = await db.execute(
        select(OrganizationInvitationCode).where(
            OrganizationInvitationCode.id == invitation_id
        )
    )
    invitation = invitation_result.scalars().first()

    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="邀请记录不存在"
        )

    # 验证用户是组织Owner
    await verify_organization_owner(db, current_user, invitation.organization_id)

    # 撤销邀请
    invitation.revoke()
    await db.commit()

    return {
        "message": "邀请链接已成功撤销",
        "invitation_id": invitation_id
    }


# ===============================
# 个人邀请码相关服务函数
# ===============================

async def grant_trial_package(db: AsyncSession, user: User) -> bool:
    """
    为用户发放试用套餐

    Args:
        db: 数据库会话
        user: 用户对象

    Returns:
        bool: 是否成功发放
    """
    try:
        # 0. 确保用户处于个人身份（个人邀请奖励只能发放给个人身份）
        from app.core.data_isolation import DataIsolationFilter
        identity_type, _ = DataIsolationFilter.get_user_data_scope(user)

        if identity_type != "personal":
            logger.warning(f"用户 {user.id} 当前不是个人身份，跳过个人邀请奖励发放")
            return False

        # 1. 获取试用套餐配置
        package_info = await CreditPaymentConfig.get_package_by_id_async("trial_package", db)
        if not package_info:
            logger.error("试用套餐配置不存在")
            return False

        # 2. 获取或创建用户积分账户
        user_account = await CreditService.get_or_create_credit_account(user, db)
        if not user_account:
            logger.error(f"无法为用户 {user.id} 创建积分账户")
            return False

        # 3. 计算套餐开始时间（支持时间叠加）
        start_time = await PaymentService._calculate_package_start_time(user, db)

        # 4. 计算套餐过期时间
        validity_days = package_info.get("validity_days", 3)  # 试用套餐默认3天
        expires_at = start_time + timedelta(days=validity_days)

        # 5. 创建PaymentOrder记录
        order_no = PaymentService.generate_order_no()
        current_time = datetime.utcnow()

        payment_order = PaymentOrder(
            order_no=order_no,
            user_id=user.id,
            user_credit_account_id=user_account.id,
            organization_id=None,  # 个人身份
            amount=0,  # 邀请奖励免费
            credits=package_info["credits"],
            payment_method="invitation_reward",  # 特殊支付方式
            package_id="trial_package",
            status="paid",  # 直接设为已支付
            payment_time=current_time,
            expires_at=expires_at,
            remark="邀请奖励试用套餐"
        )
        db.add(payment_order)
        await db.flush()  # 获取订单ID

        # 6. 为用户增加积分（使用现有事务）
        credits_to_add = package_info["credits"]
        success, message = await CreditService._add_credits_in_existing_transaction(
            db=db,
            user=user,
            credits=credits_to_add,
            transaction_type="recharge",
            order_id=payment_order.id,  # 关联订单ID
            description=f"邀请奖励试用套餐 - {credits_to_add}积分"
        )

        if not success:
            logger.error(f"为用户 {user.id} 增加积分失败: {message}")
            return False

        logger.info(f"成功为用户 {user.id} 发放试用套餐: {credits_to_add}积分，有效期至 {expires_at}")
        return True

    except Exception as e:
        logger.error(f"发放试用套餐失败: {str(e)}")
        return False


async def process_personal_invitation_reward(
    db: AsyncSession,
    inviter: User,
    invitee: User,
    invitation_code: str
) -> PersonalInvitation:
    """
    处理个人邀请奖励

    Args:
        db: 数据库会话
        inviter: 邀请人
        invitee: 被邀请人
        invitation_code: 邀请码

    Returns:
        PersonalInvitation: 邀请记录
    """
    # 创建邀请记录
    invitation_record = PersonalInvitation(
        inviter_user_id=inviter.id,
        invitee_user_id=invitee.id,
        invitation_code=invitation_code,
        reward_package_id="trial_package"
    )
    db.add(invitation_record)

    # 为邀请人发放奖励
    inviter_reward_success = await grant_trial_package(db, inviter)
    if inviter_reward_success:
        invitation_record.inviter_rewarded = True
        invitation_record.inviter_rewarded_at = datetime.utcnow()

    # 为被邀请人发放奖励
    invitee_reward_success = await grant_trial_package(db, invitee)
    if invitee_reward_success:
        invitation_record.invitee_rewarded = True
        invitation_record.invitee_rewarded_at = datetime.utcnow()

    await db.commit()
    return invitation_record


# ===============================
# 个人邀请码相关API
# ===============================

@router.get("/personal/my-link", response_model=PersonalInvitationLinkResponse)
async def get_my_personal_invitation_link(
    db: DBSession,
    current_user: CurrentUser
):
    """
    获取用户的个人邀请链接

    Args:
        db: 数据库会话
        current_user: 当前用户

    Returns:
        PersonalInvitationLinkResponse: 个人邀请链接信息
    """
    # 检查用户身份：只有个人身份才能使用个人邀请功能
    from app.core.data_isolation import DataIsolationFilter
    identity_type, _ = DataIsolationFilter.get_user_data_scope(current_user)

    if identity_type != "personal":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="此功能只能在个人身份下使用，请切换到个人身份"
        )

    # 确保用户有邀请码
    current_user.ensure_invitation_code()
    await db.commit()

    # 生成邀请链接
    invite_link = f"{settings.FRONTEND_URL}/register?invite={current_user.invitation_code}"

    return PersonalInvitationLinkResponse(
        invitation_code=current_user.invitation_code,
        invite_link=invite_link
    )


@router.get("/personal/stats", response_model=PersonalInvitationStatsResponse)
async def get_personal_invitation_stats(
    db: DBSession,
    current_user: CurrentUser
):
    """
    获取用户的个人邀请统计信息

    Args:
        db: 数据库会话
        current_user: 当前用户

    Returns:
        PersonalInvitationStatsResponse: 邀请统计信息
    """
    # 检查用户身份：只有个人身份才能使用个人邀请功能
    from app.core.data_isolation import DataIsolationFilter
    identity_type, _ = DataIsolationFilter.get_user_data_scope(current_user)

    if identity_type != "personal":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="此功能只能在个人身份下使用，请切换到个人身份"
        )

    # 确保用户有邀请码
    current_user.ensure_invitation_code()
    await db.commit()

    # 查询邀请统计
    total_result = await db.execute(
        select(PersonalInvitation).where(
            PersonalInvitation.inviter_user_id == current_user.id
        )
    )
    total_invitations = len(total_result.scalars().all())

    successful_result = await db.execute(
        select(PersonalInvitation).where(
            PersonalInvitation.inviter_user_id == current_user.id,
            PersonalInvitation.inviter_rewarded == True
        )
    )
    successful_invitations = len(successful_result.scalars().all())

    # 计算待发放和已发放奖励
    pending_rewards = total_invitations - successful_invitations
    total_rewards = successful_invitations

    return PersonalInvitationStatsResponse(
        invitation_code=current_user.invitation_code,
        total_invitations=total_invitations,
        successful_invitations=successful_invitations,
        pending_rewards=pending_rewards,
        total_rewards=total_rewards
    )


@router.post("/personal/validate", response_model=ValidateInvitationCodeResponse)
async def validate_personal_invitation_code(
    db: DBSession,
    validate_data: ValidateInvitationCodeRequest
):
    """
    验证个人邀请码是否有效（无需登录）

    Args:
        db: 数据库会话
        validate_data: 验证请求数据

    Returns:
        ValidateInvitationCodeResponse: 验证结果
    """
    # 查找邀请码对应的用户
    user_result = await db.execute(
        select(User).where(User.invitation_code == validate_data.invitation_code)
    )
    inviter = user_result.scalars().first()

    if not inviter:
        return ValidateInvitationCodeResponse(
            is_valid=False,
            message="邀请码不存在"
        )

    return ValidateInvitationCodeResponse(
        is_valid=True,
        inviter_username=inviter.username,
        message="邀请码有效"
    )


@router.post("/personal/process-reward")
async def process_personal_invitation_reward_endpoint(
    db: DBSession,
    current_user: CurrentUser,
    validate_data: ValidateInvitationCodeRequest
):
    """
    处理登录后的个人邀请奖励（用户已登录）

    Args:
        db: 数据库会话
        current_user: 当前登录用户
        validate_data: 包含邀请码的请求数据

    Returns:
        dict: 处理结果
    """
    invitation_code = validate_data.invitation_code
    
    # 查找邀请码对应的邀请人
    inviter_result = await db.execute(
        select(User).where(User.invitation_code == invitation_code)
    )
    inviter = inviter_result.scalars().first()

    if not inviter:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邀请码不存在或无效"
        )

    # 检查是否已经处理过该邀请
    existing_invitation = await db.execute(
        select(PersonalInvitation).where(
            PersonalInvitation.inviter_user_id == inviter.id,
            PersonalInvitation.invitee_user_id == current_user.id,
            PersonalInvitation.invitation_code == invitation_code
        )
    )
    
    if existing_invitation.scalars().first():
        return {
            "success": True,
            "message": "邀请奖励已经处理过了",
            "already_processed": True
        }

    # 不能邀请自己
    if inviter.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能使用自己的邀请码"
        )

    try:
        # 处理邀请奖励
        invitation_record = await process_personal_invitation_reward(
            db, inviter, current_user, invitation_code
        )

        # 获取试用套餐信息
        package_info = await CreditPaymentConfig.get_package_by_id_async("trial_package", db)
        trial_days = package_info.get("validity_days", 3) if package_info else 3

        message = f"邀请成功！您和邀请人都获得了 {trial_days} 天试用套餐"
        
        return {
            "success": True,
            "message": message,
            "invitation_id": invitation_record.id,
            "trial_days": trial_days,
            "inviter_username": inviter.username,
            "inviter_rewarded": invitation_record.inviter_rewarded,
            "invitee_rewarded": invitation_record.invitee_rewarded
        }

    except Exception as e:
        logger.error(f"处理个人邀请奖励失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理邀请奖励时发生错误: {str(e)}"
        )
