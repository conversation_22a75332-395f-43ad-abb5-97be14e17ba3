import asyncio
import io
import urllib.parse
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload
from typing import List, Optional, Dict, Any
import logging

from app.utils.excel_export import SchoolBookExcelExporter

# 时间工具函数
def get_utc_now():
    """获取当前UTC时间（不带时区信息，用于数据库存储）"""
    return datetime.utcnow()

from app.core.dependencies import CurrentUser, DBSession
from app.core.cache import cached, cache_client_data, cache_profile_summary
from app.core.data_isolation import DataIsolationFilter, get_current_data_scope
from app.models.client import (
    Client, Education, Academic, Work, Activity, Award, Skill, LanguageScore, Thought,
    BackgroundCustomModule, ThoughtCustomModule, ClientProgram
)
from app.schemas.client import (
    ClientCreate, ClientUpdate, ClientResponse, ClientDetailResponse,
    EducationCreate, EducationUpdate, EducationResponse,
    AcademicCreate, AcademicUpdate, AcademicResponse,
    WorkCreate, WorkUpdate, WorkResponse,
    ActivityCreate, ActivityUpdate, ActivityResponse,
    AwardCreate, AwardUpdate, AwardResponse,
    SkillCreate, SkillUpdate, SkillResponse,
    LanguageScoreCreate, LanguageScoreUpdate, LanguageScoreResponse,
    ThoughtCreate, ThoughtUpdate, ThoughtResponse,
    CustomModuleCreate, CustomModuleUpdate, CustomModuleResponse,
    ClientProgramCreate, ClientProgramResponse, ClientDocumentSummary,
    SchoolBookExportRequest, SchoolBookExportResponse
)
from pydantic import BaseModel, Field
from typing import Optional
from app.ai_selection.db.models import AISelectionProgram
from app.models.client import AIWritingCV, AIWritingPS, AIWritingRL
from app.utils.excel_export import SchoolBookExcelExporter

logger = logging.getLogger(__name__)

# 快速建档相关Schema
class QuickProfileCreate(BaseModel):
    """快速建档创建请求"""
    name: str = Field(..., description="客户姓名")
    school: Optional[str] = Field(None, description="本科院校")
    major: Optional[str] = Field(None, description="本科专业")
    gpa: Optional[str] = Field(None, description="本科成绩")
    gpa_scale: Optional[str] = Field("100", description="GPA制度：100(百分制)、4.0(四分制)、5.0(五分制)")
    service_type: Optional[str] = Field("master", description="服务类型：undergraduate(本科)、master(硕士)等")

class QuickProfileResponse(BaseModel):
    """快速建档响应"""
    id_hashed: str = Field(..., description="客户哈希ID")
    name: str = Field(..., description="客户姓名")
    profile_type: bool = Field(..., description="档案类型：false=快速建档")
    education: Optional[dict] = Field(None, description="教育背景信息")
    created_at: str = Field(..., description="创建时间")

class QuickProfileUpdate(BaseModel):
    """快速建档更新请求"""
    school: Optional[str] = Field(None, description="本科院校")
    major: Optional[str] = Field(None, description="本科专业")
    gpa: Optional[str] = Field(None, description="本科成绩")
    gpa_scale: Optional[str] = Field(None, description="GPA制度")

# 创建路由器
router = APIRouter(prefix="/clients", tags=["客户"])

# 辅助函数
async def verify_client_ownership(
    db: DBSession,
    client_id: str,
    current_user: CurrentUser
) -> Client:
    """
    验证客户是否属于当前用户（支持多租户数据隔离）

    Args:
        db: 数据库会话
        client_id: 客户哈希ID
        current_user: 当前用户

    Returns:
        Client: 客户对象

    Raises:
        HTTPException: 如果客户不存在或不属于当前用户/组织
    """
    # 构建基础查询
    query = select(Client).where(Client.id_hashed == client_id)
    
    # 应用身份隔离过滤（支持Owner放宽）
    query = await DataIsolationFilter.apply_client_filter_async(query, current_user, db)

    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        # 获取当前数据范围信息用于错误提示
        data_scope = get_current_data_scope(current_user)
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"客户不存在或您在当前身份 ({data_scope['description']}) 下没有访问权限"
        )

    return client

# 客户信息CRUD相关：
@router.get("/", status_code=status.HTTP_200_OK)
@cache_profile_summary(ttl=300)  # 缓存5分钟
async def get_clients(
    db: DBSession,
    current_user: CurrentUser,
    skip: int = Query(0, description="分页起始位置"),
    limit: int = Query(100, description="每页数量"),
    is_archived: Optional[bool] = Query(None, description="是否已归档（服务完成）"),
    search: Optional[str] = Query(None, description="搜索关键词（支持姓名、电话、邮箱、学生ID等多字段搜索）"),
):
    """
    获取客户列表

    Args:
        skip: 分页起始位置
        limit: 每页数量
        is_archived: 是否已归档（服务完成），None表示不筛选
        search: 搜索关键词，支持多字段模糊匹配（姓名、电话、邮箱、身份证、护照、哈希ID）
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[Dict]: 客户列表数据，包含教育信息
    """
    # 构建基础查询 - 只返回完整档案客户记录，并预加载教育信息
    query = select(Client).options(joinedload(Client.education)).where(
        Client.profile_type == True  # 只显示完整档案客户
    )
    
    # 应用身份隔离过滤（支持Owner放宽）
    query = await DataIsolationFilter.apply_client_filter_async(query, current_user, db)

    # 根据归档状态筛选
    if is_archived is not None:
        query = query.where(Client.is_archived == is_archived)

    # 根据搜索关键词筛选 - 支持多字段搜索
    if search:
        search_term = f"%{search}%"
        # 使用 OR 条件匹配多个字段
        search_conditions = [
            Client.name.ilike(search_term),           # 姓名
            Client.phone.ilike(search_term),          # 电话
            Client.email.ilike(search_term),          # 邮箱
            Client.id_card.ilike(search_term),        # 身份证（学生ID）
            Client.passport.ilike(search_term),       # 护照（学生ID）
            Client.id_hashed.ilike(search_term),      # 哈希ID（系统学生ID）
            Client.location.ilike(search_term)        # 所在城市
        ]
        # 过滤掉None值的条件
        from sqlalchemy import or_
        query = query.where(or_(*[cond for cond in search_conditions if cond is not None]))

    # 分页
    query = query.offset(skip).limit(limit)

    # 执行查询
    result = await db.execute(query)
    clients = result.scalars().unique().all()

    # 构建返回数据，包含教育信息
    client_list = []
    for client in clients:
        # 获取第一条教育记录的学校名称
        school_name = None
        if client.education and len(client.education) > 0:
            school_name = client.education[0].school

        client_data = {
            "id_hashed": client.id_hashed,
            "name": client.name,
            "gender": client.gender,
            "phone": client.phone,
            "email": client.email,
            "location": client.location,
            "address": client.address,
            "id_card": client.id_card,
            "passport": client.passport,
            "service_type": client.service_type,
            "profile_type": client.profile_type,
            "is_archived": client.is_archived,
            "created_at": client.created_at,
            "updated_at": client.updated_at,
            "school": school_name  # 添加学校名称字段
        }
        client_list.append(client_data)

    return client_list

@router.post("/", response_model=ClientResponse, status_code=status.HTTP_201_CREATED)
async def create_client(
    db: DBSession,
    client_data: ClientCreate,
    current_user: CurrentUser,
):
    """
    创建新客户

    Args:
        client_data: 客户数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 创建的客户信息
    """
    # 创建新客户
    client_dict = client_data.dict()
    # 确保新客户的归档状态为False
    client_dict["is_archived"] = False
    # 自动设置为当前用户的客户
    client_dict["user_id"] = current_user.id
    
    # 根据当前身份设置组织ID（多租户支持）
    identity_type, organization_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type == 'organization' and organization_id:
        client_dict["organization_id"] = organization_id
    else:
        client_dict["organization_id"] = None
    
    client = Client(**client_dict)

    db.add(client)
    await db.commit()
    await db.refresh(client)

    return client

@router.post("/quick-profile", response_model=QuickProfileResponse, status_code=status.HTTP_201_CREATED)
async def create_quick_profile(
    db: DBSession,
    profile_data: QuickProfileCreate,
    current_user: CurrentUser,
):
    """
    创建快速建档客户

    Args:
        profile_data: 快速建档数据
        current_user: 当前用户
        db: 数据库会话

    Returns:
        QuickProfileResponse: 创建的快速建档客户信息
    """
    # 检查是否已存在同名客户（优先查找快速建档客户）
    # 首先查找快速建档客户
    quick_profile_query = select(Client).where(
        Client.name == profile_data.name,
        Client.profile_type == False
    )
    # 应用身份隔离过滤（支持Owner放宽）
    quick_profile_query = await DataIsolationFilter.apply_client_filter_async(quick_profile_query, current_user, db)
    quick_profile_result = await db.execute(quick_profile_query)
    existing_quick_client = quick_profile_result.scalars().first()

    # 如果没有快速建档客户，检查是否有完整档案客户
    existing_full_client = None
    if not existing_quick_client:
        full_profile_query = select(Client).where(
            Client.name == profile_data.name,
            Client.profile_type == True
        )
        # 应用身份隔离过滤（支持Owner放宽）
        full_profile_query = await DataIsolationFilter.apply_client_filter_async(full_profile_query, current_user, db)
        full_profile_result = await db.execute(full_profile_query)
        existing_full_client = full_profile_result.scalars().first()

    # 优先处理快速建档客户
    existing_client = existing_quick_client

    # 如果存在完整档案客户，返回提示信息
    if existing_full_client:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"客户 '{profile_data.name}' 已存在完整档案，请在客户档案中查看或编辑"
        )

    if existing_client:
        # 如果存在，更新教育信息
        if profile_data.school or profile_data.major or profile_data.gpa:
            # 查找现有的教育记录
            education_query = select(Education).where(Education.client_id == existing_client.id)
            education_result = await db.execute(education_query)
            education = education_result.scalars().first()

            if education:
                # 更新现有教育记录
                if profile_data.school:
                    education.school = profile_data.school
                if profile_data.major:
                    education.major = profile_data.major
                if profile_data.gpa:
                    education.gpa = profile_data.gpa
                if profile_data.gpa_scale:
                    education.gpa_scale = profile_data.gpa_scale

            else:
                # 创建新的教育记录
                education = Education(
                    client_id=existing_client.id,
                    school=profile_data.school,
                    major=profile_data.major,
                    gpa=profile_data.gpa,
                    gpa_scale=profile_data.gpa_scale,
                    degree="bachelor",  # 默认本科
                    start_date="",
                    end_date=""
                )
                db.add(education)

            await db.commit()
            await db.refresh(existing_client)

        # 返回现有客户信息
        education_info = None
        # 重新查询教育信息以避免异步关系访问问题
        education_query = select(Education).where(Education.client_id == existing_client.id)
        education_result = await db.execute(education_query)
        education = education_result.scalars().first()

        if education:
            education_info = {
                "school": education.school,
                "major": education.major,
                "gpa": education.gpa,
                "gpa_scale": education.gpa_scale
            }

        return QuickProfileResponse(
            id_hashed=existing_client.id_hashed,
            name=existing_client.name,
            profile_type=existing_client.profile_type,
            education=education_info,
            created_at=existing_client.created_at.isoformat()
        )

    # 创建新的快速建档客户
    client = Client(
        name=profile_data.name,
        service_type=profile_data.service_type,
        profile_type=False,  # 标记为快速建档
        is_archived=False
    )

    # 如果有当前用户，设置为客户的顾问
    if current_user:
        client.user_id = current_user.id
        
        # 根据当前身份设置组织ID（多租户支持）
        identity_type, organization_id = DataIsolationFilter.get_user_data_scope(current_user)
        if identity_type == 'organization' and organization_id:
            client.organization_id = organization_id
        else:
            client.organization_id = None

    db.add(client)
    await db.commit()
    await db.refresh(client)

    # 如果有教育信息，创建教育记录
    education_info = None
    if profile_data.school or profile_data.major or profile_data.gpa:
        education = Education(
            client_id=client.id,
            school=profile_data.school or "",
            major=profile_data.major or "",
            gpa=profile_data.gpa or "",
            gpa_scale=profile_data.gpa_scale or "100",
            degree="bachelor",  # 默认本科
            start_date="",
            end_date=""
        )
        db.add(education)
        await db.commit()
        await db.refresh(education)

        education_info = {
            "school": education.school,
            "major": education.major,
            "gpa": education.gpa,
            "gpa_scale": education.gpa_scale
        }

    return QuickProfileResponse(
        id_hashed=client.id_hashed,
        name=client.name,
        profile_type=client.profile_type,
        education=education_info,
        created_at=client.created_at.isoformat()
    )

@router.get("/quick-profile/{client_name}", response_model=QuickProfileResponse, status_code=status.HTTP_200_OK)
async def get_quick_profile_by_name(
    db: DBSession,
    current_user: CurrentUser,
    client_name: str = Path(..., description="客户姓名"),
):
    """
    根据姓名获取快速建档客户信息

    **安全说明**: 只能查询当前用户创建的客户信息，确保数据隔离

    Args:
        client_name: 客户姓名
        current_user: 当前登录用户（必需，用于数据权限控制）
        db: 数据库会话

    Returns:
        QuickProfileResponse: 快速建档客户信息

    Raises:
        HTTPException: 客户不存在或无权限访问
    """
    # 查询快速建档客户 - 添加用户权限过滤
    query = select(Client).options(
        selectinload(Client.education)
    ).where(
        Client.name == client_name,
        Client.profile_type == False
    )

    # 应用身份隔离过滤（支持Owner放宽） - 🔒
    query = await DataIsolationFilter.apply_client_filter_async(query, current_user, db)
    result = await db.execute(query)
    client = result.scalars().first()

    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到该快速建档客户"
        )

    # 获取教育信息
    education_info = None
    if client.education:
        education = client.education[0]  # 取第一条教育记录
        education_info = {
            "school": education.school,
            "major": education.major,
            "gpa": education.gpa,
            "gpa_scale": education.gpa_scale
        }

    return QuickProfileResponse(
        id_hashed=client.id_hashed,
        name=client.name,
        profile_type=client.profile_type,
        education=education_info,
        created_at=client.created_at.isoformat()
    )

@router.get("/{client_id}", response_model=ClientDetailResponse, status_code=status.HTTP_200_OK)
@cache_client_data(ttl=600)  # 缓存10分钟
async def get_client(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    include_modules: Optional[str] = Query(None, description="要包含的模块，逗号分隔：education,academic,work,activities,awards,skills,language_scores,thoughts,background_modules,thought_modules,client_programs"),
):
    """
    获取单个客户详情 - 优化版本，支持懒加载和按需查询

    Args:
        client_id: 客户哈希ID
        include_modules: 要包含的模块列表，支持按需加载以提高性能
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientDetailResponse: 客户详细信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 获取客户真实ID用于关联查询
    real_client_id = client.id

    # 解析要包含的模块
    if include_modules:
        requested_modules = set(include_modules.split(','))
    else:
        # 默认包含基本模块，包括语言成绩
        requested_modules = {'education', 'academic', 'work', 'activities', 'awards', 'skills', 'language_scores'}

    # 只查询请求的模块，实现懒加载
    query_tasks = []
    
    if 'education' in requested_modules:
        education_query = select(Education).where(Education.client_id == real_client_id)
        query_tasks.append(('education', db.execute(education_query)))
    
    if 'academic' in requested_modules:
        academic_query = select(Academic).where(Academic.client_id == real_client_id)
        query_tasks.append(('academic', db.execute(academic_query)))
    
    if 'work' in requested_modules:
        work_query = select(Work).where(Work.client_id == real_client_id)
        query_tasks.append(('work', db.execute(work_query)))
    
    if 'activities' in requested_modules:
        activity_query = select(Activity).where(Activity.client_id == real_client_id)
        query_tasks.append(('activities', db.execute(activity_query)))
    
    if 'awards' in requested_modules:
        award_query = select(Award).where(Award.client_id == real_client_id)
        query_tasks.append(('awards', db.execute(award_query)))
    
    if 'skills' in requested_modules:
        skill_query = select(Skill).where(Skill.client_id == real_client_id)
        query_tasks.append(('skills', db.execute(skill_query)))
    
    if 'language_scores' in requested_modules:
        language_score_query = select(LanguageScore).where(LanguageScore.client_id == real_client_id)
        query_tasks.append(('language_scores', db.execute(language_score_query)))
    
    if 'thoughts' in requested_modules:
        thought_query = select(Thought).where(Thought.client_id == real_client_id)
        query_tasks.append(('thoughts', db.execute(thought_query)))
    
    if 'background_modules' in requested_modules:
        background_module_query = select(BackgroundCustomModule).where(BackgroundCustomModule.client_id == real_client_id)
        query_tasks.append(('background_modules', db.execute(background_module_query)))
    
    if 'thought_modules' in requested_modules:
        thought_module_query = select(ThoughtCustomModule).where(ThoughtCustomModule.client_id == real_client_id)
        query_tasks.append(('thought_modules', db.execute(thought_module_query)))
    
    if 'client_programs' in requested_modules:
        client_program_query = select(ClientProgram).options(
            joinedload(ClientProgram.ai_selection_program)
        ).where(ClientProgram.client_id == real_client_id)
        query_tasks.append(('client_programs', db.execute(client_program_query)))

    # 并行执行选中的查询
    results = {}
    if query_tasks:
        query_results = await asyncio.gather(*[task[1] for task in query_tasks])
        for i, (module_name, _) in enumerate(query_tasks):
            results[module_name] = query_results[i].scalars().all()

    # 构建客户数据
    client_data = {
        "id_hashed": client.id_hashed,
        "name": client.name,
        "gender": client.gender,
        "phone": client.phone,
        "email": client.email,
        "location": client.location,
        "address": client.address,
        "id_card": client.id_card,
        "passport": client.passport,
        "id_card_issuer": client.id_card_issuer,
        "id_card_validity": client.id_card_validity,
        "passport_issue_place": client.passport_issue_place,
        "passport_issue_date": client.passport_issue_date,
        "passport_expiry": client.passport_expiry,
        "service_type": client.service_type,
        "user_id": client.user_id,
        "is_archived": client.is_archived,
        "created_at": client.created_at.isoformat() if client.created_at else None,
        "updated_at": client.updated_at.isoformat() if client.updated_at else None,
    }

    # 只添加请求的模块数据
    for module_name in requested_modules:
        if module_name in results:
            if module_name == 'client_programs':
                # 处理定校书数据的特殊逻辑
                client_programs_with_details = []
                school_names = []
                
                for cp in results[module_name]:
                    if cp.ai_selection_program and cp.ai_selection_program.school_name_cn:
                        school_names.append(cp.ai_selection_program.school_name_cn)
                
                # 批量查询学校logo
                school_logo_map = {}
                if school_names:
                    from app.ai_selection.db.models import AISelectionAbroadSchool
                    school_logo_query = select(
                        AISelectionAbroadSchool.school_name_cn,
                        AISelectionAbroadSchool.school_logo_url
                    ).where(AISelectionAbroadSchool.school_name_cn.in_(school_names))
                    school_logo_result = await db.execute(school_logo_query)
                    school_logo_map = {row[0]: row[1] for row in school_logo_result.fetchall()}
                
                for cp in results[module_name]:
                    cp_dict = cp.to_dict()
                    if cp.ai_selection_program:
                        school_logo_url = school_logo_map.get(cp.ai_selection_program.school_name_cn)
                        program_details = {
                            'id': cp.ai_selection_program.id,
                            'school_name_cn': cp.ai_selection_program.school_name_cn,
                            'school_name_en': cp.ai_selection_program.school_name_en,
                            'school_qs_rank': cp.ai_selection_program.school_qs_rank,
                            'school_region': cp.ai_selection_program.school_region,
                            'program_name_cn': cp.ai_selection_program.program_name_cn,
                            'program_name_en': cp.ai_selection_program.program_name_en,
                            'program_category': cp.ai_selection_program.program_category,
                            'degree': cp.ai_selection_program.degree,
                            'program_website': cp.ai_selection_program.program_website,
                            'gpa_requirements': cp.ai_selection_program.gpa_requirements,
                            'language_requirements': cp.ai_selection_program.language_requirements,
                            'application_requirements': cp.ai_selection_program.application_requirements,
                            'program_tuition': cp.ai_selection_program.program_tuition,
                            'enrollment_time': cp.ai_selection_program.enrollment_time,
                            'program_duration': cp.ai_selection_program.program_duration,
                            'school_logo_url': school_logo_url
                        }
                        cp_dict['program_details'] = program_details
                    client_programs_with_details.append(cp_dict)
                
                client_data[module_name] = client_programs_with_details
            else:
                client_data[module_name] = [item.to_dict() for item in results[module_name]]
        else:
            # 未请求的模块设为空列表
            client_data[module_name] = []

    # 使用 Pydantic 模型创建响应
    return ClientDetailResponse(**client_data)

@router.put("/{client_id}", response_model=ClientResponse, status_code=status.HTTP_200_OK)
async def update_client(
    db: DBSession,
    client_data: ClientUpdate,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    更新客户信息

    Args:
        client_data: 客户更新数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 更新后的客户信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 更新客户信息
    update_data = client_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(client, key, value)

    await db.commit()
    await db.refresh(client)

    return client

@router.delete("/{client_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_client(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    删除客户

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 删除客户
    await db.delete(client)
    await db.commit()

    return None

@router.patch("/{client_id}/archive", response_model=ClientResponse, status_code=status.HTTP_200_OK)
async def toggle_client_archive_status(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    切换客户归档状态

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientResponse: 更新后的客户信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 切换归档状态
    client.is_archived = not client.is_archived

    await db.commit()
    await db.refresh(client)

    return client

# 教育经历相关API
@router.post("/{client_id}/education", response_model=EducationResponse, status_code=status.HTTP_201_CREATED)
async def add_education(
    db: DBSession,
    education_data: EducationCreate,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加教育经历

    Args:
        education_data: 教育经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        EducationResponse: 创建的教育经历信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建教育经历（使用客户真实ID）
    education = Education(**education_data.dict(), client_id=client.id)
    db.add(education)
    await db.commit()
    await db.refresh(education)

    return education

@router.put("/{client_id}/education/{education_id}", response_model=EducationResponse, status_code=status.HTTP_200_OK)
async def update_education(
    db: DBSession,
    education_data: EducationUpdate,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    education_id: int = Path(..., description="教育经历ID"),
):
    """
    更新教育经历

    Args:
        education_data: 教育经历更新数据
        client_id: 客户哈希ID
        education_id: 教育经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        EducationResponse: 更新后的教育经历信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询教育经历是否存在（使用客户真实ID）
    education_query = select(Education).where(
        (Education.id == education_id) &
        (Education.client_id == client.id)
    )
    education_result = await db.execute(education_query)
    education = education_result.scalars().first()

    if not education:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="教育经历不存在或不属于该客户"
        )

    # 更新教育经历信息
    update_data = education_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(education, key, value)

    await db.commit()
    await db.refresh(education)

    return education

@router.delete("/{client_id}/education/{education_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_education(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    education_id: int = Path(..., description="教育经历ID"),
):
    """
    删除教育经历

    Args:
        client_id: 客户哈希ID
        education_id: 教育经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询教育经历是否存在（使用客户真实ID）
    education_query = select(Education).where(
        (Education.id == education_id) &
        (Education.client_id == client.id)
    )
    education_result = await db.execute(education_query)
    education = education_result.scalars().first()

    if not education:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="教育经历不存在或不属于该客户"
        )

    # 删除教育经历
    await db.delete(education)
    await db.commit()

    return None

# 学术经历相关API
@router.post("/{client_id}/academic", response_model=AcademicResponse, status_code=status.HTTP_201_CREATED)
async def add_academic(
    db: DBSession,
    academic_data: AcademicCreate,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加学术经历

    Args:
        academic_data: 学术经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AcademicResponse: 创建的学术经历信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建学术经历（使用客户真实ID）
    academic = Academic(**academic_data.dict(), client_id=client.id)
    db.add(academic)
    await db.commit()
    await db.refresh(academic)

    return academic

@router.put("/{client_id}/academic/{academic_id}", response_model=AcademicResponse, status_code=status.HTTP_200_OK)
async def update_academic(
    db: DBSession,
    academic_data: AcademicUpdate,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    academic_id: int = Path(..., description="学术经历ID"),
):
    """
    更新学术经历

    Args:
        academic_data: 学术经历更新数据
        client_id: 客户哈希ID
        academic_id: 学术经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AcademicResponse: 更新后的学术经历信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询学术经历是否存在（使用客户真实ID）
    academic_query = select(Academic).where(
        (Academic.id == academic_id) &
        (Academic.client_id == client.id)
    )
    academic_result = await db.execute(academic_query)
    academic = academic_result.scalars().first()

    if not academic:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="学术经历不存在或不属于该客户"
        )

    # 更新学术经历信息
    update_data = academic_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(academic, key, value)

    await db.commit()
    await db.refresh(academic)

    return academic

@router.delete("/{client_id}/academic/{academic_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_academic(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    academic_id: int = Path(..., description="学术经历ID"),
):
    """
    删除学术经历

    Args:
        client_id: 客户哈希ID
        academic_id: 学术经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询学术经历是否存在（使用客户真实ID）
    academic_query = select(Academic).where(
        (Academic.id == academic_id) &
        (Academic.client_id == client.id)
    )
    academic_result = await db.execute(academic_query)
    academic = academic_result.scalars().first()

    if not academic:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="学术经历不存在或不属于该客户"
        )

    # 删除学术经历
    await db.delete(academic)
    await db.commit()

    return None

# 工作经历相关API
@router.post("/{client_id}/work", response_model=WorkResponse, status_code=status.HTTP_201_CREATED)
async def add_work(
    db: DBSession,
    current_user: CurrentUser,
    work_data: WorkCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加工作经历

    Args:
        work_data: 工作经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        WorkResponse: 创建的工作经历信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建工作经历（使用客户真实ID）
    work = Work(**work_data.dict(), client_id=client.id)
    db.add(work)
    await db.commit()
    await db.refresh(work)

    return work

@router.put("/{client_id}/work/{work_id}", response_model=WorkResponse, status_code=status.HTTP_200_OK)
async def update_work(
    db: DBSession,
    current_user: CurrentUser,
    work_data: WorkUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    work_id: int = Path(..., description="工作经历ID"),
):
    """
    更新工作经历

    Args:
        work_data: 工作经历更新数据
        client_id: 客户哈希ID
        work_id: 工作经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        WorkResponse: 更新后的工作经历信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询工作经历是否存在（使用客户真实ID）
    work_query = select(Work).where(
        (Work.id == work_id) &
        (Work.client_id == client.id)
    )
    work_result = await db.execute(work_query)
    work = work_result.scalars().first()

    if not work:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="工作经历不存在或不属于该客户"
        )

    # 更新工作经历信息
    update_data = work_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(work, key, value)

    await db.commit()
    await db.refresh(work)

    return work

@router.delete("/{client_id}/work/{work_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_work(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    work_id: int = Path(..., description="工作经历ID"),
):
    """
    删除工作经历

    Args:
        client_id: 客户哈希ID
        work_id: 工作经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询工作经历是否存在（使用客户真实ID）
    work_query = select(Work).where(
        (Work.id == work_id) &
        (Work.client_id == client.id)
    )
    work_result = await db.execute(work_query)
    work = work_result.scalars().first()

    if not work:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="工作经历不存在或不属于该客户"
        )

    # 删除工作经历
    await db.delete(work)
    await db.commit()

    return None

# 活动经历相关API
@router.post("/{client_id}/activities", response_model=ActivityResponse, status_code=status.HTTP_201_CREATED)
async def add_activity(
    db: DBSession,
    current_user: CurrentUser,
    activity_data: ActivityCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加活动经历

    Args:
        activity_data: 活动经历数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ActivityResponse: 创建的活动经历信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建活动经历（使用客户真实ID）
    activity = Activity(**activity_data.dict(), client_id=client.id)
    db.add(activity)
    await db.commit()
    await db.refresh(activity)

    return activity

@router.put("/{client_id}/activities/{activity_id}", response_model=ActivityResponse, status_code=status.HTTP_200_OK)
async def update_activity(
    db: DBSession,
    current_user: CurrentUser,
    activity_data: ActivityUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    activity_id: int = Path(..., description="活动经历ID"),
):
    """
    更新活动经历

    Args:
        activity_data: 活动经历更新数据
        client_id: 客户哈希ID
        activity_id: 活动经历ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ActivityResponse: 更新后的活动经历信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询活动经历是否存在（使用客户真实ID）
    activity_query = select(Activity).where(
        (Activity.id == activity_id) &
        (Activity.client_id == client.id)
    )
    activity_result = await db.execute(activity_query)
    activity = activity_result.scalars().first()

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="活动经历不存在或不属于该客户"
        )

    # 更新活动经历信息
    update_data = activity_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(activity, key, value)

    await db.commit()
    await db.refresh(activity)

    return activity

@router.delete("/{client_id}/activities/{activity_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_activity(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    activity_id: int = Path(..., description="活动经历ID"),
):
    """
    删除活动经历

    Args:
        client_id: 客户哈希ID
        activity_id: 活动经历ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询活动经历是否存在（使用客户真实ID）
    activity_query = select(Activity).where(
        (Activity.id == activity_id) &
        (Activity.client_id == client.id)
    )
    activity_result = await db.execute(activity_query)
    activity = activity_result.scalars().first()

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="活动经历不存在或不属于该客户"
        )

    # 删除活动经历
    await db.delete(activity)
    await db.commit()

    return None

# 奖项相关API
@router.post("/{client_id}/awards", response_model=AwardResponse, status_code=status.HTTP_201_CREATED)
async def add_award(
    db: DBSession,
    current_user: CurrentUser,
    award_data: AwardCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加奖项

    Args:
        award_data: 奖项数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AwardResponse: 创建的奖项信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建奖项（使用客户真实ID）
    award = Award(**award_data.dict(), client_id=client.id)
    db.add(award)
    await db.commit()
    await db.refresh(award)

    return award

@router.put("/{client_id}/awards/{award_id}", response_model=AwardResponse, status_code=status.HTTP_200_OK)
async def update_award(
    db: DBSession,
    current_user: CurrentUser,
    award_data: AwardUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    award_id: int = Path(..., description="奖项ID"),
):
    """
    更新奖项

    Args:
        award_data: 奖项更新数据
        client_id: 客户哈希ID
        award_id: 奖项ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        AwardResponse: 更新后的奖项信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询奖项是否存在（使用客户真实ID）
    award_query = select(Award).where(
        (Award.id == award_id) &
        (Award.client_id == client.id)
    )
    award_result = await db.execute(award_query)
    award = award_result.scalars().first()

    if not award:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="奖项不存在或不属于该客户"
        )

    # 更新奖项信息
    update_data = award_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(award, key, value)

    await db.commit()
    await db.refresh(award)

    return award

@router.delete("/{client_id}/awards/{award_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_award(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    award_id: int = Path(..., description="奖项ID"),
):
    """
    删除奖项

    Args:
        client_id: 客户哈希ID
        award_id: 奖项ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询奖项是否存在（使用客户真实ID）
    award_query = select(Award).where(
        (Award.id == award_id) &
        (Award.client_id == client.id)
    )
    award_result = await db.execute(award_query)
    award = award_result.scalars().first()

    if not award:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="奖项不存在或不属于该客户"
        )

    # 删除奖项
    await db.delete(award)
    await db.commit()

    return None

# 技能相关API
@router.post("/{client_id}/skills", response_model=SkillResponse, status_code=status.HTTP_201_CREATED)
async def add_skill(
    db: DBSession,
    current_user: CurrentUser,
    skill_data: SkillCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加技能

    Args:
        skill_data: 技能数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        SkillResponse: 创建的技能信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建技能（使用客户真实ID）
    skill = Skill(**skill_data.dict(), client_id=client.id)
    db.add(skill)
    await db.commit()
    await db.refresh(skill)

    return skill

@router.put("/{client_id}/skills/{skill_id}", response_model=SkillResponse, status_code=status.HTTP_200_OK)
async def update_skill(
    db: DBSession,
    current_user: CurrentUser,
    skill_data: SkillUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    skill_id: int = Path(..., description="技能ID"),
):
    """
    更新技能

    Args:
        skill_data: 技能更新数据
        client_id: 客户哈希ID
        skill_id: 技能ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        SkillResponse: 更新后的技能信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询技能是否存在（使用客户真实ID）
    skill_query = select(Skill).where(
        (Skill.id == skill_id) &
        (Skill.client_id == client.id)
    )
    skill_result = await db.execute(skill_query)
    skill = skill_result.scalars().first()

    if not skill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="技能不存在或不属于该客户"
        )

    # 更新技能信息
    update_data = skill_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(skill, key, value)

    await db.commit()
    await db.refresh(skill)

    return skill

@router.delete("/{client_id}/skills/{skill_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_skill(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    skill_id: int = Path(..., description="技能ID"),
):
    """
    删除技能

    Args:
        client_id: 客户哈希ID
        skill_id: 技能ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询技能是否存在（使用客户真实ID）
    skill_query = select(Skill).where(
        (Skill.id == skill_id) &
        (Skill.client_id == client.id)
    )
    skill_result = await db.execute(skill_query)
    skill = skill_result.scalars().first()

    if not skill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="技能不存在或不属于该客户"
        )

    # 删除技能
    await db.delete(skill)
    await db.commit()

    return None

# 语言成绩相关API
@router.post("/{client_id}/language-scores", response_model=LanguageScoreResponse, status_code=status.HTTP_201_CREATED)
async def add_language_score(
    db: DBSession,
    current_user: CurrentUser,
    language_score_data: LanguageScoreCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加语言成绩

    Args:
        language_score_data: 语言成绩数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        LanguageScoreResponse: 创建的语言成绩信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建语言成绩（使用客户真实ID）
    language_score = LanguageScore(**language_score_data.dict(), client_id=client.id)
    db.add(language_score)
    await db.commit()
    await db.refresh(language_score)

    return language_score

@router.put("/{client_id}/language-scores/{language_score_id}", response_model=LanguageScoreResponse, status_code=status.HTTP_200_OK)
async def update_language_score(
    db: DBSession,
    current_user: CurrentUser,
    language_score_data: LanguageScoreUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    language_score_id: int = Path(..., description="语言成绩ID"),
):
    """
    更新语言成绩

    Args:
        language_score_data: 语言成绩更新数据
        client_id: 客户哈希ID
        language_score_id: 语言成绩ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        LanguageScoreResponse: 更新后的语言成绩信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询语言成绩是否存在（使用客户真实ID）
    language_score_query = select(LanguageScore).where(
        (LanguageScore.id == language_score_id) &
        (LanguageScore.client_id == client.id)
    )
    language_score_result = await db.execute(language_score_query)
    language_score = language_score_result.scalars().first()

    if not language_score:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="语言成绩不存在或不属于该客户"
        )

    # 更新语言成绩信息
    update_data = language_score_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(language_score, key, value)

    await db.commit()
    await db.refresh(language_score)

    return language_score

@router.delete("/{client_id}/language-scores/{language_score_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_language_score(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    language_score_id: int = Path(..., description="语言成绩ID"),
):
    """
    删除语言成绩

    Args:
        client_id: 客户哈希ID
        language_score_id: 语言成绩ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询语言成绩是否存在（使用客户真实ID）
    language_score_query = select(LanguageScore).where(
        (LanguageScore.id == language_score_id) &
        (LanguageScore.client_id == client.id)
    )
    language_score_result = await db.execute(language_score_query)
    language_score = language_score_result.scalars().first()

    if not language_score:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="语言成绩不存在或不属于该客户"
        )

    # 删除语言成绩
    await db.delete(language_score)
    await db.commit()

    return None

# 个人想法相关API
@router.post("/{client_id}/thoughts", response_model=ThoughtResponse, status_code=status.HTTP_201_CREATED)
async def add_thought(
    db: DBSession,
    current_user: CurrentUser,
    thought_data: ThoughtCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加个人想法

    Args:
        thought_data: 个人想法数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ThoughtResponse: 创建的个人想法信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询是否已存在个人想法记录（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    existing_thought = thought_result.scalars().first()

    if existing_thought:
        # 如果已存在，则更新现有记录
        update_data = thought_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(existing_thought, key, value)
        await db.commit()
        await db.refresh(existing_thought)
        return existing_thought
    else:
        # 如果不存在，则创建新记录（使用客户真实ID）
        thought = Thought(**thought_data.dict(), client_id=client.id)
        db.add(thought)
        await db.commit()
        await db.refresh(thought)
        return thought

@router.put("/{client_id}/thoughts", response_model=ThoughtResponse, status_code=status.HTTP_200_OK)
async def update_thought(
    db: DBSession,
    current_user: CurrentUser,
    thought_data: ThoughtUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    更新个人想法

    Args:
        thought_data: 个人想法更新数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ThoughtResponse: 更新后的个人想法信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询个人想法是否存在（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    thought = thought_result.scalars().first()

    if not thought:
        # 如果不存在，则创建新记录（使用客户真实ID）
        thought = Thought(**thought_data.dict(), client_id=client.id)
        db.add(thought)
    else:
        # 如果存在，则更新现有记录
        update_data = thought_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(thought, key, value)

    # 更新客户的modified_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(thought)

    return thought

@router.delete("/{client_id}/thoughts", status_code=status.HTTP_204_NO_CONTENT)
async def delete_thought(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    删除个人想法

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询个人想法是否存在（使用客户真实ID）
    thought_query = select(Thought).where(Thought.client_id == client.id)
    thought_result = await db.execute(thought_query)
    thought = thought_result.scalars().first()

    if not thought:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="个人想法不存在或不属于该客户"
        )

    # 删除个人想法
    await db.delete(thought)
    
    # 更新客户的updated_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()
    
    await db.commit()

    return None

# 背景自定义模块相关API
@router.post("/{client_id}/background-modules", response_model=CustomModuleResponse, status_code=status.HTTP_201_CREATED)
async def add_background_module(
    db: DBSession,
    current_user: CurrentUser,
    module_data: CustomModuleCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加背景自定义模块

    Args:
        module_data: 背景模块数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 创建的背景模块信息
    """
   # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建背景模块（使用客户真实ID）
    background_module = BackgroundCustomModule(**module_data.dict(), client_id=client.id)
    db.add(background_module)
    await db.commit()
    await db.refresh(background_module)

    return background_module

@router.put("/{client_id}/background-modules/{module_id}", response_model=CustomModuleResponse, status_code=status.HTTP_200_OK)
async def update_background_module(
    db: DBSession,
    current_user: CurrentUser,
    module_data: CustomModuleUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
):
    """
    更新背景自定义模块

    Args:
        module_data: 背景模块更新数据
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 更新后的背景模块信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询背景模块是否存在（使用客户真实ID）
    module_query = select(BackgroundCustomModule).where(
        (BackgroundCustomModule.id == module_id) &
        (BackgroundCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    background_module = module_result.scalars().first()

    if not background_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="背景模块不存在或不属于该客户"
        )

    # 更新背景模块信息
    update_data = module_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(background_module, key, value)

    await db.commit()
    await db.refresh(background_module)

    return background_module

@router.delete("/{client_id}/background-modules/{module_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_background_module(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
):
    """
    删除背景自定义模块

    Args:
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话
    """
   # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询背景模块是否存在（使用客户真实ID）
    module_query = select(BackgroundCustomModule).where(
        (BackgroundCustomModule.id == module_id) &
        (BackgroundCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    background_module = module_result.scalars().first()

    if not background_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="背景模块不存在或不属于该客户"
        )

    # 删除背景模块
    await db.delete(background_module)
    await db.commit()

    return None

# 想法自定义模块相关API
@router.post("/{client_id}/thought-modules", response_model=CustomModuleResponse, status_code=status.HTTP_201_CREATED)
async def add_thought_module(
    db: DBSession,
    current_user: CurrentUser,
    module_data: CustomModuleCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加想法自定义模块

    Args:
        module_data: 想法模块数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 创建的想法模块信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 创建想法模块（使用客户真实ID）
    thought_module = ThoughtCustomModule(**module_data.dict(), client_id=client.id)
    db.add(thought_module)
    await db.commit()
    await db.refresh(thought_module)

    return thought_module

@router.put("/{client_id}/thought-modules/{module_id}", response_model=CustomModuleResponse, status_code=status.HTTP_200_OK)
async def update_thought_module(
    db: DBSession,
    current_user: CurrentUser,
    module_data: CustomModuleUpdate,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
):
    """
    更新想法自定义模块

    Args:
        module_data: 想法模块更新数据
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        CustomModuleResponse: 更新后的想法模块信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询想法模块是否存在（使用客户真实ID）
    module_query = select(ThoughtCustomModule).where(
        (ThoughtCustomModule.id == module_id) &
        (ThoughtCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    thought_module = module_result.scalars().first()

    if not thought_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="想法模块不存在或不属于该客户"
        )

    # 更新想法模块信息
    update_data = module_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(thought_module, key, value)
    
    # 更新客户的updated_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()

    await db.commit()
    await db.refresh(thought_module)

    return thought_module

@router.delete("/{client_id}/thought-modules/{module_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_thought_module(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    module_id: int = Path(..., description="模块ID"),
):
    """
    删除想法自定义模块

    Args:
        client_id: 客户哈希ID
        module_id: 模块ID
        current_user: 当前用户
        db: 数据库会话
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询想法模块是否存在（使用客户真实ID）
    module_query = select(ThoughtCustomModule).where(
        (ThoughtCustomModule.id == module_id) &
        (ThoughtCustomModule.client_id == client.id)
    )
    module_result = await db.execute(module_query)
    thought_module = module_result.scalars().first()

    if not thought_module:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="想法模块不存在或不属于该客户"
        )

    # 删除想法模块
    await db.delete(thought_module)
    
    # 更新客户的updated_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()
    
    await db.commit()

    return None

# 定校书相关API
@router.get("/{client_id}/programs", response_model=List[ClientProgramResponse], status_code=status.HTTP_200_OK)
async def get_client_programs(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    获取客户定校书列表 - 优化版本，解决N+1查询问题

    Args:
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[ClientProgramResponse]: 客户定校书列表
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 查询定校书（使用客户真实ID）- 包含项目详情
    program_query = select(ClientProgram).options(
        joinedload(ClientProgram.ai_selection_program)
    ).where(ClientProgram.client_id == client.id)
    program_result = await db.execute(program_query)
    programs = program_result.scalars().all()

    # 批量查询学校logo - 解决N+1查询问题
    from app.ai_selection.db.models import AISelectionAbroadSchool
    school_names = [cp.ai_selection_program.school_name_cn 
                    for cp in programs 
                    if cp.ai_selection_program and cp.ai_selection_program.school_name_cn]
    
    school_logo_map = {}
    if school_names:
        school_logo_query = select(
            AISelectionAbroadSchool.school_name_cn,
            AISelectionAbroadSchool.school_logo_url
        ).where(AISelectionAbroadSchool.school_name_cn.in_(school_names))
        school_logo_result = await db.execute(school_logo_query)
        school_logo_map = {row[0]: row[1] for row in school_logo_result.fetchall()}

    # 转换为字典并包含项目详情
    programs_with_details = []
    for cp in programs:
        cp_dict = cp.to_dict()
        if cp.ai_selection_program:
            # 使用预查询的logo数据
            school_logo_url = school_logo_map.get(cp.ai_selection_program.school_name_cn)

            # 添加项目详情信息
            program_details = {
                'id': cp.ai_selection_program.id,
                'school_name_cn': cp.ai_selection_program.school_name_cn,
                'school_name_en': cp.ai_selection_program.school_name_en,
                'school_qs_rank': cp.ai_selection_program.school_qs_rank,
                'school_region': cp.ai_selection_program.school_region,
                'program_name_cn': cp.ai_selection_program.program_name_cn,
                'program_name_en': cp.ai_selection_program.program_name_en,
                'program_category': cp.ai_selection_program.program_category,
                'degree': cp.ai_selection_program.degree,
                'program_website': cp.ai_selection_program.program_website,
                'gpa_requirements': cp.ai_selection_program.gpa_requirements,
                'language_requirements': cp.ai_selection_program.language_requirements,
                'application_requirements': cp.ai_selection_program.application_requirements,
                'program_tuition': cp.ai_selection_program.program_tuition,
                'enrollment_time': cp.ai_selection_program.enrollment_time,
                'program_duration': cp.ai_selection_program.program_duration,
                'school_logo_url': school_logo_url
            }
            cp_dict['program_details'] = program_details
        programs_with_details.append(cp_dict)

    return programs_with_details

@router.post("/{client_id}/programs", response_model=ClientProgramResponse, status_code=status.HTTP_201_CREATED)
async def add_client_program(
    db: DBSession,
    current_user: CurrentUser,
    program_data: ClientProgramCreate,
    client_id: str = Path(..., description="客户哈希ID"),
):
    """
    添加客户定校书

    Args:
        program_data: 定校书数据
        client_id: 客户哈希ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        ClientProgramResponse: 创建的定校书信息
    """
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    # 检查是否已经添加过这个项目
    existing_query = select(ClientProgram).where(
        ClientProgram.client_id == client.id,
        ClientProgram.program_id == program_data.program_id
    )
    existing_result = await db.execute(existing_query)
    existing_program = existing_result.scalars().first()

    if existing_program:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该项目已添加到定校书中"
        )

    # 创建定校书记录（使用客户真实ID）
    client_program = ClientProgram(**program_data.dict(), client_id=client.id)
    db.add(client_program)
    await db.commit()
    await db.refresh(client_program)

    return client_program

@router.delete("/{client_id}/programs/{program_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_client_program(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
    program_id: int = Path(..., description="项目ID"),
):
    """
    从定校书中移除项目

    Args:
        client_id: 客户哈希ID
        program_id: 项目ID
        current_user: 当前用户
        db: 数据库会话
    """
    print(f"删除定校书项目请求: client_id={client_id}, program_id={program_id}")
    
    # 验证客户权限
    client = await verify_client_ownership(db, client_id, current_user)

    print(f"找到客户: id={client.id}, name={client.name}")

    # 查询定校书记录
    program_query = select(ClientProgram).where(
        ClientProgram.client_id == client.id,
        ClientProgram.program_id == program_id
    )
    program_result = await db.execute(program_query)
    client_program = program_result.scalars().first()

    if not client_program:
        print(f"定校书记录不存在: client_id={client.id}, program_id={program_id}")
        # 查询该客户的所有定校书记录用于调试
        all_programs_query = select(ClientProgram).where(ClientProgram.client_id == client.id)
        all_programs_result = await db.execute(all_programs_query)
        all_programs = all_programs_result.scalars().all()
        print(f"该客户现有定校书记录: {[cp.program_id for cp in all_programs]}")
        
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="定校书记录不存在"
        )

    print(f"找到定校书记录: id={client_program.id}, program_id={client_program.program_id}")

    # 删除定校书记录
    await db.delete(client_program)
    
    # 更新客户的updated_at时间
    from datetime import datetime
    client.updated_at = datetime.utcnow()
    
    await db.commit()

    print(f"成功删除定校书记录: client_id={client.id}, program_id={program_id}")
    return None

@router.get("/{client_id}/documents", response_model=List[ClientDocumentSummary])
async def get_client_documents(
    client_id: str,
    db: DBSession,
    current_user: CurrentUser,
):
    """
    获取客户的文书列表（CV、PS和RL）
    """
    try:
        # 验证客户权限
        client = await verify_client_ownership(db, client_id, current_user)

        # 并行获取CV、PS和RL列表
        cv_query = select(AIWritingCV).where(AIWritingCV.client_id == client.id).order_by(AIWritingCV.created_at.desc())
        ps_query = select(AIWritingPS).where(AIWritingPS.client_id == client.id).order_by(AIWritingPS.created_at.desc())
        rl_query = select(AIWritingRL).where(AIWritingRL.client_id == client.id).order_by(AIWritingRL.created_at.desc())

        # 并行执行查询
        cv_result, ps_result, rl_result = await asyncio.gather(
            db.execute(cv_query),
            db.execute(ps_query),
            db.execute(rl_query)
        )
        
        cvs = cv_result.scalars().all()
        pss = ps_result.scalars().all()
        rls = rl_result.scalars().all()

        documents = []

        # 添加CV文档
        for cv in cvs:
            documents.append({
                "id": cv.id,
                "type": "CV",
                "title": cv.version_name or "未命名CV",
                "target_major": cv.target_major,
                "content_preview": cv.content_markdown[:200] + "..." if cv.content_markdown and len(cv.content_markdown) > 200 else cv.content_markdown or "",
                "created_at": cv.created_at.isoformat() if cv.created_at else None,
                "updated_at": cv.updated_at.isoformat() if cv.updated_at else None
            })

        # 添加PS文档
        for ps in pss:
            documents.append({
                "id": ps.id,
                "type": "PS",
                "title": ps.version_name or "未命名PS",
                "target_major": ps.target_major,
                "content_preview": ps.content_markdown[:200] + "..." if ps.content_markdown and len(ps.content_markdown) > 200 else ps.content_markdown or "",
                "created_at": ps.created_at.isoformat() if ps.created_at else None,
                "updated_at": ps.updated_at.isoformat() if ps.updated_at else None
            })

        # 添加RL文档
        for rl in rls:
            documents.append({
                "id": rl.id,
                "type": "RL",
                "title": rl.version_name or "未命名推荐信",
                "target_major": None,  # RL没有target_major字段
                "recommender_name": rl.recommender_name,
                "content_preview": rl.content_markdown[:200] + "..." if rl.content_markdown and len(rl.content_markdown) > 200 else rl.content_markdown or "",
                "created_at": rl.created_at.isoformat() if rl.created_at else None,
                "updated_at": rl.updated_at.isoformat() if rl.updated_at else None
            })
        
        # 按创建时间排序（最新的在前）
        documents.sort(key=lambda x: x["created_at"] or "", reverse=True)
        
        return documents
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        # logger.error(f"获取客户文书列表失败: {e}") # This line was removed as per the new_code, as the logger is not defined.
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文书列表失败"
        )

# 定校书Excel导出相关接口
@router.post(
    "/{client_id}/school-book/export",
    summary="导出客户定校书Excel文件",
    description="根据客户已添加的目标专业，生成并下载定校书Excel文件",
    status_code=status.HTTP_200_OK
)
async def export_client_school_book(
    db: DBSession,
    current_user: CurrentUser,
    client_id: str = Path(..., description="客户哈希ID"),
) -> StreamingResponse:
    """
    导出客户定校书Excel文件（基于套餐的导出次数限制）

    Args:
        client_id: 客户哈希ID
        db: 数据库会话

    Returns:
        StreamingResponse: Excel文件流式响应

    Raises:
        HTTPException: 当导出次数达到限制或其他错误时
    """
    try:
        # 1. 验证客户权限
        client = await verify_client_ownership(db, client_id, current_user)

        # 2. 检查导出权限（套餐验证 + 次数限制）
        from app.services.export_limit_service import ExportLimitService

        export_permission = await ExportLimitService.check_export_permission(
            db, current_user, client.id
        )

        if not export_permission["can_export"]:
            # 根据不同原因返回不同的错误信息
            reason = export_permission["reason"]

            # 序列化包含datetime的数据，避免JSON序列化错误
            def serialize_datetime_data(data):
                """递归序列化包含datetime的数据"""
                if isinstance(data, dict):
                    return {k: serialize_datetime_data(v) for k, v in data.items()}
                elif isinstance(data, list):
                    return [serialize_datetime_data(item) for item in data]
                elif hasattr(data, 'isoformat'):  # datetime对象
                    return data.isoformat() if data else None
                else:
                    return data

            if reason == "no_package":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail={
                        "error": "no_package",
                        "message": export_permission["message"],
                        "action_required": export_permission["action_required"],
                        "upgrade_suggestion": export_permission["upgrade_suggestion"],
                        "export_info": serialize_datetime_data(export_permission["export_info"])
                    }
                )
            elif reason == "package_expired":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail={
                        "error": "package_expired",
                        "message": export_permission["message"],
                        "action_required": export_permission["action_required"],
                        "package_status": serialize_datetime_data(export_permission["package_status"]),
                        "export_info": serialize_datetime_data(export_permission["export_info"])
                    }
                )
            elif reason == "export_limit_reached":
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail={
                        "error": "export_limit_reached",
                        "message": export_permission["message"],
                        "action_required": export_permission["action_required"],
                        "upgrade_suggestion": export_permission["upgrade_suggestion"],
                        "export_info": serialize_datetime_data(export_permission["export_info"]),
                        "package_status": serialize_datetime_data(export_permission["package_status"])
                    }
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail={
                        "error": "system_error",
                        "message": export_permission["message"]
                    }
                )
        
        # 3. 获取客户的目标专业列表
        programs_query = (
            select(ClientProgram, AISelectionProgram)
            .join(AISelectionProgram, ClientProgram.program_id == AISelectionProgram.id)
            .where(ClientProgram.client_id == client.id)
            .order_by(AISelectionProgram.school_region.asc(), AISelectionProgram.school_qs_rank.asc())
        )

        programs_result = await db.execute(programs_query)
        programs_data = programs_result.fetchall()

        if not programs_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该客户暂无目标专业，无法生成定校书"
            )
        
        # 4. 转换数据格式
        formatted_programs = []
        for client_program, ai_program in programs_data:
            program_dict = {
                'id': str(ai_program.id),
                'school_name_cn': ai_program.school_name_cn,
                'school_name_en': ai_program.school_name_en,
                'school_region': ai_program.school_region,
                'school_qs_rank': ai_program.school_qs_rank,
                'program_name_cn': ai_program.program_name_cn,
                'program_name_en': ai_program.program_name_en,
                'program_direction': ai_program.program_direction,
                'degree': ai_program.degree,
                'program_duration': ai_program.program_duration,
                'program_website': ai_program.program_website,
                'program_tuition': ai_program.program_tuition,
                'faculty': ai_program.faculty,
                'language_requirements': ai_program.language_requirements or '待查询',
                'application_time': ai_program.application_time or '待查询',
                'created_at': client_program.created_at.isoformat() if client_program.created_at else None
            }
            formatted_programs.append(program_dict)
        
        # 4. 使用Excel导出工具生成文件
        exporter = SchoolBookExcelExporter()
        excel_buffer = exporter.export_school_book(
            client_name=client.name,
            programs_data=formatted_programs,
            output_format="xlsx"
        )

        # 5. 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d')
        filename = f"{client.name}_定校书_{timestamp}.xlsx"
        
        logger.info(f"成功为客户 {client.name} 生成定校书Excel文件，包含 {len(formatted_programs)} 个专业")

        # 6. 记录导出使用次数
        record_success, record_message = await ExportLimitService.record_export_usage(
            db=db,
            user=current_user,
            client_id=client.id,
            export_type="school_book",
            description=f"导出客户 {client.name} 的定校书，包含 {len(formatted_programs)} 个专业"
        )

        if not record_success:
            logger.warning(f"导出记录失败: {record_message}")
        else:
            logger.info(f"导出记录成功: 用户{current_user.id}, 客户{client.id}")

        # 7. 获取更新后的导出信息（用于响应头）
        updated_permission = await ExportLimitService.check_export_permission(
            db, current_user, client.id
        )
        export_info = updated_permission.get("export_info", {})

        # 8. 返回文件流
        excel_data = excel_buffer.getvalue()

        def iter_file():
            yield excel_data

        # 对中文文件名进行URL编码处理，确保兼容性
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))

        # 创建备用ASCII文件名
        ascii_filename = f"client_school_book_{timestamp}.xlsx"

        # 构建响应头，包含导出次数信息
        response_headers = {
            "Content-Disposition": f"attachment; filename=\"{ascii_filename}\"; filename*=UTF-8''{encoded_filename}",
            "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "X-Export-Used": str(export_info.get("used_count", 0)),
            "X-Export-Limit": str(export_info.get("limit", 0)),
            "X-Export-Remaining": str(export_info.get("remaining", 0))
        }

        # 如果是无限次数，设置特殊标识
        if export_info.get("is_unlimited", False):
            response_headers["X-Export-Unlimited"] = "true"

        return StreamingResponse(
            content=iter_file(),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers=response_headers
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出定校书Excel失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="导出定校书失败，请稍后重试"
        )


@router.get(
    "/export/statistics",
    summary="获取导出统计信息",
    description="获取当前用户的导出次数统计和套餐信息",
    status_code=status.HTTP_200_OK
)
async def get_export_statistics(
    db: DBSession,
    current_user: CurrentUser
) -> Dict[str, Any]:
    """
    获取用户导出统计信息

    Args:
        db: 数据库会话
        current_user: 当前用户

    Returns:
        Dict: 导出统计信息
    """
    try:
        from app.services.export_limit_service import ExportLimitService

        statistics = await ExportLimitService.get_export_statistics(db, current_user)

        return {
            "success": True,
            "data": statistics,
            "message": "导出统计信息获取成功"
        }

    except Exception as e:
        logger.error(f"获取导出统计信息失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取导出统计信息失败，请稍后重试"
        )


@router.get(
    "/export/permission",
    summary="检查导出权限",
    description="检查当前用户是否可以进行导出操作",
    status_code=status.HTTP_200_OK
)
async def check_export_permission(
    db: DBSession,
    current_user: CurrentUser
) -> Dict[str, Any]:
    """
    检查用户导出权限

    Args:
        db: 数据库会话
        current_user: 当前用户

    Returns:
        Dict: 导出权限检查结果
    """
    try:
        from app.services.export_limit_service import ExportLimitService

        permission = await ExportLimitService.check_export_permission(db, current_user)

        return {
            "success": True,
            "data": permission,
            "message": "导出权限检查完成"
        }

    except Exception as e:
        logger.error(f"检查导出权限失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查导出权限失败，请稍后重试"
        )


@router.get(
    "/school-matching/search",
    status_code=status.HTTP_200_OK,
    summary="选校匹配专用客户搜索",
    description="为选校匹配功能提供的客户搜索API，支持搜索快速建档客户，包含教育信息"
)
async def search_clients_for_school_matching(
    current_user: CurrentUser,
    db: DBSession,
    search: Optional[str] = Query(None, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=100, description="返回结果数量限制"),
    is_archived: Optional[bool] = Query(False, description="是否获取已归档客户")
):
    """
    选校匹配专用客户搜索

    特殊逻辑：
    - 无搜索关键词时：只显示完整档案客户（profile_type=true）
    - 有搜索关键词时：显示所有客户，包括快速建档客户（profile_type=false）
    - 返回数据包含教育信息（学校名称）

    Args:
        search: 搜索关键词
        limit: 返回结果数量限制
        is_archived: 是否获取已归档客户
        current_user: 当前用户
        db: 数据库会话

    Returns:
        List[Dict]: 客户列表数据，包含教育信息
    """
    # 构建查询 - 根据搜索模式决定是否包含快速建档客户，并预加载教育信息
    if search and search.strip():
        # 搜索模式：包含所有客户（完整档案 + 快速建档）
        query = select(Client).options(joinedload(Client.education))
    else:
        # 默认列表模式：只显示完整档案客户
        query = select(Client).options(joinedload(Client.education)).where(
            Client.profile_type == True  # 只显示完整档案客户
        )

    # 应用身份隔离过滤（支持Owner放宽）
    query = await DataIsolationFilter.apply_client_filter_async(query, current_user, db)

    # 根据归档状态筛选
    if is_archived is not None:
        query = query.where(Client.is_archived == is_archived)

    # 根据搜索关键词筛选 - 支持多字段搜索
    if search and search.strip():
        search_term = f"%{search.strip()}%"
        # 使用 OR 条件匹配多个字段
        search_conditions = [
            Client.name.ilike(search_term),           # 姓名
            Client.phone.ilike(search_term),          # 电话
            Client.email.ilike(search_term),          # 邮箱
            Client.id_card.ilike(search_term),        # 身份证（学生ID）
            Client.passport.ilike(search_term),       # 护照（学生ID）
            Client.id_hashed.ilike(search_term),      # 哈希ID（系统学生ID）
            Client.location.ilike(search_term)        # 所在城市
        ]
        # 过滤掉None值的条件
        from sqlalchemy import or_
        query = query.where(or_(*[cond for cond in search_conditions if cond is not None]))

    # 排序和限制
    query = query.order_by(Client.updated_at.desc()).limit(limit)

    # 执行查询
    result = await db.execute(query)
    clients = result.scalars().unique().all()

    # 构建返回数据，包含教育信息
    client_list = []
    for client in clients:
        # 获取第一条教育记录的学校名称
        school_name = None
        if client.education and len(client.education) > 0:
            school_name = client.education[0].school

        client_data = {
            "id_hashed": client.id_hashed,
            "name": client.name,
            "gender": client.gender,
            "phone": client.phone,
            "email": client.email,
            "location": client.location,
            "address": client.address,
            "id_card": client.id_card,
            "passport": client.passport,
            "service_type": client.service_type,
            "profile_type": client.profile_type,
            "is_archived": client.is_archived,
            "created_at": client.created_at,
            "updated_at": client.updated_at,
            "school": school_name  # 添加学校名称字段
        }
        client_list.append(client_data)

    return client_list