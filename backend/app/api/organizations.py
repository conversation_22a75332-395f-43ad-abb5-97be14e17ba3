"""
组织管理 API 路由
提供组织的 CRUD 操作和成员管理功能
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query, Path, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload
from typing import List, Optional, Annotated
import logging
import os
from pathlib import Path as FSPath
import uuid

from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.models.organization import Organization, OrganizationMember
from app.schemas.organization import (
    OrganizationCreate,
    OrganizationUpdate,
    OrganizationResponse,
    OrganizationMemberResponse,
    OrganizationMemberUpdate,
    OrganizationStatsResponse,
    OrganizationOverviewResponse
)
from app.core.organization_permissions import (
    verify_organization_owner,
    verify_organization_member,
    require_personal_identity,
    get_user_organizations,
    check_organization_username_unique
)

# 创建路由器
router = APIRouter(prefix="/organizations", tags=["组织管理"])

# 创建logger
logger = logging.getLogger(__name__)

# 类型别名
DBSession = Annotated[AsyncSession, Depends(get_db)]
CurrentUser = Annotated[User, Depends(get_current_user)]


@router.post("/", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED)
async def create_organization(
    db: DBSession,
    current_user: CurrentUser,
    organization_data: OrganizationCreate
):
    """
    创建组织（仅个人身份可操作）
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_data: 组织创建数据
        
    Returns:
        OrganizationResponse: 创建的组织信息
        
    Raises:
        HTTPException: 各种错误情况
    """
    # 验证用户当前为个人身份（使用统一的身份验证方法）
    from app.core.data_isolation import DataIsolationFilter

    identity_type, organization_id = DataIsolationFilter.get_user_data_scope(current_user)

    if identity_type != 'personal':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只能在个人身份下创建组织"
        )

    print(f"🏢 创建组织 - 用户身份验证: {identity_type} (符合要求)")
    
    # 检查组织名称是否已存在
    existing_org_result = await db.execute(
        select(Organization).where(
            Organization.name == organization_data.name,
            Organization.is_active == True
        )
    )
    if existing_org_result.scalars().first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="组织名称已存在"
        )
    
    try:
        # 创建组织
        new_organization = Organization(
            name=organization_data.name,
            description=organization_data.description,
            owner_user_id=current_user.id
        )
        db.add(new_organization)
        await db.flush()  # 获取组织ID
        
        # 自动创建Owner成员记录
        owner_member = OrganizationMember(
            organization_id=new_organization.id,
            user_id=current_user.id,
            role="owner",
            organization_username=current_user.username  # 使用用户名作为组织用户名
        )
        db.add(owner_member)
        
        await db.commit()
        
        # 返回组织信息
        response_data = new_organization.to_dict()
        response_data['member_count'] = 1
        response_data['current_user_role'] = "owner"
        
        return response_data
        
    except Exception as e:
        await db.rollback()
        logger.error(f"创建组织失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建组织失败，请稍后重试"
        )


@router.get("/", response_model=List[OrganizationResponse])
async def get_user_organizations_list(
    db: DBSession,
    current_user: CurrentUser,
    include_stats: bool = Query(False, description="是否包含统计信息")
):
    """
    获取用户所属的组织列表
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        include_stats: 是否包含统计信息
        
    Returns:
        List[OrganizationResponse]: 组织列表
    """
    # 获取用户的组织成员关系
    memberships = await get_user_organizations(db, current_user)
    
    organizations = []
    for membership in memberships:
        # 获取组织信息
        org_result = await db.execute(
            select(Organization).where(Organization.id == membership.organization_id)
        )
        organization = org_result.scalars().first()
        
        if organization:
            org_data = organization.to_dict()
            org_data['current_user_role'] = membership.role
            
            if include_stats:
                # 获取成员数量
                member_count_result = await db.execute(
                    select(OrganizationMember).where(
                        OrganizationMember.organization_id == organization.id,
                        OrganizationMember.is_active == True
                    )
                )
                org_data['member_count'] = len(member_count_result.scalars().all())
            
            organizations.append(org_data)
    
    return organizations


@router.get("/{organization_id}", response_model=OrganizationOverviewResponse)
async def get_organization_detail(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID")
):
    """
    获取组织详细信息
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_id: 组织ID
        
    Returns:
        OrganizationOverviewResponse: 组织详细信息
        
    Raises:
        HTTPException: 权限不足或组织不存在
    """
    # 验证用户是组织成员
    membership = await verify_organization_member(db, current_user, organization_id)
    
    # 获取组织信息
    org_result = await db.execute(
        select(Organization).where(Organization.id == organization_id)
    )
    organization = org_result.scalars().first()
    
    # 获取统计信息
    members_result = await db.execute(
        select(OrganizationMember)
        .options(joinedload(OrganizationMember.user))
        .where(
            OrganizationMember.organization_id == organization_id,
            OrganizationMember.is_active == True
        )
        .order_by(OrganizationMember.joined_at.desc())
    )
    members = members_result.scalars().all()
    
    # 获取有效邀请码数量
    from app.models.organization import OrganizationInvitationCode
    active_invitations_result = await db.execute(
        select(OrganizationInvitationCode).where(
            OrganizationInvitationCode.organization_id == organization_id,
            OrganizationInvitationCode.status == "active"
        )
    )
    active_invitations = active_invitations_result.scalars().all()

    # 构建响应数据
    org_data = organization.to_dict()
    org_data['current_user_role'] = membership.role

    # 统计信息
    stats = OrganizationStatsResponse(
        total_members=len(members),
        active_members=len([m for m in members if m.is_active]),
        pending_invitations=len(active_invitations),  # 改为有效邀请码数量
        total_invitations=len(active_invitations)  # 这里可以扩展为所有邀请
    )
    
    # 最近成员（最多5个）
    recent_members = []
    for member in members[:5]:
        member_data = member.to_dict()
        if member.user:
            member_data['user_email'] = member.user.email
            member_data['user_nickname'] = member.user.nickname
            member_data['user_avatar_url'] = member.user.avatar_url
        recent_members.append(member_data)
    
    # 有效邀请码（最多5个）
    active_invitations_data = []
    for invitation in active_invitations[:5]:
        invitation_data = invitation.to_dict()
        invitation_data['organization_name'] = organization.name
        active_invitations_data.append(invitation_data)
    
    return OrganizationOverviewResponse(
        **org_data,
        stats=stats,
        recent_members=recent_members,
        pending_invitations=active_invitations_data
    )


@router.put("/{organization_id}", response_model=OrganizationResponse)
async def update_organization(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID"),
    organization_data: OrganizationUpdate = None
):
    """
    更新组织信息（仅Owner可操作）

    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_id: 组织ID
        organization_data: 组织更新数据

    Returns:
        OrganizationResponse: 更新后的组织信息

    Raises:
        HTTPException: 权限不足或组织不存在
    """
    # 验证用户是组织Owner
    await verify_organization_owner(db, current_user, organization_id)

    # 获取组织
    org_result = await db.execute(
        select(Organization).where(Organization.id == organization_id)
    )
    organization = org_result.scalars().first()

    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="组织不存在"
        )

    # 检查名称唯一性（如果要更新名称）
    if organization_data.name and organization_data.name != organization.name:
        existing_org_result = await db.execute(
            select(Organization).where(
                Organization.name == organization_data.name,
                Organization.is_active == True,
                Organization.id != organization_id
            )
        )
        if existing_org_result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="组织名称已存在"
            )

    try:
        # 更新组织信息
        if organization_data.name is not None:
            organization.name = organization_data.name
        if organization_data.description is not None:
            organization.description = organization_data.description

        await db.commit()

        # 返回更新后的组织信息
        response_data = organization.to_dict()
        response_data['current_user_role'] = "owner"

        return response_data

    except Exception as e:
        await db.rollback()
        logger.error(f"更新组织失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新组织失败，请稍后重试"
        )


@router.delete("/{organization_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_organization(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID")
):
    """
    删除组织（仅Owner可操作）

    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_id: 组织ID

    Raises:
        HTTPException: 权限不足或组织不存在
    """
    # 验证用户是组织Owner
    await verify_organization_owner(db, current_user, organization_id)

    # 获取组织
    org_result = await db.execute(
        select(Organization).where(Organization.id == organization_id)
    )
    organization = org_result.scalars().first()

    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="组织不存在"
        )

    try:
        # 软删除组织（设置为非激活状态）
        organization.is_active = False

        # 同时禁用所有成员关系
        members_result = await db.execute(
            select(OrganizationMember).where(
                OrganizationMember.organization_id == organization_id
            )
        )
        members = members_result.scalars().all()
        for member in members:
            member.is_active = False

        await db.commit()

    except Exception as e:
        await db.rollback()
        logger.error(f"删除组织失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除组织失败，请稍后重试"
        )


# ===============================
# 组织成员管理 API
# ===============================

@router.get("/{organization_id}/members", response_model=List[OrganizationMemberResponse])
async def get_organization_members(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID"),
    skip: int = Query(0, description="分页起始位置"),
    limit: int = Query(100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词（用户名、邮箱、组织用户名）")
):
    """
    获取组织成员列表

    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_id: 组织ID
        skip: 分页起始位置
        limit: 每页数量
        search: 搜索关键词

    Returns:
        List[OrganizationMemberResponse]: 成员列表

    Raises:
        HTTPException: 权限不足或组织不存在
    """
    # 验证用户是组织成员
    await verify_organization_member(db, current_user, organization_id)

    # 构建查询
    query = select(OrganizationMember).options(
        joinedload(OrganizationMember.user)
    ).where(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.is_active == True
    )

    # 添加搜索条件
    if search:
        from sqlalchemy import or_
        query = query.where(
            or_(
                OrganizationMember.organization_username.ilike(f"%{search}%"),
                OrganizationMember.user.has(User.username.ilike(f"%{search}%")),
                OrganizationMember.user.has(User.email.ilike(f"%{search}%")),
                OrganizationMember.user.has(User.nickname.ilike(f"%{search}%"))
            )
        )

    # 添加分页和排序
    query = query.order_by(
        OrganizationMember.role.desc(),  # Owner在前
        OrganizationMember.joined_at.desc()
    ).offset(skip).limit(limit)

    result = await db.execute(query)
    members = result.scalars().all()

    # 构建响应数据
    members_data = []
    for member in members:
        member_data = member.to_dict()
        if member.user:
            member_data['user_email'] = member.user.email
            member_data['user_nickname'] = member.user.nickname
            member_data['user_avatar_url'] = member.user.avatar_url
        members_data.append(member_data)

    return members_data


# ===============================
# 组织 Logo 上传 API
# ===============================

ALLOWED_IMAGE_TYPES = {"image/png", "image/jpeg", "image/jpg", "image/svg+xml"}
MAX_LOGO_SIZE = 2 * 1024 * 1024  # 2MB


@router.post("/{organization_id}/logo")
async def upload_organization_logo(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID"),
    file: UploadFile = File(..., description="组织Logo文件")
):
    """
    上传并更新组织Logo（仅Owner可操作）。

    - 验证：组织Owner、文件类型与大小
    - 存储：本地目录 backend/static/uploads/org_logos/
    - 返回：上传后的访问地址 logo_url
    """
    # 权限校验
    await verify_organization_owner(db, current_user, organization_id)

    # 基本校验
    if file.content_type not in ALLOWED_IMAGE_TYPES:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="不支持的图片格式。仅支持 PNG/JPG/JPEG/SVG")

    # 读取并校验大小
    data = await file.read()
    if not data:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="上传文件不能为空")
    if len(data) > MAX_LOGO_SIZE:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="图片大小不能超过2MB")

    # 组织存储目录
    base_static_dir = FSPath(__file__).resolve().parent.parent.parent / "static"
    upload_dir = base_static_dir / "uploads" / "org_logos"
    upload_dir.mkdir(parents=True, exist_ok=True)

    # 生成安全文件名
    ext_map = {
        "image/png": "png",
        "image/jpeg": "jpg",
        "image/jpg": "jpg",
        "image/svg+xml": "svg",
    }
    ext = ext_map.get(file.content_type, "bin")
    filename = f"org_{organization_id}_{uuid.uuid4().hex[:12]}.{ext}"
    file_path = upload_dir / filename

    try:
        # 写入文件
        with open(file_path, "wb") as f:
            f.write(data)

        # 更新数据库字段
        org_result = await db.execute(select(Organization).where(Organization.id == organization_id))
        organization = org_result.scalars().first()
        if not organization:
            # 清理写入的文件
            try:
                os.remove(file_path)
            except Exception:
                pass
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="组织不存在")

        public_url = f"/static/uploads/org_logos/{filename}"
        organization.logo_url = public_url
        await db.commit()

        return {"success": True, "logo_url": public_url}
    except HTTPException:
        raise
    except Exception as e:
        # 发生异常时尽量清理文件
        try:
            if file_path.exists():
                os.remove(file_path)
        except Exception:
            pass
        logging.getLogger(__name__).error(f"上传组织Logo失败: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="上传失败，请稍后重试")


@router.put("/{organization_id}/members/{user_id}", response_model=OrganizationMemberResponse)
async def update_organization_member(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID"),
    user_id: int = Path(..., description="用户ID"),
    member_data: OrganizationMemberUpdate = None
):
    """
    更新组织成员信息（仅Owner可操作）。

    - Owner 可更新任意普通成员的信息
    - Owner 也可以更新自己的成员信息（例如 organization_username）
    - 任何人都不允许修改其它 Owner 的信息
    - Owner 不能将自己设为未激活

    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_id: 组织ID
        user_id: 用户ID
        member_data: 成员更新数据

    Returns:
        OrganizationMemberResponse: 更新后的成员信息

    Raises:
        HTTPException: 权限不足或成员不存在
    """
    # 验证用户是组织Owner
    await verify_organization_owner(db, current_user, organization_id)

    # 获取成员
    member_result = await db.execute(
        select(OrganizationMember).options(
            joinedload(OrganizationMember.user)
        ).where(
            OrganizationMember.organization_id == organization_id,
            OrganizationMember.user_id == user_id
        )
    )
    member = member_result.scalars().first()

    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )

    # 权限限制：禁止修改其他 Owner 的信息；允许 Owner 自己修改自己的信息
    if member.role == "owner" and member.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="不能修改其他组织所有者的信息"
        )

    # 检查组织用户名唯一性
    if member_data.organization_username and member_data.organization_username != member.organization_username:
        is_unique = await check_organization_username_unique(
            db, organization_id, member_data.organization_username, user_id
        )
        if not is_unique:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="组织用户名已存在"
            )

    try:
        # 更新成员信息
        if member_data.organization_username is not None:
            member.organization_username = member_data.organization_username
        if member_data.is_active is not None:
            # 限制：Owner 不允许将自己设为未激活
            if member.role == "owner" and member.user_id == current_user.id and member_data.is_active is False:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能禁用组织所有者账户"
                )
            member.is_active = member_data.is_active

        await db.commit()

        # 返回更新后的成员信息
        member_data = member.to_dict()
        if member.user:
            member_data['user_email'] = member.user.email
            member_data['user_nickname'] = member.user.nickname
            member_data['user_avatar_url'] = member.user.avatar_url

        return member_data

    except Exception as e:
        await db.rollback()
        logger.error(f"更新成员信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新成员信息失败，请稍后重试"
        )


@router.delete("/{organization_id}/members/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_organization_member(
    db: DBSession,
    current_user: CurrentUser,
    organization_id: int = Path(..., description="组织ID"),
    user_id: int = Path(..., description="用户ID")
):
    """
    移除组织成员（仅Owner可操作）

    Args:
        db: 数据库会话
        current_user: 当前用户
        organization_id: 组织ID
        user_id: 用户ID

    Raises:
        HTTPException: 权限不足或成员不存在
    """
    # 验证用户是组织Owner
    await verify_organization_owner(db, current_user, organization_id)

    # 获取成员
    member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == organization_id,
            OrganizationMember.user_id == user_id
        )
    )
    member = member_result.scalars().first()

    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )

    # 不能移除Owner
    if member.role == "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="不能移除组织所有者"
        )

    try:
        # 软删除成员（设置为非激活状态）
        member.is_active = False
        await db.commit()

    except Exception as e:
        await db.rollback()
        logger.error(f"移除成员失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="移除成员失败，请稍后重试"
        )
