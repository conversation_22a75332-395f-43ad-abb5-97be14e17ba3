import asyncio
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from typing import List, Dict, Any

from app.db.database import get_db
from app.models.user import User
from app.core.dependencies import CurrentUser, DBSession
from app.core.cache import cached, get_cache_stats, clear_cache_pattern

# 创建路由器
router = APIRouter(prefix="/dashboard", tags=["仪表盘"])

@cached(ttl=60, key_prefix="dashboard")  # 缓存1分钟
async def _get_dashboard_stats(db: DBSession) -> Dict[str, Any]:
    """
    获取仪表盘统计数据的内部函数（带缓存）
    """
    
    # 并行执行所有统计查询，减少数据库访问次数
    total_users_query = select(func.count()).select_from(User)
    active_users_query = select(func.count()).select_from(User).where(User.is_active == True)
    
    # 并行执行查询
    total_users_result, active_users_result = await asyncio.gather(
        db.execute(total_users_query),
        db.execute(active_users_query)
    )
    
    total_users = total_users_result.scalar()
    active_users = active_users_result.scalar()
    
    return {
        "total_users": total_users,
        "active_users": active_users,
    }

@router.get("/", status_code=status.HTTP_200_OK)
async def get_dashboard_data(current_user: CurrentUser, db: DBSession):
    """
    获取仪表盘数据 - 优化版本，合并查询减少数据库访问，添加缓存
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        Dict: 仪表盘数据
    """
    # 获取缓存的统计数据
    stats = await _get_dashboard_stats(db)
    
    # TODO: 实现获取最近活动的逻辑
    recent_activities = []
    
    return {
        "message": "获取仪表盘数据成功",
        "data": {
            **stats,
            "recent_activities": recent_activities
        }
    }

@router.get("/cache-stats", status_code=status.HTTP_200_OK)
async def get_cache_stats_endpoint(current_user: CurrentUser):
    """
    获取缓存统计信息 (需要认证)
    """
    stats = await get_cache_stats()
    return {
        "message": "缓存统计获取成功",
        "data": stats
    }

@router.post("/clear-cache", status_code=status.HTTP_200_OK)
async def clear_cache_endpoint(
    current_user: CurrentUser,
    pattern: str = "tunshu:cache:*"
):
    """
    清除缓存（管理员功能，需要认证）
    """
    # 🔒 安全检查：只有管理员可以清除缓存
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以执行此操作"
        )
    cleared_count = await clear_cache_pattern(pattern)
    return {
        "message": f"缓存清除成功，共清除 {cleared_count} 个缓存项",
        "data": {
            "cleared_count": cleared_count,
            "pattern": pattern
        }
    } 