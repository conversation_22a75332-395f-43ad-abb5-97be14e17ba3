"""
Token计费相关API端点
提供用户token使用查询和统计功能
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_

from app.core.dependencies import CurrentUser, DBSession
from app.models.token_usage import TokenUsage, UserTokenSummary
from app.services.token_billing import TokenBillingService

router = APIRouter(prefix="/token-billing", tags=["Token计费"])


@router.get("/summary")
async def get_user_token_summary(
    current_user: CurrentUser,
    db: DBSession
):
    """
    获取当前用户的token使用汇总
    
    Returns:
        用户token使用汇总信息
    """
    try:
        summary = await TokenBillingService.get_user_token_summary(current_user.id, db)
        if summary is None:
            raise HTTPException(status_code=404, detail="用户token使用记录不存在")
        
        return {
            "status": "success",
            "data": summary
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取token使用汇总失败: {str(e)}")


@router.get("/usage-history")
async def get_user_token_usage_history(
    current_user: CurrentUser,
    db: DBSession,
    service_type: Optional[str] = Query(None, description="服务类型筛选"),
    days: int = Query(30, description="查询最近多少天的记录", ge=1, le=365),
    limit: int = Query(100, description="返回记录数量限制", ge=1, le=1000)
):
    """
    获取用户的token使用历史记录
    
    Args:
        service_type: 服务类型筛选 (ai_selection, ai_writing等)
        days: 查询最近多少天的记录
        limit: 返回记录数量限制
        
    Returns:
        用户token使用历史记录列表
    """
    try:
        # 计算查询的时间范围
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 构建查询条件
        conditions = [
            TokenUsage.user_id == current_user.id,
            TokenUsage.created_at >= start_date
        ]
        
        if service_type:
            conditions.append(TokenUsage.service_type == service_type)
        
        # 执行查询
        query = (
            select(TokenUsage)
            .where(and_(*conditions))
            .order_by(TokenUsage.created_at.desc())
            .limit(limit)
        )
        
        result = await db.execute(query)
        usage_records = result.scalars().all()
        
        # 转换为字典格式
        records = [record.to_dict() for record in usage_records]
        
        return {
            "status": "success",
            "data": {
                "records": records,
                "total_count": len(records),
                "query_params": {
                    "service_type": service_type,
                    "days": days,
                    "limit": limit
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取token使用历史失败: {str(e)}")


@router.get("/usage-stats")
async def get_user_token_usage_stats(
    current_user: CurrentUser,
    db: DBSession,
    days: int = Query(30, description="统计最近多少天的数据", ge=1, le=365)
):
    """
    获取用户的token使用统计信息
    
    Args:
        days: 统计最近多少天的数据
        
    Returns:
        用户token使用统计信息
    """
    try:
        # 计算查询的时间范围
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 按服务类型统计token使用量
        service_stats_query = (
            select(
                TokenUsage.service_type,
                func.sum(TokenUsage.tokens_consumed).label('total_tokens'),
                func.count(TokenUsage.id).label('request_count'),
                func.avg(TokenUsage.tokens_consumed).label('avg_tokens_per_request')
            )
            .where(
                and_(
                    TokenUsage.user_id == current_user.id,
                    TokenUsage.created_at >= start_date
                )
            )
            .group_by(TokenUsage.service_type)
        )
        
        result = await db.execute(service_stats_query)
        service_stats = result.fetchall()
        
        # 按操作类型统计token使用量
        operation_stats_query = (
            select(
                TokenUsage.operation_type,
                func.sum(TokenUsage.tokens_consumed).label('total_tokens'),
                func.count(TokenUsage.id).label('request_count')
            )
            .where(
                and_(
                    TokenUsage.user_id == current_user.id,
                    TokenUsage.created_at >= start_date
                )
            )
            .group_by(TokenUsage.operation_type)
        )
        
        result = await db.execute(operation_stats_query)
        operation_stats = result.fetchall()
        
        # 按日期统计token使用量（最近7天）
        if days >= 7:
            daily_stats_query = (
                select(
                    func.date(TokenUsage.created_at).label('date'),
                    func.sum(TokenUsage.tokens_consumed).label('total_tokens'),
                    func.count(TokenUsage.id).label('request_count')
                )
                .where(
                    and_(
                        TokenUsage.user_id == current_user.id,
                        TokenUsage.created_at >= datetime.utcnow() - timedelta(days=7)
                    )
                )
                .group_by(func.date(TokenUsage.created_at))
                .order_by(func.date(TokenUsage.created_at))
            )
            
            result = await db.execute(daily_stats_query)
            daily_stats = result.fetchall()
        else:
            daily_stats = []
        
        return {
            "status": "success",
            "data": {
                "service_stats": [
                    {
                        "service_type": row.service_type,
                        "total_tokens": int(row.total_tokens or 0),
                        "request_count": int(row.request_count or 0),
                        "avg_tokens_per_request": round(float(row.avg_tokens_per_request or 0), 2)
                    }
                    for row in service_stats
                ],
                "operation_stats": [
                    {
                        "operation_type": row.operation_type,
                        "total_tokens": int(row.total_tokens or 0),
                        "request_count": int(row.request_count or 0)
                    }
                    for row in operation_stats
                ],
                "daily_stats": [
                    {
                        "date": row.date.isoformat(),
                        "total_tokens": int(row.total_tokens or 0),
                        "request_count": int(row.request_count or 0)
                    }
                    for row in daily_stats
                ],
                "query_params": {
                    "days": days
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取token使用统计失败: {str(e)}")


@router.post("/test-record")
async def test_token_record(
    current_user: CurrentUser,
    tokens: int = Query(..., description="测试记录的token数量", ge=1),
    service_type: str = Query("ai_selection", description="服务类型"),
    operation_type: str = Query("test", description="操作类型")
):
    """
    测试token记录功能（仅用于开发测试）
    
    Args:
        tokens: 测试记录的token数量
        service_type: 服务类型
        operation_type: 操作类型
        
    Returns:
        记录结果
    """
    try:
        success = await TokenBillingService.record_token_usage(
            user_id=current_user.id,
            service_type=service_type,
            operation_type=operation_type,
            tokens_consumed=tokens,
            model_name="test-model"
        )
        
        if success:
            return {
                "status": "success",
                "message": f"成功记录 {tokens} 个token的使用"
            }
        else:
            raise HTTPException(status_code=500, detail="记录token使用失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试token记录失败: {str(e)}")
