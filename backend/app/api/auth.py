from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from datetime import datetime
import httpx
import urllib.parse
import uuid
import logging
import re

from app.core.security import create_access_token, create_refresh_token
from app.db.database import get_db
from app.models.user import User
from app.models.organization import Organization, OrganizationMember, UserLoginSession, PersonalInvitation
from app.schemas.user import UserCreate, UserLogin, Token, UserResponse, RefreshTokenRequest
from app.schemas.organization import (
    UserIdentitiesResponse,
    SwitchIdentityRequest,
    CurrentIdentityResponse,
    UserIdentity,
    IdentityType,
    OrganizationRole
)
from app.core.dependencies import CurrentUser, DBSession
from app.core.cache import cache
from app.core.config import settings
from app.core.permissions import get_user_permissions, has_test_access
from app.core.organization_permissions import get_user_organizations

# 创建路由器
router = APIRouter(prefix="/auth", tags=["认证"])

# 创建logger
logger = logging.getLogger(__name__)

@router.post("/test", status_code=status.HTTP_200_OK)
async def test(current_user: CurrentUser):
    """
    测试 API 是否正常运行 (需要认证)
    """
    return {
        "message": "API is working!",
        "status": "success",
        "user_id": current_user.id
    }

@router.post("/register", response_model=Token, status_code=status.HTTP_201_CREATED)
async def register(user_in: UserCreate, db: DBSession):
    """
    用户注册
    
    Args:
        user_in: 注册用户信息
        db: 数据库会话
        
    Returns:
        Token: 包含访问令牌和刷新令牌的响应
        
    Raises:
        HTTPException: 用户名或邮箱已存在
    """
    # 检查用户名是否已存在
    result = await db.execute(select(User).where(User.username == user_in.username))
    if result.scalars().first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    result = await db.execute(select(User).where(User.email == user_in.email))
    if result.scalars().first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 验证邀请码（如果提供）
    inviter = None
    if user_in.invitation_code:
        inviter_result = await db.execute(
            select(User).where(User.invitation_code == user_in.invitation_code)
        )
        inviter = inviter_result.scalars().first()

        if not inviter:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邀请码无效"
            )

        # 检查是否自己邀请自己
        if inviter.username == user_in.username or inviter.email == user_in.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能使用自己的邀请码"
            )

    # 创建新用户
    user = User(
        username=user_in.username,
        email=user_in.email
    )
    user.set_password(user_in.password)

    # 保存到数据库
    db.add(user)
    await db.commit()
    await db.refresh(user)

    # 处理邀请奖励（如果有邀请码）
    if inviter:
        try:
            # 导入邀请处理函数
            from app.api.invitations import process_personal_invitation_reward

            await process_personal_invitation_reward(
                db, inviter, user, user_in.invitation_code
            )

            logging.info(f"用户 {user.username} 通过邀请码 {user_in.invitation_code} 注册成功，已发放邀请奖励")

        except Exception as e:
            # 邀请奖励发放失败不影响注册
            logging.error(f"邀请奖励发放失败: {str(e)}")

    # 生成访问令牌和刷新令牌
    access_token = create_access_token(subject=user.id)
    refresh_token = create_refresh_token(subject=user.id)

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@router.post("/login", response_model=Token)
async def login(db: DBSession, form_data: OAuth2PasswordRequestForm = Depends()):
    """
    用户登录
    
    Args:
        db: 数据库会话
        form_data: 表单数据，包含用户名和密码
        
    Returns:
        Token: 包含访问令牌和刷新令牌的响应
        
    Raises:
        HTTPException: 用户名或密码错误
    """
    # 查找用户
    result = await db.execute(select(User).where(User.username == form_data.username))
    user = result.scalars().first()
    
    # 检查用户是否存在以及密码是否正确
    if not user or not user.check_password(form_data.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户是否被禁用
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户已被禁用"
        )
    
    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    await db.commit()
    
    # 清理用户缓存（因为last_login时间更新了）
    cache_key = f"user:{user.id}"
    await cache.delete(cache_key)
    
    # 登录成功，删除错误尝试计数缓存
    cache_key = f"login_attempts:{form_data.username}"
    await cache.delete(cache_key)
    
    # 生成访问令牌和刷新令牌
    access_token = create_access_token(subject=user.id)
    refresh_token = create_refresh_token(subject=user.id)
    
    # 返回token和用户信息
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "nickname": user.nickname,
            "role": user.role,
            "is_active": user.is_active,
            "openid": user.openid,
            "unionid": user.unionid,
            "wechat_nickname": user.wechat_nickname,
            "avatar_url": user.avatar_url,
            "login_type": user.login_type,
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None
        }
    }

@router.post("/refresh", response_model=Token)
async def refresh_token_endpoint(db: DBSession, current_user: CurrentUser):
    """
    刷新令牌 - 保持原有接口兼容性

    Args:
        db: 数据库会话
        current_user: 当前用户

    Returns:
        Token: 包含新的访问令牌的响应
    """
    # 🔥 修复：从当前用户token中提取身份信息，保持身份一致性
    current_identity_type = getattr(current_user, '_current_identity_type', 'personal')
    current_organization_id = getattr(current_user, '_current_organization_id', None)
    current_organization_role = getattr(current_user, '_current_organization_role', None)
    
    # 生成新的访问令牌，保持原有身份信息
    access_token = create_access_token(
        subject=current_user.id,
        identity_type=current_identity_type,
        organization_id=current_organization_id,
        organization_role=current_organization_role
    )

    return {
        "access_token": access_token,
        "refresh_token": create_refresh_token(
            subject=current_user.id,
            identity_type=current_identity_type,
            organization_id=current_organization_id,
            organization_role=current_organization_role
        ),
        "token_type": "bearer"
    }

@router.post("/refresh-token", response_model=Token)
async def refresh_token_with_body(db: DBSession, data: RefreshTokenRequest):
    """
    使用refresh_token刷新访问令牌

    Args:
        db: 数据库会话
        data: 包含refresh_token的请求数据

    Returns:
        Token: 包含新的访问令牌的响应

    Raises:
        HTTPException: 刷新令牌无效或过期
    """
    # 导入JWT相关模块
    from jose import jwt, JWTError

    refresh_token = data.refresh_token
    if not refresh_token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少refresh_token"
        )

    try:
        # 验证refresh_token
        payload = jwt.decode(
            refresh_token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的refresh_token"
            )

        # 检查用户是否存在
        result = await db.execute(select(User).where(User.id == int(user_id)))
        user = result.scalars().first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在"
            )

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="账户已被禁用"
            )

        # 🔥 修复：从原始refresh_token中提取身份信息，保持身份一致性
        identity_type = payload.get("identity_type", "personal")
        organization_id = payload.get("organization_id")
        organization_role = payload.get("organization_role")
        
        # 生成新的访问令牌和刷新令牌，保持原有身份信息
        access_token = create_access_token(
            subject=user.id,
            identity_type=identity_type,
            organization_id=organization_id,
            organization_role=organization_role
        )
        new_refresh_token = create_refresh_token(
            subject=user.id,
            identity_type=identity_type,
            organization_id=organization_id,
            organization_role=organization_role
        )

        return {
            "access_token": access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer"
        }

    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的refresh_token"
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user(current_user: CurrentUser):
    """
    获取当前用户信息

    Args:
        current_user: 当前用户

    Returns:
        UserResponse: 用户信息（包含权限信息）
    """
    # 获取用户权限信息
    user_permissions = get_user_permissions(current_user)
    has_test_permission = has_test_access(current_user)

    # 创建响应数据
    user_data = current_user.to_dict()
    user_data['permissions'] = user_permissions
    user_data['has_test_access'] = has_test_permission

    return user_data

@router.put("/update-profile", response_model=UserResponse)
async def update_profile(current_user: CurrentUser, db: DBSession, data: dict):
    """
    更新用户个人资料

    Args:
        current_user: 当前用户
        db: 数据库会话
        data: 更新数据，支持更新昵称、手机号等

    Returns:
        UserResponse: 更新后的用户信息
    """
    # 更新昵称
    if "nickname" in data:
        current_user.nickname = data["nickname"]



    # 更新邮箱
    if "email" in data:
        email = data["email"].strip() if data["email"] else None

        # 如果提供了邮箱，验证格式并检查唯一性
        if email:
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱格式不正确"
                )

            # 检查邮箱是否已被其他用户使用
            result = await db.execute(
                select(User).where(
                    (User.email == email) & (User.id != current_user.id)
                )
            )
            if result.scalars().first():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该邮箱已被其他用户使用"
                )

        current_user.email = email

    # 更新最后修改时间
    current_user.updated_at = datetime.utcnow()

    # 保存到数据库
    db.add(current_user)
    await db.commit()
    await db.refresh(current_user)

    # 清理用户缓存
    cache_key = f"user:{current_user.id}"
    await cache.delete(cache_key)

    return current_user

@router.post("/change-password", status_code=status.HTTP_200_OK)
async def change_password(
    current_user: CurrentUser, 
    db: DBSession, 
    data: dict
):
    """
    修改密码
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        data: 密码数据，包含old_password和new_password
        
    Returns:
        dict: 操作结果
        
    Raises:
        HTTPException: 旧密码错误或其他错误
    """
    # 验证参数
    if "old_password" not in data or "new_password" not in data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少必要参数"
        )
    
    # 验证旧密码
    if not current_user.check_password(data["old_password"]):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    # 设置新密码
    current_user.set_password(data["new_password"])
    current_user.updated_at = datetime.utcnow()
    
    # 保存到数据库
    db.add(current_user)
    await db.commit()
    
    # 清理用户缓存
    cache_key = f"user:{current_user.id}"
    await cache.delete(cache_key)
    
    return {"message": "密码修改成功"}


# ===== 微信登录相关API =====

# 微信开发者凭据配置
WECHAT_APP_ID = "wx2e38845232935a60"
WECHAT_APP_SECRET = "d81fa9ab53084286ea490ec3455418b0"

# 根据环境自动选择回调地址
import os
ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')

if ENVIRONMENT == 'production':
    WECHAT_REDIRECT_URI = "https://tunshuedu.com/auth/wechat/callback"
else:
    # 开发环境使用Nginx代理地址（端口80），而不是直接的前端端口3000
    WECHAT_REDIRECT_URI = "http://localhost/auth/wechat/callback"

@router.get("/wechat/login-url")
async def get_wechat_login_url():
    """
    生成微信授权登录URL
    
    Returns:
        dict: 包含授权URL和state参数的响应
    """
    # 生成state参数防止CSRF攻击
    state = str(uuid.uuid4())
    
    # 将state参数存储到缓存中，5分钟有效期
    cache_key = f"wechat_state:{state}"
    await cache.set(cache_key, "valid", ttl=300)
    
    # 构建微信授权URL
    params = {
        'appid': WECHAT_APP_ID,
        'redirect_uri': urllib.parse.quote(WECHAT_REDIRECT_URI),
        'response_type': 'code',
        'scope': 'snsapi_login',
        'state': state
    }
    
    auth_url = "https://open.weixin.qq.com/connect/qrconnect?" + urllib.parse.urlencode(params) + "#wechat_redirect"
    
    return {
        "auth_url": auth_url,
        "state": state
    }

@router.get("/wechat/qr-config")
async def get_wechat_qr_config():
    """
    获取内嵌二维码配置参数

    Returns:
        dict: 微信登录JS配置参数

    Raises:
        HTTPException: 当微信配置不完整或缓存操作失败时
    """
    try:
        # 验证微信配置参数
        if not WECHAT_APP_ID or not WECHAT_APP_SECRET or not WECHAT_REDIRECT_URI:
            logger.error("微信登录配置不完整")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="微信登录配置不完整，请联系管理员"
            )

        # 生成state参数防止CSRF攻击
        state = str(uuid.uuid4())
        logger.info(f"生成微信登录state参数: {state}")

        # 将state参数存储到缓存中，5分钟有效期
        cache_key = f"wechat_state:{state}"
        await cache.set(cache_key, "valid", ttl=300)
        logger.info(f"微信登录state已存储到缓存: {cache_key}")

        config = {
            "appid": WECHAT_APP_ID,
            "scope": "snsapi_login",
            "redirect_uri": WECHAT_REDIRECT_URI,
            "state": state,
            "style": "black",
            "href": ""
        }

        logger.info("微信二维码配置生成成功")
        return config

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取微信二维码配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取微信登录配置失败，请稍后重试"
        )

@router.post("/wechat/callback")
async def wechat_callback(db: DBSession, data: dict):
    """
    处理微信授权回调，完成用户登录
    
    Args:
        db: 数据库会话
        data: 回调数据，包含code和state
        
    Returns:
        Token: 包含访问令牌和刷新令牌的响应
        
    Raises:
        HTTPException: 各种错误情况
    """
    code = data.get('code')
    state = data.get('state')
    
    if not code or not state:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少必要参数"
        )
    
    # 验证state参数
    cache_key = f"wechat_state:{state}"
    cached_state = await cache.get(cache_key)
    if not cached_state:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的state参数或已过期"
        )
    
    # 删除已使用的state
    await cache.delete(cache_key)
    
    try:
        # 使用code换取access_token
        async with httpx.AsyncClient() as client:
            token_response = await client.get(
                "https://api.weixin.qq.com/sns/oauth2/access_token",
                params={
                    'appid': WECHAT_APP_ID,
                    'secret': WECHAT_APP_SECRET,
                    'code': code,
                    'grant_type': 'authorization_code'
                }
            )
            
            token_data = token_response.json()
            
            if 'errcode' in token_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"微信授权失败: {token_data.get('errmsg', '未知错误')}"
                )
            
            wechat_access_token = token_data.get('access_token')
            openid = token_data.get('openid')
            unionid = token_data.get('unionid')
            
            if not wechat_access_token or not openid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="获取微信用户标识失败"
                )
            
            # 获取微信用户信息
            user_response = await client.get(
                "https://api.weixin.qq.com/sns/userinfo",
                params={
                    'access_token': wechat_access_token,
                    'openid': openid,
                    'lang': 'zh_CN'
                }
            )
            
            user_data = user_response.json()
            
            if 'errcode' in user_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"获取微信用户信息失败: {user_data.get('errmsg', '未知错误')}"
                )
            
            # 查找或创建用户
            created_new_user = False
            # 首先尝试通过unionid查找（如果有的话）
            user = None
            if unionid:
                result = await db.execute(
                    select(User).where(User.unionid == unionid)
                )
                user = result.scalars().first()
            
            # 如果没有找到，尝试通过openid查找
            if not user:
                result = await db.execute(
                    select(User).where(User.openid == openid)
                )
                user = result.scalars().first()
            
            # 如果用户不存在，创建新用户
            if not user:
                # 生成唯一用户名
                base_username = f"wx_{openid[:8]}"
                username = base_username
                counter = 1
                
                while True:
                    result = await db.execute(
                        select(User).where(User.username == username)
                    )
                    if not result.scalars().first():
                        break
                    username = f"{base_username}_{counter}"
                    counter += 1
                
                # 创建新用户
                user = User(
                    username=username,
                    nickname=user_data.get('nickname', username),
                    email=f"{openid}@wechat.temp",  # 临时邮箱
                    openid=openid,
                    unionid=unionid,
                    wechat_nickname=user_data.get('nickname', ''),
                    avatar_url=user_data.get('headimgurl', ''),
                    login_type="wechat",
                    is_active=True,
                    last_login=datetime.utcnow()
                )
                
                # 微信用户不需要密码，设置一个随机密码
                user.set_password(str(uuid.uuid4()))
                
                db.add(user)
                await db.commit()
                await db.refresh(user)
                created_new_user = True
            else:
                # 更新现有用户信息
                user.nickname = user_data.get('nickname', user.nickname)
                user.wechat_nickname = user_data.get('nickname', user.wechat_nickname)
                user.avatar_url = user_data.get('headimgurl', user.avatar_url)
                user.last_login = datetime.utcnow()
                
                # 更新openid和unionid（如果有变化）
                if unionid and not user.unionid:
                    user.unionid = unionid
                
                await db.commit()
                await db.refresh(user)
            
            # 生成JWT令牌
            access_token = create_access_token(subject=user.id)
            refresh_token = create_refresh_token(subject=user.id)
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "nickname": user.nickname,
                    "role": user.role,
                    "is_active": user.is_active,
                    "openid": user.openid,
                    "unionid": user.unionid,
                    "wechat_nickname": user.wechat_nickname,
                    "avatar_url": user.avatar_url,
                    "login_type": user.login_type,
                    "is_new_user": created_new_user,
                    "last_login": user.last_login.isoformat() if user.last_login else None,
                    "created_at": user.created_at.isoformat() if user.created_at else None,
                    "updated_at": user.updated_at.isoformat() if user.updated_at else None
                }
            }
            
    except httpx.RequestError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"网络请求失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


# ===============================
# 身份管理 API
# ===============================

@router.get("/identities", response_model=UserIdentitiesResponse)
async def get_user_identities(
    db: DBSession,
    current_user: CurrentUser
):
    """
    获取用户可选身份列表

    Args:
        db: 数据库会话
        current_user: 当前用户

    Returns:
        UserIdentitiesResponse: 用户身份列表
    """
    # 获取当前身份信息
    current_identity_type = getattr(current_user, '_current_identity_type', 'personal')
    current_organization_id = getattr(current_user, '_current_organization_id', None)

    # 构建当前身份
    current_identity = UserIdentity(
        identity_type=IdentityType.PERSONAL if current_identity_type == 'personal' else IdentityType.ORGANIZATION,
        organization_id=current_organization_id
    )

    # 如果当前是组织身份，获取组织信息
    if current_identity_type == 'organization' and current_organization_id:
        org_result = await db.execute(
            select(Organization).where(Organization.id == current_organization_id)
        )
        organization = org_result.scalars().first()

        member_result = await db.execute(
            select(OrganizationMember).where(
                OrganizationMember.organization_id == current_organization_id,
                OrganizationMember.user_id == current_user.id
            )
        )
        member = member_result.scalars().first()

        if organization and member:
            current_identity.organization_name = organization.name
            current_identity.organization_role = OrganizationRole.OWNER if member.role == 'owner' else OrganizationRole.MEMBER
            current_identity.organization_username = member.organization_username

    # 获取所有可选身份
    available_identities = [
        UserIdentity(identity_type=IdentityType.PERSONAL)  # 个人身份总是可选
    ]

    # 获取用户所属的组织
    memberships = await get_user_organizations(db, current_user)
    for membership in memberships:
        org_result = await db.execute(
            select(Organization).where(Organization.id == membership.organization_id)
        )
        organization = org_result.scalars().first()

        if organization:
            available_identities.append(UserIdentity(
                identity_type=IdentityType.ORGANIZATION,
                organization_id=organization.id,
                organization_name=organization.name,
                organization_role=OrganizationRole.OWNER if membership.role == 'owner' else OrganizationRole.MEMBER,
                organization_username=membership.organization_username
            ))

    return UserIdentitiesResponse(
        current_identity=current_identity,
        available_identities=available_identities
    )


@router.post("/switch-identity", response_model=Token)
async def switch_identity(
    db: DBSession,
    current_user: CurrentUser,
    switch_request: SwitchIdentityRequest
):
    """
    切换用户身份

    Args:
        db: 数据库会话
        current_user: 当前用户
        switch_request: 切换身份请求

    Returns:
        Token: 新的访问令牌

    Raises:
        HTTPException: 切换失败
    """
    # 验证切换请求
    if switch_request.identity_type == IdentityType.ORGANIZATION:
        if not switch_request.organization_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="切换到组织身份时必须提供组织ID"
            )

        # 验证用户是否为该组织成员
        member_result = await db.execute(
            select(OrganizationMember).where(
                OrganizationMember.organization_id == switch_request.organization_id,
                OrganizationMember.user_id == current_user.id,
                OrganizationMember.is_active == True
            )
        )
        member = member_result.scalars().first()

        if not member:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您不是该组织的成员"
            )

        # 验证组织是否激活
        org_result = await db.execute(
            select(Organization).where(
                Organization.id == switch_request.organization_id,
                Organization.is_active == True
            )
        )
        organization = org_result.scalars().first()

        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="组织不存在或已被禁用"
            )

    try:
        # 更新或创建用户登录会话记录
        session_result = await db.execute(
            select(UserLoginSession).where(UserLoginSession.user_id == current_user.id)
        )
        session = session_result.scalars().first()

        if not session:
            session = UserLoginSession(user_id=current_user.id)
            db.add(session)

        # 保存当前身份为上次身份
        session.last_identity_type = session.current_identity_type
        session.last_organization_id = session.current_organization_id

        # 更新当前身份
        session.current_identity_type = switch_request.identity_type.value
        session.current_organization_id = switch_request.organization_id
        session.session_token = str(uuid.uuid4())

        await db.commit()

        # 生成新的JWT令牌
        if switch_request.identity_type == IdentityType.ORGANIZATION:
            member_result = await db.execute(
                select(OrganizationMember).where(
                    OrganizationMember.organization_id == switch_request.organization_id,
                    OrganizationMember.user_id == current_user.id
                )
            )
            member = member_result.scalars().first()

            access_token = create_access_token(
                subject=current_user.id,
                identity_type="organization",
                organization_id=switch_request.organization_id,
                organization_role=member.role
            )
            refresh_token = create_refresh_token(
                subject=current_user.id,
                identity_type="organization",
                organization_id=switch_request.organization_id,
                organization_role=member.role
            )
        else:
            access_token = create_access_token(
                subject=current_user.id,
                identity_type="personal"
            )
            refresh_token = create_refresh_token(
                subject=current_user.id,
                identity_type="personal"
            )

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }

    except Exception as e:
        await db.rollback()
        logger.error(f"切换身份失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="切换身份失败，请稍后重试"
        )


@router.get("/current-identity", response_model=CurrentIdentityResponse)
async def get_current_identity(
    db: DBSession,
    current_user: CurrentUser
):
    """
    获取当前身份信息

    Args:
        db: 数据库会话
        current_user: 当前用户

    Returns:
        CurrentIdentityResponse: 当前身份信息
    """
    # 获取当前身份信息
    current_identity_type = getattr(current_user, '_current_identity_type', 'personal')
    current_organization_id = getattr(current_user, '_current_organization_id', None)

    response_data = CurrentIdentityResponse(
        user_id=current_user.id,
        identity_type=IdentityType.PERSONAL if current_identity_type == 'personal' else IdentityType.ORGANIZATION,
        organization_id=current_organization_id
    )

    # 如果是组织身份，获取组织详细信息
    if current_identity_type == 'organization' and current_organization_id:
        # 获取组织信息
        org_result = await db.execute(
            select(Organization).where(Organization.id == current_organization_id)
        )
        organization = org_result.scalars().first()

        # 获取成员信息
        member_result = await db.execute(
            select(OrganizationMember).where(
                OrganizationMember.organization_id == current_organization_id,
                OrganizationMember.user_id == current_user.id
            )
        )
        member = member_result.scalars().first()

        if organization and member:
            response_data.organization_name = organization.name
            response_data.organization_role = OrganizationRole.OWNER if member.role == 'owner' else OrganizationRole.MEMBER
            response_data.organization_username = member.organization_username

    # 获取会话令牌
    session_result = await db.execute(
        select(UserLoginSession).where(UserLoginSession.user_id == current_user.id)
    )
    session = session_result.scalars().first()
    if session:
        response_data.session_token = session.session_token

    return response_data
