from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from datetime import datetime, timezone

from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.models.system_notification import SystemNotification
from app.schemas.system_notification import (
    SystemNotificationResponse,
    NotificationListResponse,
    NotificationCountResponse,
    SystemNotificationCreate
)

router = APIRouter()


@router.get("/notifications", response_model=NotificationListResponse)
async def get_system_notifications(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    type_filter: Optional[str] = Query(None, description="通知类型过滤"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取系统通知列表
    支持分页和类型过滤
    系统公告无需认证即可查看
    """
    offset = (page - 1) * page_size
    
    # 构建查询条件 - 只显示激活的、未过期的全员通知
    conditions = [
        SystemNotification.is_active == True,
        or_(
            SystemNotification.expire_at.is_(None),
            SystemNotification.expire_at > datetime.now(timezone.utc)
        ),
        SystemNotification.user_id.is_(None)  # 只显示全员通知
    ]
    
    if type_filter:
        conditions.append(SystemNotification.type == type_filter)
    
    # 查询通知列表
    stmt = (
        select(SystemNotification)
        .where(and_(*conditions))
        .order_by(
            SystemNotification.priority.desc(),
            SystemNotification.created_at.desc()
        )
        .offset(offset)
        .limit(page_size)
    )
    
    result = await db.execute(stmt)
    notifications = result.scalars().all()
    
    # 查询总数量
    count_stmt = select(func.count(SystemNotification.id)).where(and_(*conditions))
    total_result = await db.execute(count_stmt)
    total = total_result.scalar()
    
    return NotificationListResponse(
        notifications=[SystemNotificationResponse.from_orm(n) for n in notifications],
        total=total
    )


@router.get("/notifications/count", response_model=NotificationCountResponse)
async def get_notification_count(
    db: AsyncSession = Depends(get_db)
):
    """
    获取系统通知数量
    系统公告无需认证即可查看
    """
    conditions = [
        SystemNotification.is_active == True,
        or_(
            SystemNotification.expire_at.is_(None),
            SystemNotification.expire_at > datetime.now(timezone.utc)
        ),
        SystemNotification.user_id.is_(None)  # 只显示全员通知
    ]
    
    # 总通知数量
    total_stmt = select(func.count(SystemNotification.id)).where(and_(*conditions))
    total_result = await db.execute(total_stmt)
    total_count = total_result.scalar()
    
    return NotificationCountResponse(
        total_count=total_count
    )


@router.get("/notifications/{notification_id}", response_model=SystemNotificationResponse)
async def get_notification_detail(
    notification_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    获取通知详情
    系统公告无需认证即可查看
    """
    stmt = select(SystemNotification).where(
        and_(
            SystemNotification.id == notification_id,
            SystemNotification.is_active == True,
            SystemNotification.user_id.is_(None)  # 只显示全员通知
        )
    )
    
    result = await db.execute(stmt)
    notification = result.scalar_one_or_none()
    
    if not notification:
        raise HTTPException(status_code=404, detail="通知不存在")
    
    return SystemNotificationResponse.from_orm(notification)


# 管理员功能 - 创建系统通知
@router.post("/admin/notifications", response_model=SystemNotificationResponse)
async def create_system_notification(
    notification: SystemNotificationCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建系统通知（管理员功能）
    """
    # 检查管理员权限
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="权限不足，需要管理员权限")
    
    # 创建全员通知
    db_notification = SystemNotification(
        title=notification.title,
        content=notification.content,
        type=notification.type,
        priority=notification.priority,
        user_id=None,  # 全员通知
        is_active=notification.is_active,
        expire_at=notification.expire_at,
        action_data=notification.action_data
    )
    
    db.add(db_notification)
    await db.commit()
    await db.refresh(db_notification)
    
    return SystemNotificationResponse.from_orm(db_notification)


@router.get("/admin/notifications")
async def get_admin_notifications(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取系统通知列表（管理员功能）
    """
    # 检查管理员权限
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="权限不足，需要管理员权限")
    
    offset = (page - 1) * page_size
    
    # 查询所有通知（包括已删除的）
    stmt = (
        select(SystemNotification)
        .where(SystemNotification.user_id.is_(None))  # 只显示全员通知
        .order_by(SystemNotification.created_at.desc())
        .offset(offset)
        .limit(page_size)
    )
    
    result = await db.execute(stmt)
    notifications = result.scalars().all()
    
    # 查询总数量
    count_stmt = select(func.count(SystemNotification.id)).where(SystemNotification.user_id.is_(None))
    total_result = await db.execute(count_stmt)
    total = total_result.scalar()
    
    return {
        "notifications": [SystemNotificationResponse.from_orm(n) for n in notifications],
        "total": total,
        "has_more": offset + len(notifications) < total
    } 