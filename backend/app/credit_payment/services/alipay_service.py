"""
支付宝支付服务
处理支付宝支付的订单创建、支付回调、退款处理等功能
"""

from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from decimal import Decimal
from alipay import AliPay

from app.credit_payment.config import CreditPaymentConfig
from app.models.credit_payment import PaymentOrder


class AlipayService:
    """支付宝支付服务类"""

    def __init__(self):
        self.config = CreditPaymentConfig.get_alipay_config()
        self.alipay = self._init_alipay()

    def _init_alipay(self) -> AliPay:
        """初始化支付宝客户端"""
        try:
            # 读取私钥
            private_key_path = self.config["private_key_path"]
            with open(private_key_path, "r", encoding="utf-8") as f:
                app_private_key_string = f.read()

            # 读取支付宝公钥
            public_key_path = self.config["alipay_public_key_path"]
            with open(public_key_path, "r", encoding="utf-8") as f:
                alipay_public_key_string = f.read()

            # 初始化 AliPay 对象
            alipay = AliPay(
                appid=self.config["app_id"],
                app_notify_url=None,  # 支付结果异步通知 URL，可根据实际情况设置
                app_private_key_string=app_private_key_string,
                alipay_public_key_string=alipay_public_key_string,
                sign_type="RSA2",  # 签名算法，推荐使用 RSA2
                debug=self.config.get("debug", True)  # 调试模式，True 表示使用沙箱环境
            )

            return alipay

        except Exception as e:
            print(f"初始化支付宝客户端失败: {e}")
            raise Exception(f"支付宝客户端初始化失败: {str(e)}")

    async def create_payment_url(self, order: PaymentOrder) -> Tuple[bool, str, Optional[str]]:
        """
        创建支付宝支付URL (alipay.trade.page.pay)

        Args:
            order: 支付订单

        Returns:
            Tuple[bool, str, Optional[str]]: (是否成功, 消息, 支付URL)
        """
        try:
            # 构造支付标题subject
            if getattr(order, "package_id", None):
                try:
                    pkg = CreditPaymentConfig.get_package_by_id(order.package_id)
                    package_name = pkg.get("name") if isinstance(pkg, dict) else None
                except Exception:
                    package_name = None
                if not package_name:
                    pkg2 = CreditPaymentConfig.RECHARGE_PACKAGES.get(order.package_id)
                    package_name = pkg2.get("name") if isinstance(pkg2, dict) else str(order.package_id)
                subject = f"选校规划-{package_name}"
            else:
                # 组装积分包中文名作为subject
                bundle_name = None
                try:
                    # 优先按金额精确匹配
                    for b in CreditPaymentConfig.CREDIT_BUNDLES:
                        if b.get("amount") == order.amount:
                            bundle_name = b.get("name")
                            break
                    # 回退按积分匹配
                    if not bundle_name:
                        for b in CreditPaymentConfig.CREDIT_BUNDLES:
                            if int(b.get("credits", 0)) == int(order.credits or 0):
                                bundle_name = b.get("name")
                                break
                except Exception:
                    bundle_name = None
                subject = f"积分包-{bundle_name or f'{order.credits}积分'}"

            # 发起支付请求
            order_string = self.alipay.api_alipay_trade_page_pay(
                out_trade_no=order.order_no,
                total_amount=str(order.amount),
                subject=subject,
                return_url=self.config.get("return_url", ""),  # 支付成功后跳转的页面 URL
                notify_url=self.config.get("notify_url", "")   # 支付结果异步通知 URL
            )

            # 生成支付链接
            payment_url = f"{self.config['gateway']}?{order_string}"

            print(f"创建支付URL成功，订单号: {order.order_no}")
            print(f"支付链接：{payment_url}")

            return True, "支付URL创建成功", payment_url

        except Exception as e:
            print(f"创建支付宝支付URL失败: {e}")
            return False, f"创建支付URL失败: {str(e)}", None
    
    async def verify_notify(self, notify_data: Dict[str, str]) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        验证支付宝异步通知

        根据支付宝文档要求：
        1. 验证签名
        2. 核对app_id、out_trade_no、total_amount等参数
        3. 根据trade_status进行后续业务处理

        Args:
            notify_data: 通知数据

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 消息, 验证后的数据)
        """
        try:
            print(f"开始验证支付宝异步通知: {notify_data}")

            # 1. 使用支付宝SDK验证签名
            # 根据支付宝官方文档：排除sign和sign_type参数
            verify_data = {k: v for k, v in notify_data.items() if k not in ['sign', 'sign_type']}
            sign = notify_data.get("sign", "")

            print(f"异步通知验签调试信息:")
            print(f"  原始数据: {notify_data}")
            print(f"  验签数据: {verify_data}")
            print(f"  签名: {sign[:50]}...")

            # 使用支付宝SDK验证签名
            verify_result = self.alipay.verify(verify_data, sign)
            print(f"  验签结果: {verify_result}")

            if not verify_result:
                print(f"异步通知验签失败，完整调试信息:")
                print(f"  App ID: {self.config['app_id']}")
                print(f"  Debug模式: {self.config.get('debug', True)}")
                return False, "签名验证失败", None

            # 2. 验证app_id（必须与请求中的一致）
            if notify_data.get("app_id") != self.config["app_id"]:
                return False, f"app_id不匹配，期望: {self.config['app_id']}, 实际: {notify_data.get('app_id')}", None

            # 3. 验证必要参数是否存在
            required_params = ["out_trade_no", "trade_no", "total_amount", "trade_status"]
            for param in required_params:
                if not notify_data.get(param):
                    return False, f"缺少必要参数: {param}", None

            # 4. 验证交易状态（根据支付宝文档）
            trade_status = notify_data.get("trade_status")
            if trade_status not in ["TRADE_SUCCESS", "TRADE_FINISHED"]:
                return False, f"交易状态异常: {trade_status}", None

            print(f"异步通知验证通过，订单: {notify_data.get('out_trade_no')}, 状态: {trade_status}")

            # 5. 获取订单信息进行进一步验证
            from app.credit_payment.services.payment_service import PaymentService
            from app.db.database import get_db

            order_no = notify_data.get("out_trade_no")
            total_amount = notify_data.get("total_amount")

            # 查询订单信息
            async for db in get_db():
                order = await PaymentService.get_order_by_no(order_no, db)
                if not order:
                    return False, f"订单不存在: {order_no}", None

                # 验证订单金额是否一致
                if str(order.amount) != total_amount:
                    return False, f"订单金额不匹配，期望: {order.amount}, 实际: {total_amount}", None

                break

            print("✓ 支付宝异步通知验证成功")
            return True, "通知验证成功", notify_data

        except Exception as e:
            print(f"验证支付宝异步通知失败: {e}")
            return False, f"通知验证失败: {str(e)}", None

    async def verify_return(self, return_data: Dict[str, str]) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        验证支付宝同步返回

        Args:
            return_data: 同步返回数据

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 消息, 验证后的数据)
        """
        try:
            # 准备验签数据：排除sign和sign_type参数
            verify_data = {k: v for k, v in return_data.items() if k not in ['sign', 'sign_type']}
            sign = return_data.get("sign", "")

            print(f"同步返回验签调试信息:")
            print(f"  原始数据: {return_data}")
            print(f"  验签数据: {verify_data}")
            print(f"  签名: {sign[:50]}...")  # 只显示签名的前50个字符

            # 使用支付宝SDK验证签名
            verify_result = self.alipay.verify(verify_data, sign)
            print(f"  验签结果: {verify_result}")

            if not verify_result:
                print(f"同步返回验签失败，完整调试信息:")
                print(f"  App ID: {self.config['app_id']}")
                print(f"  Debug模式: {self.config.get('debug', True)}")
                return False, "同步返回签名验证失败", None

            # 验证返回参数
            if return_data.get("app_id") != self.config["app_id"]:
                return False, "同步返回app_id不匹配", None

            # 注意：支付宝同步返回可能不包含 trade_status
            # 这是正常的，因为同步返回主要用于页面跳转，不用于业务逻辑判断

            return True, "同步返回验证成功", return_data

        except Exception as e:
            print(f"验证支付宝同步返回失败: {e}")
            return False, f"同步返回验证失败: {str(e)}", None
    
    async def query_trade(self, order_no: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        查询交易状态 (alipay.trade.query)

        Args:
            order_no: 订单号

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 消息, 交易信息)
        """
        try:
            # 查询订单
            result = self.alipay.api_alipay_trade_query(out_trade_no=order_no)

            print(f"查询订单 {order_no} 结果: {result}")

            if result.get("code") == "10000":
                trade_status = result.get("trade_status")
                if trade_status == "TRADE_SUCCESS":
                    print("订单支付成功")
                elif trade_status == "TRADE_CLOSED":
                    print("订单已关闭")
                else:
                    print(f"订单状态：{trade_status}")

                return True, "查询成功", result
            else:
                error_msg = result.get("msg", "查询失败")
                return False, error_msg, None

        except Exception as e:
            print(f"查询支付宝交易失败: {e}")
            return False, f"查询交易失败: {str(e)}", None
    
    async def refund_trade(
        self,
        order_no: str,
        refund_amount: Decimal,
        refund_reason: str = "用户申请退款"
    ) -> Tuple[bool, str, Optional[str]]:
        """
        申请退款 (alipay.trade.refund)

        Args:
            order_no: 订单号
            refund_amount: 退款金额
            refund_reason: 退款原因

        Returns:
            Tuple[bool, str, Optional[str]]: (是否成功, 消息, 退款单号)
        """
        import asyncio

        # 生成退款请求号（用于查询和幂等性控制）
        refund_request_no = f"RF{order_no}{datetime.now().strftime('%H%M%S')}"

        try:
            print(f"开始退款请求，订单号: {order_no}, 金额: {refund_amount}, 请求号: {refund_request_no}")

            # 发起退款请求，使用out_request_no确保幂等性
            result = self.alipay.api_alipay_trade_refund(
                out_trade_no=order_no,
                refund_amount=str(refund_amount),
                refund_reason=refund_reason,
                out_request_no=refund_request_no  # 添加退款请求号
            )

            print(f"退款请求结果: {result}")

            # 检查接口调用是否成功
            if result.get("code") == "10000":
                # 接口调用成功，但需要检查fund_change判断是否真正退款
                # 根据支付宝文档：fund_change=Y表示退款成功，fund_change=N表示本次请求未发生资金变动
                fund_change = result.get("fund_change", "N")

                print(f"退款接口调用成功，fund_change: {fund_change}")

                if fund_change == "Y":
                    # 资金发生变动，退款成功
                    print("✓ 退款成功：本次请求发生了资金变动")
                    return True, "退款成功", refund_request_no
                elif fund_change == "N":
                    # 资金未变动，可能是重复退款或其他原因，需要查询确认
                    print("⚠ 退款接口返回fund_change=N，本次请求未发生资金变动")
                    print("根据支付宝建议，使用退款查询接口确认退款状态...")
                    return await self._handle_fund_change_n(order_no, refund_amount, refund_request_no)
                else:
                    # 未知的fund_change值，按N处理
                    print(f"⚠ 未知的fund_change值: {fund_change}，按N处理")
                    return await self._handle_fund_change_n(order_no, refund_amount, refund_request_no)
            else:
                # 接口调用失败
                error_msg = result.get("msg", "退款失败")
                print(f"退款接口调用失败，错误信息：{error_msg}")
                return False, error_msg, None

        except Exception as e:
            error_str = str(e)
            print(f"支付宝退款异常: {error_str}")

            # 如果是超时错误，尝试查询退款状态确认
            if "timeout" in error_str.lower() or "timed out" in error_str.lower():
                print("检测到超时错误，尝试查询退款状态确认...")
                return await self._handle_refund_timeout(order_no, refund_amount, refund_request_no)
            else:
                return False, f"退款失败: {error_str}", None

    async def _handle_refund_timeout(
        self,
        order_no: str,
        refund_amount: Decimal,
        refund_no: str
    ) -> Tuple[bool, str, Optional[str]]:
        """
        处理退款超时情况，通过查询确认退款状态

        Args:
            order_no: 订单号
            refund_amount: 退款金额
            refund_no: 退款单号

        Returns:
            Tuple[bool, str, Optional[str]]: (是否成功, 消息, 退款单号)
        """
        import asyncio

        max_retries = 3
        retry_delay = 2  # 秒

        for attempt in range(max_retries):
            try:
                print(f"第{attempt + 1}次查询退款状态...")

                # 等待一段时间再查询，给支付宝系统处理时间
                if attempt > 0:
                    await asyncio.sleep(retry_delay)

                # 查询交易状态，看是否有退款记录
                success, _, trade_info = await self.query_trade(order_no)

                if success and trade_info:
                    # 检查是否有退款信息
                    refund_fee = trade_info.get("refund_fee", "0")
                    if refund_fee and float(refund_fee) >= float(refund_amount):
                        print(f"确认退款成功，退款金额: {refund_fee}")
                        return True, "退款成功（超时后确认）", refund_no

                # 尝试查询退款接口（使用退款请求号确保参数一致）
                refund_success, _, refund_info = await self.query_refund(order_no, refund_no)
                if refund_success and refund_info:
                    refund_status = refund_info.get("refund_status")
                    if refund_status == "REFUND_SUCCESS":
                        print("通过退款查询确认退款成功")
                        return True, "退款成功（超时后确认）", refund_no

                print(f"第{attempt + 1}次查询未确认退款成功，继续重试...")

            except Exception as query_e:
                print(f"查询退款状态异常: {query_e}")
                continue

        # 所有重试都失败，返回超时错误但建议用户稍后查询
        return False, "退款请求超时，请稍后查询退款状态确认", refund_no

    async def _handle_fund_change_n(
        self,
        order_no: str,
        refund_amount: Decimal,  # 保留参数以备将来使用
        refund_request_no: str
    ) -> Tuple[bool, str, Optional[str]]:
        """
        处理fund_change=N的情况，通过查询确认退款状态

        Args:
            order_no: 订单号
            refund_amount: 退款金额
            refund_request_no: 退款请求号

        Returns:
            Tuple[bool, str, Optional[str]]: (是否成功, 消息, 退款单号)
        """
        import asyncio

        print("fund_change=N，等待10秒后查询退款状态（按支付宝建议间隔）...")
        await asyncio.sleep(10)  # 按支付宝建议，间隔10秒以上再查询

        try:
            # 使用退款查询接口确认状态，必须使用相同的参数
            success, _, refund_info = await self.query_refund(order_no, refund_request_no)

            if success and refund_info:
                refund_status = refund_info.get("refund_status")

                if refund_status == "REFUND_SUCCESS":
                    print("退款查询确认：退款处理成功")
                    return True, "退款成功（查询确认）", refund_request_no
                else:
                    print(f"退款查询确认：退款未成功，状态: {refund_status}")
                    return False, f"退款未成功，状态: {refund_status}", None
            else:
                print("退款查询失败，无法确认退款状态")
                return False, "退款状态不明确，请稍后手动查询确认", refund_request_no

        except Exception as e:
            print(f"查询退款状态异常: {e}")
            return False, f"退款状态查询失败: {str(e)}", refund_request_no

    async def query_refund(self, order_no: str, out_request_no: str = None) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        查询退款状态 (alipay.trade.fastpay.refund.query)

        注意：退款查询接口的 trade_no、out_trade_no、out_request_no 必须和退款接口保持一致

        Args:
            order_no: 订单号（必须与退款接口的out_trade_no一致）
            out_request_no: 退款请求号（必须与退款接口的out_request_no一致）

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 消息, 退款信息)
        """
        try:
            print(f"查询退款状态，订单号: {order_no}, 退款请求号: {out_request_no}")

            # 构建查询参数，确保与退款接口参数一致
            query_params = {
                "out_trade_no": order_no
            }

            # 如果提供了退款请求号，必须使用（确保与退款接口一致）
            if out_request_no:
                query_params["out_request_no"] = out_request_no

            # 查询退款状态
            result = self.alipay.api_alipay_trade_fastpay_refund_query(**query_params)

            print(f"退款查询结果: {result}")

            # 检查接口调用是否成功
            if result.get("code") == "10000":
                # 接口调用成功，检查退款状态
                refund_status = result.get("refund_status")

                if refund_status:
                    print(f"退款状态：{refund_status}")
                    if refund_status == "REFUND_SUCCESS":
                        print("退款查询确认：退款处理成功")
                    else:
                        print(f"退款查询确认：退款状态为 {refund_status}")
                else:
                    print("退款查询成功，但未返回具体退款信息（可能参数不匹配）")

                return True, "退款查询成功", result
            else:
                error_msg = result.get("msg", "退款查询失败")
                print(f"退款查询接口调用失败，错误信息：{error_msg}")
                return False, error_msg, None

        except Exception as e:
            print(f"查询退款状态异常: {e}")
            return False, f"退款查询失败: {str(e)}", None

    async def close_trade(self, order_no: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        关闭交易 (alipay.trade.close)

        Args:
            order_no: 订单号

        Returns:
            Tuple[bool, str, Optional[Dict]]: (是否成功, 消息, 关闭结果)
        """
        try:
            # 关闭交易
            result = self.alipay.api_alipay_trade_close(out_trade_no=order_no)

            print(f"关闭交易结果: {result}")

            if result.get("code") == "10000":
                print(f"交易 {order_no} 关闭成功")
                return True, "交易关闭成功", result
            else:
                error_msg = result.get("msg", "关闭交易失败")
                print(f"关闭交易失败，错误信息：{error_msg}")
                return False, error_msg, None

        except Exception as e:
            print(f"关闭交易失败: {e}")
            return False, f"关闭交易失败: {str(e)}", None


