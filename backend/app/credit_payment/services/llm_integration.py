"""
LLM积分与底层调用统一基座
包含：
- 底层实际API调用（同步/异步，含并发限制）
- 统一的积分与 token 计费封装
"""

from typing import Optional, Tuple
import asyncio
from concurrent.futures import ThreadPoolExecutor
import requests
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession

from app.credit_payment.services.credit_service import CreditService
from app.services.token_billing import TokenBillingService
from app.ai_selection.config import ALIBABACLOUD_API_KEY_ai_selection


@asynccontextmanager
async def track_llm_usage_with_credits(
    user,
    service_type: str,
    operation_type: str,
    request_id: Optional[str] = None,
    model_name: Optional[str] = None,
    db: Optional[AsyncSession] = None,
    use_credits: bool = True,
    use_tokens: bool = True
):
    """
    LLM使用跟踪上下文管理器，同时支持积分和token计费

    注意：此函数是对 credit_service.py 中 track_credit_consumption 的增强版本，
    增加了对 token 计费的支持，并提供了更多的功能。推荐使用此函数而不是 track_credit_consumption。

    Args:
        user_id: 用户ID
        service_type: 服务类型
        operation_type: 操作类型
        request_id: 请求ID
        model_name: 模型名称
        db: 数据库会话
        use_credits: 是否使用积分计费
        use_tokens: 是否使用token计费

    使用示例:
    async with track_llm_usage_with_credits(user_id, "ai_selection", "user_profile") as tracker:
        response, tokens = await llm_api_call(prompt)
        tracker.record_usage(tokens, len(prompt), len(response))
    """
    
    class DualTracker:
        def __init__(self):
            self.tokens_consumed = 0
            self.prompt_length = None
            self.response_length = None
            self.error_message = None
            self.credit_success = False
            self.token_success = False
        
        def record_usage(self, tokens: int, prompt_len: Optional[int] = None, response_len: Optional[int] = None):
            """记录使用量"""
            self.tokens_consumed = tokens
            self.prompt_length = prompt_len
            self.response_length = response_len
        
        def record_error(self, error: str):
            """记录错误信息"""
            self.error_message = error
    
    tracker = DualTracker()
    
    # 如果使用积分，先检查积分余额（通用逻辑）
    if use_credits:
        # 直接使用传入的 user 对象（已包含多租户上下文）
        if user is None:
            balance = 0
        else:
            if db is None:
                from app.db.database import get_db
                async for session in get_db():
                    balance = await CreditService.get_credit_balance(user, session)
                    break
            else:
                balance = await CreditService.get_credit_balance(user, db)

        if balance <= 0:
            tracker.record_error("积分余额不足")
            yield tracker
            return
    
    try:
        yield tracker
        
        # 处理积分扣减和token记录（在同一个事务中保持原子性）
        if (use_credits or use_tokens) and tracker.tokens_consumed > 0 and not tracker.error_message:
            try:
                if db is None:
                    # 如果没有提供db，创建新的会话并管理事务
                    from app.db.database import get_db
                    async for session in get_db():
                        async with session.begin():
                            # 在同一个事务中处理积分和token
                            if not user:
                                tracker.record_error("用户不存在")
                                break
                            
                            # 处理积分扣减
                            if use_credits:
                                dynamic_credits = CreditService.tokens_to_credits(tracker.tokens_consumed)
                                credit_success, credit_message = await CreditService.consume_credits(
                                    user=user,
                                    credits=dynamic_credits,
                                    service_type=service_type,
                                    operation_type=operation_type,
                                    tokens_consumed=tracker.tokens_consumed,
                                    request_id=request_id,
                                    description=f"{service_type}服务消费{dynamic_credits}积分",
                                    db=session
                                )
                                tracker.credit_success = credit_success
                                if not credit_success:
                                    tracker.record_error(f"积分扣减失败: {credit_message}")
                                    await session.rollback()
                                    break
                            
                            # 处理token记录
                            if use_tokens:
                                token_success = await TokenBillingService.record_token_usage(
                                    user_id=user.id if user else None,
                                    service_type=service_type,
                                    operation_type=operation_type,
                                    tokens_consumed=tracker.tokens_consumed,
                                    request_id=request_id,
                                    model_name=model_name,
                                    prompt_length=tracker.prompt_length,
                                    response_length=tracker.response_length,
                                    error_message=tracker.error_message,
                                    db=session
                                )
                                tracker.token_success = token_success
                                if not token_success:
                                    tracker.record_error("Token记录失败")
                                    await session.rollback()
                                    break
                            
                            # 如果所有操作都成功，提交事务
                            await session.commit()
                            print(f"✅ Request {request_id or 'N/A'} 积分和Token记录事务完成")
                        break
                else:
                    # 使用提供的db会话，在同一个事务中处理积分和token
                    if not user:
                        tracker.record_error("用户不存在")
                        await db.rollback()
                        return
                    
                    # 处理积分扣减
                    if use_credits:
                        dynamic_credits = CreditService.tokens_to_credits(tracker.tokens_consumed)
                        credit_success, credit_message = await CreditService.consume_credits(
                            user=user,
                            credits=dynamic_credits,
                            service_type=service_type,
                            operation_type=operation_type,
                            tokens_consumed=tracker.tokens_consumed,
                            request_id=request_id,
                            description=f"{service_type}服务消费{dynamic_credits}积分",
                            db=db
                        )
                        tracker.credit_success = credit_success
                        if not credit_success:
                            tracker.record_error(f"积分扣减失败: {credit_message}")
                            await db.rollback()
                            return

                    
                    # 处理token记录
                    if use_tokens:
                        token_success = await TokenBillingService.record_token_usage(
                            user_id=user.id if user else None,
                            service_type=service_type,
                            operation_type=operation_type,
                            tokens_consumed=tracker.tokens_consumed,
                            request_id=request_id,
                            model_name=model_name,
                            prompt_length=tracker.prompt_length,
                            response_length=tracker.response_length,
                            error_message=tracker.error_message,
                            db=db
                        )
                        tracker.token_success = token_success
                        if not token_success:
                            tracker.record_error("Token记录失败")
                            await db.rollback()
                            return
                    
                    # 如果所有操作都成功，提交事务
                    await db.commit()
                    print(f"✅ Request {request_id or 'N/A'} 积分和Token记录事务完成")
                        
            except Exception as e:
                print(f"积分和Token记录事务处理失败: {e}")
                tracker.record_error(f"事务处理失败: {str(e)}")
                if db:
                    await db.rollback()
        
        # 处理只有token记录，没有积分扣除的情况（错误记录等）
        elif use_tokens and tracker.error_message and not use_credits:
            try:
                success = await TokenBillingService.record_token_usage(
                    user_id=getattr(user, 'id', None),
                    service_type=service_type,
                    operation_type=operation_type,
                    tokens_consumed=tracker.tokens_consumed,
                    request_id=request_id,
                    model_name=model_name,
                    prompt_length=tracker.prompt_length,
                    response_length=tracker.response_length,
                    error_message=tracker.error_message,
                    db=db
                )
                tracker.token_success = success
            except Exception as e:
                print(f"Token错误记录失败: {e}")
                tracker.token_success = False
            
    except Exception as e:
        tracker.record_error(f"LLM使用跟踪失败: {str(e)}")


############################
# 底层实际API调用（统一下沉） #
############################

# 并发限制与线程池
API_SEMAPHORE = asyncio.Semaphore(5)
_executor = ThreadPoolExecutor(max_workers=5)


def process_text_with_api(prompt: str) -> Tuple[str, int]:
    """
    调用大模型API处理文本，并返回结果（同步版本，仅供线程池调用）
    返回 (回复, 消耗token)
    """
    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"

    payload = {
        "model": "qwen3-4b",
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "stream": False,
        "max_tokens": 512,
        "stop": None,
        "temperature": 0.3,
        "top_p": 0.7,
        "top_k": 50,
        "enable_thinking": False,
        "n": 1,
        "response_format": {"type": "text"}
    }
    headers = {
        "Authorization": f"Bearer {ALIBABACLOUD_API_KEY_ai_selection}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.request("POST", url, json=payload, headers=headers, timeout=(5, 10))
        if response.status_code != 200:
            print(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return "API调用失败，请稍后重试。", 0

        if not response.text.strip():
            print("API返回空响应")
            return "API调用失败，请稍后重试。", 0

        try:
            response_json = response.json()
        except ValueError as json_error:
            print(f"JSON解析失败: {json_error}, 响应内容: {response.text[:200]}")
            return "API调用失败，请稍后重试。", 0

        if not response_json.get("choices"):
            print(f"API响应格式异常: {response_json}")
            return "API调用失败，请稍后重试。", 0

        content = response_json.get("choices")[0].get("message", {}).get("content", "")
        if not content:
            print("API返回空内容")
            return "API调用失败，请稍后重试。", 0

        usage = response_json.get("usage", {})
        total_tokens = usage.get("total_tokens", 0)
        return content, total_tokens

    except requests.exceptions.Timeout:
        print("API调用超时")
        return "API调用超时，请稍后重试。", 0
    except requests.exceptions.ConnectionError:
        print("API连接失败")
        return "网络连接失败，请检查网络后重试。", 0
    except Exception as e:
        print(f"API调用异常: {e}")
        return "API调用失败，请稍后重试。", 0


async def process_text_with_api_async(prompt: str) -> Tuple[str, int]:
    """
    异步调用大模型API并返回 (回复, token)
    """
    async with API_SEMAPHORE:
        loop = asyncio.get_event_loop()
        try:
            result = await loop.run_in_executor(_executor, process_text_with_api, prompt)
            return result
        except Exception as e:
            print(f"异步API调用失败: {e}")
            return "API调用失败，请稍后重试。", 0


async def process_text_with_credit_tracking_async(
    prompt: str,
    user=None,
    service_type: str = "ai_selection",
    operation_type: str = "general",
    request_id: Optional[str] = None,
    model_name: str = "qwen3-1.7b",
    db: Optional[AsyncSession] = None,
    use_credits: bool = True,
    use_tokens: bool = True
) -> Tuple[str, bool, str]:
    """
    带积分和token双重跟踪的LLM调用包装器
    
    Args:
        prompt: 提示词
        user_id: 用户ID
        service_type: 服务类型
        operation_type: 操作类型
        request_id: 请求ID
        model_name: 模型名称
        db: 数据库会话
        use_credits: 是否使用积分计费
        use_tokens: 是否使用token计费
    
    Returns:
        Tuple[str, bool, str]: (模型回复, 是否成功, 错误信息)
    """
    try:
        if user is None:
            # 如果没有用户对象，直接调用API
            response, tokens = await process_text_with_api_async(prompt)
            return response, True, ""
        
        # 使用双重跟踪
        async with track_llm_usage_with_credits(
            user=user,
            service_type=service_type,
            operation_type=operation_type,
            request_id=request_id,
            model_name=model_name,
            db=db,
            use_credits=use_credits,
            use_tokens=use_tokens
        ) as tracker:
            
            # 调用LLM API（本文件内函数）
            response, tokens = await process_text_with_api_async(prompt)
            
            # 记录使用量
            tracker.record_usage(tokens, len(prompt), len(response))
            
            # 检查是否有错误
            if tracker.error_message:
                return response, False, tracker.error_message
            
            return response, True, ""
            
    except Exception as e:
        error_msg = f"LLM调用失败: {str(e)}"
        print(error_msg)
        return "API调用失败，请稍后重试。", False, error_msg


async def process_text_with_credit_and_token_tracking_async(
    prompt: str,
    user=None,
    service_type: str = "ai_selection",
    operation_type: str = "general",
    request_id: Optional[str] = None,
    db: Optional[AsyncSession] = None,
    use_credits: bool = True,
    use_tokens: bool = True
) -> str:
    """
    统一基座：带积分与 token 双计费的异步 LLM 调用。

    说明：
    - 作为所有模块调用 LLM 的唯一入口，确保计费与错误处理一致。
    - 对外保持字符串返回类型与历史兼容；内部通过三元返回处理错误分支。
    - 当 user_id 为空时，不进行积分与 token 计费，仅返回模型结果。
    """
    try:
        # 延迟导入以避免循环依赖
        from app.ai_selection.utils.llm import get_current_model_name

        response, success, error = await process_text_with_credit_tracking_async(
            prompt=prompt,
            user=user,
            service_type=service_type,
            operation_type=operation_type,
            request_id=request_id,
            model_name=get_current_model_name(),
            db=db,
            use_credits=use_credits,
            use_tokens=use_tokens
        )

        if success:
            return response

        # 统一错误文案
        if error and "积分余额不足" in error:
            return "积分余额不足，请充值后再试。如需帮助，请联系客服。"
        return "API调用失败，请稍后重试。"

    except Exception as e:
        print(f"统一基座LLM调用异常: {e}")
        return "API调用失败，请稍后重试。"