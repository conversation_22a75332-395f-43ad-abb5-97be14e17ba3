"""
积分系统核心服务
提供积分账户管理、积分扣减、余额查询等功能
支持多租户数据隔离
"""

import uuid
from typing import Optional, Dict, Any, Tuple, List
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, desc, func


def utc_now():
    """返回UTC时区的当前时间（不带时区信息，适配数据库）"""
    # 使用timezone-aware的datetime，然后移除时区信息以适配数据库
    return datetime.now(timezone.utc).replace(tzinfo=None)

from app.models.credit_payment import UserCreditAccount, CreditTransaction
from app.credit_payment.config import CreditPaymentConfig
from app.models.user import User
from app.db.database import get_db
from app.core.data_isolation import DataIsolationFilter


class CreditService:
    """积分系统核心服务类"""


    
    @staticmethod
    async def get_or_create_credit_account(
        user: User,
        db: Optional[AsyncSession] = None
    ) -> Optional[UserCreditAccount]:
        """
        获取或创建用户积分账户（支持多租户）

        Args:
            user: 用户对象（包含身份信息）
            db: 数据库会话（可选）

        Returns:
            UserCreditAccount: 用户积分账户，如果创建失败返回None
        """
        if db is None:
            async for session in get_db():
                return await CreditService._get_or_create_account_with_session(session, user)
        else:
            return await CreditService._get_or_create_account_with_session(db, user)
    
    @staticmethod
    async def _get_or_create_account_with_session(
        db: AsyncSession,
        user: User
    ) -> Optional[UserCreditAccount]:
        """使用指定的数据库会话获取或创建积分账户（支持多租户）"""
        try:
            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"

            # 检查是否已经在事务中
            in_transaction = db.in_transaction()

            if in_transaction:
                # 如果已经在事务中，使用事务内方法
                return await CreditService._get_or_create_account_in_transaction(db, user, identity_type, organization_id)
            else:
                # 如果不在事务中，开始新事务
                async with db.begin():
                    return await CreditService._get_or_create_account_in_transaction(db, user, identity_type, organization_id)

        except Exception as e:
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"获取或创建{identity_name}积分账户失败: {e}")
            return None

    @staticmethod
    async def _get_or_create_account_in_transaction(
        db: AsyncSession,
        user: User,
        identity_type: str,
        organization_id: Optional[int]
    ) -> Optional[UserCreditAccount]:
        """在现有事务中获取或创建积分账户（不提交事务，支持多租户）"""
        try:
            # 首先尝试获取现有账户（基于用户ID和组织ID）
            query = select(UserCreditAccount).where(
                and_(
                    UserCreditAccount.user_id == user.id,
                    UserCreditAccount.organization_id == organization_id
                )
            )
            result = await db.execute(query)
            account = result.scalars().first()

            if account is None:
                # 如果不存在，创建新账户
                account = UserCreditAccount(
                    user_id=user.id,
                    organization_id=organization_id
                )
                db.add(account)
                await db.flush()  # 使用flush而不是commit，确保ID可用但不提交事务
                await db.refresh(account)

                identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
                print(f"✅ 为用户 {user.id} 创建了新的{identity_name}积分账户")

            return account
        except Exception as e:
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"获取或创建{identity_name}积分账户失败: {e}")
            return None
    
    @staticmethod
    async def get_credit_balance(
        user: User,
        db: Optional[AsyncSession] = None
    ) -> int:
        """
        获取用户积分余额（支持多租户和组织积分池共享）

        Args:
            user: 用户对象（包含身份信息）
            db: 数据库会话（可选）

        Returns:
            int: 积分余额（组织身份下返回组织积分池总额）
        """
        # 获取用户身份信息
        identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

        if identity_type == 'organization' and organization_id:
            # 组织身份：返回组织积分池总额（所有组织成员的积分总和）
            return await CreditService._get_organization_credit_pool(organization_id, db)
        else:
            # 个人身份：返回个人积分余额
            account = await CreditService.get_or_create_credit_account(user, db)
            return account.credit_balance if account else 0

    @staticmethod
    async def _get_organization_credit_pool(
        organization_id: int,
        db: Optional[AsyncSession] = None
    ) -> int:
        """
        获取组织积分池总额（所有组织成员的积分总和）

        Args:
            organization_id: 组织ID
            db: 数据库会话（可选）

        Returns:
            int: 组织积分池总额
        """
        if db is None:
            async for session in get_db():
                return await CreditService._get_organization_credit_pool_with_session(
                    organization_id, session
                )
        else:
            return await CreditService._get_organization_credit_pool_with_session(
                organization_id, db
            )

    @staticmethod
    async def _get_organization_credit_pool_with_session(
        organization_id: int,
        db: AsyncSession
    ) -> int:
        """
        使用指定的数据库会话获取组织积分池总额

        优化策略：
        - 实际场景中，主要积分由组织owner持有
        - 直接查询组织owner的积分余额作为积分池总额
        - 如果owner没有积分，则查询所有成员的积分总和（兼容性）
        """
        try:
            from sqlalchemy import func
            from app.models.organization import Organization

            # 首先获取组织owner的积分账户
            owner_query = (
                select(UserCreditAccount.credit_balance)
                .join(Organization, Organization.owner_user_id == UserCreditAccount.user_id)
                .where(
                    and_(
                        Organization.id == organization_id,
                        UserCreditAccount.organization_id == organization_id,
                        UserCreditAccount.is_active == True
                    )
                )
            )

            owner_result = await db.execute(owner_query)
            owner_credits = owner_result.scalar()

            if owner_credits and owner_credits > 0:
                # 如果owner有积分，直接返回owner的积分作为积分池总额
                print(f"💰 组织积分池(优化): 组织{organization_id} owner积分 {owner_credits}")
                return owner_credits
            else:
                # 如果owner没有积分，查询所有成员的积分总和（兼容性保证）
                query = select(func.sum(UserCreditAccount.credit_balance)).where(
                    and_(
                        UserCreditAccount.organization_id == organization_id,
                        UserCreditAccount.is_active == True
                    )
                )

                result = await db.execute(query)
                total_credits = result.scalar() or 0

                print(f"💰 组织积分池(兼容): 组织{organization_id} 总积分 {total_credits}")
                return total_credits

        except Exception as e:
            print(f"获取组织积分池失败: {e}")
            return 0
    
    @staticmethod
    async def consume_credits(
        user: User,
        credits: int,
        service_type: str,
        operation_type: str,
        tokens_consumed: Optional[int] = None,
        request_id: Optional[str] = None,
        description: Optional[str] = None,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """
        消费积分（支持多租户）

        Args:
            user: 用户对象（包含身份信息）
            credits: 消费的积分数量
            service_type: 服务类型
            operation_type: 操作类型
            tokens_consumed: 消费的tokens数量（可选）
            request_id: 请求ID（可选）
            description: 描述（可选）
            db: 数据库会话（可选）

        Returns:
            Tuple[bool, str]: (是否成功, 错误信息)
        """
        if db is None:
            async for session in get_db():
                return await CreditService._consume_credits_with_session(
                    session, user, credits, service_type, operation_type,
                    tokens_consumed, request_id, description
                )
        else:
            return await CreditService._consume_credits_with_session(
                db, user, credits, service_type, operation_type,
                tokens_consumed, request_id, description
            )
    
    @staticmethod
    async def _consume_credits_with_session(
        db: AsyncSession,
        user: User,
        credits: int,
        service_type: str,
        operation_type: str,
        tokens_consumed: Optional[int] = None,
        request_id: Optional[str] = None,
        description: Optional[str] = None
    ) -> Tuple[bool, str]:
        """使用指定的数据库会话消费积分（支持多租户）"""
        try:
            # 检查是否已经在事务中
            in_transaction = db.in_transaction()

            if in_transaction:
                # 如果已经在事务中，直接执行操作
                return await CreditService._consume_credits_in_existing_transaction(
                    db, user, credits, service_type, operation_type,
                    tokens_consumed, request_id, description
                )
            else:
                # 如果不在事务中，开始新事务
                async with db.begin():
                    return await CreditService._consume_credits_in_existing_transaction(
                        db, user, credits, service_type, operation_type,
                        tokens_consumed, request_id, description
                    )

        except Exception as e:
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"{identity_name}积分消费失败: {e}")
            return False, f"积分消费失败: {str(e)}"

    @staticmethod
    async def _consume_credits_in_existing_transaction(
        db: AsyncSession,
        user: User,
        credits: int,
        service_type: str,
        operation_type: str,
        tokens_consumed: Optional[int] = None,
        request_id: Optional[str] = None,
        description: Optional[str] = None
    ) -> Tuple[bool, str]:
        """在现有事务中消费积分（不开始新事务，支持多租户）"""
        try:
            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"

            # 如果有request_id，先检查是否已经扣费过
            if request_id:
                already_charged = await CreditService._check_request_charged_with_session(
                    db, user, request_id, service_type
                )
                if already_charged:
                    print(f"🔄 Request {request_id} ({identity_name}) 已扣费，跳过重复扣费")
                    return True, "该请求已扣费，跳过重复扣费"

            if identity_type == 'organization' and organization_id:
                # 组织身份：使用组织积分池共享逻辑
                return await CreditService._consume_from_organization_pool(
                    db, user, organization_id, credits, service_type, operation_type,
                    tokens_consumed, request_id, description
                )
            else:
                # 个人身份：使用个人积分账户
                return await CreditService._consume_from_personal_account(
                    db, user, identity_type, organization_id, credits, service_type, operation_type,
                    tokens_consumed, request_id, description
                )

        except Exception as e:
            identity_type, _ = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"{identity_name}积分消费失败: {e}")
            return False, f"积分消费失败: {str(e)}"

    @staticmethod
    async def _consume_from_organization_pool(
        db: AsyncSession,
        user: User,
        organization_id: int,
        credits: int,
        service_type: str,
        operation_type: str,
        tokens_consumed: Optional[int] = None,
        request_id: Optional[str] = None,
        description: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        从组织积分池消费积分（优化版）

        优化策略：
        - 实际场景中，积分主要由组织owner持有
        - 优先从组织owner账户扣费，提高效率
        - 如果owner积分不足，再从其他成员账户补充（兼容性）
        """
        try:
            # 检查用户是否为组织成员
            from app.models.organization import OrganizationMember, Organization

            member_query = select(OrganizationMember).where(
                and_(
                    OrganizationMember.organization_id == organization_id,
                    OrganizationMember.user_id == user.id,
                    OrganizationMember.is_active == True
                )
            )
            member_result = await db.execute(member_query)
            member = member_result.scalars().first()

            if not member:
                return False, "您不是该组织的成员，无法使用组织积分"

            # 优化策略：优先从组织owner的积分账户扣费
            owner_account_query = (
                select(UserCreditAccount)
                .join(Organization, Organization.owner_user_id == UserCreditAccount.user_id)
                .where(
                    and_(
                        Organization.id == organization_id,
                        UserCreditAccount.organization_id == organization_id,
                        UserCreditAccount.is_active == True
                    )
                )
            )

            owner_account_result = await db.execute(owner_account_query)
            owner_account = owner_account_result.scalars().first()

            consumed_accounts = []
            remaining_credits = credits

            # 第一步：尝试从owner账户扣费
            if owner_account and owner_account.credit_balance > 0:
                deduct_from_owner = min(owner_account.credit_balance, remaining_credits)

                # 记录扣费前余额
                balance_before = owner_account.credit_balance

                # 从owner账户扣除积分
                owner_account.credit_balance -= deduct_from_owner
                owner_account.total_credits_consumed += deduct_from_owner
                owner_account.last_consumption_at = utc_now()

                # 记录消费信息
                consumed_accounts.append({
                    'account': owner_account,
                    'deducted': deduct_from_owner,
                    'balance_before': balance_before,
                    'balance_after': owner_account.credit_balance
                })

                remaining_credits -= deduct_from_owner
                print(f"💳 从组织owner账户扣费: {deduct_from_owner}积分，剩余需扣费: {remaining_credits}积分")

            # 第二步：如果owner积分不足，从其他成员账户补充（兼容性保证）
            if remaining_credits > 0:
                other_accounts_query = (
                    select(UserCreditAccount)
                    .where(
                        and_(
                            UserCreditAccount.organization_id == organization_id,
                            UserCreditAccount.credit_balance > 0,
                            UserCreditAccount.is_active == True,
                            UserCreditAccount.id != (owner_account.id if owner_account else -1)  # 排除owner账户
                        )
                    )
                    .order_by(desc(UserCreditAccount.credit_balance))
                )

                other_accounts_result = await db.execute(other_accounts_query)
                other_accounts = other_accounts_result.scalars().all()

                for account in other_accounts:
                    if remaining_credits <= 0:
                        break

                    # 计算从当前账户扣除的积分
                    deduct_from_this_account = min(account.credit_balance, remaining_credits)

                    # 记录扣费前余额
                    balance_before = account.credit_balance

                    # 扣除积分
                    account.credit_balance -= deduct_from_this_account
                    account.total_credits_consumed += deduct_from_this_account
                    account.last_consumption_at = utc_now()

                    # 记录消费信息
                    consumed_accounts.append({
                        'account': account,
                        'deducted': deduct_from_this_account,
                        'balance_before': balance_before,
                        'balance_after': account.credit_balance
                    })

                    remaining_credits -= deduct_from_this_account
                    print(f"💳 从成员账户扣费: {deduct_from_this_account}积分，剩余需扣费: {remaining_credits}积分")

            # 检查是否成功扣费完成
            if remaining_credits > 0:
                # 计算总可用积分
                total_available = sum(info['balance_before'] for info in consumed_accounts)
                return False, f"组织积分池余额不足，当前总余额：{total_available}，需要：{credits}"

            # 为每个被扣费的账户创建交易记录
            for consumed_info in consumed_accounts:
                # 区分实际消费者和被扣费账户
                actual_consumer_id = user.id  # 实际发起消费的用户
                deducted_account_user_id = consumed_info['account'].user_id  # 被扣费的账户所有者

                transaction = CreditTransaction(
                    user_id=actual_consumer_id,  # 记录实际消费者的ID
                    organization_id=organization_id,
                    user_credit_account_id=consumed_info['account'].id,  # 被扣费的账户ID
                    transaction_type="consumption",
                    amount=-consumed_info['deducted'],  # 负数表示消费
                    balance_before=consumed_info['balance_before'],
                    balance_after=consumed_info['balance_after'],
                    service_type=service_type,
                    operation_type=operation_type,
                    request_id=request_id,
                    tokens_consumed=tokens_consumed,
                    conversion_rate=CreditPaymentConfig.TOKENS_PER_CREDIT,
                    description=f"组织积分池消费(优化) - {description or (service_type + '服务消费' + str(consumed_info['deducted']) + '积分')} (消费者: {actual_consumer_id}, 扣费账户: {deducted_account_user_id})"
                )
                db.add(transaction)

            total_consumed = sum(info['deducted'] for info in consumed_accounts)
            owner_consumed = consumed_accounts[0]['deducted'] if consumed_accounts and owner_account and consumed_accounts[0]['account'].id == owner_account.id else 0
            other_consumed = total_consumed - owner_consumed

            success_message = f"组织积分池消费成功，共消费{credits}积分"
            if owner_consumed > 0 and other_consumed > 0:
                success_message += f" (owner账户: {owner_consumed}, 其他账户: {other_consumed})"
            elif owner_consumed > 0:
                success_message += f" (全部从owner账户扣除)"
            else:
                success_message += f" (从成员账户扣除)"

            return True, success_message

        except Exception as e:
            print(f"组织积分池消费失败: {e}")
            return False, f"组织积分池消费失败: {str(e)}"

    @staticmethod
    async def _consume_from_personal_account(
        db: AsyncSession,
        user: User,
        identity_type: str,
        organization_id: Optional[int],
        credits: int,
        service_type: str,
        operation_type: str,
        tokens_consumed: Optional[int] = None,
        request_id: Optional[str] = None,
        description: Optional[str] = None
    ) -> Tuple[bool, str]:
        """从个人积分账户消费积分"""
        try:
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"

            # 获取用户积分账户（在现有事务中）
            account = await CreditService._get_or_create_account_in_transaction(db, user, identity_type, organization_id)
            if not account:
                return False, f"无法获取{identity_name}积分账户"

            # 检查账户是否激活
            if not account.is_active:
                return False, f"{identity_name}积分账户已被禁用"

            # 检查积分余额是否足够
            if account.credit_balance < credits:
                return False, f"{identity_name}积分余额不足，当前余额：{account.credit_balance}，需要：{credits}"

            # 记录交易前余额
            balance_before = account.credit_balance

            # 扣减积分
            account.credit_balance -= credits
            account.total_credits_consumed += credits
            account.last_consumption_at = utc_now()

            # 创建交易记录
            transaction = CreditTransaction(
                user_id=user.id,
                organization_id=organization_id,
                user_credit_account_id=account.id,
                transaction_type="consumption",
                amount=-credits,  # 负数表示消费
                balance_before=balance_before,
                balance_after=account.credit_balance,
                service_type=service_type,
                operation_type=operation_type,
                request_id=request_id,
                tokens_consumed=tokens_consumed,
                conversion_rate=CreditPaymentConfig.TOKENS_PER_CREDIT,
                description=description or f"{service_type}服务消费{credits}积分"
            )
            db.add(transaction)

            # 不提交事务，由调用方负责提交
            return True, f"{identity_name}积分消费成功"

        except Exception as e:
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"{identity_name}积分消费失败: {e}")
            return False, f"{identity_name}积分消费失败: {str(e)}"
    
    @staticmethod
    async def add_credits(
        user: User,
        credits: int,
        transaction_type: str = "recharge",
        order_id: Optional[int] = None,
        description: Optional[str] = None,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """
        增加积分（充值或退款，支持多租户）

        Args:
            user: 用户对象（包含身份信息）
            credits: 增加的积分数量
            transaction_type: 交易类型（recharge, refund）
            order_id: 关联的订单ID（可选）
            description: 描述（可选）
            db: 数据库会话（可选）

        Returns:
            Tuple[bool, str]: (是否成功, 错误信息)
        """
        if db is None:
            async for session in get_db():
                return await CreditService._add_credits_with_session(
                    session, user, credits, transaction_type, order_id, description
                )
        else:
            return await CreditService._add_credits_with_session(
                db, user, credits, transaction_type, order_id, description
            )
    
    @staticmethod
    async def _add_credits_with_session(
        db: AsyncSession,
        user: User,
        credits: int,
        transaction_type: str,
        order_id: Optional[int] = None,
        description: Optional[str] = None
    ) -> Tuple[bool, str]:
        """使用指定的数据库会话增加积分（支持多租户）"""
        try:
            # 检查是否已经在事务中
            in_transaction = db.in_transaction()

            if in_transaction:
                # 如果已经在事务中，直接执行操作
                return await CreditService._add_credits_in_existing_transaction(
                    db, user, credits, transaction_type, order_id, description
                )
            else:
                # 如果不在事务中，开始新事务
                async with db.begin():
                    return await CreditService._add_credits_in_existing_transaction(
                        db, user, credits, transaction_type, order_id, description
                    )

        except Exception as e:
            identity_type, _ = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"{identity_name}积分增加失败: {e}")
            return False, f"积分增加失败: {str(e)}"

    @staticmethod
    async def _add_credits_in_existing_transaction(
        db: AsyncSession,
        user: User,
        credits: int,
        transaction_type: str,
        order_id: Optional[int] = None,
        description: Optional[str] = None
    ) -> Tuple[bool, str]:
        """在现有事务中增加或扣除积分（不开始新事务，支持多租户）"""
        try:
            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"

            # 获取用户积分账户（在现有事务中）
            account = await CreditService._get_or_create_account_in_transaction(db, user, identity_type, organization_id)
            if not account:
                return False, f"无法获取{identity_name}积分账户"

            # 记录交易前余额
            balance_before = account.credit_balance

            # 如果是扣除积分（负数），检查余额是否足够
            if credits < 0 and balance_before < abs(credits):
                return False, f"{identity_name}积分余额不足，当前余额: {balance_before}，需要扣除: {abs(credits)}"

            # 更新积分余额
            account.credit_balance += credits

            # 根据交易类型更新相关字段
            if transaction_type == "recharge" and credits > 0:
                account.total_credits_purchased += credits
                account.last_recharge_at = utc_now()
            elif transaction_type == "refund" and credits < 0:
                # 退款时扣除积分，更新消费统计
                account.total_credits_consumed += abs(credits)
                account.last_consumption_at = utc_now()

            # 创建交易记录
            action_desc = "增加" if credits > 0 else "扣除"
            transaction = CreditTransaction(
                user_id=user.id,
                organization_id=organization_id,
                user_credit_account_id=account.id,
                transaction_type=transaction_type,
                amount=credits,  # 正数表示增加，负数表示扣除
                balance_before=balance_before,
                balance_after=account.credit_balance,
                order_id=order_id,
                description=description or f"{transaction_type}{action_desc}{abs(credits)}积分"
            )
            db.add(transaction)

            # 不提交事务，由调用方负责提交
            action = "增加" if credits > 0 else "扣除"
            return True, f"{identity_name}积分{action}成功，{action}{abs(credits)}积分，当前余额: {account.credit_balance}"

        except Exception as e:
            identity_type, _ = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"{identity_name}积分操作失败: {e}")
            return False, f"积分操作失败: {str(e)}"
    
    @staticmethod
    async def get_credit_summary(
        user: User,
        db: Optional[AsyncSession] = None
    ) -> Optional[Dict[str, Any]]:
        """
        获取用户积分汇总信息（支持多租户）

        Args:
            user: 用户对象（包含身份信息）
            db: 数据库会话（可选）

        Returns:
            Dict: 积分汇总信息，如果用户不存在返回None
        """
        if db is None:
            async for session in get_db():
                return await CreditService._get_summary_with_session(session, user)
        else:
            return await CreditService._get_summary_with_session(db, user)
    
    @staticmethod
    async def _get_summary_with_session(
        db: AsyncSession,
        user: User
    ) -> Optional[Dict[str, Any]]:
        """使用指定的数据库会话获取积分汇总（支持多租户）"""
        try:
            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"

            account = await CreditService._get_or_create_account_with_session(db, user)
            if account:
                summary = account.to_dict()
                summary['identity_type'] = identity_type
                summary['identity_name'] = identity_name
                return summary
            else:
                return {
                    'user_id': user.id,
                    'organization_id': organization_id,
                    'identity_type': identity_type,
                    'identity_name': identity_name,
                    'credit_balance': 0,
                    'total_credits_purchased': 0,
                    'total_credits_consumed': 0,
                    'is_active': True,
                    'last_recharge_at': None,
                    'last_consumption_at': None,
                    'created_at': None,
                    'updated_at': None
                }
        except Exception as e:
            identity_type, _ = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"获取{identity_name}积分汇总失败: {e}")
            return None
    
    @staticmethod
    def generate_request_id() -> str:
        """
        生成唯一的请求ID，用于关联同一次服务的多个操作
        
        Returns:
            str: 唯一的请求ID
        """
        return str(uuid.uuid4())
    
    @staticmethod
    def tokens_to_credits(tokens: int) -> int:
        """将tokens转换为积分"""
        return CreditPaymentConfig.tokens_to_credits(tokens)
    
    @staticmethod
    def credits_to_tokens(credits: int) -> int:
        """将积分转换为tokens"""
        return CreditPaymentConfig.credits_to_tokens(credits)



    @staticmethod
    async def check_request_already_charged(
        user: User,
        request_id: str,
        service_type: str,
        db: Optional[AsyncSession] = None
    ) -> bool:
        """
        检查指定的request_id是否已经扣费过（支持多租户）

        Args:
            user: 用户对象（包含身份信息）
            request_id: 请求ID
            service_type: 服务类型
            db: 数据库会话（可选）

        Returns:
            bool: 如果已经扣费过返回True，否则返回False
        """
        if not request_id:
            return False

        if db is None:
            async for session in get_db():
                return await CreditService._check_request_charged_with_session(
                    session, user, request_id, service_type
                )
        else:
            return await CreditService._check_request_charged_with_session(
                db, user, request_id, service_type
            )

    @staticmethod
    async def _check_request_charged_with_session(
        db: AsyncSession,
        user: User,
        request_id: str,
        service_type: str
    ) -> bool:
        """使用指定的数据库会话检查request_id是否已扣费（支持多租户）"""
        try:
            from sqlalchemy.future import select
            from app.models.credit_payment import CreditTransaction

            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            # 查询是否存在相同request_id的消费记录（不区分operation_type，防止同一请求重复扣费）
            query = select(CreditTransaction).where(
                and_(
                    CreditTransaction.user_id == user.id,
                    CreditTransaction.organization_id == organization_id,
                    CreditTransaction.request_id == request_id,
                    CreditTransaction.service_type == service_type,
                    CreditTransaction.transaction_type == "consumption"
                )
            )

            result = await db.execute(query)
            existing_transaction = result.scalars().first()

            return existing_transaction is not None

        except Exception as e:
            identity_type, _ = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"检查{identity_name}重复扣费时发生错误: {e}")
            # 出错时返回False，允许扣费（保守策略）
            return False

    @staticmethod
    async def get_credit_transactions(
        user: User,
        transaction_type: Optional[str] = None,
        service_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        page_size: int = 20,
        db: Optional[AsyncSession] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取用户积分交易记录列表（支持多租户）

        Args:
            user: 用户对象（包含身份信息）
            transaction_type: 交易类型过滤 (recharge/consumption/refund)
            service_type: 服务类型过滤 (ai_selection/ai_writing/ai_matching)
            start_date: 开始时间过滤
            end_date: 结束时间过滤
            page: 页码
            page_size: 每页数量
            db: 数据库会话（可选）

        Returns:
            Tuple[List[Dict], int]: (交易记录列表, 总记录数)
        """
        if db is None:
            async for session in get_db():
                return await CreditService._get_transactions_with_session(
                    session, user, transaction_type, service_type, start_date, end_date, page, page_size
                )
        else:
            return await CreditService._get_transactions_with_session(
                db, user, transaction_type, service_type, start_date, end_date, page, page_size
            )
    
    @staticmethod
    async def _get_transactions_with_session(
        db: AsyncSession,
        user: User,
        transaction_type: Optional[str],
        service_type: Optional[str], 
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        page: int,
        page_size: int
    ) -> Tuple[List[Dict[str, Any]], int]:
        """使用指定的数据库会话获取积分交易记录"""
        try:
            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            # 构建基础查询
            query = select(CreditTransaction).where(
                and_(
                    CreditTransaction.user_id == user.id,
                    CreditTransaction.organization_id == organization_id
                )
            )

            # 添加筛选条件
            if transaction_type:
                query = query.where(CreditTransaction.transaction_type == transaction_type)
            
            if service_type:
                query = query.where(CreditTransaction.service_type == service_type)
            
            if start_date:
                query = query.where(CreditTransaction.created_at >= start_date)
            
            if end_date:
                query = query.where(CreditTransaction.created_at <= end_date)

            # 获取总数
            count_query = select(func.count()).select_from(query.subquery())
            count_result = await db.execute(count_query)
            total = count_result.scalar()

            # 添加排序和分页
            query = query.order_by(CreditTransaction.created_at.desc())
            query = query.offset((page - 1) * page_size).limit(page_size)

            # 执行查询
            result = await db.execute(query)
            transactions = result.scalars().all()

            # 转换为字典格式
            transaction_list = []
            for transaction in transactions:
                transaction_dict = transaction.to_dict()
                transaction_list.append(transaction_dict)

            return transaction_list, total

        except Exception as e:
            identity_type, _ = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"获取{identity_name}积分交易记录失败: {e}")
            return [], 0

