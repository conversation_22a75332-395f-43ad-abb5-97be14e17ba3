"""
支付服务
处理订单创建、支付状态管理等功能
"""

import uuid
from typing import Optional, Dict, Any, Tuple
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, desc


def utc_now():
    """返回UTC时区的当前时间（不带时区信息，适配数据库）"""
    # 使用timezone-aware的datetime，然后移除时区信息以适配数据库
    return datetime.now(timezone.utc).replace(tzinfo=None)

from app.models.credit_payment import PaymentOrder, UserCreditAccount
from app.models.user import User
from app.credit_payment.config import CreditPaymentConfig
from app.credit_payment.services.credit_service import CreditService
from app.db.database import get_db


class PaymentService:
    """支付服务类"""
    
    @staticmethod
    async def _calculate_package_start_time(user: User, db: AsyncSession) -> datetime:
        """
        计算新套餐的开始时间（支持时间叠加）

        Args:
            user: 用户对象
            db: 数据库会话

        Returns:
            新套餐应该开始的时间
        """
        try:
            from datetime import datetime, timezone
            current_time = datetime.now(timezone.utc).replace(tzinfo=None)

            # 获取用户当前身份信息（不强制修改）
            from app.core.data_isolation import DataIsolationFilter
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            # 查询用户最新的已支付套餐订单（根据当前身份）
            if identity_type == 'organization' and organization_id:
                # 组织身份：查询该组织的套餐订单
                query = (
                    select(PaymentOrder)
                    .where(
                        and_(
                            PaymentOrder.user_id == user.id,
                            PaymentOrder.organization_id == organization_id,
                            PaymentOrder.status == "paid",
                            PaymentOrder.package_id.isnot(None),  # 只查询套餐订单
                            PaymentOrder.expires_at.isnot(None)   # 只查询有过期时间的订单
                        )
                    )
                    .order_by(desc(PaymentOrder.payment_time))
                )
            else:
                # 个人身份：查询个人套餐订单
                query = (
                    select(PaymentOrder)
                    .where(
                        and_(
                            PaymentOrder.user_id == user.id,
                            PaymentOrder.organization_id.is_(None),  # 个人身份的订单
                            PaymentOrder.status == "paid",
                            PaymentOrder.package_id.isnot(None),  # 只查询套餐订单
                            PaymentOrder.expires_at.isnot(None)   # 只查询有过期时间的订单
                        )
                    )
                    .order_by(desc(PaymentOrder.payment_time))
                )

            result = await db.execute(query)
            latest_order = result.scalars().first()

            # 如果没有套餐历史，从当前时间开始
            if not latest_order:
                return current_time

            # 如果最新套餐已过期，从当前时间开始
            if latest_order.expires_at <= current_time:
                return current_time

            # 如果最新套餐未过期，从其过期时间开始叠加
            print(f"🔄 检测到用户 {user.id} 有未过期套餐，新套餐将从 {latest_order.expires_at} 开始叠加")
            return latest_order.expires_at

        except Exception as e:
            print(f"计算套餐开始时间时发生错误: {e}")
            # 发生错误时，从当前时间开始（安全fallback）
            from datetime import datetime, timezone
            return datetime.now(timezone.utc).replace(tzinfo=None)

    @staticmethod
    def generate_order_no() -> str:
        """生成订单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(uuid.uuid4()).replace("-", "")[:8].upper()
        return f"{CreditPaymentConfig.ORDER_NO_PREFIX}{timestamp}{random_suffix}"
    
    @staticmethod
    async def create_payment_order(
        user: User,
        amount: Decimal,
        payment_method: str,
        package_id: Optional[str] = None,
        user_count: int = 1,
        remark: Optional[str] = None,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str, Optional[PaymentOrder]]:
        """
        创建支付订单

        Args:
            user: 用户对象
            amount: 支付金额
            payment_method: 支付方式
            package_id: 套餐ID（可选）
            user_count: 用户数量（默认为1）
            remark: 备注（可选）
            db: 数据库会话（可选）

        Returns:
            Tuple[bool, str, Optional[PaymentOrder]]: (是否成功, 消息, 订单对象)
        """
        if db is None:
            async for session in get_db():
                return await PaymentService._create_order_with_session(
                    session, user, amount, payment_method, package_id, user_count, remark
                )
        else:
            return await PaymentService._create_order_with_session(
                db, user, amount, payment_method, package_id, user_count, remark
            )
    
    @staticmethod
    async def _create_order_with_session(
        db: AsyncSession,
        user: User,
        amount: Decimal,
        payment_method: str,
        package_id: Optional[str] = None,
        user_count: int = 1,
        remark: Optional[str] = None
    ) -> Tuple[bool, str, Optional[PaymentOrder]]:
        """使用指定的数据库会话创建支付订单"""
        try:
            # 验证支付方式
            if payment_method not in CreditPaymentConfig.PAYMENT_METHODS:
                return False, f"不支持的支付方式: {payment_method}", None
            
            # 验证金额
            if amount < CreditPaymentConfig.SECURITY_CONFIG["min_recharge_amount"]:
                return False, f"充值金额不能少于{CreditPaymentConfig.SECURITY_CONFIG['min_recharge_amount']}元", None
            
            if amount > CreditPaymentConfig.SECURITY_CONFIG["max_single_recharge_amount"]:
                return False, f"单次充值金额不能超过{CreditPaymentConfig.SECURITY_CONFIG['max_single_recharge_amount']}元", None
            
            # 检查每日充值限额
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_end = today_start + timedelta(days=1)
            
            result = await db.execute(
                select(func.sum(PaymentOrder.amount)).where(
                    PaymentOrder.user_id == user.id,
                    PaymentOrder.status == "paid",
                    PaymentOrder.created_at >= today_start,
                    PaymentOrder.created_at < today_end
                )
            )
            today_total = result.scalar() or Decimal("0")
            
            if today_total + amount > CreditPaymentConfig.SECURITY_CONFIG["max_daily_recharge_amount"]:
                return False, f"今日充值金额已达上限{CreditPaymentConfig.SECURITY_CONFIG['max_daily_recharge_amount']}元", None
            
            # 计算积分数量：支持两种模式
            if package_id:
                # 1) 套餐充值模式（保持现有逻辑）
                try:
                    package_info = await CreditPaymentConfig.get_package_by_id_async(package_id, db)
                except ValueError:
                    package_info = CreditPaymentConfig.RECHARGE_PACKAGES.get(package_id)
                    if not package_info:
                        return False, f"套餐不存在: {package_id}", None
                    package_info = {"package_id": package_id, **package_info}

                if package_info.get("contact_required"):
                    return False, f"企业套餐需要咨询定价，请联系销售: {package_info.get('contact_phone', '************')}", None

                price_info = await CreditPaymentConfig.calculate_package_price_async(package_id, user_count, db)
                if not price_info:
                    return False, f"套餐价格计算失败，套餐ID: {package_id}, 用户数量: {user_count}", None

                expected_amount = price_info["total_amount"]
                if expected_amount != amount:
                    return False, f"套餐金额不匹配，期望: {expected_amount}元，实际: {amount}元", None

                credits = price_info["total_credits"]
                expires_at_value = utc_now() + timedelta(minutes=CreditPaymentConfig.ORDER_EXPIRE_MINUTES)
            else:
                # 2) 单独积分充值模式（无需套餐）
                bundle_credits = CreditPaymentConfig.get_credits_for_amount(amount)
                if bundle_credits <= 0:
                    return False, "不支持的积分充值金额，请使用预设的积分档位", None
                credits = bundle_credits
                # 对于积分充值订单，expires_at 仍用于订单支付超时（创建时），支付成功后不设置套餐有效期
                expires_at_value = utc_now() + timedelta(minutes=CreditPaymentConfig.ORDER_EXPIRE_MINUTES)

            # 获取或创建用户积分账户（保持当前身份状态）
            from app.credit_payment.services.credit_service import CreditService
            user_account = await CreditService.get_or_create_credit_account(user, db)
            if not user_account:
                return False, "无法创建用户积分账户", None

            # 检查用户当前身份状态
            from app.core.data_isolation import DataIsolationFilter
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            # 创建订单
            order = PaymentOrder(
                order_no=PaymentService.generate_order_no(),
                user_id=user.id,
                user_credit_account_id=user_account.id,
                amount=amount,
                credits=credits,
                payment_method=payment_method,
                recharge_type=("package" if package_id else "credits"),
                package_id=package_id,
                package_quantity=user_count if package_id else 0,  # 积分充值时为0
                organization_id=organization_id,  # 设置组织ID（如果是组织身份）
                remark=remark,
                expires_at=expires_at_value  # 订单支付超时控制；套餐过期时间在支付成功后设置
            )
            
            db.add(order)
            await db.commit()
            await db.refresh(order)
            
            return True, "订单创建成功", order
            
        except Exception as e:
            await db.rollback()
            print(f"创建支付订单失败: {e}")
            return False, f"创建订单失败: {str(e)}", None
    
    @staticmethod
    async def get_order_by_no(
        order_no: str,
        db: Optional[AsyncSession] = None
    ) -> Optional[PaymentOrder]:
        """根据订单号获取订单"""
        if db is None:
            async for session in get_db():
                return await PaymentService._get_order_by_no_with_session(session, order_no)
        else:
            return await PaymentService._get_order_by_no_with_session(db, order_no)
    
    @staticmethod
    async def _get_order_by_no_with_session(
        db: AsyncSession,
        order_no: str
    ) -> Optional[PaymentOrder]:
        """使用指定的数据库会话根据订单号获取订单"""
        try:
            result = await db.execute(
                select(PaymentOrder).where(PaymentOrder.order_no == order_no)
            )
            return result.scalars().first()
        except Exception as e:
            print(f"获取订单失败: {e}")
            return None
    
    @staticmethod
    async def complete_payment(
        order_no: str,
        trade_no: str,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """
        完成支付（支付回调时调用）

        Args:
            order_no: 订单号
            trade_no: 第三方支付交易号
            db: 数据库会话（可选）

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        if db is None:
            async for session in get_db():
                return await PaymentService._complete_payment_with_session(
                    session, order_no, trade_no
                )
        else:
            # 检查当前会话是否已经在事务中
            in_transaction = db.in_transaction()
            if in_transaction:
                print(f"警告: complete_payment 被调用时已经在事务中")

            return await PaymentService._complete_payment_with_session(
                db, order_no, trade_no
            )
    
    @staticmethod
    async def _complete_payment_with_session(
        db: AsyncSession,
        order_no: str,
        trade_no: str
    ) -> Tuple[bool, str]:
        """使用指定的数据库会话完成支付"""
        try:
            # 检查是否已经在事务中
            in_transaction = db.in_transaction()

            if in_transaction:
                # 如果已经在事务中，直接执行操作
                return await PaymentService._complete_payment_in_transaction(
                    db, order_no, trade_no
                )
            else:
                # 如果不在事务中，开始新事务
                async with db.begin():
                    return await PaymentService._complete_payment_in_transaction(
                        db, order_no, trade_no
                    )

        except Exception as e:
            print(f"完成支付失败: {e}")
            return False, f"支付处理失败: {str(e)}"

    @staticmethod
    async def _complete_payment_in_transaction(
        db: AsyncSession,
        order_no: str,
        trade_no: str
    ) -> Tuple[bool, str]:
        """在现有事务中完成支付操作"""
        try:
            # 获取订单
            order = await PaymentService._get_order_by_no_with_session(db, order_no)
            if not order:
                return False, "订单不存在"

            # 检查订单状态
            if order.status != "pending":
                return False, f"订单状态异常: {order.status}"

            # 检查订单是否过期
            if utc_now() > order.expires_at:
                order.status = "failed"
                return False, "订单已过期"

            # 更新订单状态
            order.status = "paid"
            order.trade_no = trade_no
            order.payment_time = utc_now()

            # 获取用户对象（用于设置身份与后续积分变更）
            user_result = await db.execute(select(User).where(User.id == order.user_id))
            user = user_result.scalars().first()
            if not user:
                return False, f"用户不存在: {order.user_id}"

            # 🎯 设置过期时间逻辑
            if order.package_id:
                # 套餐订单：设置套餐有效期
                from datetime import timedelta
                from app.credit_payment.config import CreditPaymentConfig

                try:
                    package_info = await CreditPaymentConfig.get_package_by_id_async(order.package_id, db)
                except ValueError:
                    package_info = CreditPaymentConfig.RECHARGE_PACKAGES.get(order.package_id)

                start_time = await PaymentService._calculate_package_start_time(user, db)

                if package_info and package_info.get("is_trial"):
                    validity_days = package_info.get("validity_days", 3)
                    order.expires_at = start_time + timedelta(days=validity_days)
                else:
                    order.expires_at = start_time + timedelta(days=365)
            else:
                # 积分充值：不设置套餐有效期，保持创建时的订单支付超时值
                pass

            # 确保订单更改被持久化到数据库
            db.add(order)
            await db.flush()  # 立即刷新到数据库，确保后续操作能看到更新
            print(f"✅ 订单状态已更新: {order_no} -> payment_time: {order.payment_time}, expires_at: {order.expires_at}")

            # 恢复用户的身份状态（基于订单的organization_id）
            if order.organization_id:
                user._current_identity_type = 'organization'
                user._current_organization_id = order.organization_id
            else:
                user._current_identity_type = 'personal'
                user._current_organization_id = None

            # 为用户增加积分（在同一事务中，使用正确的身份）
            from app.credit_payment.services.credit_service import CreditService
            success, message = await CreditService._add_credits_in_existing_transaction(
                db=db,
                user=user,
                credits=order.credits,
                transaction_type="recharge",
                order_id=order.id,
                description=f"充值订单{order_no}完成，获得{order.credits}积分"
            )

            if not success:
                return False, f"积分充值失败: {message}"

            # 根据订单类型返回不同的成功消息
            if order.package_id:
                return True, f"套餐购买成功，积分已到账，套餐有效期至 {order.expires_at.strftime('%Y-%m-%d')}"
            else:
                return True, "支付完成，积分已到账"

        except Exception as e:
            print(f"事务内支付处理失败: {e}")
            return False, f"支付处理失败: {str(e)}"

    @staticmethod
    async def handle_cancelled_order_payment(
        order_no: str,
        trade_no: str,
        amount: str,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """
        处理已取消订单的支付成功通知

        当用户取消订单后仍然完成了支付，需要自动退款

        Args:
            order_no: 订单号
            trade_no: 支付宝交易号
            amount: 支付金额
            db: 数据库会话（可选）

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        if db is None:
            async for session in get_db():
                return await PaymentService._handle_cancelled_order_payment_with_session(
                    session, order_no, trade_no, amount
                )
        else:
            return await PaymentService._handle_cancelled_order_payment_with_session(
                db, order_no, trade_no, amount
            )

    @staticmethod
    async def _handle_cancelled_order_payment_with_session(
        db: AsyncSession,
        order_no: str,
        trade_no: str,
        amount: str
    ) -> Tuple[bool, str]:
        """使用指定的数据库会话处理已取消订单的支付"""
        try:
            # 检查是否已经在事务中
            in_transaction = db.in_transaction()

            if in_transaction:
                # 如果已经在事务中，直接执行操作
                return await PaymentService._handle_cancelled_order_payment_in_transaction(
                    db, order_no, trade_no, amount
                )
            else:
                # 如果不在事务中，开始新事务
                async with db.begin():
                    return await PaymentService._handle_cancelled_order_payment_in_transaction(
                        db, order_no, trade_no, amount
                    )

        except Exception as e:
            print(f"处理已取消订单支付失败: {e}")
            return False, f"处理已取消订单支付失败: {str(e)}"

    @staticmethod
    async def _handle_cancelled_order_payment_in_transaction(
        db: AsyncSession,
        order_no: str,
        trade_no: str,
        amount: str
    ) -> Tuple[bool, str]:
        """在现有事务中处理已取消订单的支付"""
        try:
            from decimal import Decimal

            # 获取订单
            order = await PaymentService._get_order_by_no_with_session(db, order_no)
            if not order:
                return False, "订单不存在"

            # 确认订单状态为已取消
            if order.status != "cancelled":
                return False, f"订单状态不是已取消: {order.status}"

            # 记录这次支付（更新订单信息但保持cancelled状态）
            order.trade_no = trade_no
            order.payment_time = utc_now()
            # 注意：不改变订单状态，保持为cancelled

            # 确保订单更改被持久化到数据库
            db.add(order)
            await db.flush()
            print(f"✅ 已取消订单支付信息已更新: {order_no} -> payment_time: {order.payment_time}")

            # 创建积分交易记录（标记为退款）
            from app.credit_payment.services.credit_service import CreditService

            # 记录这次异常支付情况
            description = f"已取消订单{order_no}收到支付成功通知，金额{amount}元，将自动退款"

            # 这里不增加积分，因为订单已取消
            # 但需要记录这个事件用于退款处理

            # 调用支付宝退款接口自动退款
            from app.credit_payment.services.alipay_service import AlipayService

            print(f"开始自动退款处理: 订单{order_no}, 交易号: {trade_no}, 金额: {amount}元")

            try:
                alipay_service = AlipayService()
                refund_amount = Decimal(amount)
                refund_reason = f"订单{order_no}已取消，自动退款"

                # 调用支付宝退款接口
                refund_success, refund_message, refund_no = await alipay_service.refund_trade(
                    order_no=order_no,
                    refund_amount=refund_amount,
                    refund_reason=refund_reason
                )

                if refund_success:
                    print(f"自动退款成功: 订单{order_no}, 退款单号: {refund_no}")

                    # 更新订单备注，记录退款信息
                    order.remark = f"订单已取消并自动退款，退款单号: {refund_no}"

                    return True, f"已取消订单支付处理完成，已自动退款: {amount}元，退款单号: {refund_no}"
                else:
                    print(f"自动退款失败: 订单{order_no}, 错误: {refund_message}")

                    # 退款失败，记录信息供人工处理
                    order.remark = f"订单已取消，自动退款失败: {refund_message}，需要人工处理"

                    return True, f"已取消订单支付已记录，自动退款失败，需要人工处理: 订单{order_no}, 金额{amount}元, 错误: {refund_message}"

            except Exception as refund_error:
                print(f"退款处理异常: 订单{order_no}, 异常: {refund_error}")

                # 退款异常，记录信息供人工处理
                order.remark = f"订单已取消，退款处理异常: {str(refund_error)}，需要人工处理"

                return True, f"已取消订单支付已记录，退款处理异常，需要人工处理: 订单{order_no}, 金额{amount}元"

        except Exception as e:
            print(f"事务内处理已取消订单支付失败: {e}")
            return False, f"处理已取消订单支付失败: {str(e)}"
    
    @staticmethod
    async def cancel_order(
        order_no: str,
        reason: str = "用户取消",
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """
        取消订单
        
        Args:
            order_no: 订单号
            reason: 取消原因
            db: 数据库会话（可选）
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        if db is None:
            async for session in get_db():
                return await PaymentService._cancel_order_with_session(
                    session, order_no, reason
                )
        else:
            return await PaymentService._cancel_order_with_session(
                db, order_no, reason
            )
    
    @staticmethod
    async def _cancel_order_with_session(
        db: AsyncSession,
        order_no: str,
        reason: str
    ) -> Tuple[bool, str]:
        """使用指定的数据库会话取消订单"""
        try:
            order = await PaymentService._get_order_by_no_with_session(db, order_no)
            if not order:
                return False, "订单不存在"
            
            if order.status != "pending":
                return False, f"订单状态异常，无法取消: {order.status}"
            
            order.status = "cancelled"
            order.remark = f"{order.remark or ''} [取消原因: {reason}]"
            
            await db.commit()
            return True, "订单已取消"
            
        except Exception as e:
            await db.rollback()
            print(f"取消订单失败: {e}")
            return False, f"取消订单失败: {str(e)}"
    
    @staticmethod
    async def get_user_orders(
        user: User,
        status: Optional[str] = None,
        limit: int = 20,
        offset: int = 0,
        db: Optional[AsyncSession] = None
    ) -> list:
        """
        获取用户订单列表

        Args:
            user: 用户对象
            status: 订单状态过滤（可选）
            limit: 限制数量
            offset: 偏移量
            db: 数据库会话（可选）

        Returns:
            list: 订单列表
        """
        if db is None:
            async for session in get_db():
                return await PaymentService._get_user_orders_with_session(
                    session, user, status, limit, offset
                )
        else:
            return await PaymentService._get_user_orders_with_session(
                db, user, status, limit, offset
            )
    
    @staticmethod
    async def _get_user_orders_with_session(
        db: AsyncSession,
        user: User,
        status: Optional[str],
        limit: int,
        offset: int
    ) -> list:
        """使用指定的数据库会话获取用户订单列表"""
        try:
            # 获取用户当前身份信息（不强制修改）
            from app.core.data_isolation import DataIsolationFilter
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            # 根据身份类型查询对应的订单
            if identity_type == 'organization' and organization_id:
                # 组织身份：查询该组织的订单
                query = select(PaymentOrder).where(
                    and_(
                        PaymentOrder.user_id == user.id,
                        PaymentOrder.organization_id == organization_id
                    )
                )
            else:
                # 个人身份：查询个人订单
                query = select(PaymentOrder).where(
                    and_(
                        PaymentOrder.user_id == user.id,
                        PaymentOrder.organization_id.is_(None)
                    )
                )

            if status:
                query = query.where(PaymentOrder.status == status)

            query = query.order_by(PaymentOrder.created_at.desc()).limit(limit).offset(offset)

            result = await db.execute(query)
            orders = result.scalars().all()

            return [order.to_dict() for order in orders]

        except Exception as e:
            print(f"获取用户订单失败: {e}")
            return []

    @staticmethod
    async def process_refund(
        order_no: str,
        refund_amount: Decimal,
        refund_reason: str = "用户申请退款",
        refund_no: Optional[str] = None,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """
        处理退款（更新订单状态和积分）

        Args:
            order_no: 订单号
            refund_amount: 退款金额
            refund_reason: 退款原因
            refund_no: 退款单号（可选）
            db: 数据库会话（可选）

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        if db is None:
            async for session in get_db():
                return await PaymentService._process_refund_with_session(
                    session, order_no, refund_amount, refund_reason, refund_no
                )
        else:
            return await PaymentService._process_refund_with_session(
                db, order_no, refund_amount, refund_reason, refund_no
            )

    @staticmethod
    async def _process_refund_with_session(
        db: AsyncSession,
        order_no: str,
        refund_amount: Decimal,
        refund_reason: str,
        refund_no: Optional[str]
    ) -> Tuple[bool, str]:
        """使用指定的数据库会话处理退款"""
        try:
            # 检查是否已经在事务中
            in_transaction = db.in_transaction()

            if in_transaction:
                # 如果已经在事务中，直接执行操作
                return await PaymentService._process_refund_in_transaction(
                    db, order_no, refund_amount, refund_reason, refund_no
                )
            else:
                # 如果不在事务中，开始新事务
                async with db.begin():
                    return await PaymentService._process_refund_in_transaction(
                        db, order_no, refund_amount, refund_reason, refund_no
                    )

        except Exception as e:
            print(f"处理退款失败: {e}")
            return False, f"退款处理失败: {str(e)}"

    @staticmethod
    async def _process_refund_in_transaction(
        db: AsyncSession,
        order_no: str,
        refund_amount: Decimal,
        refund_reason: str,
        refund_no: Optional[str]
    ) -> Tuple[bool, str]:
        """在现有事务中处理退款操作"""
        try:
            # 获取订单
            order = await PaymentService.get_order_by_no(order_no, db)
            if not order:
                return False, "订单不存在"

            # 检查订单状态 - 只有已支付的订单才能退款
            if order.status != "paid":
                return False, f"订单状态不允许退款: {order.status}"

            # 检查是否已经退款过（幂等性控制）
            if order.refund_amount > 0:
                return False, f"订单已退款，退款金额: {order.refund_amount}元"

            # 验证退款金额
            if refund_amount > order.amount:
                return False, f"退款金额({refund_amount})不能超过订单金额({order.amount})"

            # 计算需要扣除的积分（基于套餐配置）
            refund_credits = await PaymentService._calculate_refund_credits(db, order, refund_amount)

            # 获取用户对象
            from app.models.user import User
            from sqlalchemy.future import select
            user_result = await db.execute(select(User).where(User.id == order.user_id))
            user = user_result.scalars().first()
            if not user:
                return False, f"用户不存在: {order.user_id}"

            # 恢复用户的身份状态（基于订单的organization_id）
            if order.organization_id:
                user._current_identity_type = 'organization'
                user._current_organization_id = order.organization_id
            else:
                user._current_identity_type = 'personal'
                user._current_organization_id = None

            # 检查用户积分余额是否足够扣除（使用正确的身份）
            from app.credit_payment.services.credit_service import CreditService
            user_balance = await CreditService.get_credit_balance(user, db)
            if user_balance < refund_credits:
                return False, f"用户积分余额不足，当前余额: {user_balance}，需要扣除: {refund_credits}"

            # 扣除用户积分（在同一事务中）
            success, message = await CreditService._add_credits_in_existing_transaction(
                db=db,
                user=user,
                credits=-refund_credits,  # 负数表示扣除
                transaction_type="refund",
                order_id=order.id,
                description=f"订单{order_no}退款，扣除{refund_credits}积分"
            )

            if not success:
                return False, f"积分扣除失败: {message}"

            # 更新订单状态和退款信息
            if refund_amount == order.amount:
                order.status = "refunded"  # 全额退款
            else:
                order.status = "partial_refunded"  # 部分退款

            order.refund_amount = refund_amount
            order.refund_reason = refund_reason
            order.refund_time = utc_now()

            # 如果有退款单号，可以存储在备注中
            if refund_no:
                order.remark = f"{order.remark or ''} [退款单号: {refund_no}]"

            return True, f"退款处理成功，已扣除{refund_credits}积分"

        except Exception as e:
            print(f"事务内退款处理失败: {e}")
            return False, f"退款处理失败: {str(e)}"

    @staticmethod
    async def _calculate_refund_credits(db: AsyncSession, order: 'PaymentOrder', refund_amount: Decimal) -> int:
        """
        基于套餐配置计算退款时需要扣除的积分

        Args:
            order: 支付订单对象
            refund_amount: 退款金额

        Returns:
            int: 需要扣除的积分数量
        """
        try:
            # 如果没有套餐ID，按原有逻辑处理（向后兼容）
            if not hasattr(order, 'package_id') or not order.package_id:
                print(f"订单 {order.order_no} 没有套餐ID，使用比例计算退款积分")
                if refund_amount == order.amount:
                    return order.credits
                else:
                    refund_ratio = refund_amount / order.amount
                    return int(order.credits * refund_ratio)

            # 获取套餐配置（优先使用异步方法）
            from app.credit_payment.config import CreditPaymentConfig
            try:
                package_info = await CreditPaymentConfig.get_package_by_id_async(order.package_id, db)
            except ValueError:
                package_info = CreditPaymentConfig.RECHARGE_PACKAGES.get(order.package_id)
                if package_info:
                    package_info = {"package_id": order.package_id, **package_info}

            if not package_info:
                # 套餐不存在，按原有逻辑处理
                print(f"警告: 订单 {order.order_no} 的套餐 {order.package_id} 不存在，使用比例计算")
                if refund_amount == order.amount:
                    return order.credits
                else:
                    refund_ratio = refund_amount / order.amount
                    return int(order.credits * refund_ratio)

            # 计算用户数量（基于订单金额和套餐单价）
            package_amount = package_info["amount"]

            # 检查是否有批量折扣
            if package_info.get("bulk_discount") and order.amount < package_amount:
                # 使用折扣价格计算
                discounted_amount = package_info["bulk_discount"]["discounted_amount"]
                user_count = int(order.amount / discounted_amount)
                unit_price = discounted_amount
            else:
                # 使用原价计算
                user_count = int(order.amount / package_amount)
                unit_price = package_amount

            # 计算退款的用户数量
            if refund_amount == order.amount:
                # 全额退款
                refund_user_count = user_count
            else:
                # 部分退款，必须按套餐单位退款
                refund_user_count = int(refund_amount / unit_price)

                # 验证退款金额是否为套餐单价的整数倍
                expected_refund_amount = refund_user_count * unit_price
                if abs(refund_amount - expected_refund_amount) > Decimal("0.01"):
                    raise ValueError(f"退款金额必须为套餐单价的整数倍。套餐单价: {unit_price}元，退款金额: {refund_amount}元")

            # 计算需要扣除的积分
            package_credits = package_info["credits"]
            refund_credits = package_credits * refund_user_count

            print(f"套餐退款计算: 套餐={order.package_id}, 用户数={user_count}, 退款用户数={refund_user_count}, 单人积分={package_credits}, 扣除积分={refund_credits}")

            return refund_credits

        except Exception as e:
            print(f"计算退款积分失败: {e}")
            # 发生错误时，使用原有的比例计算方法
            if refund_amount == order.amount:
                return order.credits
            else:
                refund_ratio = refund_amount / order.amount
                return int(order.credits * refund_ratio)
