"""
套餐验证服务
实现套餐购买状态检查和验证逻辑
支持多租户数据隔离
"""

from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, desc

from app.models.credit_payment import PaymentOrder
from app.models.user import User
from app.core.data_isolation import DataIsolationFilter


class PackageService:
    """套餐验证服务类"""
    
    @staticmethod
    async def check_user_package_status(
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """
        检查用户套餐购买状态（包含过期检测，支持多租户）

        Args:
            db: 数据库会话
            user: 用户对象（包含身份信息）

        Returns:
            Dict包含套餐状态信息，包含过期状态
        """
        try:
            from datetime import datetime, timezone
            current_time = datetime.now(timezone.utc).replace(tzinfo=None)

            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            
            # 🔧 修复：添加详细的身份和查询调试信息
            print(f"📦 套餐状态查询 - 用户ID: {user.id}, 身份: {identity_name}, 组织ID: {organization_id}")

            # 查询用户最新的已支付套餐订单（包括分配的套餐，支持多租户）
            query = (
                select(PaymentOrder)
                .where(
                    and_(
                        PaymentOrder.user_id == user.id,
                        PaymentOrder.organization_id == organization_id,
                        PaymentOrder.status == "paid",
                        PaymentOrder.package_id.isnot(None)  # 只查询套餐订单
                    )
                )
                .order_by(desc(PaymentOrder.payment_time))
            )

            result = await db.execute(query)
            latest_order = result.scalars().first()
            
            # 🔧 修复：添加查询结果调试信息
            if latest_order:
                print(f"📦 找到套餐订单 - 订单ID: {latest_order.id}, 套餐ID: {latest_order.package_id}, 支付方式: {latest_order.payment_method}, 组织ID: {latest_order.organization_id}")
            else:
                print(f"📦 未找到套餐订单 - 查询条件: 用户ID={user.id}, 组织ID={organization_id}, 身份={identity_name}")

            # 如果找到订单，还需要验证分配的套餐是否仍然有效
            if latest_order and latest_order.payment_method == "allocation":
                # 这是一个分配的套餐，需要检查分配状态
                from app.models.package_allocation import PackageAllocation

                print(f"🔍 [分配套餐验证] 订单{latest_order.id}是分配订单，检查分配状态...")

                allocation_query = select(PackageAllocation).where(
                    and_(
                        PackageAllocation.allocated_order_id == latest_order.id,
                        PackageAllocation.status == "active"
                    )
                )
                allocation_result = await db.execute(allocation_query)
                allocation = allocation_result.scalars().first()

                print(f"🔍 [分配套餐验证] 分配记录: {allocation.id if allocation else None}, 状态: {allocation.status if allocation else '未找到'}")

                # 如果分配已被回收，则视为无套餐
                if not allocation:
                    print(f"❌ [分配套餐验证] 分配记录无效，视为无套餐")
                    latest_order = None
                else:
                    print(f"✅ [分配套餐验证] 分配记录有效，套餐可用")

            if not latest_order:
                print(f"❌ [套餐状态查询] 用户{user.id}在{identity_name}下未找到任何套餐订单")
                return {
                    "has_package": False,
                    "identity_type": identity_type,
                    "identity_name": identity_name,
                    "organization_id": organization_id,
                    "package_id": None,
                    "package_name": None,
                    "purchase_time": None,
                    "expires_at": None,
                    "is_expired": False,
                    "days_remaining": 0,
                    "credits_purchased": 0,
                    "order_amount": 0.0,
                    "message": f"{identity_name}未购买任何套餐",
                    "expiry_status": {
                        "is_expired": False,
                        "days_remaining": 0,
                        "expires_at": None,
                        "warning_threshold": 30
                    }
                }

            # 检查套餐是否过期
            is_expired = False
            days_remaining = 0

            if latest_order.expires_at:
                is_expired = current_time > latest_order.expires_at
                if not is_expired:
                    time_diff = latest_order.expires_at - current_time
                    days_remaining = max(0, time_diff.days)
            else:
                # 如果没有过期时间，视为永久有效（兼容旧数据）
                is_expired = False
                days_remaining = 999999  # 表示永久

            # 获取套餐信息（优先从数据库）
            package_info = await PackageService._get_package_info_async(db, latest_order.package_id)

            # 如果套餐已过期，视为无有效套餐
            has_valid_package = not is_expired

            # 检查是否为分配的套餐
            is_allocated_package = latest_order.payment_method == "allocation"

            # 构建消息
            if has_valid_package:
                if is_allocated_package:
                    message = f"{identity_name}已分配{package_info['name']}套餐"
                else:
                    message = f"{identity_name}已购买{package_info['name']}套餐"
            else:
                if is_allocated_package:
                    message = f"{identity_name}分配的{package_info['name']}套餐已过期或被回收"
                else:
                    message = f"{identity_name}{package_info['name']}套餐已过期，请续费"

            print(f"✅ [套餐状态查询] 最终结果: has_package={has_valid_package}, package_name={package_info['name']}, is_allocated={is_allocated_package}")

            return {
                "has_package": has_valid_package,
                "identity_type": identity_type,
                "identity_name": identity_name,
                "organization_id": organization_id,
                "package_id": latest_order.package_id,
                "package_name": package_info["name"] if has_valid_package else f"{package_info['name']}（已过期）",
                "purchase_time": latest_order.payment_time,
                "expires_at": latest_order.expires_at,
                "is_expired": is_expired,
                "days_remaining": days_remaining,
                "credits_purchased": latest_order.credits,
                "order_amount": float(latest_order.amount),
                "is_allocated": is_allocated_package,  # 新增：是否为分配的套餐
                "message": message,
                "expiry_status": {
                    "is_expired": is_expired,
                    "days_remaining": days_remaining,
                    "expires_at": latest_order.expires_at.isoformat() if latest_order.expires_at else None,
                    "warning_threshold": 30,  # 30天内到期显示警告
                    "needs_renewal": is_expired or days_remaining <= 30
                }
            }

        except Exception as e:
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"检查{identity_name}套餐状态时发生错误: {e}")
            return {
                "has_package": False,
                "identity_type": identity_type,
                "identity_name": identity_name,
                "organization_id": organization_id,
                "package_id": None,
                "package_name": None,
                "purchase_time": None,
                "expires_at": None,
                "is_expired": False,
                "days_remaining": 0,
                "credits_purchased": 0,
                "order_amount": 0.0,
                "message": f"{identity_name}套餐状态检查失败",
                "expiry_status": {
                    "is_expired": False,
                    "days_remaining": 0,
                    "expires_at": None,
                    "warning_threshold": 30,
                    "needs_renewal": False
                }
            }
    
    @staticmethod
    def _get_package_info(package_id: Optional[str]) -> Dict[str, str]:
        """
        获取套餐信息（同步方法，使用硬编码配置作为fallback）
        注意：此方法为向后兼容保留，建议使用 _get_package_info_async

        Args:
            package_id: 套餐ID

        Returns:
            套餐信息字典
        """
        # 硬编码配置作为fallback
        package_mapping = {
            "trial_package": {
                "name": "体验套餐",
                "description": "3天体验期套餐"
            },
            "personal_standard": {
                "name": "个人标准版",
                "description": "适合个人用户的基础套餐"
            },
            "personal_professional": {
                "name": "个人专业版",
                "description": "适合个人用户的专业套餐"
            },
            "business_flagship": {
                "name": "商业旗舰版",
                "description": "适合小团队的商业套餐"
            },
            "enterprise_flagship": {
                "name": "企业旗舰版",
                "description": "适合大型企业的旗舰套餐"
            }
        }

        return package_mapping.get(package_id, {
            "name": "未知套餐",
            "description": "套餐信息未找到"
        })

    @staticmethod
    async def _get_package_info_async(db: AsyncSession, package_id: Optional[str]) -> Dict[str, str]:
        """
        异步获取套餐信息（优先从数据库，fallback到硬编码）

        Args:
            db: 数据库会话
            package_id: 套餐ID

        Returns:
            套餐信息字典
        """
        if not package_id:
            return {
                "name": "未知套餐",
                "description": "套餐ID为空"
            }

        try:
            # 优先从数据库获取配置
            from app.credit_payment.config import CreditPaymentConfig
            package_info = await CreditPaymentConfig.get_package_by_id_async(package_id, db)

            return {
                "name": package_info.get("name", "未知套餐"),
                "description": package_info.get("description", "套餐信息未找到")
            }

        except ValueError:
            # 如果数据库中没有找到，使用硬编码配置作为fallback
            return PackageService._get_package_info(package_id)
        except Exception as e:
            print(f"获取套餐信息异常: {e}")
            # 发生异常时使用硬编码配置作为fallback
            return PackageService._get_package_info(package_id)
    
    @staticmethod
    async def validate_package_access(db: AsyncSession, user: User) -> Dict[str, Any]:
        """
        验证用户套餐访问权限（第一层验证 - 门票机制，支持多租户）
        适用于所有智能选校模式（硬筛选 + AI智能匹配）

        Args:
            db: 数据库会话
            user: 用户对象（包含身份信息）

        Returns:
            Dict: 套餐验证结果
        """
        try:
            # 检查套餐购买状态
            package_status = await PackageService.check_user_package_status(db, user)

            if package_status["has_package"]:
                return {
                    "can_access": True,
                    "reason": "success",
                    "message": f"{package_status['identity_name']}套餐验证通过，可以使用智能选校功能",
                    "package_status": package_status
                }
            else:
                return {
                    "can_access": False,
                    "reason": "no_package",
                    "message": f"请先为{package_status['identity_name']}购买套餐后使用智能选校功能",
                    "package_status": package_status
                }

        except Exception as e:
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"❌ {identity_name}套餐验证异常: {e}")
            return {
                "can_access": False,
                "reason": "system_error",
                "message": f"{identity_name}系统错误，请稍后重试",
                "package_status": {
                    "has_package": False,
                    "identity_type": identity_type,
                    "identity_name": identity_name,
                    "organization_id": organization_id,
                    "package_name": None,
                    "purchase_time": None
                }
            }

    @staticmethod
    async def validate_ai_selection_access(
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """
        验证用户是否可以访问智能选校功能（支持多租户）
        实现套餐+积分双重验证

        Args:
            db: 数据库会话
            user: 用户对象（包含身份信息）

        Returns:
            验证结果字典
        """
        try:
            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"

            # 1. 检查套餐状态
            package_status = await PackageService.check_user_package_status(db, user)

            if not package_status["has_package"]:
                return {
                    "can_access": False,
                    "reason": "no_package",
                    "message": f"请先为{identity_name}购买套餐后使用智能选校功能",
                    "package_status": package_status,
                    "credit_status": None
                }

            # 2. 检查积分余额
            from app.credit_payment.services.credit_service import CreditService
            credit_balance = await CreditService.get_credit_balance(user, db)

            # 构造积分状态信息
            credit_status = {
                "balance": credit_balance,
                "identity_type": identity_type,
                "identity_name": identity_name,
                "organization_id": organization_id,
                "account_status": "active" if credit_balance >= 0 else "inactive"
            }

            # 智能选校固定消耗5积分
            required_credits = 5

            if credit_status["balance"] < required_credits:
                return {
                    "can_access": False,
                    "reason": "insufficient_credits",
                    "message": f"{identity_name}积分不足，智能选校需要{required_credits}积分，当前余额{credit_status['balance']}积分",
                    "package_status": package_status,
                    "credit_status": credit_status,
                    "required_credits": required_credits
                }

            # 3. 双重验证通过
            return {
                "can_access": True,
                "reason": "success",
                "message": f"{identity_name}验证通过，可以使用智能选校功能",
                "package_status": package_status,
                "credit_status": credit_status,
                "required_credits": required_credits
            }

        except Exception as e:
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"验证{identity_name}智能选校访问权限时发生错误: {e}")
            return {
                "can_access": False,
                "reason": "system_error",
                "message": f"{identity_name}系统错误，请稍后重试",
                "package_status": None,
                "credit_status": None
            }
    
    @staticmethod
    async def get_user_package_summary(
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """
        获取用户套餐购买汇总信息（支持多租户）

        Args:
            db: 数据库会话
            user: 用户对象（包含身份信息）

        Returns:
            套餐汇总信息
        """
        try:
            # 获取用户身份信息
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"

            # 查询所有已支付订单（支持多租户）
            query = (
                select(PaymentOrder)
                .where(
                    and_(
                        PaymentOrder.user_id == user.id,
                        PaymentOrder.organization_id == organization_id,
                        PaymentOrder.status == "paid"
                    )
                )
                .order_by(desc(PaymentOrder.payment_time))
            )
            
            result = await db.execute(query)
            paid_orders = result.scalars().all()
            
            if not paid_orders:
                return {
                    "identity_type": identity_type,
                    "identity_name": identity_name,
                    "organization_id": organization_id,
                    "total_orders": 0,
                    "total_packages": 0,  # 新增：总套餐数量
                    "total_amount": 0.0,
                    "total_credits": 0,
                    "latest_package": None,
                    "purchase_history": []
                }
            
            # 统计信息
            total_amount = sum(float(order.amount) for order in paid_orders)
            total_credits = sum(order.credits for order in paid_orders)
            total_packages = sum(getattr(order, 'package_quantity', 1) or 1 for order in paid_orders if order.package_id)

            # 购买历史
            purchase_history = []
            for order in paid_orders:
                package_info = await PackageService._get_package_info_async(db, order.package_id)
                package_quantity = getattr(order, 'package_quantity', 1) or 1
                purchase_history.append({
                    "order_no": order.order_no,
                    "package_id": order.package_id,
                    "package_name": package_info["name"],
                    "package_quantity": package_quantity,
                    "amount": float(order.amount),
                    "credits": order.credits,
                    "payment_time": order.payment_time,
                    "payment_method": order.payment_method
                })

            # 最新套餐
            latest_order = paid_orders[0]
            latest_package_info = await PackageService._get_package_info_async(db, latest_order.package_id)
            
            return {
                "identity_type": identity_type,
                "identity_name": identity_name,
                "organization_id": organization_id,
                "total_orders": len(paid_orders),
                "total_packages": total_packages,  # 新增：总套餐数量
                "total_amount": total_amount,
                "total_credits": total_credits,
                "latest_package": {
                    "package_id": latest_order.package_id,
                    "package_name": latest_package_info["name"],
                    "package_quantity": getattr(latest_order, 'package_quantity', 1) or 1,  # 新增：最新订单的套餐数量
                    "purchase_time": latest_order.payment_time,
                    "credits": latest_order.credits
                },
                "purchase_history": purchase_history
            }
            
        except Exception as e:
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)
            identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
            print(f"获取{identity_name}套餐汇总信息时发生错误: {e}")
            return {
                "identity_type": identity_type,
                "identity_name": identity_name,
                "organization_id": organization_id,
                "total_orders": 0,
                "total_packages": 0,  # 新增：总套餐数量
                "total_amount": 0.0,
                "total_credits": 0,
                "latest_package": None,
                "purchase_history": [],
                "error": f"获取{identity_name}套餐汇总时发生错误"
            }
