"""
支付安全服务
提供签名验证、防重放攻击、支付日志监控等安全功能
"""

import hashlib
import hmac
import time
import json
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta, timezone


def utc_now():
    """返回UTC时区的当前时间（不带时区信息，适配数据库）"""
    # 使用timezone-aware的datetime，然后移除时区信息以适配数据库
    return datetime.now(timezone.utc).replace(tzinfo=None)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func

from app.core.cache import get_cache, set_cache
from app.credit_payment.config import CreditPaymentConfig
from app.models.credit_payment import PaymentOrder
from app.db.database import get_db

# 设置日志
logger = logging.getLogger(__name__)


class SecurityService:
    """支付安全服务类"""
    
    # 防重放攻击的缓存前缀
    NONCE_CACHE_PREFIX = "payment_nonce:"
    # 请求频率限制缓存前缀
    RATE_LIMIT_PREFIX = "payment_rate_limit:"
    # 支付日志缓存前缀
    PAYMENT_LOG_PREFIX = "payment_log:"
    
    @staticmethod
    def generate_signature(data: Dict[str, Any], secret_key: str) -> str:
        """
        生成签名
        
        Args:
            data: 待签名的数据
            secret_key: 密钥
            
        Returns:
            str: 签名字符串
        """
        try:
            # 排序并构建签名字符串
            sorted_items = sorted(data.items())
            sign_string = "&".join([f"{k}={v}" for k, v in sorted_items if v is not None])
            sign_string += f"&key={secret_key}"
            
            # 使用HMAC-SHA256生成签名
            signature = hmac.new(
                secret_key.encode('utf-8'),
                sign_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest().upper()
            
            return signature
        except Exception as e:
            logger.error(f"生成签名失败: {e}")
            return ""
    
    @staticmethod
    def verify_signature(data: Dict[str, Any], signature: str, secret_key: str) -> bool:
        """
        验证签名
        
        Args:
            data: 待验证的数据
            signature: 签名字符串
            secret_key: 密钥
            
        Returns:
            bool: 验证结果
        """
        try:
            expected_signature = SecurityService.generate_signature(data, secret_key)
            return hmac.compare_digest(signature.upper(), expected_signature.upper())
        except Exception as e:
            logger.error(f"验证签名失败: {e}")
            return False
    
    @staticmethod
    async def check_nonce(nonce: str, user_id: int, expire_seconds: int = 300) -> bool:
        """
        检查并记录nonce，防止重放攻击
        
        Args:
            nonce: 随机数
            user_id: 用户ID
            expire_seconds: 过期时间（秒）
            
        Returns:
            bool: True表示nonce有效（首次使用），False表示重复使用
        """
        try:
            cache_key = f"{SecurityService.NONCE_CACHE_PREFIX}{user_id}:{nonce}"
            
            # 检查nonce是否已存在
            existing = await get_cache(cache_key)
            if existing:
                logger.warning(f"检测到重放攻击，用户ID: {user_id}, nonce: {nonce}")
                return False
            
            # 记录nonce
            await set_cache(cache_key, "used", expire_seconds)
            return True
            
        except Exception as e:
            logger.error(f"检查nonce失败: {e}")
            return False
    
    @staticmethod
    async def check_rate_limit(
        user_id: int,
        action: str,
        max_requests: int = 10,
        window_seconds: int = 60
    ) -> Tuple[bool, int]:
        """
        检查请求频率限制
        
        Args:
            user_id: 用户ID
            action: 操作类型
            max_requests: 最大请求次数
            window_seconds: 时间窗口（秒）
            
        Returns:
            Tuple[bool, int]: (是否允许请求, 剩余请求次数)
        """
        try:
            cache_key = f"{SecurityService.RATE_LIMIT_PREFIX}{user_id}:{action}"
            
            # 获取当前请求次数
            current_requests = await get_cache(cache_key)
            if current_requests is None:
                current_requests = 0
            else:
                current_requests = int(current_requests)
            
            # 检查是否超过限制
            if current_requests >= max_requests:
                logger.warning(f"用户请求频率超限，用户ID: {user_id}, 操作: {action}")
                return False, 0
            
            # 增加请求次数
            new_count = current_requests + 1
            await set_cache(cache_key, str(new_count), window_seconds)
            
            return True, max_requests - new_count
            
        except Exception as e:
            logger.error(f"检查请求频率限制失败: {e}")
            return True, max_requests  # 出错时允许请求
    
    @staticmethod
    async def log_payment_event(
        event_type: str,
        user_id: int,
        order_no: Optional[str] = None,
        amount: Optional[float] = None,
        status: Optional[str] = None,
        extra_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """
        记录支付事件日志
        
        Args:
            event_type: 事件类型（create_order, payment_success, payment_failed等）
            user_id: 用户ID
            order_no: 订单号
            amount: 金额
            status: 状态
            extra_data: 额外数据
            ip_address: IP地址
            user_agent: 用户代理
        """
        try:
            log_data = {
                "timestamp": utc_now().isoformat(),
                "event_type": event_type,
                "user_id": user_id,
                "order_no": order_no,
                "amount": amount,
                "status": status,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "extra_data": extra_data or {}
            }
            
            # 记录到日志文件
            logger.info(f"支付事件: {json.dumps(log_data, ensure_ascii=False)}")
            
            # 缓存关键事件（可选，用于实时监控）
            if event_type in ["payment_success", "payment_failed", "refund"]:
                cache_key = f"{SecurityService.PAYMENT_LOG_PREFIX}{event_type}:{int(time.time())}"
                await set_cache(cache_key, json.dumps(log_data), 3600)  # 缓存1小时
            
        except Exception as e:
            logger.error(f"记录支付事件失败: {e}")
    
    @staticmethod
    async def validate_payment_amount(
        user_id: int,
        amount: float,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """
        验证支付金额的合理性
        
        Args:
            user_id: 用户ID
            amount: 支付金额
            db: 数据库会话
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            # 基本金额验证
            if amount <= 0:
                return False, "支付金额必须大于0"
            
            min_amount = float(CreditPaymentConfig.SECURITY_CONFIG["min_recharge_amount"])
            max_single_amount = float(CreditPaymentConfig.SECURITY_CONFIG["max_single_recharge_amount"])
            max_daily_amount = float(CreditPaymentConfig.SECURITY_CONFIG["max_daily_recharge_amount"])
            
            if amount < min_amount:
                return False, f"支付金额不能少于{min_amount}元"
            
            if amount > max_single_amount:
                return False, f"单次支付金额不能超过{max_single_amount}元"
            
            # 检查每日支付限额
            if db is None:
                async for session in get_db():
                    return await SecurityService._check_daily_limit(
                        session, user_id, amount, max_daily_amount
                    )
            else:
                return await SecurityService._check_daily_limit(
                    db, user_id, amount, max_daily_amount
                )
            
        except Exception as e:
            logger.error(f"验证支付金额失败: {e}")
            return False, "支付金额验证失败"
    
    @staticmethod
    async def _check_daily_limit(
        db: AsyncSession,
        user_id: int,
        amount: float,
        max_daily_amount: float
    ) -> Tuple[bool, str]:
        """检查每日支付限额"""
        try:
            # 获取今日已支付金额
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_end = today_start + timedelta(days=1)
            
            result = await db.execute(
                select(func.sum(PaymentOrder.amount)).where(
                    PaymentOrder.user_id == user_id,
                    PaymentOrder.status == "paid",
                    PaymentOrder.created_at >= today_start,
                    PaymentOrder.created_at < today_end
                )
            )
            today_total = float(result.scalar() or 0)
            
            if today_total + amount > max_daily_amount:
                return False, f"今日支付金额已达上限{max_daily_amount}元"
            
            return True, "金额验证通过"
            
        except Exception as e:
            logger.error(f"检查每日限额失败: {e}")
            return False, "每日限额检查失败"
    
    @staticmethod
    def validate_timestamp(timestamp: int, tolerance_seconds: int = 300) -> bool:
        """
        验证时间戳，防止重放攻击
        
        Args:
            timestamp: 时间戳
            tolerance_seconds: 容忍的时间差（秒）
            
        Returns:
            bool: 时间戳是否有效
        """
        try:
            current_time = int(time.time())
            time_diff = abs(current_time - timestamp)
            
            if time_diff > tolerance_seconds:
                logger.warning(f"时间戳验证失败，时间差: {time_diff}秒")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证时间戳失败: {e}")
            return False
    
    @staticmethod
    async def detect_suspicious_activity(
        user_id: int,
        event_type: str,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """
        检测可疑活动
        
        Args:
            user_id: 用户ID
            event_type: 事件类型
            db: 数据库会话
            
        Returns:
            Tuple[bool, str]: (是否可疑, 描述)
        """
        try:
            # 检查短时间内的订单创建频率
            if event_type == "create_order":
                return await SecurityService._check_order_frequency(user_id, db)
            
            # 检查异常支付模式
            elif event_type == "payment":
                return await SecurityService._check_payment_pattern(user_id, db)
            
            return False, "正常活动"
            
        except Exception as e:
            logger.error(f"检测可疑活动失败: {e}")
            return False, "检测失败"
    
    @staticmethod
    async def _check_order_frequency(
        user_id: int,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """检查订单创建频率"""
        try:
            if db is None:
                async for session in get_db():
                    return await SecurityService._check_order_frequency_with_session(
                        session, user_id
                    )
            else:
                return await SecurityService._check_order_frequency_with_session(db, user_id)
                
        except Exception as e:
            logger.error(f"检查订单频率失败: {e}")
            return False, "检查失败"
    
    @staticmethod
    async def _check_order_frequency_with_session(
        db: AsyncSession,
        user_id: int
    ) -> Tuple[bool, str]:
        """使用指定会话检查订单创建频率"""
        try:
            # 检查最近5分钟内的订单数量
            five_minutes_ago = utc_now() - timedelta(minutes=5)
            
            result = await db.execute(
                select(func.count(PaymentOrder.id)).where(
                    PaymentOrder.user_id == user_id,
                    PaymentOrder.created_at >= five_minutes_ago
                )
            )
            recent_orders = result.scalar() or 0
            
            # 如果5分钟内创建超过5个订单，标记为可疑
            if recent_orders > 5:
                return True, f"5分钟内创建了{recent_orders}个订单"
            
            return False, "订单频率正常"
            
        except Exception as e:
            logger.error(f"检查订单频率失败: {e}")
            return False, "检查失败"
    
    @staticmethod
    async def _check_payment_pattern(
        user_id: int,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str]:
        """检查支付模式"""
        # 这里可以实现更复杂的支付模式检测逻辑
        # 例如：检查是否有异常的支付金额、支付时间等
        return False, "支付模式正常"
