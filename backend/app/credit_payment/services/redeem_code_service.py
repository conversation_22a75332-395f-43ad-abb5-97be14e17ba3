"""
兑换码服务
提供兑换码的创建、验证和使用功能
"""

from typing import Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from datetime import datetime, timezone

from app.db.database import get_db
from app.models.credit_payment import RedeemCode, RedeemCodeRedemption
from app.models.user import User
from app.credit_payment.services.credit_service import CreditService


def utc_now():
    """返回UTC时区的当前时间（不带时区信息，适配数据库）"""
    return datetime.now(timezone.utc).replace(tzinfo=None)


class RedeemCodeService:
    """兑换码服务类"""
    
    @staticmethod
    async def redeem_code(
        user: User,
        code: str,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str, Optional[int], Optional[int], Optional[str], Optional[str], Optional[str]]:
        """
        兑换码兑换（支持积分和套餐兑换）

        Args:
            user: 用户对象
            code: 兑换码
            db: 数据库会话（可选）

        Returns:
            Tuple[bool, str, Optional[int], Optional[int], Optional[str], Optional[str], Optional[str]]:
            (是否成功, 消息, 获得的积分数量, 新的余额, 套餐名称, 套餐过期时间, 奖励类型)
        """
        if db is None:
            async for session in get_db():
                return await RedeemCodeService._redeem_code_with_session(
                    session, user, code
                )
        else:
            return await RedeemCodeService._redeem_code_with_session(
                db, user, code
            )
    
    @staticmethod
    async def _redeem_code_with_session(
        db: AsyncSession,
        user: User,
        code: str
    ) -> Tuple[bool, str, Optional[int], Optional[int]]:
        """使用指定的数据库会话进行兑换码兑换"""
        try:
            # 标准化兑换码
            code = code.strip().upper()
            
            # 查找兑换码
            query = select(RedeemCode).where(RedeemCode.code == code)
            result = await db.execute(query)
            redeem_code = result.scalar_one_or_none()
            
            if not redeem_code:
                return False, "兑换码不存在", None, None, None, None, None

            # 检查兑换码是否有效
            if not redeem_code.is_valid():
                if not redeem_code.is_active:
                    return False, "兑换码已被禁用", None, None, None, None, None
                elif redeem_code.used_count >= redeem_code.max_uses:
                    return False, "兑换码已被使用完毕", None, None, None, None, None
                elif redeem_code.expires_at and redeem_code.expires_at < utc_now():
                    return False, "兑换码已过期", None, None, None, None, None
                else:
                    return False, "兑换码无效", None, None, None, None, None

            # 检查用户是否已经使用过这个兑换码
            redemption_query = select(RedeemCodeRedemption).where(
                and_(
                    RedeemCodeRedemption.redeem_code_id == redeem_code.id,
                    RedeemCodeRedemption.user_id == user.id
                )
            )
            existing_redemption = await db.execute(redemption_query)
            if existing_redemption.scalar_one_or_none():
                return False, "您已经使用过这个兑换码", None, None, None, None, None

            # 开始事务处理
            reward_type = redeem_code.get_reward_type()

            if reward_type == "package":
                # 套餐兑换逻辑
                return await RedeemCodeService._redeem_package(db, user, code, redeem_code)
            elif reward_type == "credits":
                # 积分兑换逻辑（兼容旧版本）
                return await RedeemCodeService._redeem_credits(db, user, code, redeem_code)
            else:
                return False, "兑换码配置错误，请联系管理员", None, None, None, None, None

        except Exception as e:
            await db.rollback()
            print(f"兑换码兑换失败: {e}")
            return False, "兑换失败，请稍后重试", None, None, None, None, None

    @staticmethod
    async def _redeem_package(
        db: AsyncSession,
        user: User,
        code: str,
        redeem_code: RedeemCode
    ) -> Tuple[bool, str, Optional[int], Optional[int], Optional[str], Optional[str], Optional[str]]:
        """处理套餐兑换逻辑（免费获得套餐）"""
        try:
            from app.credit_payment.config import CreditPaymentConfig
            from app.models.credit_payment import PaymentOrder
            from app.credit_payment.services.credit_service import CreditService
            from datetime import timedelta

            # 获取套餐配置（优先使用异步方法）
            try:
                package_info = await CreditPaymentConfig.get_package_by_id_async(redeem_code.package_id, db)
            except ValueError:
                # fallback到硬编码配置
                package_info = CreditPaymentConfig.RECHARGE_PACKAGES.get(redeem_code.package_id)
                if not package_info:
                    return False, f"套餐配置不存在: {redeem_code.package_id}", None, None, None, None, None
                package_info = {"package_id": redeem_code.package_id, **package_info}

            # 获取用户当前身份信息（不强制修改，保持用户真实身份状态）
            from app.core.data_isolation import DataIsolationFilter
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            print(f"🎫 兑换码使用 - 用户身份: {identity_type}, 组织ID: {organization_id}")

            # 获取或创建用户积分账户（使用当前身份）
            user_account = await CreditService.get_or_create_credit_account(user, db)
            if not user_account:
                return False, "无法创建用户积分账户", None, None, None, None, None

            # 生成订单号
            from app.credit_payment.services.payment_service import PaymentService
            order_no = PaymentService.generate_order_no()

            # 直接创建已支付的套餐订单（兑换码免费获得）
            current_time = utc_now()

            # 计算套餐过期时间（支持时间叠加）
            from app.credit_payment.services.payment_service import PaymentService
            start_time = await PaymentService._calculate_package_start_time(user, db)

            if package_info.get("is_trial"):
                # 体验套餐：3天有效期
                validity_days = package_info.get("validity_days", 3)
                expires_at = start_time + timedelta(days=validity_days)
                print(f"💼 兑换码体验套餐过期时间设置: {redeem_code.package_id} -> {expires_at} (从{start_time}开始，有效期{validity_days}天)")
            else:
                # 其他套餐：365天有效期
                expires_at = start_time + timedelta(days=365)
                print(f"💼 兑换码套餐过期时间设置: {redeem_code.package_id} -> {expires_at} (从{start_time}开始，有效期365天)")

            # 创建免费套餐订单
            order = PaymentOrder(
                order_no=order_no,
                user_id=user.id,
                user_credit_account_id=user_account.id,
                amount=0,  # 兑换码获得的套餐金额为0
                credits=package_info["credits"],
                payment_method="redeem_code_free",  # 特殊标识：兑换码免费获得
                package_id=redeem_code.package_id,
                package_quantity=1,  # 兑换码套餐数量为1
                status="paid",  # 直接设为已支付状态
                trade_no=f"REDEEM_{code}_{order_no}",
                payment_time=current_time,
                expires_at=expires_at,
                remark=f"兑换码 {code} 免费兑换{package_info['name']}套餐"
            )

            db.add(order)
            await db.flush()  # 立即刷新获取订单ID

            print(f"✅ 兑换码套餐订单创建成功: {order_no} -> {package_info['name']} (有效期至 {expires_at})")

            # 为用户增加积分（在同一事务中）
            success, message = await CreditService._add_credits_in_existing_transaction(
                db=db,
                user=user,
                credits=package_info["credits"],
                transaction_type="redeem_code",
                order_id=order.id,
                description=f"兑换码 {code} 兑换{package_info['name']}套餐获得{package_info['credits']}积分"
            )

            if not success:
                return False, f"积分充值失败: {message}", None, None, None, None, None

            # 更新兑换码使用次数
            redeem_code.used_count += 1
            redeem_code.updated_at = utc_now()

            # 创建兑换记录
            redemption = RedeemCodeRedemption(
                redeem_code_id=redeem_code.id,
                user_id=user.id,
                credits_received=package_info["credits"],
                package_order_id=order.id,
                redeemed_at=utc_now()
            )
            db.add(redemption)

            # 提交事务
            await db.commit()

            # 获取新的余额
            new_balance = await CreditService.get_credit_balance(user, db)

            # 获取套餐过期时间
            expires_at_str = order.expires_at.isoformat() if order.expires_at else None

            print(f"🎉 兑换码套餐兑换成功: 用户{user.id} 获得 {package_info['name']} 套餐，{package_info['credits']}积分")

            # 构建动态的有效期消息
            if package_info.get("is_trial"):
                validity_days = package_info.get("validity_days", 3)
                validity_message = f"有效期{validity_days}天"
            else:
                validity_message = "有效期365天"

            success_message = f"兑换成功！获得{package_info['name']}套餐，包含{package_info['credits']}积分，{validity_message}"

            return True, success_message, package_info["credits"], new_balance, package_info["name"], expires_at_str, "package"

        except Exception as e:
            await db.rollback()
            print(f"套餐兑换失败: {e}")
            import traceback
            traceback.print_exc()
            return False, "套餐兑换失败，请稍后重试", None, None, None, None, None

    @staticmethod
    async def _redeem_credits(
        db: AsyncSession,
        user: User,
        code: str,
        redeem_code: RedeemCode
    ) -> Tuple[bool, str, Optional[int], Optional[int], Optional[str], Optional[str], Optional[str]]:
        """处理积分兑换逻辑（兼容旧版本）"""
        try:
            from app.credit_payment.services.credit_service import CreditService

            # 获取用户当前身份信息（不强制修改，保持用户真实身份状态）
            from app.core.data_isolation import DataIsolationFilter
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            print(f"🎫 兑换码积分增加 - 用户身份: {identity_type}, 组织ID: {organization_id}")

            # 增加用户积分（使用当前身份）
            success, message = await CreditService._add_credits_in_existing_transaction(
                db=db,
                user=user,
                credits=redeem_code.credits,
                transaction_type="redeem_code",
                description=f"兑换码 {code} 兑换获得 {redeem_code.credits} 积分"
            )

            if not success:
                return False, f"积分充值失败: {message}", None, None, None, None, None

            # 更新兑换码使用次数
            redeem_code.used_count += 1
            redeem_code.updated_at = utc_now()

            # 创建兑换记录
            redemption = RedeemCodeRedemption(
                redeem_code_id=redeem_code.id,
                user_id=user.id,
                credits_received=redeem_code.credits,
                redeemed_at=utc_now()
            )
            db.add(redemption)

            # 提交事务
            await db.commit()

            # 获取新的余额
            new_balance = await CreditService.get_credit_balance(user, db)

            return True, f"兑换成功！获得 {redeem_code.credits} 积分", redeem_code.credits, new_balance, None, None, "credits"

        except Exception as e:
            await db.rollback()
            print(f"积分兑换失败: {e}")
            return False, "积分兑换失败，请稍后重试", None, None, None, None, None
    
    @staticmethod
    async def validate_code(
        code: str,
        db: Optional[AsyncSession] = None
    ) -> Tuple[bool, str, Optional[RedeemCode]]:
        """
        验证兑换码是否有效
        
        Args:
            code: 兑换码
            db: 数据库会话（可选）
            
        Returns:
            Tuple[bool, str, Optional[RedeemCode]]: (是否有效, 消息, 兑换码对象)
        """
        if db is None:
            async for session in get_db():
                return await RedeemCodeService._validate_code_with_session(session, code)
        else:
            return await RedeemCodeService._validate_code_with_session(db, code)
    
    @staticmethod
    async def _validate_code_with_session(
        db: AsyncSession,
        code: str
    ) -> Tuple[bool, str, Optional[RedeemCode]]:
        """使用指定的数据库会话验证兑换码"""
        try:
            # 标准化兑换码
            code = code.strip().upper()
            
            # 查找兑换码
            query = select(RedeemCode).where(RedeemCode.code == code)
            result = await db.execute(query)
            redeem_code = result.scalar_one_or_none()
            
            if not redeem_code:
                return False, "兑换码不存在", None
            
            # 检查兑换码是否有效
            if not redeem_code.is_valid():
                if not redeem_code.is_active:
                    return False, "兑换码已被禁用", redeem_code
                elif redeem_code.used_count >= redeem_code.max_uses:
                    return False, "兑换码已被使用完毕", redeem_code
                elif redeem_code.expires_at and redeem_code.expires_at < utc_now():
                    return False, "兑换码已过期", redeem_code
                else:
                    return False, "兑换码无效", redeem_code
            
            return True, "兑换码有效", redeem_code
            
        except Exception as e:
            print(f"验证兑换码失败: {e}")
            return False, "验证失败", None
    
    @staticmethod
    async def check_user_redemption(
        user_id: int,
        redeem_code_id: int,
        db: Optional[AsyncSession] = None
    ) -> bool:
        """
        检查用户是否已经使用过指定的兑换码
        
        Args:
            user_id: 用户ID
            redeem_code_id: 兑换码ID
            db: 数据库会话（可选）
            
        Returns:
            bool: 是否已经使用过
        """
        if db is None:
            async for session in get_db():
                return await RedeemCodeService._check_user_redemption_with_session(
                    session, user_id, redeem_code_id
                )
        else:
            return await RedeemCodeService._check_user_redemption_with_session(
                db, user_id, redeem_code_id
            )
    
    @staticmethod
    async def _check_user_redemption_with_session(
        db: AsyncSession,
        user_id: int,
        redeem_code_id: int
    ) -> bool:
        """使用指定的数据库会话检查用户兑换记录"""
        try:
            query = select(RedeemCodeRedemption).where(
                and_(
                    RedeemCodeRedemption.redeem_code_id == redeem_code_id,
                    RedeemCodeRedemption.user_id == user_id
                )
            )
            result = await db.execute(query)
            return result.scalar_one_or_none() is not None
            
        except Exception as e:
            print(f"检查用户兑换记录失败: {e}")
            return False
