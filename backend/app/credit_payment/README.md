# 积分付费系统开发文档

## 系统概述

积分付费系统是为AI选校匹配系统设计的付费解决方案，采用积分制度，支持支付宝支付，与现有token计费系统并行运行。

### 核心特性

- **积分管理**: 用户积分账户管理、余额查询、消费记录
- **支付集成**: 支付宝支付接口集成，支持订单创建、支付回调、退款处理
- **安全机制**: 签名验证、防重放攻击、支付日志监控
- **LLM集成**: 与AI选校模块无缝集成，支持积分扣减
- **兼容性**: 与现有token计费系统并行运行，不影响现有功能

## 系统架构

```
积分付费系统
├── models.py              # 数据库模型
├── config.py              # 系统配置
├── services/              # 核心服务
│   ├── credit_service.py      # 积分管理服务
│   ├── payment_service.py     # 支付管理服务
│   ├── alipay_service.py      # 支付宝集成服务
│   ├── security_service.py    # 安全服务
│   └── llm_integration.py     # LLM集成服务
├── api/                   # API接口
│   └── router.py              # 路由定义
├── schemas.py             # Pydantic模式
├── middleware.py          # 中间件
└── test_credit_payment.html  # 测试界面
```

## 数据库设计

### 用户积分账户表 (user_credit_accounts)
- `user_id`: 用户ID (主键)
- `credit_balance`: 当前积分余额
- `total_credits_purchased`: 累计购买积分数
- `total_credits_consumed`: 累计消费积分数
- `is_active`: 账户是否激活
- `last_recharge_at`: 最后充值时间
- `last_consumption_at`: 最后消费时间

### 支付订单表 (payment_orders)
- `id`: 订单ID (自增主键)
- `order_no`: 订单号 (唯一)
- `user_id`: 用户ID
- `amount`: 支付金额
- `credits`: 购买的积分数量
- `payment_method`: 支付方式
- `status`: 订单状态
- `trade_no`: 第三方支付交易号
- `payment_time`: 支付完成时间

### 积分交易记录表 (credit_transactions)
- `id`: 交易记录ID (自增主键)
- `user_id`: 用户ID
- `transaction_type`: 交易类型 (recharge/consumption/refund)
- `amount`: 积分变动数量
- `balance_before`: 交易前积分余额
- `balance_after`: 交易后积分余额
- `tokens_consumed`: 消费的tokens数量
- `conversion_rate`: 换算比例

## 积分换算规则

- **基础换算**: 1积分 = 100 tokens
- **充值套餐**:
  - 基础套餐: ¥10 = 1000积分
  - 标准套餐: ¥50 = 5500积分 (赠送500积分)
  - 高级套餐: ¥100 = 12000积分 (赠送2000积分)
  - 企业套餐: ¥500 = 65000积分 (赠送15000积分)

## API接口

### 积分管理
- `GET /api/credit-payment/credit/account` - 获取积分账户信息
- `GET /api/credit-payment/credit/balance` - 获取积分余额
- `POST /api/credit-payment/credit/consume` - 消费积分

### 支付管理
- `GET /api/credit-payment/packages` - 获取充值套餐
- `POST /api/credit-payment/payment/create` - 创建支付订单
- `POST /api/credit-payment/payment/query` - 查询支付状态
- `POST /api/credit-payment/alipay/notify` - 支付宝异步通知

### 订单管理
- `GET /api/credit-payment/orders` - 获取用户订单列表

## 安全机制

### 1. 签名验证
- 使用HMAC-SHA256算法生成和验证签名
- 防止数据篡改和伪造请求

### 2. 防重放攻击
- 使用nonce机制防止重复请求
- 时间戳验证，容忍时间差300秒

### 3. 请求频率限制
- 每用户每分钟最多20次支付相关请求
- 5分钟内最多创建5个订单

### 4. 金额验证
- 单次充值金额限制: ¥1 - ¥1000
- 每日充值金额限制: ¥5000
- 积分消费限制: 1 - 10000积分

### 5. 支付日志监控
- 记录所有支付相关事件
- 实时监控可疑活动
- 支持事件回溯和审计

## 部署配置

### 1. 环境变量配置
```bash
# 支付宝配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY_PATH=keys/alipay_private_key.pem
ALIPAY_PUBLIC_KEY_PATH=keys/alipay_public_key.pem

# 数据库配置
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/db

# Redis配置 (用于缓存)
REDIS_URL=redis://localhost:6379/0
```

### 2. 数据库迁移
```bash
# 执行数据库迁移
psql -U postgres -d tunshuedu_db -f migrations/add_credit_payment_tables.sql
```

### 3. 支付宝配置
1. 在支付宝开放平台创建应用
2. 配置应用公钥和私钥
3. 设置异步通知URL: `https://yourdomain.com/api/credit-payment/alipay/notify`
4. 设置同步跳转URL: `https://yourdomain.com/payment/success`

### 4. 启动服务
```bash
# 启动后端服务
cd backend
./start.sh
```

## 测试指南

### 1. 使用测试界面
打开 `backend/app/credit_payment/test_credit_payment.html` 进行功能测试

### 2. API测试流程
1. 用户注册/登录
2. 获取充值套餐
3. 创建支付订单
4. 模拟支付完成 (沙箱环境)
5. 查询积分余额
6. 测试积分消费
7. 查询订单历史

### 3. 支付宝沙箱测试
- 使用支付宝提供的沙箱账号进行测试
- 沙箱网关: `https://openapi.alipaydev.com/gateway.do`
- 测试账号和密码可在支付宝开放平台获取

## 监控和维护

### 1. 日志监控
- 支付事件日志: 记录所有支付相关操作
- 错误日志: 记录系统异常和API调用失败
- 性能日志: 记录请求处理时间和系统性能

### 2. 数据备份
- 定期备份积分账户数据
- 备份支付订单和交易记录
- 建立数据恢复机制

### 3. 安全审计
- 定期检查可疑交易
- 监控异常支付模式
- 审计用户积分变动

## 故障排除

### 常见问题

1. **积分扣减失败**
   - 检查用户积分余额
   - 验证数据库连接
   - 查看错误日志

2. **支付回调失败**
   - 验证支付宝签名
   - 检查网络连接
   - 确认回调URL配置

3. **订单状态异常**
   - 查询支付宝交易状态
   - 检查订单过期时间
   - 验证订单号格式

### 联系支持
如遇到技术问题，请联系开发团队或查看详细的错误日志。
