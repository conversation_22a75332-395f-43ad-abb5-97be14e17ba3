"""
积分付费系统的Pydantic模式
定义API请求和响应的数据结构
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator


class CreditAccountResponse(BaseModel):
    """积分账户响应模型"""
    user_id: int
    credit_balance: int
    total_credits_purchased: int
    total_credits_consumed: int
    is_active: bool
    last_recharge_at: Optional[datetime] = None
    last_consumption_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CreatePaymentOrderRequest(BaseModel):
    """创建支付订单请求模型"""
    amount: Decimal = Field(..., gt=0, description="支付金额，必须大于0")
    payment_method: str = Field(..., description="支付方式：alipay, wechat")
    package_id: Optional[str] = Field(None, description="套餐ID（可选）")
    user_count: int = Field(1, ge=1, le=999999, description="用户数量，默认为1")
    remark: Optional[str] = Field(None, max_length=500, description="备注信息")
    
    @validator('payment_method')
    def validate_payment_method(cls, v):
        if v not in ['alipay', 'wechat']:
            raise ValueError('支付方式必须是 alipay 或 wechat')
        return v
    
    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('支付金额必须大于0')
        if v > Decimal('10000'):
            raise ValueError('单次支付金额不能超过10000元')
        return v


class PaymentOrderResponse(BaseModel):
    """支付订单响应模型"""
    id: int
    order_no: str
    user_id: int
    amount: Decimal
    credits: int
    payment_method: str
    recharge_type: str
    package_id: Optional[str] = None
    status: str
    trade_no: Optional[str] = None
    payment_time: Optional[datetime] = None
    refund_amount: Decimal
    refund_reason: Optional[str] = None
    refund_time: Optional[datetime] = None
    remark: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    expires_at: datetime
    
    class Config:
        from_attributes = True


class CreatePaymentOrderResponse(BaseModel):
    """创建支付订单响应模型"""
    success: bool
    message: str
    order: Optional[PaymentOrderResponse] = None
    payment_url: Optional[str] = None


class CreditTransactionResponse(BaseModel):
    """积分交易记录响应模型"""
    id: int
    user_id: int
    transaction_type: str
    amount: int
    balance_before: int
    balance_after: int
    order_id: Optional[int] = None
    service_type: Optional[str] = None
    operation_type: Optional[str] = None
    request_id: Optional[str] = None
    tokens_consumed: Optional[int] = None
    conversion_rate: int
    description: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class CreditConsumptionRequest(BaseModel):
    """积分消费请求模型"""
    credits: int = Field(..., gt=0, description="消费的积分数量")
    service_type: str = Field(..., description="服务类型")
    operation_type: str = Field(..., description="操作类型")
    tokens_consumed: Optional[int] = Field(None, description="消费的tokens数量")
    request_id: Optional[str] = Field(None, description="请求ID")
    description: Optional[str] = Field(None, description="描述信息")
    
    @validator('credits')
    def validate_credits(cls, v):
        if v <= 0:
            raise ValueError('消费积分必须大于0')
        if v > 10000:
            raise ValueError('单次消费积分不能超过10000')
        return v


class CreditConsumptionResponse(BaseModel):
    """积分消费响应模型"""
    success: bool
    message: str
    remaining_balance: Optional[int] = None


class RechargePackageResponse(BaseModel):
    """充值套餐响应模型"""
    package_id: str
    name: str
    description: str
    amount: Decimal
    credits: int  # 年度总积分量
    bonus: int
    total_credits: int  # credits + bonus
    billing_cycle: str  # 计费周期：yearly
    monthly_price: Decimal  # 月均价格
    min_users: int  # 最少用户数
    max_users: int  # 最多用户数
    features: List[str]  # 功能特性
    contact_required: Optional[bool] = False  # 是否需要联系销售
    contact_phone: Optional[str] = None  # 联系电话
    bulk_discount: Optional[Dict] = None  # 批量折扣信息
    is_test: Optional[bool] = False  # 是否为测试套餐
    test_purpose: Optional[str] = None  # 测试用途


class CreditBundleResponse(BaseModel):
    """单独积分充值选项响应模型"""
    amount: Decimal
    credits: int
    name: str
    description: str


class PackagePriceCalculationRequest(BaseModel):
    """套餐价格计算请求模型"""
    package_id: str = Field(..., description="套餐ID")
    user_count: int = Field(1, ge=1, le=999999, description="用户数量")


class PackagePriceCalculationResponse(BaseModel):
    """套餐价格计算响应模型"""
    package_id: str
    package_name: str
    user_count: int
    unit_price: Decimal  # 单价（每人每年）
    total_amount: Decimal  # 总金额
    credits_per_user: int  # 每人积分
    total_credits: int  # 总积分
    discount_applied: bool  # 是否应用了折扣
    billing_cycle: str  # 计费周期
    monthly_price: Decimal  # 月均价格
    features: List[str]  # 功能特性
    contact_required: Optional[bool] = False  # 是否需要联系销售
    contact_phone: Optional[str] = None  # 联系电话


class AlipayNotifyRequest(BaseModel):
    """支付宝异步通知请求模型"""
    # 支付宝会发送的主要参数
    app_id: str
    trade_no: str
    out_trade_no: str
    trade_status: str
    total_amount: str
    receipt_amount: Optional[str] = None
    buyer_id: Optional[str] = None
    seller_id: Optional[str] = None
    gmt_create: Optional[str] = None
    gmt_payment: Optional[str] = None
    sign: str
    sign_type: str
    
    # 允许额外字段
    class Config:
        extra = "allow"


class PaymentQueryRequest(BaseModel):
    """支付查询请求模型"""
    order_no: str = Field(..., description="订单号")


class PaymentQueryResponse(BaseModel):
    """支付查询响应模型"""
    success: bool
    message: str
    order: Optional[PaymentOrderResponse] = None
    trade_info: Optional[Dict[str, Any]] = None


class RefundRequest(BaseModel):
    """退款请求模型"""
    order_no: str = Field(..., description="订单号")
    refund_amount: Optional[Decimal] = Field(None, description="退款金额（不填则全额退款）")
    refund_reason: str = Field("用户申请退款", description="退款原因")
    
    @validator('refund_amount')
    def validate_refund_amount(cls, v):
        if v is not None and v <= 0:
            raise ValueError('退款金额必须大于0')
        return v


class RefundResponse(BaseModel):
    """退款响应模型"""
    success: bool
    message: str
    refund_no: Optional[str] = None


class CreditStatisticsResponse(BaseModel):
    """积分统计响应模型"""
    total_users: int
    total_credits_sold: int
    total_credits_consumed: int
    total_revenue: Decimal
    active_users_today: int
    orders_today: int
    revenue_today: Decimal


class UserOrderListRequest(BaseModel):
    """用户订单列表请求模型"""
    status: Optional[str] = Field(None, description="订单状态过滤")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")


class UserOrderListResponse(BaseModel):
    """用户订单列表响应模型"""
    success: bool
    message: str
    orders: List[PaymentOrderResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class CreditTransactionListRequest(BaseModel):
    """积分交易记录列表请求模型"""
    transaction_type: Optional[str] = Field(None, description="交易类型过滤")
    service_type: Optional[str] = Field(None, description="服务类型过滤")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")


class CreditTransactionListResponse(BaseModel):
    """积分交易记录列表响应模型"""
    success: bool
    message: str
    transactions: List[CreditTransactionResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class RedeemCodeRequest(BaseModel):
    """兑换码兑换请求模型"""
    redeem_code: str = Field(..., min_length=1, max_length=50, description="兑换码")

    @validator('redeem_code')
    def validate_redeem_code(cls, v):
        if not v or not v.strip():
            raise ValueError('兑换码不能为空')
        return v.strip().upper()


class RedeemCodeResponse(BaseModel):
    """兑换码兑换响应模型"""
    success: bool
    message: str
    credits_received: Optional[int] = None
    new_balance: Optional[int] = None
    package_name: Optional[str] = None
    package_expires_at: Optional[str] = None
    reward_type: Optional[str] = None  # "credits" 或 "package"

    class Config:
        from_attributes = True
