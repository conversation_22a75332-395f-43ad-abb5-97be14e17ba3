"""
积分付费系统配置
包含积分换算比例、支付配置等
"""

from typing import Dict, List, Optional
from decimal import Decimal
import logging
import asyncio
from datetime import datetime, timedelta

# 导入路径解析工具
try:
    from .utils import get_alipay_private_key_path, get_alipay_public_key_path, validate_key_files
except ImportError:
    # 如果utils模块不存在，使用默认路径
    def get_alipay_private_key_path():
        return "keys/app_private_key.pem"
    def get_alipay_public_key_path():
        return "keys/alipay_public_key.pem"
    def validate_key_files():
        return False

logger = logging.getLogger(__name__)


class CreditPaymentConfig:
    """积分付费系统配置类"""

    # 积分换算配置
    TOKENS_PER_CREDIT = 175  # 1积分 = 175 tokens

    # 数据库配置缓存
    _db_packages_cache: Optional[Dict[str, Dict]] = None
    _cache_timestamp: Optional[datetime] = None
    _cache_ttl_seconds = 300  # 缓存5分钟
    _cache_lock = asyncio.Lock()
    
    # 充值套餐配置（按年付费订阅制，积分为年度总量）
    RECHARGE_PACKAGES: Dict[str, Dict] = {
        "trial_package": {
            "amount": Decimal("29.90"),  # 体验套餐价格：29.9元
            "credits": 20,  # 体验套餐积分：20积分
            "bonus": 0,  # 无赠送
            "name": "体验套餐",
            "description": "29.9元体验套餐，包含20积分，有效期3天，适合新用户体验",
            "billing_cycle": "trial",  # 体验套餐
            "monthly_price": Decimal("29.90"),  # 一次性价格
            "min_users": 1,  # 最少用户数
            "max_users": 1,  # 最多用户数
            "validity_days": 3,  # 有效期3天
            "features": ["不限次数选校方案匹配", "20积分", "数据方案导出", "定校书不限项目个数"],
            "is_trial": True  # 标记为体验套餐
        },
        "personal_standard": {
            "amount": Decimal("1080.00"),  # 90元/人/月 × 12月 = 1080元/年
            "credits": 500,  # 年度总积分量：500积分/年
            "bonus": 0,  # 无赠送
            "name": "个人标准版",
            "description": "90元/人/月，按年付费，包含500积分/年，适合个人用户日常使用",
            "billing_cycle": "yearly",  # 按年付费
            "monthly_price": Decimal("90.00"),  # 月均价格
            "min_users": 1,  # 最少用户数
            "max_users": 1,  # 最多用户数
            "features": ["基础AI功能", "标准客服支持", "数据导出"]
        },
        "personal_professional": {
            "amount": Decimal("1680.00"),  # 140元/人/月 × 12月 = 1680元/年
            "credits": 1000,  # 年度总积分量：1000积分/年
            "bonus": 0,  # 无赠送
            "name": "个人专业版",
            "description": "140元/人/月，按年付费，包含1000积分/年，适合专业用户深度使用",
            "billing_cycle": "yearly",  # 按年付费
            "monthly_price": Decimal("140.00"),  # 月均价格
            "min_users": 1,  # 最少用户数
            "max_users": 1,  # 最多用户数
            "features": ["高级AI功能", "优先客服支持", "高级数据分析", "API访问"]
        },
        "business_flagship": {
            "amount": Decimal("1920.00"),  # 160元/人/月 × 12月 = 1920元/年
            "credits": 1000,  # 年度总积分量：1000积分/年
            "bonus": 0,  # 无赠送
            "name": "商业旗舰版",
            "description": "160元/人/月，按年付费，包含1000积分/年，适合小型团队使用",
            "billing_cycle": "yearly",  # 按年付费
            "monthly_price": Decimal("160.00"),  # 月均价格（原价）
            "min_users": 1,  # 最少1人（可以少于5人购买）
            "max_users": 999,  # 最多999人
            "bulk_discount": {
                "threshold": 5,  # 5人起购享受折扣
                "discount_rate": Decimal("0.8"),  # 八折优惠
                "discounted_amount": Decimal("1536.00"),  # 128元/人/月 × 12月 = 1536元/年（八折后）
                "discounted_monthly_price": Decimal("128.00")  # 八折后月均价格
            },
            "features": ["全部AI功能", "专属客服支持", "团队协作", "高级分析", "定制化服务"]
        },
        "enterprise_flagship": {
            "amount": Decimal("0.00"),  # 需要咨询定价
            "credits": 0,  # 根据需求定制
            "bonus": 0,
            "name": "企业旗舰版",
            "description": "企业级解决方案，需要电话咨询定价，提供定制化服务",
            "billing_cycle": "yearly",  # 按年付费
            "monthly_price": Decimal("0.00"),  # 需要咨询
            "min_users": 1,  # 最少1人
            "max_users": 999999,  # 无限制
            "contact_required": True,  # 需要联系销售
            "contact_phone": "************",  # 咨询电话
            "features": ["企业级AI功能", "7×24专属支持", "私有化部署", "定制开发", "SLA保障", "数据安全认证"]
        }
    }

    # 单独积分充值定价表（金额 -> 积分）
    CREDIT_BUNDLES = [
        {"amount": Decimal("99"), "credits": 200, "name": "体验积分包", "description": "200积分，可写约6～7篇文书"},
        {"amount": Decimal("499"), "credits": 1100, "name": "基础积分包", "description": "1100积分，可写约28～38篇文书"},
        {"amount": Decimal("999"), "credits": 2300, "name": "进阶积分包", "description": "2300积分，可写约58～77篇文书"},
        {"amount": Decimal("1999"), "credits": 4800, "name": "专业积分包", "description": "4800积分，可写约120～160篇文书"},
        {"amount": Decimal("5999"), "credits": 15000, "name": "企业积分包", "description": "15000积分，可写约375～500篇文书"},
    ]

    @classmethod
    def get_credit_bundles(cls) -> List[Dict]:
        """获取单独积分充值定价表"""
        return cls.CREDIT_BUNDLES.copy()

    @classmethod
    def get_credits_for_amount(cls, amount: Decimal) -> int:
        """根据金额匹配对应的积分（严格匹配）"""
        for bundle in cls.CREDIT_BUNDLES:
            if bundle["amount"] == amount:
                return int(bundle["credits"])  # 保证为int
        return 0


    # 支付方式配置
    PAYMENT_METHODS: List[str] = ["alipay", "wechat"]
    
    # 订单配置
    ORDER_EXPIRE_MINUTES = 30  # 订单过期时间（分钟）
    ORDER_NO_PREFIX = "TSE"  # 订单号前缀
    
    # 支付宝配置
    @classmethod
    def get_alipay_config(cls) -> Dict:
        """
        获取支付宝配置，使用动态路径解析

        Returns:
            Dict: 支付宝配置字典
        """
        try:
            # 使用动态路径解析
            private_key_path = get_alipay_private_key_path()
            public_key_path = get_alipay_public_key_path()

            # 验证密钥文件
            if not validate_key_files():
                logger.warning("密钥文件验证失败，请检查密钥文件是否存在且权限正确")

            # 根据环境动态设置回调URL
            base_url = cls._get_base_url()

            config = {
                # "app_id": "9021000150651433",  # 沙箱应用ID
                "app_id": "2021005176601884",  # 生产应用ID
                "private_key_path": private_key_path,
                "alipay_public_key_path": public_key_path,
                "sign_type": "RSA2",
                "debug": False,  # 生产环境
                # "gateway": "https://openapi-sandbox.dl.alipaydev.com/gateway.do",  # 沙箱网关
                "gateway": "https://openapi.alipay.com/gateway.do",  # 生产环境网关
                "notify_url": f"{base_url}/api/credit-payment/alipay/notify",  # 异步通知URL
                "return_url": f"{base_url}/api/credit-payment/alipay/return"   # 同步返回URL
            }

            logger.info(f"支付宝配置加载成功，私钥路径: {private_key_path}, 回调URL: {base_url}")
            return config

        except Exception as e:
            logger.error(f"加载支付宝配置失败: {e}")
            # 返回默认配置（用于向后兼容）
            base_url = cls._get_base_url()
            return {
                "app_id": "2021005176601884",
                "private_key_path": "keys/app_private_key.pem",
                "alipay_public_key_path": "keys/alipay_public_key.pem",
                "sign_type": "RSA2",
                "debug": False,
                "gateway": "https://openapi.alipay.com/gateway.do",
                "notify_url": f"{base_url}/api/credit-payment/alipay/notify",
                "return_url": f"{base_url}/api/credit-payment/alipay/return"
            }

    @classmethod
    def _get_base_url(cls) -> str:
        """
        根据环境获取基础URL

        Returns:
            str: 基础URL
        """
        import os

        # Docker环境检测
        if os.path.exists('/app') and os.path.exists('/app/app'):
            # Docker环境，使用nginx代理的地址
            return "http://localhost"  # 通过nginx代理访问

        # 本地开发环境
        return "http://localhost:8000"

    # 为了向后兼容，保留静态配置属性
    _alipay_config_cache = None

    @classmethod
    def get_alipay_config_cached(cls) -> Dict:
        """
        获取缓存的支付宝配置，避免重复加载

        Returns:
            Dict: 支付宝配置字典
        """
        if cls._alipay_config_cache is None:
            cls._alipay_config_cache = cls.get_alipay_config()
        return cls._alipay_config_cache

    @property
    def ALIPAY_CONFIG(self) -> Dict:
        """
        向后兼容的支付宝配置属性

        Returns:
            Dict: 支付宝配置字典
        """
        return self.get_alipay_config_cached()
    
    # 微信支付配置（预留）
    @classmethod
    def get_wechat_config(cls) -> Dict:
        """获取微信支付配置"""
        base_url = cls._get_base_url()
        return {
            "app_id": "your_wechat_app_id",
            "mch_id": "your_mch_id",
            "api_key": "your_api_key",
            "notify_url": f"{base_url}/api/credit-payment/wechat/notify"
        }

    # 向后兼容的静态配置
    @property
    def WECHAT_CONFIG(self) -> Dict:
        return self.get_wechat_config()
    
    # 积分消费配置
    CREDIT_CONSUMPTION_CONFIG = {
        "ai_selection": {
            "min_credits": 10,  # 最少消费积分
            "max_credits": 1000,  # 最多消费积分
            "description": "AI选校服务"
        },
        "ai_writing": {
            "min_credits": 5,
            "max_credits": 500,
            "description": "AI文书写作服务"
        }
    }
    
    # 安全配置
    SECURITY_CONFIG = {
        "max_daily_recharge_amount": Decimal("500000.00"),  # 每日最大充值金额
        "max_single_recharge_amount": Decimal("500000.00"),  # 单次最大充值金额
        "min_recharge_amount": Decimal("0.01"),  # 最小充值金额
        "signature_timeout": 300,  # 签名超时时间（秒）
        "max_retry_times": 3  # 最大重试次数
    }
    
    @classmethod
    def get_package_by_id(cls, package_id: str) -> Dict:
        """
        根据套餐ID获取套餐信息（同步方法，使用硬编码配置）
        注意：此方法为向后兼容保留，建议使用 get_package_by_id_async
        """
        package_info = cls.RECHARGE_PACKAGES.get(package_id)
        if package_info:
            return {
                "package_id": package_id,
                **package_info
            }
        return None

    @classmethod
    def calculate_package_price(cls, package_id: str, user_count: int = 1) -> Dict:
        """
        计算套餐价格（考虑用户数量和批量折扣）

        Args:
            package_id: 套餐ID
            user_count: 用户数量

        Returns:
            Dict: 包含价格计算结果的字典
        """
        package = cls.get_package_by_id(package_id)
        if not package:
            return None

        # 企业旗舰版需要咨询
        if package.get("contact_required"):
            return {
                "package_id": package_id,
                "package_name": package["name"],
                "user_count": user_count,
                "contact_required": True,
                "contact_phone": package.get("contact_phone", "************"),
                "total_amount": Decimal("0.00"),
                "unit_price": Decimal("0.00"),
                "credits_per_user": 0,
                "total_credits": 0,
                "discount_applied": False
            }

        # 检查用户数量限制
        min_users = package.get("min_users", 1)
        max_users = package.get("max_users", 999999)

        if user_count < min_users or user_count > max_users:
            return None

        # 基础价格计算
        unit_price = package["amount"]
        total_credits = package["credits"] * user_count

        # 检查是否有批量折扣
        discount_applied = False
        bulk_discount = package.get("bulk_discount")

        if bulk_discount and user_count >= bulk_discount["threshold"]:
            # 应用批量折扣
            unit_price = bulk_discount["discounted_amount"]
            discount_applied = True

        total_amount = unit_price * user_count

        return {
            "package_id": package_id,
            "package_name": package["name"],
            "user_count": user_count,
            "unit_price": unit_price,
            "total_amount": total_amount,
            "credits_per_user": package["credits"],
            "total_credits": total_credits,
            "discount_applied": discount_applied,
            "billing_cycle": package["billing_cycle"],
            "monthly_price": bulk_discount.get("discounted_monthly_price", package["monthly_price"]) if discount_applied else package["monthly_price"],
            "features": package["features"]
        }
    @classmethod
    async def calculate_package_price_async(cls, package_id: str, user_count: int = 1, db=None) -> Optional[Dict]:
        """
        异步计算套餐价格（考虑用户数量和批量折扣）

        与同步版本 calculate_package_price 返回结构和逻辑保持一致，
        但套餐信息来源使用 await get_package_by_id_async(package_id, db)。
        """
        try:
            package = await cls.get_package_by_id_async(package_id, db)
        except ValueError:
            # 与同步逻辑保持一致：不存在则返回 None
            return None

        # 企业旗舰版需要咨询
        if package.get("contact_required"):
            return {
                "package_id": package_id,
                "package_name": package["name"],
                "user_count": user_count,
                "contact_required": True,
                "contact_phone": package.get("contact_phone", "************"),
                "total_amount": Decimal("0.00"),
                "unit_price": Decimal("0.00"),
                "credits_per_user": 0,
                "total_credits": 0,
                "discount_applied": False
            }

        # 检查用户数量限制
        min_users = package.get("min_users", 1)
        max_users = package.get("max_users", 999999)
        if user_count < min_users or user_count > max_users:
            return None

        # 基础价格计算
        unit_price = package["amount"]
        total_credits = package["credits"] * user_count

        # 检查是否有批量折扣
        discount_applied = False
        bulk_discount = package.get("bulk_discount")
        if bulk_discount and user_count >= bulk_discount["threshold"]:
            unit_price = bulk_discount["discounted_amount"]
            discount_applied = True

        total_amount = unit_price * user_count

        return {
            "package_id": package_id,
            "package_name": package["name"],
            "user_count": user_count,
            "unit_price": unit_price,
            "total_amount": total_amount,
            "credits_per_user": package["credits"],
            "total_credits": total_credits,
            "discount_applied": discount_applied,
            "billing_cycle": package["billing_cycle"],
            "monthly_price": (bulk_discount.get("discounted_monthly_price", package["monthly_price"]) if discount_applied else package["monthly_price"]) if bulk_discount else package["monthly_price"],
            "features": package["features"],
        }


    
    @classmethod
    def tokens_to_credits(cls, tokens: int) -> int:
        """将tokens转换为积分"""
        return max(1, (tokens + cls.TOKENS_PER_CREDIT - 1) // cls.TOKENS_PER_CREDIT)  # 向上取整
    
    @classmethod
    def credits_to_tokens(cls, credits: int) -> int:
        """将积分转换为tokens"""
        return credits * cls.TOKENS_PER_CREDIT

    @classmethod
    async def _get_packages_from_db(cls, db=None) -> Dict[str, Dict]:
        """
        从数据库获取套餐配置

        Args:
            db: 数据库会话（可选）

        Returns:
            套餐配置字典
        """
        try:
            # 动态导入避免循环依赖
            from app.models.credit_payment import PackageConfig
            from sqlalchemy import select

            if db is None:
                # 如果没有提供数据库会话，尝试创建一个
                from app.db.database import AsyncSessionLocal
                async with AsyncSessionLocal() as session:
                    query = select(PackageConfig).where(PackageConfig.is_active == True).order_by(PackageConfig.sort_order)
                    result = await session.execute(query)
                    configs = result.scalars().all()
            else:
                query = select(PackageConfig).where(PackageConfig.is_active == True).order_by(PackageConfig.sort_order)
                result = await db.execute(query)
                configs = result.scalars().all()

            # 转换为字典格式
            packages_dict = {}
            for config in configs:
                packages_dict[config.package_id] = config.to_config_dict()

            logger.info(f"从数据库加载套餐配置: {len(packages_dict)} 个套餐")
            return packages_dict

        except Exception as e:
            logger.warning(f"从数据库获取套餐配置失败，使用硬编码配置: {e}")
            return {}

    @classmethod
    async def get_recharge_packages(cls, db=None) -> Dict[str, Dict]:
        """
        获取充值套餐配置（优先从数据库，fallback到硬编码）

        Args:
            db: 数据库会话（可选）

        Returns:
            套餐配置字典
        """
        async with cls._cache_lock:
            # 检查缓存是否有效
            if (cls._cache_timestamp and
                datetime.now() - cls._cache_timestamp < timedelta(seconds=cls._cache_ttl_seconds) and
                cls._db_packages_cache is not None):
                logger.debug("使用缓存的套餐配置")
                return cls._db_packages_cache.copy()

            try:
                # 从数据库获取配置
                db_packages = await cls._get_packages_from_db(db)

                # 如果数据库中有配置，使用数据库配置
                if db_packages:
                    cls._db_packages_cache = db_packages
                    cls._cache_timestamp = datetime.now()
                    logger.info(f"使用数据库套餐配置: {len(db_packages)} 个套餐")
                    return db_packages.copy()
                else:
                    # 如果数据库中没有配置，使用硬编码配置
                    logger.info("数据库中没有套餐配置，使用硬编码配置")
                    cls._db_packages_cache = cls.RECHARGE_PACKAGES.copy()
                    cls._cache_timestamp = datetime.now()
                    return cls.RECHARGE_PACKAGES.copy()

            except Exception as e:
                logger.error(f"获取套餐配置失败: {e}")
                # 如果有缓存，使用缓存
                if cls._db_packages_cache:
                    logger.warning("使用过期缓存的套餐配置")
                    return cls._db_packages_cache.copy()
                # 否则使用硬编码配置
                logger.warning("使用硬编码套餐配置作为fallback")
                return cls.RECHARGE_PACKAGES.copy()

    @classmethod
    async def clear_packages_cache(cls):
        """清除套餐配置缓存"""
        async with cls._cache_lock:
            cls._db_packages_cache = None
            cls._cache_timestamp = None
            logger.debug("套餐配置缓存已清除")

    @classmethod
    async def get_package_by_id_async(cls, package_id: str, db=None) -> Dict:
        """
        异步方式根据套餐ID获取充值套餐信息

        Args:
            package_id: 套餐ID
            db: 数据库会话（可选）

        Returns:
            套餐信息字典

        Raises:
            ValueError: 套餐不存在
        """
        packages = await cls.get_recharge_packages(db)
        package_info = packages.get(package_id)
        if not package_info:
            raise ValueError(f"套餐不存在: {package_id}")

        return {
            "package_id": package_id,
            **package_info
        }

    @classmethod
    async def get_credits_by_package_async(cls, package_id: str, user_count: int = 1, db=None) -> int:
        """
        异步方式根据套餐ID和用户数量获取积分数量

        Args:
            package_id: 套餐ID
            user_count: 用户数量
            db: 数据库会话（可选）

        Returns:
            总积分数量（套餐积分 × 用户数量）

        Raises:
            ValueError: 套餐不存在或需要咨询定价
        """
        packages = await cls.get_recharge_packages(db)
        package_info = packages.get(package_id)
        if not package_info:
            raise ValueError(f"套餐不存在: {package_id}")

        if package_info.get("contact_required"):
            raise ValueError(f"企业套餐需要咨询定价: {package_id}")

        # 积分 = 套餐积分 × 用户数量
        return package_info["credits"] * user_count
