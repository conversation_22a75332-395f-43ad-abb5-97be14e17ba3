"""
积分付费系统API路由
提供积分管理、支付处理、订单管理等API接口
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, Form
from fastapi.responses import RedirectResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime
import logging
import asyncio

from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.credit_payment.schemas import (
    CreditAccountResponse,
    CreatePaymentOrderRequest,
    CreatePaymentOrderResponse,
    PaymentOrderResponse,
    CreditConsumptionRequest,
    CreditConsumptionResponse,
    CreditBundleResponse,
    RechargePackageResponse,
    PackagePriceCalculationRequest,
    PackagePriceCalculationResponse,
    PaymentQueryRequest,
    PaymentQueryResponse,
    RefundRequest,
    RefundResponse,
    UserOrderListRequest,
    UserOrderListResponse,
    CreditTransactionListRequest,
    CreditTransactionListResponse,
    AlipayNotifyRequest,
    RedeemCodeRequest,
    RedeemCodeResponse
)
from app.credit_payment.services.credit_service import CreditService
from app.credit_payment.services.package_service import PackageService
from app.credit_payment.services.payment_service import PaymentService
from app.credit_payment.services.alipay_service import AlipayService
from app.credit_payment.services.redeem_code_service import RedeemCodeService
from app.credit_payment.config import CreditPaymentConfig

from app.core.config import settings

# 创建路由器
router = APIRouter(tags=["积分付费系统"])

# 设置日志
logger = logging.getLogger(__name__)


# ==================== 健康检查和系统信息 ====================

@router.get("/health", summary="健康检查")
async def health_check():
    """
    积分付费系统健康检查
    """
    return {
        "status": "healthy",
        "service": "credit_payment",
        "message": "积分付费系统运行正常"
    }


@router.get("/info", summary="系统信息")
async def system_info():
    """
    获取积分付费系统信息
    """
    return {
        "service_name": "积分付费系统",
        "version": "1.0.0",
        "description": "提供积分管理、支付处理、订单管理等功能",
        "features": [
            "积分账户管理",
            "支付订单处理",
            "积分交易记录",
            "支付宝集成",
            "订单状态查询"
        ]
    }


# ==================== 积分管理相关接口 ====================


async def _validate_package_refund(order, refund_amount: Decimal):
    """
    验证套餐退款规则

    Args:
        order: 支付订单对象
        refund_amount: 退款金额

    Raises:
        ValueError: 当退款不符合套餐规则时
    """
    # 如果没有套餐ID，允许任意金额退款（向后兼容）
    if not hasattr(order, 'package_id') or not order.package_id:
        logger.info(f"订单 {order.order_no} 没有套餐ID，允许任意金额退款")
        return

    # 获取套餐配置（优先使用异步方法）
    try:
        package_info = await CreditPaymentConfig.get_package_by_id_async(order.package_id)
    except ValueError:
        # fallback到硬编码配置
        package_info = CreditPaymentConfig.RECHARGE_PACKAGES.get(order.package_id)
        if not package_info:
            # 套餐不存在，允许退款但记录警告
            logger.warning(f"订单 {order.order_no} 的套餐 {order.package_id} 不存在")
            return

    # 全额退款总是允许的
    if refund_amount == order.amount:
        return

    # 部分退款必须按套餐单位进行
    package_amount = package_info["amount"]

    # 检查是否有批量折扣
    if package_info.get("bulk_discount") and order.amount < package_amount:
        # 使用折扣价格
        unit_price = package_info["bulk_discount"]["discounted_amount"]
    else:
        # 使用原价
        unit_price = package_amount

    # 验证退款金额是否为套餐单价的整数倍
    if refund_amount % unit_price != 0:
        raise ValueError(
            f"部分退款必须按套餐单位进行。套餐单价: {unit_price}元，"
            f"退款金额: {refund_amount}元。请输入 {unit_price}元 的整数倍金额。"
        )

    # 验证退款用户数量不超过原购买数量
    refund_user_count = int(refund_amount / unit_price)
    total_user_count = int(order.amount / unit_price)

    if refund_user_count > total_user_count:
        raise ValueError(
            f"退款用户数量({refund_user_count})不能超过原购买数量({total_user_count})"
        )



@router.get("/credit/account", response_model=CreditAccountResponse)
async def get_credit_account(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户当前身份的积分账户信息"""
    try:

        # 使用重构后的积分服务获取当前身份的积分账户信息
        account_info = await CreditService.get_credit_summary(current_user, db)
        if not account_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="积分账户不存在"
            )
        return account_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取积分账户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取积分账户失败"
        )


@router.get("/credit/balance")
async def get_credit_balance(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户当前身份的积分余额"""
    try:
        # 使用重构后的积分服务获取当前身份的积分余额
        balance = await CreditService.get_credit_balance(current_user, db)
        
        # 获取身份信息用于响应
        from app.core.data_isolation import DataIsolationFilter
        identity_type, organization_id = DataIsolationFilter.get_user_data_scope(current_user)
        identity_name = "组织身份" if identity_type == 'organization' else "个人身份"
        
        return {
            "success": True,
            "balance": balance,
            "identity_type": identity_type,
            "identity_name": identity_name,
            "organization_id": organization_id,
            "message": f"获取{identity_name}余额成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取积分余额失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取积分余额失败"
        )


@router.post("/credit/consume", response_model=CreditConsumptionResponse)
async def consume_credits(
    request: CreditConsumptionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """消费用户当前身份的积分"""
    try:
        # 使用重构后的积分服务消费当前身份的积分
        success, message = await CreditService.consume_credits(
            user=current_user,
            credits=request.credits,
            service_type=request.service_type,
            operation_type=request.operation_type,
            tokens_consumed=request.tokens_consumed,
            request_id=request.request_id,
            description=request.description,
            db=db
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )

        # 获取当前身份的剩余余额
        remaining_balance = await CreditService.get_credit_balance(current_user, db)

        return CreditConsumptionResponse(
            success=True,
            message=message,
            remaining_balance=remaining_balance
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"积分消费失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="积分消费失败"
        )


@router.post("/redeem-code", response_model=RedeemCodeResponse)
async def redeem_code(
    request: RedeemCodeRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """兑换码兑换"""
    try:
        success, message, credits_received, new_balance, package_name, package_expires_at, reward_type = await RedeemCodeService.redeem_code(
            user=current_user,
            code=request.redeem_code,
            db=db
        )

        return RedeemCodeResponse(
            success=success,
            message=message,
            credits_received=credits_received,
            new_balance=new_balance,
            package_name=package_name,
            package_expires_at=package_expires_at,
            reward_type=reward_type
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"兑换码兑换失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="兑换失败，请稍后重试"
        )


@router.get("/packages", response_model=List[RechargePackageResponse])
async def get_recharge_packages():
    """获取充值套餐列表（公开接口，无需登录）"""
    try:
        # 优先从数据库获取套餐配置
        packages_config = await CreditPaymentConfig.get_recharge_packages()

        packages = []
        for package_id, package_info in packages_config.items():
            packages.append(RechargePackageResponse(
                package_id=package_id,
                name=package_info["name"],
                description=package_info["description"],
                amount=package_info["amount"],
                credits=package_info["credits"],  # 年度总积分量
                bonus=package_info["bonus"],
                total_credits=package_info["credits"] + package_info["bonus"],
                billing_cycle=package_info["billing_cycle"],
                monthly_price=package_info["monthly_price"],
                min_users=package_info["min_users"],
                max_users=package_info["max_users"],
                features=package_info["features"],
                contact_required=package_info.get("contact_required", False),
                contact_phone=package_info.get("contact_phone"),
                bulk_discount=package_info.get("bulk_discount"),
                is_test=package_info.get("is_test", False),
                test_purpose=package_info.get("test_purpose")
            ))
        return packages
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取充值套餐失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取充值套餐失败"
        )

@router.get("/credit-bundles", response_model=List[CreditBundleResponse])
async def get_credit_bundles():
    """获取单独积分充值档位列表（公开接口，无需登录）"""
    bundles = CreditPaymentConfig.get_credit_bundles()
    return [CreditBundleResponse(**b) for b in bundles]



@router.post("/packages/calculate-price", response_model=PackagePriceCalculationResponse)
async def calculate_package_price(request: PackagePriceCalculationRequest, db: AsyncSession = Depends(get_db)):
    """计算套餐价格（考虑用户数量和批量折扣）"""
    try:
        price_info = await CreditPaymentConfig.calculate_package_price_async(
            package_id=request.package_id,
            user_count=request.user_count,
            db=db
        )

        if not price_info:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="套餐不存在或用户数量超出限制"
            )

        return PackagePriceCalculationResponse(**price_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"计算套餐价格失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="计算套餐价格失败"
        )


@router.post("/payment/create", response_model=CreatePaymentOrderResponse)
async def create_payment_order(
    request: CreatePaymentOrderRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建支付订单"""
    try:
        logger.info(f"用户 {current_user.id} 创建支付订单: {request.model_dump()}")

        # 创建订单
        success, message, order = await PaymentService.create_payment_order(
            user=current_user,
            amount=request.amount,
            payment_method=request.payment_method,
            package_id=request.package_id,
            user_count=request.user_count,
            remark=request.remark,
            db=db
        )

        if not success:
            logger.warning(f"订单创建失败: {message}")
            return CreatePaymentOrderResponse(
                success=False,
                message=message
            )

        # 根据支付方式生成支付URL
        payment_url = None
        if request.payment_method == "alipay":
            alipay_service = AlipayService()
            url_success, url_message, payment_url = await alipay_service.create_payment_url(order)
            if not url_success:
                logger.error(f"生成支付URL失败: {url_message}")
                return CreatePaymentOrderResponse(
                    success=False,
                    message=f"生成支付URL失败: {url_message}"
                )

        logger.info(f"订单创建成功: {order.order_no}")
        return CreatePaymentOrderResponse(
            success=True,
            message="订单创建成功",
            order=PaymentOrderResponse.from_orm(order),
            payment_url=payment_url
        )
        
    except HTTPException:
        # 重新抛出HTTP异常（如401认证错误）
        raise
    except Exception as e:
        logger.error(f"创建支付订单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建支付订单失败"
        )


@router.post("/payment/query", response_model=PaymentQueryResponse)
async def query_payment(
    request: PaymentQueryRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """查询支付状态"""
    try:
        order = await PaymentService.get_order_by_no(request.order_no, db)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 验证订单所有权
        if order.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单"
            )

        # 如果订单已经是最终状态，直接返回，不需要查询第三方
        if order.status in ["paid", "failed", "cancelled", "refunded"]:
            return PaymentQueryResponse(
                success=True,
                message="查询成功",
                order=PaymentOrderResponse.from_orm(order),
                trade_info=None
            )

        # 只有pending状态的支付宝订单才查询第三方状态
        trade_info = None
        if order.payment_method == "alipay" and order.status == "pending":
            try:
                # 设置查询超时，避免长时间等待
                alipay_service = AlipayService()

                # 使用asyncio.wait_for设置总超时时间
                query_task = alipay_service.query_trade(order.order_no)
                query_success, query_message, trade_info = await asyncio.wait_for(
                    query_task,
                    timeout=15.0  # 15秒总超时
                )

                # 如果查询到支付成功，更新订单状态
                if query_success and trade_info and trade_info.get("trade_status") in ["TRADE_SUCCESS", "TRADE_FINISHED"]:
                    await PaymentService.complete_payment(
                        order_no=order.order_no,
                        trade_no=trade_info.get("trade_no"),
                        db=db
                    )
                    # 重新获取订单信息
                    order = await PaymentService.get_order_by_no(request.order_no, db)

            except asyncio.TimeoutError:
                logger.warning(f"支付查询超时: {request.order_no}")
                # 超时时返回当前订单状态，不影响用户体验
                return PaymentQueryResponse(
                    success=True,
                    message="查询成功（第三方查询超时，返回当前状态）",
                    order=PaymentOrderResponse.from_orm(order),
                    trade_info=None
                )
            except Exception as e:
                logger.warning(f"第三方支付查询失败: {e}")
                # 第三方查询失败时，仍然返回当前订单状态
                return PaymentQueryResponse(
                    success=True,
                    message="查询成功（第三方查询失败，返回当前状态）",
                    order=PaymentOrderResponse.from_orm(order),
                    trade_info=None
                )

        return PaymentQueryResponse(
            success=True,
            message="查询成功",
            order=PaymentOrderResponse.from_orm(order),
            trade_info=trade_info
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询支付状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询支付状态失败"
        )



@router.get("/alipay/return")
async def alipay_return(request: Request):
    """
    支付宝同步返回处理
    注意：由于同步返回的不可靠性，支付结果必须以异步通知或查询接口返回为准
    """
    try:
        # 获取URL参数
        query_params = dict(request.query_params)

        logger.info(f"收到支付宝同步返回: {query_params}")
        logger.info(f"原始URL: {request.url}")

        # 获取订单号（同步返回最重要的是订单号）
        order_no = query_params.get("out_trade_no")
        trade_no = query_params.get("trade_no")

        if not order_no:
            logger.error("支付宝同步返回缺少订单号")
            return RedirectResponse(url=f"{settings.FRONTEND_URL}/account/payment-result?status=failed&reason=missing_order")

        # 根据支付宝官方建议：同步返回不可靠，不应用于业务逻辑判断
        # 这里我们直接重定向到结果页面，让用户通过查询接口确认真实支付状态
        logger.info(f"支付同步返回: {order_no}, 支付宝交易号: {trade_no}")
        logger.info("根据支付宝建议，跳过同步返回验签，通过查询接口确认支付状态")

        # 可选：如果需要验签调试，可以异步执行验签（不影响用户体验）
        try:
            alipay_service = AlipayService()
            verify_success, verify_message, verified_data = await alipay_service.verify_return(query_params)
            logger.info(f"同步返回验签结果: {verify_success}, 消息: {verify_message}")
        except Exception as e:
            logger.warning(f"同步返回验签异常（不影响业务流程）: {e}")

        # 重定向到前端支付结果页面，让前端查询实际支付状态
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/account/payment-result?order_no={order_no}")

    except Exception as e:
        logger.error(f"处理支付宝同步返回失败: {e}")
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/account/payment-result?status=failed&reason=system_error")


@router.post("/alipay/notify")
async def alipay_notify(request: Request, db: AsyncSession = Depends(get_db)):
    """支付宝异步通知处理"""
    try:
        # 获取表单数据
        form_data = await request.form()
        notify_data = dict(form_data)

        logger.info(f"收到支付宝异步通知: {notify_data}")

        # 验证通知
        alipay_service = AlipayService()
        verify_success, verify_message, verified_data = await alipay_service.verify_notify(notify_data)

        if not verify_success:
            logger.error(f"支付宝异步通知验证失败: {verify_message}")
            return "fail"

        # 根据支付宝文档要求，进行严格的业务验证
        order_no = verified_data.get("out_trade_no")
        trade_no = verified_data.get("trade_no")
        total_amount = verified_data.get("total_amount")
        trade_status = verified_data.get("trade_status")

        if not order_no or not trade_no:
            logger.error("支付宝异步通知缺少必要参数")
            return "fail"

        # 验证订单是否存在于商家系统中
        try:
            from app.models.credit_payment import PaymentOrder
            from sqlalchemy import select

            result = await db.execute(select(PaymentOrder).where(PaymentOrder.order_no == order_no))
            order = result.scalar_one_or_none()

            if not order:
                logger.error(f"订单不存在于商家系统中: {order_no}")
                return "fail"

            # 验证金额是否匹配
            if str(order.amount) != total_amount:
                logger.error(f"订单金额不匹配，期望: {order.amount}, 实际: {total_amount}")
                return "fail"

            # 检查订单状态，处理边缘情况
            if order.status == "cancelled":
                logger.warning(f"收到已取消订单的支付通知: {order_no}, 将自动退款")

                # 处理已取消订单的支付成功通知
                success, message = await PaymentService.handle_cancelled_order_payment(
                    order_no, trade_no, total_amount, db
                )

                if success:
                    logger.info(f"已取消订单支付处理成功（已发起退款）: {order_no}")
                    return "success"
                else:
                    logger.error(f"已取消订单支付处理失败: {message}")
                    return "fail"

            elif order.status == "paid":
                logger.info(f"订单已支付，忽略重复通知: {order_no}")
                return "success"

            elif order.status != "pending":
                logger.error(f"订单状态异常，无法处理支付: {order_no}, 状态: {order.status}")
                return "fail"

            # 验证app_id（已在verify_notify中验证）
            # 验证seller_id（如果需要的话）

            logger.info(f"异步通知业务验证通过: {order_no}, 金额: {total_amount}, 状态: {trade_status}")

        except Exception as e:
            logger.error(f"异步通知业务验证失败: {e}")
            return "fail"

        # 处理正常的待支付订单
        success, message = await PaymentService.complete_payment(order_no, trade_no, db)

        if success:
            logger.info(f"支付完成处理成功: {order_no}")
            return "success"
        else:
            logger.error(f"支付完成处理失败: {message}")
            return "fail"

    except Exception as e:
        logger.error(f"处理支付宝异步通知失败: {e}")
        return "fail"


@router.get("/orders", response_model=UserOrderListResponse)
async def get_user_orders(
    status: str = None,
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户订单列表"""
    try:
        # 验证参数
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20

        offset = (page - 1) * page_size

        # 获取订单列表
        orders = await PaymentService.get_user_orders(
            user=current_user,
            status=status,
            limit=page_size,
            offset=offset,
            db=db
        )

        # 计算总数（这里简化处理，实际应该单独查询总数）
        total = len(orders)
        total_pages = (total + page_size - 1) // page_size

        return UserOrderListResponse(
            success=True,
            message="获取订单列表成功",
            orders=[PaymentOrderResponse(**order) for order in orders],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户订单列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取订单列表失败"
        )


@router.get("/transactions", response_model=CreditTransactionListResponse)
async def get_credit_transactions(
    transaction_type: Optional[str] = None,
    service_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取积分交易记录"""
    try:
        # 验证分页参数
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20

        # 解析时间参数
        start_datetime = None
        end_datetime = None
        
        if start_date:
            try:
                start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                # 转换为naive datetime（移除时区信息）
                start_datetime = start_datetime.replace(tzinfo=None)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="开始时间格式错误，请使用ISO格式"
                )
        
        if end_date:
            try:
                end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                # 转换为naive datetime（移除时区信息）
                end_datetime = end_datetime.replace(tzinfo=None)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="结束时间格式错误，请使用ISO格式"
                )
        
        # 验证时间范围
        if start_datetime and end_datetime and start_datetime > end_datetime:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="结束时间不能早于开始时间"
            )

        # 获取交易记录
        transactions, total = await CreditService.get_credit_transactions(
            user=current_user,
            transaction_type=transaction_type,
            service_type=service_type,
            start_date=start_datetime,
            end_date=end_datetime,
            page=page,
            page_size=page_size,
            db=db
        )

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        return CreditTransactionListResponse(
            success=True,
            message="获取积分交易记录成功",
            transactions=transactions,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取积分交易记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取积分交易记录失败"
        )


@router.get("/mock-payment/{order_no}")
async def mock_payment(
    order_no: str,
    db: AsyncSession = Depends(get_db)
):
    """
    模拟支付接口（仅用于开发环境）

    此接口模拟支付宝支付成功的回调，用于开发环境中测试支付流程
    """
    try:
        # 检查是否为开发环境
        from app.credit_payment.config import CreditPaymentConfig
        alipay_config = CreditPaymentConfig.get_alipay_config()
        if not alipay_config.get("debug", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="此接口仅在开发环境中可用"
            )

        # 在单一事务中处理整个支付流程
        async with db.begin():
            # 获取订单
            order = await PaymentService._get_order_by_no_with_session(db, order_no)
            if not order:
                return {
                    "success": False,
                    "message": "订单不存在"
                }

            # 检查订单状态
            if order.status != "pending":
                return {
                    "success": False,
                    "message": f"订单状态不正确: {order.status}"
                }

            # 模拟支付成功 - 直接使用事务内方法
            trade_no = f"2025071522001{order_no[-6:]}"
            success, message = await PaymentService._complete_payment_in_transaction(
                db=db,
                order_no=order_no,
                trade_no=trade_no
            )

        # 事务完成后返回结果
        if success:
            return {
                "success": True,
                "message": "模拟支付成功",
                "order_no": order_no,
                "trade_no": trade_no
            }
        else:
            return {
                "success": False,
                "message": message
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模拟支付失败: {e}")
        return {
            "success": False,
            "message": f"模拟支付失败: {str(e)}"
        }


@router.post("/payment/refund", response_model=RefundResponse)
async def refund_payment(
    request: RefundRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """申请退款"""
    try:
        # 获取订单
        order = await PaymentService.get_order_by_no(request.order_no, db)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 验证订单所有权
        if order.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单"
            )

        # 检查订单状态
        if order.status not in ["paid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"订单状态不允许退款: {order.status}"
            )

        # 检查是否已经退款过（幂等性控制）
        if order.refund_amount > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"订单已退款，退款金额: {order.refund_amount}元"
            )

        # 确定退款金额
        refund_amount = request.refund_amount or order.amount

        # 验证退款金额
        if refund_amount > order.amount:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="退款金额不能超过订单金额"
            )

        if refund_amount <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="退款金额必须大于0"
            )

        # 验证套餐退款规则
        try:
            await _validate_package_refund(order, refund_amount)
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )

        # 调用支付宝退款接口
        if order.payment_method == "alipay":
            alipay_service = AlipayService()
            success, message, refund_no = await alipay_service.refund_trade(
                order_no=order.order_no,
                refund_amount=refund_amount,
                refund_reason=request.refund_reason
            )

            # 处理不同的退款结果
            if success:
                # 支付宝退款成功，处理本地数据库和积分
                db_success, db_message = await PaymentService.process_refund(
                    order_no=order.order_no,
                    refund_amount=refund_amount,
                    refund_reason=request.refund_reason,
                    refund_no=refund_no,
                    db=db
                )

                if not db_success:
                    # 如果本地处理失败，记录错误但告知用户退款成功
                    logger.error(f"退款本地处理失败: {db_message}, 订单号: {order.order_no}")
                    return RefundResponse(
                        success=True,
                        message=f"退款成功，但系统处理异常，请联系客服确认: {db_message}",
                        refund_no=refund_no
                    )

                return RefundResponse(
                    success=True,
                    message="退款申请成功",
                    refund_no=refund_no
                )

            elif "超时" in message or "timeout" in message.lower():
                # 超时情况，建议用户稍后查询
                return RefundResponse(
                    success=False,
                    message=f"退款请求处理中，{message}。请稍后查询退款状态确认结果。",
                    refund_no=refund_no
                )

            else:
                # 其他退款失败情况
                return RefundResponse(
                    success=False,
                    message=f"支付宝退款失败: {message}"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="暂不支持该支付方式的退款"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"申请退款失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="申请退款失败"
        )


@router.post("/payment/confirm-refund/{order_no}")
async def confirm_refund_status(
    order_no: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """确认退款状态（用于处理超时情况）"""
    try:
        # 获取订单
        order = await PaymentService.get_order_by_no(order_no, db)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 验证订单所有权
        if order.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单"
            )

        # 如果订单已经是退款状态，直接返回成功
        if order.status in ["refunded", "partial_refunded"]:
            return {
                "success": True,
                "message": "订单已退款",
                "order_status": order.status,
                "refund_amount": float(order.refund_amount) if order.refund_amount else 0
            }

        # 调用支付宝查询退款状态
        if order.payment_method == "alipay":
            alipay_service = AlipayService()

            # 先查询交易状态
            trade_success, _, trade_info = await alipay_service.query_trade(order_no)

            if trade_success and trade_info:
                refund_fee = trade_info.get("refund_fee", "0")
                if refund_fee and float(refund_fee) > 0:
                    # 发现有退款，更新本地状态
                    refund_amount = Decimal(refund_fee)

                    # 处理本地数据库和积分
                    db_success, db_message = await PaymentService.process_refund(
                        order_no=order_no,
                        refund_amount=refund_amount,
                        refund_reason="系统确认退款",
                        refund_no=f"CONFIRM_{order_no}",
                        db=db
                    )

                    if db_success:
                        return {
                            "success": True,
                            "message": "退款状态已确认并更新",
                            "order_status": "refunded",
                            "refund_amount": float(refund_amount)
                        }
                    else:
                        logger.error(f"确认退款时本地处理失败: {db_message}, 订单号: {order_no}")
                        return {
                            "success": True,
                            "message": f"退款已确认，但系统更新异常: {db_message}",
                            "order_status": order.status,
                            "refund_amount": float(refund_fee)
                        }

            # 查询退款接口
            refund_success, _, refund_info = await alipay_service.query_refund(order_no)
            if refund_success and refund_info:
                refund_status = refund_info.get("refund_status")
                if refund_status == "REFUND_SUCCESS":
                    refund_amount = Decimal(refund_info.get("refund_amount", "0"))

                    # 处理本地数据库和积分
                    db_success, db_message = await PaymentService.process_refund(
                        order_no=order_no,
                        refund_amount=refund_amount,
                        refund_reason="系统确认退款",
                        refund_no=f"CONFIRM_{order_no}",
                        db=db
                    )

                    if db_success:
                        return {
                            "success": True,
                            "message": "退款状态已确认并更新",
                            "order_status": "refunded",
                            "refund_amount": float(refund_amount)
                        }

            return {
                "success": False,
                "message": "未发现退款记录",
                "order_status": order.status,
                "refund_amount": 0
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="暂不支持该支付方式的退款确认"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认退款状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="确认退款状态失败"
        )


@router.get("/payment/query-refund/{order_no}")
async def query_refund_status(
    order_no: str,
    out_request_no: str = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """查询退款状态"""
    try:
        # 获取订单
        order = await PaymentService.get_order_by_no(order_no, db)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 验证订单所有权
        if order.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单"
            )

        # 调用支付宝退款查询接口
        if order.payment_method == "alipay":
            alipay_service = AlipayService()
            success, message, refund_info = await alipay_service.query_refund(
                order_no=order_no,
                out_request_no=out_request_no
            )

            return {
                "success": success,
                "message": message,
                "data": refund_info
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="暂不支持该支付方式的退款查询"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询退款状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询退款状态失败"
        )


@router.post("/payment/close-trade/{order_no}")
async def close_trade(
    order_no: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """关闭交易"""
    try:
        # 获取订单
        order = await PaymentService.get_order_by_no(order_no, db)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 验证订单所有权
        if order.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此订单"
            )

        # 检查订单状态
        if order.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有待支付的订单才能关闭"
            )

        # 处理支付宝订单取消逻辑
        if order.payment_method == "alipay":
            alipay_service = AlipayService()

            # 直接调用支付宝原始API获取完整的交易信息，避免重复调用
            logger.info(f"查询订单 {order_no} 在支付宝的交易状态...")
            try:
                raw_result = alipay_service.alipay.api_alipay_trade_query(out_trade_no=order_no)
                logger.info(f"支付宝查询结果: {raw_result}")

                should_close_alipay_trade = False
                close_info = None

                if raw_result.get("code") == "10000":
                    # 查询成功，有交易记录
                    trade_status = raw_result.get("trade_status")
                    trade_no = raw_result.get("trade_no")

                    logger.info(f"订单 {order_no} 在支付宝的状态: {trade_status}, 支付宝交易号: {trade_no}")

                    if trade_status == "TRADE_SUCCESS":
                        # 交易已成功，不允许取消
                        logger.error(f"订单 {order_no} 已支付成功，不允许取消")
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="订单已支付成功，无法取消。如需退款请联系客服。"
                        )
                    elif trade_status == "WAIT_BUYER_PAY":
                        # 等待买家付款，需要关闭交易
                        should_close_alipay_trade = True
                        logger.info(f"订单 {order_no} 等待买家付款，需要关闭支付宝交易")
                    elif trade_status == "TRADE_CLOSED":
                        # 交易已关闭，无需重复操作
                        logger.info(f"订单 {order_no} 在支付宝已关闭，直接取消本地订单")
                    elif trade_status == "TRADE_FINISHED":
                        # 交易完结，不允许取消
                        logger.error(f"订单 {order_no} 交易已完结，不允许取消")
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="订单交易已完结，无法取消"
                        )
                    else:
                        # 其他状态，记录日志并尝试关闭
                        logger.warning(f"订单 {order_no} 在支付宝的状态为 {trade_status}，尝试关闭交易")
                        should_close_alipay_trade = True

                else:
                    # 查询失败，检查错误类型
                    error_code = raw_result.get("code", "")
                    sub_code = raw_result.get("sub_code", "")
                    error_msg = raw_result.get("msg", "")
                    sub_msg = raw_result.get("sub_msg", "")

                    logger.info(f"支付宝查询失败: code={error_code}, sub_code={sub_code}, msg={error_msg}, sub_msg={sub_msg}")

                    if sub_code == "ACQ.TRADE_NOT_EXIST":
                        # 交易不存在，用户未扫码支付
                        logger.info(f"订单 {order_no} 在支付宝不存在交易记录，用户未扫码支付，直接取消本地订单")
                        should_close_alipay_trade = False
                    else:
                        # 其他错误，为安全起见仍尝试关闭交易
                        logger.warning(f"订单 {order_no} 查询失败但非交易不存在错误，为安全起见尝试关闭交易")
                        should_close_alipay_trade = True

            except HTTPException:
                # 重新抛出HTTP异常
                raise
            except Exception as e:
                # 查询异常，为安全起见尝试关闭交易
                logger.error(f"查询订单 {order_no} 支付宝状态异常: {e}")
                should_close_alipay_trade = True

            # 根据查询结果决定是否调用支付宝关闭交易接口
            if should_close_alipay_trade:
                logger.info(f"调用支付宝关闭交易接口，订单号: {order_no}")
                close_success, close_message, close_info = await alipay_service.close_trade(order_no)

                if not close_success:
                    # 关闭交易失败，检查是否是因为交易不存在
                    if "ACQ.TRADE_NOT_EXIST" in close_message or "交易不存在" in close_message:
                        logger.info(f"订单 {order_no} 关闭交易失败因为交易不存在，继续取消本地订单")
                    else:
                        # 其他错误，返回失败
                        logger.error(f"关闭支付宝交易失败: {close_message}")
                        return {
                            "success": False,
                            "message": f"关闭支付宝交易失败: {close_message}",
                            "data": close_info
                        }
                else:
                    logger.info(f"支付宝交易关闭成功，订单号: {order_no}")

            # 更新本地订单状态为已取消
            try:
                cancel_success, cancel_message = await PaymentService.cancel_order(order_no, "用户主动取消订单", db)
                if cancel_success:
                    logger.info(f"订单 {order_no} 本地状态已更新为 cancelled")

                    # 构建返回消息
                    if should_close_alipay_trade and close_info:
                        message = "订单取消成功，支付宝交易已关闭"
                    else:
                        message = "订单取消成功"

                    return {
                        "success": True,
                        "message": message,
                        "data": {
                            "order_no": order_no,
                            "status": "cancelled",
                            "alipay_closed": should_close_alipay_trade,
                            "close_info": close_info
                        }
                    }
                else:
                    logger.error(f"更新订单状态失败: {cancel_message}")
                    return {
                        "success": False,
                        "message": f"取消订单失败: {cancel_message}",
                        "data": None
                    }
            except Exception as e:
                logger.error(f"更新订单状态异常: {e}")
                return {
                    "success": False,
                    "message": f"取消订单失败: {str(e)}",
                    "data": None
                }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="暂不支持该支付方式的交易关闭"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"关闭交易失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="关闭交易失败"
        )