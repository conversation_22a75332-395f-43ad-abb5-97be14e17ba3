"""
积分付费系统工具函数
包含路径解析、文件处理等通用工具
"""

import os
from typing import Optional
import logging

logger = logging.getLogger(__name__)


def get_project_root() -> str:
    """
    获取项目根目录的绝对路径
    
    Returns:
        str: 项目根目录的绝对路径
    """
    # 检查是否在Docker环境中
    if os.path.exists('/app') and os.path.exists('/app/app'):
        # Docker环境：后端代码在 /app 目录
        return '/app'

    # 本地开发环境：从当前文件位置向上查找项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 当前文件在 app/credit_payment/utils.py，需要向上3级到达项目根目录
    project_root = os.path.join(current_dir, "..", "..", "..")
    return os.path.abspath(project_root)


def get_keys_directory() -> str:
    """
    获取密钥文件存储目录的绝对路径

    Returns:
        str: 密钥目录的绝对路径
    """
    project_root = get_project_root()

    # 检查是否在Docker环境中
    if project_root == '/app':
        # Docker环境：密钥文件在 /app/keys/ 目录下
        keys_dir = os.path.join(project_root, "keys")
    else:
        # 本地开发环境：密钥文件存储在 backend/keys/ 目录下
        keys_dir = os.path.join(project_root, "backend", "keys")

    return os.path.abspath(keys_dir)


def get_key_file_path(filename: str) -> str:
    """
    获取密钥文件的绝对路径
    
    Args:
        filename: 密钥文件名
        
    Returns:
        str: 密钥文件的绝对路径
        
    Raises:
        FileNotFoundError: 当密钥文件不存在时
    """
    keys_dir = get_keys_directory()
    key_path = os.path.join(keys_dir, filename)
    
    if not os.path.exists(key_path):
        # 在Docker环境中，如果密钥文件不存在，记录警告但不抛出异常
        if get_project_root() == '/app':
            logger.warning(f"Docker环境中密钥文件不存在: {key_path} - 支付功能将不可用")
            return key_path  # 返回路径，但文件不存在
        else:
            raise FileNotFoundError(f"密钥文件不存在: {key_path}")

    # 检查文件权限（应该是600或更严格）
    file_stat = os.stat(key_path)
    file_mode = file_stat.st_mode & 0o777

    if file_mode & 0o077:  # 检查是否有组或其他用户的权限
        logger.warning(f"密钥文件权限不安全: {key_path} (权限: {oct(file_mode)}) - 建议设置为600")
    else:
        logger.debug(f"密钥文件权限安全: {key_path} (权限: {oct(file_mode)})")
    
    return key_path


def get_alipay_private_key_path() -> str:
    """
    获取支付宝应用私钥文件路径
    
    Returns:
        str: 支付宝应用私钥文件的绝对路径
    """
    try:
        return get_key_file_path("app_private_key.pem")
    except FileNotFoundError:
        # 在Docker环境中返回默认路径
        if get_project_root() == '/app':
            return os.path.join(get_keys_directory(), "app_private_key.pem")
        raise


def get_alipay_public_key_path() -> str:
    """
    获取支付宝公钥文件路径
    
    Returns:
        str: 支付宝公钥文件的绝对路径
    """
    try:
        return get_key_file_path("alipay_public_key.pem")
    except FileNotFoundError:
        # 在Docker环境中返回默认路径
        if get_project_root() == '/app':
            return os.path.join(get_keys_directory(), "alipay_public_key.pem")
        raise


def validate_key_files() -> bool:
    """
    验证所有必需的密钥文件是否存在且权限正确
    
    Returns:
        bool: 如果所有密钥文件都有效则返回True
    """
    try:
        # 检查应用私钥
        private_key_path = get_alipay_private_key_path()
        logger.info(f"应用私钥文件: {private_key_path}")
        
        # 检查支付宝公钥
        public_key_path = get_alipay_public_key_path()
        logger.info(f"支付宝公钥文件: {public_key_path}")

        # 在Docker环境中，如果文件不存在，返回False但不报错
        if get_project_root() == '/app':
            if not os.path.exists(private_key_path) or not os.path.exists(public_key_path):
                logger.warning("Docker环境中密钥文件不存在，支付功能将不可用")
                return False

        # 检查密钥目录权限
        keys_dir = get_keys_directory()
        if os.path.exists(keys_dir):
            dir_stat = os.stat(keys_dir)
            dir_mode = dir_stat.st_mode & 0o777

            if dir_mode & 0o022:  # 检查是否有组或其他用户的写权限
                logger.warning(f"密钥目录权限不安全: {keys_dir} (权限: {oct(dir_mode)}) - 建议设置为700")
            else:
                logger.debug(f"密钥目录权限安全: {keys_dir} (权限: {oct(dir_mode)})")

        logger.info("所有密钥文件验证通过")
        return True
        
    except FileNotFoundError as e:
        logger.warning(f"密钥文件验证失败: {e}")
        return False
    except Exception as e:
        logger.error(f"密钥文件验证异常: {e}")
        return False


def get_static_file_path(filename: str) -> str:
    """
    获取静态文件的绝对路径（用于HTML等静态资源）
    
    Args:
        filename: 文件名
        
    Returns:
        str: 文件的绝对路径
    """
    project_root = get_project_root()
    return os.path.abspath(os.path.join(project_root, filename))


def ensure_keys_directory() -> str:
    """
    确保密钥目录存在，如果不存在则创建
    
    Returns:
        str: 密钥目录的绝对路径
    """
    keys_dir = get_keys_directory()
    
    if not os.path.exists(keys_dir):
        os.makedirs(keys_dir, mode=0o700)  # 创建目录，权限为700
        logger.info(f"创建密钥目录: {keys_dir}")
    
    return keys_dir


def get_environment_config() -> dict:
    """
    获取环境相关的配置信息
    
    Returns:
        dict: 环境配置信息
    """
    return {
        "project_root": get_project_root(),
        "keys_directory": get_keys_directory(),
        "keys_exist": validate_key_files(),
        "python_path": os.sys.executable,
        "working_directory": os.getcwd()
    }
