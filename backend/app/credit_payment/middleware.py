"""
积分付费系统中间件
提供支付安全检查、日志记录等功能
"""

import json
import time
from typing import Callable, Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
import logging

from app.credit_payment.services.security_service import SecurityService

logger = logging.getLogger(__name__)


class PaymentSecurityMiddleware:
    """支付安全中间件"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)
            
            # 只对支付相关的API进行安全检查
            if self._is_payment_endpoint(request.url.path):
                # 执行安全检查
                security_result = await self._check_security(request)
                if not security_result["allowed"]:
                    response = JSONResponse(
                        status_code=security_result["status_code"],
                        content={"error": security_result["message"]}
                    )
                    await response(scope, receive, send)
                    return
        
        await self.app(scope, receive, send)
    
    def _is_payment_endpoint(self, path: str) -> bool:
        """判断是否为支付相关的端点"""
        payment_paths = [
            "/api/credit-payment/payment/create",
            "/api/credit-payment/payment/query",
            "/api/credit-payment/credit/consume",
            "/api/credit-payment/alipay/notify"
        ]
        return any(path.startswith(p) for p in payment_paths)
    
    async def _check_security(self, request: Request) -> dict:
        """执行安全检查"""
        try:
            # 获取用户ID（如果有）
            user_id = await self._get_user_id(request)
            
            # 获取客户端IP
            client_ip = self._get_client_ip(request)
            
            # 检查请求频率限制
            if user_id:
                allowed, remaining = await SecurityService.check_rate_limit(
                    user_id=user_id,
                    action="payment_request",
                    max_requests=20,  # 每分钟最多20次请求
                    window_seconds=60
                )
                
                if not allowed:
                    await SecurityService.log_payment_event(
                        event_type="rate_limit_exceeded",
                        user_id=user_id,
                        ip_address=client_ip,
                        user_agent=request.headers.get("user-agent")
                    )
                    return {
                        "allowed": False,
                        "status_code": status.HTTP_429_TOO_MANY_REQUESTS,
                        "message": "请求过于频繁，请稍后再试"
                    }
            
            # 检测可疑活动
            if user_id and request.url.path.endswith("/create"):
                suspicious, description = await SecurityService.detect_suspicious_activity(
                    user_id=user_id,
                    event_type="create_order"
                )
                
                if suspicious:
                    await SecurityService.log_payment_event(
                        event_type="suspicious_activity",
                        user_id=user_id,
                        ip_address=client_ip,
                        user_agent=request.headers.get("user-agent"),
                        extra_data={"description": description}
                    )
                    # 可疑活动不直接拒绝，但会记录日志
                    logger.warning(f"检测到可疑活动: 用户{user_id}, {description}")
            
            return {"allowed": True}
            
        except Exception as e:
            logger.error(f"安全检查失败: {e}")
            return {"allowed": True}  # 出错时允许通过，避免影响正常业务
    
    async def _get_user_id(self, request: Request) -> Optional[int]:
        """从请求中获取用户ID"""
        try:
            # 这里需要根据实际的认证机制来获取用户ID
            # 可以从JWT token、session等中获取
            auth_header = request.headers.get("authorization")
            if auth_header and auth_header.startswith("Bearer "):
                # 这里应该解析JWT token获取用户ID
                # 为了简化，这里返回None
                pass
            return None
        except Exception:
            return None
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 优先从X-Forwarded-For头获取（考虑代理情况）
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # 从X-Real-IP头获取
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 最后从客户端地址获取
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"


async def payment_logging_middleware(request: Request, call_next: Callable) -> Response:
    """支付日志中间件"""
    start_time = time.time()
    
    # 记录请求开始
    if request.url.path.startswith("/api/credit-payment/"):
        logger.info(f"支付请求开始: {request.method} {request.url.path}")
    
    try:
        response = await call_next(request)
        
        # 记录请求完成
        if request.url.path.startswith("/api/credit-payment/"):
            process_time = time.time() - start_time
            logger.info(
                f"支付请求完成: {request.method} {request.url.path} "
                f"状态码: {response.status_code} 耗时: {process_time:.3f}s"
            )
        
        return response
        
    except Exception as e:
        # 记录请求异常
        if request.url.path.startswith("/api/credit-payment/"):
            process_time = time.time() - start_time
            logger.error(
                f"支付请求异常: {request.method} {request.url.path} "
                f"错误: {str(e)} 耗时: {process_time:.3f}s"
            )
        raise


async def payment_response_middleware(request: Request, call_next: Callable) -> Response:
    """支付响应中间件，添加安全头"""
    response = await call_next(request)
    
    # 为支付相关的响应添加安全头
    if request.url.path.startswith("/api/credit-payment/"):
        # 防止缓存敏感信息
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        
        # 防止XSS攻击
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        
        # 添加自定义安全头
        response.headers["X-Payment-Security"] = "enabled"
    
    return response
