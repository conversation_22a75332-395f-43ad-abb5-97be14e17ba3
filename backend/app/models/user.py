from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
import bcrypt
import random
import string
from app.db.database import Base

class User(Base):
    """
    用户模型类，对应 PostgreSQL 数据库中的 users 表
    """
    __tablename__ = "users"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="用户 ID，自增主键")
    username = Column(String(64), unique=True, nullable=False, index=True, comment="用户名，唯一，用于登录")
    email = Column(String(120), unique=True, nullable=False, index=True, comment="电子邮箱，唯一")
    nickname = Column(String(64), nullable=True, comment="用户昵称，可选")

    password_hash = Column(String(128), nullable=True, comment="密码哈希值，微信登录用户可为空")
    role = Column(String(20), default="user", comment="用户角色，默认为普通用户(user)")
    is_active = Column(Boolean, default=True, comment="账户是否激活，默认为激活状态")
    
    # 微信登录相关字段
    openid = Column(String(64), nullable=True, unique=True, index=True, comment="微信OpenID，唯一标识")
    unionid = Column(String(64), nullable=True, index=True, comment="微信UnionID，跨应用唯一标识")
    wechat_nickname = Column(String(64), nullable=True, comment="微信昵称")
    avatar_url = Column(String(255), nullable=True, comment="头像URL")
    wechat_refresh_token = Column(String(255), nullable=True, comment="微信refresh_token，用于刷新access_token")
    login_type = Column(String(20), default="password", comment="登录方式：password(密码登录)、wechat(微信登录)")

    # 邀请相关字段
    invitation_code = Column(String(6), nullable=True, unique=True, index=True, comment="用户专属邀请码，6位数字字母组合")

    # 关联的客户（一对多关系）
    clients = relationship("Client", back_populates="user")

    # 关联的token使用记录（一对多关系）
    token_usage_records = relationship("TokenUsage", back_populates="user", cascade="all, delete-orphan")

    # 关联的token使用汇总（一对一关系）
    token_summary = relationship("UserTokenSummary", back_populates="user", uselist=False, cascade="all, delete-orphan")

    # 关联的积分账户（一对一关系）
    credit_account = relationship("UserCreditAccount", back_populates="user", uselist=False, cascade="all, delete-orphan")

    # 关联的系统通知（一对多关系）
    notifications = relationship("SystemNotification", back_populates="user", cascade="all, delete-orphan")

    # 时间相关字段
    last_login = Column(DateTime, nullable=True, comment="最后登录时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def set_password(self, password: str) -> None:
        """
        设置用户密码，将明文密码转换为哈希值存储

        Args:
            password: 明文密码
        """
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def check_password(self, password: str) -> bool:
        """
        验证用户密码是否正确

        Args:
            password: 明文密码

        Returns:
            bool: 密码是否正确
        """
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))

    @classmethod
    def generate_invitation_code(cls) -> str:
        """
        生成6位数字字母组合的邀请码

        Returns:
            str: 6位邀请码
        """
        characters = string.ascii_uppercase + string.digits
        return ''.join(random.choice(characters) for _ in range(6))

    def ensure_invitation_code(self) -> str:
        """
        确保用户有邀请码，如果没有则生成一个

        Returns:
            str: 用户的邀请码
        """
        if not self.invitation_code:
            self.invitation_code = self.generate_invitation_code()
        return self.invitation_code

    def to_dict(self) -> dict:
        """
        将用户对象转换为字典，用于 API 响应

        Returns:
            dict: 用户信息字典
        """
        # 安全处理日期字段，支持datetime对象和字符串
        def safe_isoformat(date_field):
            if date_field is None:
                return None
            if isinstance(date_field, str):
                return date_field
            return date_field.isoformat()

        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'nickname': self.nickname,
            'role': self.role,
            'is_active': self.is_active,
            'openid': self.openid,
            'unionid': self.unionid,
            'wechat_nickname': self.wechat_nickname,
            'avatar_url': self.avatar_url,
            'login_type': self.login_type,
            'invitation_code': self.invitation_code,
            'last_login': safe_isoformat(self.last_login),
            'created_at': safe_isoformat(self.created_at),
            'updated_at': safe_isoformat(self.updated_at)
        }