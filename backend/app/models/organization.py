"""
组织与账号管理相关数据库模型
包含组织、成员关系、邀请记录、用户登录会话等模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, UniqueConstraint, Index
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
import uuid
from app.db.database import Base


class Organization(Base):
    """
    组织模型类，对应 PostgreSQL 数据库中的 organizations 表
    """
    __tablename__ = "organizations"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="组织ID，自增主键")
    name = Column(String(100), nullable=False, index=True, comment="组织名称")
    description = Column(Text, nullable=True, comment="组织描述")
    # 品牌字段
    logo_url = Column(String(512), nullable=True, comment="组织Logo的对外访问URL")
    
    # 所有者信息
    owner_user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="创建者/主账号用户ID")
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False, comment="组织是否激活")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    # 关联关系
    owner = relationship("User", foreign_keys=[owner_user_id], backref="owned_organizations")
    members = relationship("OrganizationMember", back_populates="organization", cascade="all, delete-orphan")
    invitation_codes = relationship("OrganizationInvitationCode", back_populates="organization", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_org_name_active', 'name', 'is_active'),
        Index('idx_org_owner_active', 'owner_user_id', 'is_active'),
    )

    def to_dict(self) -> dict:
        """
        将组织对象转换为字典，用于 API 响应
        
        Returns:
            dict: 组织信息字典
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'logo_url': self.logo_url,
            'owner_user_id': self.owner_user_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class OrganizationMember(Base):
    """
    组织成员关系模型类，对应 PostgreSQL 数据库中的 organization_members 表
    """
    __tablename__ = "organization_members"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="成员关系ID，自增主键")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=False, index=True, comment="组织ID")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="用户ID")
    
    # 角色和身份信息
    role = Column(String(20), nullable=False, default="member", comment="角色：owner/member")
    organization_username = Column(String(64), nullable=False, comment="组织内用户名")
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False, comment="成员是否激活")
    
    # 时间字段
    joined_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="加入时间")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    # 关联关系
    organization = relationship("Organization", back_populates="members")
    user = relationship("User", backref="organization_memberships")

    # 约束和索引
    __table_args__ = (
        UniqueConstraint('organization_id', 'user_id', name='uq_org_member'),
        UniqueConstraint('organization_id', 'organization_username', name='uq_org_username'),
        Index('idx_member_org_user', 'organization_id', 'user_id'),
        Index('idx_member_user_active', 'user_id', 'is_active'),
    )

    def to_dict(self) -> dict:
        """
        将成员关系对象转换为字典，用于 API 响应
        
        Returns:
            dict: 成员关系信息字典
        """
        return {
            'id': self.id,
            'organization_id': self.organization_id,
            'user_id': self.user_id,
            'role': self.role,
            'organization_username': self.organization_username,
            'is_active': self.is_active,
            'joined_at': self.joined_at.isoformat() if self.joined_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class OrganizationInvitationCode(Base):
    """
    组织邀请码模型类，对应 PostgreSQL 数据库中的 organization_invitation_codes 表
    每个组织在24小时内只能有一个有效的邀请码
    """
    __tablename__ = "organization_invitation_codes"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="邀请码ID，自增主键")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=False, index=True, comment="组织ID")
    invited_by_user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="邀请人用户ID")

    # 邀请码信息
    invitation_code = Column(String(36), nullable=False, unique=True, index=True, comment="邀请码，UUID格式")

    # 状态和时间
    status = Column(String(20), nullable=False, default="active", comment="状态：active/expired/revoked")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")

    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    # 关联关系
    organization = relationship("Organization", back_populates="invitation_codes")
    invited_by = relationship("User", foreign_keys=[invited_by_user_id], backref="sent_invitation_codes")
    usages = relationship("OrganizationInvitationUsage", back_populates="invitation_code", cascade="all, delete-orphan")

    # 索引和约束
    __table_args__ = (
        Index('idx_invitation_code_org_status', 'organization_id', 'status'),
        Index('idx_invitation_code_status_expires', 'status', 'expires_at'),
        Index('idx_invitation_code_expires', 'expires_at'),
    )

    @classmethod
    def generate_invitation_code(cls) -> str:
        """
        生成唯一的邀请码

        Returns:
            str: UUID格式的邀请码
        """
        return str(uuid.uuid4())

    @classmethod
    def get_default_expiry(cls) -> datetime:
        """
        获取默认过期时间（24小时后）

        Returns:
            datetime: 过期时间
        """
        return datetime.utcnow() + timedelta(hours=24)

    def is_expired(self) -> bool:
        """
        检查邀请码是否已过期

        Returns:
            bool: 是否已过期
        """
        return datetime.utcnow() > self.expires_at

    def is_valid(self) -> bool:
        """
        检查邀请码是否有效（未过期且状态为active）

        Returns:
            bool: 是否有效
        """
        return self.status == "active" and not self.is_expired()

    def revoke(self) -> None:
        """
        撤销邀请码
        """
        self.status = "revoked"
        self.updated_at = datetime.utcnow()

    def get_remaining_time(self) -> timedelta:
        """
        获取邀请码剩余有效时间

        Returns:
            timedelta: 剩余时间，如果已过期则返回0
        """
        if self.is_expired():
            return timedelta(0)
        return self.expires_at - datetime.utcnow()

    def to_dict(self) -> dict:
        """
        将邀请码对象转换为字典，用于 API 响应

        Returns:
            dict: 邀请码信息字典
        """
        return {
            'id': self.id,
            'organization_id': self.organization_id,
            'invited_by_user_id': self.invited_by_user_id,
            'invitation_code': self.invitation_code,
            'status': self.status,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class OrganizationInvitationUsage(Base):
    """
    组织邀请码使用记录模型类，对应 PostgreSQL 数据库中的 organization_invitation_usages 表
    记录用户使用邀请码加入组织的历史
    """
    __tablename__ = "organization_invitation_usages"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="使用记录ID，自增主键")
    invitation_code_id = Column(Integer, ForeignKey("organization_invitation_codes.id", ondelete="CASCADE"), nullable=False, index=True, comment="邀请码ID")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="用户ID")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=False, index=True, comment="组织ID")

    # 使用信息
    organization_username = Column(String(64), nullable=False, comment="用户在组织中的用户名")
    joined_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="加入时间")

    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    # 关联关系
    invitation_code = relationship("OrganizationInvitationCode", back_populates="usages")
    user = relationship("User", backref="invitation_usages")
    organization = relationship("Organization", backref="invitation_usages")

    # 索引和约束
    __table_args__ = (
        UniqueConstraint('invitation_code_id', 'user_id', name='uq_invitation_usage_user'),
        Index('idx_invitation_usage_org', 'organization_id'),
        Index('idx_invitation_usage_user', 'user_id'),
        Index('idx_invitation_usage_joined', 'joined_at'),
    )

    def to_dict(self) -> dict:
        """
        将使用记录对象转换为字典，用于 API 响应

        Returns:
            dict: 使用记录信息字典
        """
        return {
            'id': self.id,
            'invitation_code_id': self.invitation_code_id,
            'user_id': self.user_id,
            'organization_id': self.organization_id,
            'organization_username': self.organization_username,
            'joined_at': self.joined_at.isoformat() if self.joined_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class UserLoginSession(Base):
    """
    用户登录会话模型类，记录用户当前登录身份和偏好
    对应 PostgreSQL 数据库中的 user_login_sessions 表
    """
    __tablename__ = "user_login_sessions"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="会话记录ID，自增主键")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True, index=True, comment="用户ID，唯一")

    # 当前身份信息
    current_identity_type = Column(String(20), nullable=False, default="personal", comment="当前身份类型：personal/organization")
    current_organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="SET NULL"), nullable=True, comment="当前组织ID，个人身份时为空")

    # 上次身份信息（用于记住用户偏好）
    last_identity_type = Column(String(20), nullable=True, comment="上次身份类型")
    last_organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="SET NULL"), nullable=True, comment="上次组织ID")

    # 会话信息
    session_token = Column(String(36), nullable=True, unique=True, index=True, comment="会话令牌，用于标识唯一会话")

    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    # 关联关系
    user = relationship("User", backref="login_session")
    current_organization = relationship("Organization", foreign_keys=[current_organization_id])
    last_organization = relationship("Organization", foreign_keys=[last_organization_id])

    # 索引
    __table_args__ = (
        Index('idx_session_user_token', 'user_id', 'session_token'),
        Index('idx_session_current_org', 'current_organization_id'),
    )

    def to_dict(self) -> dict:
        """
        将登录会话对象转换为字典，用于 API 响应

        Returns:
            dict: 登录会话信息字典
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'current_identity_type': self.current_identity_type,
            'current_organization_id': self.current_organization_id,
            'last_identity_type': self.last_identity_type,
            'last_organization_id': self.last_organization_id,
            'session_token': self.session_token,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class PersonalInvitation(Base):
    """
    个人邀请记录模型
    记录用户之间的邀请关系和奖励状态
    """
    __tablename__ = "personal_invitations"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="邀请记录ID")
    inviter_user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="邀请人用户ID")
    invitee_user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="被邀请人用户ID")
    invitation_code = Column(String(6), nullable=False, index=True, comment="使用的邀请码")

    # 奖励状态
    inviter_rewarded = Column(Boolean, default=False, nullable=False, comment="邀请人是否已获得奖励")
    invitee_rewarded = Column(Boolean, default=False, nullable=False, comment="被邀请人是否已获得奖励")
    reward_package_id = Column(String(50), default="trial_package", nullable=False, comment="奖励套餐ID")

    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="邀请时间")
    inviter_rewarded_at = Column(DateTime, nullable=True, comment="邀请人获得奖励时间")
    invitee_rewarded_at = Column(DateTime, nullable=True, comment="被邀请人获得奖励时间")

    # 关联关系
    inviter = relationship("User", foreign_keys=[inviter_user_id], backref="sent_personal_invitations")
    invitee = relationship("User", foreign_keys=[invitee_user_id], backref="received_personal_invitations")

    # 索引
    __table_args__ = (
        Index('idx_personal_invitation_inviter', 'inviter_user_id'),
        Index('idx_personal_invitation_invitee', 'invitee_user_id'),
        Index('idx_personal_invitation_code', 'invitation_code'),
        Index('idx_personal_invitation_created', 'created_at'),
    )

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'id': self.id,
            'inviter_user_id': self.inviter_user_id,
            'invitee_user_id': self.invitee_user_id,
            'invitation_code': self.invitation_code,
            'inviter_rewarded': self.inviter_rewarded,
            'invitee_rewarded': self.invitee_rewarded,
            'reward_package_id': self.reward_package_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'inviter_rewarded_at': self.inviter_rewarded_at.isoformat() if self.inviter_rewarded_at else None,
            'invitee_rewarded_at': self.invitee_rewarded_at.isoformat() if self.invitee_rewarded_at else None
        }
