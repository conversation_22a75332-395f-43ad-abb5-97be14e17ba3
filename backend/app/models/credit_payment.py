"""
积分付费系统数据库模型
包含用户积分账户、支付订单、积分消费记录等模型
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean, Index, JSON
from sqlalchemy.types import DECIMAL
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from app.db.database import Base


def utc_now():
    """返回UTC时区的当前时间（不带时区信息，适配数据库）"""
    # 使用timezone-aware的datetime，然后移除时区信息以适配数据库
    return datetime.now(timezone.utc).replace(tzinfo=None)


class UserCreditAccount(Base):
    """
    用户积分账户模型
    管理用户的积分余额和账户状态
    支持多租户：同一用户可以有个人账户和组织账户
    """
    __tablename__ = "user_credit_accounts"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="积分账户ID，自增主键")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="用户ID")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True, index=True, comment="所属组织ID，用于多租户积分隔离。NULL表示个人积分账户")

    # 积分信息
    credit_balance = Column(Integer, default=0, nullable=False, comment="当前积分余额")
    total_credits_purchased = Column(Integer, default=0, nullable=False, comment="累计购买积分数")
    total_credits_consumed = Column(Integer, default=0, nullable=False, comment="累计消费积分数")

    # 账户状态
    is_active = Column(Boolean, default=True, nullable=False, comment="账户是否激活")
    last_recharge_at = Column(DateTime, nullable=True, comment="最后充值时间")
    last_consumption_at = Column(DateTime, nullable=True, comment="最后消费时间")

    # 时间字段
    created_at = Column(DateTime, default=utc_now, nullable=False, comment="账户创建时间")
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now, nullable=False, comment="账户更新时间")

    # 关联关系
    user = relationship("User", back_populates="credit_account")
    payment_orders = relationship("PaymentOrder", back_populates="user_account", cascade="all, delete-orphan")
    credit_transactions = relationship("CreditTransaction", back_populates="user_account", cascade="all, delete-orphan")

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'organization_id': self.organization_id,
            'credit_balance': self.credit_balance,
            'total_credits_purchased': self.total_credits_purchased,
            'total_credits_consumed': self.total_credits_consumed,
            'is_active': self.is_active,
            'last_recharge_at': self.last_recharge_at.isoformat() if self.last_recharge_at else None,
            'last_consumption_at': self.last_consumption_at.isoformat() if self.last_consumption_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class PaymentOrder(Base):
    """
    支付订单模型
    记录用户的充值订单信息
    """
    __tablename__ = "payment_orders"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="订单ID，自增主键")
    order_no = Column(String(64), unique=True, nullable=False, index=True, comment="订单号，唯一")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="用户ID")
    user_credit_account_id = Column(Integer, ForeignKey("user_credit_accounts.id", ondelete="CASCADE"), nullable=False, index=True, comment="积分账户ID")

    # 订单信息
    amount = Column(DECIMAL(10, 2), nullable=False, comment="支付金额（元）")
    credits = Column(Integer, nullable=False, comment="购买的积分数量")
    payment_method = Column(String(20), nullable=False, comment="支付方式：alipay, wechat")
    recharge_type = Column(String(20), nullable=False, default="package", comment="充值类型：package, credits")
    package_id = Column(String(50), nullable=True, comment="套餐ID：personal_standard, personal_professional, business_flagship, enterprise_flagship")
    package_quantity = Column(Integer, default=1, nullable=False, comment="套餐购买数量，默认为1，支持批量购买")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True, index=True, comment="所属组织ID，用于多租户订单隔离。NULL表示个人订单")

    # 订单状态
    status = Column(String(20), default="pending", nullable=False, comment="订单状态：pending, paid, failed, cancelled, refunded, partial_refunded")

    # 支付信息
    trade_no = Column(String(100), nullable=True, comment="第三方支付交易号")
    payment_time = Column(DateTime, nullable=True, comment="支付完成时间")

    # 退款信息
    refund_amount = Column(DECIMAL(10, 2), default=0, nullable=False, comment="退款金额")
    refund_reason = Column(Text, nullable=True, comment="退款原因")
    refund_time = Column(DateTime, nullable=True, comment="退款时间")

    # 备注信息
    remark = Column(Text, nullable=True, comment="订单备注")

    # 时间字段
    created_at = Column(DateTime, default=utc_now, nullable=False, comment="订单创建时间")
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now, nullable=False, comment="订单更新时间")
    expires_at = Column(DateTime, nullable=False, comment="套餐过期时间")

    # 关联关系
    user = relationship("User")
    user_account = relationship("UserCreditAccount", back_populates="payment_orders")

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'id': self.id,
            'order_no': self.order_no,
            'user_id': self.user_id,
            'organization_id': self.organization_id,
            'amount': float(self.amount),
            'credits': self.credits,
            'payment_method': self.payment_method,
            'recharge_type': self.recharge_type,
            'package_id': self.package_id,
            'package_quantity': self.package_quantity,
            'status': self.status,
            'trade_no': self.trade_no,
            'payment_time': self.payment_time.isoformat() if self.payment_time else None,
            'refund_amount': float(self.refund_amount),
            'refund_reason': self.refund_reason,
            'refund_time': self.refund_time.isoformat() if self.refund_time else None,
            'remark': self.remark,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }


class CreditTransaction(Base):
    """
    积分交易记录模型
    记录用户的积分变动历史
    """
    __tablename__ = "credit_transactions"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="交易记录ID，自增主键")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="用户ID")
    user_credit_account_id = Column(Integer, ForeignKey("user_credit_accounts.id", ondelete="CASCADE"), nullable=False, index=True, comment="积分账户ID")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=True, index=True, comment="所属组织ID，用于多租户交易记录隔离。NULL表示个人交易")

    # 交易信息
    transaction_type = Column(String(20), nullable=False, comment="交易类型：recharge, consumption, refund")
    amount = Column(Integer, nullable=False, comment="积分变动数量（正数为增加，负数为减少）")
    balance_before = Column(Integer, nullable=False, comment="交易前积分余额")
    balance_after = Column(Integer, nullable=False, comment="交易后积分余额")

    # 关联信息
    order_id = Column(Integer, ForeignKey("payment_orders.id"), nullable=True, comment="关联的支付订单ID（充值时）")
    service_type = Column(String(50), nullable=True, comment="服务类型（消费时）：ai_selection, ai_writing等")
    operation_type = Column(String(100), nullable=True, comment="操作类型（消费时）")
    request_id = Column(String(100), nullable=True, comment="请求ID（消费时）")

    # 换算信息（消费时记录tokens换算）
    tokens_consumed = Column(Integer, nullable=True, comment="消费的tokens数量")
    conversion_rate = Column(Integer, default=100, nullable=False, comment="换算比例（1积分=多少tokens）")

    # 描述信息
    description = Column(Text, nullable=True, comment="交易描述")

    # 时间字段
    created_at = Column(DateTime, default=utc_now, nullable=False, comment="交易时间")

    # 关联关系
    user = relationship("User")
    user_account = relationship("UserCreditAccount", back_populates="credit_transactions")
    payment_order = relationship("PaymentOrder")

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'organization_id': self.organization_id,
            'transaction_type': self.transaction_type,
            'amount': self.amount,
            'balance_before': self.balance_before,
            'balance_after': self.balance_after,
            'order_id': self.order_id,
            'service_type': self.service_type,
            'operation_type': self.operation_type,
            'request_id': self.request_id,
            'tokens_consumed': self.tokens_consumed,
            'conversion_rate': self.conversion_rate,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class RedeemCode(Base):
    """
    兑换码模型
    管理兑换码的生成、使用和状态
    """
    __tablename__ = "redeem_codes"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="兑换码ID，自增主键")
    code = Column(String(50), unique=True, nullable=False, index=True, comment="兑换码")

    # 兑换码信息
    credits = Column(Integer, nullable=True, comment="兑换码价值（积分数量）- 兼容旧版本")
    package_id = Column(String(50), nullable=True, comment="兑换的套餐ID")
    description = Column(String(200), nullable=True, comment="兑换码描述")

    # 使用限制
    max_uses = Column(Integer, default=1, nullable=False, comment="最大使用次数")
    used_count = Column(Integer, default=0, nullable=False, comment="已使用次数")

    # 状态和时间
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    created_at = Column(DateTime, default=utc_now, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now, nullable=False, comment="更新时间")

    # 创建者信息
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="创建者用户ID")

    # 关联关系
    creator = relationship("User", foreign_keys=[created_by])
    redemptions = relationship("RedeemCodeRedemption", back_populates="redeem_code")

    def is_valid(self) -> bool:
        """检查兑换码是否有效"""
        if not self.is_active:
            return False
        if self.used_count >= self.max_uses:
            return False
        if self.expires_at and self.expires_at < utc_now():
            return False
        return True

    def can_be_used(self) -> bool:
        """检查兑换码是否可以使用"""
        return self.is_valid() and self.used_count < self.max_uses

    def get_reward_type(self) -> str:
        """获取兑换码奖励类型"""
        if self.package_id:
            return "package"
        elif self.credits:
            return "credits"
        else:
            return "unknown"


class RedeemCodeRedemption(Base):
    """
    兑换码使用记录模型
    记录兑换码的使用历史
    """
    __tablename__ = "redeem_code_redemptions"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="使用记录ID，自增主键")
    redeem_code_id = Column(Integer, ForeignKey("redeem_codes.id", ondelete="CASCADE"), nullable=False, index=True, comment="兑换码ID")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="使用者用户ID")

    # 兑换信息
    credits_received = Column(Integer, nullable=True, comment="获得的积分数量")
    package_order_id = Column(Integer, ForeignKey("payment_orders.id"), nullable=True, comment="关联的套餐订单ID")
    transaction_id = Column(Integer, ForeignKey("credit_transactions.id"), nullable=True, comment="关联的积分交易记录ID")

    # 时间信息
    redeemed_at = Column(DateTime, default=utc_now, nullable=False, comment="兑换时间")

    # 关联关系
    redeem_code = relationship("RedeemCode", back_populates="redemptions")
    user = relationship("User")
    package_order = relationship("PaymentOrder", foreign_keys=[package_order_id])
    transaction = relationship("CreditTransaction")


# 创建索引以优化查询性能
Index('idx_payment_orders_user_status', PaymentOrder.user_id, PaymentOrder.status)
Index('idx_payment_orders_created_at', PaymentOrder.created_at)
Index('idx_payment_orders_order_no', PaymentOrder.order_no)
Index('idx_credit_transactions_user_type', CreditTransaction.user_id, CreditTransaction.transaction_type)
Index('idx_credit_transactions_created_at', CreditTransaction.created_at)
Index('idx_credit_transactions_request_id', CreditTransaction.request_id)
Index('idx_redeem_codes_code', RedeemCode.code)
Index('idx_redeem_codes_active_expires', RedeemCode.is_active, RedeemCode.expires_at)
Index('idx_redeem_codes_package_id', RedeemCode.package_id)
Index('idx_redeem_code_redemptions_user_code', RedeemCodeRedemption.user_id, RedeemCodeRedemption.redeem_code_id)
Index('idx_redeem_code_redemptions_package_order', RedeemCodeRedemption.package_order_id)


class PackageConfig(Base):
    """
    套餐配置模型
    管理系统中所有套餐的配置信息，支持动态修改
    为外部管理系统提供数据基础
    """
    __tablename__ = "packages_config"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="配置ID，自增主键")
    package_id = Column(String(50), unique=True, nullable=False, index=True, comment="套餐ID，唯一标识")

    # 套餐基本信息
    name = Column(String(100), nullable=False, comment="套餐名称")
    description = Column(Text, nullable=True, comment="套餐描述")

    # 价格和积分配置
    amount = Column(DECIMAL(10, 2), nullable=False, comment="套餐价格（元）")
    credits = Column(Integer, nullable=False, comment="套餐包含的积分数量")
    bonus = Column(Integer, default=0, nullable=False, comment="赠送积分数量")

    # 计费配置
    billing_cycle = Column(String(20), nullable=False, comment="计费周期：trial, yearly, monthly")
    monthly_price = Column(DECIMAL(10, 2), nullable=True, comment="月均价格（元）")

    # 用户限制
    min_users = Column(Integer, default=1, nullable=False, comment="最少用户数")
    max_users = Column(Integer, default=1, nullable=False, comment="最多用户数")

    # 有效期配置
    validity_days = Column(Integer, nullable=True, comment="有效期天数（体验套餐使用）")

    # 特殊标记
    is_trial = Column(Boolean, default=False, nullable=False, comment="是否为体验套餐")
    contact_required = Column(Boolean, default=False, nullable=False, comment="是否需要联系销售")
    contact_phone = Column(String(20), nullable=True, comment="联系电话")

    # 功能特性（JSON格式存储）
    features = Column(JSON, nullable=True, comment="套餐功能特性列表")

    # 批量折扣配置（JSON格式存储）
    bulk_discount = Column(JSON, nullable=True, comment="批量折扣配置")

    # 导出限制配置
    export_limit_excel = Column(Integer, default=-1, nullable=False, comment="Excel导出次数限制（-1表示无限制，0表示不允许，正数表示具体次数）")
    export_limit_pdf = Column(Integer, default=-1, nullable=False, comment="PDF导出次数限制（-1表示无限制，0表示不允许，正数表示具体次数）")

    # 状态控制
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序顺序")

    # 时间字段
    created_at = Column(DateTime, default=utc_now, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now, nullable=False, comment="更新时间")

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "package_id": self.package_id,
            "name": self.name,
            "description": self.description,
            "amount": float(self.amount) if self.amount else 0.0,
            "credits": self.credits,
            "bonus": self.bonus,
            "billing_cycle": self.billing_cycle,
            "monthly_price": float(self.monthly_price) if self.monthly_price else None,
            "min_users": self.min_users,
            "max_users": self.max_users,
            "validity_days": self.validity_days,
            "is_trial": self.is_trial,
            "contact_required": self.contact_required,
            "contact_phone": self.contact_phone,
            "features": self.features or [],
            "bulk_discount": self.bulk_discount,
            "export_limit_excel": self.export_limit_excel,
            "export_limit_pdf": self.export_limit_pdf,
            "is_active": self.is_active,
            "sort_order": self.sort_order,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    def to_config_dict(self) -> dict:
        """转换为配置格式的字典（兼容原有的RECHARGE_PACKAGES格式）"""
        config = {
            "amount": self.amount,
            "credits": self.credits,
            "bonus": self.bonus,
            "name": self.name,
            "description": self.description,
            "billing_cycle": self.billing_cycle,
            "monthly_price": self.monthly_price,
            "min_users": self.min_users,
            "max_users": self.max_users,
            "features": self.features or [],
            "is_trial": self.is_trial,
            "contact_required": self.contact_required,
            "contact_phone": self.contact_phone
        }

        # 添加有效期（仅体验套餐）
        if self.validity_days is not None:
            config["validity_days"] = self.validity_days

        # 添加批量折扣配置
        if self.bulk_discount:
            config["bulk_discount"] = self.bulk_discount

        # 添加导出限制配置
        config["export_limit_excel"] = self.export_limit_excel
        config["export_limit_pdf"] = self.export_limit_pdf

        return config


# 索引定义
Index('idx_packages_config_package_id', PackageConfig.package_id)
Index('idx_packages_config_active_sort', PackageConfig.is_active, PackageConfig.sort_order)
