from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class SystemNotification(Base):
    """
    系统通知模型
    
    对应数据库中的 system_notifications 表
    用于存储系统通知信息，包括维护通知、功能更新、安全提醒等
    """
    __tablename__ = "system_notifications"

    id = Column(Integer, primary_key=True, index=True, comment="通知ID，自增主键")
    title = Column(String(200), nullable=False, comment="通知标题")
    content = Column(Text, nullable=False, comment="通知内容")
    type = Column(String(50), nullable=False, default="general", comment="通知类型：maintenance(维护), feature(功能), security(安全), promotion(促销), general(一般)")
    priority = Column(String(20), nullable=False, default="medium", comment="优先级：high(重要), medium(一般), low(普通)")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True, comment="目标用户ID，为空表示全员通知")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活显示")
    expire_at = Column(DateTime(timezone=True), nullable=True, comment="过期时间，过期后自动隐藏")
    action_data = Column(JSON, nullable=True, comment="扩展数据，JSON格式存储点击行为等信息")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")

    # 关系
    user = relationship("User", back_populates="notifications")

    def __repr__(self):
        return f"<SystemNotification(id={self.id}, title='{self.title}', type='{self.type}')>" 