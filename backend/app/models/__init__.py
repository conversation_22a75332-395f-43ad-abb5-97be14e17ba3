from app.models.user import User
from app.models.client import (
    Client,
    Education,
    Academic,
    Work,
    Activity,
    Award,
    Skill,
    LanguageScore,
    Thought,
    BackgroundCustomModule,
    ThoughtCustomModule,
    ClientProgram
)
from app.models.token_usage import TokenUsage, UserTokenSummary
from app.models.credit_payment import UserCreditAccount, PaymentOrder, CreditTransaction, RedeemCode, RedeemCodeRedemption
from app.models.organization import Organization, OrganizationMember, OrganizationInvitationCode, OrganizationInvitationUsage, UserLoginSession, PersonalInvitation
from app.models.package_allocation import PackageAllocation
from app.models.system_notification import SystemNotification

# 导入 CRM 模块的模型
from app.crm.models import CRMClient, CRMClientStaff

# 导入 AI 选择模块的模型
from app.ai_selection.db.models import (
    AISelectionProgram,
    AISelectionCase,
    AISelectionHomeSchool,
    AISelectionAbroadSchool
)

# 导出所有模型，方便其他模块导入
__all__ = [
    "User",
    "Client",
    "Education",
    "Academic",
    "Work",
    "Activity",
    "Award",
    "Skill",
    "LanguageScore",
    "Thought",
    "BackgroundCustomModule",
    "ThoughtCustomModule",
    "ClientProgram",
    "TokenUsage",
    "UserTokenSummary",
    "AISelectionProgram",
    "AISelectionCase",
    "AISelectionHomeSchool",
    "AISelectionAbroadSchool",
    # 积分付费系统模型
    "UserCreditAccount",
    "PaymentOrder",
    "CreditTransaction",
    "RedeemCode",
    "RedeemCodeRedemption",
    # 组织管理系统模型
    "Organization",
    "OrganizationMember",
    "OrganizationInvitationCode",
    "OrganizationInvitationUsage",
    "UserLoginSession",
    # 个人邀请系统模型
    "PersonalInvitation",
    # 组织权益分配系统模型
    "PackageAllocation",
    # 系统通知模型
    "SystemNotification",
    # CRM系统模型
    "CRMClient",
    "CRMClientStaff"
]