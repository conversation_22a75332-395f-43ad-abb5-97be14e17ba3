"""
Token使用记录模型
用于记录用户的LLM token消耗情况，支持计费和审计
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Index
from sqlalchemy.orm import relationship
from datetime import datetime
from app.db.database import Base


class TokenUsage(Base):
    """
    Token使用记录模型，记录每次LLM调用的token消耗
    """
    __tablename__ = "token_usage"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="记录ID，自增主键")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="用户ID，外键关联users表")
    
    # Token消耗信息
    service_type = Column(String(50), nullable=False, index=True, comment="服务类型：ai_selection, ai_writing等")
    operation_type = Column(String(100), nullable=False, comment="操作类型：user_profile, recommendation_reason等")
    tokens_consumed = Column(Integer, nullable=False, comment="本次消耗的token数量")
    
    # 请求信息
    request_id = Column(String(100), nullable=True, index=True, comment="请求ID，用于关联同一次推荐的多个LLM调用")
    model_name = Column(String(50), nullable=True, comment="使用的模型名称")
    
    # 上下文信息（可选，用于调试和优化）
    prompt_length = Column(Integer, nullable=True, comment="提示词长度（字符数）")
    response_length = Column(Integer, nullable=True, comment="响应长度（字符数）")
    error_message = Column(Text, nullable=True, comment="如果调用失败，记录错误信息")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="记录创建时间")
    
    # 关联关系
    user = relationship("User", back_populates="token_usage_records")

    def to_dict(self) -> dict:
        """
        将token使用记录转换为字典
        
        Returns:
            dict: token使用记录字典
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'service_type': self.service_type,
            'operation_type': self.operation_type,
            'tokens_consumed': self.tokens_consumed,
            'request_id': self.request_id,
            'model_name': self.model_name,
            'prompt_length': self.prompt_length,
            'response_length': self.response_length,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class UserTokenSummary(Base):
    """
    用户Token使用汇总表，用于快速查询用户的总token消耗
    """
    __tablename__ = "user_token_summary"

    # 基本字段
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), primary_key=True, comment="用户ID，主键")
    
    # 汇总统计
    total_tokens_consumed = Column(Integer, default=0, nullable=False, comment="总消耗token数量")
    ai_selection_tokens = Column(Integer, default=0, nullable=False, comment="AI选校服务消耗的token数量")
    ai_writing_tokens = Column(Integer, default=0, nullable=False, comment="AI写作服务消耗的token数量")
    
    # 统计信息
    total_requests = Column(Integer, default=0, nullable=False, comment="总请求次数")
    last_usage_at = Column(DateTime, nullable=True, comment="最后使用时间")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="记录创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="记录更新时间")
    
    # 关联关系
    user = relationship("User", back_populates="token_summary")

    def to_dict(self) -> dict:
        """
        将用户token汇总转换为字典
        
        Returns:
            dict: 用户token汇总字典
        """
        return {
            'user_id': self.user_id,
            'total_tokens_consumed': self.total_tokens_consumed,
            'ai_selection_tokens': self.ai_selection_tokens,
            'ai_writing_tokens': self.ai_writing_tokens,
            'total_requests': self.total_requests,
            'last_usage_at': self.last_usage_at.isoformat() if self.last_usage_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


# 创建索引以优化查询性能
Index('idx_token_usage_user_service', TokenUsage.user_id, TokenUsage.service_type)
Index('idx_token_usage_created_at', TokenUsage.created_at)
Index('idx_token_usage_request_id', TokenUsage.request_id)
