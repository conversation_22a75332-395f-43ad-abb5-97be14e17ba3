"""
组织权益分配系统数据库模型
包含套餐分配记录等模型
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean, Index
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from app.db.database import Base


def utc_now():
    """返回UTC时区的当前时间（不带时区信息，适配数据库）"""
    return datetime.now(timezone.utc).replace(tzinfo=None)


class PackageAllocation(Base):
    """
    套餐分配记录模型
    记录组织内套餐权益的分配关系
    """
    __tablename__ = "package_allocations"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="分配记录ID，自增主键")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=False, index=True, comment="组织ID")
    owner_user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="主账号用户ID（套餐购买者）")
    allocated_user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True, comment="分配给的用户ID（子账号）")
    
    # 订单关联
    source_order_id = Column(Integer, ForeignKey("payment_orders.id", ondelete="CASCADE"), nullable=False, index=True, comment="源订单ID（主账号购买的订单）")
    allocated_order_id = Column(Integer, ForeignKey("payment_orders.id", ondelete="CASCADE"), nullable=True, index=True, comment="分配订单ID（为子账号创建的虚拟订单）")
    
    # 套餐信息
    package_id = Column(String(50), nullable=False, comment="套餐ID")
    
    # 状态信息
    status = Column(String(20), default="active", nullable=False, comment="分配状态：active/revoked")
    
    # 时间字段
    allocated_at = Column(DateTime, default=utc_now, nullable=False, comment="分配时间")
    revoked_at = Column(DateTime, nullable=True, comment="回收时间")
    created_at = Column(DateTime, default=utc_now, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now, nullable=False, comment="更新时间")

    # 关联关系
    organization = relationship("Organization", foreign_keys=[organization_id])
    owner_user = relationship("User", foreign_keys=[owner_user_id])
    allocated_user = relationship("User", foreign_keys=[allocated_user_id])
    source_order = relationship("PaymentOrder", foreign_keys=[source_order_id])
    allocated_order = relationship("PaymentOrder", foreign_keys=[allocated_order_id])

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'organization_id': self.organization_id,
            'owner_user_id': self.owner_user_id,
            'allocated_user_id': self.allocated_user_id,
            'source_order_id': self.source_order_id,
            'allocated_order_id': self.allocated_order_id,
            'package_id': self.package_id,
            'status': self.status,
            'allocated_at': self.allocated_at.isoformat() if self.allocated_at else None,
            'revoked_at': self.revoked_at.isoformat() if self.revoked_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f"<PackageAllocation(id={self.id}, org={self.organization_id}, owner={self.owner_user_id}, allocated={self.allocated_user_id}, package={self.package_id}, status={self.status})>"


# 创建索引以优化查询性能
Index('idx_package_allocations_org_user_status', PackageAllocation.organization_id, PackageAllocation.allocated_user_id, PackageAllocation.status)
Index('idx_package_allocations_owner_package', PackageAllocation.owner_user_id, PackageAllocation.package_id)
Index('idx_package_allocations_source_order', PackageAllocation.source_order_id)
Index('idx_package_allocations_allocated_order', PackageAllocation.allocated_order_id)
