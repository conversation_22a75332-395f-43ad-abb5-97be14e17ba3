from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from datetime import datetime

class UserBase(BaseModel):
    """
    用户基础信息模型
    """
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="电子邮箱")
    
class UserCreate(UserBase):
    """
    创建用户请求模型
    """
    password: str = Field(..., min_length=6, description="密码，至少6个字符")
    invitation_code: Optional[str] = Field(None, min_length=6, max_length=6, description="邀请码（可选）")

    @validator('username')
    def username_alphanumeric(cls, v):
        """
        验证用户名是否只包含字母和数字
        """
        if not v.isalnum():
            raise ValueError('用户名只能包含字母和数字')
        return v

    @validator('invitation_code')
    def validate_invitation_code(cls, v):
        """
        验证邀请码格式
        """
        if v is not None:
            if not v.isalnum():
                raise ValueError('邀请码只能包含字母和数字')
            if len(v) != 6:
                raise ValueError('邀请码必须是6位')
        return v

class UserLogin(BaseModel):
    """
    用户登录请求模型
    """
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

class UserUpdate(BaseModel):
    """
    更新用户信息请求模型
    """
    nickname: Optional[str] = Field(None, min_length=2, max_length=50, description="用户昵称")

class ChangePassword(BaseModel):
    """
    修改密码请求模型
    """
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, description="新密码，至少6个字符")

class UserResponse(BaseModel):
    """
    用户信息响应模型
    """
    id: int
    username: str
    email: EmailStr
    nickname: Optional[str] = None
    role: str
    is_active: bool
    # 微信登录相关字段
    openid: Optional[str] = None
    unionid: Optional[str] = None
    wechat_nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    login_type: Optional[str] = "password"
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    # 权限相关字段
    permissions: Optional[List[str]] = None
    has_test_access: Optional[bool] = None

    class Config:
        """
        ORM 模式配置，允许直接从 ORM 模型创建
        """
        from_attributes = True

class Token(BaseModel):
    """
    令牌响应模型
    """
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user: Optional[UserResponse] = None
    
class TokenPayload(BaseModel):
    """
    令牌载荷模型 - 扩展支持组织身份
    """
    sub: Optional[int] = None  # 用户 ID
    exp: Optional[int] = None  # 过期时间
    # 组织身份相关字段
    identity_type: Optional[str] = "personal"  # 身份类型：personal/organization
    organization_id: Optional[int] = None  # 组织ID
    organization_role: Optional[str] = None  # 组织角色：owner/member

class RefreshTokenRequest(BaseModel):
    """
    刷新令牌请求模型 - 增强安全性
    """
    refresh_token: str = Field(..., min_length=10, description="刷新令牌")
    
    @validator('refresh_token')
    def validate_refresh_token_format(cls, v):
        """验证refresh token格式"""
        if not v or len(v.strip()) == 0:
            raise ValueError('refresh_token不能为空')
        # 简单验证JWT格式（至少包含两个点）
        if v.count('.') != 2:
            raise ValueError('refresh_token格式无效')
        return v.strip()