from .user import UserCreate, UserUpdate, UserResponse, UserLogin, Token, TokenPayload
from .client import *
from .system_notification import *

# 导出所有schema，方便其他模块导入
__all__ = [
    # 用户相关
    "UserBase", "UserCreate", "UserLogin", "UserUpdate",
    "ChangePassword", "UserResponse", "Token", "TokenPayload",

    # 客户相关
    "ClientBase", "ClientCreate", "ClientUpdate", "ClientResponse", "ClientDetailResponse",

    # 教育经历相关
    "EducationBase", "EducationCreate", "EducationUpdate", "EducationResponse",

    # 学术经历相关
    "AcademicBase", "AcademicCreate", "AcademicUpdate", "AcademicResponse",

    # 工作经历相关
    "WorkBase", "WorkCreate", "WorkUpdate", "WorkResponse",

    # 课外活动相关
    "ActivityBase", "ActivityCreate", "ActivityUpdate", "ActivityResponse",

    # 奖项荣誉相关
    "AwardBase", "AwardCreate", "AwardUpdate", "AwardResponse",

    # 技能相关
    "SkillBase", "SkillCreate", "SkillUpdate", "SkillResponse",

    # 语言成绩相关
    "LanguageScoreBase", "LanguageScoreCreate", "LanguageScoreUpdate", "LanguageScoreResponse"
]