"""
组织与账号管理相关的 Pydantic 模型
用于 API 请求和响应的数据验证
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum


class IdentityType(str, Enum):
    """身份类型枚举"""
    PERSONAL = "personal"
    ORGANIZATION = "organization"


class OrganizationRole(str, Enum):
    """组织角色枚举"""
    OWNER = "owner"
    MEMBER = "member"





# ===============================
# 组织相关模型
# ===============================

class OrganizationBase(BaseModel):
    """组织基础信息模型"""
    name: str = Field(..., min_length=1, max_length=100, description="组织名称")
    description: Optional[str] = Field(None, max_length=500, description="组织描述")


class OrganizationCreate(OrganizationBase):
    """创建组织请求模型"""
    pass


class OrganizationUpdate(BaseModel):
    """更新组织请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="组织名称")
    description: Optional[str] = Field(None, max_length=500, description="组织描述")


class OrganizationResponse(OrganizationBase):
    """组织信息响应模型"""
    id: int
    owner_user_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    logo_url: Optional[str] = None
    
    # 扩展信息（可选）
    member_count: Optional[int] = None
    current_user_role: Optional[OrganizationRole] = None

    class Config:
        from_attributes = True


# ===============================
# 组织成员相关模型
# ===============================

class OrganizationMemberBase(BaseModel):
    """组织成员基础信息模型"""
    organization_username: str = Field(..., min_length=1, max_length=64, description="组织内用户名")


class OrganizationMemberUpdate(BaseModel):
    """更新组织成员请求模型"""
    organization_username: Optional[str] = Field(None, min_length=1, max_length=64, description="组织内用户名")
    is_active: Optional[bool] = Field(None, description="是否激活")


class OrganizationMemberResponse(OrganizationMemberBase):
    """组织成员信息响应模型"""
    id: int
    organization_id: int
    user_id: int
    role: OrganizationRole
    is_active: bool
    joined_at: datetime
    created_at: datetime
    updated_at: datetime
    
    # 扩展用户信息（可选）
    user_email: Optional[str] = None
    user_nickname: Optional[str] = None
    user_avatar_url: Optional[str] = None

    class Config:
        from_attributes = True





# ===============================
# 用户邀请码相关模型
# ===============================

class UserInvitationLinkResponse(BaseModel):
    """用户邀请链接响应模型"""
    invitation_code: str
    organization_id: int
    organization_name: str
    invite_link: str
    expires_at: Optional[str] = None
    remaining_hours: Optional[int] = None
    status: Optional[str] = None


class UserInvitationDetailResponse(BaseModel):
    """用户邀请详情响应模型（通过用户邀请码访问）"""
    invitation_code: str
    invited_by_username: str
    invited_by_nickname: Optional[str] = None
    invited_by_organization_username: Optional[str] = None
    organization_id: int
    organization_name: str
    organization_description: Optional[str] = None


class JoinOrganizationRequest(BaseModel):
    """加入组织请求模型（通过动态邀请码）"""
    invitation_code: str = Field(..., min_length=36, max_length=36, description="动态邀请码（UUID格式）")
    organization_id: int = Field(..., description="组织ID")
    organization_username: str = Field(..., min_length=1, max_length=64, description="组织内用户名")

    @validator('organization_username')
    def validate_organization_username(cls, v):
        """验证组织用户名格式"""
        if not v.strip():
            raise ValueError('组织用户名不能为空')
        return v.strip()


class JoinOrganizationResponse(BaseModel):
    """加入组织响应模型"""
    message: str
    organization_id: int
    organization_name: str
    organization_username: str
    role: str


# ===============================
# 个人邀请码相关模型
# ===============================

class PersonalInvitationLinkResponse(BaseModel):
    """个人邀请链接响应模型"""
    invitation_code: str
    invite_link: str


class PersonalInvitationStatsResponse(BaseModel):
    """个人邀请统计响应模型"""
    invitation_code: str
    total_invitations: int
    successful_invitations: int
    pending_rewards: int
    total_rewards: int


class ValidateInvitationCodeRequest(BaseModel):
    """验证邀请码请求模型"""
    invitation_code: str = Field(..., min_length=6, max_length=6, description="邀请码")


class ValidateInvitationCodeResponse(BaseModel):
    """验证邀请码响应模型"""
    is_valid: bool
    inviter_username: Optional[str] = None
    message: str


class PersonalInvitationResponse(BaseModel):
    """个人邀请记录响应模型"""
    id: int
    inviter_user_id: int
    invitee_user_id: int
    invitation_code: str
    inviter_rewarded: bool
    invitee_rewarded: bool
    reward_package_id: str
    created_at: datetime
    inviter_rewarded_at: Optional[datetime] = None
    invitee_rewarded_at: Optional[datetime] = None

    # 扩展信息
    inviter_username: Optional[str] = None
    invitee_username: Optional[str] = None


# ===============================
# 身份管理相关模型
# ===============================

class UserIdentity(BaseModel):
    """用户身份信息模型"""
    identity_type: IdentityType
    organization_id: Optional[int] = None
    organization_name: Optional[str] = None
    organization_role: Optional[OrganizationRole] = None
    organization_username: Optional[str] = None


class UserIdentitiesResponse(BaseModel):
    """用户可选身份列表响应模型"""
    current_identity: UserIdentity
    available_identities: List[UserIdentity]


class SwitchIdentityRequest(BaseModel):
    """切换身份请求模型"""
    identity_type: IdentityType
    organization_id: Optional[int] = Field(None, description="组织ID，切换到组织身份时必填")
    
    @validator('organization_id')
    def validate_organization_id(cls, v, values):
        """验证组织ID"""
        if values.get('identity_type') == IdentityType.ORGANIZATION and v is None:
            raise ValueError('切换到组织身份时必须提供组织ID')
        if values.get('identity_type') == IdentityType.PERSONAL and v is not None:
            raise ValueError('切换到个人身份时不应提供组织ID')
        return v


class CurrentIdentityResponse(BaseModel):
    """当前身份信息响应模型"""
    user_id: int
    identity_type: IdentityType
    organization_id: Optional[int] = None
    organization_name: Optional[str] = None
    organization_role: Optional[OrganizationRole] = None
    organization_username: Optional[str] = None
    session_token: Optional[str] = None


# ===============================
# 扩展的用户响应模型
# ===============================

class UserWithIdentityResponse(BaseModel):
    """包含身份信息的用户响应模型"""
    id: int
    username: str
    email: str
    nickname: Optional[str] = None
    role: str
    is_active: bool
    # 微信登录相关字段
    openid: Optional[str] = None
    unionid: Optional[str] = None
    wechat_nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    login_type: Optional[str] = "password"
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    # 身份信息
    current_identity: UserIdentity
    available_identities: List[UserIdentity]

    class Config:
        from_attributes = True


# ===============================
# 统计和概览模型
# ===============================

class OrganizationStatsResponse(BaseModel):
    """组织统计信息响应模型"""
    total_members: int
    active_members: int


class OrganizationOverviewResponse(OrganizationResponse):
    """组织概览响应模型（包含统计信息）"""
    stats: OrganizationStatsResponse
    recent_members: List[OrganizationMemberResponse]
