from typing import Optional, List, Any, Dict
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class NotificationType(str, Enum):
    """通知类型枚举"""
    MAINTENANCE = "maintenance"     # 维护通知
    FEATURE = "feature"            # 功能更新
    SECURITY = "security"          # 安全提醒
    PROMOTION = "promotion"        # 促销活动
    GENERAL = "general"            # 一般通知


class NotificationPriority(str, Enum):
    """通知优先级枚举"""
    HIGH = "high"      # 重要
    MEDIUM = "medium"  # 一般
    LOW = "low"        # 普通


class SystemNotificationBase(BaseModel):
    """系统通知基础模式"""
    title: str = Field(..., max_length=200, description="通知标题")
    content: str = Field(..., description="通知内容")
    type: NotificationType = Field(default=NotificationType.GENERAL, description="通知类型")
    priority: NotificationPriority = Field(default=NotificationPriority.MEDIUM, description="优先级")
    is_active: bool = Field(default=True, description="是否激活显示")
    expire_at: Optional[datetime] = Field(None, description="过期时间，过期后自动隐藏")
    action_data: Optional[Dict[str, Any]] = Field(None, description="扩展数据，存储点击行为等信息")


class SystemNotificationCreate(SystemNotificationBase):
    """创建系统通知的请求模式"""
    pass


class SystemNotificationResponse(SystemNotificationBase):
    """系统通知响应模式"""
    id: int = Field(..., description="通知ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class NotificationListResponse(BaseModel):
    """通知列表响应模式"""
    notifications: List[SystemNotificationResponse] = Field(..., description="通知列表")
    total: int = Field(..., description="总数量")


class NotificationCountResponse(BaseModel):
    """通知数量响应模式"""
    total_count: int = Field(..., description="总通知数量") 