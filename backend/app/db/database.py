from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from app.core.config import settings

# 创建异步引擎 (优化阿里云RDS PostgreSQL连接)
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,  # 设置为 True 会在控制台显示 SQL 语句，方便调试
    future=True,  # 使用 SQLAlchemy 2.0 特性
    
    # # 连接池优化 - 本地数据库：大幅增加连接数以提高并发性能
    # pool_size=50,  # 增加连接池大小至50以支持更多并发连接
    # max_overflow=100,  # 增加最大溢出连接数至100
    # pool_timeout=20,  # 减少获取连接的超时时间至20秒
    # pool_recycle=3600,  # 连接回收时间(1小时)，防止连接被数据库服务器断开
    # pool_pre_ping=True,  # 启用连接前ping，确保连接有效性
    
    # 连接池优化 - 适配阿里云RDS云数据库环境
    pool_size=20,  # 降低连接池大小，云数据库连接宝贵
    max_overflow=40,  # 适当减少最大溢出连接数
    pool_timeout=30,  # 增加获取连接的超时时间（云数据库网络延迟）
    pool_recycle=3600,  # 连接回收时间(1小时)，防止连接被数据库服务器断开
    pool_pre_ping=True,  # 启用连接前ping，确保连接有效性（云数据库必需）
    
    connect_args={
        # 阿里云RDS PostgreSQL连接配置 (根据实际SSL状态调整)
        "ssl": "disable",  # 阿里云RDS SSL状态为off，使用disable模式
        # "ssl": "prefer",  # 备选：prefer模式，优先尝试SSL但不强制要求
        # "ssl": "require",  # 如果RDS开启了强制SSL，使用此选项
        
        # 连接超时优化 - 适配云数据库网络环境
        "command_timeout": 30,  # 增加命令超时时间至30秒（云数据库网络延迟）/ 本地数据库时设置为10秒
        "server_settings": {
            "timezone": "Asia/Shanghai",  # 设置时区为上海时区
            "application_name": "TunshuEdu_Backend",  # 应用标识，方便在RDS监控中识别
        }
    }
)

# 创建异步会话工厂（性能优化配置）
AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,  # 提交后不过期，允许在事务提交后仍使用对象
    autoflush=False,  # 禁用自动刷新，手动控制刷新时机以提高性能
    autocommit=False,  # 禁用自动提交，手动控制事务
)

# 创建所有模型的基类
Base = declarative_base()

# 创建异步获取数据库会话的依赖函数
async def get_db():
    """
    获取异步数据库会话的依赖函数，用于 FastAPI 依赖注入
    """
    session = AsyncSessionLocal()
    try:
        yield session
        # 如果没有异常，则提交事务
        await session.commit()
    except Exception as e:
        # 如果有异常，则回滚事务
        await session.rollback()
        raise e
    finally:
        # 最后关闭会话
        await session.close()