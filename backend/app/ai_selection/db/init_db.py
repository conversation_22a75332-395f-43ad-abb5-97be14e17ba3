"""
AI选校系统数据库初始化脚本
用于为现有数据生成嵌入向量
"""
import asyncio
from app.ai_selection.db.seed import generate_embeddings_for_existing_data, clear_all_embeddings

async def init_ai_selection_db():
    """为AI选校系统现有数据生成嵌入向量"""
    
    # 清空现有嵌入向量
    await clear_all_embeddings()

    print("开始为AI选校系统现有数据生成嵌入向量...")
    
    # 生成嵌入向量
    await generate_embeddings_for_existing_data()
    
    print("AI选校系统嵌入向量生成完成！")

if __name__ == "__main__":
    asyncio.run(init_ai_selection_db()) 