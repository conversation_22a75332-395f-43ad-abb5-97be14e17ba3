from sqlalchemy import Column, Integer, String, Float, Text, ForeignKey, DateTime, JSON, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.db.database import Base
from datetime import datetime

# 项目模型（合并了学校信息和地区信息）
class AISelectionProgram(Base):
    __tablename__ = "ai_selection_programs"
    
    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校信息
    school_name_cn = Column(String(50), nullable=False, index=True, comment="学校中文名")
    school_name_en = Column(String(100), nullable=True, index=True, comment="学校英文名")
    school_region = Column(String(10), nullable=True, comment="学校所在地区")
    # school_labels = Column(String(200), nullable=True, comment="学校标签")
    # school_ranks = Column(String(100), nullable=True, comment="学校排名信息")
    school_qs_rank = Column(String(20), nullable=True, comment="学校QS排名")

    # 项目基本信息
    program_code = Column(Integer, nullable=True, comment="专业代码")
    program_website = Column(String(300), nullable=True, comment="项目官网")
    program_name_cn = Column(String(100), nullable=False, index=True, comment="项目中文名")
    program_name_en = Column(String(200), nullable=True, index=True, comment="项目英文名")
    program_category = Column(String(50), nullable=True, comment="项目类别")
    program_direction = Column(String(50), nullable=True, comment="项目方向")
    faculty = Column(String(50), nullable=True, comment="所在学院")
    enrollment_time = Column(String(50), nullable=True, comment="入学时间")
    program_duration = Column(String(50), nullable=True, comment="项目时长")
    program_tuition = Column(String(100), nullable=True, comment="项目学费")

    # 项目申请相关信息
    application_time = Column(Text, nullable=True, comment="申请时间")
    application_requirements = Column(Text, nullable=True, comment="申请要求")
    gpa_requirements = Column(Text, nullable=True, comment="GPA要求")
    language_requirements = Column(Text, nullable=True, comment="语言要求")
    # interview_type = Column(String(50), nullable=True, comment="面试类型")
    # interview_experience = Column(Text, nullable=True, comment="面试经验")

    # 项目详情
    program_objectives = Column(Text, nullable=True, comment="项目培养目标")
    courses = Column(Text, nullable=True, comment="课程设置")
    # consultant_analysis = Column(Text, nullable=True, comment="顾问分析")

    # 其他信息
    other_cost = Column(String(100), nullable=True, comment="其他费用")
    degree = Column(String(10), nullable=True, comment="申请学位类型")
    degree_evaluation = Column(Text, nullable=True, comment="留服认证")
    
    # 向量嵌入（用于AI匹配）
    embedding = Column(JSONB, nullable=True, comment="项目描述的向量嵌入")

    # 时间戳字段
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联到案例
    cases = relationship("AISelectionCase", back_populates="offer_program_rel")
    
    # 关键：建立与分数要求表的“一对多”关系
    # 'scores' 是一个虚拟字段，可以让你通过 program.scores 访问其所有分数要求
    scores = relationship("AISelectionProgramScoreRequirement", back_populates="program", cascade="all, delete-orphan")

# 案例模型
class AISelectionCase(Base):
    """
    案例数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_cases 表
    存储学生案例信息数据
    """
    __tablename__ = "ai_selection_cases"
    
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 录取信息
    offer_school = Column(String(200), nullable=True, comment="录取学校")
    offer_program = Column(String(200), nullable=True, comment="录取项目")
    offer_program_id = Column(Integer, ForeignKey("ai_selection_programs.id"), comment="录取项目ID")
    offer_program_code = Column(Integer, nullable=True, comment="录取项目代码")
    offer_region = Column(String(100), nullable=True, comment="录取地区")
    offer_degree = Column(String(50), nullable=True, comment="录取学位")
    offer_major_direction = Column(String(200), nullable=True, comment="录取专业方向")
    
    # 学生基本信息
    student_name = Column(String(100), nullable=True, comment="学生姓名")
    undergraduate_school = Column(String(200), nullable=True, comment="本科学校")
    undergraduate_school_tier = Column(String(50), nullable=True, comment="本科学校层次")
    undergraduate_major = Column(String(200), nullable=True, comment="本科专业")
    gpa = Column(Float, nullable=True, comment="绩点")
    language_score = Column(String(200), nullable=True, comment="语言成绩")
    key_experiences = Column(Text, nullable=True, comment="关键经历描述")
    embedding = Column(JSONB, nullable=True, comment="案例描述的向量嵌入")
    
    # 关系字段 - 重命名避免与offer_program字段冲突
    offer_program_rel = relationship("AISelectionProgram", back_populates="cases") 

# 境内院校模型
class AISelectionHomeSchool(Base):
    """
    境内院校数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_home_schools 表
    存储境内院校信息数据
    """
    __tablename__ = "ai_selection_home_schools"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")

    # 学校基本信息
    school_name = Column(String(200), nullable=False, index=True, comment="学校名称")
    school_name_en = Column(String(200), nullable=True, index=True, comment="学校英文名称")
    school_type = Column(String(100), nullable=True, comment="学校类型")
    school_code = Column(String(50), nullable=True, index=True, comment="学校标识码")
    location = Column(String(100), nullable=True, comment="所在地")
    authority = Column(String(200), nullable=True, comment="主管部门")

    # 排名和层级信息
    ranking_ruanke = Column(Integer, nullable=True, comment="软科排名")
    school_tier = Column(String(50), nullable=True, index=True, comment="学校层级(如985、211、双一流等)")

    # GPA要求信息 (JSON格式存储不同目标院校的GPA要求)
    gpa_requirements = Column(JSON, nullable=True, comment="GPA要求，JSON格式存储各目标院校的GPA要求")

    # 其他信息
    remarks = Column(Text, nullable=True, comment="备注")

# 境外院校模型
class AISelectionAbroadSchool(Base):
    """
    境外院校数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_abroad_schools 表
    存储境外院校信息数据
    """
    __tablename__ = "ai_selection_abroad_schools"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校基本信息
    school_name_cn = Column(String(100), nullable=False, index=True, comment="学校中文名")
    school_name_en = Column(String(100), nullable=True, index=True, comment="学校英文名")
    school_code = Column(Integer, nullable=True, index=True, comment="学校代码")
    school_city = Column(String(20), nullable=True, comment="学校所在城市")
    school_region = Column(String(10), nullable=True, comment="学校所在地区/国家")
    
    # 排名信息
    school_qs_rank = Column(String(20), nullable=True, comment="学校QS排名")
    school_usnews_rank = Column(String(20), nullable=True, comment="学校US News排名")
    school_the_rank = Column(String(20), nullable=True, comment="学校THE排名")
    school_arwu_rank = Column(String(20), nullable=True, comment="学校ARWU排名")
    school_ranks = Column(String(100), nullable=True, comment="学校综合排名信息")
    
    # 其他信息
    school_labels = Column(String(200), nullable=True, comment="学校标签")
    school_logo_url = Column(String(100), nullable=True, comment="学校logo链接")

# 分数要求表
class AISelectionProgramScoreRequirement(Base):
    """
    项目分数要求数据库模型类，存储每个项目对特定本科院校的分数要求。
    这是一个“长”表结构，具有良好的扩展性和查询性能。
    """
    __tablename__ = "ai_selection_program_score_requirements"

    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")

    # 外键，关联到项目信息表
    program_id = Column(Integer, ForeignKey("ai_selection_programs.id"), nullable=False, index=True)

    # 具体的本科院校名称
    home_school_name = Column(String(100), nullable=False, index=True, comment="国内本科院校名称")

    # 分数要求
    # 允许分数为 NULL，以表示“无法申请”
    score_requirement = Column(Float, nullable=True, comment="分数要求, NULL表示无法申请")

    # 时间戳字段
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关键：建立与项目表的反向关系
    # 'program' 是一个虚拟字段，可以让你通过 score.program 访问其所属的项目信息
    program = relationship("AISelectionProgram", back_populates="scores")

    #  添加 program_id 和 home_school_name 的复合唯一约束
    __table_args__ = (
        UniqueConstraint('program_id', 'home_school_name', name='uq_program_home_school'),
    )

    def __repr__(self):
        return f"<Score(program_id={self.program_id}, school='{self.home_school_name}', score={self.score_requirement})>"