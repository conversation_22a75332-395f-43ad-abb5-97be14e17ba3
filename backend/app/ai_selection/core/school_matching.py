from typing import List, Dict, Any, Tuple, Optional
import json
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sqlalchemy import select, and_

from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case
)
from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.utils.rag import embed_text
from app.ai_selection.utils.language_parser import parse_language_score_safely
from app.ai_selection.config import GPA_TOLERANCE, MIN_CASE_THRESHOLD
from app.ai_selection.utils.ranking_parser import parse_qs_rank

async def identify_reachable_schools(user_profile: EnhancedUserProfile) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    识别用户可达的院校圈
    优先查询同本科院校的案例（更精准），再查询同层级院校案例作为补充
    
    Args:
        user_profile: 增强用户画像
        
    Returns:
        可达院校列表和支持案例列表的元组
    """
    async for session in get_db():
        # 提取核心用户背景
        tier = user_profile.undergraduate_school_tier
        gpa = user_profile.gpa
        major = user_profile.undergraduate_major
        
        # 第一优先级：基于具体本科院校的精准匹配查询
        query1 = (
            select(
                Case.id,
                Case.student_name,
                Case.undergraduate_school,
                Case.undergraduate_school_tier,
                Case.undergraduate_major,
                Case.gpa,
                Case.offer_region,
                Case.offer_degree,
                Case.offer_major_direction,
                Case.key_experiences,
                Case.language_score,
                Case.offer_program_id,
                Program.school_name_cn.label("offer_school_name"),
                Program.school_qs_rank.label("offer_school_qs_rank"),
                Program.program_name_cn.label("offer_program_name")
            )
            .join(Program, Case.offer_program_id == Program.id)
            .where(
                and_(
                    Case.undergraduate_school == user_profile.undergraduate_school,
                    Case.gpa.between(gpa * (1 - GPA_TOLERANCE), gpa * (1 + GPA_TOLERANCE))
                )
            )
        )
        
        result1 = await session.execute(query1)
        rows1 = result1.fetchall()
        
        # 收集已经查询到的案例ID，避免重复
        existing_case_ids = {row.id for row in rows1}
        
        # 第二优先级：基于院校层级的补充查询（排除已有的同校案例）
        query2 = (
            select(
                Case.id,
                Case.student_name,
                Case.undergraduate_school,
                Case.undergraduate_school_tier,
                Case.undergraduate_major,
                Case.gpa,
                Case.offer_region,
                Case.offer_degree,
                Case.offer_major_direction,
                Case.key_experiences,
                Case.language_score,
                Case.offer_program_id,
                Program.school_name_cn.label("offer_school_name"),
                Program.school_qs_rank.label("offer_school_qs_rank"),
                Program.program_name_cn.label("offer_program_name")
            )
            .join(Program, Case.offer_program_id == Program.id)
            .where(
                and_(
                    Case.undergraduate_school_tier == tier,
                    Case.gpa.between(gpa * (1 - GPA_TOLERANCE), gpa * (1 + GPA_TOLERANCE)),
                    ~Case.id.in_(existing_case_ids) if existing_case_ids else True  # 排除已有案例
                )
            )
        )
        
        result2 = await session.execute(query2)
        rows2 = result2.fetchall()
        
        # 合并查询结果，优先显示同校案例
        cases_hard_match = []
        
        # 先处理同校案例（标记为高优先级）
        for row in rows1:
            case_dict = {
                "id": row.id,
                "student_name": row.student_name,
                "undergraduate_school": row.undergraduate_school,
                "undergraduate_school_tier": row.undergraduate_school_tier,
                "undergraduate_major": row.undergraduate_major,
                "gpa": row.gpa,
                "offer_region": row.offer_region,
                "offer_degree": row.offer_degree,
                "offer_major_direction": row.offer_major_direction,
                "key_experiences": row.key_experiences,
                "language_score": parse_language_score_safely(row.language_score),
                "offer_program_id": row.offer_program_id,
                "offer_school_name": row.offer_school_name,
                "offer_school_qs_rank": row.offer_school_qs_rank,
                "offer_program_name": row.offer_program_name,
                "match_type": "same_school",  # 标记案例类型
                "priority": 1  # 高优先级
            }
            cases_hard_match.append(case_dict)
        
        # 再处理同层级案例（标记为中优先级）
        for row in rows2:
            case_dict = {
                "id": row.id,
                "student_name": row.student_name,
                "undergraduate_school": row.undergraduate_school,
                "undergraduate_school_tier": row.undergraduate_school_tier,
                "undergraduate_major": row.undergraduate_major,
                "gpa": row.gpa,
                "offer_region": row.offer_region,
                "offer_degree": row.offer_degree,
                "offer_major_direction": row.offer_major_direction,
                "key_experiences": row.key_experiences,
                "language_score": parse_language_score_safely(row.language_score),
                "offer_program_id": row.offer_program_id,
                "offer_school_name": row.offer_school_name,
                "offer_school_qs_rank": row.offer_school_qs_rank,
                "offer_program_name": row.offer_program_name,
                "match_type": "same_tier",  # 标记案例类型
                "priority": 2  # 中优先级
            }
            cases_hard_match.append(case_dict)
        
        # 如果硬匹配结果太少，使用向量检索补充（简化版本）
        if len(cases_hard_match) < MIN_CASE_THRESHOLD:
            # 这里可以添加向量检索逻辑，暂时跳过复杂实现
            pass
        
        # 统计各学校出现频次，同校案例给予更高权重
        school_counts = {}
        for case in cases_hard_match:
            school_name = case["offer_school_name"]
            school_qs_rank = case["offer_school_qs_rank"]
            case_weight = 2.0 if case["match_type"] == "same_school" else 1.0  # 同校案例权重更高
            
            if school_name not in school_counts:
                school_counts[school_name] = {
                    "name": school_name,
                    "qs_rank": school_qs_rank,
                    "tier": _infer_school_tier_from_qs_rank(school_qs_rank),
                    "count": 0,
                    "weighted_count": 0.0,  # 新增加权计数
                    "cases": [],
                    "same_school_cases": 0,  # 同校案例数量
                    "same_tier_cases": 0     # 同层级案例数量
                }
            
            school_counts[school_name]["count"] += 1
            school_counts[school_name]["weighted_count"] += case_weight
            school_counts[school_name]["cases"].append(case["id"])
            
            # 分别统计不同类型的案例数量
            if case["match_type"] == "same_school":
                school_counts[school_name]["same_school_cases"] += 1
            else:
                school_counts[school_name]["same_tier_cases"] += 1
        
        # 转换为列表并按加权频次排序（同校案例更有影响力）
        reachable_schools = list(school_counts.values())
        reachable_schools.sort(key=lambda x: (x["weighted_count"], x["count"]), reverse=True)
        
        return reachable_schools, cases_hard_match

def determine_dynamic_qs_range(
    tier: str,
    reachable_schools: List[Dict[str, Any]]
) -> Tuple[Optional[int], Optional[int]]:
    """
    根据本科层级和可达院校案例，动态确定一个合理的QS排名筛选范围。
    """
    base_ranges = {
        "tier1": (1, 200),  # 985学生，目标范围更广
        "tier2": (50, 300), # 211学生
        "tier3": (100, 500) # 其他学生
    }
    min_rank, max_rank = base_ranges.get(tier, (100, 500))

    if not reachable_schools:
        return min_rank, max_rank

    # 从可达院校案例中提取QS排名
    case_ranks = []
    for school in reachable_schools:
        qs_rank_str = school.get("qs_rank")
        rank = parse_qs_rank(qs_rank_str)
        if rank:
            case_ranks.append(rank)

    if not case_ranks:
        return min_rank, max_rank

    # 基于案例动态调整范围
    median_rank = int(np.median(case_ranks))

    # 根据本科层级设置不同的扩展范围
    if tier == "tier1":
        # 985学生可以向上多探索，向下保底
        dynamic_min = max(1, median_rank - 100)
        dynamic_max = median_rank + 150
    elif tier == "tier2":
        dynamic_min = max(1, median_rank - 75)
        dynamic_max = median_rank + 150
    else: # tier3
        dynamic_min = max(1, median_rank - 50)
        dynamic_max = median_rank + 200

    # 与基础范围融合，取并集
    final_min = min(min_rank, dynamic_min) if min_rank is not None else dynamic_min
    final_max = max(max_rank, dynamic_max) if max_rank is not None else dynamic_max

    print(f"动态QS范围确定: tier='{tier}', median_rank={median_rank}, range=({final_min}, {final_max})")

    return final_min, final_max

async def calculate_school_match_scores(
    candidate_pool: List[Dict[str, Any]], 
    reachable_schools: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    计算每个候选学校的院校层级匹配分数
    考虑同校案例的权重和案例类型的差异
    
    Args:
        candidate_pool: 候选池
        reachable_schools: 可达院校列表
        
    Returns:
        带有匹配分数的候选池
    """
    # 创建学校名称到可达性的映射
    reachable_map = {school["name"]: school for school in reachable_schools}
    
    # 为候选池中的每个项目计算匹配分数
    for candidate in candidate_pool:
        school_name = candidate.get("school_name_cn") or candidate.get("school_name")
        
        # 如果学校在可达学校列表中，计算匹配分数
        if school_name in reachable_map:
            reachable_info = reachable_map[school_name]
            
            # 使用加权计数和案例类型信息计算更精确的匹配分数
            weighted_count = reachable_info.get("weighted_count", 0)
            same_school_cases = reachable_info.get("same_school_cases", 0)
            same_tier_cases = reachable_info.get("same_tier_cases", 0)
            total_cases = reachable_info.get("count", 0)
            
            # 基础匹配分数：基于加权案例数量
            base_score = min(1.0, weighted_count / 8)  # 调整分母，因为同校案例权重为2
            
            # 同校案例奖励：如果有同校案例，额外加分
            same_school_bonus = min(0.3, same_school_cases * 0.1)  # 最多30%的奖励
            
            # 案例丰富度奖励：如果案例数量足够多，额外加分
            richness_bonus = min(0.1, (total_cases - 3) * 0.02) if total_cases > 3 else 0
            
            # 计算最终匹配分数
            school_tier_match = min(1.0, base_score + same_school_bonus + richness_bonus)
            
            candidate["school_tier_match"] = school_tier_match
            candidate["matching_cases"] = reachable_info["cases"]
            candidate["same_school_cases_count"] = same_school_cases
            candidate["same_tier_cases_count"] = same_tier_cases
            candidate["case_support_detail"] = {
                "total_cases": total_cases,
                "same_school_cases": same_school_cases,
                "same_tier_cases": same_tier_cases,
                "weighted_score": weighted_count
            }
        else:
            # 如果学校不在可达列表中，根据层级判断
            candidate_tier = candidate.get("school_tier", "tier3")
            
            # 找出可达学校的主要层级
            if reachable_schools:
                main_tier = reachable_schools[0]["tier"]
                
                if main_tier == "tier1":
                    if candidate_tier == "tier1":
                        candidate["school_tier_match"] = 0.8  # 可达学校主要是tier1，申请tier1有一定难度
                    elif candidate_tier == "tier2":
                        candidate["school_tier_match"] = 0.9  # 可达学校主要是tier1，申请tier2较容易
                    else:  # tier3
                        candidate["school_tier_match"] = 1.0  # 可达学校主要是tier1，申请tier3很容易
                elif main_tier == "tier2":
                    if candidate_tier == "tier1":
                        candidate["school_tier_match"] = 0.5  # 可达学校主要是tier2，申请tier1有较大难度
                    elif candidate_tier == "tier2":
                        candidate["school_tier_match"] = 0.8  # 可达学校主要是tier2，申请tier2有一定难度
                    else:  # tier3
                        candidate["school_tier_match"] = 0.9  # 可达学校主要是tier2，申请tier3较容易
                else:  # tier3
                    if candidate_tier == "tier1":
                        candidate["school_tier_match"] = 0.3  # 可达学校主要是tier3，申请tier1难度很大
                    elif candidate_tier == "tier2":
                        candidate["school_tier_match"] = 0.5  # 可达学校主要是tier3，申请tier2有较大难度
                    else:  # tier3
                        candidate["school_tier_match"] = 0.7  # 可达学校主要是tier3，申请tier3有一定难度
            else:
                # 如果没有可达学校信息，给一个默认分数
                candidate["school_tier_match"] = 0.5
            
            candidate["matching_cases"] = []
            candidate["same_school_cases_count"] = 0
            candidate["same_tier_cases_count"] = 0
            candidate["case_support_detail"] = {
                "total_cases": 0,
                "same_school_cases": 0,
                "same_tier_cases": 0,
                "weighted_score": 0
            }
    
    return candidate_pool

def _infer_school_tier_from_qs_rank(qs_rank: str) -> str:
    """
    根据QS排名推断学校层级
    
    Args:
        qs_rank: QS排名字符串
        
    Returns:
        学校层级 (tier1, tier2, tier3)
    """
    if not qs_rank:
        return "tier3"  # 默认层级
    
    try:
        rank = int(qs_rank)
        if rank <= 50:
            return "tier1"
        elif rank <= 200:
            return "tier2"
        else:
            return "tier3"
    except (ValueError, TypeError):
        return "tier3"  # 如果无法解析排名，返回默认层级 

async def calculate_school_ranking_scores(
    candidate_pool: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    计算每个候选学校的排名评分
    基于QS排名位置给出标准化评分
    
    Args:
        candidate_pool: 候选池
        
    Returns:
        带有排名评分的候选池
    """
    for candidate in candidate_pool:
        qs_rank_str = candidate.get("school_qs_rank")
        ranking_score = calculate_ranking_score(qs_rank_str)
        candidate["school_ranking_score"] = ranking_score
    
    return candidate_pool

def calculate_ranking_score(qs_rank_str: str) -> float:
    """
    根据QS排名计算标准化排名评分
    
    Args:
        qs_rank_str: QS排名字符串
        
    Returns:
        0-1之间的排名评分，越高表示排名越好
    """
    if not qs_rank_str:
        return 0.0
    
    rank = parse_qs_rank(qs_rank_str)
    if not rank:
        return 0.0
    
    # 分段计算排名评分，体现排名的边际效应
    if rank <= 10:
        return 1.0  # 顶级名校
    elif rank <= 50:
        return 0.9 - (rank - 10) * 0.01  # 0.9-0.5线性递减
    elif rank <= 100:
        return 0.5 - (rank - 50) * 0.006  # 0.5-0.2线性递减
    elif rank <= 200:
        return 0.2 - (rank - 100) * 0.001  # 0.2-0.1线性递减
    elif rank <= 500:
        return 0.1 - (rank - 200) * 0.0002  # 0.1-0.04线性递减
    else:
        return 0.02  # 500名后给予很低但非零的分数

 