from typing import List, Dict, Any, Tuple, Optional
import asyncio
import math

from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.utils.rag import match_program_with_experience_async



async def _get_program_gpa_requirement_by_id(
    program_id: int,
    undergraduate_school: str
) -> Optional[float]:
    """
    根据program_id查询精确的GPA要求（新版本）

    Args:
        program_id: 项目ID
        undergraduate_school: 本科院校名称

    Returns:
        GPA要求值，如果未找到则返回None
    """
    try:
        # 使用新的基于program_id的查询方式
        from app.ai_selection.core.candidate_pool import get_program_score_requirements_by_program_ids

        score_requirements = await get_program_score_requirements_by_program_ids(
            [program_id], undergraduate_school
        )

        if program_id in score_requirements:
            score_requirement = score_requirements[program_id]
            if score_requirement is not None:
                print(f"找到 program_id {program_id} 的精确GPA要求: {score_requirement} (本科院校: {undergraduate_school})")
                return float(score_requirement)
            else:
                print(f"program_id {program_id} 不认可本科院校 {undergraduate_school}")
                return None
        else:
            print(f"program_id {program_id} 不在精确要求表中")
            return None

    except Exception as e:
        print(f"查询 program_id {program_id} 的精确GPA要求时发生错误: {e}")
        return None





async def _process_single_candidate(
    candidate: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    cases: List[Dict[str, Any]],
    index: int,
    user=None,
    request_id: str = None
) -> Dict[str, Any]:
    """
    处理单个候选专业的所有匹配评估 - 异步版本
    
    Args:
        candidate: 候选专业
        user_profile: 用户画像
        cases: 案例列表
        index: 候选专业索引（用于调试）
        
    Returns:
        更新后的候选专业信息
    """
    try:
        # 创建所有匹配任务
        direction_task = _calculate_program_direction_match(candidate, user_profile, user, request_id)

        experience_task = _calculate_experience_match_async(
            candidate, user_profile, cases, user, request_id
        )

        academic_task = _calculate_academic_performance_match_async(
            candidate, user_profile, cases
        )

        # 并发执行所有匹配评估
        direction_match, (experience_match, experience_reason), academic_match = await asyncio.gather(
            direction_task,
            experience_task, 
            academic_task
        )
        
        # 更新候选专业信息
        candidate.update({
            "program_direction_match": direction_match,
            "experience_match": experience_match,
            "experience_match_reason": experience_reason,
            "academic_performance_match": academic_match
        })
        
        return candidate
        
    except Exception as e:
        print(f"处理候选专业 {index} 时出错: {e}")
        # 返回带有默认值的候选专业
        candidate.update({
            "program_direction_match": 0.5,
            "experience_match": 0.5,
            "experience_match_reason": f"处理出错: {str(e)}",
            "academic_performance_match": 0.5
        })
        return candidate

async def _calculate_experience_match_async(
    candidate: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    cases: List[Dict[str, Any]],
    user=None,
    request_id: str = None
) -> Tuple[float, str]:
    """
    异步版本的经历匹配计算
    """
    if user_profile.key_experiences:
        # 使用异步版本的经历匹配函数，传递user_id和request_id
        experience_match, match_reason = await match_program_with_experience_async(
            candidate,
            user_profile.key_experiences,
            cases,
            user=user,
            request_id=request_id,
            use_credits=False,
            use_tokens=False
        )
        return experience_match, match_reason
    else:
        return 0.5, "未提供经历信息，无法进行详细匹配分析。"
        
async def _calculate_academic_performance_match_async(
    candidate: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    cases: List[Dict[str, Any]]
) -> float:
    """
    异步版本的学术表现匹配计算，集成新的GPA筛选机制

    Args:
        candidate: 候选专业信息
        user_profile: 用户画像
        cases: 案例列表

    Returns:
        学术表现匹配分数 (0.0-1.0)
    """
    # 获取用户GPA和本科院校
    user_gpa = user_profile.gpa
    undergraduate_school = user_profile.undergraduate_school
    target_school = candidate.get("school_name_cn", "")

    print(f"计算学术表现匹配: {undergraduate_school} → {target_school}, 用户GPA: {user_gpa}")

    # 第一优先级：使用案例数据计算平均GPA
    program_gpa_req = 0.0
    case_based_gpa = None

    if cases:
        program_id = candidate.get("id")
        matching_cases = [case for case in cases if case.get("offer_program_id") == program_id]

        if matching_cases:
            avg_gpa = sum(case.get("gpa", 0) for case in matching_cases) / len(matching_cases)
            case_based_gpa = avg_gpa
            program_gpa_req = avg_gpa
            print(f"使用案例数据计算的平均GPA: {avg_gpa:.1f} (基于 {len(matching_cases)} 个案例)")

    # 第二优先级：当案例数据不足时，使用精确的program GPA要求
    if program_gpa_req == 0.0 and undergraduate_school:
        program_id = candidate.get("id")
        if program_id:
            # 优先使用新的基于program_id的精确查询
            precise_gpa_req = await _get_program_gpa_requirement_by_id(
                program_id, undergraduate_school
            )

            if precise_gpa_req is not None:
                program_gpa_req = precise_gpa_req
                print(f"使用精确的program GPA要求: {precise_gpa_req}")
            else:
                # 如果精确查询失败，使用默认值
                program_gpa_req = 80  # 假设80分是一个常见的基准线
                print(f"未找到精确GPA要求，使用默认GPA要求: {program_gpa_req}")
        else:
            program_gpa_req = 80
            print(f"无program_id，使用默认GPA要求: {program_gpa_req}")
    elif program_gpa_req == 0.0:
        program_gpa_req = 80
        print(f"使用默认GPA要求: {program_gpa_req}")

    # 计算GPA匹配度
    if user_gpa >= program_gpa_req * 1.1:  # 显著高于要求
        gpa_match = 1.0
    elif user_gpa >= program_gpa_req:  # 达到或略高于要求
        gpa_match = 0.8
    elif user_gpa >= program_gpa_req * 0.95:  # 略低于要求
        gpa_match = 0.6
    elif user_gpa >= program_gpa_req * 0.9:  # 显著低于要求
        gpa_match = 0.4
    else:  # 远低于要求
        gpa_match = 0.2

    # 考虑用户学术潜力
    academic_potential = user_profile.academic_potential
    potential_bonus = 0.0

    if academic_potential == "高":
        potential_bonus = 0.2
    elif academic_potential == "中等偏上":
        potential_bonus = 0.1

    # 计算最终学术匹配分数
    academic_match = min(1.0, gpa_match + potential_bonus)

    return academic_match

async def _calculate_program_direction_match(
    program_info: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    user=None,
    request_id: str = None
) -> float:
    """
    计算专业方向与用户目标的匹配度 - 优化版
    将多个目标专业合并为一次API调用，降低成本和延迟
    
    Args:
        program_info: 专业信息
        user_profile: 用户画像
        
    Returns:
        专业方向匹配分数
    """
    # 获取目标专业方向列表
    target_major_directions = user_profile.target_major_directions

    # 如果没有目标专业，返回0分
    if not target_major_directions:
        return 0.0

    # 将多个意向专业合并为一个字符串，用于单次API调用
    combined_directions = ", ".join(target_major_directions)

    prompt = f"""
    请你作为留学选校专家，评估学生的目标专业方向与某个具体专业项目的匹配程度。

    学生信息:
    本科专业: {user_profile.undergraduate_major}
    目标专业方向(可能多个): {combined_directions}
    推断的兴趣领域: {user_profile.inferred_interest_domain or "未知"}
    
    专业项目信息:
    专业名称: {program_info.get('program_name_cn', '')}
    英文名称: {program_info.get('program_name_en', '')}
    学科分类: {program_info.get('program_direction', '')}
    专业描述: {program_info.get('program_objectives', '')}
    
    请评估专业项目与学生【任一】目标专业方向的最高匹配程度，给出一个0到1之间的分数，其中0表示完全不匹配，1表示完全匹配。
    只需返回一个数字，不要有其他任何文字。
    """
    
    try:
        # 执行单次API调用，使用积分和token双重tracking版本
        # 注意：智能选校服务的积分已在主流程中扣除，这里的LLM调用不会重复扣费
        from app.ai_selection.utils.llm import process_text_with_credit_and_token_tracking_async
        response = await process_text_with_credit_and_token_tracking_async(
            prompt,
            user=user,
            service_type="ai_selection",
            operation_type="program_direction_match",
            request_id=request_id,
            use_credits=False,
            use_tokens=False
        )

        if (
            "API调用失败" in response
            or "积分余额不足" in response
            or "API调用超时" in response
            or "网络连接失败" in response
        ):
            print(f"专业方向匹配评估API调用失败: {response}")
            # 如果API调用失败或积分不足，使用备选方法
            score = await _fallback_program_direction_match(program_info, user_profile)
            return score
        
        # 解析返回的分数
        score = float(response.strip())
        return max(0.0, min(1.0, score))

    except (ValueError, Exception) as e:
        print(f"无法解析LLM返回的匹配分数或API调用失败: {e}")
        # 如果API调用失败或返回值无法解析，使用备选方法
        score = await _fallback_program_direction_match(program_info, user_profile)
        return score

async def _fallback_program_direction_match(
    program_info: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    target_direction: Optional[str] = None # 此参数保留以兼容旧调用，但新逻辑不使用
) -> float:
    """
    备选的专业方向匹配度计算方法 - 基于关键词，本地计算
    当API调用失败时使用
    """
    # 新逻辑：总是遍历所有目标方向，取最高分
    target_major_directions = user_profile.target_major_directions
    if not target_major_directions:
        return 0.0
        
    scores = [
        _keyword_based_match(program_info, user_profile, direction)
        for direction in target_major_directions
    ]
    return max(scores) if scores else 0.0

def _keyword_based_match(
    program_info: Dict[str, Any],
    user_profile: EnhancedUserProfile,
    target_direction: str
) -> float:
    """关键词匹配的备选方法"""
    target_direction_lower = target_direction.lower()
    program_name = program_info.get("program_name_cn", "").lower()
    program_name_en = program_info.get("program_name_en", "").lower()
    program_discipline = program_info.get("program_direction", "").lower()
    program_description = program_info.get("program_objectives", "").lower()
    
    # 专业名称精确匹配
    if target_direction_lower in program_name or target_direction_lower in program_name_en:
        return 1.0
    
    # 学科分类匹配
    if target_direction_lower in program_discipline:
        return 0.9
    
    # 关键词匹配
    # 将目标方向分解为关键词
    keywords = target_direction_lower.split()
    matched_keywords = sum(1 for kw in keywords if kw in program_name 
                         or kw in program_name_en 
                         or kw in program_discipline 
                         or kw in program_description)
    
    if matched_keywords > 0:
        return min(0.8, 0.4 + matched_keywords * 0.2)  # 最高0.8
    
    # 学科相关性
    # 这里需要定义不同学科之间的相关性
    major_related_disciplines = {
        # 计算机与信息技术类专业的相关领域
        "计算机": ["计算机科学", "软件工程", "人工智能", "数据科学", "信息技术", "网络工程", "信息安全"],
        "软件": ["计算机科学", "软件工程", "信息技术", "系统工程", "数据科学"],
        "信息": ["计算机科学", "信息技术", "数据科学", "信息系统", "信息管理"],
        "人工智能": ["计算机科学", "数据科学", "机器学习", "认知科学", "统计学", "数学"],
        "数据科学": ["计算机科学", "统计学", "数学", "商业分析", "人工智能", "经济学"],
        "网络": ["网络工程", "信息安全", "计算机科学", "通信工程", "电子工程"],
        
        # 工程技术类专业的相关领域
        "电子": ["电子工程", "电气工程", "通信工程", "微电子", "计算机科学", "物理学"],
        "电气": ["电气工程", "电子工程", "自动化", "控制工程", "机械工程"],
        "通信": ["通信工程", "电子工程", "信息工程", "网络工程", "计算机科学"],
        "机械": ["机械工程", "自动化", "制造工程", "工业工程", "材料科学", "物理学"],
        "土木": ["土木工程", "建筑工程", "环境工程", "交通工程", "水利工程", "材料科学"],
        "材料": ["材料科学", "化学工程", "物理学", "化学", "机械工程", "电子工程"],
        "化工": ["化学工程", "化学", "材料科学", "环境工程", "生物工程", "石油工程"],
        "航空": ["航空工程", "航天工程", "机械工程", "材料科学", "物理学", "控制工程"],
        "生物工程": ["生物医学工程", "生物技术", "医学工程", "化学工程", "生物科学"],
        "环境": ["环境工程", "化学工程", "生态学", "地理科学", "公共卫生", "土木工程"],
        
        # 理学类专业的相关领域
        "数学": ["数学", "统计学", "数据科学", "金融数学", "计算机科学", "物理学"],
        "统计": ["统计学", "数学", "数据科学", "经济学", "心理学", "公共卫生"],
        "物理": ["物理学", "材料科学", "工程物理", "数学", "天文学", "地球科学"],
        "化学": ["化学", "材料科学", "化学工程", "生物化学", "药学", "环境科学"],
        "生物": ["生物科学", "生物技术", "生物医学", "医学", "环境科学", "农学"],
        "心理": ["心理学", "认知科学", "教育学", "社会学", "医学", "统计学"],
        "地理": ["地理科学", "环境科学", "测绘工程", "城市规划", "地质学", "生态学"],
        "地质": ["地质学", "地球科学", "环境科学", "地理科学", "材料科学", "物理学"],
        
        # 商科与经济类专业的相关领域
        "金融": ["金融", "经济学", "投资学", "风险管理", "数学", "统计学", "会计学"],
        "经济": ["经济学", "金融", "国际贸易", "统计学", "数学", "政治学", "社会学"],
        "管理": ["管理学", "工商管理", "人力资源", "心理学", "经济学", "社会学"],
        "会计": ["会计学", "金融", "经济学", "管理学", "数学", "统计学"],
        "市场": ["市场营销", "心理学", "统计学", "管理学", "传播学", "设计学"],
        "物流": ["物流管理", "供应链管理", "运筹学", "管理学", "工业工程", "经济学"],
        "商业": ["商业分析", "数据科学", "统计学", "管理学", "经济学", "计算机科学"],
        
        # 医学与健康类专业的相关领域
        "医学": ["临床医学", "基础医学", "生物医学", "生物科学", "化学", "心理学"],
        "护理": ["护理学", "医学", "心理学", "社会学", "管理学", "公共卫生"],
        "药学": ["药学", "化学", "生物科学", "医学", "化学工程", "生物工程"],
        "公共卫生": ["公共卫生", "医学", "统计学", "社会学", "环境科学", "管理学"],
        "康复": ["康复治疗", "医学", "心理学", "运动科学", "生物医学工程"],
        
        # 人文社科类专业的相关领域
        "教育": ["教育学", "心理学", "管理学", "社会学", "哲学", "数字媒体"],
        "法学": ["法学", "政治学", "社会学", "经济学", "哲学", "管理学"],
        "社会": ["社会学", "心理学", "政治学", "人类学", "教育学", "管理学"],
        "新闻": ["新闻学", "传播学", "社会学", "政治学", "文学", "数字媒体"],
        "语言": ["语言学", "文学", "教育学", "心理学", "传播学", "计算机科学"],
        "历史": ["历史学", "文学", "哲学", "考古学", "社会学", "政治学"],
        "哲学": ["哲学", "逻辑学", "心理学", "社会学", "政治学", "宗教学"],
        "政治": ["政治学", "国际关系", "法学", "经济学", "社会学", "历史学"],
        
        # 艺术设计类专业的相关领域
        "设计": ["设计学", "艺术学", "计算机科学", "心理学", "工程学", "市场营销"],
        "建筑": ["建筑学", "城市规划", "土木工程", "艺术学", "环境科学", "历史学"],
        "艺术": ["艺术学", "设计学", "文学", "历史学", "心理学", "教育学"],
        "传媒": ["传播学", "艺术学", "计算机科学", "心理学", "社会学", "市场营销"],
        
        # 农业与食品类专业的相关领域
        "农业": ["农学", "生物科学", "环境科学", "化学", "经济学", "管理学"],
        "食品": ["食品科学", "化学", "生物科学", "营养学", "化学工程", "管理学"],
        "林业": ["林学", "生态学", "环境科学", "生物科学", "地理科学", "管理学"],
        "水产": ["水产科学", "生物科学", "环境科学", "海洋科学", "食品科学"],
        
        # 其他交叉学科专业的相关领域
        "体育": ["体育学", "医学", "心理学", "教育学", "管理学", "康复治疗"],
        "旅游": ["旅游管理", "管理学", "经济学", "地理科学", "心理学", "市场营销"],
        "能源": ["能源工程", "物理学", "化学工程", "环境工程", "机械工程", "电气工程"],
        
        # 新兴交叉领域
        "金融科技": ["金融", "计算机科学", "数据科学", "数学", "统计学", "经济学"],
        "生物信息": ["生物科学", "计算机科学", "数学", "统计学", "医学", "化学"],
        "智能制造": ["机械工程", "计算机科学", "自动化", "工业工程", "材料科学"],
        "数字媒体": ["计算机科学", "艺术学", "传播学", "心理学", "市场营销", "设计学"],
        "可持续发展": ["环境科学", "经济学", "管理学", "工程学", "政治学", "社会学"]
    }
    
    # 提取用户兴趣领域
    user_domain = None
    for domain, disciplines in major_related_disciplines.items():
        if any(d.lower() in target_direction_lower for d in disciplines):
            user_domain = domain
            break
    
    # 提取专业所属领域
    program_domain = None
    for domain, disciplines in major_related_disciplines.items():
        if any(d.lower() in program_discipline for d in disciplines):
            program_domain = domain
            break
    
    # 如果两者属于同一领域
    if user_domain and program_domain and user_domain == program_domain:
        return 0.7
    
    # 默认返回一个较低的匹配度
    return 0.3

# 旧的同步版本已被移除，现在统一使用异步版本 _calculate_academic_performance_match_async