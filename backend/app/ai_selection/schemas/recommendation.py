from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

class RecommendationScore(BaseModel):
    """推荐评分模型"""
    school_tier_match: float = Field(..., description="院校层级匹配分")
    school_ranking_score: float = Field(..., description="院校排名评分")
    program_direction_match: float = Field(..., description="专业方向契合分")
    experience_match: float = Field(..., description="经历匹配分")
    academic_performance_match: float = Field(..., description="学术细节匹配分")
    total_match: float = Field(..., description="总匹配分")

class RecommendationItem(BaseModel):
    """单个推荐项目模型"""
    rank: int = Field(..., description="推荐排名")
    school_name: str
    school_tier: str
    region_name: str
    program_id: int
    program_name_cn: str
    program_name_en: str
    program_direction: str
    scores: RecommendationScore
    matching_cases_count: int = Field(0, description="匹配案例数量")

class RecommendationResponse(BaseModel):
    """推荐结果响应模型"""
    recommendations: List[RecommendationItem]
    user_profile_summary: Dict[str, Any] = Field(..., description="用户画像摘要")
    metadata: Dict[str, Any] = Field({}, description="元数据，如处理时间等") 