from fastapi import APIRouter

from app.ai_selection.api.endpoints import recommendation, data, validation

api_router = APIRouter()

# 包含推荐相关端点
api_router.include_router(
    recommendation.router,
    prefix="/recommendation",
    tags=["ai-recommendation"]
)

# 包含数据相关端点
api_router.include_router(
    data.router,
    prefix="/data",
    tags=["ai-data"]
)

# 包含验证相关端点
api_router.include_router(
    validation.router,
    prefix="/validation",
    tags=["ai-validation"]
)