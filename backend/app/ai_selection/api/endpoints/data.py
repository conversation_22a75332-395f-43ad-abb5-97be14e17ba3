from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Dict, Any, Optional
from sqlalchemy import select, and_, distinct, func, Integer, case, cast, or_
import json

from app.db.database import get_db
from app.core.cache import cache_ai_selection_data
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case,
    AISelectionHomeSchool as HomeSchool,
    AISelectionAbroadSchool as AbroadSchool
)
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession
from app.ai_selection.utils.language_parser import parse_language_score_safely
from app.ai_selection.utils.school_abbreviations import (
    get_search_terms_for_query, 
    expand_school_aliases,
    optimize_combo_search_terms,
    is_school_related_term,
    expand_school_abbreviation,
    is_likely_abbreviation,
    is_program_term
)

router = APIRouter()

@router.get("/regions")
@cache_ai_selection_data(ttl=1800)  # 缓存30分钟，地区数据较稳定
async def get_regions() -> List[Dict[str, Any]]:
    """
    获取所有地区列表（按指定顺序返回）
    
    Returns:
        地区列表
    """
    # 指定的显示顺序
    display_order = [
        "中国香港",
        "新加坡", 
        "英国",
        "美国",
        "澳大利亚",
        "中国澳门",
        "马来西亚"
    ]
    
    async for session in get_db():
        query = select(distinct(Program.school_region)).where(Program.school_region.isnot(None))
        result = await session.execute(query)
        available_regions = result.scalars().all()
        
        # 过滤掉 'NaN' 值，只保留有效地区
        valid_regions = [region for region in available_regions if region and region != 'NaN']
        
        # 按指定顺序排列
        ordered_regions = []
        for display_region in display_order:
            if display_region in valid_regions:
                ordered_regions.append(display_region)
        
        # 添加不在预定义列表中但存在的其他地区
        for region in sorted(valid_regions):
            if region not in display_order:
                ordered_regions.append(region)
        
        return [{"name": region} for region in ordered_regions]

@router.get("/categories")
@cache_ai_selection_data(ttl=1800)  # 缓存30分钟，专业类别数据较稳定
async def get_program_categories() -> List[Dict[str, Any]]:
    """
    获取所有专业大类列表
    
    Returns:
        专业大类列表
    """
    async for session in get_db():
        query = select(distinct(Program.program_category)).where(Program.program_category.isnot(None))
        result = await session.execute(query)
        categories = result.scalars().all()
        
        return [{"name": category} for category in sorted(categories) if category]

@router.get("/directions")
@cache_ai_selection_data(ttl=1800)  # 缓存30分钟，专业方向数据较稳定
async def get_program_directions() -> List[Dict[str, Any]]:
    """
    获取所有专业方向列表
    
    Returns:
        专业方向列表
    """
    async for session in get_db():
        query = select(distinct(Program.program_direction)).where(Program.program_direction.isnot(None))
        result = await session.execute(query)
        directions = result.scalars().all()
        
        return [{"name": direction} for direction in sorted(directions) if direction]

@router.get("/schools")
async def get_schools(
    region: str = Query(None, description="按地区筛选"),
    qs_rank_range: str = Query(None, description="按QS排名范围筛选，格式：1-50")
) -> List[Dict[str, Any]]:
    """
    获取学校列表，支持按地区和QS排名筛选（从programs表中提取）
    
    Args:
        region: 地区名称（可选）
        qs_rank_range: QS排名范围（可选）
        
    Returns:
        学校列表
    """
    async for session in get_db():
        query = select(
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region
        ).where(Program.school_name_cn.isnot(None)).distinct()
        
        conditions = []
        if region is not None:
            conditions.append(Program.school_region == region)
        
        if qs_rank_range is not None:
            try:
                start_rank, end_rank = map(int, qs_rank_range.split('-'))
                # 注意：school_qs_rank是字符串类型，需要转换
                conditions.append(
                    and_(
                        Program.school_qs_rank.isnot(None),
                        Program.school_qs_rank.cast(Integer) >= start_rank,
                        Program.school_qs_rank.cast(Integer) <= end_rank
                    )
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="QS排名范围格式错误，应为：1-50")
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        # 去重并排序
        schools = {}
        for row in rows:
            key = row.school_name_cn
            if key not in schools:
                schools[key] = {
                    "school_name_cn": row.school_name_cn,
                    "school_name_en": row.school_name_en,
                    "school_qs_rank": row.school_qs_rank,
                    "school_region": row.school_region
                }
        
        return list(schools.values())

@router.get("/home-schools")
async def get_home_schools(
    location: str = Query(None, description="按所在地筛选"),
    school_type: str = Query(None, description="按学校类型筛选"),
    search: str = Query(None, description="按学校名称搜索"),
    limit: int = Query(2000, description="返回数量限制")
) -> List[Dict[str, Any]]:
    """
    获取境内院校列表，支持按所在地和学校类型筛选，按软科排名排序
    
    Args:
        location: 所在地（可选）
        school_type: 学校类型（可选）
        search: 学校名称搜索关键词（可选）
        limit: 返回数量限制（默认2000）
        
    Returns:
        境内院校列表，按软科排名排序
    """
    async for session in get_db():
        query = select(
            HomeSchool.id,
            HomeSchool.school_name,
            HomeSchool.school_type,
            HomeSchool.school_code,
            HomeSchool.location,
            HomeSchool.authority,
            HomeSchool.ranking_ruanke,
            HomeSchool.remarks
        )
        
        conditions = []
        if location is not None:
            conditions.append(HomeSchool.location.ilike(f"%{location}%"))
        if school_type is not None:
            conditions.append(HomeSchool.school_type == school_type)
        if search is not None:
            conditions.append(HomeSchool.school_name.ilike(f"%{search}%"))
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 按软科排名排序：有排名的在前，按排名从小到大，无排名的在后按学校名称排序
        query = query.order_by(
            HomeSchool.ranking_ruanke.is_(None).asc(),  # NULL值排在后面
            HomeSchool.ranking_ruanke.asc(),            # 排名从小到大
            HomeSchool.school_name.asc()                # 同排名或无排名按学校名称排序
        )
        
        # 添加数量限制
        query = query.limit(limit)
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        return [
            {
                "id": row.id,
                "school_name": row.school_name,
                "school_type": row.school_type,
                "school_code": row.school_code,
                "location": row.location,
                "authority": row.authority,
                "ranking_ruanke": row.ranking_ruanke,
                "remarks": row.remarks
            }
            for row in rows
        ]

@router.get("/programs")
async def get_programs(
    # 智能搜索参数 - 支持学校缩写、中文简写和精确组合搜索
    search: str = Query(None, description="智能关键词搜索，支持学校缩写（如HKU、NUS、MIT等）、中文简写（如港大、曼大、帝国理工等）、精确学校名+专业组合搜索，覆盖300+国际知名大学"),
    
    # 原有的具体字段搜索参数保留
    school_name: str = Query(None, description="按学校中文名称筛选"),
    school_name_en: str = Query(None, description="按学校英文名称筛选"),
    program_name: str = Query(None, description="按专业中文名称筛选"),
    program_name_en: str = Query(None, description="按专业英文名称筛选"),
    region: str = Query(None, description="按地区筛选"),
    program_direction: str = Query(None, description="按专业方向筛选"),
    degree: str = Query(None, description="按学位类型筛选"),
    program_category: str = Query(None, description="按专业大类筛选"),
    faculty: str = Query(None, description="按所在学院筛选"),
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="跳过条数（用于分页）")
) -> List[Dict[str, Any]]:
    """
    获取专业项目列表，支持智能关键词搜索（包括学校缩写和组合搜索）和多种筛选条件
    
    Args:
        search: 智能关键词搜索，支持学校缩写（如HKU、NUS、MIT等）和"大学名+专业"组合搜索
        school_name: 学校中文名称（可选）
        school_name_en: 学校英文名称（可选）
        program_name: 专业中文名称（可选）
        program_name_en: 专业英文名称（可选）
        region: 地区（可选）
        program_direction: 专业方向（可选）
        degree: 学位类型（可选）
        program_category: 专业大类（可选）
        faculty: 所在学院（可选）
        limit: 返回数量限制
        offset: 跳过条数（用于分页）
        
    Returns:
        专业项目列表
    """
    async for session in get_db():
        query = select(
            Program.id,
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region,
            Program.program_name_cn,
            Program.program_name_en,
            Program.program_category,
            Program.program_direction,
            Program.faculty,
            Program.degree,
            Program.program_duration,
            Program.program_tuition,
            Program.gpa_requirements,
            Program.language_requirements,
            Program.application_time,
            Program.program_website
        )
        
        conditions = []
        
        # 智能关键词搜索 - 使用优化的组合搜索
        if search is not None:
            search_term = search.strip()
            if search_term:
                # 使用优化的组合搜索算法
                expanded_search_terms = optimize_combo_search_terms(search_term)
                
                # 如果组合搜索没有产生足够的结果，回退到原有方法
                if not expanded_search_terms:
                    expanded_search_terms = get_search_terms_for_query(search_term)
                
                # 为原始搜索词也添加别名展开
                aliased_terms = expand_school_aliases(search_term)
                for aliased_term in aliased_terms:
                    if aliased_term not in expanded_search_terms:
                        expanded_search_terms.append(aliased_term)
                
                # 判断是否为组合搜索（包含空格分隔的多个词）
                original_terms = search_term.split()
                is_combo_search = len(original_terms) > 1
                
                if is_combo_search:
                    # 组合搜索：识别学校词汇和专业词汇，使用AND逻辑
                    school_terms = []
                    program_terms = []
                    
                    # 分析原始搜索词，识别学校相关和专业相关
                    for term in original_terms:
                        # 获取该词的展开结果
                        # 优先识别专业术语，避免专业术语被误判为学校
                        if (is_school_related_term(term) or is_likely_abbreviation(term)) and not is_program_term(term):
                            # 学校相关词汇，获取其展开
                            expanded = expand_school_abbreviation(term)
                            aliased = expand_school_aliases(term)
                            school_terms.extend(expanded + aliased)
                        else:
                            # 专业相关词汇
                            program_terms.append(term)
                            # 也检查别名展开
                            aliased = expand_school_aliases(term)
                            program_terms.extend([t for t in aliased if t != term])
                    
                    # 去重
                    school_terms = list(set(school_terms))
                    program_terms = list(set(program_terms))
                    
                    search_conditions = []
                    
                    # 如果识别到学校词汇，添加学校条件（OR逻辑）
                    if school_terms:
                        school_or_conditions = []
                        for term in school_terms:
                            school_or_conditions.extend([
                                Program.school_name_cn.ilike(f"%{term}%"),
                                Program.school_name_en.ilike(f"%{term}%"),
                                Program.school_region.ilike(f"%{term}%")
                            ])
                        if school_or_conditions:
                            search_conditions.append(or_(*school_or_conditions))
                    
                    # 如果识别到专业词汇，添加专业条件（OR逻辑）
                    if program_terms:
                        program_or_conditions = []
                        for term in program_terms:
                            program_or_conditions.extend([
                                Program.program_name_cn.ilike(f"%{term}%"),
                                Program.program_name_en.ilike(f"%{term}%"),
                                Program.program_direction.ilike(f"%{term}%"),
                                Program.faculty.ilike(f"%{term}%"),
                                Program.program_category.ilike(f"%{term}%")
                            ])
                        if program_or_conditions:
                            search_conditions.append(or_(*program_or_conditions))
                    
                    # 使用AND逻辑组合学校和专业条件
                    if search_conditions:
                        combined_search_condition = and_(*search_conditions)
                        conditions.append(combined_search_condition)
                
                else:
                    # 单词搜索：使用原有的OR逻辑
                    search_or_conditions = []
                    
                    for term in expanded_search_terms:
                        term_conditions = or_(
                            Program.school_name_cn.ilike(f"%{term}%"),
                            Program.school_name_en.ilike(f"%{term}%"),
                            Program.program_name_cn.ilike(f"%{term}%"),
                            Program.program_name_en.ilike(f"%{term}%"),
                            Program.program_direction.ilike(f"%{term}%"),
                            Program.faculty.ilike(f"%{term}%"),
                            Program.program_category.ilike(f"%{term}%"),
                            Program.school_region.ilike(f"%{term}%")
                        )
                        search_or_conditions.append(term_conditions)
                    
                    # 将所有搜索词条件用OR连接
                    if search_or_conditions:
                        combined_search_condition = or_(*search_or_conditions)
                        conditions.append(combined_search_condition)
        
        # 原有的具体字段筛选条件
        if school_name is not None:
            conditions.append(Program.school_name_cn.ilike(f"%{school_name}%"))
        if school_name_en is not None:
            conditions.append(Program.school_name_en.ilike(f"%{school_name_en}%"))
        if program_name is not None:
            conditions.append(Program.program_name_cn.ilike(f"%{program_name}%"))
        if program_name_en is not None:
            conditions.append(Program.program_name_en.ilike(f"%{program_name_en}%"))
        if region is not None:
            conditions.append(Program.school_region == region)
        if program_direction is not None:
            conditions.append(Program.program_direction.ilike(f"%{program_direction}%"))
        if degree is not None:
            conditions.append(Program.degree == degree)
        if program_category is not None:
            conditions.append(Program.program_category == program_category)
        if faculty is not None:
            conditions.append(Program.faculty.ilike(f"%{faculty}%"))
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 改进排序逻辑：优化组合搜索的相关性评分
        if search is not None and search.strip():
            search_term = search.strip()
            
            # 获取展开的搜索词，用于相关性评分
            expanded_terms = optimize_combo_search_terms(search_term)
            if not expanded_terms:
                expanded_terms = get_search_terms_for_query(search_term)
            
            aliased_terms = expand_school_aliases(search_term)
            all_terms = list(set(expanded_terms + aliased_terms))
            
            # 构建增强的相关性评分条件
            relevance_conditions = []
            
            # 识别学校相关词汇和专业相关词汇
            school_terms = []
            program_terms = []
            
            for term in all_terms:
                if is_school_related_term(term):
                    school_terms.append(term)
                else:
                    program_terms.append(term)
            
            # 为学校相关词汇构建评分条件（更高权重）
            for i, term in enumerate(school_terms):
                base_score = 20 - i  # 学校词汇基础分更高
                relevance_conditions.extend([
                    # 学校精确匹配得分最高
                    (Program.school_name_cn.ilike(f"{term}"), base_score + 15),
                    (Program.school_name_en.ilike(f"{term}"), base_score + 14),
                    # 学校包含匹配
                    (Program.school_name_cn.ilike(f"%{term}%"), base_score + 12),
                    (Program.school_name_en.ilike(f"%{term}%"), base_score + 11),
                    # 地区匹配
                    (Program.school_region.ilike(f"%{term}%"), base_score + 8),
                ])
            
            # 为专业相关词汇构建评分条件
            for i, term in enumerate(program_terms):
                base_score = 15 - i  # 专业词汇基础分
                relevance_conditions.extend([
                    # 专业精确匹配得分高
                    (Program.program_name_cn.ilike(f"{term}"), base_score + 10),
                    (Program.program_name_en.ilike(f"{term}"), base_score + 9),
                    # 专业方向和大类匹配
                    (Program.program_direction.ilike(f"%{term}%"), base_score + 8),
                    (Program.program_category.ilike(f"%{term}%"), base_score + 7),
                    # 专业包含匹配
                    (Program.program_name_cn.ilike(f"%{term}%"), base_score + 6),
                    (Program.program_name_en.ilike(f"%{term}%"), base_score + 5),
                    # 学院匹配
                    (Program.faculty.ilike(f"%{term}%"), base_score + 4),
                ])
            
            # 对于无法分类的词汇，使用通用评分
            other_terms = [term for term in all_terms if term not in school_terms and term not in program_terms]
            for i, term in enumerate(other_terms):
                base_score = 10 - i
                relevance_conditions.extend([
                    (Program.school_name_cn.ilike(f"%{term}%"), base_score + 5),
                    (Program.program_name_cn.ilike(f"%{term}%"), base_score + 4),
                    (Program.program_direction.ilike(f"%{term}%"), base_score + 3),
                    (Program.faculty.ilike(f"%{term}%"), base_score + 2),
                    (Program.program_category.ilike(f"%{term}%"), base_score + 1),
                ])
            
            # 构建CASE表达式
            if relevance_conditions:
                relevance_score = case(
                    *relevance_conditions,
                    else_=1
                )
                
                query = query.order_by(
                    relevance_score.desc(),  # 按相关性评分降序
                    Program.school_qs_rank.is_(None).asc(),  # 有QS排名的优先
                    func.length(Program.school_qs_rank).asc(),  # QS排名数值排序
                    Program.school_qs_rank.asc(),
                    Program.school_name_cn.asc()  # 最后按学校名称排序
                )
            else:
                # 回退到原有排序
                query = query.order_by(
                    Program.school_qs_rank.is_(None).asc(),
                    func.length(Program.school_qs_rank).asc(),
                    Program.school_qs_rank.asc(),
                    Program.school_name_cn.asc()
                )
        else:
            # 没有搜索关键词时使用原有排序逻辑
            query = query.order_by(
                Program.school_qs_rank.is_(None).asc(),
                func.length(Program.school_qs_rank).asc(),
                Program.school_qs_rank.asc(),
                Program.school_name_cn.asc()
            )
        
        # 添加分页支持
        query = query.offset(offset).limit(limit)
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        return [
            {
                "id": row.id,
                "school_name_cn": row.school_name_cn,
                "school_name_en": row.school_name_en,
                "school_qs_rank": row.school_qs_rank,
                "school_region": row.school_region,
                "program_name_cn": row.program_name_cn,
                "program_name_en": row.program_name_en,
                "program_category": row.program_category,
                "program_direction": row.program_direction,
                "faculty": row.faculty,
                "degree": row.degree,
                "program_duration": row.program_duration,
                "program_tuition": row.program_tuition,
                "gpa_requirements": row.gpa_requirements,
                "language_requirements": row.language_requirements,
                "application_time": row.application_time,
                "program_website": row.program_website
            }
            for row in rows
        ]

@router.get("/programs/count")
async def get_programs_count(
    # 智能搜索参数 - 支持学校缩写、中文简写和精确组合搜索
    search: str = Query(None, description="智能关键词搜索，支持学校缩写（如HKU、NUS、MIT等）、中文简写（如港大、曼大、帝国理工等）、精确学校名+专业组合搜索，覆盖300+国际知名大学"),
    
    # 原有参数保留
    school_name: str = Query(None, description="按学校中文名称筛选"),
    school_name_en: str = Query(None, description="按学校英文名称筛选"),
    program_name: str = Query(None, description="按专业中文名称筛选"),
    program_name_en: str = Query(None, description="按专业英文名称筛选"),
    region: str = Query(None, description="按地区筛选"),
    program_direction: str = Query(None, description="按专业方向筛选"),
    degree: str = Query(None, description="按学位类型筛选"),
    program_category: str = Query(None, description="按专业大类筛选"),
    faculty: str = Query(None, description="按所在学院筛选")
) -> Dict[str, int]:
    """
    获取符合筛选条件的专业项目总数，支持智能关键词搜索（包括学校缩写和组合搜索）
    
    Args:
        search: 智能关键词搜索，支持学校缩写和"大学名+专业"组合搜索
        school_name: 学校中文名称（可选）
        school_name_en: 学校英文名称（可选）
        program_name: 专业中文名称（可选）
        program_name_en: 专业英文名称（可选）
        region: 地区（可选）
        program_direction: 专业方向（可选）
        degree: 学位类型（可选）
        program_category: 专业大类（可选）
        faculty: 所在学院（可选）
        
    Returns:
        总数信息
    """
    async for session in get_db():
        query = select(func.count(Program.id))
        
        conditions = []
        
        # 智能关键词搜索逻辑 - 使用优化的组合搜索
        if search is not None:
            search_term = search.strip()
            if search_term:
                # 使用优化的组合搜索算法
                expanded_search_terms = optimize_combo_search_terms(search_term)
                
                # 如果组合搜索没有产生结果，回退到原有方法
                if not expanded_search_terms:
                    expanded_search_terms = get_search_terms_for_query(search_term)
                
                aliased_terms = expand_school_aliases(search_term)
                for aliased_term in aliased_terms:
                    if aliased_term not in expanded_search_terms:
                        expanded_search_terms.append(aliased_term)
                
                # 判断是否为组合搜索（包含空格分隔的多个词）
                original_terms = search_term.split()
                is_combo_search = len(original_terms) > 1
                
                if is_combo_search:
                    # 组合搜索：识别学校词汇和专业词汇，使用AND逻辑
                    school_terms = []
                    program_terms = []
                    
                    # 分析原始搜索词，识别学校相关和专业相关
                    for term in original_terms:
                        # 获取该词的展开结果
                        # 优先识别专业术语，避免专业术语被误判为学校
                        if (is_school_related_term(term) or is_likely_abbreviation(term)) and not is_program_term(term):
                            # 学校相关词汇，获取其展开
                            expanded = expand_school_abbreviation(term)
                            aliased = expand_school_aliases(term)
                            school_terms.extend(expanded + aliased)
                        else:
                            # 专业相关词汇
                            program_terms.append(term)
                            # 也检查别名展开
                            aliased = expand_school_aliases(term)
                            program_terms.extend([t for t in aliased if t != term])
                    
                    # 去重
                    school_terms = list(set(school_terms))
                    program_terms = list(set(program_terms))
                    
                    search_conditions = []
                    
                    # 如果识别到学校词汇，添加学校条件（OR逻辑）
                    if school_terms:
                        school_or_conditions = []
                        for term in school_terms:
                            school_or_conditions.extend([
                                Program.school_name_cn.ilike(f"%{term}%"),
                                Program.school_name_en.ilike(f"%{term}%"),
                                Program.school_region.ilike(f"%{term}%")
                            ])
                        if school_or_conditions:
                            search_conditions.append(or_(*school_or_conditions))
                    
                    # 如果识别到专业词汇，添加专业条件（OR逻辑）
                    if program_terms:
                        program_or_conditions = []
                        for term in program_terms:
                            program_or_conditions.extend([
                                Program.program_name_cn.ilike(f"%{term}%"),
                                Program.program_name_en.ilike(f"%{term}%"),
                                Program.program_direction.ilike(f"%{term}%"),
                                Program.faculty.ilike(f"%{term}%"),
                                Program.program_category.ilike(f"%{term}%")
                            ])
                        if program_or_conditions:
                            search_conditions.append(or_(*program_or_conditions))
                    
                    # 使用AND逻辑组合学校和专业条件
                    if search_conditions:
                        combined_search_condition = and_(*search_conditions)
                        conditions.append(combined_search_condition)
                
                else:
                    # 单词搜索：使用原有的OR逻辑
                    search_or_conditions = []
                    
                    for term in expanded_search_terms:
                        term_conditions = or_(
                            Program.school_name_cn.ilike(f"%{term}%"),
                            Program.school_name_en.ilike(f"%{term}%"),
                            Program.program_name_cn.ilike(f"%{term}%"),
                            Program.program_name_en.ilike(f"%{term}%"),
                            Program.program_direction.ilike(f"%{term}%"),
                            Program.faculty.ilike(f"%{term}%"),
                            Program.program_category.ilike(f"%{term}%"),
                            Program.school_region.ilike(f"%{term}%")
                        )
                        search_or_conditions.append(term_conditions)
                    
                    # 将所有搜索词条件用OR连接
                    if search_or_conditions:
                        combined_search_condition = or_(*search_or_conditions)
                        conditions.append(combined_search_condition)
        
        # 原有筛选条件
        if school_name is not None:
            conditions.append(Program.school_name_cn.ilike(f"%{school_name}%"))
        if school_name_en is not None:
            conditions.append(Program.school_name_en.ilike(f"%{school_name_en}%"))
        if program_name is not None:
            conditions.append(Program.program_name_cn.ilike(f"%{program_name}%"))
        if program_name_en is not None:
            conditions.append(Program.program_name_en.ilike(f"%{program_name_en}%"))
        if region is not None:
            conditions.append(Program.school_region == region)
        if program_direction is not None:
            conditions.append(Program.program_direction.ilike(f"%{program_direction}%"))
        if degree is not None:
            conditions.append(Program.degree == degree)
        if program_category is not None:
            conditions.append(Program.program_category == program_category)
        if faculty is not None:
            conditions.append(Program.faculty.ilike(f"%{faculty}%"))
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await session.execute(query)
        count = result.scalar()
        
        return {"total": count}

@router.get("/programs/{program_id}")
async def get_program_detail(program_id: int) -> Dict[str, Any]:
    """
    获取专业项目详细信息
    
    Args:
        program_id: 专业项目ID
        
    Returns:
        专业项目详细信息
    """
    async for session in get_db():
        query = select(Program).where(Program.id == program_id)
        result = await session.execute(query)
        program = result.scalar_one_or_none()
        
        if not program:
            raise HTTPException(status_code=404, detail="专业项目不存在")
        
        return {
            "id": program.id,
            "school_name_cn": program.school_name_cn,
            "school_name_en": program.school_name_en,
            "school_qs_rank": program.school_qs_rank,
            "school_region": program.school_region,
            "program_code": program.program_code,
            "degree": program.degree,
            "program_name_cn": program.program_name_cn,
            "program_name_en": program.program_name_en,
            "program_category": program.program_category,
            "program_direction": program.program_direction,
            "faculty": program.faculty,
            "enrollment_time": program.enrollment_time,
            "program_duration": program.program_duration,
            "program_tuition": program.program_tuition,
            "application_time": program.application_time,
            "application_requirements": program.application_requirements,
            "gpa_requirements": program.gpa_requirements,
            "language_requirements": program.language_requirements,
            "program_objectives": program.program_objectives,
            "courses": program.courses,
            "program_website": program.program_website,
            "other_cost": program.other_cost,
            "degree_evaluation": program.degree_evaluation
        }

@router.get("/cases")
async def get_cases(
    limit: int = Query(10, description="返回案例数量"),
    program_id: int = Query(None, description="按专业ID筛选"),
    offer_region: str = Query(None, description="按offer地区筛选"),
    undergraduate_tier: str = Query(None, description="按本科院校层级筛选"),
    undergraduate_major: str = Query(None, description="按本科专业筛选")
) -> List[Dict[str, Any]]:
    """
    获取申请案例列表，支持各种筛选条件
    
    Args:
        limit: 返回数量
        program_id: 专业ID（可选）
        offer_region: offer地区（可选）
        undergraduate_tier: 本科院校层级（可选）
        undergraduate_major: 本科专业（可选）
        
    Returns:
        案例列表
    """
    async for session in get_db():
        query = (
            select(
                Case.id,
                Case.student_name,
                Case.undergraduate_school,
                Case.undergraduate_school_tier,
                Case.undergraduate_major,
                Case.gpa,
                Case.offer_region,
                Case.offer_degree,
                Case.offer_major_direction,
                Case.key_experiences,
                Case.language_score,
                Case.offer_program_id,
                Program.program_name_cn,
                Program.school_name_cn
            )
            .join(Program, Case.offer_program_id == Program.id)
        )
        
        conditions = []
        if program_id is not None:
            conditions.append(Case.offer_program_id == program_id)
        if offer_region is not None:
            conditions.append(Case.offer_region == offer_region)
        if undergraduate_tier is not None:
            conditions.append(Case.undergraduate_school_tier == undergraduate_tier)
        if undergraduate_major is not None:
            conditions.append(Case.undergraduate_major.ilike(f"%{undergraduate_major}%"))
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.limit(limit)
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        cases = []
        for row in rows:
            # 处理language_score的JSON数据 - 使用安全解析函数
            language_score = parse_language_score_safely(row.language_score)
            
            cases.append({
                "id": row.id,
                "student_name": row.student_name,
                "undergraduate_school": row.undergraduate_school,
                "undergraduate_school_tier": row.undergraduate_school_tier,
                "undergraduate_major": row.undergraduate_major,
                "gpa": row.gpa,
                "offer_region": row.offer_region,
                "offer_degree": row.offer_degree,
                "offer_major_direction": row.offer_major_direction,
                "key_experiences": row.key_experiences,
                "language_score": language_score,
                "offer_program_id": row.offer_program_id,
                "offer_program_name": row.program_name_cn,
                "offer_school_name": row.school_name_cn
            })
        
        return cases

@router.get("/statistics")
@cache_ai_selection_data(ttl=900)  # 缓存15分钟，统计数据更新不频繁
async def get_statistics() -> Dict[str, Any]:
    """
    获取系统统计信息
    
    Returns:
        统计信息
    """
    async for session in get_db():
        # 获取专业项目数量
        program_query = select(func.count(Program.id))
        program_result = await session.execute(program_query)
        program_count = program_result.scalar()
        
        # 暂时跳过案例数量查询（案例表不存在）
        case_count = 0
        
        # 获取学校数量（去重）
        school_query = select(func.count(distinct(Program.school_name_cn)))
        school_result = await session.execute(school_query)
        school_count = school_result.scalar()
        
        # 获取地区数量（去重）
        region_query = select(func.count(distinct(Program.school_region))).where(Program.school_region.isnot(None))
        region_result = await session.execute(region_query)
        region_count = region_result.scalar()
        
        return {
            "total_programs": program_count,
            "total_cases": case_count,
            "total_schools": school_count,
            "total_regions": region_count
        }

@router.get("/abroad-schools")
@cache_ai_selection_data(ttl=1800)
async def get_abroad_schools(
    school_name: str = Query(None, description="按学校中文名称搜索"),
    qs_rank_range: str = Query(None, description="按QS排名范围筛选，格式：1-50"),
    limit: int = Query(100, description="返回数量限制"),
    session: AsyncSession = Depends(get_db)
) -> List[Dict[str, Any]]:
    """
    获取境外院校列表，支持按学校名称搜索和QS排名筛选
    
    Args:
        school_name: 学校中文名称搜索关键词（可选）
        qs_rank_range: QS排名范围（可选）
        limit: 返回数量限制（默认100）
        
    Returns:
        境外院校列表，按QS排名排序
    """
    try:
        query = select(
            AbroadSchool.id,
            AbroadSchool.school_name_cn,
            AbroadSchool.school_name_en,
            AbroadSchool.school_qs_rank,
            AbroadSchool.school_logo_url
        )
        
        conditions = []
        if school_name is not None:
            conditions.append(AbroadSchool.school_name_cn.ilike(f"%{school_name}%"))
        
        if qs_rank_range is not None:
            try:
                start_rank, end_rank = map(int, qs_rank_range.split('-'))
                conditions.append(
                    and_(
                        AbroadSchool.school_qs_rank.isnot(None),
                        AbroadSchool.school_qs_rank >= start_rank,
                        AbroadSchool.school_qs_rank <= end_rank
                    )
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="QS排名范围格式错误，应为：1-50")
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 按QS排名排序（较小的排名在前）
        query = query.order_by(AbroadSchool.school_qs_rank.asc().nulls_last()).limit(limit)

        result = await session.execute(query)
        rows = result.fetchall()

        return [
            {
                "id": row.id,
                "school_name_cn": row.school_name_cn,
                "school_name_en": row.school_name_en,
                "school_qs_rank": row.school_qs_rank,
                "school_logo_url": row.school_logo_url
            }
            for row in rows
        ]
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"获取境外院校列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="获取院校数据失败，请稍后重试"
        )

class SchoolLogosRequest(BaseModel):
    school_names: List[str] = Field(..., min_length=1, max_length=200, description="学校中文名列表，最多200个")

class SchoolLogoItem(BaseModel):
    name: str
    logo_url: Optional[str] = None

@router.post("/abroad-schools/logos", response_model=List[SchoolLogoItem])
@cache_ai_selection_data(ttl=3600)
async def get_abroad_school_logos_batch(payload: SchoolLogosRequest, session: AsyncSession = Depends(get_db)) -> List[Dict[str, Optional[str]]]:
    """
    批量获取学校Logo（按学校中文名精确匹配）。
    - 最多支持200个名称
    - 返回列表与请求顺序一致，未命中返回 logo_url=None
    """
    # 预处理与去重，但要保留原始顺序用于输出
    original_names: List[str] = [n.strip() for n in payload.school_names if isinstance(n, str) and n.strip()]
    if not original_names:
        return []
    # 限流保护
    names_limited = original_names[:200]
    unique_names = list(dict.fromkeys(names_limited))

    query = select(
        AbroadSchool.school_name_cn,
        AbroadSchool.school_logo_url
    ).where(AbroadSchool.school_name_cn.in_(unique_names))

    result = await session.execute(query)
    rows = result.fetchall()

    found_map: Dict[str, Optional[str]] = {row.school_name_cn: row.school_logo_url for row in rows}

    # 构造与输入顺序一致的结果
    return [
        {"name": name, "logo_url": found_map.get(name)}
        for name in names_limited
    ]