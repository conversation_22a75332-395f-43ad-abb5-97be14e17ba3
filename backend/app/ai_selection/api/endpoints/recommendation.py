from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, AsyncGenerator, Optional
import json
import asyncio
import time

from app.ai_selection.schemas.student_profile import StudentProfile
from app.ai_selection.core.user_profile import build_user_profile
from app.ai_selection.core.candidate_pool import generate_candidate_pool, generate_hard_filtered_results
from app.ai_selection.core.school_matching import identify_reachable_schools, calculate_school_match_scores, determine_dynamic_qs_range, calculate_school_ranking_scores
from app.ai_selection.core.streaming_ranking import stream_recommendations, stream_hard_filtered_results
from app.core.dependencies import get_current_user
from app.models.user import User
from app.db.database import get_db
from app.credit_payment.services.package_service import PackageService
from app.credit_payment.services.credit_service import CreditService
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()

@router.post("/recommend/stream")
async def get_school_recommendations_stream(
    student_profile: StudentProfile,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    流式推荐接口 - 实现套餐+积分双重验证机制

    **双重验证机制：**
    1. 套餐验证（门票机制）：用户必须先购买任意套餐才能访问智能选校功能
    2. 积分消耗（使用货币）：每次使用智能选校功能固定消耗5积分

    Args:
        student_profile: 结构化的学生画像信息
        current_user: 当前登录用户（必须认证）
        db: 数据库会话

    Returns:
        Server-Sent Events 流式响应

    Raises:
        HTTPException: 验证失败时返回相应错误
    """

    # 🎫 第一层验证：套餐验证（门票机制）- 所有模式的前置条件
    try:
        package_status = await PackageService.check_user_package_status(db, current_user)

        if not package_status["has_package"]:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail={
                    "error": "no_package",
                    "message": "请先购买套餐后使用智能选校功能",
                    "package_status": package_status,
                    "required_action": "purchase_package"
                }
            )

        print(f"✅ 套餐验证通过 - 用户ID: {current_user.id}, 套餐: {package_status['package_name']}")

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 套餐验证异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "package_validation_error", "message": "套餐验证过程中发生错误，请稍后重试"}
        )

    async def generate_recommendation_stream() -> AsyncGenerator[str, None]:
        """生成流式推荐数据"""
        try:
            # 获取用户ID（用户必须已登录）
            user_id = current_user.id

            # 生成请求ID用于关联整个推荐流程的所有LLM调用
            from app.services.token_billing import TokenBillingService
            request_id = TokenBillingService.generate_request_id()
            print(f"🔗 院校推荐流程开始，用户ID: {user_id}, request_id: {request_id}")

            # 发送开始事件
            yield f"data: {json.dumps({'type': 'start', 'message': '开始分析您的背景...'})}\n\n"

            # 阶段1: 用户画像构建
            start_time = time.time()
            enhanced_profile = await build_user_profile(student_profile, current_user, request_id)

            yield f"data: {json.dumps({'type': 'progress', 'stage': 'profile', 'message': '用户画像构建完成', 'elapsed': round(time.time() - start_time, 2)})}\n\n"
            
            # 检查是否启用AI智能匹配
            enable_ai_selection = enhanced_profile.enable_ai_selection
            
            if not enable_ai_selection:
                # 硬筛选模式：只进行数据库筛选
                yield f"data: {json.dumps({'type': 'mode_selection', 'mode': 'hard_filter', 'message': '精准匹配：根据您的留学意向进行数据库筛选...'})}\n\n"
                
                # 阶段2: 生成硬筛选结果
                stage_start = time.time()
                hard_filtered_results = await generate_hard_filtered_results(enhanced_profile)
                
                if not hard_filtered_results:
                    yield f"data: {json.dumps({'type': 'error', 'message': '未找到符合条件的专业，请尝试调整专业方向或目标地区'})}\n\n"
                    return
                
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'hard_filter_complete', 'message': f'硬筛选完成，找到 {len(hard_filtered_results)} 个匹配专业', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"
                
                # 阶段3: 流式返回硬筛选结果
                async for stream_data in stream_hard_filtered_results(hard_filtered_results, enhanced_profile):
                    yield f"data: {json.dumps(stream_data)}\n\n"
                
            else:
                # AI智能匹配模式：完整的AI推荐流程
                yield f"data: {json.dumps({'type': 'mode_selection', 'mode': 'ai_selection', 'message': 'AI智能匹配模式：开始深度分析和个性化推荐...'})}\n\n"

                # 💰 第二层验证：积分验证和扣除（仅AI智能匹配模式）
                try:
                    # 检查积分余额
                    credit_balance = await CreditService.get_credit_balance(current_user, db)
                    if credit_balance < 5:
                        yield f"data: {json.dumps({'type': 'error', 'message': f'积分不足，AI智能匹配需要5积分，当前余额：{credit_balance}积分'})}\n\n"
                        return

                    # 扣除积分（每个request_id只扣费一次）
                    success, error_message = await CreditService.consume_credits(
                        user=current_user,
                        credits=5,
                        service_type="ai_selection",
                        operation_type="AI智能匹配服务",
                        request_id=request_id,
                        description="AI智能匹配服务",
                        db=db
                    )

                    if not success:
                        yield f"data: {json.dumps({'type': 'error', 'message': f'积分扣除失败: {error_message}'})}\n\n"
                        return

                    # 获取扣除后的余额
                    remaining_balance = await CreditService.get_credit_balance(current_user, db)
                    print(f"💰 AI智能匹配积分扣除成功 - 用户ID: {user_id}, 扣除积分: 5, 剩余积分: {remaining_balance}")
                    yield f"data: {json.dumps({'type': 'credit_deducted', 'message': f'已扣除5积分，剩余积分：{remaining_balance}'})}\n\n"

                except Exception as e:
                    print(f"❌ AI智能匹配积分验证/扣除异常: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'message': '积分验证失败，请稍后重试'})}\n\n"
                    return
                
                # 阶段2.A: 识别可达院校 (提前执行)
                stage_start = time.time()
                reachable_schools, similar_cases = await identify_reachable_schools(enhanced_profile)
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'school_matching', 'message': f'院校可达性分析完成，发现 {len(reachable_schools)} 所相关院校案例', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"

                # 阶段2.B: 动态确定QS排名范围
                dynamic_qs_range = determine_dynamic_qs_range(
                    enhanced_profile.undergraduate_school_tier,
                    reachable_schools
                )
                min_r, max_r = dynamic_qs_range
                yield f"data: {json.dumps({'type': 'progress', 'stage': '动态范围确定', 'message': f'已根据您的背景确定动态推荐范围: QS {min_r}-{max_r}', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"

                # 阶段3: 候选池生成 (使用动态范围)
                stage_start = time.time()
                candidate_pool = await generate_candidate_pool(enhanced_profile, dynamic_qs_range)
                
                if not candidate_pool:
                    yield f"data: {json.dumps({'type': 'error', 'message': '未找到符合条件的专业，请尝试调整专业方向或目标地区'})}\n\n"
                    return
                
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'candidates', 'message': f'找到 {len(candidate_pool)} 个候选专业', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"
                
                # 阶段4: 计算院校匹配分数 (现在可以更准确)
                stage_start = time.time()
                candidate_pool = await calculate_school_match_scores(candidate_pool, reachable_schools)
                
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'school_scoring', 'message': f'院校匹配分数计算完成', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"
                
                # 阶段5: 流式专业匹配和推荐生成
                async for stream_data in stream_recommendations(candidate_pool, enhanced_profile, similar_cases, current_user, request_id):
                    yield f"data: {json.dumps(stream_data)}\n\n"
            
            # 发送完成事件
            total_time = time.time() - start_time
            yield f"data: {json.dumps({'type': 'complete', 'message': '推荐生成完成', 'total_elapsed': round(total_time, 2)})}\n\n"
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            yield f"data: {json.dumps({'type': 'error', 'message': f'推荐过程中出现错误: {str(e)}'})}\n\n"
    
    return StreamingResponse(
        generate_recommendation_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@router.post("/analyze_profile")
async def analyze_user_profile(
    student_profile: StudentProfile,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    分析用户画像，返回增强后的用户信息

    Args:
        student_profile: 结构化的学生画像信息
        current_user: 当前登录用户（必须认证）

    Returns:
        增强后的用户画像

    Raises:
        HTTPException: 用户未认证时返回401错误
    """
    try:
        # 生成请求ID用于token计费
        from app.services.token_billing import TokenBillingService
        request_id = TokenBillingService.generate_request_id()

        enhanced_profile = await build_user_profile(student_profile, current_user, request_id)
        return enhanced_profile.model_dump()
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"构建用户画像失败: {str(e)}") 