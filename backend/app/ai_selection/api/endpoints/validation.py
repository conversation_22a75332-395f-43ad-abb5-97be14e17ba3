"""
智能选校验证API端点
提供套餐+积分双重验证相关的API接口
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from app.core.dependencies import get_current_user
from app.models.user import User
from app.db.database import get_db
from app.credit_payment.services.package_service import PackageService
from app.credit_payment.services.credit_service import CreditService

router = APIRouter()

@router.get("/package-access-validation")
async def validate_package_access(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    debug: bool = False
) -> Dict[str, Any]:
    """
    验证用户套餐访问权限（第一层验证）
    适用于所有智能选校模式（硬筛选 + AI智能匹配）

    **验证逻辑：**
    1. 检查用户是否购买了任意套餐（门票机制）
    2. 套餐验证通过后，用户可以无限使用硬筛选模式

    Returns:
        套餐验证结果
    """
    try:
        # 🔧 修复：启用调试模式
        if debug:
            current_user._debug_identity_check = True
            print(f"🔍 套餐权限验证调试 - 用户ID: {current_user.id}")
        
        # 使用重构后的套餐服务进行验证
        validation_result = await PackageService.validate_package_access(db, current_user)

        return {
            "success": True,
            "data": validation_result,
            "message": "多租户套餐验证完成"
        }

    except Exception as e:
        print(f"❌ 验证套餐访问权限时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "package_validation_error", "message": "套餐验证过程中发生错误，请稍后重试"}
        )



@router.get("/package-status")
async def get_user_package_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    debug: bool = False
) -> Dict[str, Any]:
    """
    获取用户当前身份的套餐购买状态（包含过期信息）

    Args:
        debug: 是否启用调试模式，输出详细的身份检查信息

    Returns:
        用户当前身份的套餐状态信息，包含过期状态和剩余天数
    """
    try:
        # 🔧 修复：启用调试模式
        if debug:
            current_user._debug_identity_check = True
            print(f"🔍 套餐状态查询调试 - 用户ID: {current_user.id}")
        
        # 使用重构后的套餐服务获取当前身份的套餐状态
        package_status = await PackageService.check_user_package_status(db, current_user)

        # 添加用户友好的过期状态描述
        if package_status.get("expiry_status"):
            expiry_status = package_status["expiry_status"]
            days_remaining = expiry_status.get("days_remaining", 0)

            if expiry_status.get("is_expired"):
                package_status["user_message"] = "您的套餐已过期，请续费后继续使用智能选校功能"
                package_status["action_required"] = "renew_package"
            elif days_remaining <= 7:
                package_status["user_message"] = f"您的套餐将在 {days_remaining} 天后过期，建议及时续费"
                package_status["action_required"] = "renew_soon"
            elif days_remaining <= 30:
                package_status["user_message"] = f"您的套餐还有 {days_remaining} 天到期"
                package_status["action_required"] = "renew_reminder"
            else:
                package_status["user_message"] = f"您的套餐还有 {days_remaining} 天有效期"
                package_status["action_required"] = "none"

        return {
            "success": True,
            "data": package_status,
            "message": "套餐状态获取成功"
        }

    except Exception as e:
        print(f"❌ 获取套餐状态时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "package_status_error", "message": "获取套餐状态失败，请稍后重试"}
        )

@router.get("/credit-status")
async def get_user_credit_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户当前身份的积分状态

    Returns:
        用户当前身份的积分余额和相关信息
    """
    try:
        # 使用重构后的积分服务获取当前身份的积分余额
        credit_balance = await CreditService.get_credit_balance(current_user, db)
        
        # 获取身份信息
        from app.core.data_isolation import DataIsolationFilter
        identity_type, organization_id = DataIsolationFilter.get_user_data_scope(current_user)
        identity_name = "组织身份" if identity_type == 'organization' else "个人身份"

        credit_status = {
            "balance": credit_balance,
            "identity_type": identity_type,
            "identity_name": identity_name,
            "organization_id": organization_id,
            "account_status": "active" if credit_balance >= 0 else "inactive",
            "sufficient_for_ai_selection": credit_balance >= 5
        }

        return {
            "success": True,
            "data": credit_status,
            "message": f"{identity_name}积分状态获取成功"
        }

    except Exception as e:
        print(f"❌ 获取积分状态时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "credit_status_error", "message": "获取积分状态失败，请稍后重试"}
        )

@router.get("/package-summary")
async def get_user_package_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户当前身份的套餐购买汇总信息
    
    Returns:
        用户当前身份的套餐购买历史和汇总信息
    """
    try:
        # 使用重构后的套餐服务获取当前身份的套餐汇总
        package_summary = await PackageService.get_user_package_summary(db, current_user)
        
        identity_name = package_summary.get('identity_name', '当前身份')
        
        return {
            "success": True,
            "data": package_summary,
            "message": f"{identity_name}套餐汇总信息获取成功"
        }
        
    except Exception as e:
        print(f"❌ 获取套餐汇总信息时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "package_summary_error", "message": "获取套餐汇总信息失败，请稍后重试"}
        )

@router.get("/ai-selection-requirements")
async def get_ai_selection_requirements() -> Dict[str, Any]:
    """
    获取智能选校功能的使用要求（分层验证机制）

    Returns:
        智能选校功能的使用要求和说明
    """
    return {
        "success": True,
        "data": {
            "validation_strategy": "layered_validation",
            "layer_1_package": {
                "required": True,
                "description": "需要购买任意套餐才能使用智能选校功能（门票机制）",
                "applies_to": ["硬筛选模式", "AI智能匹配模式"],
                "benefits": "套餐验证通过后，可以无限使用硬筛选模式"
            },
            "layer_2_credits": {
                "required": True,
                "description": "仅AI智能匹配模式需要消耗积分（使用货币）",
                "credits_required": 5,
                "applies_to": ["AI智能匹配模式"],
                "benefits": "更精准的个性化推荐和深度分析"
            },
            "modes": {
                "hard_filter": {
                    "name": "硬筛选模式",
                    "requirements": ["套餐验证"],
                    "cost": "免费（套餐内包含）",
                    "description": "基于条件的数据库筛选，无限使用"
                },
                "ai_selection": {
                    "name": "AI智能匹配模式",
                    "requirements": ["套餐验证", "积分消耗（5积分）"],
                    "cost": "5积分/次",
                    "description": "AI深度分析和个性化推荐"
                }
            },
            "validation_steps": [
                "1. 第一层：检查用户是否购买了任意套餐（所有模式必需）",
                "2. 第二层：仅AI智能匹配模式检查积分余额（5积分）",
                "3. 硬筛选模式只需通过第一层验证即可使用"
            ],
            "error_scenarios": {
                "no_package": {
                    "code": "no_package",
                    "message": "请先购买套餐后使用智能选校功能",
                    "action": "跳转到套餐购买页面",
                    "affects": ["硬筛选模式", "AI智能匹配模式"]
                },
                "insufficient_credits": {
                    "code": "insufficient_credits",
                    "message": "积分不足，AI智能匹配需要5积分",
                    "action": "跳转到积分充值页面或使用硬筛选模式",
                    "affects": ["AI智能匹配模式"]
                }
            }
        },
        "message": "智能选校使用要求获取成功"
    }

@router.post("/pre-validation")
async def pre_validate_ai_selection(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    智能选校验证接口
    支持GET和POST两种方式，在用户点击"开始匹配"按钮前进行验证
    实现套餐+积分双重验证机制，提供详细的错误信息和操作建议

    **验证逻辑：**
    1. 检查用户是否购买了任意套餐（门票机制）
    2. 检查用户积分余额是否足够（5积分）
    3. 返回详细的验证结果、错误信息和建议操作

    Returns:
        验证结果，包含can_proceed字段和具体的操作建议
    """
    try:
        # 使用重构后的套餐服务进行验证
        validation_result = await PackageService.validate_ai_selection_access(db, current_user)
        
        if validation_result["can_access"]:
            return {
                "success": True,
                "can_proceed": True,
                "data": validation_result,
                "message": "验证通过，可以开始智能选校"
            }
        else:
            # 验证失败，返回详细的错误信息和建议操作
            error_actions = {
                "no_package": {
                    "action": "purchase_package",
                    "action_text": "购买套餐",
                    "redirect_url": "/credit-payment/packages"
                },
                "insufficient_credits": {
                    "action": "recharge_credits",
                    "action_text": "充值积分", 
                    "redirect_url": "/credit-payment/recharge"
                },
                "system_error": {
                    "action": "retry",
                    "action_text": "重试",
                    "redirect_url": None
                }
            }
            
            action_info = error_actions.get(validation_result["reason"], {
                "action": "contact_support",
                "action_text": "联系客服",
                "redirect_url": None
            })
            
            return {
                "success": True,
                "can_proceed": False,
                "data": validation_result,
                "action": action_info,
                "message": validation_result["message"]
            }
            
    except Exception as e:
        print(f"❌ 智能选校预验证时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "pre_validation_error", "message": "预验证过程中发生错误，请稍后重试"}
        )
