"""
语言分数解析工具模块
处理各种格式的语言成绩字符串，转换为标准化的字典格式
"""

import re
import json
from typing import Dict, List


def parse_language_score_safely(language_score: str) -> Dict[str, float]:
    """
    安全解析语言分数字段，优先支持纯文本格式
    
    Args:
        language_score: 语言分数字符串或JSON字符串
        
    Returns:
        解析后的语言分数字典
        
    Examples:
        parse_language_score_safely("雅思7.0") -> {"IELTS": 7.0}
        parse_language_score_safely("托福92") -> {"TOEFL": 92.0}
        parse_language_score_safely("雅思7.0(L:7.5/R:8.0/W:6.5/S:6.0)") -> {"IELTS": 7.0}
        parse_language_score_safely("雅思7.5、六级536.0") -> {"IELTS": 7.5, "CET6": 536.0}
    """
    if not language_score:
        return {}
    
    # 如果已经是字典，直接返回
    if isinstance(language_score, dict):
        return language_score
    
    # 如果是字符串
    if isinstance(language_score, str):
        # 先检查是否为空字符串
        language_score = language_score.strip()
        if not language_score:
            return {}
        
        # 检查是否为常见的无效值
        invalid_values = {'nan', 'null', 'none', 'n/a', '-', '无', '无成绩', '暂无'}
        if language_score.lower() in invalid_values:
            return {}
        
        # 首先尝试解析为纯文本格式（现在是主要格式）
        text_result = parse_text_language_score(language_score)
        if text_result:
            return text_result
        
        # 如果文本解析失败，再尝试JSON解析（为了兼容性）
        if language_score.startswith('{') or language_score.startswith('['):
            try:
                parsed = json.loads(language_score)
                if isinstance(parsed, dict):
                    return parsed
                else:
                    # JSON解析结果不是字典，使用文本解析结果
                    return text_result
            except (json.JSONDecodeError, ValueError):
                # JSON解析失败，返回文本解析结果
                return text_result
        
        # 既不是JSON格式也无法文本解析，返回空字典
        return {}
    
    # 其他类型，返回空字典
    return {}


def parse_text_language_score(text_score: str) -> Dict[str, float]:
    """
    解析纯文本格式的语言分数
    
    Args:
        text_score: 纯文本格式的语言分数，如"雅思6.5", "托福92", "雅思7.0(L:7.5/R:8.0/W:6.5/S:6.0)"
        
    Returns:
        解析后的字典格式
    """
    result = {}
    text_score = text_score.strip()
    
    # 检查无效值
    invalid_values = {'nan', 'null', 'none', 'n/a', '-', '无', '无成绩', '暂无'}
    if text_score.lower() in invalid_values:
        return {}
    
    # 常见的语言考试类型映射
    exam_mapping = {
        '雅思': 'IELTS',
        'ielts': 'IELTS', 
        '托福': 'TOEFL',
        'toefl': 'TOEFL',
        'gre': 'GRE',
        'gmat': 'GMAT',
        '四级': 'CET4',
        '六级': 'CET6',
        'cet4': 'CET4',
        'cet6': 'CET6',
        'cet-4': 'CET4',
        'cet-6': 'CET6'
    }
    
    # 处理复合格式，如 "雅思7.5、六级536.0"
    if any(sep in text_score for sep in ['、', '，', ';', '|', '+']):
        # 分割多个成绩
        parts = re.split(r'[、，;|+]', text_score)
        for part in parts:
            part = part.strip()
            if part:
                part_result = parse_single_score(part, exam_mapping)
                result.update(part_result)
        return result
    
    # 处理单个成绩
    return parse_single_score(text_score, exam_mapping)


def parse_single_score(text_score: str, exam_mapping: Dict[str, str]) -> Dict[str, float]:
    """
    解析单个语言成绩
    
    Args:
        text_score: 单个语言成绩文本
        exam_mapping: 考试类型映射字典
        
    Returns:
        解析后的字典格式
    """
    result = {}
    
    # 处理带有详细分数的格式，如 "雅思7.0(L:7.5/R:8.0/W:6.5/S:6.0)"
    detail_match = re.search(r'([^(]+)\(([^)]+)\)', text_score)
    if detail_match:
        # 提取主要分数
        main_part = detail_match.group(1).strip()
        detail_part = detail_match.group(2).strip()
        
        # 解析主要分数
        main_result = parse_simple_score(main_part, exam_mapping)
        result.update(main_result)
        
        # 可以进一步解析详细分数，但目前只返回主要分数
        return result
    
    # 处理简单格式
    return parse_simple_score(text_score, exam_mapping)


def parse_simple_score(text_score: str, exam_mapping: Dict[str, str]) -> Dict[str, float]:
    """
    解析简单格式的语言成绩
    
    Args:
        text_score: 简单格式的语言成绩，如 "雅思6.5", "托福92"
        exam_mapping: 考试类型映射字典
        
    Returns:
        解析后的字典格式
    """
    result = {}
    
    # 模式1: "雅思6.5" 或 "IELTS 7.0"
    for exam_cn, exam_en in exam_mapping.items():
        # 更严格的模式匹配，避免误匹配
        pattern = rf'{re.escape(exam_cn)}[\s:：]*([0-9]+\.?[0-9]*)'
        match = re.search(pattern, text_score, re.IGNORECASE)
        if match:
            try:
                score = float(match.group(1))
                # 验证分数范围的合理性
                if is_valid_score(exam_en, score):
                    result[exam_en] = score
                    return result
            except ValueError:
                continue
    
    # 模式2: 如果没有明确的考试类型，尝试提取数字并推断
    number_match = re.search(r'([0-9]+\.?[0-9]*)', text_score)
    if number_match:
        try:
            score = float(number_match.group(1))
            # 根据分数范围推断考试类型
            if 0 <= score <= 9:
                result['IELTS'] = score
            elif 10 <= score <= 120:
                result['TOEFL'] = score
            elif 200 <= score <= 800:
                # 可能是GMAT或四六级
                if 200 <= score <= 710:
                    result['GMAT'] = score
                elif 220 <= score <= 710:
                    result['CET'] = score
                else:
                    result['其他'] = score
            elif 260 <= score <= 340:
                result['GRE'] = score
            else:
                result['其他'] = score
            return result
        except ValueError:
            pass
    
    # 如果都无法解析，返回空字典
    return {}


def is_valid_score(exam_type: str, score: float) -> bool:
    """
    验证分数是否在合理范围内
    
    Args:
        exam_type: 考试类型
        score: 分数
        
    Returns:
        是否有效
    """
    score_ranges = {
        'IELTS': (0, 9),
        'TOEFL': (0, 120),
        'GRE': (260, 340),
        'GMAT': (200, 800),
        'CET4': (220, 710),
        'CET6': (220, 710),
        'CET': (220, 710)
    }
    
    if exam_type in score_ranges:
        min_score, max_score = score_ranges[exam_type]
        return min_score <= score <= max_score
    
    # 对于未知类型，允许正数
    return score >= 0


def normalize_language_scores(language_scores: Dict[str, float]) -> Dict[str, float]:
    """
    标准化语言分数字典，统一考试类型名称
    
    Args:
        language_scores: 原始语言分数字典
        
    Returns:
        标准化后的语言分数字典
    """
    if not language_scores:
        return {}
    
    # 标准化映射
    normalize_mapping = {
        'ielts': 'IELTS',
        'toefl': 'TOEFL',
        'gre': 'GRE',
        'gmat': 'GMAT',
        'cet4': 'CET4',
        'cet6': 'CET6',
        'cet': 'CET',
        '雅思': 'IELTS',
        '托福': 'TOEFL',
        '四级': 'CET4',
        '六级': 'CET6'
    }
    
    normalized = {}
    for key, value in language_scores.items():
        # 标准化键名
        normalized_key = normalize_mapping.get(key.lower(), key.upper())
        normalized[normalized_key] = value
    
    return normalized


# 测试函数
def test_language_parser():
    """测试语言分数解析功能"""
    test_cases = [
        ("雅思7.0", {"IELTS": 7.0}),
        ("托福92", {"TOEFL": 92.0}),
        ("雅思7.0(L:7.5/R:8.0/W:6.5/S:6.0)", {"IELTS": 7.0}),
        ("雅思7.5、六级536.0", {"IELTS": 7.5, "CET6": 536.0}),
        ("IELTS 6.5", {"IELTS": 6.5}),
        ("TOEFL 100", {"TOEFL": 100.0}),
        ("nan", {}),
        ("无", {}),
        ("", {}),
        ("6.5", {"IELTS": 6.5}),
        ("100", {"TOEFL": 100.0}),
        ("雅思6", {"IELTS": 6.0}),
    ]
    
    print("=== 测试语言分数解析 ===")
    for input_text, expected in test_cases:
        result = parse_language_score_safely(input_text)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{input_text}' -> {result} (期望: {expected})")


if __name__ == "__main__":
    test_language_parser() 