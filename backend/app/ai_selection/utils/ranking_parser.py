"""
排名解析工具模块
处理QS排名字符串，支持单个数字和范围格式
"""

import re
from typing import Optional, <PERSON><PERSON>


def parse_qs_rank(qs_rank: str) -> Optional[int]:
    """
    解析QS排名字符串，返回用于排序的数值
    
    Args:
        qs_rank: QS排名字符串，可能是数字或范围（如"100-200"）
        
    Returns:
        用于排序的数值，范围取中位数，无法解析返回None
        
    Examples:
        parse_qs_rank("50") -> 50
        parse_qs_rank("100-200") -> 150
        parse_qs_rank("200+") -> 200
        parse_qs_rank("NR") -> None
    """
    if not qs_rank or qs_rank.strip() == "":
        return None
    
    qs_rank = str(qs_rank).strip()
    
    # 处理特殊情况
    if qs_rank.upper() in ["NR", "N/A", "NULL", "NONE", "-"]:
        return None
    
    # 移除可能的前缀（如"QS"、"#"等）
    qs_rank = re.sub(r'^(QS|#|\s)+', '', qs_rank, flags=re.IGNORECASE)
    
    # 情况1：单个数字（如"50"）
    single_number_match = re.match(r'^(\d+)$', qs_rank)
    if single_number_match:
        return int(single_number_match.group(1))
    
    # 情况2：范围格式（如"100-200"、"100~200"）
    range_match = re.match(r'^(\d+)[-~](\d+)$', qs_rank)
    if range_match:
        start = int(range_match.group(1))
        end = int(range_match.group(2))
        # 返回范围的中位数
        return (start + end) // 2
    
    # 情况3：大于某个数字（如"200+"、">200"）
    greater_than_match = re.match(r'^[>+]?(\d+)\+?$', qs_rank)
    if greater_than_match:
        return int(greater_than_match.group(1))
    
    # 情况4：小于某个数字（如"<50"）
    less_than_match = re.match(r'^<(\d+)$', qs_rank)
    if less_than_match:
        return int(less_than_match.group(1))
    
    # 情况5：尝试提取任何数字
    number_match = re.search(r'(\d+)', qs_rank)
    if number_match:
        return int(number_match.group(1))
    
    # 无法解析
    return None


def get_ranking_sort_key(qs_rank: str) -> Tuple[int, int]:
    """
    获取排名的排序键值，用于数据库查询的ORDER BY
    
    Args:
        qs_rank: QS排名字符串
        
    Returns:
        (优先级, 排名数值) 元组
        - 优先级: 0=有排名, 1=无排名
        - 排名数值: 解析后的数值，无排名时为999999
        
    Examples:
        get_ranking_sort_key("50") -> (0, 50)
        get_ranking_sort_key("100-200") -> (0, 150)
        get_ranking_sort_key("NR") -> (1, 999999)
    """
    parsed_rank = parse_qs_rank(qs_rank)
    
    if parsed_rank is not None:
        return (0, parsed_rank)  # 有排名，优先级0
    else:
        return (1, 999999)  # 无排名，优先级1，排在最后


def infer_school_tier_from_qs_rank(qs_rank: str) -> str:
    """
    根据QS排名推断学校层级
    
    Args:
        qs_rank: QS排名字符串
        
    Returns:
        学校层级 (tier1, tier2, tier3)
    """
    parsed_rank = parse_qs_rank(qs_rank)
    
    if parsed_rank is None:
        return "tier3"  # 无排名默认为tier3
    
    if parsed_rank <= 50:
        return "tier1"
    elif parsed_rank <= 200:
        return "tier2"
    else:
        return "tier3"


def format_qs_rank_display(qs_rank: str) -> str:
    """
    格式化QS排名用于显示
    
    Args:
        qs_rank: 原始QS排名字符串
        
    Returns:
        格式化后的显示字符串
        
    Examples:
        format_qs_rank_display("50") -> "QS #50"
        format_qs_rank_display("100-200") -> "QS #100-200"
        format_qs_rank_display("NR") -> "未排名"
    """
    if not qs_rank or qs_rank.strip() == "":
        return "未排名"
    
    qs_rank = str(qs_rank).strip()
    
    if qs_rank.upper() in ["NR", "N/A", "NULL", "NONE", "-"]:
        return "未排名"
    
    # 如果已经包含QS前缀，直接返回
    if qs_rank.upper().startswith("QS"):
        return qs_rank
    
    # 添加QS前缀和#符号
    return f"QS #{qs_rank}"


# 测试函数
def test_ranking_parser():
    """测试排名解析功能"""
    test_cases = [
        ("50", 50),
        ("100-200", 150),
        ("200+", 200),
        (">300", 300),
        ("NR", None),
        ("N/A", None),
        ("QS 25", 25),
        ("#47", 47),
        ("", None),
        (None, None),
    ]
    
    print("=== 测试排名解析 ===")
    for input_rank, expected in test_cases:
        result = parse_qs_rank(input_rank)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{input_rank}' -> {result} (期望: {expected})")
    
    print("\n=== 测试排序键值 ===")
    test_ranks = ["50", "100-200", "25", "NR", "300+"]
    for rank in test_ranks:
        sort_key = get_ranking_sort_key(rank)
        print(f"'{rank}' -> {sort_key}")
    
    print("\n=== 测试显示格式化 ===")
    for rank in test_ranks:
        display = format_qs_rank_display(rank)
        print(f"'{rank}' -> '{display}'")


if __name__ == "__main__":
    test_ranking_parser() 