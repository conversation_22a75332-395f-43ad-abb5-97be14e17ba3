"""
LLM (Large Language Model) 功能实现
使用实际大模型API进行文本生成和分析
"""

from typing import Dict, Any, List, Optional
from app.credit_payment.services.llm_integration import (
    process_text_with_credit_and_token_tracking_async as _unified_llm_call,
)

def get_current_model_name() -> str:
    """
    获取当前配置的模型名称

    Returns:
        str: 当前使用的模型名称
    """
    # 从实际API调用配置中获取模型名称
    # 这里应该与 process_text_with_api 函数中的模型配置保持一致
    return "qwen3-4b"

# 底层API调用与并发控制已下沉至 llm_integration 统一基座

async def process_text_with_credit_and_token_tracking_async(
    prompt: str,
    user=None,
    service_type: str = "ai_selection",
    operation_type: str = "general",
    request_id: Optional[str] = None,
    db = None,
    use_credits: bool = True,
    use_tokens: bool = True
) -> str:
    """
    转发器：统一走 `credit_payment.services.llm_integration` 的基座。
    将 user 对象直接传递以支持多租户积分扣除。
    保持对旧调用方的兼容需要同步更新调用处。
    """
    return await _unified_llm_call(
        prompt=prompt,
        user=user,
        service_type=service_type,
        operation_type=operation_type,
        request_id=request_id,
        db=db,
        use_credits=use_credits,
        use_tokens=use_tokens
    )

# 同步/异步底层调用已移动，删除本地实现

async def extract_key_entities_from_experience_async(
    experience: str,
    user=None,
    request_id: Optional[str] = None
) -> List[str]:
    """
    异步版本：从用户经历中提取关键实体
    
    Args:
        experience: 用户经历描述
        
    Returns:
        提取的关键实体列表
    """
    if not experience:
        return []
    
    prompt = f"""
    请分析以下学生的经历描述，提取其中包含的关键技能、经验类型和领域知识。
    返回一个标签列表，每个标签应该是简短的名词短语，表示一种技能、经验类型或领域知识。
    
    经历描述:
    {experience}
    
    请直接列出标签，每行一个，不要有编号或其他格式。例如:
    研究经验
    Python编程
    机器学习
    """
    
    try:
        # 使用积分和token双重tracking版本
        response = await process_text_with_credit_and_token_tracking_async(
            prompt,
            user=user,
            service_type="ai_selection",
            operation_type="extract_entities",
            request_id=request_id,
            use_credits=False,
            use_tokens=False
        )
        # 解析响应，获取实体列表
        entities = [line.strip() for line in response.strip().split('\n') if line.strip()]
        return entities
    except Exception as e:
        print(f"提取实体API调用失败: {e}")
        # 回退到关键词匹配方法
        return _fallback_entity_extraction(experience)

def _fallback_entity_extraction(experience: str) -> List[str]:
    """API调用失败时的备选实体提取方法"""
    entities = []
    
    # 简单的关键词匹配
    if "研究" in experience:
        entities.append("研究经验")
    if "实习" in experience:
        entities.append("实习经验")
    
    domain_keywords = {
        # 计算机与信息技术类专业的相关领域
        "计算机": ["计算机科学", "软件工程", "人工智能", "数据科学", "信息技术", "网络工程", "信息安全"],
        "软件": ["计算机科学", "软件工程", "信息技术", "系统工程", "数据科学"],
        "信息": ["计算机科学", "信息技术", "数据科学", "信息系统", "信息管理"],
        "人工智能": ["计算机科学", "数据科学", "机器学习", "认知科学", "统计学", "数学"],
        "数据科学": ["计算机科学", "统计学", "数学", "商业分析", "人工智能", "经济学"],
        "网络": ["网络工程", "信息安全", "计算机科学", "通信工程", "电子工程"],
        
        # 工程技术类专业的相关领域
        "电子": ["电子工程", "电气工程", "通信工程", "微电子", "计算机科学", "物理学"],
        "电气": ["电气工程", "电子工程", "自动化", "控制工程", "机械工程"],
        "通信": ["通信工程", "电子工程", "信息工程", "网络工程", "计算机科学"],
        "机械": ["机械工程", "自动化", "制造工程", "工业工程", "材料科学", "物理学"],
        "土木": ["土木工程", "建筑工程", "环境工程", "交通工程", "水利工程", "材料科学"],
        "材料": ["材料科学", "化学工程", "物理学", "化学", "机械工程", "电子工程"],
        "化工": ["化学工程", "化学", "材料科学", "环境工程", "生物工程", "石油工程"],
        "航空": ["航空工程", "航天工程", "机械工程", "材料科学", "物理学", "控制工程"],
        "生物工程": ["生物医学工程", "生物技术", "医学工程", "化学工程", "生物科学"],
        "环境": ["环境工程", "化学工程", "生态学", "地理科学", "公共卫生", "土木工程"],
        
        # 理学类专业的相关领域
        "数学": ["数学", "统计学", "数据科学", "金融数学", "计算机科学", "物理学"],
        "统计": ["统计学", "数学", "数据科学", "经济学", "心理学", "公共卫生"],
        "物理": ["物理学", "材料科学", "工程物理", "数学", "天文学", "地球科学"],
        "化学": ["化学", "材料科学", "化学工程", "生物化学", "药学", "环境科学"],
        "生物": ["生物科学", "生物技术", "生物医学", "医学", "环境科学", "农学"],
        "心理": ["心理学", "认知科学", "教育学", "社会学", "医学", "统计学"],
        "地理": ["地理科学", "环境科学", "测绘工程", "城市规划", "地质学", "生态学"],
        "地质": ["地质学", "地球科学", "环境科学", "地理科学", "材料科学", "物理学"],
        
        # 商科与经济类专业的相关领域
        "金融": ["金融", "经济学", "投资学", "风险管理", "数学", "统计学", "会计学"],
        "经济": ["经济学", "金融", "国际贸易", "统计学", "数学", "政治学", "社会学"],
        "管理": ["管理学", "工商管理", "人力资源", "心理学", "经济学", "社会学"],
        "会计": ["会计学", "金融", "经济学", "管理学", "数学", "统计学"],
        "市场": ["市场营销", "心理学", "统计学", "管理学", "传播学", "设计学"],
        "物流": ["物流管理", "供应链管理", "运筹学", "管理学", "工业工程", "经济学"],
        "商业": ["商业分析", "数据科学", "统计学", "管理学", "经济学", "计算机科学"],
        
        # 医学与健康类专业的相关领域
        "医学": ["临床医学", "基础医学", "生物医学", "生物科学", "化学", "心理学"],
        "护理": ["护理学", "医学", "心理学", "社会学", "管理学", "公共卫生"],
        "药学": ["药学", "化学", "生物科学", "医学", "化学工程", "生物工程"],
        "公共卫生": ["公共卫生", "医学", "统计学", "社会学", "环境科学", "管理学"],
        "康复": ["康复治疗", "医学", "心理学", "运动科学", "生物医学工程"],
        
        # 人文社科类专业的相关领域
        "教育": ["教育学", "心理学", "管理学", "社会学", "哲学", "数字媒体"],
        "法学": ["法学", "政治学", "社会学", "经济学", "哲学", "管理学"],
        "社会": ["社会学", "心理学", "政治学", "人类学", "教育学", "管理学"],
        "新闻": ["新闻学", "传播学", "社会学", "政治学", "文学", "数字媒体"],
        "语言": ["语言学", "文学", "教育学", "心理学", "传播学", "计算机科学"],
        "历史": ["历史学", "文学", "哲学", "考古学", "社会学", "政治学"],
        "哲学": ["哲学", "逻辑学", "心理学", "社会学", "政治学", "宗教学"],
        "政治": ["政治学", "国际关系", "法学", "经济学", "社会学", "历史学"],
        
        # 艺术设计类专业的相关领域
        "设计": ["设计学", "艺术学", "计算机科学", "心理学", "工程学", "市场营销"],
        "建筑": ["建筑学", "城市规划", "土木工程", "艺术学", "环境科学", "历史学"],
        "艺术": ["艺术学", "设计学", "文学", "历史学", "心理学", "教育学"],
        "传媒": ["传播学", "艺术学", "计算机科学", "心理学", "社会学", "市场营销"],
        
        # 农业与食品类专业的相关领域
        "农业": ["农学", "生物科学", "环境科学", "化学", "经济学", "管理学"],
        "食品": ["食品科学", "化学", "生物科学", "营养学", "化学工程", "管理学"],
        "林业": ["林学", "生态学", "环境科学", "生物科学", "地理科学", "管理学"],
        "水产": ["水产科学", "生物科学", "环境科学", "海洋科学", "食品科学"],
        
        # 其他交叉学科专业的相关领域
        "体育": ["体育学", "医学", "心理学", "教育学", "管理学", "康复治疗"],
        "旅游": ["旅游管理", "管理学", "经济学", "地理科学", "心理学", "市场营销"],
        "能源": ["能源工程", "物理学", "化学工程", "环境工程", "机械工程", "电气工程"],
        
        # 新兴交叉领域
        "金融科技": ["金融", "计算机科学", "数据科学", "数学", "统计学", "经济学"],
        "生物信息": ["生物科学", "计算机科学", "数学", "统计学", "医学", "化学"],
        "智能制造": ["机械工程", "计算机科学", "自动化", "工业工程", "材料科学"],
        "数字媒体": ["计算机科学", "艺术学", "传播学", "心理学", "市场营销", "设计学"],
        "可持续发展": ["环境科学", "经济学", "管理学", "工程学", "政治学", "社会学"]
    }
    
    for domain, keywords in domain_keywords.items():
        for kw in keywords:
            if kw in experience:
                entities.append(f"{domain}领域技能")
                break
    
    # 通用技能
    if any(kw in experience for kw in ["项目", "设计", "系统"]):
        entities.append("项目经验")
    if any(kw in experience for kw in ["竞赛", "比赛", "获奖", "奖项"]):
        entities.append("竞赛获奖")
    
    return entities

async def enhance_user_profile_async(
    basic_profile: Dict[str, Any],
    experiences: str,
    user=None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    异步版本：增强用户画像，添加从非结构化描述中提取的信息
    
    Args:
        basic_profile: 基本用户信息
        experiences: 用户经历描述
        
    Returns:
        增强后的用户画像
    """
    enhanced_profile = basic_profile.copy()
    
    # 提取关键实体和技能（异步）
    key_entities = await extract_key_entities_from_experience_async(experiences, user, request_id)
    enhanced_profile["extracted_skills"] = key_entities
    
    # 使用大模型推断兴趣领域和学术潜力（异步）
    if experiences:
        prompt = f"""
        请分析以下学生的背景信息和经历，推断:
        1. 该学生最可能感兴趣的学术领域
        2. 该学生的学术潜力评级（高/中等偏上/中等）
        
        学生背景:
        本科学校: {basic_profile.get("undergraduate_school", "")}
        本科专业: {basic_profile.get("undergraduate_major", "")}
        GPA: {basic_profile.get("gpa", "")}

        留学意向:
        意向地区: {basic_profile.get("target_regions", "")}
        意向专业领域: {basic_profile.get("target_major_direction", "")}
        
        学生经历:
        {experiences}
        
        请按以下格式回答:
        兴趣领域: [领域名称]
        学术潜力: [评级]
        """
        
        try:
            # 使用积分和token双重tracking版本
            response = await process_text_with_credit_and_token_tracking_async(
                prompt,
                user=user,
                service_type="ai_selection",
                operation_type="enhance_profile",
                request_id=request_id,
                use_credits=False,
                use_tokens=False
            )

            # 解析响应
            lines = response.strip().split('\n')
            interest_line = next((line for line in lines if line.startswith("兴趣领域:")), None)
            potential_line = next((line for line in lines if line.startswith("学术潜力:")), None)
            
            if interest_line:
                enhanced_profile["inferred_interest_domain"] = interest_line.replace("兴趣领域:", "").strip()
            if potential_line:
                enhanced_profile["academic_potential"] = potential_line.replace("学术潜力:", "").strip()
            
            # 如果解析失败，使用备选方法
            if not enhanced_profile.get("inferred_interest_domain") or not enhanced_profile.get("academic_potential"):
                fallback_results = _fallback_profile_enhancement(basic_profile, experiences, key_entities)
                if not enhanced_profile.get("inferred_interest_domain"):
                    enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
                if not enhanced_profile.get("academic_potential"):
                    enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
        
        except Exception as e:
            print(f"增强用户画像API调用失败: {e}")
            # 使用备选方法
            fallback_results = _fallback_profile_enhancement(basic_profile, experiences, key_entities)
            enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
            enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
    
    else:
        # 如果没有经历信息，使用备选方法
        fallback_results = _fallback_profile_enhancement(basic_profile, "", key_entities)
        enhanced_profile["inferred_interest_domain"] = fallback_results.get("inferred_interest_domain")
        enhanced_profile["academic_potential"] = fallback_results.get("academic_potential")
    
    return enhanced_profile

def _fallback_profile_enhancement(
    basic_profile: Dict[str, Any], 
    experiences: str, 
    key_entities: List[str]
) -> Dict[str, Any]:
    """API调用失败时的备选用户画像增强方法"""
    result = {}
    
    # 推断主要兴趣领域
    major = basic_profile.get("undergraduate_major", "").lower()
    interest_domain = None
    
    if any(kw in major for kw in ["计算机", "软件", "信息"]):
        interest_domain = "计算机科学与技术"
    elif any(kw in major for kw in ["电子", "电气", "通信"]):
        interest_domain = "电子与电气工程"
    elif any(kw in major for kw in ["数学", "统计"]):
        interest_domain = "数学与统计"
    elif any(kw in major for kw in ["经济", "金融", "商业", "管理"]):
        interest_domain = "商业与经济"
    else:
        # 从经历中推断
        if "计算机领域技能" in key_entities:
            interest_domain = "计算机科学与技术"
        elif "电子领域技能" in key_entities:
            interest_domain = "电子与电气工程"
        elif "数学领域技能" in key_entities:
            interest_domain = "数学与统计"
        elif "商业领域技能" in key_entities:
            interest_domain = "商业与经济"
    
    if interest_domain:
        result["inferred_interest_domain"] = interest_domain
    
    # 评估学术潜力
    gpa = float(basic_profile.get("gpa", 0))
    has_research = "研究经验" in key_entities
    
    if gpa > 85 and has_research:
        academic_potential = "高"
    elif gpa > 80 or has_research:
        academic_potential = "中等偏上"
    else:
        academic_potential = "中等"
    
    result["academic_potential"] = academic_potential
    
    return result