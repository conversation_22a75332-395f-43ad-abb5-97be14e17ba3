from typing import List, Dict, Any

from app.ai_selection.schemas.student_profile import StrengthProfile

def parse_gpa_format(gpa_str: str) -> tuple[str, str]:
    """
    解析GPA格式，兼容正斜杠 '/' 和反斜杠 '\' 两种分隔符
    
    Args:
        gpa_str: 原始GPA字符串，如 "3.1231/5", "81.231\100" 等
        
    Returns:
        tuple[str, str]: (gpa_value, scale_value)
    """
    if not gpa_str or not isinstance(gpa_str, str):
        return "", ""
    
    gpa_str = gpa_str.strip()
    
    # 检查是否包含分隔符（正斜杠或反斜杠）
    if '/' in gpa_str or '\\' in gpa_str:
        # 统一使用正斜杠进行分割，先将反斜杠替换为正斜杠
        normalized_str = gpa_str.replace('\\', '/')
        parts = normalized_str.split('/')
        
        if len(parts) == 2:
            gpa_part = parts[0].strip()
            scale_part = parts[1].strip()
            
            # 验证两部分都是有效数字
            try:
                float(gpa_part)
                float(scale_part)
                return gpa_part, scale_part
            except (ValueError, TypeError):
                pass
    
    # 如果没有分隔符或解析失败，返回原值和空制式
    return gpa_str, ""

def convert_gpa_to_100_scale(gpa_str: str, scale_str: str = "") -> float:
    """
    将不同制式的GPA转换为百分制，支持多种输入格式
    
    Args:
        gpa_str: GPA值字符串，可能包含格式如 "3.1231/5" 或 "81.231\100"
        scale_str: GPA制式，如果为空则从gpa_str中解析
    """
    try:
        # 如果没有提供制式，尝试从gpa_str中解析
        if not scale_str:
            parsed_gpa, parsed_scale = parse_gpa_format(gpa_str)
            if parsed_scale:
                gpa_str = parsed_gpa
                scale_str = parsed_scale
        
        gpa = float(gpa_str)
        
        if scale_str == "100" or scale_str == "100.0":
            return min(max(gpa, 0), 100)
        elif scale_str == "4.0" or scale_str == "4":
            return min(max(gpa * 25, 0), 100)
        elif scale_str == "5.0" or scale_str == "5":
            return min(max(gpa * 20, 0), 100)
        else:
            # 如果制式未知，但GPA看起来像4分或5分制，则进行猜测
            if 0 <= gpa <= 4:
                return gpa * 25
            if 4 < gpa <= 5:
                return gpa * 20
            return gpa  # 否则返回原值
    except (ValueError, TypeError):
        return 80.0 # 如果转换失败，返回默认值

def map_country_names(countries: List[str]) -> List[str]:
    """
    将前端传入的国家/地区名称直接返回（已无需映射）
    """
    return countries

def format_strength_as_text(strength: StrengthProfile) -> str:
    """
    将结构化的软实力信息格式化为一段描述性文本
    """
    if not strength:
        return ""

    parts = []
    
    # 映射字典
    competition_map = {"provincial": "省级", "national": "国家级", "international": "国际级"}
    internship_map = {
        "internet": "互联网大厂", "finance": "头部金融机构", "4a": "4A广告公司",
        "big4": "四大会计事务所", "fortune500": "世界500强", "listed": "上市公司", "other": "其他"
    }
    research_map = {
        "normal-paper": "普通刊物发表论文", "core-paper": "核心刊物发表论文",
        "sci-paper": "SCI刊物发表论文", "utility-patent": "实用新型专利",
        "invention-patent": "发明专利"
    }

    if strength.competition:
        comps = [competition_map.get(c, c) for c in strength.competition]
        parts.append(f"竞赛经历: {', '.join(comps)}")
    
    if strength.internship:
        interns = [internship_map.get(i, i) for i in strength.internship]
        parts.append(f"实习经历: {', '.join(interns)}")
        
    if strength.research:
        res = [research_map.get(r, r) for r in strength.research]
        parts.append(f"科研经历: {', '.join(res)}")

    return ". ".join(parts)

def infer_school_tier(school_name: str) -> str:
    """
    根据学校名称推断其层级 (tier1: 985, tier2: 211, tier3: 其他)
    这是一个简化的实现，实际应用中可能需要更复杂的规则库或模型。
    """
    # tier1 (C9 + 其他顶尖985)
    tier1_keywords = [
        "北京大学",
    "中国人民大学",
    "中国人民大学（苏州）", 
    "清华大学",
    "北京航空航天大学",
    "北京理工大学",
    "中国农业大学",
    "北京师范大学",
    "北京师范大学（珠海）", 
    "中央民族大学",
    "南开大学",
    "天津大学",
    "大连理工大学",
    "大连理工大学（盘锦）", 
    "东北大学",
    "东北大学（秦皇岛）", 
    "吉林大学",
    "哈尔滨工业大学",
    "哈尔滨工业大学（深圳）", 
    "哈尔滨工业大学（威海）", 
    "复旦大学",
    "同济大学",
    "上海交通大学",
    "华东师范大学",
    "南京大学",
    "东南大学",
    "浙江大学",
    "中国科学技术大学",
    "厦门大学",
    "山东大学",
    "山东大学（威海）",   
    "山东大学（青岛）",   
    "中国海洋大学",
    "武汉大学",
    "华中科技大学",
    "湖南大学",
    "中南大学",
    "国防科学技术大学",
    "中山大学",
    "中山大学（深圳）",   
    "中山大学（珠海）",   
    "华南理工大学",
    "四川大学",
    "电子科技大学",
    "重庆大学",
    "西安交通大学",
    "西北工业大学",
    "西北农林科技大学",
    "兰州大学"
    ]
    # tier2 (一些知名的211)
    tier2_keywords = [
    "北京交通大学",
    "北京工业大学",
    "北京科技大学",
    "北京化工大学",
    "北京邮电大学",
    "北京林业大学",
    "北京中医药大学",
    "北京外国语大学",
    "中国传媒大学",
    "中央财经大学",
    "对外经济贸易大学",
    "北京体育大学",
    "中央音乐学院",
    "中国政法大学",
    "华北电力大学",
    "华北电力大学（北京）", 
    "华北电力大学（保定）", 
    "天津医科大学",
    "河北工业大学",
    "太原理工大学",
    "内蒙古大学",
    "辽宁大学",
    "大连海事大学",
    "延边大学",
    "东北师范大学",
    "哈尔滨工程大学",
    "东北农业大学",
    "东北林业大学",
    "华东理工大学",
    "东华大学",
    "上海外国语大学",
    "上海财经大学",
    "上海大学",
    "第二军医大学",
    "海军军医大学",
    "苏州大学",
    "南京航空航天大学",
    "南京理工大学",
    "中国矿业大学",
    "中国矿业大学（北京）", 
    "中国矿业大学（徐州）",
    "河海大学",
    "河海大学（常州）",   
    "江南大学",
    "南京农业大学",
    "中国药科大学",
    "南京师范大学",
    "安徽大学",
    "合肥工业大学",
    "合肥工业大学（宣城）", 
    "福州大学",
    "南昌大学",
    "中国石油大学",
    "中国石油大学（北京）", 
    "中国石油大学（华东）", 
    "郑州大学",
    "中国地质大学",
    "中国地质大学（北京）", 
    "中国地质大学（武汉）", 
    "武汉理工大学",
    "华中农业大学",
    "华中师范大学",
    "中南财经政法大学",
    "湖南师范大学",
    "暨南大学",
    "华南师范大学",
    "广西大学",
    "海南大学",
    "西南交通大学",
    "西南交通大学（峨眉）",
    "四川农业大学",
    "四川农业大学（雅安）", 
    "四川农业大学（成都）", 
    "四川农业大学（都江堰）",
    "西南财经大学",
    "西南大学",
    "西南大学（荣昌）",   
    "贵州大学",
    "云南大学",
    "西藏大学",
    "西北大学",
    "西安电子科技大学",
    "长安大学",
    "陕西师范大学",
    "第四军医大学",
    "空军军医大学",
    "青海大学",
    "宁夏大学",
    "新疆大学",
    "石河子大学"
  ]

    for keyword in tier1_keywords:
        if keyword in school_name:
            return "tier1"
    
    for keyword in tier2_keywords:
        if keyword in school_name:
            return "tier2"
    
    return "tier3" # 其他情况为tier3
    
def map_major_names(majors: List[str]) -> List[str]:
    """
    将意向专业列表的英文代码映射为中文名称列表。
    如果传入的是大类（如 "science_field"），则自动展开为该大类下的所有具体专业。
    """
    # 1. 定义嵌套的专业领域结构
    major_fields = {
        "business_field": {
            "financial_engineering": "金融工程/金融数学", "finance": "金融",
            "business_analytics": "商业分析", "economics": "经济学", "accounting": "会计",
            "marketing": "市场营销", "information_systems": "信息系统管理", "management": "管理学",
            "hrm": "人力资源管理", "scm": "供应链管理", "entrepreneurship": "创业与创新",
            "real_estate": "房地产", "hospitality": "酒店管理", "business_admin": "工商管理", "other_business": "其他商科",
        },
        "engineering_field": {
            "computer_science": "计算机科学", "electrical_engineering": "电气电子工程",
            "mechanical_engineering": "机械工程", "materials": "材料科学", "chemical_engineering": "化学工程",
            "bioengineering": "生物工程", "civil_engineering": "土木工程", "engineering_management": "工程管理",
            "environmental_engineering": "环境工程", "industrial_engineering": "工业工程", "energy": "能源",
            "aerospace": "航空航天工程", "earth_science": "地球科学", "transportation": "交通运输",
            "marine_technology": "海洋技术", "food_science": "食品科学", "other_engineering": "其他工科",
        },
        "science_field": {
            "physics": "物理", "chemistry": "化学", "mathematics": "数学", "biology": "生物", "data_science": "数据科学",
        },
        "social_science_field": {
            "education": "教育学", "architecture": "建筑学", "law": "法律", "sociology": "社会学与社工",
            "international_relations": "国际关系", "philosophy": "哲学", "history": "历史",
            "public_policy": "公共政策", "arts": "艺术", "public_health": "公共卫生",
            "psychology": "心理学", "sports": "体育", "pharmacy": "药学", "medicine": "医学",
            "journalism": "新闻", "film": "影视", "culture": "文化", "media_communication": "媒体与传播",
            "new_media": "新媒体", "media_society": "媒介与社会", "science_communication": "科学传播",
            "strategic_communication": "策略传播", "media_industry": "媒体产业", "language": "语言学", "other_social_science": "其他社科",
        }
    }

    # 2. 创建一个扁平的映射字典，用于最终的翻译
    major_map = {major_key: name for field in major_fields.values() for major_key, name in field.items()}

    # 3. 展开专业列表
    expanded_majors = set()
    for major in majors:
        if major.endswith('_field'):
            # 如果是大类，展开为所有子专业
            if major in major_fields:
                expanded_majors.update(major_fields[major].keys())
        else:
            # 如果是具体专业，直接添加
            expanded_majors.add(major)
            
    # 4. 翻译并返回列表
    # 使用 sorted(list(expanded_majors)) 确保每次输出顺序一致
    translated_majors = [major_map.get(major, major.replace('_', ' ').title()) for major in sorted(list(expanded_majors))]
    
    return translated_majors

def map_degree_type(education: str) -> str:
    """
    将前端的学位代码映射为中文
    
    Args:
        education: 前端传递的学位代码
        
    Returns:
        中文学位名称
    """
    degree_map = {
        "undergraduate": "本科", 
        "master": "硕士",
        "phd": "博士"
    }
    return degree_map.get(education, "硕士")  # 默认为硕士 