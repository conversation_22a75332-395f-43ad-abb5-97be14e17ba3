"""
导出次数限制服务
基于套餐的导出次数管理和验证
"""

from typing import Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, func, desc
from datetime import datetime, timezone
from decimal import Decimal

from app.models.credit_payment import PaymentOrder, CreditTransaction, PackageConfig
from app.models.user import User
from app.credit_payment.services.package_service import PackageService
from app.credit_payment.config import CreditPaymentConfig


class ExportLimitService:
    """导出次数限制服务类"""

    # 套餐导出次数配置（硬编码fallback，保证系统稳定性）
    PACKAGE_EXPORT_LIMITS = {
        "trial_package": -1,    # 暂时全部设置为无限次，为后期区分excel导出无限和pdf导出计费做铺垫
        "personal_standard": -1,
        "personal_professional": -1,
        "business_flagship": -1,
        "enterprise_flagship": -1
    }

    # 缓存配置
    _export_limits_cache = {}
    _cache_timestamp = None
    _cache_ttl_seconds = 300  # 缓存5分钟

    @staticmethod
    async def _get_package_export_limit_async(
        db: AsyncSession,
        package_id: str,
        export_type: str = "excel"
    ) -> int:
        """
        从数据库获取套餐导出限制配置

        Args:
            db: 数据库会话
            package_id: 套餐ID
            export_type: 导出类型（excel/pdf）

        Returns:
            int: 导出次数限制（-1表示无限制）
        """
        try:
            # 检查缓存
            cache_key = f"{package_id}_{export_type}"
            current_time = datetime.now()

            if (ExportLimitService._cache_timestamp and
                (current_time - ExportLimitService._cache_timestamp).total_seconds() < ExportLimitService._cache_ttl_seconds and
                cache_key in ExportLimitService._export_limits_cache):
                return ExportLimitService._export_limits_cache[cache_key]

            # 从数据库查询
            query = select(PackageConfig).where(
                and_(
                    PackageConfig.package_id == package_id,
                    PackageConfig.is_active == True
                )
            )

            result = await db.execute(query)
            package_config = result.scalar_one_or_none()

            if package_config:
                # 根据导出类型获取限制
                if export_type == "pdf":
                    limit = package_config.export_limit_pdf
                else:  # 默认为excel
                    limit = package_config.export_limit_excel

                # 更新缓存
                ExportLimitService._export_limits_cache[cache_key] = limit
                ExportLimitService._cache_timestamp = current_time

                return limit
            else:
                # 数据库中没有找到配置，使用硬编码fallback
                fallback_limit = ExportLimitService.PACKAGE_EXPORT_LIMITS.get(package_id, 0)

                # 缓存fallback结果
                ExportLimitService._export_limits_cache[cache_key] = fallback_limit
                ExportLimitService._cache_timestamp = current_time

                print(f"⚠️ 套餐 {package_id} 在数据库中未找到，使用硬编码配置: {fallback_limit}")
                return fallback_limit

        except Exception as e:
            print(f"❌ 从数据库获取导出限制失败: {e}")
            # 发生错误时使用硬编码fallback
            return ExportLimitService.PACKAGE_EXPORT_LIMITS.get(package_id, 0)

    @staticmethod
    def _serialize_datetime_data(data):
        """递归序列化包含datetime的数据，避免JSON序列化错误"""
        if isinstance(data, dict):
            return {k: ExportLimitService._serialize_datetime_data(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [ExportLimitService._serialize_datetime_data(item) for item in data]
        elif hasattr(data, 'isoformat'):  # datetime对象
            return data.isoformat() if data else None
        else:
            return data
    
    @staticmethod
    async def check_export_permission(
        db: AsyncSession,
        user: User,
        client_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        检查用户导出权限

        Args:
            db: 数据库会话
            user: 用户对象
            client_id: 客户ID（可选，用于记录）

        Returns:
            Dict: 导出权限检查结果
        """
        try:
            # 获取用户当前身份信息（不强制修改，保持用户真实身份状态）
            from app.core.data_isolation import DataIsolationFilter
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            print(f"📤 导出权限检查 - 用户身份: {identity_type}, 组织ID: {organization_id}")

            # 1. 检查用户套餐状态（使用当前身份）
            package_status = await PackageService.check_user_package_status(db, user)
            
            if not package_status["has_package"]:
                return {
                    "can_export": False,
                    "reason": "no_package",
                    "message": "请先购买套餐后使用导出功能",
                    "package_status": ExportLimitService._serialize_datetime_data(package_status),
                    "export_info": {
                        "used_count": 0,
                        "limit": 0,
                        "remaining": 0
                    },
                    "action_required": "purchase_package",
                    "upgrade_suggestion": "建议购买个人标准版套餐，享受100次导出额度"
                }
            
            # 2. 检查套餐是否过期
            if package_status.get("is_expired", False):
                return {
                    "can_export": False,
                    "reason": "package_expired",
                    "message": "您的套餐已过期，请续费后继续使用导出功能",
                    "package_status": ExportLimitService._serialize_datetime_data(package_status),
                    "export_info": {
                        "used_count": 0,
                        "limit": 0,
                        "remaining": 0
                    },
                    "action_required": "renew_package",
                    "upgrade_suggestion": "请续费您的套餐以继续使用导出功能"
                }
            
            # 3. 获取套餐导出限制（从数据库读取，fallback到硬编码）
            package_id = package_status["package_id"]
            export_limit = await ExportLimitService._get_package_export_limit_async(db, package_id, "excel")

            # 4. 获取已使用的导出次数
            used_count = await ExportLimitService._get_used_export_count(db, user.id, package_status)

            # 5. 检查是否达到限制
            if export_limit == -1:  # 不限次数
                return {
                    "can_export": True,
                    "reason": "unlimited",
                    "message": "您的套餐支持无限次导出",
                    "package_status": ExportLimitService._serialize_datetime_data(package_status),
                    "export_info": {
                        "used_count": used_count,
                        "limit": -1,
                        "remaining": -1,
                        "is_unlimited": True
                    },
                    "action_required": "none"
                }
            
            remaining = export_limit - used_count
            
            if remaining <= 0:
                # 达到导出限制
                upgrade_suggestion = ExportLimitService._get_upgrade_suggestion(package_id)
                return {
                    "can_export": False,
                    "reason": "export_limit_reached",
                    "message": f"您的{package_status['package_name']}套餐导出次数已用完（{used_count}/{export_limit}次）",
                    "package_status": ExportLimitService._serialize_datetime_data(package_status),
                    "export_info": {
                        "used_count": used_count,
                        "limit": export_limit,
                        "remaining": 0
                    },
                    "action_required": "upgrade_package",
                    "upgrade_suggestion": upgrade_suggestion
                }
            
            # 6. 可以导出
            return {
                "can_export": True,
                "reason": "success",
                "message": f"可以导出，剩余{remaining}次导出机会",
                "package_status": ExportLimitService._serialize_datetime_data(package_status),
                "export_info": {
                    "used_count": used_count,
                    "limit": export_limit,
                    "remaining": remaining
                },
                "action_required": "none" if remaining > 10 else "consider_upgrade",
                "upgrade_suggestion": ExportLimitService._get_upgrade_suggestion(package_id) if remaining <= 10 else None
            }
            
        except Exception as e:
            print(f"❌ 检查导出权限时发生错误: {e}")
            return {
                "can_export": False,
                "reason": "system_error",
                "message": "系统错误，请稍后重试",
                "package_status": None,
                "export_info": {
                    "used_count": 0,
                    "limit": 0,
                    "remaining": 0
                },
                "action_required": "retry"
            }
    
    @staticmethod
    async def record_export_usage(
        db: AsyncSession,
        user: User,
        client_id: int,
        export_type: str = "school_book",
        description: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        记录导出使用次数
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            client_id: 客户ID
            export_type: 导出类型
            description: 描述信息
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 获取用户当前身份信息（不强制修改，保持用户真实身份状态）
            from app.core.data_isolation import DataIsolationFilter
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            print(f"📤 记录导出操作 - 用户身份: {identity_type}, 组织ID: {organization_id}")

            # 获取用户套餐信息（使用当前身份）
            package_status = await PackageService.check_user_package_status(db, user)

            if not package_status["has_package"]:
                return False, "用户无有效套餐"

            # 使用积分交易表记录导出次数（amount=0，仅作记录）
            from app.credit_payment.services.credit_service import CreditService

            # 获取用户积分账户
            account = await CreditService.get_or_create_credit_account(user, db)

            # 创建导出记录（使用积分交易表，但不扣减积分）
            transaction = CreditTransaction(
                user_id=user.id,
                organization_id=organization_id,  # 设置组织ID（个人身份时为None）
                user_credit_account_id=account.id,
                transaction_type="export_usage",  # 新的交易类型
                amount=0,  # 不扣减积分
                balance_before=account.credit_balance,
                balance_after=account.credit_balance,
                service_type="export_service",
                operation_type=export_type,
                request_id=f"export_{client_id}_{int(datetime.now().timestamp())}",
                tokens_consumed=0,
                conversion_rate=0,
                description=description or f"导出定校书 - 客户ID: {client_id} (身份: {identity_type})",
                metadata={
                    "client_id": client_id,
                    "export_type": export_type,
                    "package_id": package_status["package_id"],
                    "package_name": package_status["package_name"],
                    "identity_type": identity_type,
                    "organization_id": organization_id
                }
            )
            
            db.add(transaction)
            await db.commit()
            
            print(f"✅ 记录导出使用: 用户{user.id}, 客户{client_id}, 套餐{package_status['package_id']}")
            return True, "导出记录成功"
            
        except Exception as e:
            await db.rollback()
            print(f"❌ 记录导出使用失败: {e}")
            return False, f"记录导出使用失败: {str(e)}"
    
    @staticmethod
    async def _get_used_export_count(
        db: AsyncSession,
        user_id: int,
        package_status: Dict[str, Any]
    ) -> int:
        """
        获取用户已使用的导出次数（基于当前套餐的购买时间）
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            package_status: 套餐状态信息
            
        Returns:
            int: 已使用的导出次数
        """
        try:
            # 获取当前套餐的购买时间
            purchase_time = package_status.get("purchase_time")
            if not purchase_time:
                return 0
            
            # 查询从购买时间开始的导出记录
            query = select(func.count(CreditTransaction.id)).where(
                and_(
                    CreditTransaction.user_id == user_id,
                    CreditTransaction.transaction_type == "export_usage",
                    CreditTransaction.service_type == "export_service",
                    CreditTransaction.created_at >= purchase_time
                )
            )
            
            result = await db.execute(query)
            count = result.scalar() or 0
            
            return count
            
        except Exception as e:
            print(f"❌ 获取导出使用次数失败: {e}")
            return 0
    
    @staticmethod
    def _get_upgrade_suggestion(current_package_id: str) -> str:
        """
        获取升级建议
        
        Args:
            current_package_id: 当前套餐ID
            
        Returns:
            str: 升级建议文本
        """
        suggestions = {
            "trial_package": "建议购买个人标准版，享受500积分和100次导出额度",
            "personal_standard": "建议升级到个人专业版，享受1000积分和500次导出额度",
            "personal_professional": "建议升级到商业旗舰版，享受无限次导出",
            "test_production": "建议购买个人标准版，享受500积分和100次导出额度"
        }
        
        return suggestions.get(current_package_id, "建议升级到更高级套餐，享受更多导出次数")
    
    @staticmethod
    async def get_export_statistics(
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """
        获取用户导出统计信息

        Args:
            db: 数据库会话
            user: 用户对象

        Returns:
            Dict: 导出统计信息
        """
        try:
            # 获取用户当前身份信息（不强制修改，保持用户真实身份状态）
            from app.core.data_isolation import DataIsolationFilter
            identity_type, organization_id = DataIsolationFilter.get_user_data_scope(user)

            print(f"📊 导出统计查询 - 用户身份: {identity_type}, 组织ID: {organization_id}")

            # 获取套餐状态（使用当前身份）
            package_status = await PackageService.check_user_package_status(db, user)
            
            if not package_status["has_package"]:
                return {
                    "package_info": package_status,
                    "export_limit": 0,
                    "used_count": 0,
                    "remaining": 0,
                    "usage_history": []
                }
            
            # 获取导出限制（从数据库读取，fallback到硬编码）
            package_id = package_status["package_id"]
            export_limit = await ExportLimitService._get_package_export_limit_async(db, package_id, "excel")
            
            # 获取已使用次数
            used_count = await ExportLimitService._get_used_export_count(db, user.id, package_status)

            # 获取使用历史
            purchase_time = package_status.get("purchase_time")
            history_query = (
                select(CreditTransaction)
                .where(
                    and_(
                        CreditTransaction.user_id == user.id,
                        CreditTransaction.transaction_type == "export_usage",
                        CreditTransaction.service_type == "export_service",
                        CreditTransaction.created_at >= purchase_time if purchase_time else True
                    )
                )
                .order_by(desc(CreditTransaction.created_at))
                .limit(20)  # 最近20次记录
            )
            
            history_result = await db.execute(history_query)
            history_records = history_result.scalars().all()
            
            usage_history = []
            for record in history_records:
                usage_history.append({
                    "export_time": record.created_at.isoformat() if record.created_at else None,
                    "description": record.description,
                    "operation_type": record.operation_type,
                    "metadata": record.metadata
                })
            
            return {
                "package_info": package_status,
                "export_limit": export_limit,
                "used_count": used_count,
                "remaining": export_limit - used_count if export_limit != -1 else -1,
                "is_unlimited": export_limit == -1,
                "usage_history": usage_history
            }
            
        except Exception as e:
            print(f"❌ 获取导出统计信息失败: {e}")
            return {
                "package_info": None,
                "export_limit": 0,
                "used_count": 0,
                "remaining": 0,
                "usage_history": [],
                "error": str(e)
            }
