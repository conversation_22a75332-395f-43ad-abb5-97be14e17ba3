"""
组织管理服务层
提供组织相关的业务逻辑封装
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload
from datetime import datetime
import logging


from app.models.user import User
from app.models.organization import Organization, OrganizationMember, UserLoginSession
from app.schemas.organization import OrganizationCreate, OrganizationUpdate
from app.core.organization_permissions import (
    verify_organization_owner,
    verify_organization_member,
    get_user_organizations,
    check_organization_username_unique
)

logger = logging.getLogger(__name__)


class OrganizationService:
    """组织管理服务类"""
    
    @staticmethod
    async def create_organization(
        db: AsyncSession,
        user: User,
        organization_data: OrganizationCreate
    ) -> Organization:
        """
        创建组织
        
        Args:
            db: 数据库会话
            user: 创建者用户
            organization_data: 组织数据
            
        Returns:
            Organization: 创建的组织
            
        Raises:
            ValueError: 组织名称已存在
        """
        # 检查组织名称唯一性
        existing_org = await db.execute(
            select(Organization).where(
                Organization.name == organization_data.name,
                Organization.is_active == True
            )
        )
        if existing_org.scalars().first():
            raise ValueError("组织名称已存在")
        
        # 创建组织
        organization = Organization(
            name=organization_data.name,
            description=organization_data.description,
            owner_user_id=user.id
        )
        db.add(organization)
        await db.flush()
        
        # 创建Owner成员记录
        owner_member = OrganizationMember(
            organization_id=organization.id,
            user_id=user.id,
            role="owner",
            organization_username=user.username
        )
        db.add(owner_member)
        
        await db.commit()
        return organization
    
    @staticmethod
    async def get_user_organizations_with_stats(
        db: AsyncSession,
        user: User
    ) -> List[Dict[str, Any]]:
        """
        获取用户所属组织列表（包含统计信息）
        
        Args:
            db: 数据库会话
            user: 用户
            
        Returns:
            List[Dict]: 组织列表（包含统计信息）
        """
        memberships = await get_user_organizations(db, user)
        
        organizations = []
        for membership in memberships:
            # 获取组织信息
            org_result = await db.execute(
                select(Organization).where(Organization.id == membership.organization_id)
            )
            organization = org_result.scalars().first()
            
            if organization:
                # 获取成员数量
                member_count_result = await db.execute(
                    select(OrganizationMember).where(
                        OrganizationMember.organization_id == organization.id,
                        OrganizationMember.is_active == True
                    )
                )
                member_count = len(member_count_result.scalars().all())
                
                # 获取有效邀请码数量（仅Owner可见）
                active_invitations_count = 0
                if membership.role == "owner":
                    from app.models.organization import OrganizationInvitationCode
                    active_invitations_result = await db.execute(
                        select(OrganizationInvitationCode).where(
                            OrganizationInvitationCode.organization_id == organization.id,
                            OrganizationInvitationCode.status == "active"
                        )
                    )
                    active_invitations_count = len(active_invitations_result.scalars().all())
                
                org_data = organization.to_dict()
                org_data.update({
                    'current_user_role': membership.role,
                    'member_count': member_count,
                    'pending_invitations_count': active_invitations_count,
                    'user_organization_username': membership.organization_username
                })
                organizations.append(org_data)
        
        return organizations
    
    @staticmethod
    async def update_organization(
        db: AsyncSession,
        user: User,
        organization_id: int,
        organization_data: OrganizationUpdate
    ) -> Organization:
        """
        更新组织信息
        
        Args:
            db: 数据库会话
            user: 当前用户
            organization_id: 组织ID
            organization_data: 更新数据
            
        Returns:
            Organization: 更新后的组织
            
        Raises:
            ValueError: 组织名称已存在
        """
        # 验证权限
        await verify_organization_owner(db, user, organization_id)
        
        # 获取组织
        org_result = await db.execute(
            select(Organization).where(Organization.id == organization_id)
        )
        organization = org_result.scalars().first()
        
        if not organization:
            raise ValueError("组织不存在")
        
        # 检查名称唯一性
        if organization_data.name and organization_data.name != organization.name:
            existing_org = await db.execute(
                select(Organization).where(
                    Organization.name == organization_data.name,
                    Organization.is_active == True,
                    Organization.id != organization_id
                )
            )
            if existing_org.scalars().first():
                raise ValueError("组织名称已存在")
        
        # 更新组织信息
        if organization_data.name is not None:
            organization.name = organization_data.name
        if organization_data.description is not None:
            organization.description = organization_data.description
        
        await db.commit()
        return organization
    
    @staticmethod
    async def delete_organization(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> None:
        """
        删除组织（软删除）
        
        Args:
            db: 数据库会话
            user: 当前用户
            organization_id: 组织ID
        """
        # 验证权限
        await verify_organization_owner(db, user, organization_id)
        
        # 获取组织
        org_result = await db.execute(
            select(Organization).where(Organization.id == organization_id)
        )
        organization = org_result.scalars().first()
        
        if not organization:
            raise ValueError("组织不存在")
        
        # 软删除组织
        organization.is_active = False
        
        # 禁用所有成员关系
        members_result = await db.execute(
            select(OrganizationMember).where(
                OrganizationMember.organization_id == organization_id
            )
        )
        members = members_result.scalars().all()
        for member in members:
            member.is_active = False
        
        # 撤销所有有效邀请码
        from app.models.organization import OrganizationInvitationCode
        active_invitations_result = await db.execute(
            select(OrganizationInvitationCode).where(
                OrganizationInvitationCode.organization_id == organization_id,
                OrganizationInvitationCode.status == "active"
            )
        )
        active_invitations = active_invitations_result.scalars().all()
        for invitation in active_invitations:
            invitation.status = "revoked"
        
        await db.commit()
    
    @staticmethod
    async def get_organization_overview(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> Dict[str, Any]:
        """
        获取组织概览信息
        
        Args:
            db: 数据库会话
            user: 当前用户
            organization_id: 组织ID
            
        Returns:
            Dict: 组织概览信息
        """
        # 验证权限
        membership = await verify_organization_member(db, user, organization_id)
        
        # 获取组织信息
        org_result = await db.execute(
            select(Organization).where(Organization.id == organization_id)
        )
        organization = org_result.scalars().first()
        
        # 获取成员列表
        members_result = await db.execute(
            select(OrganizationMember)
            .options(joinedload(OrganizationMember.user))
            .where(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.is_active == True
            )
            .order_by(OrganizationMember.joined_at.desc())
        )
        members = members_result.scalars().all()
        
        # 获取有效邀请码（仅Owner可见）
        active_invitations = []
        if membership.role == "owner":
            from app.models.organization import OrganizationInvitationCode
            active_invitations_result = await db.execute(
                select(OrganizationInvitationCode)
                .options(joinedload(OrganizationInvitationCode.invited_by))
                .where(
                    OrganizationInvitationCode.organization_id == organization_id,
                    OrganizationInvitationCode.status == "active"
                )
                .order_by(OrganizationInvitationCode.created_at.desc())
            )
            active_invitations = active_invitations_result.scalars().all()
        
        # 构建响应数据
        org_data = organization.to_dict()
        org_data['current_user_role'] = membership.role
        
        # 统计信息
        stats = {
            'total_members': len(members),
            'active_members': len([m for m in members if m.is_active]),
            'pending_invitations': len(active_invitations),
            'total_invitations': len(active_invitations)  # 可以扩展为所有邀请
        }
        
        # 最近成员（最多5个）
        recent_members = []
        for member in members[:5]:
            member_data = member.to_dict()
            if member.user:
                member_data.update({
                    'user_email': member.user.email,
                    'user_nickname': member.user.nickname,
                    'user_avatar_url': member.user.avatar_url
                })
            recent_members.append(member_data)
        
        # 有效邀请码（最多5个）
        active_invitations_data = []
        for invitation in active_invitations[:5]:
            invitation_data = invitation.to_dict()
            
            # 获取邀请人的组织用户名
            inviter_org_username = None
            if invitation.invited_by:
                # 查找邀请人在该组织中的成员信息
                inviter_member_result = await db.execute(
                    select(OrganizationMember).where(
                        OrganizationMember.user_id == invitation.invited_by.id,
                        OrganizationMember.organization_id == organization_id,
                        OrganizationMember.is_active == True
                    )
                )
                inviter_member = inviter_member_result.scalars().first()
                if inviter_member:
                    inviter_org_username = inviter_member.organization_username
            
            invitation_data.update({
                'organization_name': organization.name,
                'invited_by_username': invitation.invited_by.username if invitation.invited_by else None,
                'invited_by_nickname': invitation.invited_by.nickname if invitation.invited_by else None,
                'invited_by_organization_username': inviter_org_username,
                'is_expired': invitation.is_expired()
            })
            active_invitations_data.append(invitation_data)
        
        return {
            **org_data,
            'stats': stats,
            'recent_members': recent_members,
            'pending_invitations': active_invitations_data
        }
