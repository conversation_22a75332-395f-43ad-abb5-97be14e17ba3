"""
Token计费服务
负责记录和统计用户的LLM token消耗，支持事务性操作和错误处理
"""

import uuid
from typing import Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from contextlib import asynccontextmanager

from app.models.token_usage import TokenUsage, UserTokenSummary
from app.models.user import User
from app.db.database import get_db


class TokenBillingService:
    """Token计费服务类"""
    
    @staticmethod
    async def record_token_usage(
        user_id: int,
        service_type: str,
        operation_type: str,
        tokens_consumed: int,
        request_id: Optional[str] = None,
        model_name: Optional[str] = None,
        prompt_length: Optional[int] = None,
        response_length: Optional[int] = None,
        error_message: Optional[str] = None,
        db: Optional[AsyncSession] = None
    ) -> bool:
        """
        记录token使用情况
        
        Args:
            user_id: 用户ID
            service_type: 服务类型 (ai_selection, ai_writing等)
            operation_type: 操作类型 (user_profile, recommendation_reason等)
            tokens_consumed: 消耗的token数量
            request_id: 请求ID，用于关联同一次推荐的多个调用
            model_name: 模型名称
            prompt_length: 提示词长度
            response_length: 响应长度
            error_message: 错误信息（如果有）
            db: 数据库会话（可选，如果不提供会自动创建）
            
        Returns:
            bool: 是否记录成功
        """
        # 如果没有提供数据库会话，创建一个新的
        if db is None:
            async for session in get_db():
                return await TokenBillingService._record_with_session(
                    session, user_id, service_type, operation_type, tokens_consumed,
                    request_id, model_name, prompt_length, response_length, error_message
                )
        else:
            return await TokenBillingService._record_with_session(
                db, user_id, service_type, operation_type, tokens_consumed,
                request_id, model_name, prompt_length, response_length, error_message
            )
    
    @staticmethod
    async def _record_with_session(
        db: AsyncSession,
        user_id: int,
        service_type: str,
        operation_type: str,
        tokens_consumed: int,
        request_id: Optional[str] = None,
        model_name: Optional[str] = None,
        prompt_length: Optional[int] = None,
        response_length: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """
        使用指定的数据库会话记录token使用情况
        
        注意：此函数不再自己管理事务，而是使用传入的会话
        调用方需要负责事务的开始和提交/回滚
        """
        try:
            # 1. 创建token使用记录
            token_usage = TokenUsage(
                user_id=user_id,
                service_type=service_type,
                operation_type=operation_type,
                tokens_consumed=tokens_consumed,
                request_id=request_id,
                model_name=model_name,
                prompt_length=prompt_length,
                response_length=response_length,
                error_message=error_message
            )
            db.add(token_usage)
            
            # 2. 更新用户token汇总
            # 首先尝试获取现有的汇总记录
            result = await db.execute(
                select(UserTokenSummary).where(UserTokenSummary.user_id == user_id)
            )
            summary = result.scalars().first()
            
            if summary is None:
                # 如果不存在汇总记录，创建一个新的
                summary = UserTokenSummary(
                    user_id=user_id,
                    total_tokens_consumed=tokens_consumed,
                    ai_selection_tokens=tokens_consumed if service_type == "ai_selection" else 0,
                    ai_writing_tokens=tokens_consumed if service_type == "ai_writing" else 0,
                    total_requests=1,
                    last_usage_at=datetime.utcnow()
                )
                db.add(summary)
            else:
                # 更新现有汇总记录
                summary.total_tokens_consumed += tokens_consumed
                summary.total_requests += 1
                summary.last_usage_at = datetime.utcnow()
                
                if service_type == "ai_selection":
                    summary.ai_selection_tokens += tokens_consumed
                elif service_type == "ai_writing":
                    summary.ai_writing_tokens += tokens_consumed
            
            # 不在这里提交，由调用方决定何时提交
            await db.flush()  # 确保变更被刷入会话但不提交
            return True
                
        except Exception as e:
            print(f"记录token使用失败: {e}")
            return False
    
    @staticmethod
    async def get_user_token_summary(user_id: int, db: Optional[AsyncSession] = None) -> Optional[Dict[str, Any]]:
        """
        获取用户的token使用汇总
        
        Args:
            user_id: 用户ID
            db: 数据库会话（可选）
            
        Returns:
            Dict: 用户token使用汇总，如果用户不存在返回None
        """
        if db is None:
            async for session in get_db():
                return await TokenBillingService._get_summary_with_session(session, user_id)
        else:
            return await TokenBillingService._get_summary_with_session(db, user_id)
    
    @staticmethod
    async def _get_summary_with_session(db: AsyncSession, user_id: int) -> Optional[Dict[str, Any]]:
        """
        使用指定的数据库会话获取用户token汇总
        """
        try:
            result = await db.execute(
                select(UserTokenSummary).where(UserTokenSummary.user_id == user_id)
            )
            summary = result.scalars().first()
            
            if summary:
                return summary.to_dict()
            else:
                # 如果没有汇总记录，返回默认值
                return {
                    'user_id': user_id,
                    'total_tokens_consumed': 0,
                    'ai_selection_tokens': 0,
                    'ai_writing_tokens': 0,
                    'total_requests': 0,
                    'last_usage_at': None,
                    'created_at': None,
                    'updated_at': None
                }
        except Exception as e:
            print(f"获取用户token汇总失败: {e}")
            return None
    
    @staticmethod
    def generate_request_id() -> str:
        """
        生成唯一的请求ID，用于关联同一次推荐的多个LLM调用
        
        Returns:
            str: 唯一的请求ID
        """
        return str(uuid.uuid4())
