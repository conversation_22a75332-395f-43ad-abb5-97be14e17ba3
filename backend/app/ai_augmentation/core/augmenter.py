import logging
import json_repair
from fastapi import UploadFile, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.models.client import Client, Academic, Work
from app.ai_augmentation.utils.file_parser import parse_file
from app.ai_augmentation.utils.llm import process_text_with_api_async
from app.ai_augmentation.schemas.augmentation import AugmentationResult

logger = logging.getLogger(__name__)

ACADEMIC_PROMPT_TEMPLATE = """
## 任务
请从提供的文档中提取一段学术经历，并按JSON格式返回。只需提取最主要或最相关的一段经历。

文档内容:
{document_text}

请提取以下字段:
- title: 学术项目标题 (必填)
- type: 项目类型 (例如: 毕业设计, 论文, 科研项目, 学科课程项目)
- date: 研究日期 (格式: YYYY-MM-DD)
- description: 详细学术经历描述 (请详细完整地提取，不要省略概括)

请严格按照以下JSON格式返回，确保括号闭合:
{{
  "title": "...",
  "type": "...",
  "date": "...",
  "description": "..."
}}
"""

WORK_PROMPT_TEMPLATE = """
## 任务
请从提供的文档中提取一段工作或实习经历，并按JSON格式返回。只需提取最主要或最相关的一段经历。

文档内容:
{document_text}

请提取以下字段:
- company: 公司/单位名称 (必填)
- position: 职位
- start_date: 开始日期 (格式: YYYY-MM-DD)
- end_date: 结束日期 (格式: YYYY-MM-DD)
- description: 详细工作经历描述 (请详细完整地提取，不要省略概括)

请严格按照以下JSON格式返回，确保括号闭合:
{{
  "company": "...",
  "position": "...",
  "start_date": "...",
  "end_date": "...",
  "description": "..."
}}
"""

async def augment_and_save_experience(
    db: AsyncSession,
    client_id: str,
    experience_type: str,
    file: UploadFile
) -> AugmentationResult:
    """
    核心业务逻辑：解析文件，调用LLM提取信息，并将结构化数据作为新实例存入数据库。
    """
    # 1. 验证客户是否存在
    query = select(Client).where(Client.id_hashed == client_id)
    result = await db.execute(query)
    client = result.scalars().first()
    if not client:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="客户不存在。")

    # 2. 解析文件内容
    try:
        document_text = await parse_file(file)
        if not document_text.strip():
            raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="文件内容为空或无法解析。")
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

    # 3. 根据类型选择提示模板并调用LLM
    if experience_type == "academic":
        prompt = ACADEMIC_PROMPT_TEMPLATE.format(document_text=document_text)
        model = Academic
    elif experience_type == "work":
        prompt = WORK_PROMPT_TEMPLATE.format(document_text=document_text)
        model = Work
    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的经历类型。")

    logger.info(f"正在为客户 {client_id} 智能提取 {experience_type} 经历...")
    llm_response_str = await process_text_with_api_async(prompt)
    
    if llm_response_str is None or "API调用失败" in llm_response_str:
        raise HTTPException(status_code=status.HTTP_504_GATEWAY_TIMEOUT, detail="调用LLM服务超时或失败。")

    # 4. 解析和修复JSON
    try:
        logger.info(f"正在解析LLM返回的 {experience_type} 经历JSON数据...")
        extracted_data = json_repair.loads(llm_response_str)
        # 确保提取的数据是字典
        if not isinstance(extracted_data, dict):
            # 兼容返回列表的情况
            if isinstance(extracted_data, list) and extracted_data:
                extracted_data = extracted_data[0]
            else:
                raise ValueError("LLM返回的顶层结构不是一个有效的JSON对象或列表。")
    except Exception as e:
        logger.error(f"无法解析LLM返回的数据: {e}\n原始数据: {llm_response_str}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="无法解析AI模型返回的数据。")

    # 5. 将提取的数据存入数据库
    try:
        logger.info(f"正在将提取的 {experience_type} 经历存入数据库...")
        
        # 验证必须的字段是否存在
        required_field = "title" if experience_type == "academic" else "company"
        if not extracted_data.get(required_field):
             raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"AI模型未能提取到必要的字段 '{required_field}'。请检查上传的文件内容。"
            )
            
        new_experience = model(**extracted_data, client_id=client.id)
        db.add(new_experience)
        await db.commit()
        await db.refresh(new_experience)

        logger.info(f"成功为客户 {client.id_hashed} 创建了新的 {experience_type} 经历, ID: {new_experience.id}")

        return AugmentationResult(experience_type=experience_type, data=new_experience)

    except Exception as e:
        await db.rollback()
        logger.error(f"数据保存失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"保存提取数据时发生内部错误: {str(e)}") 