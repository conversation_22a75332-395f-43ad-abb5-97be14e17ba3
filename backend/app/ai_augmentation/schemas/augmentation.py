from pydantic import BaseModel, Field
from typing import Union, Dict, Any
from app.schemas.client import AcademicResponse, WorkResponse

class AugmentationResponse(BaseModel):
    """
    智能增强响应模型
    """
    status: str = "success"
    experience_type: str = Field(..., description="The type of experience that was created ('academic' or 'work').")
    data: Union[AcademicResponse, WorkResponse] = Field(..., description="The created experience data.")

class AugmentationResult(BaseModel):
    """
    用于在核心逻辑和API层之间传递数据的模型
    """
    experience_type: str
    data: Any 