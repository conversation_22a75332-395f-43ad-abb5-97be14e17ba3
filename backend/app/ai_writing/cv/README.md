# AI 简历(CV)生成模块 - 前端对接文档

## 1. 模块概述

本模块提供基于客户档案的AI简历(CV)智能生成功能。前端通过调用一系列API，可以实现从选择客户、模块化勾选经历到最终生成带样式的HTML简历的完整流程。

### 核心特性

-   **客户数据驱动**：基于已有的客户档案信息。
-   **模块化选择**：前端可以灵活控制将客户的哪些经历（教育、工作、学术等）作为生成素材。
-   **双语支持**：支持生成中文简历和英文CV。
-   **HTML格式输出**：后端直接生成带内联样式的HTML，前端可直接渲染。

## 2. 集成流程 (Workflow)

前端与本模块的交互遵循以下步骤：

![CV Generation Workflow](https://i.imgur.com/example.png)  <!-- 预留图片链接 -->

1.  **获取客户列表**
    -   **动作**: 页面加载或用户点击"获取客户"按钮。
    -   **API调用**: `GET /api/ai-writing/cv/clients`。
    -   **结果**: 获得一个客户摘要列表，用于填充前端的选择框（Dropdown）。

2.  **选择客户，获取详细数据**
    -   **动作**: 用户从选择框中选择一个客户。
    -   **API调用**: `GET /api/ai-writing/cv/clients/{client_id}/modules`。
    -   **结果**: 获得该客户所有模块的详细经历数据。前端根据这些数据动态渲染出各个模块（教育、工作等）的选择列表。

3.  **用户选择经历 & 配置**
    -   **动作**: 用户在界面上勾选希望包含在CV中的具体经历条目，并选择生成语言。
    -   **前端逻辑**: 维护一个包含所有被选中项目ID的JavaScript对象。

4.  **发起生成请求**
    -   **动作**: 用户点击"生成CV"按钮。
    -   **API调用**: `POST /api/ai-writing/cv/generate`。
    -   **前端逻辑**: 根据用户的选择构造请求体（Request Body）。

5.  **渲染CV预览**
    -   **动作**: 接收到生成成功的API响应。
    -   **前端逻辑**: 将响应中的 `cv_content` (HTML字符串) 渲染到 `<iframe>` 中，以实现样式隔离，避免影响主页面布局。

## 3. API 端点详解

### 3.1. 获取客户档案列表

-   **功能**: 获取所有未归档客户的摘要信息，用于前端选择框。
-   **URL**: `GET /api/ai-writing/cv/clients`
-   **方法**: `GET`
-   **请求参数**: 无
-   **成功响应 (200 OK)**:
    -   **类型**: `List[ClientProfileSummary]`
    -   **示例**:
        ```json
        [
            {
                "id_hashed": "a1b2c3d4e5f6g7h8",
                "name": "张三",
                "education_count": 2,
                "academic_count": 1,
                "work_count": 3,
                "activity_count": 2,
                "award_count": 4,
                "skill_count": 1
            },
            {
                "id_hashed": "i9j0k1l2m3n4o5p6",
                "name": "Lisi",
                "education_count": 1,
                "academic_count": 0,
                "work_count": 1,
                "activity_count": 5,
                "award_count": 1,
                "skill_count": 1
            }
        ]
        ```

### 3.2. 获取客户详细模块数据

-   **功能**: 获取指定客户的所有详细经历数据，用于渲染勾选列表。
-   **URL**: `GET /api/ai-writing/cv/clients/{client_id}/modules`
-   **方法**: `GET`
-   **路径参数**:
    -   `client_id` (string, required): 客户的哈希ID (例如: `a1b2c3d4e5f6g7h8`)。
-   **成功响应 (200 OK)**:
    -   **类型**: `ClientModuleData`
    -   **示例**:
        ```json
        {
            "client_info": {
                "name": "张三",
                "email": "<EMAIL>",
                "phone": "13800138000",
                "location": "上海"
            },
            "education": [
                { "id": 1, "school": "清华大学", "major": "计算机科学", "degree": "本科", ... }
            ],
            "academic": [
                { "id": 10, "title": "基于深度学习的图像识别研究", "type": "科研项目", ... }
            ],
            "work": [
                { "id": 25, "company": "腾讯", "position": "软件工程师", ... }
            ],
            "activities": [],
            "awards": [],
            "skills": [],
            "language_scores": []
        }
        ```

### 3.3. 生成CV/简历

-   **功能**: 核心接口，根据用户选择生成最终的CV。
-   **URL**: `POST /api/ai-writing/cv/generate`
-   **方法**: `POST`
-   **请求体 (Request Body)**:
    -   **类型**: `CVGenerationRequest`
    -   **示例**:
        ```json
        {
            "client_id": "a1b2c3d4e5f6g7h8",
            "language": "english",
            "additional_info": "Applying for a Master's program in AI at Carnegie Mellon University.",
            "selected_education_ids": [1],
            "selected_academic_ids": [10],
            "selected_work_ids": [25, 26],
            "selected_activity_ids": [],
            "selected_award_ids": [31, 32, 33],
            "selected_skill_ids": [40],
            "selected_language_score_ids": []
        }
        ```
    -   **注意**: 对于任何用户未选择的模块，传递空数组 `[]` 即可。
-   **成功响应 (201 Created)**:
    -   **类型**: `CVGenerationResponse`
    -   **示例**:
        ```json
        {
            "status": "success",
            "cv_content": "<!DOCTYPE html><html><head>...</head><body>...</body></html>",
            "client_name": "张三",
            "language": "english"
        }
        ```

### 3.4. 保存CV内容

-   **功能**: 将当前编辑的CV内容保存为一个新的版本。该接口始终创建新记录，用于版本控制。
-   **URL**: `POST /api/ai-writing/cv/save`
-   **方法**: `POST`
-   **请求体 (Request Body)**:
    -   **类型**: `CVSaveRequest`
    -   **示例**:
        ```json
        {
            "client_id": "a1b2c3d4e5f6g7h8",
            "content_markdown": "# **ZHANG Ersan**\\n\\n**Phone: ...",
            "version_name": "版本 2025/7/3 17:45:00",
            "target_major": "Computer Science"
        }
        ```
-   **成功响应 (200 OK)**:
    -   **类型**: `CVSaveResponse`
    -   **示例**:
        ```json
        {
            "status": "success",
            "message": "CV内容保存成功",
            "client_name": "张三",
            "saved_at": "2025-07-03T17:45:01.123456"
        }
        ```
-   **错误响应**:
    -   `404 Not Found`: 如果提供的 `client_id` 不存在。
    -   `422 Unprocessable Entity`: 如果请求体缺少`client_id`, `content_markdown`, 或 `version_name`。
    -   `500 Internal Server Error`: 如果数据库保存操作失败。

## 4. 数据模型 (Schemas)

### `ClientProfileSummary` (客户摘要)

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `id_hashed` | string | 客户的哈希ID，用于API调用 |
| `name` | string | 客户姓名 |
| `education_count`| int | 教育经历数量 |
| `academic_count`| int | 学术经历数量 |
| `work_count` | int | 工作经历数量 |
| `activity_count` | int | 课外活动数量 |
| `award_count` | int | 奖项数量 |
| `skill_count` | int | 技能数量 |

### `ClientModuleData` (客户详细模块数据)

这是一个嵌套对象，包含客户基本信息和各个经历模块的列表。每个经历条目都是一个包含其所有字段的字典（如 `id`, `school`, `major` 等）。

### `CVGenerationRequest` (CV生成请求)

| 字段名 | 类型 | 描述 | 默认值 |
| :--- | :--- | :--- | :--- |
| `client_id` | string | **必需**，客户的哈希ID | |
| `language` | string | **必需**，`"english"` 或 `"chinese"` | |
| `additional_info` | string | 可选，补充信息 | `null` |
| `selected_..._ids`| List[int] | 各个模块选中的ID列表 | `[]` |

### `CVSaveRequest` (CV保存请求)

| 字段名 | 类型 | 描述 | 是否必需 |
| :--- | :--- | :--- | :--- |
| `client_id` | string | 客户的哈希ID | **是** |
| `content_markdown` | string | CV的完整Markdown内容 | **是** |
| `version_name` | string | 为当前保存的CV版本命名 | **是** |
| `target_major` | string | 该CV版本对应的目标专业 | 否 |

### `CVGenerationResponse` (CV生成响应)

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `status` | string | 状态，恒为 `"success"` |
| `cv_content` | string | 生成的CV内容（HTML格式） |
| `client_name` | string | 客户姓名 |
| `language` | string | 生成的语言 |

## 5. 错误处理

前端应准备处理以下常见的HTTP错误：

-   **`404 Not Found`**:
    -   当 `GET /clients/{client_id}/modules` 中的 `client_id` 无效时触发。
    -   当 `POST /generate` 中的 `client_id` 无效时触发。
    -   **建议操作**: 提示用户"客户不存在"。
-   **`500 Internal Server Error`**:
    -   当后端AI模型生成失败时触发。
    -   **建议操作**: 提示用户"CV生成失败，请稍后重试"，并在预览区显示错误提示。
-   **`422 Unprocessable Entity`**:
    -   当前端发送的请求体格式不正确时（如缺少必填字段）触发。
    -   **建议操作**: 开发阶段检查请求体构造逻辑。

## 6. 特别注意

-   **样式隔离**: 强烈建议使用 `<iframe>` 来渲染返回的 `cv_content`，这是避免AI生成的HTML样式污染主页面布局的最佳实践。
-   **截断警告**: 后端已实现内容截断检测。如果AI模型因为内容过长而截断输出，会在返回的 `cv_content` 结尾附加一个红色的警告框。前端无需特殊处理，只需正常渲染即可。这可以帮助用户理解为何内容不完整。 