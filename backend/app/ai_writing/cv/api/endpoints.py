from fastapi import APIRouter, status, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import Dict, Any, AsyncGenerator

from app.core.dependencies import DBSession, get_current_user
from app.models.user import User
from app.ai_writing.cv.core.generator import (
    generate_cv_stream,
    get_client_module_data,
    save_cv_content,
    get_cv_document,
    get_client_by_id_hashed
)
from app.ai_writing.schemas.cv import (
    CVGenerationRequest,
    ClientModuleData,
    CVSaveRequest,
    CVSaveResponse
)
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from app.models.client import AIWritingCV, Client
from app.core.data_isolation import DataIsolationFilter

router = APIRouter()

@router.post(
    "/generate",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="流式生成CV/简历",
    description="根据客户档案信息和用户选择的模块流式生成专业的CV/简历"
)
async def generate_cv_stream_endpoint(
    request: CVGenerationRequest,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> StreamingResponse:
    """
    流式生成CV/简历

    以流式响应返回生成的CV内容。
    🔒 关键安全修复：确保多租户数据隔离
    """
    cv_generator = generate_cv_stream(db, request, current_user)
    return StreamingResponse(cv_generator, media_type="text/markdown")

@router.post(
    "/save",
    response_model=CVSaveResponse,
    status_code=status.HTTP_200_OK,
    summary="保存CV内容",
    description="将CV的Markdown内容保存到数据库"
)
async def save_cv_endpoint(
    request: CVSaveRequest,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> CVSaveResponse:
    """
    保存CV内容
    
    将用户编辑后的CV Markdown内容保存到数据库中。
    """
    result = await save_cv_content(db, request)
    return result

# 客户列表接口已移除，前端统一使用主客户API (/api/clients/)
# 这样与PS和RL模块保持一致，避免认证问题

@router.get(
    "/clients/{client_id}/modules",
    response_model=ClientModuleData,
    status_code=status.HTTP_200_OK,
    summary="获取客户详细模块数据",
    description="获取指定客户的所有模块详细数据，用于前端显示和选择"
)
async def get_client_modules(
    client_id: str,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> ClientModuleData:
    """
    获取客户详细模块数据

    根据客户ID获取该客户的所有教育经历、工作经历、学术经历等详细信息，
    用于前端显示和用户选择特定的经历条目。
    🔒 关键安全修复：确保多租户数据隔离
    """
    module_data = await get_client_module_data(db, client_id, current_user)
    return ClientModuleData(**module_data)


@router.get(
    "/documents/{document_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="获取CV文档详情",
    description="根据文档ID获取CV文档的详细信息，包括内容和版本信息"
)
async def get_cv_document_endpoint(
    document_id: int,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取CV文档详情

    根据文档ID获取CV文档的详细信息，用于编辑模式加载。
    """
    document = await get_cv_document(db, document_id)
    return document


@router.get(
    "/clients/{client_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="获取客户基本信息",
    description="根据客户哈希ID获取客户的基本信息"
)
async def get_client_info_endpoint(
    client_id: str,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取客户基本信息

    根据客户哈希ID获取客户的基本信息，用于文书写作页面的客户选择。
    🔒 关键安全修复：确保多租户数据隔离
    """
    client = await get_client_by_id_hashed(db, client_id, current_user)
    return client


@router.delete(
    "/documents/{document_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除CV文档",
    description="硬删除指定的CV文档（需要有该客户的访问权限）"
)
async def delete_cv_document_endpoint(
    document_id: int,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> None:
    """
    删除CV文档（硬删除）

    权限验证：当前用户必须对该文档所属客户有访问权限
    """
    # 1. 获取文档及其所属客户
    result = await db.execute(
        select(AIWritingCV).options(selectinload(AIWritingCV.client)).where(AIWritingCV.id == document_id)
    )
    cv_doc = result.scalar_one_or_none()

    if not cv_doc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="CV文档不存在")

    # 2. 基于多租户隔离校验客户访问权限
    client_q = select(Client).where(Client.id == cv_doc.client_id)
    client_q = await DataIsolationFilter.apply_client_filter_async(client_q, current_user, db)
    client_check = await db.execute(client_q)
    if client_check.scalar_one_or_none() is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="无权限访问该客户文档或客户不存在")

    # 3. 硬删除并提交
    await db.delete(cv_doc)
    await db.commit()