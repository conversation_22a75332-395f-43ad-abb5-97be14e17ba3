"""
LLM (Large Language Model) 功能实现
专门用于AI文书生成
"""

from typing import Dict, Any, AsyncGenerator, Optional
import httpx
import asyncio
import logging
import re
import json

import json_repair

from app.ai_writing.config import ALIBABACLOUD_API_KEY_ai_writing, DOUBAO_API_KEY
from app.ai_writing.schemas.rl import RecommenderProfile
from sqlalchemy.ext.asyncio import AsyncSession
from app.credit_payment.services.llm_integration import track_llm_usage_with_credits

logger = logging.getLogger(__name__)

# 创建并发限制信号量，避免API过载
API_SEMAPHORE = asyncio.Semaphore(5)  # 限制为5个并发请求
HTTP_CLIENT = httpx.AsyncClient(timeout=httpx.Timeout(5.0, read=180.0))

async def _call_llm_stream_async(
    prompt: str,
    system_prompt: str,
    model: str = "qwen-flash",
    temperature: float = 0.3,
    usage_holder: Optional[Dict[str, Any]] = None
) -> AsyncGenerator[str, None]:
    """
    使用指定的提示词异步流式调用LLM API。
    这是一个内部通用函数，封装了API请求、错误处理和流式响应解析的逻辑。
    输出Markdown格式的内容。
    """
    async with API_SEMAPHORE:
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "stream": True,  # 启用流式输出
            "max_tokens": 12000,
            "temperature": temperature,
            "stream_options": {"include_usage": True},
            "top_p": 0.8,
            "stop": None,
            "top_k": 30,
            "enable_thinking": False,
            "n": 1,
        }
        headers = {
            "Authorization": f"Bearer {ALIBABACLOUD_API_KEY_ai_writing}",
            "Content-Type": "application/json"
        }

        try:
            async with HTTP_CLIENT.stream("POST", url, json=payload, headers=headers) as response:
                response.raise_for_status()
                
                finish_reason = None
                total_tokens_reported = None
                async for line in response.aiter_lines():
                    if not line or not line.startswith('data:'):
                        continue

                    line_data = line[len('data: '):].strip()
                    if not line_data:
                        continue
                    if line_data == '[DONE]':
                        break

                    try:
                        chunk = json.loads(line_data)
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析流式响应中的JSON: {line_data}")
                        continue

                    # 采集 usage（可能在最后一个单独chunk出现，choices可能为空）
                    usage = chunk.get("usage")

                    if isinstance(usage, dict):
                        tt = usage.get("total_tokens")
                        if isinstance(tt, int):
                            total_tokens_reported = tt

                    # 输出内容（需要 choices 且有 delta/content）
                    choices = chunk.get("choices")
                    if isinstance(choices, list) and choices:
                        first = choices[0] if isinstance(choices[0], dict) else {}
                        if isinstance(first, dict):
                            delta = first.get("delta", {})
                            if isinstance(delta, dict):
                                content = delta.get("content")
                                if content:
                                    yield content

                            fr = first.get("finish_reason")
                            if fr:
                                finish_reason = fr
                
                if finish_reason == "length":
                    logger.warning("内容生成被截断，因为达到了模型的最大token限制。")
                    warning_message = """
<div style='border: 2px solid red; color: red; background-color: #ffebee; padding: 15px; margin: 20px; text-align: center; font-family: sans-serif;'>
    <strong>警告：内容可能不完整</strong>
    <p>AI生成的内容因为达到最大长度而被截断。请尝试减少选择的经历数量或简化要求。</p>
</div>
"""
                    yield warning_message

                # 将 usage.total_tokens 写入外部容器（在可能位于最后一个chunk的场景下）
                if usage_holder is not None and total_tokens_reported is not None:
                    usage_holder["total_tokens"] = total_tokens_reported

        except httpx.TimeoutException:
            logger.error("API调用超时")
            yield "生成超时，请稍后重试。"
        except httpx.RequestError as e:
            logger.error(f"API请求失败: {e.request.url}, 错误: {e}")
            yield "生成失败，请稍后重试。"
        except Exception as e:
            logger.error(f"API调用时发生未知异常: {e}", exc_info=True)
            yield "生成失败，请稍后重试。"

async def _call_doubao_stream_async(
    prompt: str,
    system_prompt: str,
    temperature: float = 0.2,
    usage_holder: Optional[Dict[str, Any]] = None
) -> AsyncGenerator[str, None]:
    """
    使用豆包模型异步流式调用API
    """
    async with API_SEMAPHORE:
        url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        payload = {
            "model": "ep-20250808170149-7zck8",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "stream": True,
            "stream_options": {"include_usage": True},
            "thinking": {
                "type": "disabled"
            },
            "max_tokens": 12000,
            "temperature": temperature,
            "top_p": 0.8,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }
        headers = {
            "Authorization": f"Bearer {DOUBAO_API_KEY}",
            "Content-Type": "application/json"
        }

        try:
            async with HTTP_CLIENT.stream("POST", url, json=payload, headers=headers) as response:
                response.raise_for_status()

                finish_reason = None
                total_tokens_reported = None
                async for line in response.aiter_lines():
                    if not line.startswith('data:'):
                        continue

                    line_data = line[len('data: '):].strip()
                    if line_data == '[DONE]':
                        break

                    try:
                        chunk = json.loads(line_data)
                    except json.JSONDecodeError:
                        logger.warning(f"无法解析豆包流式响应中的JSON: {line_data}")
                        continue

                    # 采集 usage（可能在最后一个单独chunk出现，choices可能为空）
                    usage = chunk.get("usage")
                    if isinstance(usage, dict):
                        tt = usage.get("total_tokens")
                        if isinstance(tt, int):
                            total_tokens_reported = tt

                    # 输出内容（需要 choices 且有 delta/content）
                    choices = chunk.get("choices")
                    if isinstance(choices, list) and choices:
                        first = choices[0] if isinstance(choices[0], dict) else {}
                        if isinstance(first, dict):
                            delta = first.get("delta", {})
                            if isinstance(delta, dict):
                                content = delta.get("content")
                                if content:
                                    yield content

                            fr = first.get("finish_reason")
                            if fr:
                                finish_reason = fr

                if finish_reason == "length":
                    logger.warning("内容生成被截断，因为达到了模型的最大token限制。")
                    warning_message = """
<div style='border: 2px solid red; color: red; background-color: #ffebee; padding: 15px; margin: 20px; text-align: center; font-family: sans-serif;'>
    <strong>警告：内容可能不完整</strong>
    <p>AI生成的内容因为达到最大长度而被截断。请尝试减少选择的经历数量或简化要求。</p>
</div>
"""
                    yield warning_message

                if usage_holder is not None and total_tokens_reported is not None:
                    usage_holder["total_tokens"] = total_tokens_reported

        except httpx.TimeoutException:
            logger.error("豆包API调用超时")
            yield "调用超时，请稍后重试。"
        except httpx.RequestError as e:
            logger.error(f"豆包API请求失败: {e.request.url}, 错误: {e}")
            yield "调用失败，请稍后重试。"
        except Exception as e:
            logger.error(f"豆包API调用时发生未知异常: {e}", exc_info=True)
            yield "调用失败，请稍后重试。"

async def generate_cv_content_stream_async(
    client_data: Dict[str, Any],
    language: str = "english",
    additional_info: str = "",
    user=None,
    db: Optional[AsyncSession] = None,
    service_type: str = "ai_writing",
    operation_type: str = "cv_generation",
    request_id: Optional[str] = None
) -> AsyncGenerator[str, None]:
    """
    异步调用大模型API流式生成CV内容
    """
    prompt = _build_cv_prompt(client_data, language, additional_info)
    system_prompt = _build_cv_system_prompt(language)
    
    # 必须登录
    if user is None:
        yield "未登录或会话已过期，请重新登录后使用AI文书功能。"
        return

    accumulated_content = ""
    # 使用统一追踪上下文，积分不足时提前返回友好提示
    async with track_llm_usage_with_credits(
        user=user,
        service_type=service_type,
        operation_type=operation_type,
        request_id=request_id,
        model_name="qwen-flash",
        db=db,
        use_credits=True,
        use_tokens=True,
    ) as tracker:
        if getattr(tracker, "error_message", None):
            yield "积分余额不足，请充值后再试。如需帮助，请联系客服。"
            return

        usage_holder: Dict[str, Any] = {}
        async for chunk in _call_llm_stream_async(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=0.2,
            usage_holder=usage_holder
        ):
            accumulated_content += chunk
            yield chunk

        total_tokens = int(usage_holder.get("total_tokens", 0))
        tracker.record_usage(total_tokens, len(prompt), len(accumulated_content))

async def generate_ps_content_stream_async(
    client_data: Dict[str, Any],
    request: Any,
    user=None,
    db: Optional[AsyncSession] = None,
    service_type: str = "ai_writing",
    operation_type: str = "ps_generation",
    request_id: Optional[str] = None
) -> AsyncGenerator[str, None]:
    """
    异步调用大模型API流式生成PS内容
    使用双模型流程：豆包分段生成中文PS，Qwen翻译为英文
    """

    # 必须登录：在同一追踪上下文下累计豆包中文分段与Qwen英文翻译的tokens
    if user is None:
        yield "未登录或会话已过期，请重新登录后使用AI文书功能。"
        return

    async with track_llm_usage_with_credits(
        user=user,
        service_type=service_type,
        operation_type=operation_type,
        request_id=request_id,
        model_name="doubao+qwen",
        db=db,
        use_credits=True,
        use_tokens=True,
    ) as tracker_all:
        if getattr(tracker_all, "error_message", None):
            yield "积分余额不足，请充值后再试。如需帮助，请联系客服。"
            return
    
        # 第一步：使用豆包分段生成中文PS（内部使用，不输出）
        logger.info("开始使用豆包模型分段生成中文PS")

        # 计算各部分字数分配和段落分配
        word_allocation = _calculate_word_allocation(request.word_limit)
        paragraph_allocation = _calculate_paragraph_allocation(request.paragraph_setting, request.word_limit)
        logger.info(f"字数分配策略: {word_allocation}")
        logger.info(f"段落分配策略: {paragraph_allocation}")

        # 并行生成中文PS的三个部分（在同一追踪上下文中统计豆包 tokens）
        logger.info("开始并行生成三个部分")

        # 准备三个部分的生成任务
        section1_prompt = _build_ps_section1_prompt(client_data, request, word_allocation['section1'], paragraph_allocation['section1'])
        section1_system_prompt = _build_ps_section_system_prompt("第一部分，Why School")

        section2_prompt = _build_ps_section2_prompt(client_data, request, word_allocation['section2'], paragraph_allocation['section2'])
        section2_system_prompt = _build_ps_section_system_prompt("中间部分，能力展示")

        section3_prompt = _build_ps_section3_prompt(client_data, request, word_allocation['section3'], paragraph_allocation['section3'])
        section3_system_prompt = _build_ps_section_system_prompt("结尾部分，未来规划")

        # 并行生成三个部分
        h1: Dict[str, Any] = {}
        h2: Dict[str, Any] = {}
        h3: Dict[str, Any] = {}
        section1_task = _generate_section_content(section1_prompt, section1_system_prompt, "第一部分（Why School）", h1)
        section2_task = _generate_section_content(section2_prompt, section2_system_prompt, "中间部分（能力展示）", h2)
        section3_task = _generate_section_content(section3_prompt, section3_system_prompt, "结尾部分（未来规划）", h3)

        # 等待所有部分完成，并统计 usage.total_tokens
        logger.info("等待三个部分并行生成完成...")

        section1_task = asyncio.create_task(section1_task)
        section2_task = asyncio.create_task(section2_task)
        section3_task = asyncio.create_task(section3_task)
        section1_content, section2_content, section3_content = await asyncio.gather(section1_task, section2_task, section3_task)
        # 将中文阶段 tokens 计入本次会话统计（稍后统一 record）
        tokens_cn = int(h1.get("total_tokens", 0)) + int(h2.get("total_tokens", 0)) + int(h3.get("total_tokens", 0))
        chinese_sections = {
            'section1': section1_content,
            'section2': section2_content,
            'section3': section3_content
        }

        # 拼接完整的中文PS
        chinese_content = _assemble_chinese_ps_sections(chinese_sections)

        # 第二步：使用大模型将中文PS翻译为英文并流式输出（带积分与token追踪）
        logger.info("开始使用Qwen模型翻译PS为英文")
        translation_prompt = _build_ps_translation_prompt(chinese_content, client_data, request)
        translation_system_prompt = "你是一位专业的留学文书翻译专家，擅长将中文个人陈述翻译成地道的英文Personal Statement。请保持原文的逻辑结构和情感表达，使用学术性的英文表达。"

        total_tokens = tokens_cn
        prompt_len_total = len(section1_prompt) + len(section2_prompt) + len(section3_prompt)
        response_len_total = len(section1_content) + len(section2_content) + len(section3_content)

        # 2) Qwen 翻译英文并流式输出
        accumulated_en = ""
        usage_holder_en: Dict[str, Any] = {}
        try:
            async for chunk in _call_llm_stream_async(
                prompt=translation_prompt,
                system_prompt=translation_system_prompt,
                temperature=0.2,
                usage_holder=usage_holder_en
            ):
                accumulated_en += chunk
                yield chunk
        except Exception as e:
            logger.error(f"Qwen翻译失败，回退到豆包翻译: {e}")
            async for chunk in _call_doubao_stream_async(
                prompt=translation_prompt,
                system_prompt=translation_system_prompt,
                temperature=0.2,
                usage_holder=usage_holder_en
            ):
                accumulated_en += chunk
                yield chunk

        total_tokens += int(usage_holder_en.get("total_tokens", 0))

        prompt_len_total += len(translation_prompt)
        response_len_total += len(accumulated_en)

        tracker_all.record_usage(total_tokens, prompt_len_total, response_len_total)

async def _generate_section_content(
    prompt: str,
    system_prompt: str,
    section_name: str,
    usage_holder: Optional[Dict[str, Any]] = None
) -> str:
    """
    生成单个部分的内容（用于并行生成）

    Args:
        prompt: 提示词
        system_prompt: 系统提示词
        section_name: 部分名称（用于日志）

    Returns:
        生成的内容
    """
    logger.info(f"开始生成{section_name}")
    content = ""

    async for chunk in _call_doubao_stream_async(
        prompt=prompt,
        system_prompt=system_prompt,
        temperature=0.3,
        usage_holder=usage_holder
    ):
        content += chunk

    logger.info(f"{section_name}生成完成，长度: {len(content)} 字符")
    return content

async def parse_text_to_rl_profile_async(text: str) -> RecommenderProfile:
    """
    使用LLM将纯文本解析为结构化的RecommenderProfile对象。

    Args:
        text: 从文档中提取的纯文本内容。

    Returns:
        一个填充了数据的RecommenderProfile Pydantic模型实例。
    """
    from app.ai_writing.schemas.rl import RecommenderProfile

    system_prompt = """你是一个精通信息抽取的AI助手。你的任务是从用户提供的文本中，根据指定的JSON格式，准确地抽取出推荐人相关的信息。如果某些字段在文本中找不到对应信息，请将其保留为null，不要编造。"""
    
    # 构造一个带有pydantic模型json结构的few-shot prompt
    pydantic_json_structure = RecommenderProfile.model_json_schema()
    
    prompt = f"""
请从以下文本中抽取出推荐人的详细信息，并严格按照指定的JSON格式返回结果。

**待解析文本:**
---
{text}
---

**请将抽取的信息填充到以下JSON结构中:**
```json
{pydantic_json_structure}
```

请只返回符合上述JSON结构的纯文本，不要包含任何代码块标记(```json)或额外的解释。
"""
    
    # 使用非流式调用，因为我们需要完整的JSON对象
    async with API_SEMAPHORE:
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        payload = {
            "model": "qwen-turbo-latest",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "stream": False,
            "max_tokens": 2000,
            "temperature": 0.2, # 对于信息抽取，温度设为0以确保确定性
            "top_p": 0.8,
            "stop": None,
            "top_k": 30,
            "n": 1,
            "enable_thinking": False
        }
        headers = {
            "Authorization": f"Bearer {ALIBABACLOUD_API_KEY_ai_writing}",
            "Content-Type": "application/json"
        }

        try:
            response = await HTTP_CLIENT.post(url, json=payload, headers=headers)
            response.raise_for_status()
            
            response_data = response.json()
            json_content_str = response_data["choices"][0]["message"]["content"]

            # 解析JSON字符串为Pydantic模型
            parsed_data = json_repair.loads(json_content_str)
            return RecommenderProfile(**parsed_data)

        except httpx.RequestError as e:
            logger.error(f"解析RL文本时API请求失败: {e}", exc_info=True)
            raise
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"解析LLM返回的RL JSON时失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"解析RL文本时发生未知异常: {e}", exc_info=True)
            raise

async def generate_rl_content_stream_async(
    client_data: Dict[str, Any],
    recommender_profile: Dict[str, Any],
    word_limit: int,
    additional_info: str,
    user=None,
    db: Optional[AsyncSession] = None,
    service_type: str = "ai_writing",
    operation_type: str = "rl_generation",
    request_id: Optional[str] = None
) -> AsyncGenerator[str, None]:
    """
    异步调用大模型API流式生成RL内容
    """
    prompt = _build_rl_prompt(client_data, recommender_profile, word_limit, additional_info)
    system_prompt = _build_rl_system_prompt(recommender_profile)
    # 必须登录
    if user is None:
        yield "未登录或会话已过期，请重新登录后使用AI文书功能。"
        return

    accumulated_content = ""
    async with track_llm_usage_with_credits(
        user=user,
        service_type=service_type,
        operation_type=operation_type,
        request_id=request_id,
        model_name="qwen-flash",
        db=db,
        use_credits=True,
        use_tokens=True,
    ) as tracker:
        if getattr(tracker, "error_message", None):
            yield "积分余额不足，请充值后再试。如需帮助，请联系客服。"
            return

        holder: Dict[str, Any] = {}
        async for chunk in _call_llm_stream_async(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=0.2,
            usage_holder=holder
        ):
            accumulated_content += chunk
            yield chunk
        
        total_tokens = int(holder.get("total_tokens", 0))
        tracker.record_usage(total_tokens, len(prompt), len(accumulated_content))

def _build_cv_system_prompt(language: str) -> str:
    """
    构建CV生成的系统提示词
    """
    if language == "english":
        return """你是一位精通国际标准写作规范的英文简历撰写专家，具备跨学科理解能力，能够根据结构化字段准确生成风格专业、表达规范、格式统一的英文简历内容。请以Markdown格式输出简历内容，只需要简历内容，不需要其他的回答内容。"""
    else:
        return """你是一位专业的中文简历写作专家，擅长根据客户信息生成高质量的中文简历。请以Markdown格式输出简历内容，严格按照提供的格式要求生成专业的简历内容。"""

def _build_cv_prompt(
    client_data: Dict[str, Any], 
    language: str, 
    additional_info: str
) -> str:
    """
    构建CV生成的提示词
    """
    if language == "english":
        return _build_english_cv_prompt(client_data, additional_info)
    else:
        return _build_chinese_cv_prompt(client_data, additional_info)

def _build_english_cv_prompt(client_data: Dict[str, Any], additional_info: str) -> str:
    """构建英文CV的提示词"""
    
    # 基本信息部分
    name = client_data.get('name', '[Name]')
    prompt = f"""请将{name}的中文姓名转换为英文简历中常用的拼音格式，要求如下：
1. 姓氏在前，并全部大写
2. 名字在后，并保留首字母大写，其余小写
示例输入：张二三
示例输出：ZHANG Ersan

请按照以下格式生成个人信息：

# **{name}**

**Phone: {client_data.get('phone', '[Phone]')} | Email: {client_data.get('email', '[Email]')}**

"""

    # 教育背景部分
    prompt += "### **Education**\n---\n"
    
    if client_data.get('education'):
        for edu in client_data['education']:
            prompt += f"""请将以下教育信息转换为英文简历格式：
学校: {edu.get('school', '[School]')}
专业: {edu.get('major', '[Major]')}
学位: {edu.get('degree', '[Degree]')}
GPA: {edu.get('gpa', '[GPA]')}
起止时间: {edu.get('start_date', '[Start]')} - {edu.get('end_date', '[End]')}

请按照以下格式输出：
**[英文学校名] | [起止时间]**
Major: [英文专业名] (GPA: [GPA])

时间格式示例：Sep 2021 - Jun 2026

"""
    else:
        prompt += "[教育信息待补充]\n\n"

    prompt += "\n\n"

    # 学术项目部分
    if client_data.get('academic'):
        prompt += "### **Academic Projects**\n---\n"
        
        for academic in client_data['academic']:
            prompt += f"""请将以下学术项目转换为英文简历格式：
项目标题: {academic.get('title', '[Title]')}
项目类型: {academic.get('type', '[Type]')}
项目描述: {academic.get('description', '[Description]')}
项目时间: {academic.get('date', '[Date]')}

请按照以下格式输出：
**[英文项目标题]** 
*[角色/类型]*
* **Introduction:** [项目背景和目标的简洁描述]
* **[方法/技术1]:** [具体实现描述，使用专业术语]
* **[方法/技术2]:** [具体实现描述，使用专业术语]
* **[方法/技术3]:** [具体实现描述，使用专业术语]
* **Outcome:** [项目成果或状态]

注意：
1. 每个技术要点都要加粗标题
2. 使用学术性动词如 deployed, implemented, optimized 等
3. 包含具体的技术栈、工具、库等
4. 根据项目类型灵活调整格式：
   - 科研项目: 突出研究方法和学术贡献
   - 课程项目: 突出学习成果和技术应用
   - 比赛项目: 突出竞赛成果和团队协作

"""
        prompt += "\n\n"

    # 工作经历部分
    if client_data.get('work'):
        prompt += "### **Internships**\n---\n"
        
        for work in client_data['work']:
            prompt += f"""请将以下工作经历转换为英文简历格式：
职位: {work.get('position', '[Position]')}
公司: {work.get('company', '[Company]')}
工作类型: {work.get('employment_type', '[Employment Type]')}
起止时间: {work.get('start_date', '[Start Date]')} - {work.get('end_date', '[End Date]')}
工作描述: {work.get('description', '[Description]')}

请按照以下格式输出：
**[英文职位] ([职位类型]) | [起止时间]**
**[英文公司名]**
* [工作职责1的简洁描述，突出技术和成果，使用动词开头]
* [工作职责2的简洁描述，突出技术和成果，使用动词开头]
* [工作职责3的简洁描述，突出技术和成果，使用动词开头]

时间格式示例：Jul 4th, 2024 - Aug 15th, 2024
职位类型翻译：实习生 → Intern，全职 → Full-time等

"""
        prompt += "\n\n"

    # 课外活动部分
    if client_data.get('activities'):
        prompt += "### **Activities**\n---\n"
        for activity in client_data['activities']:
            prompt += f"""请将以下活动经历转换为英文简历格式：
活动名称: {activity.get('name', '')}
担任角色: {activity.get('role', '')}
起止时间: {activity.get('start_date', '')} - {activity.get('end_date', '')}
活动描述: {activity.get('description', '')}

请按照以下格式输出：
* [英文时间]: [英文角色] | **[英文活动名称]**

"""
        prompt += "\n\n"

    # 荣誉奖项部分
    if client_data.get('awards'):
        prompt += "### **Honors**\n---\n"
        for award in client_data['awards']:
            prompt += f"""请将以下奖项转换为英文简历格式：
奖项名称: {award.get('name', '')}
奖项级别: {award.get('level', '')}
获奖时间: {award.get('date', '')}
奖项描述: {award.get('description', '')}

请按照以下格式输出：
* **[英文奖项名称] | [获奖时间]**

"""
        prompt += "\n\n"

    # 技能部分
    if client_data.get('skills') or client_data.get('language_scores'):
        prompt += "### **Skills**\n---\n"
        
        if client_data.get('language_scores'):
            prompt += "* **Language Proficiency:** "
            lang_scores = []
            for lang in client_data['language_scores']:
                lang_type = lang.get('type', '').upper()
                lang_score = lang.get('score', '')
                if lang_type and lang_score:
                    if 'TBD' in lang_score.upper() or 'TBC' in lang_score.upper():
                        lang_scores.append(f"{lang_type} (TBD)")
                    else:
                        lang_scores.append(f"{lang_type}: {lang_score}")
            prompt += ", ".join(lang_scores) + "\n"
        
        if client_data.get('skills'):
            for skill in client_data['skills']:
                skill_type = skill.get('type', '')
                skill_desc = skill.get('description', '')
                if skill_type and skill_desc:
                    prompt += f"* **{skill_type}:** {skill_desc}\n"

    if additional_info:
        prompt += f"\n### **Additional Information**\n---\n{additional_info}\n"

    prompt += """

请生成专业的英文简历，严格按照上述Markdown格式要求：
1. 使用# **姓名**作为主标题
2. 使用### **章节名**作为各部分标题
3. 使用---作为分隔线
4. 重要内容使用**粗体**标记
5. 角色/类型信息使用*斜体*标记
6. 所有bullet points使用*开头
7. 保持清晰的层次结构和专业的视觉效果

重要提示：请只返回Markdown格式的简历内容，不要包含任何代码块标记（```）、额外的解释文字或其他格式。直接输出可以被Markdown解析器渲染的纯文本内容。
"""
    
    return prompt

def _build_chinese_cv_prompt(client_data: Dict[str, Any], additional_info: str) -> str:
    """构建中文CV的提示词"""
    
    # 基本信息部分
    name = client_data.get('name', '[姓名]')
    prompt = f"""请按照以下格式生成个人信息：

# **{name}**

**电话：{client_data.get('phone', '[电话]')} 邮箱：{client_data.get('email', '[邮箱]')}**

---

"""

    # 教育背景部分
    prompt += "### **教育背景**\n\n"
    
    if client_data.get('education'):
        for edu in client_data['education']:
            prompt += f"""请将以下教育信息转换为中文简历格式：
学校: {edu.get('school', '[学校]')}
专业: {edu.get('major', '[专业]')}
学位: {edu.get('degree', '[学位]')}
GPA: {edu.get('gpa', '[GPA]')}
起止时间: {edu.get('start_date', '[开始时间]')} - {edu.get('end_date', '[结束时间]')}

请按照以下格式输出：
{edu.get('school', '[学校]')} | {edu.get('start_date', '[开始时间]')} - {edu.get('end_date', '[结束时间]')}
专业：{edu.get('major', '[专业]')} | (GPA: {edu.get('gpa', '[GPA]')})

"""
    else:
        prompt += "[教育信息待补充]\n\n"

    prompt += "---\n\n"

    # 学术项目部分
    if client_data.get('academic'):
        prompt += "### **学术项目**\n\n"
        
        for academic in client_data['academic']:
            prompt += f"""请将以下学术项目转换为中文简历格式：
项目标题: {academic.get('title', '[项目标题]')}
项目类型: {academic.get('type', '[项目类型]')}
项目描述: {academic.get('description', '[项目描述]')}
项目时间: {academic.get('date', '[项目时间]')}

请按照以下格式输出：
**{academic.get('title', '[项目标题]')}** | *{academic.get('type', '[项目类型]')}*

* **项目背景：** [根据描述提取项目背景和目标的简洁描述]
* **[技术方法1]：** [具体实现描述，使用专业术语]
* **[技术方法2]：** [具体实现描述，使用专业术语]
* **[技术方法3]：** [具体实现描述，使用专业术语]
* **项目成果：** [项目成果或当前状态]

注意：
1. 每个技术要点都要加粗标题
2. 使用专业术语描述技术实现
3. 包含具体的技术栈、工具、库等
4. 根据项目类型灵活调整格式：
   - 科研项目: 突出研究方法和学术贡献
   - 课程项目: 突出学习成果和技术应用
   - 比赛项目: 突出竞赛成果和团队协作

"""
        prompt += "---\n\n"

    # 工作经历部分
    if client_data.get('work'):
        prompt += "### **工作经历**\n\n"
        
        for work in client_data['work']:
            prompt += f"""请将以下工作经历转换为中文简历格式：
职位: {work.get('position', '[职位]')}
公司: {work.get('company', '[公司]')}
工作类型: {work.get('employment_type', '[工作类型]')}
起止时间: {work.get('start_date', '[开始时间]')} - {work.get('end_date', '[结束时间]')}
工作描述: {work.get('description', '[工作描述]')}

请按照以下格式输出：
{work.get('position', '[职位]')} ({work.get('employment_type', '[工作类型]')}) | {work.get('start_date', '[开始时间]')} - {work.get('end_date', '[结束时间]')}
{work.get('company', '[公司]')}
* **[工作职责1的简洁描述，突出技术和成果]**
* **[工作职责2的简洁描述，突出技术和成果]**
* **[工作职责3的简洁描述，突出技术和成果]**

每个职责要点都要加粗并突出具体贡献

"""
        prompt += "---\n\n"

    # 课外活动部分
    if client_data.get('activities'):
        prompt += "### **课外活动**\n\n"
        for activity in client_data['activities']:
            prompt += f"""请将以下活动经历转换为中文简历格式：
活动名称: {activity.get('name', '')}
担任角色: {activity.get('role', '')}
起止时间: {activity.get('start_date', '')} - {activity.get('end_date', '')}
活动描述: {activity.get('description', '')}

请按照以下格式输出：
* {activity.get('start_date', '')} - {activity.get('end_date', '')}：{activity.get('role', '')} | **{activity.get('name', '')}**

"""
        prompt += "---\n\n"

    # 荣誉奖项部分
    if client_data.get('awards'):
        prompt += "### **荣誉奖项**\n\n"
        for award in client_data['awards']:
            prompt += f"""请将以下奖项转换为中文简历格式：
奖项名称: {award.get('name', '')}
奖项级别: {award.get('level', '')}
获奖时间: {award.get('date', '')}
奖项描述: {award.get('description', '')}

请按照以下格式输出：
* **{award.get('name', '')}**

"""
        prompt += "---\n\n"

    # 技能部分
    if client_data.get('skills') or client_data.get('language_scores'):
        prompt += "### **技能专长**\n\n"
        
        if client_data.get('language_scores'):
            prompt += "* **语言能力：** "
            lang_scores = []
            for lang in client_data['language_scores']:
                lang_type = lang.get('type', '').upper()
                lang_score = lang.get('score', '')
                if lang_type and lang_score:
                    if 'TBD' in lang_score.upper() or 'TBC' in lang_score.upper() or '待定' in lang_score:
                        lang_scores.append(f"{lang_type} (待定)")
                    else:
                        lang_scores.append(f"{lang_type}: {lang_score}")
            prompt += "，".join(lang_scores) + "\n"
        
        if client_data.get('skills'):
            for skill in client_data['skills']:
                skill_type = skill.get('type', '')
                skill_desc = skill.get('description', '')
                if skill_type and skill_desc:
                    prompt += f"* **{skill_type}：** {skill_desc}\n"

    if additional_info:
        prompt += f"\n### **补充信息**\n{additional_info}\n"

    prompt += """

请生成专业的中文简历，严格按照上述Markdown格式要求：
1. 使用# **姓名**作为主标题
2. 使用### **章节名**作为各部分标题
3. 使用---作为分隔线
4. 重要内容使用**粗体**标记
5. 角色/类型信息使用*斜体*标记
6. 右对齐时间请使用合适的空格调整
7. 所有bullet points使用*开头
8. 保持清晰的层次结构和专业的视觉效果
9. 使用中文标点符号和格式习惯

重要提示：请只返回Markdown格式的简历内容，不要包含任何代码块标记（```）、额外的解释文字或其他格式。直接输出可以被Markdown解析器渲染的纯文本内容。
"""
    
    return prompt 

def _build_rl_system_prompt(recommender_profile: Dict[str, Any]) -> str:
    """构建RL生成的系统提示词"""
    recommender_name = recommender_profile.get('first_name_en', '') + " " + recommender_profile.get('last_name_en', '')
    recommender_title = recommender_profile.get('title', 'a professional')
    
    return f"""你是一位经验丰富的推荐信撰写专家，现在将扮演 {recommender_name} ({recommender_title}) 的角色。你的任务是基于提供的学生信息和推荐人信息，撰写一封语气真实、内容具体、有说服力的英文推荐信。**重要提示:** 请只返回Markdown格式的推荐信内容，不要包含任何代码块标记（```）、额外的解释文字或其他格式。直接输出可以被Markdown解析器渲染的纯文本内容。确保内容流畅、专业。"""

def _build_rl_prompt(client_data: Dict[str, Any], recommender_profile: Dict[str, Any], word_limit: int, additional_info: str) -> str:
    """构建RL生成的提示词"""
    
    # 学生信息
    student_name = client_data.get('name', '[Student Name]')
    student_gender = client_data.get('gender', '[Student Gender]') 
    prompt = f"### Student to Recommend\n- Name: {student_name}\n- Gender: {student_gender}\n\n"

    # 推荐人信息
    recommender_name = recommender_profile.get('first_name_en', '') + " " + recommender_profile.get('last_name_en', '')
    prompt += f"### Recommender Information\n- Name: {recommender_name}\n"
    prompt += f"- Title: {recommender_profile.get('title', '')}\n"
    prompt += f"- Phone: {recommender_profile.get('phone', '')}\n"
    prompt += f"- Email: {recommender_profile.get('email', '')}\n"
    prompt += f"- Affiliation: {recommender_profile.get('affiliation', '')}\n"
    prompt += f"- Background: {recommender_profile.get('background_summary', 'N/A')}\n"

    # 关系与具体事例
    prompt += "\n**Relationship with Student & Specific Examples for the Letter**:\n"
    if recommender_profile.get('course_info'):
        course = recommender_profile['course_info']
        prompt += f"- Taught the student in the course '{course.get('course_name_en', 'N/A')}' during {course.get('term', 'N/A')}.\n"
        prompt += f"- The student's performance: Final grade was {course.get('final_grade', 'N/A')}. Note on outstanding performance: {course.get('outstanding_performance', 'N/A')}\n"
    
    if recommender_profile.get('work_info'):
        work = recommender_profile['work_info']
        prompt += f"- Supervised the student during '{work.get('collaboration_content', 'N/A')}' from {work.get('collaboration_date', 'N/A')}.\n"
        prompt += f"- The student's duties and performance: {work.get('student_duties', 'N/A')}\n"
        prompt += f"- Notable strengths observed: {work.get('praised_ability', 'N/A')}\n"
    
    if additional_info:
        prompt += f"\n### Additional Information from User\n{additional_info}\n"
        
    prompt += "\n---\n"
    
    # 生成指令
    # 处理字数限制：0表示不限制，其他正整数表示具体字数
    if word_limit == 0:
        word_limit_text = "No limit"
    else:
        word_limit_text = f"around {word_limit} words"

    prompt += f"""
### Task
Based on all the information provided above, please write a compelling and professional recommendation letter in English.

**Instructions:**
1.  **Introduction:** Start by introducing yourself (the recommender) and your relationship with the student.
2.  **Body Paragraphs:**
    -   Dedicate paragraphs to specific qualities of the student (e.g., academic ability, research potential, teamwork, leadership).
    -   Use the **concrete examples** from the "Relationship with Student & Specific Examples" section to support your claims. For instance, if the student did well in a course, mention their outstanding performance. If they excelled in a project, describe their contribution and praised abilities.
    -   Quantify achievements where possible (e.g., "ranked in the top 5% of the class based on my observation").
3.  **Conclusion:** Summarize the student's key strengths and give your unequivocal recommendation for the student.
4.  **Tone:** Maintain a professional, positive, and authentic tone. The language should reflect your role (e.g., a professor's letter would be more formal and academic). **Use appropriate pronouns (he/she) based on the student's gender.**
5.  **Format:** Output as a clean Markdown file.
6.  **Word Count:** Please adhere to a word count of **{word_limit_text}**.

**CRITICAL: You MUST structure the output with clear paragraphs. Use double newlines (\\n\\n) to separate paragraphs. For example:**
    
To Whom It May Concern,

This is the first paragraph introducing the relationship. It should be concise.

This is a body paragraph detailing the student's academic abilities, with specific examples.

This is another body paragraph focusing on teamwork and leadership skills.

In conclusion, I highly recommend this student for your program.

Sincerely,

Recommender Information.

**IMPORTANT:** Do not just list the facts. Weave them into a coherent narrative that paints a vivid picture of the student's capabilities and potential. Directly output the Markdown content of the letter without any extra explanations or code blocks.
"""
    return prompt


async def parse_text_to_ps_profile_async(text: str):
    """
    使用LLM将纯文本解析为结构化的申请动机和职业规划信息。

    Args:
        text: 从文档中提取的纯文本内容。

    Returns:
        一个填充了数据的ApplicationMotivationProfile Pydantic模型实例。
    """
    from app.ai_writing.schemas.ps import ApplicationMotivationProfile

    system_prompt = """你是一个精通信息抽取的AI助手。你的任务是从用户提供的文本中，根据指定的JSON格式，准确地抽取出申请动机和职业规划相关的信息。如果某些字段在文本中找不到对应信息，请将其保留为null，不要编造。"""

    # 构造一个带有pydantic模型json结构的few-shot prompt
    pydantic_json_structure = ApplicationMotivationProfile.model_json_schema()

    prompt = f"""
请从以下文本中抽取出申请动机和职业规划的详细信息，并严格按照指定的JSON格式返回结果。

**待解析文本:**
---
{text}
---

**请将抽取的信息填充到以下JSON结构中:**
```json
{pydantic_json_structure}
```

请只返回符合上述JSON结构的纯文本，不要包含任何代码块标记(```json)或额外的解释。
"""

    # 使用非流式调用，因为我们需要完整的JSON对象
    async with API_SEMAPHORE:
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        payload = {
            "model": "qwen-turbo-latest",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "stream": False,
            "max_tokens": 2000,
            "temperature": 0.2, # 对于信息抽取，温度设为0以确保确定性
            "top_p": 0.8,
            "stop": None,
            "top_k": 30,
            "n": 1,
            "enable_thinking": False
        }
        headers = {
            "Authorization": f"Bearer {ALIBABACLOUD_API_KEY_ai_writing}",
            "Content-Type": "application/json"
        }

        try:
            response = await HTTP_CLIENT.post(url, json=payload, headers=headers)
            if response.status_code != 200:
                error_text = response.text
                logger.error(f"LLM API调用失败: {response.status_code}, {error_text}")
                raise Exception(f"LLM API调用失败: {response.status_code}")

            response_data = response.json()
            content = response_data["choices"][0]["message"]["content"]

            # 尝试解析JSON
            try:
                # 使用json_repair来修复可能的JSON格式问题
                repaired_json = json_repair.repair_json(content)
                parsed_data = json.loads(repaired_json)
                return ApplicationMotivationProfile(**parsed_data)
            except Exception as parse_error:
                logger.error(f"解析LLM返回的JSON时出错: {parse_error}, 原始内容: {content}")
                raise Exception(f"解析LLM返回的JSON时出错: {parse_error}")

        except Exception as e:
            logger.error(f"调用LLM解析PS文档时出错: {e}")
            raise Exception(f"调用LLM解析PS文档时出错: {e}")


def _build_ps_translation_prompt(chinese_content: str, client_data: Dict[str, Any], request: Any) -> str:
    """
    构建PS翻译的提示词
    """

    prompt = f"""
Please translate the following Chinese Personal Statement into professional English, maintaining the original structure, logic, and emotional expression.

## Translation Requirements:
- Use academic and professional English
- Maintain the original paragraph structure and formatting
- Ensure the translation is natural and fluent
- Keep all Markdown formatting (headers, bold, italic, etc.)
- Adapt cultural references appropriately for international readers

## Chinese Personal Statement to Translate:
---
{chinese_content}
---

Please provide only the English translation in Markdown format, without any additional explanations or code block markers.
"""

    return prompt

def _calculate_word_allocation(target_word_limit: int) -> Dict[str, int]:
    """
    计算PS各部分字数分配（考虑中英文转换比例）

    Args:
        target_word_limit: 目标英文单词数限制

    Returns:
        各部分中文字数分配字典（已按转换比例调整）
    """
    if target_word_limit <= 0:
        # 默认分配（无字数限制时）
        return {
            'section1': 100,  # Why School
            'section2': 300,  # 能力展示
            'section3': 100   # 未来规划
        }

    # 中文到英文的转换比例动态调整
    # 根据目标字数动态调整比例：500字及以下用1.5，1000字及以上用1.8，中间线性插值
    def calculate_dynamic_ratio(word_count: int) -> float:
        if word_count <= 500:
            return 1.5
        elif word_count >= 1000:
            return 1.8
        else:
            # 线性插值：在500-1000字之间从1.5线性增长到1.8
            ratio = 1.5 + (word_count - 500) * (1.8 - 1.5) / (1000 - 500)
            return round(ratio, 2)

    CHINESE_TO_ENGLISH_RATIO = calculate_dynamic_ratio(target_word_limit)

    # 计算需要生成的中文字符数
    target_chinese_chars = int(target_word_limit * CHINESE_TO_ENGLISH_RATIO)

    logger.info(f"目标英文词数: {target_word_limit}, 动态计算中英比例: {CHINESE_TO_ENGLISH_RATIO}:1, 需要中文字符数: {target_chinese_chars}")

    # 按比例分配中文字数
    # 第一部分（Why School）：20%
    # 中间部分（能力展示）：60%
    # 结尾部分（未来规划）：20%
    section1_chars = int(target_chinese_chars * 0.2)
    section2_chars = int(target_chinese_chars * 0.6)
    section3_chars = target_chinese_chars - section1_chars - section2_chars  # 确保总数准确

    return {
        'section1': max(section1_chars, 100),  # 最少100字
        'section2': max(section2_chars, 300),  # 最少300字
        'section3': max(section3_chars, 100)   # 最少100字
    }

def _calculate_paragraph_allocation(paragraph_setting: int, target_word_limit: int = 0) -> Dict[str, int]:
    """
    计算PS各部分段落分配（结合字数条件）

    Args:
        paragraph_setting: 段落设置 (0=不限制, 1=智能分段, 2=4段, 3=5段, 4=6段, 5=7段, 6=8段)
        target_word_limit: 目标英文词数，用于智能判断段落数

    Returns:
        各部分段落分配字典
    """

    # 根据字数智能推荐段落数
    def get_recommended_paragraphs_by_word_count(word_count: int) -> int:
        if word_count <= 500:
            return 4  # 500字及以下：4段
        elif word_count <= 750:
            return 5  # 500-750字：5段
        elif word_count <= 1000:
            return 6  # 750-1000字：6段
        elif word_count <= 1250:
            return 7  # 1000-1250字：7段
        else:
            return 8

    # 确定最终使用的段落数
    if paragraph_setting <= 1:
        # 不限制或智能分段：根据字数智能判断
        if target_word_limit > 0:
            recommended_paragraphs = get_recommended_paragraphs_by_word_count(target_word_limit)
            logger.info(f"根据目标词数 {target_word_limit} 智能推荐段落数: {recommended_paragraphs}")
        else:
            recommended_paragraphs = 5  # 默认5段
        total_paragraphs = recommended_paragraphs
    else:
        # 用户指定段落数
        total_paragraphs = paragraph_setting + 2  # 2=4段, 3=5段, 4=6段, 5=7段, 6=8段

        # 如果用户指定的段落数与字数不匹配，给出警告建议
        if target_word_limit > 0:
            recommended_paragraphs = get_recommended_paragraphs_by_word_count(target_word_limit)
            if abs(total_paragraphs - recommended_paragraphs) > 1:
                logger.warning(f"用户指定 {total_paragraphs} 段，但根据字数 {target_word_limit} 建议 {recommended_paragraphs} 段")

    # 根据总段落数分配各部分段落
    if total_paragraphs == 4:
        return {'section1': 1, 'section2': 2, 'section3': 1}
    elif total_paragraphs == 5:
        return {'section1': 1, 'section2': 3, 'section3': 1}
    elif total_paragraphs == 6:
        return {'section1': 1, 'section2': 4, 'section3': 1}
    elif total_paragraphs == 7:
        return {'section1': 2, 'section2': 4, 'section3': 1}
    elif total_paragraphs == 8:
        return {'section1': 2, 'section2': 4, 'section3': 2}
    elif total_paragraphs >= 9:
        return {'section1': 2, 'section2': 5, 'section3': 2}
    else:
        # 默认分配（3段或更少的情况）
        return {'section1': 1, 'section2': 1, 'section3': 1}

def _build_ps_section_system_prompt(section_type: str) -> str:
    """
    构建PS分段生成的系统提示词

    Args:
        section_type: 段落类型

    Returns:
        系统提示词
    """
    return f"""你是一位专业的留学文书写作专家，专门负责撰写中文个人陈述(Personal Statement)的{section_type}。

你的写作特点：
1. **专业性强**：深度理解留学申请要求和招生官期望
2. **逻辑清晰**：内容结构合理，论证有力
3. **语言精练**：表达简洁有力，避免冗余
4. **真实可信**：基于提供的真实素材，不编造虚假信息
5. **格式规范**：使用Markdown格式，段落层次分明
6. **写作风格**：请在确保有文采（并非简单辞藻堆叠）的情况下去避免抽象的描述和词汇，用中国人自然语言的表达

请严格按照要求的字数范围和内容重点进行写作，确保内容质量和逻辑连贯性。"""

def _build_ps_section1_prompt(
    client_data: Dict[str, Any],
    request: Any,
    target_words: int,
    target_paragraphs: int
) -> str:
    """
    构建PS第一部分（Why School）的提示词
    """
    # 条件分支：优先使用用户提供的申请动机作为核心论点
    has_motivation = bool(getattr(getattr(request, 'motivation_profile', None), 'application_motivation', None))

    if has_motivation:
        # 模式A：有申请动机 → 以用户动机为核心论点，结合CV素材进行支撑
        prompt = f"""
请为以下学生撰写个人陈述第一部分（Why School开头段落，基于用户申请动机展开）。

## 写作要求：
- **目标字数**: {target_words}字左右
- **段落数量**: {target_paragraphs}段
- **核心任务**: 将“申请动机”作为中心论点进行论证，使用简历（CV）中的相关素材作支撑，说明该动机与目标院校/专业的匹配性与合理性。
- **写作原则**: 不编造素材；论点-论据清晰；官方、学术表达。
- **格式要求**: 使用Markdown格式

## 申请目标信息：
- 申请院校: {request.target_school}
- 申请学位: {request.degree}
- 申请专业: {request.major}

## 专业信息：
- 专业描述: {request.program_objectives or '暂无专业描述信息'}

## 用户提供的申请动机（核心论点）：
{request.motivation_profile.application_motivation}

## 简历素材（用于论据支撑）：
{_format_client_experiences_for_section1(client_data)}

## 写作指导：
1. 以“申请动机”为中心句（topic sentence）开启，直陈其来源/触发点与长期兴趣。
2. 从简历素材中择取1个最相关的经历作为论据（项目、实习、研究、活动），不用说得非常详细，通过模糊处理，强调“与该动机的内在关联”。
3. 自然连接到目标院校/专业（Why School/Program）：选1点特色（培养目标、研究方向、资源、师资），说明其如何“承接并放大”该动机。
4. 避免空话与模板化表达；不要提及具体课程；保持学术性与个性平衡。

请直接输出第一部分内容（Markdown），不要包含标题或多余说明。"""
        return prompt

    # 模式B：无申请动机 → 保持原逻辑：从CV素材选择合适经历作为故事化开头（不提课程细节）
    prompt = f"""
请为以下学生撰写个人陈述的第一部分（Why School开头段落，基于简历素材选择故事化开头）。

## 写作要求：
- **目标字数**: {target_words}字左右
- **段落数量**: {target_paragraphs}段
- **核心任务**: 从简历中选择一个具体项目/经历的片段作为开头（不要提及具体课程），通过该片段引出对专业方向的理解与兴趣，体现Why School。
- **写作原则**: 必须基于真实素材，保持官方学术风格
- **格式要求**: 使用Markdown格式

## 申请目标信息：
- 申请院校: {request.target_school}
- 申请学位: {request.degree}
- 申请专业: {request.major}

## 专业信息：
- 专业描述: {request.program_objectives or '暂无专业描述信息'}

## 学生背景素材（用于选择开头故事并作论据）：
{_format_client_experiences_for_section1(client_data)}

## 写作指导：
1. **素材选择**: 以故事作为开头，但不涉及具体课程名称与细枝末节。
2. **逻辑连接**: 将该经历与目标院校/专业的特色自然连接，突出契合度与必要性。
3. **避免套话**: 不使用“我一直梦想”等模板表达，避免泛泛而谈。
4. **具体化**: 可点到院校/项目特色或培养目标，但避免堆砌细节。
5. **真实性**: 严格基于提供的简历内容，不编造任何经历。

请直接输出第一部分内容，不要包含标题或其他说明。"""

    return prompt

def _build_ps_section2_prompt(
    client_data: Dict[str, Any],
    request: Any,
    target_words: int,
    target_paragraphs: int
) -> str:
    """
    构建PS第二部分（能力展示）的提示词
    """

    prompt = f"""请为以下学生撰写个人陈述的中间部分（能力展示段落）。

## 写作要求：
- **目标字数**: {target_words}字左右
- **段落数量**: {target_paragraphs}段
- **核心任务**: 用2-3个项目/经历展示学生具备的能力
- **组织方式**: 根据时间顺序采用递进方式，或按能力类型采用并行方式
- **格式要求**: 使用Markdown格式，每段后不要加总结

## 学生背景素材：
{_format_client_experiences_for_section2(client_data)}

## 写作指导：
1. **素材筛选**: 从简历内容中选择2-3个最有代表性的项目/经历
2. **能力展示**: 重点展示与目标专业相关的核心能力和技能
3. **具体描述**: 详细描述具体做了什么，承担了什么角色，取得了什么成果
4. **避免课程**: 不要提及具体课程名称，项目名称可以适当模糊处理
5. **逻辑安排**: 如果有时间顺序采用递进方式，无时间顺序采用并行展示
6. **无总结句**: 每段结尾不要添加总结性语句，让内容自然过渡

请直接输出中间部分内容，不要包含标题或其他说明。"""

    return prompt

def _build_ps_section3_prompt(
    client_data: Dict[str, Any],
    request: Any,
    target_words: int,
    target_paragraphs: int
) -> str:
    """
    构建PS第三部分（未来规划）的提示词
    """

    prompt = f"""请为以下学生撰写个人陈述的结尾部分（未来规划段落）。

## 写作要求：
- **目标字数**: {target_words}字左右
- **段落数量**: {target_paragraphs}段
- **核心任务**: 说明目标专业如何帮助实现未来目标，并加上升华句
- **格式要求**: 使用Markdown格式

## 申请目标信息：
- 申请院校: {request.target_school}
- 申请学位: {request.degree}
- 申请专业: {request.major}

## 专业信息：
- 专业描述: {request.program_objectives or '暂无专业描述信息'}
- 专业课程: {request.courses or '暂无课程信息'}

## 未来规划信息：
{_format_career_planning(request)}

    ## 写作指导：
    1. **规划连接**: 将个人未来规划与目标专业的培养目标自然连接
    2. **具体说明**: 具体说明专业的哪些方面能帮助实现目标
    3. **课程支撑（在本段可以明确点名课程）**: 若提供了课程信息（见上方“专业课程”），请挑选与规划最相关的1-2门具体课程/模块进行点名，说明其如何提供所需的方法、工具或视角；避免简单罗列，强调“课程→能力/路径→目标”的链路。
    4. **前景展望**: 描述完成学业后的职业发展方向
    5. **升华结尾**: 最后1-2句话升华全文，体现更高层次的追求
    6. **避免空泛**: 不使用过于宽泛的表述，要具体可信

请直接输出结尾部分内容，不要包含标题或其他说明。"""

    return prompt

def _format_client_experiences_for_section1(client_data: Dict[str, Any]) -> str:
    """格式化客户经历用于第一部分（Why School）"""
    cv_content = client_data.get('cv_content', '')

    if cv_content:
        return f"""### 学生简历内容（用于选择合适素材作为申请动机）：
{cv_content}

**注意**：请从以上简历内容中选择最能体现申请动机的经历作为Why School的开头素材。"""

    # 如果没有CV内容，回退到基本信息
    basic_info = []
    if client_data.get('name'):
        basic_info.append(f"姓名: {client_data['name']}")
    if client_data.get('email'):
        basic_info.append(f"邮箱: {client_data['email']}")
    if client_data.get('phone'):
        basic_info.append(f"电话: {client_data['phone']}")

    return '\n'.join(basic_info) if basic_info else "暂无学生背景信息"

def _format_client_experiences_for_section2(client_data: Dict[str, Any]) -> str:
    """格式化客户经历用于第二部分（能力展示）"""
    cv_content = client_data.get('cv_content', '')

    if cv_content:
        return f"""### 学生简历内容（用于能力展示）：
{cv_content}

**写作指导**：
1. 从以上简历内容中选择2-3个最有代表性的项目/经历
2. 重点展示与目标专业相关的核心能力
3. 详细描述具体做了什么，取得了什么成果
4. 不要提及具体课程名称，项目名称可以适当模糊处理
5. 确保各段落之间有逻辑联系，每段结尾不要添加总结性语句"""

    # 如果没有CV内容，提供基本提示
    return "暂无学生简历内容，请确保已正确选择CV版本。"

def _format_motivation_info(request: Any) -> str:
    """格式化申请动机信息"""
    if hasattr(request, 'motivation_profile') and request.motivation_profile:
        motivation = request.motivation_profile.application_motivation or ''
        return f"申请动机: {motivation}" if motivation else "暂无申请动机信息"
    return "暂无申请动机信息"

def _format_career_planning(request: Any) -> str:
    """格式化职业规划信息"""
    if hasattr(request, 'motivation_profile') and request.motivation_profile:
        career_planning = request.motivation_profile.career_planning or ''
        return f"职业规划: {career_planning}" if career_planning else "暂无职业规划信息"
    return "暂无职业规划信息"

def _assemble_chinese_ps_sections(sections: Dict[str, str]) -> str:
    """
    拼接各部分生成完整的中文PS

    Args:
        sections: 各部分内容字典

    Returns:
        完整的中文PS内容
    """
    assembled_content = []

    # 第一部分（Why School）
    if sections.get('section1'):
        assembled_content.append(sections['section1'].strip())
        assembled_content.append("")  # 空行分隔

    # 第二部分（能力展示）
    if sections.get('section2'):
        assembled_content.append(sections['section2'].strip())
        assembled_content.append("")  # 空行分隔

    # 第三部分（未来规划）
    if sections.get('section3'):
        assembled_content.append(sections['section3'].strip())

    return '\n'.join(assembled_content)