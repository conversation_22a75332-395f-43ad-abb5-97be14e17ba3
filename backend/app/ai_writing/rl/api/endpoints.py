from fastapi import APIRouter, Depends, status, UploadFile, File, HTTPException
from fastapi.responses import StreamingResponse
from typing import Dict, Any

from app.core.dependencies import DBSession, get_current_user
from app.models.user import User
from app.ai_writing.rl.core.generator import (
    parse_uploaded_rl_template,
    generate_rl_stream,
    save_rl_content,
    get_rl_document,
    get_client_by_id_hashed_rl
)
from app.ai_writing.schemas.rl import (
    RLParseResponse,
    RLGenerationRequest,
    RLSaveRequest,
    RLSaveResponse
)
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from app.models.client import AIWritingRL, Client
from app.core.data_isolation import DataIsolationFilter

router = APIRouter()

@router.post(
    "/parse",
    response_model=RLParseResponse,
    status_code=status.HTTP_200_OK,
    summary="解析推荐信模板文件",
    description="上传包含推荐人信息的.docx文件，解析并返回结构化的JSON数据。"
)
async def parse_rl_template_endpoint(
    file: UploadFile = File(..., description="要解析的 .docx 格式的推荐人信息收集表格"),
    current_user: User = Depends(get_current_user)
):
    """
    解析上传的推荐信模板文件。
    """
    parsed_data = await parse_uploaded_rl_template(file)
    return RLParseResponse(
        file_name=file.filename,
        parsed_data=parsed_data
    )

@router.post(
    "/generate",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="流式生成RL/推荐信",
    description="根据客户档案和解析后的推荐人信息，流式生成专业的推荐信。"
)
async def generate_rl_stream_endpoint(
    request: RLGenerationRequest,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> StreamingResponse:
    """
    流式生成RL/推荐信。
    🔒 关键安全修复：确保多租户数据隔离
    """
    rl_generator = generate_rl_stream(db, request, current_user)
    return StreamingResponse(rl_generator, media_type="text/markdown") 

@router.post(
    "/save",
    response_model=RLSaveResponse,
    status_code=status.HTTP_200_OK,
    summary="保存RL内容",
    description="将RL的Markdown内容保存到数据库"
)
async def save_rl_endpoint(
    request: RLSaveRequest,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> RLSaveResponse:
    """
    保存RL内容
    
    将用户编辑后的RL Markdown内容保存到数据库中。
    """
    result = await save_rl_content(db, request)
    return result


@router.get(
    "/documents/{document_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="获取RL文档详情",
    description="根据文档ID获取RL文档的详细信息，包括内容和版本信息"
)
async def get_rl_document_endpoint(
    document_id: int,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取RL文档详情

    根据文档ID获取RL文档的详细信息，用于编辑模式加载。
    """
    document = await get_rl_document(db, document_id)
    return document


@router.get(
    "/clients/{client_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="获取客户基本信息",
    description="根据客户哈希ID获取客户的基本信息"
)
async def get_client_info_endpoint(
    client_id: str,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取客户基本信息

    根据客户哈希ID获取客户的基本信息，用于文书写作页面的客户选择。
    """
    client = await get_client_by_id_hashed_rl(db, client_id, current_user)
    return client


@router.delete(
    "/documents/{document_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除RL文档",
    description="硬删除指定的RL文档（需要有该客户的访问权限）"
)
async def delete_rl_document_endpoint(
    document_id: int,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> None:
    """
    删除RL文档（硬删除）

    权限验证：当前用户必须对该文档所属客户有访问权限
    """
    result = await db.execute(
        select(AIWritingRL).options(selectinload(AIWritingRL.client)).where(AIWritingRL.id == document_id)
    )
    rl_doc = result.scalar_one_or_none()
    if not rl_doc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="RL文档不存在")

    client_q = select(Client).where(Client.id == rl_doc.client_id)
    client_q = await DataIsolationFilter.apply_client_filter_async(client_q, current_user, db)
    client_check = await db.execute(client_q)
    if client_check.scalar_one_or_none() is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="无权限访问该客户文档或客户不存在")

    await db.delete(rl_doc)
    await db.commit()