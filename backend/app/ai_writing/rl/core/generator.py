"""
Core logic for the Recommendation Letter (RL) generation module.
"""
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastapi import HTTPException, status, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.ai_writing.schemas.rl import (
    RLGenerationRequest, RecommenderProfile,
    RLSaveRequest, RLSaveResponse
)
from app.ai_writing.utils.client_data import get_client_with_full_relations, filter_client_experiences
from app.ai_writing.utils.llm import generate_rl_content_stream_async
from app.services.token_billing import TokenBillingService
from app.ai_writing.rl.utils.file_parser import parse_rl_document
from app.models.client import Client, AIWritingRL
from datetime import datetime

logger = logging.getLogger(__name__)

async def parse_uploaded_rl_template(file: UploadFile) -> RecommenderProfile:
    """
    Parses the uploaded RL template file and returns structured data.
    
    Args:
        file: The uploaded .docx file.
        
    Returns:
        A RecommenderProfile instance with the parsed data.
    """
    logger.info(f"开始解析推荐信模板文件: {file.filename}")
    try:
        profile = await parse_rl_document(file)
        logger.info(f"成功解析文件: {file.filename}")
        return profile
    except HTTPException as http_exc:
        # Re-raise HTTP exceptions from the parser to be handled by FastAPI
        raise http_exc
    except Exception as e:
        logger.error(f"解析推荐信模板时发生未知错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while parsing the file: {e}"
        )

async def generate_rl_stream(
    db: AsyncSession,
    request: RLGenerationRequest,
    current_user = None
) -> AsyncGenerator[str, None]:
    """
    Generates a recommendation letter stream based on client and recommender info.
    Supports multi-tenant data isolation.
    """
    # 1. Validate client and get full profile
    client = await get_client_with_full_relations(db, request.client_id, current_user)
    if not client:
        yield "Error: Client not found"
        return

    # 2. 移除经历筛选，因为根据新需求，生成RL的prompt不再需要学生的详细经历列表
    # filtered_data = await filter_client_experiences(client, request)
    
    # 3. Build the client data dictionary for the prompt
    client_data_dict = _build_rl_client_data_dict(client)
    
    # 4. Convert Pydantic model to dict for the prompt
    recommender_profile_dict = request.recommender_profile.model_dump()

    # 5. 在流式生成前获取客户名称，避免后续访问懒加载属性的问题
    client_name = client.name  # 在流式生成前就访问，确保在正确的异步上下文中
    recommender_first_name = request.recommender_profile.first_name_en or "Unknown"
    
    # 6. Call the LLM to stream the RL content（带积分与token追踪）
    logger.info(f"开始为客户 {client_name} (推荐人: {recommender_first_name}) 流式生成RL")
    request_id = TokenBillingService.generate_request_id()
    async for content_chunk in generate_rl_content_stream_async(
        client_data=client_data_dict,
        recommender_profile=recommender_profile_dict,
        word_limit=request.word_limit,
        additional_info=request.additional_info or "",
        user=current_user,
        db=db,
        service_type="ai_writing",
        operation_type="rl_generation",
        request_id=request_id
    ):
        yield content_chunk
    
    logger.info(f"成功为客户 {client_name} 流式生成RL")


def _build_rl_client_data_dict(
    client: Client
) -> Dict[str, Any]:
    """
    Converts client object into a dictionary for the LLM prompt.
    """
    return {
        'name': client.name,
        'gender': client.gender
    } 

async def save_rl_content(
    db: AsyncSession,
    request: RLSaveRequest
) -> RLSaveResponse:
    """
    保存RL内容到数据库
    """
    # 1. 验证客户是否存在
    client_query = select(Client).where(Client.id_hashed == request.client_id)
    result = await db.execute(client_query)
    client = result.scalar_one_or_none()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    try:
        # 始终创建新记录
        new_rl = AIWritingRL(
            client_id=client.id,
            content_markdown=request.content_markdown,
            version_name=request.version_name,
            recommender_name=request.recommender_name
        )
        db.add(new_rl)
        logger.info(f"为客户 {client.name} 创建新的RL记录，版本: {request.version_name}")
        
        await db.commit()
        
        return RLSaveResponse(
            status="success",
            message="RL内容保存成功",
            client_name=client.name,
            saved_at=datetime.now().isoformat()
        )
        
    except Exception as e:
        await db.rollback()
        logger.error(f"保存RL失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="保存RL内容失败"
        )


async def get_rl_document(
    db: AsyncSession,
    document_id: int
) -> Dict[str, Any]:
    """
    获取单个RL文档的详细信息
    """
    try:
        # 查询RL文档及其关联的客户信息
        query = select(AIWritingRL).options(
            selectinload(AIWritingRL.client)
        ).where(AIWritingRL.id == document_id)

        result = await db.execute(query)
        rl_document = result.scalar_one_or_none()

        if not rl_document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="推荐信文档不存在"
            )

        return {
            "id": rl_document.id,
            "version_name": rl_document.version_name,
            "recommender_name": rl_document.recommender_name,
            "content_markdown": rl_document.content_markdown,
            "client_id": rl_document.client.id_hashed,
            "client_name": rl_document.client.name,
            "created_at": rl_document.created_at.isoformat() if rl_document.created_at else None,
            "updated_at": rl_document.updated_at.isoformat() if rl_document.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取RL文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取RL文档失败"
        )


async def get_client_by_id_hashed_rl(
    db: AsyncSession,
    client_id: str,
    current_user
) -> Dict[str, Any]:
    """
    根据客户哈希ID获取客户基本信息（RL模块专用）
    支持多租户数据隔离
    """
    from app.core.data_isolation import DataIsolationFilter

    try:
        query = select(Client).where(Client.id_hashed == client_id)

        # 应用身份隔离过滤（支持Owner放宽）
        query = await DataIsolationFilter.apply_client_filter_async(query, current_user, db)
        result = await db.execute(query)
        client = result.scalar_one_or_none()

        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="客户不存在或无权限访问"
            )

        return {
            "id_hashed": client.id_hashed,
            "name": client.name,
            "phone": client.phone,
            "email": client.email,
            "location": client.location,
            "created_at": client.created_at.isoformat() if client.created_at else None,
            "updated_at": client.updated_at.isoformat() if client.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取客户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取客户信息失败"
        )