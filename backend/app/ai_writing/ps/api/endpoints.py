from fastapi import APIRouter, status, HTTPException, UploadFile, File, Depends
from fastapi.responses import StreamingResponse
from typing import Dict, Any, AsyncGenerator

from app.core.dependencies import DBSession, get_current_user
from app.models.user import User
from app.ai_writing.ps.core.generator import (
    generate_ps_stream,
    get_client_programs_data,
    get_client_module_data,
    save_ps_content,
    get_client_cvs_for_ps
)
from app.ai_writing.ps.utils.file_parser import parse_ps_document
from app.ai_writing.schemas.ps import (
    PSGenerationRequest,
    ClientProgramsData,
    ClientModuleData,
    PSParseResponse,
    PSSaveRequest,
    PSSaveResponse
)
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from app.models.client import AIWritingPS, Client
from app.core.data_isolation import DataIsolationFilter

router = APIRouter()

@router.post(
    "/generate",
    response_class=StreamingResponse,
    status_code=status.HTTP_200_OK,
    summary="流式生成PS/个人陈述",
    description="根据客户档案信息和申请目标流式生成专业的PS/个人陈述"
)
async def generate_ps_stream_endpoint(
    request: PSGenerationRequest,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> StreamingResponse:
    """
    流式生成PS/个人陈述

    根据客户信息和申请目标流式生成专业的个人陈述。
    🔒 关键安全修复：确保多租户数据隔离
    """
    ps_generator = generate_ps_stream(db, request, current_user)
    return StreamingResponse(ps_generator, media_type="text/markdown")

# 客户列表接口已移除，前端统一使用主客户API (/api/clients/)
# 这样与CV和RL模块保持一致，避免认证问题

@router.get(
    "/clients/{client_id}/programs",
    response_model=ClientProgramsData,
    status_code=status.HTTP_200_OK,
    summary="获取客户定校书数据",
    description="获取指定客户的定校书数据，用于前端显示申请目标选项"
)
async def get_client_programs(
    client_id: str,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> ClientProgramsData:
    """
    获取客户定校书数据

    根据客户ID获取该客户的定校书数据，包括申请院校、学位、专业选项，
    用于前端显示和用户选择申请目标。
    🔒 关键安全修复：确保多租户数据隔离
    """
    programs_data = await get_client_programs_data(db, client_id, current_user)
    return ClientProgramsData(**programs_data)

@router.get(
    "/clients/{client_id}/modules",
    response_model=ClientModuleData,
    status_code=status.HTTP_200_OK,
    summary="获取客户详细模块数据",
    description="获取指定客户的所有模块详细数据，用于前端显示和选择经历"
)
async def get_client_modules(
    client_id: str,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> ClientModuleData:
    """
    获取客户详细模块数据

    根据客户ID获取该客户的所有教育经历、工作经历、学术经历等详细信息，
    用于前端显示和用户选择特定的经历条目。
    🔒 关键安全修复：确保多租户数据隔离
    """
    module_data = await get_client_module_data(db, client_id, current_user)
    return ClientModuleData(**module_data)

@router.post(
    "/parse",
    response_model=PSParseResponse,
    status_code=status.HTTP_200_OK,
    summary="解析PS相关文档",
    description="上传包含申请动机和职业规划信息的.docx文件，解析并返回结构化的JSON数据。"
)
async def parse_ps_document_endpoint(
    file: UploadFile = File(..., description="包含申请动机和职业规划信息的.docx文件"),
    current_user: User = Depends(get_current_user)
) -> PSParseResponse:
    """
    解析PS相关文档

    上传.docx文件，使用LLM解析其中的申请动机和职业规划信息，
    返回结构化的数据供PS生成使用。
    """
    motivation_profile = await parse_ps_document(file)
    return PSParseResponse(
        motivation_profile=motivation_profile,
        message="文档解析成功"
    )

@router.post(
    "/save",
    response_model=PSSaveResponse,
    status_code=status.HTTP_200_OK,
    summary="保存PS内容",
    description="将PS的Markdown内容保存到数据库"
)
async def save_ps_endpoint(
    request: PSSaveRequest,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> PSSaveResponse:
    """
    保存PS内容

    将用户编辑后的PS Markdown内容保存到数据库中。
    target_major字段会自动组合为"院校 - 学位 - 专业"的格式。
    """
    result = await save_ps_content(db, request, current_user)
    return PSSaveResponse(**result)

@router.get(
    "/documents/{document_id}",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="获取PS文档详情",
    description="根据文档ID获取PS文档的详细信息，包括内容和版本信息"
)
async def get_ps_document_endpoint(
    document_id: int,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取PS文档详情

    根据文档ID获取PS文档的详细信息，用于编辑模式加载。
    """
    from app.ai_writing.ps.core.generator import get_ps_document
    document = await get_ps_document(db, document_id)
    return document


@router.delete(
    "/documents/{document_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除PS文档",
    description="硬删除指定的PS文档（需要有该客户的访问权限）"
)
async def delete_ps_document_endpoint(
    document_id: int,
    db: DBSession,
    current_user: User = Depends(get_current_user)
) -> None:
    """
    删除PS文档（硬删除）

    权限验证：当前用户必须对该文档所属客户有访问权限
    """
    result = await db.execute(
        select(AIWritingPS).options(selectinload(AIWritingPS.client)).where(AIWritingPS.id == document_id)
    )
    ps_doc = result.scalar_one_or_none()
    if not ps_doc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="PS文档不存在")

    client_q = select(Client).where(Client.id == ps_doc.client_id)
    client_q = await DataIsolationFilter.apply_client_filter_async(client_q, current_user, db)
    client_check = await db.execute(client_q)
    if client_check.scalar_one_or_none() is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="无权限访问该客户文档或客户不存在")

    await db.delete(ps_doc)
    await db.commit()

@router.get(
    "/client-cvs/{client_id}",
    status_code=status.HTTP_200_OK,
    summary="获取客户CV版本列表",
    description="获取指定客户的所有CV版本，用于PS生成时手动选择"
)
async def get_client_cvs_endpoint(
    client_id: str,
    db: DBSession,
    current_user: User = Depends(get_current_user)
):
    """
    获取客户CV版本列表

    根据客户ID获取该客户的所有CV版本，用于PS生成时手动选择合适的CV版本。
    🔒 关键安全修复：确保多租户数据隔离
    """
    result = await get_client_cvs_for_ps(db, client_id, current_user)
    return result