import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload, joinedload

from app.models.client import Client, ClientProgram, AIWritingPS, AIWritingCV
from app.ai_selection.db.models import AISelectionProgram
from app.ai_writing.utils.llm import generate_ps_content_stream_async
from app.services.token_billing import TokenBillingService
from app.ai_writing.utils.client_data import (
    get_client_with_full_relations,
    filter_client_experiences,
    get_all_clients_with_summaries
)
from app.ai_writing.schemas.ps import (
    PSGenerationRequest,
    PSGenerationResponse,
    ClientProfileSummary,
    ClientProgramsData,
    ClientBasicInfo,
    ProgramOption,
    ClientModuleData,
    PSSaveRequest
)

logger = logging.getLogger(__name__)

async def generate_ps_stream(
    db: AsyncSession,
    request: PSGenerationRequest,
    current_user = None
) -> AsyncGenerator[str, None]:
    """
    根据客户信息和申请目标流式生成PS（基于CV数据）

    Args:
        db: 数据库会话
        request: PS生成请求
        current_user: 当前用户（用于数据隔离）

    Returns:
        生成的PS响应流
    """
    # 1. 验证客户是否存在并获取完整信息
    client = await get_client_with_full_relations(db, request.client_id, current_user)
    if not client:
        yield "Error: Client not found"
        return

    # 2. 根据CV ID查找CV数据
    if not hasattr(request, 'cv_id') or not request.cv_id:
        yield "Error: 未指定CV版本ID"
        return

    query = select(AIWritingCV).where(
        AIWritingCV.client_id == client.id,
        AIWritingCV.id == request.cv_id
    )

    result = await db.execute(query)
    cv_record = result.scalars().first()

    if not cv_record:
        yield f"Error: 未找到指定的CV版本（ID: {request.cv_id}）"
        return

    # 3. 基于CV数据构建客户数据字典
    client_data = build_client_data_dict_from_cv(client, request, cv_record)

    # 4. 在流式生成前获取客户名称，避免后续访问懒加载属性的问题
    client_name = client.name  # 在流式生成前就访问，确保在正确的异步上下文中
    cv_version_name = cv_record.version_name

    # 5. 调用LLM流式生成PS内容（带积分与token追踪）
    logger.info(f"开始为客户 {client_name} 基于CV「{cv_version_name}」流式生成PS")
    request_id = TokenBillingService.generate_request_id()
    async for chunk in generate_ps_content_stream_async(
        client_data=client_data,
        request=request,
        user=current_user,
        db=db,
        service_type="ai_writing",
        operation_type="ps_generation",
        request_id=request_id
    ):
        yield chunk

    logger.info(f"成功为客户 {client_name} 基于CV流式生成PS")

def build_client_data_dict_from_cv(
    client: Client,
    request: PSGenerationRequest,
    cv_record: AIWritingCV
) -> Dict[str, Any]:
    """
    基于CV数据构建客户数据字典

    Args:
        client: 客户对象
        request: PS生成请求
        cv_record: 匹配的CV记录

    Returns:
        客户数据字典
    """
    # 基本信息
    target_major = f"{request.target_school} - {request.degree} - {request.major}"
    client_data = {
        'name': client.name,
        'email': client.email,
        'phone': client.phone,
        'location': client.location,
        'cv_content': cv_record.content_markdown,
        'cv_version': cv_record.version_name,
        'target_major': target_major
    }

    # 这里我们将CV内容作为一个整体传递给LLM，让LLM从中提取相关信息
    # 而不是尝试解析Markdown结构

    logger.info(f"基于CV「{cv_record.version_name}」构建客户数据，目标专业: {target_major}")

    return client_data

def build_client_data_dict(
    client: Client,
    request: PSGenerationRequest,
    filtered_data: Dict[str, List[Any]]
) -> Dict[str, Any]:
    """
    将客户信息和申请目标转换为字典格式
    """
    return {
        # 客户基本信息
        'name': client.name,
        'email': client.email,
        'phone': client.phone,
        'location': client.location,
        
        # 申请目标
        'target_school': request.target_school,
        'degree': request.degree,
        'major': request.major,
        'school_requirements': request.school_requirements,
        'word_limit': request.word_limit,
        'paragraph_setting': request.paragraph_setting,
        
        # 客户经历数据（使用筛选后的数据）
        'education': [edu.to_dict() for edu in filtered_data['education']],
        'academic': [academic.to_dict() for academic in filtered_data['academic']],
        'work': [work.to_dict() for work in filtered_data['work']],
        'activities': [activity.to_dict() for activity in filtered_data['activities']],
        'awards': [award.to_dict() for award in filtered_data['awards']],
        'skills': [skill.to_dict() for skill in filtered_data['skills']],
        'language_scores': [lang.to_dict() for lang in filtered_data['language_scores']],
        'thoughts': [thought.to_dict() for thought in client.thoughts]  # thoughts不需要筛选，全部包含
    }

async def get_client_profiles_summary(db: AsyncSession, current_user) -> List[Dict[str, Any]]:
    """
    获取所有客户的档案摘要，用于前端选择
    支持多租户数据隔离
    """
    clients = await get_all_clients_with_summaries(db, current_user)
    
    profiles = []
    for client in clients:
        profiles.append({
            'id_hashed': client.id_hashed,
            'name': client.name,
            'education_count': len(client.education),
            'academic_count': len(client.academic),
            'work_count': len(client.work),
            'activity_count': len(client.activities),
            'programs_count': len(client.client_programs)
        })
    
    return profiles

async def get_client_programs_data(
    db: AsyncSession,
    client_id: str,
    current_user = None
) -> Dict[str, Any]:
    """
    获取客户的定校书数据，用于前端显示申请目标选项
    支持多租户数据隔离
    """
    # 1. 获取客户基本信息
    client = await get_client_with_full_relations(db, client_id, current_user)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )
    
    # 2. 获取客户的定校书数据 (已通过get_client_with_full_relations预加载)
    client_programs = client.client_programs
    
    # 3. 整理定校书数据
    school_options = set()
    degree_options = set()
    major_options = set()
    program_combinations = []
    
    for cp in client_programs:
        program = cp.ai_selection_program
        if program:
            school_name = program.school_name_cn or program.school_name_en or "未知学校"
            degree = program.degree or "未知学位"
            program_name = program.program_name_cn or program.program_name_en or "未知专业"
            
            school_options.add(school_name)
            degree_options.add(degree)
            major_options.add(program_name)
            
            program_combinations.append({
                'school_name': school_name,
                'degree': degree,
                'program_name': program_name,
                'value': f"{school_name}|{degree}|{program_name}"
            })
    
    return {
        'client_info': {
            'id_hashed': client.id_hashed,
            'name': client.name,
            'email': client.email,
            'phone': client.phone,
            'location': client.location
        },
        'school_options': sorted(list(school_options)),
        'degree_options': sorted(list(degree_options)),
        'major_options': sorted(list(major_options)),
        'program_combinations': program_combinations
    }

async def get_client_module_data(
    db: AsyncSession,
    client_id: str,
    current_user = None
) -> Dict[str, Any]:
    """
    获取客户的详细模块数据，用于前端显示和选择经历
    支持多租户数据隔离
    """
    client = await get_client_with_full_relations(db, client_id, current_user)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    return {
        'client_info': {
            'id_hashed': client.id_hashed,
            'name': client.name,
            'email': client.email,
            'phone': client.phone,
            'location': client.location
        },
        'education': [edu.to_dict() for edu in client.education],
        'academic': [academic.to_dict() for academic in client.academic],
        'work': [work.to_dict() for work in client.work],
        'activities': [activity.to_dict() for activity in client.activities],
        'awards': [award.to_dict() for award in client.awards],
        'skills': [skill.to_dict() for skill in client.skills],
        'language_scores': [lang.to_dict() for lang in client.language_scores]
    }

async def save_ps_content(
    db: AsyncSession,
    request: 'PSSaveRequest',
    current_user
) -> Dict[str, Any]:
    """
    保存PS内容到数据库

    Args:
        db: 数据库会话
        request: PS保存请求

    Returns:
        保存结果
    """
    # 1. 验证客户是否存在（带数据隔离，支持Owner放宽）
    client = await get_client_with_full_relations(db, request.client_id, current_user)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 2. 构建target_major字段（院校-学位-专业）
    target_major = f"{request.target_school} - {request.degree} - {request.major}"

    # 3. 创建PS记录
    ps_record = AIWritingPS(
        client_id=client.id,
        version_name=request.version_name,
        target_major=target_major,
        content_markdown=request.content,
        cv_id=request.cv_id,  # 记录使用的CV版本ID
        word_limit=request.word_limit,
        paragraph_setting=request.paragraph_setting
    )

    # 4. 保存到数据库
    db.add(ps_record)
    await db.commit()
    await db.refresh(ps_record)

    logger.info(f"成功保存PS内容，客户: {client.name}, 版本: {request.version_name}, ID: {ps_record.id}")

    return {
        "status": "success",
        "message": "PS保存成功",
        "ps_id": ps_record.id,
        "version_name": request.version_name
    }

async def get_client_cvs_for_ps(
    db: AsyncSession,
    client_id: str,
    current_user = None
) -> Dict[str, Any]:
    """
    获取客户的所有CV版本列表

    Args:
        db: 数据库会话
        client_id: 客户哈希ID
        current_user: 当前用户（用于数据隔离）

    Returns:
        CV版本列表
    """
    # 1. 验证客户是否存在
    client = await get_client_with_full_relations(db, client_id, current_user)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="客户不存在"
        )

    # 2. 查询该客户的所有CV记录
    query = select(AIWritingCV).where(
        AIWritingCV.client_id == client.id
    ).order_by(AIWritingCV.updated_at.desc())

    result = await db.execute(query)
    cv_records = result.scalars().all()

    # 3. 转换为前端需要的格式
    cvs = []
    for cv_record in cv_records:
        cvs.append({
            "id": cv_record.id,
            "version_name": cv_record.version_name,
            "created_at": cv_record.created_at.isoformat() if cv_record.created_at else "",
            "updated_at": cv_record.updated_at.isoformat() if cv_record.updated_at else ""
        })

    logger.info(f"获取客户CV列表，客户: {client.name}, CV数量: {len(cvs)}")

    return {
        "status": "success",
        "cvs": cvs,
        "client_name": client.name,
        "total_count": len(cvs)
    }

async def get_ps_document(db: AsyncSession, document_id: int) -> Dict[str, Any]:
    """
    获取PS文档详情

    根据文档ID获取PS文档的详细信息，用于编辑模式加载。
    """
    try:
        # 查询PS文档
        query = select(AIWritingPS).where(AIWritingPS.id == document_id)
        result = await db.execute(query)
        ps_doc = result.scalar_one_or_none()

        if not ps_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PS文档不存在"
            )

        return {
            "id": ps_doc.id,
            "client_id": ps_doc.client_id,
            "version_name": ps_doc.version_name,
            "target_major": ps_doc.target_major,
            "content_markdown": ps_doc.content_markdown,
            "content": ps_doc.content_markdown,  # 兼容字段
            "cv_id": ps_doc.cv_id,  # 返回关联的CV ID
            "created_at": ps_doc.created_at.isoformat() if ps_doc.created_at else None,
            "updated_at": ps_doc.updated_at.isoformat() if ps_doc.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取PS文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取PS文档失败"
        )