"""
File parser for the Personal Statement (PS) module.
"""
import docx
from fastapi import UploadFile, HTTPException, status
from io import BytesIO
import logging

from app.ai_writing.schemas.ps import ApplicationMotivationProfile
from app.ai_writing.utils.llm import parse_text_to_ps_profile_async

logger = logging.getLogger(__name__)

async def parse_ps_document(file: UploadFile) -> ApplicationMotivationProfile:
    """
    Parses an uploaded document (.docx) to extract application motivation and career planning information.

    Args:
        file: The uploaded file from the FastAPI endpoint.

    Returns:
        An ApplicationMotivationProfile Pydantic model instance with the extracted data.
    
    Raises:
        HTTPException: If the file type is unsupported or parsing fails.
    """
    if file.filename.endswith(".docx"):
        content = await file.read()
        text = await _read_docx(content)
    else:
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail="Unsupported file type. Please upload a .docx file."
        )

    if not text.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The uploaded document is empty or contains no text."
        )

    try:
        # Call the LLM to parse the extracted text
        parsed_profile = await parse_text_to_ps_profile_async(text)
        return parsed_profile
    except Exception as e:
        logger.error(f"Failed to parse PS document content with LLM: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse document content. Error: {e}"
        )

async def _read_docx(content: bytes) -> str:
    """
    Reads the text content from a .docx file.

    Args:
        content: The byte content of the .docx file.

    Returns:
        The extracted text as a single string.
    """
    try:
        doc = docx.Document(BytesIO(content))
        full_text = [para.text for para in doc.paragraphs]
        return "\n".join(full_text)
    except Exception as e:
        logger.error(f"Error reading .docx file: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not read the .docx file."
        )
