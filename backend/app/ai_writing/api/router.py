from fastapi import APIRouter

from app.ai_writing.cv.api.router import cv_router
from app.ai_writing.ps.api.router import ps_router
from app.ai_writing.rl.api.router import rl_router

api_router = APIRouter()

# 包含CV功能路由
api_router.include_router(
    cv_router,
    prefix="/cv",
    tags=["AI Writing - CV"]
)

# 包含PS功能路由
api_router.include_router(
    ps_router,
    prefix="/ps", 
    tags=["AI Writing - PS"]
)

# 包含RL功能路由
api_router.include_router(
    rl_router,
    prefix="/rl",
    tags=["AI Writing - RL"] 
) 