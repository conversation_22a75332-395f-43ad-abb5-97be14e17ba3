from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any

class ApplicationMotivationProfile(BaseModel):
    """申请动机和职业规划档案模型"""
    application_motivation: Optional[str] = Field(None, description="申请动机")
    career_planning: Optional[str] = Field(None, description="职业规划")

class PSGenerationRequest(BaseModel):
    """PS 生成请求模型"""
    client_id: str = Field(..., description="客户哈希ID")
    cv_id: int = Field(..., description="选择的CV版本ID")
    target_school: str = Field(..., description="申请院校")
    degree: str = Field(..., description="申请学位：bachelor、master、phd")
    major: str = Field(..., description="申请专业")
    program_objectives: Optional[str] = Field(None, description="专业描述/培养目标")
    courses: Optional[str] = Field(None, description="专业课程信息")
    school_requirements: Optional[str] = Field(None, description="院校要求及其他说明")
    word_limit: int = Field(0, description="目标字数：0=不限制, 其他正整数=具体字数（如500、1000、1500等）")
    paragraph_setting: int = Field(0, description="段落设置：0=不限制, 1=智能分段, 2=4段, 3=5段, 4=6段, 5=7段, 6=8段")

    # 经历选择 - 用户可以选择包含哪些经历
    selected_education_ids: Optional[List[int]] = Field([], description="选择的教育经历ID列表")
    selected_academic_ids: Optional[List[int]] = Field([], description="选择的学术经历ID列表")
    selected_work_ids: Optional[List[int]] = Field([], description="选择的工作经历ID列表")
    selected_activity_ids: Optional[List[int]] = Field([], description="选择的课外活动ID列表")
    selected_award_ids: Optional[List[int]] = Field([], description="选择的奖项ID列表")
    selected_skill_ids: Optional[List[int]] = Field([], description="选择的技能ID列表")
    selected_language_score_ids: Optional[List[int]] = Field([], description="选择的语言成绩ID列表")

    # 申请动机和职业规划 - 从上传文档中解析得到
    motivation_profile: Optional[ApplicationMotivationProfile] = Field(None, description="申请动机和职业规划信息")

class PSGenerationResponse(BaseModel):
    """PS 生成响应模型"""
    status: str = "success"
    ps_content: str = Field(..., description="生成的PS内容（HTML格式）")
    client_name: str = Field(..., description="客户姓名")
    target_school: str = Field(..., description="申请院校")
    degree: str = Field(..., description="申请学位")
    major: str = Field(..., description="申请专业")

class ClientProfileSummary(BaseModel):
    """客户档案摘要模型（用于前端选择）"""
    id_hashed: str = Field(..., description="客户哈希ID")
    name: str = Field(..., description="客户姓名")
    education_count: int = Field(0, description="教育经历数量")
    academic_count: int = Field(0, description="学术经历数量")
    work_count: int = Field(0, description="工作经历数量")
    activity_count: int = Field(0, description="课外活动数量")
    programs_count: int = Field(0, description="定校书数量")

class ClientBasicInfo(BaseModel):
    """客户基本信息模型"""
    id_hashed: str = Field(..., description="客户哈希ID")
    name: str = Field(..., description="客户姓名")
    email: Optional[str] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="电话")
    location: Optional[str] = Field(None, description="位置")

class ProgramOption(BaseModel):
    """定校书项目选项模型"""
    school_name: str = Field(..., description="学校名称")
    degree: str = Field(..., description="申请学位")
    program_name: str = Field(..., description="专业名称")
    value: str = Field(..., description="选项值，格式：学校|学位|专业")

class ClientProgramsData(BaseModel):
    """客户定校书数据模型"""
    client_info: ClientBasicInfo = Field(..., description="客户基本信息")
    school_options: List[str] = Field([], description="申请院校选项")
    degree_options: List[str] = Field([], description="申请学位选项")
    major_options: List[str] = Field([], description="申请专业选项")
    program_combinations: List[ProgramOption] = Field([], description="项目组合选项")

class PSParseResponse(BaseModel):
    """PS文档解析响应模型"""
    status: str = "success"
    motivation_profile: ApplicationMotivationProfile = Field(..., description="解析出的申请动机和职业规划信息")
    message: str = Field("文档解析成功", description="响应消息")

class ClientModuleData(BaseModel):
    """客户模块数据模型（用于经历选择）"""
    client_info: ClientBasicInfo = Field(..., description="客户基本信息")
    education: List[Dict[str, Any]] = Field([], description="教育经历列表")
    academic: List[Dict[str, Any]] = Field([], description="学术经历列表")
    work: List[Dict[str, Any]] = Field([], description="工作经历列表")
    activities: List[Dict[str, Any]] = Field([], description="课外活动列表")
    awards: List[Dict[str, Any]] = Field([], description="奖项列表")
    skills: List[Dict[str, Any]] = Field([], description="技能列表")
    language_scores: List[Dict[str, Any]] = Field([], description="语言成绩列表")

class PSSaveRequest(BaseModel):
    """PS保存请求模型"""
    client_id: str = Field(..., description="客户哈希ID")
    target_school: str = Field(..., description="申请院校")
    major: str = Field(..., description="申请专业")
    degree: str = Field(..., description="申请学位")
    content: str = Field(..., description="PS内容（Markdown格式）")
    version_name: str = Field(..., description="版本名称")
    cv_id: Optional[int] = Field(None, description="关联的CV版本ID")
    word_limit: Optional[int] = Field(None, description="字数限制")
    paragraph_setting: Optional[int] = Field(None, description="段落设置")

class PSSaveResponse(BaseModel):
    """PS保存响应模型"""
    status: str = "success"
    message: str = Field("PS保存成功", description="响应消息")
    ps_id: int = Field(..., description="保存的PS记录ID")
    version_name: str = Field(..., description="版本名称")

class CVMatchRequest(BaseModel):
    """CV匹配请求模型"""
    client_id: str = Field(..., description="客户哈希ID")
    target_school: str = Field(..., description="申请院校")
    degree: str = Field(..., description="申请学位")
    major: str = Field(..., description="申请专业")

class CVMatchInfo(BaseModel):
    """匹配的CV信息模型"""
    cv_id: int = Field(..., description="CV记录ID")
    version_name: str = Field(..., description="CV版本名称")
    target_major: str = Field(..., description="目标专业")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")

class CVMatchResponse(BaseModel):
    """CV匹配响应模型"""
    status: str = Field(..., description="匹配状态：success/not_found")
    message: str = Field(..., description="响应消息")
    cv_info: Optional[CVMatchInfo] = Field(None, description="匹配的CV信息")
    target_major: str = Field(..., description="查询的目标专业")