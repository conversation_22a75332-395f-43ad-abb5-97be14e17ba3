"""
有效客户管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from typing import Optional, List
from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.crm.core.crm_service import CRMService
from app.crm.models import CRMClient, CRMClientStaff
from app.crm.schemas import (
    CRMClientCreate, CRMClientUpdate, CRMClientResponse, CRMClientListResponse,
    CRMClientFilter, CRMStatistics, AssignmentStatus, IntentLevel, LifecycleStatus,
    ValidCustomerListQuery, ValidCustomerStatisticsQuery
)
from app.core.organization_permissions import OrganizationPermissionService
from datetime import date
from app.core.data_isolation import DataIsolationFilter

router = APIRouter()


def build_valid_list_query(
    organization_id: int = Query(..., description="组织ID"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    search_keyword: Optional[str] = Query(None, description="搜索关键词"),
    intent_level: Optional[IntentLevel] = Query(None, description="意向等级"),
    assignment_status: Optional[AssignmentStatus] = Query(None, description="分配状态"),
    market_staff_user_id: Optional[int] = Query(None, description="市场人员ID"),
    sales_staff_user_id: Optional[int] = Query(None, description="销售人员ID"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    show_all: Optional[bool] = Query(False, description="是否显示所有客户（仅管理员或组织owner）"),
    is_valid: Optional[bool] = Query(None, description="是否为有效客资"),
) -> ValidCustomerListQuery:
    return ValidCustomerListQuery(
        organization_id=organization_id,
        page=page,
        size=size,
        search_keyword=search_keyword,
        intent_level=intent_level,
        assignment_status=assignment_status,
        market_staff_user_id=market_staff_user_id,
        sales_staff_user_id=sales_staff_user_id,
        start_date=start_date,
        end_date=end_date,
        show_all=show_all,
        is_valid=is_valid,
    )


def build_valid_stats_query(
    organization_id: int = Query(..., description="组织ID"),
    search_keyword: Optional[str] = Query(None, description="搜索关键词"),
    intent_level: Optional[IntentLevel] = Query(None, description="意向等级"),
    assignment_status: Optional[AssignmentStatus] = Query(None, description="分配状态"),
    market_staff_user_id: Optional[int] = Query(None, description="市场人员ID"),
    sales_staff_user_id: Optional[int] = Query(None, description="销售人员ID"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    show_all: Optional[bool] = Query(False, description="是否显示所有客户（仅管理员或组织owner）"),
    is_valid: Optional[bool] = Query(None, description="是否为有效客资"),
) -> ValidCustomerStatisticsQuery:
    return ValidCustomerStatisticsQuery(
        organization_id=organization_id,
        search_keyword=search_keyword,
        intent_level=intent_level,
        assignment_status=assignment_status,
        market_staff_user_id=market_staff_user_id,
        sales_staff_user_id=sales_staff_user_id,
        start_date=start_date,
        end_date=end_date,
        show_all=show_all,
        is_valid=is_valid,
    )


@router.get("/", response_model=CRMClientListResponse, summary="获取有效客户列表")
async def list_valid_customers(
    query: ValidCustomerListQuery = Depends(build_valid_list_query),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取有效客户列表，支持筛选和分页
    有效客户是lifecycle_status为'valid'的客户
    
    权限控制：
    - 系统管理员(admin/dev)：可以查看所有客户或指定销售人员的客户
    - 组织owner：可以查看所有客户或指定销售人员的客户
    - 普通用户：只能查看分配给自己的客户
    """
    
    # 数据隔离：组织身份与组织ID校验
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可访问CRM数据")
    if query.organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权访问其他组织数据")

    # 权限控制：根据用户角色和组织权限决定过滤逻辑
    effective_sales_staff_user_id = query.sales_staff_user_id
    
    # 检查用户是否为组织owner
    is_organization_owner = await OrganizationPermissionService.check_organization_owner(
        db, current_user, query.organization_id
    )
    
    # 权限控制逻辑
    if current_user.role in ['admin', 'dev'] or is_organization_owner:
        # 系统管理员或组织owner可以查看所有客户或指定销售人员的客户
        if query.show_all:
            # 明确要求显示所有客户，不过滤销售人员
            effective_sales_staff_user_id = query.sales_staff_user_id  # 可能为None，表示所有客户
        else:
            # 默认显示分配给自己的客户
            effective_sales_staff_user_id = current_user.id
    else:
        # 普通用户只能看分配给自己的客户，忽略前端传递的参数
        effective_sales_staff_user_id = current_user.id
        
    # 权限控制逻辑完成
    
    # 构建筛选条件，显示生命周期为valid的客户
    filters = CRMClientFilter(
        search_keyword=query.search_keyword,
        intent_level=query.intent_level,
        assignment_status=query.assignment_status,
        lifecycle_status=LifecycleStatus.VALID,  # 强制筛选生命周期为有效的客户
        market_staff_user_id=query.market_staff_user_id,
        sales_staff_user_id=effective_sales_staff_user_id,  # 使用经过权限控制的销售人员ID
        start_date=query.start_date,
        end_date=query.end_date,
        is_valid=query.is_valid  # 根据客资有效性筛选
    )
    
    clients, total = await CRMService.list_crm_clients(
        db=db,
        organization_id=query.organization_id,
        filters=filters,
        page=query.page,
        size=query.size
    )
    
    # 计算总页数
    pages = (total + query.size - 1) // query.size
    
    # 构建响应数据，包含人员分配信息（单选）
    response_items = []
    for client in clients:
        # 从staff_assignments中提取单个人员ID（每个角色只取第一个）
        market_staff_assignment = next(
            (assignment for assignment in client.staff_assignments if assignment.role == 'market'), 
            None
        )
        sales_staff_assignment = next(
            (assignment for assignment in client.staff_assignments if assignment.role == 'sales'), 
            None
        )
        document_writer_assignment = next(
            (assignment for assignment in client.staff_assignments if assignment.role == 'document_writer'), 
            None
        )
        submission_assignment = next(
            (assignment for assignment in client.staff_assignments if assignment.role == 'submission'), 
            None
        )
        
        # 创建响应对象
        client_data = CRMClientResponse.from_orm(client)
        client_data.market_staff_user_id = market_staff_assignment.user_id if market_staff_assignment else None
        client_data.sales_staff_user_id = sales_staff_assignment.user_id if sales_staff_assignment else None
        client_data.document_writer_staff_user_id = document_writer_assignment.user_id if document_writer_assignment else None
        client_data.submission_staff_user_id = submission_assignment.user_id if submission_assignment else None
        
        response_items.append(client_data)
    
    # 返回客户列表数据
    
    return CRMClientListResponse(
        total=total,
        items=response_items,
        page=query.page,
        size=query.size,
        pages=pages
    )


@router.get("/{client_id}", response_model=CRMClientResponse, summary="获取有效客户详情")
async def get_valid_customer(
    client_id: int,
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取单个有效客户的详细信息
    """
    # 数据隔离
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可访问CRM数据")
    if organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权访问其他组织数据")

    db_client = await CRMService.get_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id
    )
    
    if not db_client:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 验证是否为有效客户
    if db_client.lifecycle_status != LifecycleStatus.VALID:
        raise HTTPException(status_code=400, detail="该客户不是有效客户")
    
    # 构建包含人员分配信息的响应
    market_staff_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'market'), 
        None
    )
    sales_staff_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'sales'), 
        None
    )
    document_writer_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'document_writer'), 
        None
    )
    submission_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'submission'), 
        None
    )
    
    client_data = CRMClientResponse.from_orm(db_client)
    client_data.market_staff_user_id = market_staff_assignment.user_id if market_staff_assignment else None
    client_data.sales_staff_user_id = sales_staff_assignment.user_id if sales_staff_assignment else None
    client_data.document_writer_staff_user_id = document_writer_assignment.user_id if document_writer_assignment else None
    client_data.submission_staff_user_id = submission_assignment.user_id if submission_assignment else None
    
    return client_data


@router.put("/{client_id}", response_model=CRMClientResponse, summary="更新有效客户")
async def update_valid_customer(
    client_id: int,
    client_data: CRMClientUpdate,
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新有效客户信息，包括客资有效性和五维反馈信息
    """
    # 数据隔离
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可更新CRM数据")
    if organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权操作其他组织数据")

    # 获取现有客户
    existing_client = await CRMService.get_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id
    )
    
    if not existing_client:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 如果只是更新客资有效性（is_valid），不需要验证生命周期状态
    # 使用Pydantic的exclude_unset方法获取只有设置值的字段
    set_fields_dict = client_data.dict(exclude_unset=True)
    set_fields = list(set_fields_dict.keys())

    # 如果只设置了is_valid字段，则认为是纯客资有效性更新
    is_only_valid_update = set_fields == ['is_valid']

    if not is_only_valid_update:
        # 对于其他字段的更新，验证是否为有效客户
        if existing_client.lifecycle_status != LifecycleStatus.VALID:
            raise HTTPException(status_code=400, detail="该客户不是有效客户")
        
        # 确保不会修改生命周期状态：仅当请求体显式包含 lifecycle_status 时才校验
        if 'lifecycle_status' in set_fields_dict and client_data.lifecycle_status != LifecycleStatus.VALID:
            raise HTTPException(status_code=400, detail="不能修改有效客户的生命周期状态")
    
    db_client = await CRMService.update_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id,
        client_data=client_data,
        updated_by_user_id=current_user.id
    )
    
    if not db_client:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 构建包含人员分配信息的响应
    market_staff_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'market'), 
        None
    )
    sales_staff_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'sales'), 
        None
    )
    document_writer_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'document_writer'), 
        None
    )
    submission_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'submission'), 
        None
    )
    
    client_data = CRMClientResponse.from_orm(db_client)
    client_data.market_staff_user_id = market_staff_assignment.user_id if market_staff_assignment else None
    client_data.sales_staff_user_id = sales_staff_assignment.user_id if sales_staff_assignment else None
    client_data.document_writer_staff_user_id = document_writer_assignment.user_id if document_writer_assignment else None
    client_data.submission_staff_user_id = submission_assignment.user_id if submission_assignment else None
    
    return client_data


@router.post("/{client_id}/convert-to-signed", response_model=CRMClientResponse, summary="转为签约客户")
async def convert_to_signed_customer(
    client_id: int,
    organization_id: int = Query(..., description="组织ID"),
    converted_client_id: Optional[int] = Query(None, description="转签后的客户ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    将有效客户转为签约客户
    """
    # 数据隔离
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可更新CRM数据")
    if organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权操作其他组织数据")

    # 获取现有客户
    existing_client = await CRMService.get_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id
    )
    
    if not existing_client:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 验证是否为有效客户
    if existing_client.lifecycle_status != LifecycleStatus.VALID:
        raise HTTPException(status_code=400, detail="只有有效客户才能转为签约客户")
    
    # 更新为签约状态
    update_data = CRMClientUpdate(
        lifecycle_status=LifecycleStatus.SIGNED,
        converted_client_id=converted_client_id
    )
    
    db_client = await CRMService.update_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id,
        client_data=update_data,
        updated_by_user_id=current_user.id
    )
    
    # 构建包含人员分配信息的响应
    market_staff_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'market'), 
        None
    )
    sales_staff_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'sales'), 
        None
    )
    document_writer_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'document_writer'), 
        None
    )
    submission_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'submission'), 
        None
    )
    
    client_data = CRMClientResponse.from_orm(db_client)
    client_data.market_staff_user_id = market_staff_assignment.user_id if market_staff_assignment else None
    client_data.sales_staff_user_id = sales_staff_assignment.user_id if sales_staff_assignment else None
    client_data.document_writer_staff_user_id = document_writer_assignment.user_id if document_writer_assignment else None
    client_data.submission_staff_user_id = submission_assignment.user_id if submission_assignment else None
    
    return client_data


@router.post("/{client_id}/archive", response_model=CRMClientResponse, summary="归档有效客户")
async def archive_valid_customer(
    client_id: int,
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    归档有效客户
    """
    # 数据隔离
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可更新CRM数据")
    if organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权操作其他组织数据")

    # 获取现有客户
    existing_client = await CRMService.get_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id
    )
    
    if not existing_client:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    # 验证是否为有效客户
    if existing_client.lifecycle_status != LifecycleStatus.VALID:
        raise HTTPException(status_code=400, detail="只有有效客户才能归档")
    
    # 更新为归档状态
    update_data = CRMClientUpdate(lifecycle_status=LifecycleStatus.ARCHIVED)
    
    db_client = await CRMService.update_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id,
        client_data=update_data,
        updated_by_user_id=current_user.id
    )
    
    # 构建包含人员分配信息的响应
    market_staff_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'market'), 
        None
    )
    sales_staff_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'sales'), 
        None
    )
    document_writer_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'document_writer'), 
        None
    )
    submission_assignment = next(
        (assignment for assignment in db_client.staff_assignments if assignment.role == 'submission'), 
        None
    )
    
    client_data = CRMClientResponse.from_orm(db_client)
    client_data.market_staff_user_id = market_staff_assignment.user_id if market_staff_assignment else None
    client_data.sales_staff_user_id = sales_staff_assignment.user_id if sales_staff_assignment else None
    client_data.document_writer_staff_user_id = document_writer_assignment.user_id if document_writer_assignment else None
    client_data.submission_staff_user_id = submission_assignment.user_id if submission_assignment else None
    
    return client_data


@router.get("/statistics/overview", response_model=dict, summary="获取有效客户统计信息")
async def get_valid_customers_statistics(
    query: ValidCustomerStatisticsQuery = Depends(build_valid_stats_query),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取有效客户的统计信息，包括意向等级分布等
    
    权限控制：
    - 系统管理员(admin/dev)：可以查看所有客户或指定销售人员的客户统计
    - 组织owner：可以查看所有客户或指定销售人员的客户统计
    - 普通用户：只能查看分配给自己的客户统计
    """
    
    # 数据隔离
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可访问CRM数据")
    if query.organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权访问其他组织数据")

    # 权限控制：根据用户角色和组织权限决定过滤逻辑 - 与列表API保持一致
    effective_sales_staff_user_id = query.sales_staff_user_id
    
    # 检查用户是否为组织owner
    is_organization_owner = await OrganizationPermissionService.check_organization_owner(
        db, current_user, query.organization_id
    )
    
    # 权限控制逻辑 - 与列表API完全一致
    if current_user.role in ['admin', 'dev'] or is_organization_owner:
        # 系统管理员或组织owner可以查看所有客户或指定销售人员的客户
        if query.show_all:
            # 明确要求显示所有客户，不过滤销售人员
            effective_sales_staff_user_id = query.sales_staff_user_id  # 可能为None，表示所有客户
        else:
            # 默认显示分配给自己的客户
            effective_sales_staff_user_id = current_user.id
    else:
        # 普通用户只能看分配给自己的客户，忽略前端传递的参数
        effective_sales_staff_user_id = current_user.id
        
    # 权限控制逻辑完成
    
    # 构建基础查询条件
    base_conditions = [
        CRMClient.organization_id == query.organization_id,
        CRMClient.lifecycle_status == LifecycleStatus.VALID
    ]
    
    # 应用筛选条件 - 与列表API保持一致
    if query.search_keyword:
        search_condition = (
            CRMClient.customer_name.ilike(f"%{query.search_keyword}%") |
            CRMClient.wechat_name.ilike(f"%{query.search_keyword}%") |
            CRMClient.wechat_id.ilike(f"%{query.search_keyword}%") |
            CRMClient.phone.ilike(f"%{query.search_keyword}%")
        )
        base_conditions.append(search_condition)
    
    if query.intent_level:
        base_conditions.append(CRMClient.intent_level == query.intent_level)
    
    if query.assignment_status:
        base_conditions.append(CRMClient.assignment_status == query.assignment_status)
    
    if query.is_valid is not None:
        base_conditions.append(CRMClient.is_valid == query.is_valid)
    
    if query.start_date:
        base_conditions.append(CRMClient.add_date >= query.start_date)
    
    if query.end_date:
        base_conditions.append(CRMClient.add_date <= query.end_date)
    
    # 构建销售人员筛选条件
    if effective_sales_staff_user_id is not None:
        # 需要通过CRMClientStaff表进行关联查询
        sales_assignment_subquery = select(CRMClientStaff.crm_client_id).where(
            and_(
                CRMClientStaff.user_id == effective_sales_staff_user_id,
                CRMClientStaff.role == 'sales'
            )
        )
        base_conditions.append(CRMClient.id.in_(sales_assignment_subquery))
    
    # 构建市场人员筛选条件
    if query.market_staff_user_id is not None:
        market_assignment_subquery = select(CRMClientStaff.crm_client_id).where(
            and_(
                CRMClientStaff.user_id == query.market_staff_user_id,
                CRMClientStaff.role == 'market'
            )
        )
        base_conditions.append(CRMClient.id.in_(market_assignment_subquery))
    
    # 获取有效客户总数
    total_result = await db.execute(
        select(func.count(CRMClient.id))
        .where(and_(*base_conditions))
        .where(CRMClient.is_valid == True)
    )
    total = total_result.scalar() or 0
    
    # 意向等级分布
    intent_level_stats = await db.execute(
        select(
            CRMClient.intent_level,
            func.count(CRMClient.id).label('count')
        )
        .where(and_(*base_conditions))
        .where(CRMClient.is_valid == True)
        .group_by(CRMClient.intent_level)
    )
    intent_level_counts = {row.intent_level or 'unknown': row.count for row in intent_level_stats}
    
    # 分配状态分布
    assignment_stats = await db.execute(
        select(
            CRMClient.assignment_status,
            func.count(CRMClient.id).label('count')
        )
        .where(and_(*base_conditions))
        .where(CRMClient.is_valid == True)
        .group_by(CRMClient.assignment_status)
    )
    assignment_counts = {row.assignment_status: row.count for row in assignment_stats}
    
    # 获取无效客户总数
    invalid_result = await db.execute(
        select(func.count(CRMClient.id))
        .where(and_(*base_conditions))
        .where(CRMClient.is_valid == False)
    )
    invalid_count = invalid_result.scalar() or 0
    
    # 统计数据计算完成
    
    return {
        "total": total,
        "invalid_count": invalid_count,
        "intent_level_distribution": {
            "high": intent_level_counts.get("high", 0),
            "medium": intent_level_counts.get("medium", 0),
            "low": intent_level_counts.get("low", 0),
            "unknown": intent_level_counts.get("unknown", 0)
        },
        "assignment_distribution": {
            "assigned": assignment_counts.get("assigned", 0),
            "unassigned": assignment_counts.get("unassigned", 0)
        }
    }