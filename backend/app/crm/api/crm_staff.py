"""
CRM人员分配管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.crm.core.crm_service import CRMService
from app.crm.schemas import (
    CRMClientStaffCreate, CRMClientStaffResponse, StaffRole
)

router = APIRouter()


@router.post("/{client_id}/staff", response_model=CRMClientStaffResponse, summary="为客户分配人员")
async def assign_staff_to_client(
    client_id: int,
    user_id: int = Query(..., description="用户ID"),
    role: StaffRole = Query(..., description="角色"),
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    为CRM客户分配人员
    """
    try:
        db_staff = await CRMService.assign_staff_to_client(
            db=db,
            crm_client_id=client_id,
            organization_id=organization_id,
            user_id=user_id,
            role=role
        )
        return CRMClientStaffResponse.from_orm(db_staff)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{client_id}/staff", summary="移除客户人员分配")
async def remove_staff_from_client(
    client_id: int,
    user_id: int = Query(..., description="用户ID"),
    role: StaffRole = Query(..., description="角色"),
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    移除CRM客户的人员分配
    """
    success = await CRMService.remove_staff_from_client(
        db=db,
        crm_client_id=client_id,
        organization_id=organization_id,
        user_id=user_id,
        role=role
    )
    
    if not success:
        raise HTTPException(status_code=404, detail="人员分配不存在")
    
    return {"message": "人员分配移除成功"}


@router.get("/{client_id}/staff", response_model=List[CRMClientStaffResponse], summary="获取客户人员分配")
async def get_client_staff(
    client_id: int,
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取CRM客户的所有人员分配
    """
    staff_list = await CRMService.get_client_staff(
        db=db,
        crm_client_id=client_id,
        organization_id=organization_id
    )
    
    return [CRMClientStaffResponse.from_orm(staff) for staff in staff_list]