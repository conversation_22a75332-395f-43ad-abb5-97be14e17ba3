"""
CRM客户管理API路由
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.crm.core.crm_service import CRMService
from app.crm.schemas import (
    CRMClientCreate, CRMClientUpdate, CRMClientResponse, CRMClientListResponse,
    CRMClientFilter, CRMStatistics, AssignmentStatus, IntentLevel, LifecycleStatus,
    CRMClientListQuery
)
from datetime import date
from app.core.data_isolation import DataIsolationFilter

router = APIRouter()


def build_list_query(
    organization_id: int = Query(..., description="组织ID"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    search_keyword: Optional[str] = Query(None, description="搜索关键词"),
    channel_source: Optional[str] = Query(None, description="渠道来源"),
    assignment_status: Optional[AssignmentStatus] = Query(None, description="分配状态"),
    is_valid: Optional[bool] = Query(None, description="是否有效"),
    intent_level: Optional[IntentLevel] = Query(None, description="意向等级"),
    lifecycle_status: Optional[LifecycleStatus] = Query(None, description="生命周期状态"),
    exclude_lifecycle_status: Optional[str] = Query(None, description="排除的生命周期状态（逗号分隔）"),
    market_staff_user_id: Optional[int] = Query(None, description="市场人员ID"),
    sales_staff_user_id: Optional[int] = Query(None, description="销售人员ID"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
) -> CRMClientListQuery:
    return CRMClientListQuery(
        organization_id=organization_id,
        page=page,
        size=size,
        search_keyword=search_keyword,
        channel_source=channel_source,
        assignment_status=assignment_status,
        is_valid=is_valid,
        intent_level=intent_level,
        lifecycle_status=lifecycle_status,
        exclude_lifecycle_status=exclude_lifecycle_status,
        market_staff_user_id=market_staff_user_id,
        sales_staff_user_id=sales_staff_user_id,
        start_date=start_date,
        end_date=end_date,
    )


@router.post("/", response_model=CRMClientResponse, summary="创建CRM客户")
async def create_crm_client(
    client_data: CRMClientCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的CRM客户（市场资源或有效客户）
    """
    try:
        # 数据隔离校验：仅允许组织身份访问且组织ID匹配
        identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
        if identity_type != 'organization' or not scoped_org_id:
            raise HTTPException(status_code=403, detail="仅组织身份可创建CRM数据")
        if client_data.organization_id != scoped_org_id:
            raise HTTPException(status_code=403, detail="无权在其他组织下创建数据")

        db_client = await CRMService.create_crm_client(
            db=db,
            client_data=client_data,
            created_by_user_id=current_user.id
        )
        return CRMClientResponse.from_orm(db_client)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{client_id}", response_model=CRMClientResponse, summary="获取CRM客户详情")
async def get_crm_client(
    client_id: int,
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取单个CRM客户的详细信息
    """
    # 数据隔离校验
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可访问CRM数据")
    if organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权访问其他组织数据")

    db_client = await CRMService.get_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id
    )
    
    if not db_client:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    return CRMClientResponse.from_orm(db_client)


@router.put("/{client_id}", response_model=CRMClientResponse, summary="更新CRM客户")
async def update_crm_client(
    client_id: int,
    client_data: CRMClientUpdate,
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新CRM客户信息
    """
    # 数据隔离校验
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可更新CRM数据")
    if organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权操作其他组织数据")

    db_client = await CRMService.update_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id,
        client_data=client_data,
        updated_by_user_id=current_user.id
    )
    
    if not db_client:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    return CRMClientResponse.from_orm(db_client)


@router.delete("/{client_id}", summary="删除CRM客户")
async def delete_crm_client(
    client_id: int,
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除CRM客户
    """
    # 数据隔离校验
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可删除CRM数据")
    if organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权操作其他组织数据")

    success = await CRMService.delete_crm_client(
        db=db,
        client_id=client_id,
        organization_id=organization_id
    )
    
    if not success:
        raise HTTPException(status_code=404, detail="客户不存在")
    
    return {"message": "客户删除成功"}


@router.get("/", response_model=CRMClientListResponse, summary="获取CRM客户列表")
async def list_crm_clients(
    query: CRMClientListQuery = Depends(build_list_query),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取CRM客户列表，支持筛选和分页
    """
    # 数据隔离校验
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可访问CRM数据")
    if query.organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权访问其他组织数据")

    # 构建筛选条件
    filters = CRMClientFilter(
        search_keyword=query.search_keyword,
        channel_source=query.channel_source,
        assignment_status=query.assignment_status,
        is_valid=query.is_valid,
        intent_level=query.intent_level,
        lifecycle_status=query.lifecycle_status,
        exclude_lifecycle_status=query.exclude_lifecycle_status,
        market_staff_user_id=query.market_staff_user_id,
        sales_staff_user_id=query.sales_staff_user_id,
        start_date=query.start_date,
        end_date=query.end_date
    )
    
    clients, total = await CRMService.list_crm_clients(
        db=db,
        organization_id=query.organization_id,
        filters=filters,
        page=query.page,
        size=query.size
    )
    
    # 计算总页数
    pages = (total + query.size - 1) // query.size
    
    # 构建响应数据，包含人员分配信息（单选）
    response_items = []
    for client in clients:
        # 从staff_assignments中提取单个人员ID（每个角色只取第一个）
        market_staff_assignment = next(
            (assignment for assignment in client.staff_assignments if assignment.role == 'market'), 
            None
        )
        sales_staff_assignment = next(
            (assignment for assignment in client.staff_assignments if assignment.role == 'sales'), 
            None
        )
        document_writer_assignment = next(
            (assignment for assignment in client.staff_assignments if assignment.role == 'document_writer'), 
            None
        )
        submission_assignment = next(
            (assignment for assignment in client.staff_assignments if assignment.role == 'submission'), 
            None
        )
        
        # 创建响应对象
        client_data = CRMClientResponse.from_orm(client)
        client_data.market_staff_user_id = market_staff_assignment.user_id if market_staff_assignment else None
        client_data.sales_staff_user_id = sales_staff_assignment.user_id if sales_staff_assignment else None
        client_data.document_writer_staff_user_id = document_writer_assignment.user_id if document_writer_assignment else None
        client_data.submission_staff_user_id = submission_assignment.user_id if submission_assignment else None
        
        response_items.append(client_data)
    
    return CRMClientListResponse(
        total=total,
        items=response_items,
        page=query.page,
        size=query.size,
        pages=pages
    )


@router.get("/statistics/overview", response_model=CRMStatistics, summary="获取CRM统计信息")
async def get_crm_statistics(
    organization_id: int = Query(..., description="组织ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取CRM系统的统计信息
    """
    # 数据隔离校验
    identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
    if identity_type != 'organization' or not scoped_org_id:
        raise HTTPException(status_code=403, detail="仅组织身份可访问CRM数据")
    if organization_id != scoped_org_id:
        raise HTTPException(status_code=403, detail="无权访问其他组织数据")

    statistics = await CRMService.get_crm_statistics(
        db=db,
        organization_id=organization_id
    )
    
    return statistics