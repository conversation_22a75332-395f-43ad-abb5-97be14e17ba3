"""
CRM API主路由
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import joinedload
from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.models.organization import OrganizationMember
from .crm_clients import router as crm_clients_router
from .crm_staff import router as crm_staff_router
from .valid_customers import router as valid_customers_router
from app.core.data_isolation import DataIsolationFilter

# 创建CRM主路由器
router = APIRouter(prefix="/crm", tags=["CRM"])

# 包含子路由
router.include_router(crm_clients_router, prefix="/clients", tags=["CRM客户"])
router.include_router(crm_staff_router, prefix="/clients", tags=["CRM人员分配"])
router.include_router(valid_customers_router, prefix="/valid-customers", tags=["有效客户"])


@router.get("/organization/{organization_id}/members", summary="获取组织成员列表（用于CRM分配）")
async def get_organization_members_for_crm(
    organization_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取组织成员列表，用于CRM系统的人员分配功能
    返回简化的成员信息，包括用户ID、用户名、邮箱和角色
    """
    try:
        # 数据隔离：仅允许访问所属组织成员
        identity_type, scoped_org_id = DataIsolationFilter.get_user_data_scope(current_user)
        if identity_type != 'organization' or not scoped_org_id:
            raise HTTPException(status_code=403, detail="仅组织身份可访问组织成员")
        if organization_id != scoped_org_id:
            raise HTTPException(status_code=403, detail="无权访问其他组织成员")

        # 查询组织成员
        result = await db.execute(
            select(OrganizationMember)
            .options(joinedload(OrganizationMember.user))
            .where(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.is_active == True
            )
            .order_by(OrganizationMember.joined_at.desc())
        )
        members = result.scalars().all()
        
        # 构建响应数据
        members_data = []
        for member in members:
            if member.user:
                member_data = {
                    "user_id": member.user.id,
                    "username": member.user.username,
                    "email": member.user.email,
                    "nickname": member.user.nickname or member.user.username,
                    "organization_username": member.organization_username,
                    "organization_role": member.role,
                    "display_name": member.user.nickname or member.user.username
                }
                members_data.append(member_data)
        
        return {
            "organization_id": organization_id,
            "members": members_data,
            "total": len(members_data)
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"获取组织成员失败: {str(e)}")