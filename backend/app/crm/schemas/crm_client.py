"""
CRM客户相关的Pydantic模型
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, date
from enum import Enum


class AssignmentStatus(str, Enum):
    """分配状态枚举"""
    UNASSIGNED = "unassigned"
    ASSIGNED = "assigned"


class IntentLevel(str, Enum):
    """意向等级枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class LifecycleStatus(str, Enum):
    """生命周期状态枚举"""
    LEAD = "lead"
    VALID = "valid"
    SIGNED = "signed"
    ARCHIVED = "archived"


class StaffRole(str, Enum):
    """人员角色枚举"""
    MARKET = "market"
    SALES = "sales"
    DOCUMENT_WRITER = "document_writer"
    SUBMISSION = "submission"


class LanguageScore(BaseModel):
    """语言成绩模型"""
    type: str = Field(..., description="语言类型，如IELTS、TOEFL、CET4等")
    score: str = Field(..., description="成绩分数")


class CRMClientBase(BaseModel):
    """CRM客户基础模型"""
    add_date: date = Field(..., description="添加日期")
    customer_name: Optional[str] = Field(None, max_length=100, description="客户姓名")
    wechat_name: str = Field(..., max_length=100, description="微信名称")
    wechat_id: str = Field(..., max_length=100, description="微信账号")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")
    channel_source: str = Field(..., max_length=50, description="渠道来源")
    study_project: str = Field(..., max_length=100, description="咨询项目")
    customer_background: Optional[str] = Field(None, description="客户背景")
    market_supplement: Optional[str] = Field(None, description="市场补充")
    assignment_status: AssignmentStatus = Field(AssignmentStatus.UNASSIGNED, description="分配状态")
    is_valid: bool = Field(True, description="是否有效客户")


class CRMClientValidCustomer(BaseModel):
    """有效客户信息模型"""
    intent_level: Optional[IntentLevel] = Field(None, description="意向等级")
    feedback_budget: Optional[str] = Field(None, description="预算反馈")
    feedback_motivation: Optional[str] = Field(None, description="动机反馈")
    feedback_urgency: Optional[str] = Field(None, description="紧迫性反馈")
    feedback_comparison: Optional[str] = Field(None, description="对比反馈")
    feedback_parents: Optional[str] = Field(None, description="父母/决策人反馈")


class CRMClientEducation(BaseModel):
    """教育背景信息模型"""
    current_school: Optional[str] = Field(None, max_length=200, description="当前院校")
    current_major: Optional[str] = Field(None, max_length=200, description="当前专业")
    current_grade: Optional[str] = Field(None, max_length=50, description="当前年级")
    gpa_value: Optional[str] = Field(None, max_length=10, description="GPA数值")
    gpa_scale: Optional[str] = Field(None, max_length=10, description="GPA满分")
    language_scores: Optional[List[LanguageScore]] = Field(None, description="语言成绩")
    intended_enrollment_date: Optional[str] = Field(None, max_length=32, description="预期入学时间")


class CRMClientIntent(BaseModel):
    """意向信息模型"""
    intent_regions: List[str] = Field(default_factory=list, description="意向地区")
    intent_schools: List[str] = Field(default_factory=list, description="意向院校")
    intent_majors: List[str] = Field(default_factory=list, description="意向专业")


class CRMClientCreate(CRMClientBase, CRMClientValidCustomer, CRMClientEducation, CRMClientIntent):
    """创建CRM客户的请求模型"""
    organization_id: int = Field(..., description="所属组织ID")
    lifecycle_status: LifecycleStatus = Field(LifecycleStatus.LEAD, description="生命周期状态")


class CRMClientUpdate(BaseModel):
    """更新CRM客户的请求模型"""
    add_date: Optional[date] = Field(None, description="添加日期")
    customer_name: Optional[str] = Field(None, max_length=100, description="客户姓名")
    wechat_name: Optional[str] = Field(None, max_length=100, description="微信名称")
    wechat_id: Optional[str] = Field(None, max_length=100, description="微信账号")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")
    channel_source: Optional[str] = Field(None, max_length=50, description="渠道来源")
    study_project: Optional[str] = Field(None, max_length=100, description="咨询项目")
    customer_background: Optional[str] = Field(None, description="客户背景")
    market_supplement: Optional[str] = Field(None, description="市场补充")
    assignment_status: Optional[AssignmentStatus] = Field(None, description="分配状态")
    is_valid: Optional[bool] = Field(None, description="是否有效客户")
    intent_level: Optional[IntentLevel] = Field(None, description="意向等级")
    feedback_budget: Optional[str] = Field(None, description="预算反馈")
    feedback_motivation: Optional[str] = Field(None, description="动机反馈")
    feedback_urgency: Optional[str] = Field(None, description="紧迫性反馈")
    feedback_comparison: Optional[str] = Field(None, description="对比反馈")
    feedback_parents: Optional[str] = Field(None, description="父母/决策人反馈")
    current_school: Optional[str] = Field(None, max_length=200, description="当前院校")
    current_major: Optional[str] = Field(None, max_length=200, description="当前专业")
    current_grade: Optional[str] = Field(None, max_length=50, description="当前年级")
    gpa_value: Optional[str] = Field(None, max_length=10, description="GPA数值")
    gpa_scale: Optional[str] = Field(None, max_length=10, description="GPA满分")
    language_scores: Optional[List[LanguageScore]] = Field(None, description="语言成绩")
    intended_enrollment_date: Optional[str] = Field(None, max_length=32, description="预期入学时间")
    intent_regions: Optional[List[str]] = Field(None, description="意向地区")
    intent_schools: Optional[List[str]] = Field(None, description="意向院校")
    intent_majors: Optional[List[str]] = Field(None, description="意向专业")
    lifecycle_status: Optional[LifecycleStatus] = Field(None, description="生命周期状态")
    converted_client_id: Optional[int] = Field(None, description="转签后的客户ID")


class CRMClientResponse(CRMClientBase, CRMClientValidCustomer, CRMClientEducation, CRMClientIntent):
    """CRM客户响应模型"""
    id: int = Field(..., description="客户ID")
    organization_id: int = Field(..., description="所属组织ID")
    lifecycle_status: LifecycleStatus = Field(..., description="生命周期状态")
    converted_client_id: Optional[int] = Field(None, description="转签后的客户ID")
    created_by_user_id: Optional[int] = Field(None, description="创建者ID")
    updated_by_user_id: Optional[int] = Field(None, description="更新者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 人员分配信息（单选）
    market_staff_user_id: Optional[int] = Field(None, description="市场人员用户ID")
    sales_staff_user_id: Optional[int] = Field(None, description="销售人员用户ID")
    document_writer_staff_user_id: Optional[int] = Field(None, description="文案人员用户ID")
    submission_staff_user_id: Optional[int] = Field(None, description="递交人员用户ID")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda dt: dt.isoformat(),
            date: lambda d: d.isoformat()
        }


class CRMClientListResponse(BaseModel):
    """CRM客户列表响应模型"""
    total: int = Field(..., description="总数")
    items: List[CRMClientResponse] = Field(..., description="客户列表")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class CRMClientFilter(BaseModel):
    """CRM客户筛选条件模型"""
    search_keyword: Optional[str] = Field(None, description="搜索关键词（姓名/微信/电话）")
    channel_source: Optional[str] = Field(None, description="渠道来源")
    assignment_status: Optional[AssignmentStatus] = Field(None, description="分配状态")
    is_valid: Optional[bool] = Field(None, description="是否有效")
    intent_level: Optional[IntentLevel] = Field(None, description="意向等级")
    lifecycle_status: Optional[LifecycleStatus] = Field(None, description="生命周期状态")
    exclude_lifecycle_status: Optional[str] = Field(None, description="排除的生命周期状态（逗号分隔）")
    market_staff_user_id: Optional[int] = Field(None, description="市场人员ID")
    sales_staff_user_id: Optional[int] = Field(None, description="销售人员ID")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")


class CRMClientListQuery(BaseModel):
    """获取CRM客户列表的查询参数模型"""
    organization_id: int = Field(..., description="组织ID")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")
    search_keyword: Optional[str] = Field(None, description="搜索关键词（姓名/微信/电话）")
    channel_source: Optional[str] = Field(None, description="渠道来源")
    assignment_status: Optional[AssignmentStatus] = Field(None, description="分配状态")
    is_valid: Optional[bool] = Field(None, description="是否有效")
    intent_level: Optional[IntentLevel] = Field(None, description="意向等级")
    lifecycle_status: Optional[LifecycleStatus] = Field(None, description="生命周期状态")
    exclude_lifecycle_status: Optional[str] = Field(None, description="排除的生命周期状态（逗号分隔）")
    market_staff_user_id: Optional[int] = Field(None, description="市场人员ID")
    sales_staff_user_id: Optional[int] = Field(None, description="销售人员ID")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")


class CRMStatistics(BaseModel):
    """CRM统计信息模型"""
    total: int = Field(..., description="总客户数")
    unassigned: int = Field(..., description="未分配数")
    assigned: int = Field(..., description="已分配数")
    invalid: int = Field(..., description="无效客户数")
    lead_count: int = Field(..., description="线索数")
    valid_count: int = Field(..., description="有效客户数")
    signed_count: int = Field(..., description="已签约数")
    archived_count: int = Field(..., description="已归档数")


class ValidCustomerListQuery(BaseModel):
    """有效客户列表的查询参数模型"""
    organization_id: int = Field(..., description="组织ID")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")
    search_keyword: Optional[str] = Field(None, description="搜索关键词")
    intent_level: Optional[IntentLevel] = Field(None, description="意向等级")
    assignment_status: Optional[AssignmentStatus] = Field(None, description="分配状态")
    market_staff_user_id: Optional[int] = Field(None, description="市场人员ID")
    sales_staff_user_id: Optional[int] = Field(None, description="销售人员ID")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    show_all: Optional[bool] = Field(False, description="是否显示所有客户（仅管理员或组织owner）")
    is_valid: Optional[bool] = Field(None, description="是否为有效客资")


class ValidCustomerStatisticsQuery(BaseModel):
    """有效客户统计信息的查询参数模型"""
    organization_id: int = Field(..., description="组织ID")
    search_keyword: Optional[str] = Field(None, description="搜索关键词")
    intent_level: Optional[IntentLevel] = Field(None, description="意向等级")
    assignment_status: Optional[AssignmentStatus] = Field(None, description="分配状态")
    market_staff_user_id: Optional[int] = Field(None, description="市场人员ID")
    sales_staff_user_id: Optional[int] = Field(None, description="销售人员ID")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    show_all: Optional[bool] = Field(False, description="是否显示所有客户（仅管理员或组织owner）")
    is_valid: Optional[bool] = Field(None, description="是否为有效客资")