"""
CRM人员分配相关的Pydantic模型
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from .crm_client import StaffRole


class CRMClientStaffBase(BaseModel):
    """CRM客户人员分配基础模型"""
    crm_client_id: int = Field(..., description="CRM客户ID")
    organization_id: int = Field(..., description="组织ID")
    user_id: int = Field(..., description="用户ID")
    role: StaffRole = Field(..., description="角色")


class CRMClientStaffCreate(CRMClientStaffBase):
    """创建CRM客户人员分配的请求模型"""
    pass


class CRMClientStaffResponse(CRMClientStaffBase):
    """CRM客户人员分配响应模型"""
    id: int = Field(..., description="分配记录ID")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }


class CRMClientStaffBatchAssign(BaseModel):
    """批量分配人员的请求模型"""
    crm_client_id: int = Field(..., description="CRM客户ID")
    organization_id: int = Field(..., description="组织ID")
    assignments: List[CRMClientStaffCreate] = Field(..., description="分配列表")


class CRMClientStaffUpdate(BaseModel):
    """更新人员分配的请求模型"""
    user_ids: List[int] = Field(..., description="用户ID列表")


class CRMClientWithStaff(BaseModel):
    """包含人员分配的CRM客户模型"""
    id: int = Field(..., description="客户ID")
    customer_name: Optional[str] = Field(None, description="客户姓名")
    wechat_name: str = Field(..., description="微信名称")
    wechat_id: str = Field(..., description="微信账号")
    phone: Optional[str] = Field(None, description="电话号码")
    channel_source: str = Field(..., description="渠道来源")
    study_project: str = Field(..., description="咨询项目")
    assignment_status: str = Field(..., description="分配状态")
    is_valid: bool = Field(..., description="是否有效客户")
    lifecycle_status: str = Field(..., description="生命周期状态")
    market_staff: List[dict] = Field(default_factory=list, description="市场人员")
    sales_staff: List[dict] = Field(default_factory=list, description="销售人员")
    document_writers: List[dict] = Field(default_factory=list, description="文书老师")
    submission_staff: List[dict] = Field(default_factory=list, description="递交老师")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }