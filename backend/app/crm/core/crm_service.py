"""
CRM系统核心服务
"""

from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, update
from sqlalchemy.orm import selectinload
from app.crm.models import CR<PERSON>lient, CRMClientStaff
from app.crm.schemas import (
    CRMClientCreate, CRMClientUpdate, CRMClientResponse, CRMClientFilter,
    CRMClientStaffCreate, CRMClientStaffResponse, CRMStatistics,
    AssignmentStatus, IntentLevel, LifecycleStatus, StaffRole
)
from app.models.user import User
from datetime import datetime, date


class CRMService:
    """CRM服务类"""

    @staticmethod
    async def create_crm_client(
        db: AsyncSession,
        client_data: CRMClientCreate,
        created_by_user_id: Optional[int] = None
    ) -> CRMClient:
        """
        创建CRM客户
        """
        # 处理语言成绩JSON
        language_scores_json = None
        if client_data.language_scores:
            language_scores_json = [score.dict() for score in client_data.language_scores]

        db_client = CRMClient(
            organization_id=client_data.organization_id,
            add_date=client_data.add_date,
            customer_name=client_data.customer_name,
            wechat_name=client_data.wechat_name,
            wechat_id=client_data.wechat_id,
            phone=client_data.phone,
            channel_source=client_data.channel_source,
            study_project=client_data.study_project,
            customer_background=client_data.customer_background,
            market_supplement=client_data.market_supplement,
            assignment_status=client_data.assignment_status,
            is_valid=client_data.is_valid,
            intent_level=client_data.intent_level,
            feedback_budget=client_data.feedback_budget,
            feedback_motivation=client_data.feedback_motivation,
            feedback_urgency=client_data.feedback_urgency,
            feedback_comparison=client_data.feedback_comparison,
            feedback_parents=client_data.feedback_parents,
            current_school=client_data.current_school,
            current_major=client_data.current_major,
            current_grade=client_data.current_grade,
            gpa_value=client_data.gpa_value,
            gpa_scale=client_data.gpa_scale,
            language_scores=language_scores_json,
            intended_enrollment_date=client_data.intended_enrollment_date,
            intent_regions=client_data.intent_regions,
            intent_schools=client_data.intent_schools,
            intent_majors=client_data.intent_majors,
            lifecycle_status=client_data.lifecycle_status,
            created_by_user_id=created_by_user_id,
            updated_by_user_id=created_by_user_id
        )

        db.add(db_client)
        await db.commit()
        await db.refresh(db_client)
        return db_client

    @staticmethod
    async def get_crm_client(
        db: AsyncSession,
        client_id: int,
        organization_id: int
    ) -> Optional[CRMClient]:
        """
        获取单个CRM客户
        """
        result = await db.execute(
            select(CRMClient)
            .options(selectinload(CRMClient.staff_assignments))
            .where(and_(CRMClient.id == client_id, CRMClient.organization_id == organization_id))
        )
        return result.scalar_one_or_none()

    @staticmethod
    async def update_crm_client(
        db: AsyncSession,
        client_id: int,
        organization_id: int,
        client_data: CRMClientUpdate,
        updated_by_user_id: Optional[int] = None
    ) -> Optional[CRMClient]:
        """
        更新CRM客户
        """
        result = await db.execute(
            select(CRMClient)
            .where(and_(CRMClient.id == client_id, CRMClient.organization_id == organization_id))
        )
        db_client = result.scalar_one_or_none()
        
        if not db_client:
            return None

        # 更新字段
        update_data = client_data.dict(exclude_unset=True)
        
        # 处理语言成绩JSON
        if 'language_scores' in update_data and update_data['language_scores']:
            update_data['language_scores'] = [score.dict() for score in update_data['language_scores']]
        
        # 添加更新信息
        update_data['updated_by_user_id'] = updated_by_user_id
        update_data['updated_at'] = datetime.utcnow()

        for field, value in update_data.items():
            setattr(db_client, field, value)

        await db.commit()
        await db.refresh(db_client)
        return db_client

    @staticmethod
    async def delete_crm_client(
        db: AsyncSession,
        client_id: int,
        organization_id: int
    ) -> bool:
        """
        删除CRM客户
        """
        result = await db.execute(
            select(CRMClient)
            .where(and_(CRMClient.id == client_id, CRMClient.organization_id == organization_id))
        )
        db_client = result.scalar_one_or_none()
        
        if not db_client:
            return False

        await db.delete(db_client)
        await db.commit()
        return True

    @staticmethod
    async def list_crm_clients(
        db: AsyncSession,
        organization_id: int,
        filters: Optional[CRMClientFilter] = None,
        page: int = 1,
        size: int = 20
    ) -> Tuple[List[CRMClient], int]:
        """
        获取CRM客户列表
        """
        query = select(CRMClient).where(CRMClient.organization_id == organization_id)

        # 应用筛选条件
        if filters:
            if filters.search_keyword:
                search_term = f"%{filters.search_keyword}%"
                query = query.where(
                    or_(
                        CRMClient.customer_name.ilike(search_term),
                        CRMClient.wechat_name.ilike(search_term),
                        CRMClient.wechat_id.ilike(search_term),
                        CRMClient.phone.ilike(search_term)
                    )
                )
            
            if filters.channel_source:
                query = query.where(CRMClient.channel_source == filters.channel_source)
            
            if filters.assignment_status:
                query = query.where(CRMClient.assignment_status == filters.assignment_status)
            
            if filters.is_valid is not None:
                query = query.where(CRMClient.is_valid == filters.is_valid)
            
            if filters.intent_level:
                query = query.where(CRMClient.intent_level == filters.intent_level)
            
            if filters.lifecycle_status:
                query = query.where(CRMClient.lifecycle_status == filters.lifecycle_status)
            
            # 排除指定的生命周期状态
            if filters.exclude_lifecycle_status:
                excluded_statuses = [status.strip() for status in filters.exclude_lifecycle_status.split(',')]
                query = query.where(~CRMClient.lifecycle_status.in_(excluded_statuses))
            
            if filters.start_date:
                query = query.where(CRMClient.add_date >= filters.start_date)
            
            if filters.end_date:
                query = query.where(CRMClient.add_date <= filters.end_date)

            # 人员筛选需要联表查询
            if filters.market_staff_user_id:
                market_staff_alias = CRMClientStaff.__table__.alias('market_staff')
                query = query.join(market_staff_alias).where(
                    and_(
                        market_staff_alias.c.role == StaffRole.MARKET.value,
                        market_staff_alias.c.user_id == filters.market_staff_user_id
                    )
                )
            
            if filters.sales_staff_user_id:
                sales_staff_alias = CRMClientStaff.__table__.alias('sales_staff')
                query = query.join(sales_staff_alias).where(
                    and_(
                        sales_staff_alias.c.role == StaffRole.SALES.value,
                        sales_staff_alias.c.user_id == filters.sales_staff_user_id
                    )
                )

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 分页和排序
        query = query.order_by(desc(CRMClient.created_at))
        query = query.offset((page - 1) * size).limit(size)

        # 执行查询
        result = await db.execute(query.options(selectinload(CRMClient.staff_assignments)))
        clients = result.scalars().all()

        return clients, total

    @staticmethod
    async def get_crm_statistics(
        db: AsyncSession,
        organization_id: int
    ) -> CRMStatistics:
        """
        获取CRM统计信息
        """
        # 基础统计
        total_result = await db.execute(
            select(func.count(CRMClient.id))
            .where(CRMClient.organization_id == organization_id)
        )
        total = total_result.scalar() or 0

        # 分配状态统计
        assignment_stats = await db.execute(
            select(
                CRMClient.assignment_status,
                func.count(CRMClient.id).label('count')
            )
            .where(CRMClient.organization_id == organization_id)
            .group_by(CRMClient.assignment_status)
        )
        assignment_counts = {row.assignment_status: row.count for row in assignment_stats}

        # 有效性统计
        invalid_result = await db.execute(
            select(func.count(CRMClient.id))
            .where(and_(
                CRMClient.organization_id == organization_id,
                CRMClient.is_valid == False
            ))
        )
        invalid = invalid_result.scalar() or 0

        # 生命周期统计
        lifecycle_stats = await db.execute(
            select(
                CRMClient.lifecycle_status,
                func.count(CRMClient.id).label('count')
            )
            .where(CRMClient.organization_id == organization_id)
            .group_by(CRMClient.lifecycle_status)
        )
        lifecycle_counts = {row.lifecycle_status: row.count for row in lifecycle_stats}

        return CRMStatistics(
            total=total,
            unassigned=assignment_counts.get(AssignmentStatus.UNASSIGNED, 0),
            assigned=assignment_counts.get(AssignmentStatus.ASSIGNED, 0),
            invalid=invalid,
            lead_count=lifecycle_counts.get(LifecycleStatus.LEAD, 0),
            valid_count=lifecycle_counts.get(LifecycleStatus.VALID, 0),
            signed_count=lifecycle_counts.get(LifecycleStatus.SIGNED, 0),
            archived_count=lifecycle_counts.get(LifecycleStatus.ARCHIVED, 0)
        )

    @staticmethod
    async def assign_staff_to_client(
        db: AsyncSession,
        crm_client_id: int,
        organization_id: int,
        user_id: int,
        role: StaffRole
    ) -> CRMClientStaff:
        """
        为CRM客户分配人员
        """
        # 验证用户是否属于指定的组织
        from app.models.organization import OrganizationMember
        org_member_result = await db.execute(
            select(OrganizationMember)
            .where(and_(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.user_id == user_id,
                OrganizationMember.is_active == True
            ))
        )
        
        org_member = org_member_result.scalar_one_or_none()
        if not org_member:
            raise ValueError("该用户不属于指定的组织")
        
        # 检查是否已存在相同的分配
        existing = await db.execute(
            select(CRMClientStaff)
            .where(and_(
                CRMClientStaff.crm_client_id == crm_client_id,
                CRMClientStaff.user_id == user_id,
                CRMClientStaff.role == role
            ))
        )
        
        if existing.scalar_one_or_none():
            raise ValueError("该用户已被分配到此客户的相同角色")

        db_staff = CRMClientStaff(
            crm_client_id=crm_client_id,
            organization_id=organization_id,
            user_id=user_id,
            role=role
        )

        db.add(db_staff)
        await db.commit()
        await db.refresh(db_staff)

        # 更新客户的分配状态和生命周期状态
        # 如果分配的是销售人员，且当前客户是lead状态，则自动更新为valid状态
        if role == StaffRole.SALES:
            await db.execute(
                update(CRMClient)
                .where(and_(
                    CRMClient.id == crm_client_id, 
                    CRMClient.organization_id == organization_id,
                    CRMClient.lifecycle_status == LifecycleStatus.LEAD
                ))
                .values(
                    lifecycle_status=LifecycleStatus.VALID,
                    assignment_status=AssignmentStatus.ASSIGNED,
                    updated_at=func.now()
                )
            )
        else:
            # 对于其他角色（市场人员等），只更新分配状态
            await db.execute(
                update(CRMClient)
                .where(and_(
                    CRMClient.id == crm_client_id, 
                    CRMClient.organization_id == organization_id
                ))
                .values(
                    assignment_status=AssignmentStatus.ASSIGNED,
                    updated_at=func.now()
                )
            )
        
        await db.commit()
        return db_staff

    @staticmethod
    async def remove_staff_from_client(
        db: AsyncSession,
        crm_client_id: int,
        organization_id: int,
        user_id: int,
        role: StaffRole
    ) -> bool:
        """
        移除CRM客户的人员分配
        """
        result = await db.execute(
            select(CRMClientStaff)
            .where(and_(
                CRMClientStaff.crm_client_id == crm_client_id,
                CRMClientStaff.organization_id == organization_id,
                CRMClientStaff.user_id == user_id,
                CRMClientStaff.role == role
            ))
        )
        
        db_staff = result.scalar_one_or_none()
        if not db_staff:
            return False

        await db.delete(db_staff)
        
        # 如果移除的是销售人员，检查是否还有其他销售人员
        if role == StaffRole.SALES:
            # 检查是否还有其他销售人员分配给这个客户
            remaining_sales = await db.execute(
                select(CRMClientStaff)
                .where(and_(
                    CRMClientStaff.crm_client_id == crm_client_id,
                    CRMClientStaff.organization_id == organization_id,
                    CRMClientStaff.role == StaffRole.SALES
                ))
            )
            
            if not remaining_sales.scalar_one_or_none():
                # 没有其他销售人员了，将客户状态回退到lead
                await db.execute(
                    update(CRMClient)
                    .where(and_(
                        CRMClient.id == crm_client_id,
                        CRMClient.organization_id == organization_id,
                        CRMClient.lifecycle_status == LifecycleStatus.VALID
                    ))
                    .values(
                        lifecycle_status=LifecycleStatus.LEAD,
                        assignment_status=AssignmentStatus.UNASSIGNED,
                        updated_at=func.now()
                    )
                )
        
        await db.commit()
        return True

    @staticmethod
    async def get_client_staff(
        db: AsyncSession,
        crm_client_id: int,
        organization_id: int
    ) -> List[CRMClientStaff]:
        """
        获取CRM客户的所有人员分配
        """
        result = await db.execute(
            select(CRMClientStaff)
            .options(selectinload(CRMClientStaff.user))
            .where(and_(
                CRMClientStaff.crm_client_id == crm_client_id,
                CRMClientStaff.organization_id == organization_id
            ))
        )
        return result.scalars().all()