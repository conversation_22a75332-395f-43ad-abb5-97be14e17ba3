"""
CRM系统数据库模型
包含市场资源和有效客户管理的模型
"""

from sqlalchemy import Column, BigInteger, Integer, String, Boolean, DateTime, ForeignKey, Text, ARRAY, CheckConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from datetime import datetime
from app.db.database import Base


class CRMClient(Base):
    """
    CRM客户模型类，对应 PostgreSQL 数据库中的 crm_clients 表
    覆盖市场资源和有效客户的完整生命周期
    """
    __tablename__ = "crm_clients"

    # 基本字段
    id = Column(BigInteger, primary_key=True, index=True, comment="客户ID，自增主键")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=False, comment="所属组织ID")

    # 基础信息
    add_date = Column(DateTime, nullable=False, comment="添加日期")
    customer_name = Column(String(100), nullable=True, comment="客户姓名（可空）")
    wechat_name = Column(String(100), nullable=False, comment="微信名称（必填）")
    wechat_id = Column(String(100), nullable=False, comment="微信账号（必填）")
    phone = Column(String(20), nullable=True, comment="电话号码")
    channel_source = Column(String(50), nullable=False, comment="渠道来源（必填）")

    # 业务信息
    study_project = Column(String(100), nullable=False, comment="咨询项目（必填）")
    customer_background = Column(Text, nullable=True, comment="客户背景")
    market_supplement = Column(Text, nullable=True, comment="市场补充")

    # 分配/有效性
    assignment_status = Column(String(20), nullable=False, default="unassigned", comment="分配状态")
    is_valid = Column(Boolean, nullable=False, default=True, comment="是否有效客户")

    # 有效客户：意向等级 + 五维反馈
    intent_level = Column(String(10), nullable=True, comment="意向等级")
    feedback_budget = Column(Text, nullable=True, comment="预算反馈")
    feedback_motivation = Column(Text, nullable=True, comment="动机反馈")
    feedback_urgency = Column(Text, nullable=True, comment="紧迫性反馈")
    feedback_comparison = Column(Text, nullable=True, comment="对比反馈")
    feedback_parents = Column(Text, nullable=True, comment="父母/决策人反馈")

    # 当前就读与成绩
    current_school = Column(String(200), nullable=True, comment="当前院校")
    current_major = Column(String(200), nullable=True, comment="当前专业")
    current_grade = Column(String(50), nullable=True, comment="当前年级")
    gpa_value = Column(String(10), nullable=True, comment="GPA数值")
    gpa_scale = Column(String(10), nullable=True, comment="GPA满分")

    # 语言与入学/意向
    language_scores = Column(JSONB, nullable=True, comment="语言成绩JSON")
    intended_enrollment_date = Column(String(32), nullable=True, comment="预期入学时间")
    intent_regions = Column(ARRAY(Text), nullable=False, default=[], comment="意向地区")
    intent_schools = Column(ARRAY(Text), nullable=False, default=[], comment="意向院校")
    intent_majors = Column(ARRAY(Text), nullable=False, default=[], comment="意向专业")

    # 生命周期与转签引用
    lifecycle_status = Column(String(20), nullable=False, default="lead", comment="生命周期状态")
    converted_client_id = Column(Integer, ForeignKey("clients.id", ondelete="SET NULL"), nullable=True, comment="转签后的客户ID")

    # 审计字段
    created_by_user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建者ID")
    updated_by_user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="更新者ID")
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关联关系
    organization = relationship("Organization", backref="crm_clients")
    created_by = relationship("User", foreign_keys=[created_by_user_id], backref="created_crm_clients")
    updated_by = relationship("User", foreign_keys=[updated_by_user_id], backref="updated_crm_clients")
    converted_client = relationship("Client", backref="crm_source")
    staff_assignments = relationship("CRMClientStaff", back_populates="crm_client", cascade="all, delete-orphan")

    # 约束
    __table_args__ = (
        CheckConstraint("assignment_status IN ('unassigned', 'assigned')", name="chk_assignment_status"),
        CheckConstraint("intent_level IN ('high', 'medium', 'low')", name="chk_intent_level"),
        CheckConstraint("lifecycle_status IN ('lead', 'valid', 'signed', 'archived')", name="chk_lifecycle_status"),
    )

    def to_dict(self) -> dict:
        """
        将CRM客户对象转换为字典，用于 API 响应
        """
        return {
            'id': self.id,
            'organization_id': self.organization_id,
            'add_date': self.add_date.isoformat() if self.add_date else None,
            'customer_name': self.customer_name,
            'wechat_name': self.wechat_name,
            'wechat_id': self.wechat_id,
            'phone': self.phone,
            'channel_source': self.channel_source,
            'study_project': self.study_project,
            'customer_background': self.customer_background,
            'market_supplement': self.market_supplement,
            'assignment_status': self.assignment_status,
            'is_valid': self.is_valid,
            'intent_level': self.intent_level,
            'feedback_budget': self.feedback_budget,
            'feedback_motivation': self.feedback_motivation,
            'feedback_urgency': self.feedback_urgency,
            'feedback_comparison': self.feedback_comparison,
            'feedback_parents': self.feedback_parents,
            'current_school': self.current_school,
            'current_major': self.current_major,
            'current_grade': self.current_grade,
            'gpa_value': self.gpa_value,
            'gpa_scale': self.gpa_scale,
            'language_scores': self.language_scores,
            'intended_enrollment_date': self.intended_enrollment_date,
            'intent_regions': self.intent_regions,
            'intent_schools': self.intent_schools,
            'intent_majors': self.intent_majors,
            'lifecycle_status': self.lifecycle_status,
            'converted_client_id': self.converted_client_id,
            'created_by_user_id': self.created_by_user_id,
            'updated_by_user_id': self.updated_by_user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CRMClientStaff(Base):
    """
    CRM客户人员分配模型类，对应 PostgreSQL 数据库中的 crm_client_staff 表
    支持多负责人分配和外键校验
    """
    __tablename__ = "crm_client_staff"

    # 基本字段
    id = Column(BigInteger, primary_key=True, index=True, comment="分配记录ID，自增主键")
    crm_client_id = Column(BigInteger, ForeignKey("crm_clients.id", ondelete="CASCADE"), nullable=False, comment="关联的CRM客户ID")
    organization_id = Column(Integer, ForeignKey("organizations.id", ondelete="CASCADE"), nullable=False, comment="组织ID")
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    role = Column(String(20), nullable=False, comment="角色")
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")

    # 关联关系
    crm_client = relationship("CRMClient", back_populates="staff_assignments")
    organization = relationship("Organization", backref="crm_staff_assignments")
    user = relationship("User", backref="crm_staff_assignments")

    # 约束
    __table_args__ = (
        CheckConstraint("role IN ('market', 'sales', 'document_writer', 'submission')", name="chk_staff_role"),
    )

    def to_dict(self) -> dict:
        """
        将人员分配对象转换为字典，用于 API 响应
        """
        return {
            'id': self.id,
            'crm_client_id': self.crm_client_id,
            'organization_id': self.organization_id,
            'user_id': self.user_id,
            'role': self.role,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }