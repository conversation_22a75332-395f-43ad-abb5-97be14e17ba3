from pydantic_settings import BaseSettings
from typing import Optional
from pathlib import Path

# 获取当前文件所在目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

class Settings(BaseSettings):
    """
    应用配置类，使用 pydantic 进行环境变量和配置管理
    """
    # 项目基本信息
    PROJECT_NAME: str = "囤鼠科技教育平台"
    PROJECT_VERSION: str = "0.4.1"

    # 数据库配置
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "changeme"
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: str = "5432"
    POSTGRES_DB: str = "tunshuedu_db"

    DATABASE_URL: Optional[str] = None

    # JWT 安全配置
    SECRET_KEY: str = "change-this-secret-key-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 720  # 12小时，提升用户体验
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30     # 延长到30天
    
    # LLM API配置
    SILICONE_FLOW_API_KEY: str = "your-silicone-flow-api-key"
    ALIBABACLOUD_API_KEY_ai_selection: str = "your-alibaba-ai-selection-key"
    ALIBABACLOUD_API_KEY_bg_extraction: str = "your-alibaba-bg-extraction-key"
    ALIBABACLOUD_API_KEY_ai_augmentation: str = "your-alibaba-ai-augmentation-key"
    ALIBABACLOUD_API_KEY_ai_writing: str = "your-alibaba-ai-writing-key"
    DOUBAO_API_KEY: str = "your-doubao-api-key"

    # AI检测API配置
    ZEROGPT_API_KEY: str = "your-zerogpt-api-key"

    # Redis缓存配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379

    # 前端配置
    FRONTEND_URL: str = "http://localhost:3000"

    # 微信登录配置
    WECHAT_APP_ID: str = "your-wechat-app-id"
    WECHAT_APP_SECRET: str = "your-wechat-app-secret"
    WECHAT_REDIRECT_URI: str = "http://localhost:3000/auth/wechat/callback"

    # Redis缓存配置
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: Optional[str] = None
    CACHE_EXPIRE_TIME: int = 300  # 默认缓存5分钟

    # 环境标识
    ENVIRONMENT: str = "development"

    class Config:
        """
        配置类的设置
        """
        case_sensitive = True
        env_file = str(BASE_DIR / ".env")
        env_file_encoding = "utf-8"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 如果没有设置 DATABASE_URL，则根据其他数据库配置生成
        if not self.DATABASE_URL:
            # 使用标准连接URL，时区设置将在engine创建时通过connect_args添加
            self.DATABASE_URL = f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
        
        # 如果没有设置 REDIS_URL，则根据其他Redis配置生成
        if not self.REDIS_URL:
            if self.REDIS_PASSWORD:
                self.REDIS_URL = f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
            else:
                self.REDIS_URL = f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"

# 创建设置实例
settings = Settings()