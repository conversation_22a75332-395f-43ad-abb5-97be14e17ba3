"""
组织权限验证模块
提供基于角色的访问控制（RBAC）功能
"""

from typing import Optional, List
from fastapi import HTTPException, status, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from functools import wraps

from app.models.user import User
from app.models.organization import Organization, OrganizationMember
from app.core.dependencies import get_current_user


class OrganizationPermissionError(HTTPException):
    """组织权限错误"""
    def __init__(self, detail: str = "权限不足"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


class OrganizationNotFoundError(HTTPException):
    """组织不存在错误"""
    def __init__(self, detail: str = "组织不存在"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )


async def verify_organization_access(
    db: AsyncSession,
    user: User,
    organization_id: int,
    required_roles: Optional[List[str]] = None
) -> OrganizationMember:
    """
    验证用户对组织的访问权限

    Args:
        db: 数据库会话
        user: 当前用户
        organization_id: 组织ID
        required_roles: 需要的角色列表，如 ["owner"] 或 ["owner", "member"]

    Returns:
        OrganizationMember: 用户在组织中的成员关系

    Raises:
        OrganizationNotFoundError: 组织不存在
        OrganizationPermissionError: 权限不足
    """
    # ⚖️ 放宽：不再强制依赖“当前身份”上下文，仅以路径参数 organization_id + 成员/角色校验为准
    # 兼容旧逻辑：打印调试信息，但不以此拦截请求，避免前端必须先切换身份
    try:
        from app.core.data_isolation import DataIsolationFilter
        identity_type, current_organization_id = DataIsolationFilter.get_user_data_scope(user)
        print(
            f"🔐 权限验证(放宽) - 用户身份: {identity_type}, 当前组织: {current_organization_id}, 目标组织: {organization_id}"
        )
    except Exception:
        # 调试信息失败不影响授权流程
        pass

    # 检查组织是否存在且激活
    org_result = await db.execute(
        select(Organization).where(
            Organization.id == organization_id,
            Organization.is_active == True
        )
    )
    organization = org_result.scalars().first()
    if not organization:
        raise OrganizationNotFoundError("组织不存在或已被禁用")

    # 基于 organization_id 做成员校验（不依赖当前身份），避免前端必须切换
    member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == organization_id,
            OrganizationMember.user_id == user.id,
            OrganizationMember.is_active == True
        )
    )
    member = member_result.scalars().first()
    if not member:
        raise OrganizationPermissionError("您不是该组织的成员")

    # 检查角色权限
    if required_roles and member.role not in required_roles:
        raise OrganizationPermissionError(f"需要以下角色之一：{', '.join(required_roles)}")

    return member


async def verify_organization_owner(
    db: AsyncSession,
    user: User,
    organization_id: int
) -> OrganizationMember:
    """
    验证用户是否为组织的所有者
    
    Args:
        db: 数据库会话
        user: 当前用户
        organization_id: 组织ID
        
    Returns:
        OrganizationMember: 用户在组织中的成员关系
        
    Raises:
        OrganizationPermissionError: 权限不足
    """
    return await verify_organization_access(db, user, organization_id, ["owner"])


async def verify_organization_member(
    db: AsyncSession,
    user: User,
    organization_id: int
) -> OrganizationMember:
    """
    验证用户是否为组织成员（包括所有者）
    
    Args:
        db: 数据库会话
        user: 当前用户
        organization_id: 组织ID
        
    Returns:
        OrganizationMember: 用户在组织中的成员关系
        
    Raises:
        OrganizationPermissionError: 权限不足
    """
    return await verify_organization_access(db, user, organization_id, ["owner", "member"])


def require_personal_identity(current_user: User = Depends(get_current_user)):
    """
    要求用户当前为个人身份的依赖

    Args:
        current_user: 当前用户

    Returns:
        User: 当前用户

    Raises:
        OrganizationPermissionError: 当前不是个人身份
    """
    # 使用DataIsolationFilter获取用户当前身份
    from app.core.data_isolation import DataIsolationFilter

    identity_type, _ = DataIsolationFilter.get_user_data_scope(current_user)

    if identity_type != "personal":
        raise OrganizationPermissionError("此操作只能在个人身份下进行，请切换到个人身份")

    return current_user


def require_organization_identity(current_user: User = Depends(get_current_user)):
    """
    要求用户当前为组织身份的依赖
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前用户
        
    Raises:
        OrganizationPermissionError: 当前不是组织身份
    """
    if hasattr(current_user, '_current_identity_type'):
        if current_user._current_identity_type != "organization":
            raise OrganizationPermissionError("此操作只能在组织身份下进行")
    
    return current_user


async def get_user_organizations(db: AsyncSession, user: User) -> List[OrganizationMember]:
    """
    获取用户所属的所有组织

    Args:
        db: 数据库会话
        user: 用户对象

    Returns:
        List[OrganizationMember]: 用户的组织成员关系列表
    """
    result = await db.execute(
        select(OrganizationMember)
        .join(Organization)
        .where(
            OrganizationMember.user_id == user.id,
            OrganizationMember.is_active == True,
            Organization.is_active == True
        )
        .order_by(OrganizationMember.joined_at.desc())
    )
    return result.scalars().all()


# ==================== 工具函数 ====================

async def check_current_organization_context(
    user: User,
    required_organization_id: int
) -> None:
    """
    检查用户当前是否在正确的组织身份下
    兼容权益分配系统的接口

    Args:
        user: 用户对象
        required_organization_id: 要求的组织ID

    Raises:
        OrganizationPermissionError: 如果用户不在正确的组织身份下
    """
    from app.core.data_isolation import DataIsolationFilter

    identity_type, current_organization_id = DataIsolationFilter.get_user_data_scope(user)

    if identity_type != 'organization' or current_organization_id != required_organization_id:
        raise OrganizationPermissionError("请切换到正确的组织身份后再执行此操作")


async def get_user_organization_role(
    db: AsyncSession,
    user: User,
    organization_id: int
) -> Optional[str]:
    """
    获取用户在组织中的角色
    兼容权益分配系统的接口

    Args:
        db: 数据库会话
        user: 用户对象
        organization_id: 组织ID

    Returns:
        Optional[str]: 用户角色（owner/member），如果不是成员则返回None
    """
    try:
        # 先检查是否为组织主账号
        try:
            await verify_organization_owner(db, user, organization_id)
            return "owner"
        except (OrganizationNotFoundError, OrganizationPermissionError):
            pass

        # 再检查是否为组织成员
        try:
            await verify_organization_member(db, user, organization_id)
            return "member"
        except (OrganizationNotFoundError, OrganizationPermissionError):
            pass

        return None

    except Exception as e:
        print(f"获取用户组织角色失败: {e}")
        return None


# ==================== 统一权限服务类 ====================

class OrganizationPermissionService:
    """
    统一的组织权限控制服务
    整合了原有的两个权限服务的功能
    """

    # 布尔返回值方法（兼容权益分配系统）
    @staticmethod
    async def check_organization_owner(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> bool:
        """
        检查用户是否为组织的主账号（owner）

        Args:
            db: 数据库会话
            user: 用户对象
            organization_id: 组织ID

        Returns:
            bool: 是否为组织主账号
        """
        try:
            # 复用现有的验证逻辑
            await verify_organization_owner(db, user, organization_id)
            return True
        except (OrganizationNotFoundError, OrganizationPermissionError):
            return False
        except Exception as e:
            print(f"检查组织主账号权限失败: {e}")
            return False

    @staticmethod
    async def check_organization_member(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> bool:
        """
        检查用户是否为组织成员

        Args:
            db: 数据库会话
            user: 用户对象
            organization_id: 组织ID

        Returns:
            bool: 是否为组织成员
        """
        try:
            # 复用现有的验证逻辑
            await verify_organization_member(db, user, organization_id)
            return True
        except (OrganizationNotFoundError, OrganizationPermissionError):
            return False
        except Exception as e:
            print(f"检查组织成员权限失败: {e}")
            return False

    # 异常抛出方法（严格权限验证）
    @staticmethod
    async def require_organization_owner(
        db: AsyncSession,
        user: User,
        organization_id: int,
        operation: str = "此操作"
    ) -> None:
        """
        要求用户必须是组织主账号，否则抛出异常

        Args:
            db: 数据库会话
            user: 用户对象
            organization_id: 组织ID
            operation: 操作描述

        Raises:
            OrganizationPermissionError: 如果用户不是组织主账号
        """
        try:
            await verify_organization_owner(db, user, organization_id)
        except OrganizationPermissionError:
            raise OrganizationPermissionError(f"权限不足：{operation}只能由组织主账号执行")
        except OrganizationNotFoundError:
            raise OrganizationNotFoundError("组织不存在或已被禁用")

    @staticmethod
    async def require_organization_member(
        db: AsyncSession,
        user: User,
        organization_id: int,
        operation: str = "此操作"
    ) -> None:
        """
        要求用户必须是组织成员，否则抛出异常

        Args:
            db: 数据库会话
            user: 用户对象
            organization_id: 组织ID
            operation: 操作描述

        Raises:
            OrganizationPermissionError: 如果用户不是组织成员
        """
        try:
            await verify_organization_member(db, user, organization_id)
        except OrganizationPermissionError:
            raise OrganizationPermissionError(f"权限不足：{operation}只能由组织成员执行")
        except OrganizationNotFoundError:
            raise OrganizationNotFoundError("组织不存在或已被禁用")

    # 对象返回方法（详细信息获取）
    @staticmethod
    async def verify_organization_owner(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> OrganizationMember:
        """验证用户是否为组织的所有者，返回成员对象"""
        return await verify_organization_owner(db, user, organization_id)

    @staticmethod
    async def verify_organization_member(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> OrganizationMember:
        """验证用户是否为组织成员，返回成员对象"""
        return await verify_organization_member(db, user, organization_id)

    # 身份上下文检查
    @staticmethod
    async def check_current_organization_context(
        user: User,
        required_organization_id: int
    ) -> None:
        """检查用户当前是否在正确的组织身份下"""
        await check_current_organization_context(user, required_organization_id)

    # 角色获取
    @staticmethod
    async def get_user_organization_role(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> Optional[str]:
        """获取用户在组织中的角色"""
        return await get_user_organization_role(db, user, organization_id)

    # 工具方法
    @staticmethod
    async def get_user_organizations(
        db: AsyncSession,
        user: User
    ) -> List[OrganizationMember]:
        """获取用户所属的所有组织"""
        return await get_user_organizations(db, user)


async def check_organization_username_unique(
    db: AsyncSession,
    organization_id: int,
    username: str,
    exclude_user_id: Optional[int] = None
) -> bool:
    """
    检查组织内用户名是否唯一
    
    Args:
        db: 数据库会话
        organization_id: 组织ID
        username: 用户名
        exclude_user_id: 排除的用户ID（用于更新时检查）
        
    Returns:
        bool: 是否唯一
    """
    query = select(OrganizationMember).where(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.organization_username == username,
        OrganizationMember.is_active == True
    )
    
    if exclude_user_id:
        query = query.where(OrganizationMember.user_id != exclude_user_id)
    
    result = await db.execute(query)
    existing_member = result.scalars().first()
    return existing_member is None


def organization_permission_required(required_roles: List[str]):
    """
    组织权限装饰器
    
    Args:
        required_roles: 需要的角色列表
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取必要的依赖
            db = None
            current_user = None
            organization_id = None
            
            # 查找依赖参数
            for arg in args:
                if isinstance(arg, AsyncSession):
                    db = arg
                elif isinstance(arg, User):
                    current_user = arg
            
            # 从 kwargs 中查找 organization_id
            organization_id = kwargs.get('organization_id') or kwargs.get('org_id')
            
            if not all([db, current_user, organization_id]):
                raise OrganizationPermissionError("缺少必要的权限验证参数")
            
            # 验证权限
            await verify_organization_access(db, current_user, organization_id, required_roles)
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# 常用的权限装饰器
owner_required = organization_permission_required(["owner"])
member_required = organization_permission_required(["owner", "member"])
