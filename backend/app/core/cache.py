import asyncio
import time
import json
import logging
from typing import Any, Dict, Optional, Callable, Union
from functools import wraps
import hashlib

try:
    import aioredis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from app.core.config import settings

logger = logging.getLogger(__name__)

class RedisCache:
    """
    Redis分布式缓存实现，支持异步操作和自动序列化
    """
    
    def __init__(self, redis_url: str, default_ttl: int = 300):
        self.redis_url = redis_url
        self._default_ttl = default_ttl
        self._redis: Optional[aioredis.Redis] = None
        self._connection_pool: Optional[aioredis.ConnectionPool] = None
    
    async def _ensure_connection(self) -> aioredis.Redis:
        """确保Redis连接已建立"""
        if self._redis is None or self._redis.connection_pool.connection_kwargs.get('host') is None:
            try:
                # 创建连接池以提高性能
                self._connection_pool = aioredis.ConnectionPool.from_url(
                    self.redis_url,
                    max_connections=20,
                    retry_on_timeout=True,
                    socket_keepalive=True,
                    socket_keepalive_options={},
                    health_check_interval=30
                )
                self._redis = aioredis.Redis(connection_pool=self._connection_pool)
                # 测试连接
                await self._redis.ping()
                logger.info("Redis连接已建立")
            except Exception as e:
                logger.error(f"Redis连接失败: {e}")
                # 降级到内存缓存
                return None
        return self._redis
    
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键 - 增强安全性"""
        # 🔒 安全增强：添加salt和更强的哈希
        from app.core.config import settings
        key_data = {
            'func': func_name,
            'args': args,
            'kwargs': kwargs,
            'salt': settings.SECRET_KEY[:16]  # 使用SECRET_KEY的前16位作为盐
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        # 使用SHA-256而不是MD5，更安全
        return f"tunshu:cache:{hashlib.sha256(key_str.encode()).hexdigest()[:32]}"
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        redis = await self._ensure_connection()
        if redis is None:
            return None
            
        try:
            value = await redis.get(key)
            if value:
                return json.loads(value)
        except Exception as e:
            logger.error(f"Redis GET错误: {e}")
        return None
    
    def _serialize_value(self, value: Any) -> str:
        """序列化值，特殊处理某些对象类型"""
        def default_serializer(obj):
            # 检查是否是SQLAlchemy模型对象
            if hasattr(obj, '__table__'):
                # 对于SQLAlchemy模型，使用to_dict方法或手动转换
                if hasattr(obj, 'to_dict'):
                    return obj.to_dict()
                else:
                    # 手动提取属性
                    return {c.name: getattr(obj, c.name) for c in obj.__table__.columns}
            # 对于datetime对象
            elif hasattr(obj, 'isoformat'):
                return obj.isoformat()
            # 其他情况转为字符串
            else:
                return str(obj)

        return json.dumps(value, default=default_serializer)

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        redis = await self._ensure_connection()
        if redis is None:
            return False

        try:
            ttl = ttl or self._default_ttl
            serialized_value = self._serialize_value(value)
            await redis.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            logger.error(f"Redis SET错误: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        redis = await self._ensure_connection()
        if redis is None:
            return False
            
        try:
            await redis.delete(key)
            return True
        except Exception as e:
            logger.error(f"Redis DELETE错误: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """按模式清除缓存"""
        redis = await self._ensure_connection()
        if redis is None:
            return 0
            
        try:
            keys = await redis.keys(pattern)
            if keys:
                return await redis.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Redis CLEAR错误: {e}")
            return 0
    
    async def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        redis = await self._ensure_connection()
        if redis is None:
            return {"status": "disconnected"}
            
        try:
            info = await redis.info()
            return {
                "status": "connected",
                "used_memory": info.get("used_memory", 0),
                "connected_clients": info.get("connected_clients", 0),
                "total_commands_processed": info.get("total_commands_processed", 0)
            }
        except Exception as e:
            logger.error(f"Redis STATS错误: {e}")
            return {"status": "error", "error": str(e)}

    async def close(self):
        """关闭Redis连接"""
        if self._redis:
            await self._redis.close()
        if self._connection_pool:
            await self._connection_pool.disconnect()

class FallbackMemoryCache:
    """
    内存缓存作为Redis的降级方案
    """
    
    def __init__(self, default_ttl: int = 300):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._default_ttl = default_ttl
    
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键 - 增强安全性"""
        # 🔒 安全增强：使用更强的哈希和盐
        from app.core.config import settings
        key_data = {
            'func': func_name,
            'args': args,
            'kwargs': kwargs,
            'salt': settings.SECRET_KEY[:16]  # 使用SECRET_KEY的前16位作为盐
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.sha256(key_str.encode()).hexdigest()[:32]
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self._cache:
            cache_data = self._cache[key]
            if time.time() < cache_data['expires_at']:
                return cache_data['value']
            else:
                del self._cache[key]
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        ttl = ttl or self._default_ttl
        self._cache[key] = {
            'value': value,
            'expires_at': time.time() + ttl,
            'created_at': time.time()
        }
        return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        self._cache.pop(key, None)
        return True
    
    async def clear_pattern(self, pattern: str) -> int:
        """按模式清除缓存（简化版）"""
        pattern = pattern.replace('*', '')
        keys_to_delete = [k for k in self._cache.keys() if pattern in k]
        for key in keys_to_delete:
            del self._cache[key]
        return len(keys_to_delete)
    
    async def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        now = time.time()
        active_count = sum(1 for data in self._cache.values() if now < data['expires_at'])
        return {
            'status': 'memory',
            'total_keys': len(self._cache),
            'active_keys': active_count,
            'expired_keys': len(self._cache) - active_count
        }

# 初始化缓存实例
# if REDIS_AVAILABLE:
#     cache = RedisCache(settings.REDIS_URL, settings.CACHE_EXPIRE_TIME)
# else:
#     logger.warning("Redis不可用，使用内存缓存作为降级方案")
#     cache = FallbackMemoryCache(settings.CACHE_EXPIRE_TIME)

# 智能选择缓存方式：优先Redis，降级到内存缓存
if REDIS_AVAILABLE:
    try:
        cache = RedisCache(settings.REDIS_URL, settings.CACHE_EXPIRE_TIME)
        logger.info("使用Redis缓存")
    except Exception as e:
        logger.warning(f"Redis初始化失败，降级到内存缓存: {e}")
        cache = FallbackMemoryCache(settings.CACHE_EXPIRE_TIME)
else:
    logger.warning("Redis不可用，使用内存缓存作为降级方案")
    cache = FallbackMemoryCache(settings.CACHE_EXPIRE_TIME)


def cached(ttl: int = 300, key_prefix: str = ""):
    """
    缓存装饰器，用于缓存函数结果
    
    Args:
        ttl: 缓存时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{cache._generate_key(func.__name__, args, kwargs)}"
            
            # 尝试从缓存获取
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {func.__name__}")
                return cached_result
            
            # 执行函数并缓存结果
            logger.debug(f"缓存未命中，执行函数: {func.__name__}")
            result = await func(*args, **kwargs)
            await cache.set(cache_key, result, ttl)
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 对于同步函数，我们需要在异步环境中运行缓存操作
            async def _async_sync_wrapper():
                cache_key = f"{key_prefix}:{cache._generate_key(func.__name__, args, kwargs)}"
                
                cached_result = await cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"缓存命中: {func.__name__}")
                    return cached_result
                
                logger.debug(f"缓存未命中，执行函数: {func.__name__}")
                result = func(*args, **kwargs)
                await cache.set(cache_key, result, ttl)
                return result
            
            # 尝试在现有事件循环中运行
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，使用 run_coroutine_threadsafe
                    import concurrent.futures
                    import threading
                    
                    def run_in_thread():
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            return new_loop.run_until_complete(_async_sync_wrapper())
                        finally:
                            new_loop.close()
                    
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(run_in_thread)
                        return future.result()
                else:
                    return loop.run_until_complete(_async_sync_wrapper())
            except RuntimeError:
                # 没有事件循环，创建新的
                return asyncio.run(_async_sync_wrapper())
        
        # 根据函数是否为协程选择包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# 提供一些便捷的缓存操作函数
async def clear_cache_pattern(pattern: str = "tunshu:cache:*") -> int:
    """清空匹配模式的缓存"""
    return await cache.clear_pattern(pattern)

async def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息"""
    return await cache.stats()

async def close_cache():
    """关闭缓存连接"""
    if hasattr(cache, 'close'):
        await cache.close()

async def get_cache(key: str) -> Optional[Any]:
    """获取缓存值"""
    return await cache.get(key)

async def set_cache(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """设置缓存值"""
    return await cache.set(key, value, ttl)

# 特定模块的缓存装饰器
def cache_client_data(ttl: int = 600):
    """客户数据专用缓存装饰器，缓存时间更长（10分钟）"""
    return cached(ttl=ttl, key_prefix="client_data")

def cache_ai_selection_data(ttl: int = 1800):
    """AI选校数据专用缓存装饰器，缓存时间更长（30分钟）"""
    return cached(ttl=ttl, key_prefix="ai_selection")

def cache_user_data(ttl: int = 300):
    """
    专门用于缓存用户数据的装饰器，能正确处理User对象的序列化和反序列化
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            # 🔒 用户数据缓存安全增强：添加用户ID隔离
            user_id = ""
            if args and hasattr(args[1], 'id'):  # 通常args[1]是current_user
                user_id = f"_uid_{args[1].id}"
            cache_key = f"user_data{user_id}:{cache._generate_key(func.__name__, args, kwargs)}"

            # 尝试从缓存获取
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"用户缓存命中: {func.__name__}")
                # 如果缓存的是字典，需要重建User对象
                if isinstance(cached_result, dict) and 'id' in cached_result:
                    from app.models.user import User
                    from datetime import datetime
                    user = User(
                        id=cached_result['id'],
                        username=cached_result['username'],
                        email=cached_result['email'],
                        nickname=cached_result.get('nickname'),
                        role=cached_result.get('role', 'user'),
                        is_active=cached_result.get('is_active', True),
                        openid=cached_result.get('openid'),
                        unionid=cached_result.get('unionid'),
                        wechat_nickname=cached_result.get('wechat_nickname'),
                        avatar_url=cached_result.get('avatar_url'),
                        login_type=cached_result.get('login_type', 'password')
                    )
                    # 设置时间戳属性
                    if cached_result.get('created_at'):
                        user.created_at = datetime.fromisoformat(cached_result['created_at']) if isinstance(cached_result['created_at'], str) else cached_result['created_at']
                    if cached_result.get('updated_at'):
                        user.updated_at = datetime.fromisoformat(cached_result['updated_at']) if isinstance(cached_result['updated_at'], str) else cached_result['updated_at']
                    if cached_result.get('last_login'):
                        user.last_login = datetime.fromisoformat(cached_result['last_login']) if isinstance(cached_result['last_login'], str) else cached_result['last_login']
                    return user
                return cached_result

            # 执行函数并缓存结果
            logger.debug(f"用户缓存未命中，执行函数: {func.__name__}")
            result = await func(*args, **kwargs)
            await cache.set(cache_key, result, ttl)
            return result

        return async_wrapper
    return decorator

def cache_profile_summary(ttl: int = 900):
    """客户档案摘要专用缓存装饰器（15分钟）"""
    return cached(ttl=ttl, key_prefix="profile_summary") 