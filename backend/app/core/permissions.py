"""
权限管理模块

提供基于角色的权限验证功能，包括：
1. 权限常量定义
2. 角色权限映射
3. 权限检查装饰器
4. 权限验证依赖项
"""

from functools import wraps
from typing import List, Optional, Callable, Any
from fastapi import HTTPException, status, Depends
from app.models.user import User
from app.core.dependencies import get_current_user

# 权限常量定义
class Permissions:
    """权限常量类"""
    # 测试功能权限
    TEST_FEATURES = "test_features"
    
    # 管理员权限
    ADMIN_PANEL = "admin_panel"
    USER_MANAGEMENT = "user_management"
    SYSTEM_CONFIG = "system_config"
    
    # 开发者权限
    DEV_TOOLS = "dev_tools"
    DEBUG_MODE = "debug_mode"
    
    # 业务功能权限
    CLIENT_MANAGEMENT = "client_management"
    WRITING_TOOLS = "writing_tools"
    AI_TOOLS = "ai_tools"
    CRM_SYSTEM = "crm_system"
    SCHOOL_MATCHING = "school_matching"
    
    # 导出功能权限
    EXPORT_DOCUMENTS = "export_documents"
    
    # 高级功能权限
    ADVANCED_FEATURES = "advanced_features"

# 角色常量定义
class Roles:
    """角色常量类"""
    ADMIN = "admin"
    DEV = "dev"
    USER = "user"

# 角色权限映射
ROLE_PERMISSIONS = {
    Roles.ADMIN: [
        # 管理员拥有所有权限
        Permissions.TEST_FEATURES,
        Permissions.ADMIN_PANEL,
        Permissions.USER_MANAGEMENT,
        Permissions.SYSTEM_CONFIG,
        Permissions.DEV_TOOLS,
        Permissions.DEBUG_MODE,
        Permissions.CLIENT_MANAGEMENT,
        Permissions.WRITING_TOOLS,
        Permissions.AI_TOOLS,
        Permissions.CRM_SYSTEM,
        Permissions.SCHOOL_MATCHING,
        Permissions.EXPORT_DOCUMENTS,
        Permissions.ADVANCED_FEATURES
    ],
    
    Roles.DEV: [
        # 开发者拥有测试和开发相关权限
        Permissions.TEST_FEATURES,
        Permissions.DEV_TOOLS,
        Permissions.DEBUG_MODE,
        Permissions.CLIENT_MANAGEMENT,
        Permissions.WRITING_TOOLS,
        Permissions.AI_TOOLS,
        Permissions.CRM_SYSTEM,
        Permissions.SCHOOL_MATCHING,
        Permissions.EXPORT_DOCUMENTS
    ],
    
    Roles.USER: [
        # 普通用户只有基础业务权限
        Permissions.CLIENT_MANAGEMENT,
        Permissions.WRITING_TOOLS,
        Permissions.SCHOOL_MATCHING,
        Permissions.EXPORT_DOCUMENTS
    ]
}

def has_permission(user: User, permission: str) -> bool:
    """
    检查用户是否拥有特定权限
    
    Args:
        user: 用户对象
        permission: 权限标识
        
    Returns:
        bool: 是否拥有权限
    """
    if not user or not user.is_active:
        return False
    
    user_role = user.role or Roles.USER
    role_permissions = ROLE_PERMISSIONS.get(user_role, [])
    
    return permission in role_permissions

def has_any_permission(user: User, permissions: List[str]) -> bool:
    """
    检查用户是否拥有任意一个权限
    
    Args:
        user: 用户对象
        permissions: 权限列表
        
    Returns:
        bool: 是否拥有任意一个权限
    """
    return any(has_permission(user, permission) for permission in permissions)

def has_all_permissions(user: User, permissions: List[str]) -> bool:
    """
    检查用户是否拥有所有权限
    
    Args:
        user: 用户对象
        permissions: 权限列表
        
    Returns:
        bool: 是否拥有所有权限
    """
    return all(has_permission(user, permission) for permission in permissions)

def get_user_permissions(user: User) -> List[str]:
    """
    获取用户的所有权限
    
    Args:
        user: 用户对象
        
    Returns:
        List[str]: 权限列表
    """
    if not user or not user.is_active:
        return []
    
    user_role = user.role or Roles.USER
    return ROLE_PERMISSIONS.get(user_role, [])

def is_admin(user: User) -> bool:
    """检查是否为管理员"""
    return user and user.role == Roles.ADMIN

def is_dev(user: User) -> bool:
    """检查是否为开发者"""
    return user and user.role == Roles.DEV

def has_test_access(user: User) -> bool:
    """检查是否有测试功能访问权限"""
    return has_permission(user, Permissions.TEST_FEATURES)

# 权限验证装饰器
def require_permission(permission: str):
    """
    权限验证装饰器
    
    Args:
        permission: 所需权限
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            if not current_user:
                # 如果没有在kwargs中找到，尝试从args中获取
                for arg in args:
                    if isinstance(arg, User):
                        current_user = arg
                        break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要登录"
                )
            
            if not has_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {permission}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_any_permission(permissions: List[str]):
    """
    要求任意一个权限的装饰器
    
    Args:
        permissions: 权限列表
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                for arg in args:
                    if isinstance(arg, User):
                        current_user = arg
                        break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要登录"
                )
            
            if not has_any_permission(current_user, permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要以下任意权限: {', '.join(permissions)}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_role(role: str):
    """
    角色验证装饰器
    
    Args:
        role: 所需角色
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                for arg in args:
                    if isinstance(arg, User):
                        current_user = arg
                        break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要登录"
                )
            
            if current_user.role != role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要角色: {role}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# FastAPI依赖项
def require_permission_dependency(permission: str):
    """
    创建权限验证依赖项
    
    Args:
        permission: 所需权限
        
    Returns:
        依赖项函数
    """
    def permission_checker(current_user: User = Depends(get_current_user)):
        if not has_permission(current_user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要权限: {permission}"
            )
        return current_user
    
    return permission_checker

def require_test_access_dependency():
    """测试功能访问权限依赖项"""
    return require_permission_dependency(Permissions.TEST_FEATURES)

def require_admin_dependency():
    """管理员权限依赖项"""
    def admin_checker(current_user: User = Depends(get_current_user)):
        if not is_admin(current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        return current_user
    
    return admin_checker
