"""
数据隔离中间件
确保不同身份（个人/组织）的数据完全隔离，权益不相通
"""

from typing import Optional, Union, Tuple
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import Select, Update, Delete
from sqlalchemy import and_, or_

from app.models.user import User
from app.models.client import Client


class DataIsolationFilter:
    """数据隔离过滤器"""
    
    @staticmethod
    def get_user_data_scope(current_user: User) -> Tuple[str, Optional[int]]:
        """
        获取用户的数据范围
        
        Args:
            current_user: 当前用户（包含身份信息）
            
        Returns:
            Tuple[str, Optional[int]]: (身份类型, 组织ID)
        """
        # 从用户对象获取身份信息（这些信息来自JWT token）
        identity_type = getattr(current_user, '_current_identity_type', 'personal')
        organization_id = getattr(current_user, '_current_organization_id', None)
        
        # 🔧 修复：确保组织身份的有效性验证
        if identity_type == 'organization' and organization_id is None:
            # 如果声明是组织身份但没有组织ID，回退到个人身份
            identity_type = 'personal'
            print(f"⚠️ 身份修正：用户{current_user.id}声明组织身份但缺少组织ID，回退到个人身份")
        
        # 🔧 修复：增加调试信息，便于排查问题
        if hasattr(current_user, '_debug_identity_check'):
            print(f"🔍 身份检查：用户{current_user.id} -> 身份类型:{identity_type}, 组织ID:{organization_id}")
        
        return identity_type, organization_id
    
    @staticmethod
    async def apply_client_filter_async(
        query: Union[Select, Update, Delete],
        current_user: User,
        db: AsyncSession
    ) -> Union[Select, Update, Delete]:
        """异步版本：支持组织Owner放宽为“全组织范围”。
        - 组织身份 + Owner: 仅限制 organization_id
        - 组织身份 + Member: 限制 user_id + organization_id
        - 个人身份: 限制 user_id 且 organization_id 为空/0
        """
        identity_type, organization_id = DataIsolationFilter.get_user_data_scope(current_user)

        if identity_type == 'organization' and organization_id:
            try:
                # 检查是否为组织Owner
                from app.core.organization_permissions import verify_organization_owner
                await verify_organization_owner(db, current_user, organization_id)
                # Owner：全组织可见
                return query.where(
                    Client.organization_id == organization_id
                )
            except Exception:
                # 非Owner（成员）：仅自己名下，且在该组织内
                return query.where(
                    and_(
                        Client.user_id == current_user.id,
                        Client.organization_id == organization_id
                    )
                )
        # 个人身份：仅个人客户（无组织）
        return query.where(
            and_(
                Client.user_id == current_user.id,
                or_(Client.organization_id.is_(None), Client.organization_id == 0)
            )
        )


    @staticmethod
    def validate_data_access(
        current_user: User,
        resource_user_id: int,
        resource_organization_id: Optional[int] = None
    ) -> bool:
        """
        验证用户是否有权访问特定数据
        
        Args:
            current_user: 当前用户
            resource_user_id: 资源所属用户ID
            resource_organization_id: 资源所属组织ID
            
        Returns:
            bool: 是否有访问权限
        """
        identity_type, current_organization_id = DataIsolationFilter.get_user_data_scope(current_user)
        
        if identity_type == 'organization' and current_organization_id:
            # 组织身份：只能访问该组织的数据
            return (
                resource_organization_id == current_organization_id and
                resource_user_id == current_user.id
            )
        else:
            # 个人身份：只能访问个人数据（非组织数据）
            return (
                resource_user_id == current_user.id and
                resource_organization_id is None
            )
    
    @staticmethod
    def get_data_scope_description(current_user: User) -> str:
        """
        获取当前数据范围的描述
        
        Args:
            current_user: 当前用户
            
        Returns:
            str: 数据范围描述
        """
        identity_type, organization_id = DataIsolationFilter.get_user_data_scope(current_user)
        
        if identity_type == 'organization' and organization_id:
            return f"组织身份 (组织ID: {organization_id})"
        else:
            return "个人身份"


class DataIsolationMixin:
    """数据隔离混入类，为API端点提供数据隔离功能"""
    
    @staticmethod
    def validate_resource_access(
        current_user: User,
        resource_user_id: int,
        resource_organization_id: Optional[int] = None
    ) -> None:
        """
        验证资源访问权限，不通过则抛出异常
        
        Args:
            current_user: 当前用户
            resource_user_id: 资源所属用户ID
            resource_organization_id: 资源所属组织ID
            
        Raises:
            HTTPException: 如果没有访问权限
        """
        if not DataIsolationFilter.validate_data_access(
            current_user, resource_user_id, resource_organization_id
        ):
            identity_desc = DataIsolationFilter.get_data_scope_description(current_user)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足：当前身份 ({identity_desc}) 无法访问此资源"
            )


# 便捷函数
def get_current_data_scope(current_user: User) -> dict:
    """
    获取当前用户的数据范围信息
    
    Args:
        current_user: 当前用户
        
    Returns:
        dict: 数据范围信息
    """
    identity_type, organization_id = DataIsolationFilter.get_user_data_scope(current_user)
    
    return {
        "identity_type": identity_type,
        "organization_id": organization_id,
        "description": DataIsolationFilter.get_data_scope_description(current_user),
        "user_id": current_user.id
    }
