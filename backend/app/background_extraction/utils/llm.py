"""
LLM (Large Language Model) 功能实现
使用实际大模型API进行文本生成和分析
"""

from typing import Dict, Any, List, Tuple
import requests
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

from app.background_extraction.config import SILICONE_FLOW_API_KEY, ALIBABACLOUD_API_KEY_bg_extraction

# 创建并发限制信号量，避免API过载
API_SEMAPHORE = asyncio.Semaphore(5)  # 降低到5个并发请求，避免API限流

# 线程池执行器，用于同步API调用的异步化
executor = ThreadPoolExecutor(max_workers=5)  # 同样降低线程池大小

async def process_text_with_api_async(prompt: str) -> str:
    """
    异步调用大模型API处理文本，并返回结果
    
    Args:
        prompt: 提示词
        
    Returns:
        模型生成的回复
    """
    async with API_SEMAPHORE:  # 限制并发数量
        loop = asyncio.get_event_loop()
        # 在线程池中执行同步API调用
        try:
            result = await loop.run_in_executor(executor, process_text_with_api, prompt)
            return result
        except Exception as e:
            print(f"异步API调用失败: {e}")
            return "API调用失败，请稍后重试。"

def process_text_with_api(prompt: str) -> str:
    """
    调用大模型API处理文本，并返回结果（同步版本，仅供线程池调用）
    
    Args:
        prompt: 提示词
        
    Returns:
        模型生成的回复
    """
    # url = "https://api.siliconflow.cn/v1/chat/completions"

    # payload = {
    #     "model": "Qwen/Qwen3-14B",
    #     "messages": [
    #         {
    #             "role": "user",
    #             "content": prompt
    #         }
    #     ],
    #     "stream": False,
    #     "max_tokens": 512,
    #     "stop": None,
    #     "temperature": 0.3,
    #     "top_p": 0.7,
    #     "top_k": 50,
    #     "frequency_penalty": 0.5,
    #     "chat_template_kwargs": {"enable_thinking": False},
    #     "n": 1,
    #     "response_format": {"type": "text"}
    # }
    # headers = {
    #     "Authorization": f"Bearer {SILICONE_FLOW_API_KEY}",
    #     "Content-Type": "application/json"
    # }

    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"

    payload = {
        "model": "qwen-turbo",
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "stream": False,
        "max_tokens": 16384,
        "stop": None,
        "temperature": 0.2,
        "top_p": 0.6,
        "top_k": 30,
        "enable_thinking": False,
        "n": 1,
        "response_format": {"type": "text"}
    }
    headers = {
        "Authorization": f"Bearer {ALIBABACLOUD_API_KEY_bg_extraction}",
        "Content-Type": "application/json"
    }

    try:
        # 添加超时设置：连接超时5秒，读取超时60秒
        response = requests.request("POST", url, json=payload, headers=headers, timeout=(5, 60))
        
        # 检查HTTP状态码
        if response.status_code != 200:
            print(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return "API调用失败，请稍后重试。"
        
        # 检查响应内容是否为空
        if not response.text.strip():
            print("API返回空响应")
            return "API调用失败，请稍后重试。"
        
        # 尝试解析JSON
        try:
            response_json = response.json()
        except ValueError as json_error:
            print(f"JSON解析失败: {json_error}, 响应内容: {response.text[:200]}")
            return "API调用失败，请稍后重试。"
        
        # 检查响应结构
        if not response_json.get("choices"):
            print(f"API响应格式异常: {response_json}")
            return "API调用失败，请稍后重试。"
        
        content = response_json.get("choices")[0].get("message", {}).get("content", "")
        if not content:
            print("API返回空内容")
            return "API调用失败，请稍后重试。"
        
        return content
    except requests.exceptions.Timeout:
        print("API调用超时")
        return "API调用超时，请稍后重试。"
    except requests.exceptions.ConnectionError:
        print("API连接失败")
        return "网络连接失败，请检查网络后重试。"
    except Exception as e:
        print(f"API调用异常: {e}")
        return "API调用失败，请稍后重试。"