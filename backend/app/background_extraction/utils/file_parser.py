import io
from fastapi import UploadFile
import docx
from pypdf import PdfReader
import logging

logger = logging.getLogger(__name__)

async def parse_file(file: UploadFile) -> str:
    """
    根据文件类型解析上传的文件，并提取纯文本内容。

    Args:
        file: FastAPI的UploadFile对象，包含文件名和文件内容。

    Returns:
        从文件中提取的纯文本字符串。

    Raises:
        ValueError: 如果文件类型不支持。
    """
    filename = file.filename.lower()
    content = await file.read()
    
    try:
        if filename.endswith('.pdf'):
            return _parse_pdf(content)
        elif filename.endswith('.docx'):
            return _parse_docx(content)
        elif filename.endswith('.txt'):
            return _parse_txt(content)
        else:
            logger.warning(f"不支持的文件类型: {filename}")
            raise ValueError(f"不支持的文件类型: {filename}。目前仅支持 .pdf, .docx, .txt 文件。")
    except Exception as e:
        logger.error(f"解析文件 '{filename}' 时发生错误: {e}", exc_info=True)
        raise ValueError(f"解析文件 '{filename}' 失败: {str(e)}")

def _parse_pdf(content: bytes) -> str:
    """解析PDF文件内容。"""
    text = ""
    with io.BytesIO(content) as f:
        reader = PdfReader(f)
        for page in reader.pages:
            text += page.extract_text() or ""
    logger.info("PDF文件解析完成。")
    return text

def _parse_docx(content: bytes) -> str:
    """解析DOCX文件内容，包括段落和表格。"""
    text_content = []
    
    with io.BytesIO(content) as f:
        document = docx.Document(f)
        
        # 获取所有段落和表格的副本，用于按顺序处理
        paragraphs = document.paragraphs.copy()
        tables = document.tables.copy()
        
        # 按照文档中的实际顺序处理元素
        for element in document.element.body:
            if hasattr(element, "tag"):
                if isinstance(element.tag, str) and element.tag.endswith("p"):  # 段落
                    if paragraphs:
                        para = paragraphs.pop(0)
                        parsed_paragraph = _parse_paragraph(para)
                        if parsed_paragraph.strip():
                            text_content.append(parsed_paragraph)
                        else:
                            text_content.append("\n")
                elif isinstance(element.tag, str) and element.tag.endswith("tbl"):  # 表格
                    if tables:
                        table = tables.pop(0)
                        table_content = _table_to_markdown(table)
                        if table_content.strip():
                            text_content.append(table_content)
                            text_content.append("\n")  # 表格后添加换行
    
    logger.info("DOCX文件解析完成（包含表格内容）。")
    return "\n".join(text_content)

def _parse_paragraph(paragraph) -> str:
    """解析段落内容。"""
    paragraph_content = []
    for run in paragraph.runs:
        if run.text.strip():
            paragraph_content.append(run.text.strip())
    return " ".join(paragraph_content) if paragraph_content else ""

def _table_to_markdown(table) -> str:
    """将表格转换为Markdown格式。"""
    if not table.rows:
        return ""
    
    markdown = []
    # 计算总列数
    total_cols = max(len(row.cells) for row in table.rows) if table.rows else 0
    
    if total_cols == 0:
        return ""
    
    # 处理表头
    if table.rows:
        header_row = table.rows[0]
        headers = _parse_row(header_row, total_cols)
        markdown.append("| " + " | ".join(headers) + " |")
        markdown.append("| " + " | ".join(["---"] * total_cols) + " |")
        
        # 处理数据行
        for row in table.rows[1:]:
            row_cells = _parse_row(row, total_cols)
            markdown.append("| " + " | ".join(row_cells) + " |")
    
    return "\n".join(markdown)

def _parse_row(row, total_cols: int) -> list[str]:
    """解析表格行，处理单元格合并。"""
    # 初始化行，默认都为空
    row_cells = [""] * total_cols
    col_index = 0
    
    for cell in row.cells:
        # 确保col_index不超出范围，跳过已填充的单元格
        while col_index < total_cols and row_cells[col_index] != "":
            col_index += 1
        
        # 如果col_index超出范围则跳出循环
        if col_index >= total_cols:
            break
            
        cell_content = _parse_cell(cell).strip()
        # 处理单元格跨列（如果有的话）
        cell_colspan = getattr(cell, 'grid_span', None) or 1
        
        for i in range(cell_colspan):
            if col_index + i < total_cols:
                row_cells[col_index + i] = cell_content if i == 0 else ""
        
        col_index += cell_colspan
    
    return row_cells

def _parse_cell(cell) -> str:
    """解析单元格内容。"""
    cell_content = []
    for paragraph in cell.paragraphs:
        parsed_paragraph = _parse_cell_paragraph(paragraph)
        if parsed_paragraph:
            cell_content.append(parsed_paragraph)
    
    # 去重并保持顺序
    unique_content = list(dict.fromkeys(cell_content))
    return " ".join(unique_content)

def _parse_cell_paragraph(paragraph) -> str:
    """解析单元格中的段落。"""
    paragraph_content = []
    for run in paragraph.runs:
        if run.text.strip():
            paragraph_content.append(run.text.strip())
    return " ".join(paragraph_content) if paragraph_content else ""

def _parse_txt(content: bytes) -> str:
    """解析TXT文件内容。"""
    logger.info("TXT文件解析完成。")
    return content.decode('utf-8')
