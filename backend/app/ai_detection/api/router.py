"""
AI检测路由器
"""

from fastapi import APIRouter
from .endpoints import router as endpoints_router
from .humanizer_endpoints import router as humanizer_router

# 创建主路由器
router = APIRouter(
    prefix="/ai-detection",
    tags=["AI检测"],
    responses={
        404: {"description": "未找到"},
        500: {"description": "服务器内部错误"}
    }
)

# 包含所有端点
router.include_router(endpoints_router) 
router.include_router(humanizer_router)