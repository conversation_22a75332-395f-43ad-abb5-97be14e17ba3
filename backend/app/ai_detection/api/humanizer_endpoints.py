"""AI Humanizer API Endpoints - 降重/人性化改写 API"""
from fastapi import APIRouter, status, HTTPException

from app.ai_detection.schemas.humanizer import (
    HumanizeRequest,
    HumanizeResponse,
    HumanizeBatchRequest,
    HumanizeBatchResponse,
    HumanizeBatchItem
)
from app.ai_detection.core.humanizer import humanize_text_once
from app.core.dependencies import CurrentUser, DBSession
from app.ai_detection.utils.credit_utils import calc_humanize_credits
from app.credit_payment.services.credit_service import CreditService
from app.services.token_billing import TokenBillingService


router = APIRouter()


@router.post(
    "/humanize",
    response_model=HumanizeResponse,
    status_code=status.HTTP_200_OK,
    summary="AI降重（单文本）",
    description="检测AI痕迹并对高亮句子进行人性化改写，返回改写后的完整文本"
)
async def humanize_text(request: HumanizeRequest, db: DBSession, current_user: CurrentUser) -> HumanizeResponse:
    # 积分计算与扣除
    required = calc_humanize_credits(request.text)
    request_id = TokenBillingService.generate_request_id()

    balance = await CreditService.get_credit_balance(current_user, db)
    if balance < required:
        raise HTTPException(status_code=status.HTTP_402_PAYMENT_REQUIRED, detail={
            "error": "insufficient_credits",
            "message": f"积分不足，降AI率需要{required}积分，当前余额：{balance}",
            "required": required,
            "balance": balance
        })

    ok, err = await CreditService.consume_credits(
        user=current_user,
        credits=required,
        service_type="ai_detection",
        operation_type="humanize",
        request_id=request_id,
        description="降AI率服务扣费",
        db=db
    )
    if not ok:
        raise HTTPException(status_code=status.HTTP_402_PAYMENT_REQUIRED, detail={
            "error": "credit_deduction_failed",
            "message": f"积分扣除失败: {err}",
            "required": required,
            "balance": balance
        })

    return await humanize_text_once(
        text=request.text,
        language=request.language,
        highlighted_sentences=request.highlighted_sentences,
        highlighted_sentences_raw=request.highlighted_sentences_raw,
    )


@router.post(
    "/humanize-batch",
    response_model=HumanizeBatchResponse,
    status_code=status.HTTP_200_OK,
    summary="AI降重（批量）",
    description="对多段文本执行降重，逐条返回结果"
)
async def humanize_text_batch(request: HumanizeBatchRequest, db: DBSession, current_user: CurrentUser) -> HumanizeBatchResponse:
    # 按整批计费，一次扣费
    total_text = "\n".join(request.texts or [])
    required = calc_humanize_credits(total_text)
    request_id = TokenBillingService.generate_request_id()

    balance = await CreditService.get_credit_balance(current_user, db)
    if balance < required:
        raise HTTPException(status_code=status.HTTP_402_PAYMENT_REQUIRED, detail={
            "error": "insufficient_credits",
            "message": f"积分不足，批量降AI率需要{required}积分，当前余额：{balance}",
            "required": required,
            "balance": balance
        })

    ok, err = await CreditService.consume_credits(
        user=current_user,
        credits=required,
        service_type="ai_detection",
        operation_type="humanize_batch",
        request_id=request_id,
        description="批量降AI率服务扣费",
        db=db
    )
    if not ok:
        raise HTTPException(status_code=status.HTTP_402_PAYMENT_REQUIRED, detail={
            "error": "credit_deduction_failed",
            "message": f"积分扣除失败: {err}",
            "required": required,
            "balance": balance
        })

    results: list[HumanizeBatchItem] = []
    succeeded = 0
    for idx, text in enumerate(request.texts):
        try:
            resp = await humanize_text_once(text, request.language)
            results.append(HumanizeBatchItem(index=idx, success=True, response=resp))
            succeeded += 1
        except Exception as e:
            results.append(HumanizeBatchItem(index=idx, success=False, error=str(e)))

    return HumanizeBatchResponse(
        total=len(request.texts),
        succeeded=succeeded,
        failed=len(request.texts) - succeeded,
        results=results
    )


