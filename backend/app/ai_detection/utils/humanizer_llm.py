"""
AI Humanizer LLM 工具
- 参考 ai_writing/utils/llm.py 的豆包API流式/非流式实现
- 这里封装一个改写调用：输入句子列表，输出逐句改写结果
"""
from typing import List, AsyncGenerator
import httpx
import asyncio
import json
import logging
import json_repair

from app.ai_writing.config import DOUBAO_API_KEY

logger = logging.getLogger(__name__)

API_SEMAPHORE = asyncio.Semaphore(5)
HTTP_CLIENT = httpx.AsyncClient(timeout=httpx.Timeout(5.0, read=120.0))


def build_humanize_prompt(sentences: List[str], language: str | None = None) -> str:
    lang = (language or "auto").lower()
    task_lang = "中文" if lang in ("cn", "zh", "chinese") else ("英文" if lang in ("en", "english") else "原文语言")
    joined = "\n".join([f"- {s}" for s in sentences])
    return f"""
用自己的话说一遍，替换句子中的专业词汇为人类自然对话词汇，同时保持专业性，不额外添加内容，不允许拆句。
语言要求：{task_lang}
格式要求：输出与输入一一对应的改写结果，严格使用JSON数组格式（不包含代码块标记）：["句子1改写", "句子2改写", ...]。

待改写句子：
{joined}
""".strip()


async def humanize_sentences(sentences: List[str], language: str | None = None) -> List[str]:
    """使用豆包模型对句子进行人性化改写（非流式，一次返回JSON数组）。"""
    if not sentences:
        return []

    system_prompt = "你是一位资深的人类写作者与文字编辑，专注于去AI化的人性化改写。"
    prompt = build_humanize_prompt(sentences, language)

    async with API_SEMAPHORE:
        url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        payload = {
            "model": "ep-20250808170149-7zck8",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "stream": False,
            "thinking": {"type": "disabled"},
            "max_tokens": 4096,
            "temperature": 0.4,
            "top_p": 0.8,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }
        headers = {
            "Authorization": f"Bearer {DOUBAO_API_KEY}",
            "Content-Type": "application/json"
        }

        try:
            resp = await HTTP_CLIENT.post(url, json=payload, headers=headers)
            resp.raise_for_status()
            data = resp.json()
            content = data["choices"][0]["message"]["content"]
            # 尝试解析为JSON数组
            content = content.strip()
            if content.startswith("```"):
                # 容错：去掉代码块包装
                content = content.strip("`\n ")
            revised_list = json_repair.loads(content)
            if isinstance(revised_list, list):
                return [str(x) for x in revised_list]
            logger.warning("Humanizer返回非列表JSON，原始内容: %s", content[:200])
            return []
        except Exception as e:
            logger.error("Humanizer豆包调用失败: %s", e, exc_info=True)
            return []


