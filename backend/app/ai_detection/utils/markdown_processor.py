"""
Markdown格式保留处理器 - 混合模式实现
智能检测Markdown复杂度，使用分层处理策略：
- 简单格式：轻量级正则处理
- 复杂格式：AST解析处理
- 错误场景：优雅降级
"""
import re
import logging
from typing import Dict, List, Tuple, Optional, Union
from enum import Enum

logger = logging.getLogger(__name__)


class MarkdownComplexity(Enum):
    """Markdown复杂度级别"""
    SIMPLE = "simple"       # 简单内联格式
    MODERATE = "moderate"   # 中等复杂度（列表、引用等）
    COMPLEX = "complex"     # 复杂结构（表格、代码块、嵌套等）


class HybridMarkdownProcessor:
    """混合模式Markdown处理器"""
    
    # 简单内联格式模式
    SIMPLE_INLINE_PATTERNS = {
        'bold_double': r'\*\*([^*]+?)\*\*',
        'bold_single': r'\*([^*\n]+?)\*',
        'italic_underscore': r'_([^_\n]+?)_',
        'code_inline': r'`([^`\n]+?)`',
        'strikethrough': r'~~([^~\n]+?)~~',
    }
    
    # 复杂结构检测模式
    COMPLEX_PATTERNS = {
        'table': r'\|.*\|.*\n\|[-:]+\|',
        'code_block': r'```[\s\S]*?```',
        'nested_list': r'^\s{2,}[-*+]\s',
        'definition_list': r'^\s*\w+\s*\n\s*:\s',
        'footnote': r'\[\^[^\]]+\]',
        'html_tag': r'<[^>]+>',
        'math_block': r'\$\$[\s\S]*?\$\$',
    }
    
    # 中等复杂度模式
    MODERATE_PATTERNS = {
        'header': r'^#{1,6}\s+',
        'list_unordered': r'^\s*[-*+]\s+',
        'list_ordered': r'^\s*\d+\.\s+',
        'blockquote': r'^\s*>\s+',
        'horizontal_rule': r'^[-*_]{3,}',
        'link': r'\[([^\]]+?)\]\([^)]+?\)',
        'image': r'!\[([^\]]*?)\]\([^)]+?\)',
    }
    
    def __init__(self):
        self._complexity_cache = {}  # 缓存复杂度检测结果
    
    def detect_complexity(self, markdown_text: str) -> MarkdownComplexity:
        """
        检测Markdown文本的复杂度级别
        
        Returns:
            MarkdownComplexity: 复杂度级别
        """
        # 检查缓存
        text_hash = hash(markdown_text)
        if text_hash in self._complexity_cache:
            return self._complexity_cache[text_hash]
        
        complexity = MarkdownComplexity.SIMPLE
        
        # 检测复杂结构
        for pattern_name, pattern in self.COMPLEX_PATTERNS.items():
            if re.search(pattern, markdown_text, re.MULTILINE | re.DOTALL):
                logger.debug(f"检测到复杂结构: {pattern_name}")
                complexity = MarkdownComplexity.COMPLEX
                break
        
        # 如果不是复杂结构，检测中等复杂度
        if complexity == MarkdownComplexity.SIMPLE:
            moderate_count = 0
            for pattern_name, pattern in self.MODERATE_PATTERNS.items():
                if re.search(pattern, markdown_text, re.MULTILINE):
                    moderate_count += 1
                    if moderate_count >= 2:  # 两个或以上中等特征
                        complexity = MarkdownComplexity.MODERATE
                        break
        
        # 缓存结果
        self._complexity_cache[text_hash] = complexity
        logger.debug(f"文本复杂度: {complexity.value}")
        return complexity
    
    def smart_sentence_replace(self, markdown_text: str, sentence_replacements: Dict[str, str]) -> str:
        """
        智能句子替换，根据复杂度选择处理策略
        
        Args:
            markdown_text: 原始Markdown文本
            sentence_replacements: 句子替换映射
        
        Returns:
            替换后的Markdown文本
        """
        if not sentence_replacements:
            return markdown_text
        
        complexity = self.detect_complexity(markdown_text)
        
        try:
            if complexity == MarkdownComplexity.SIMPLE:
                return self._simple_regex_replace(markdown_text, sentence_replacements)
            elif complexity == MarkdownComplexity.MODERATE:
                return self._moderate_smart_replace(markdown_text, sentence_replacements)
            else:  # COMPLEX
                return self._complex_ast_replace(markdown_text, sentence_replacements)
                
        except Exception as e:
            logger.error(f"Markdown处理失败，使用降级策略: {e}")
            return self._fallback_replace(markdown_text, sentence_replacements)
    
    def _simple_regex_replace(self, text: str, replacements: Dict[str, str]) -> str:
        """
        简单正则替换策略
        适用于只包含基本内联格式的文本
        """
        logger.debug("使用简单正则替换策略")
        result = text
        
        for original, replacement in replacements.items():
            # 直接替换
            if original in result:
                result = result.replace(original, replacement, 1)
                continue
            
            # 构建灵活的匹配模式，允许内联格式干扰
            flexible_pattern = self._build_flexible_pattern(original)
            if flexible_pattern:
                try:
                    result = re.sub(flexible_pattern, replacement, result, count=1, flags=re.DOTALL)
                except re.error:
                    # 正则失败，回退到简单替换
                    result = result.replace(original, replacement, 1)
        
        return result
    
    def _moderate_smart_replace(self, text: str, replacements: Dict[str, str]) -> str:
        """
        中等复杂度智能替换策略
        考虑列表、引用等结构化元素
        """
        logger.debug("使用中等复杂度智能替换策略")
        
        # 分行处理，保持结构
        lines = text.split('\n')
        result_lines = []
        
        for line in lines:
            modified_line = line
            
            # 检查是否是结构化行（标题、列表等）
            is_structural = any(
                re.match(pattern, line) 
                for pattern in self.MODERATE_PATTERNS.values()
            )
            
            if is_structural:
                # 对结构化行进行特殊处理
                modified_line = self._replace_in_structural_line(line, replacements)
            else:
                # 普通行使用简单替换
                for original, replacement in replacements.items():
                    if original in modified_line:
                        modified_line = modified_line.replace(original, replacement, 1)
            
            result_lines.append(modified_line)
        
        return '\n'.join(result_lines)
    
    def _complex_ast_replace(self, text: str, replacements: Dict[str, str]) -> str:
        """
        复杂结构AST替换策略
        使用Markdown解析器进行精确处理
        """
        logger.debug("使用复杂结构AST替换策略")
        
        try:
            # 尝试使用markdown库进行AST处理
            return self._ast_based_replace(text, replacements)
        except ImportError:
            logger.warning("markdown库不可用，降级到正则处理")
            return self._moderate_smart_replace(text, replacements)
        except Exception as e:
            logger.error(f"AST处理失败: {e}")
            return self._fallback_replace(text, replacements)
    
    def _fallback_replace(self, text: str, replacements: Dict[str, str]) -> str:
        """
        降级替换策略
        最保守的替换方法，确保不会破坏文本
        """
        logger.debug("使用降级替换策略")
        result = text
        
        for original, replacement in replacements.items():
            # 只进行最安全的直接替换
            if original in result:
                result = result.replace(original, replacement, 1)
        
        return result
    
    def _build_flexible_pattern(self, sentence: str) -> Optional[str]:
        """
        构建灵活的匹配模式，允许内联格式干扰
        """
        words = sentence.strip().split()
        if len(words) < 2:
            return None
        
        pattern_parts = []
        for word in words:
            escaped_word = re.escape(word)
            # 允许单词前后有内联格式标记
            word_pattern = f"(?:\\*\\*|\\*|_|`|~~)*{escaped_word}(?:\\*\\*|\\*|_|`|~~)*"
            pattern_parts.append(word_pattern)
        
        # 单词之间允许空格和少量格式标记
        return r'\s+'.join(pattern_parts)
    
    def _replace_in_structural_line(self, line: str, replacements: Dict[str, str]) -> str:
        """
        在结构化行中进行替换
        保留行的结构标记
        """
        # 提取行的结构前缀（如 "# ", "- ", "> " 等）
        structure_match = re.match(r'^(\s*(?:#{1,6}\s+|[-*+]\s+|\d+\.\s+|>\s+))', line)
        
        if structure_match:
            prefix = structure_match.group(1)
            content = line[len(prefix):]
            
            # 只在内容部分进行替换
            for original, replacement in replacements.items():
                if original in content:
                    content = content.replace(original, replacement, 1)
            
            return prefix + content
        else:
            # 非结构化行，直接替换
            result = line
            for original, replacement in replacements.items():
                if original in result:
                    result = result.replace(original, replacement, 1)
            return result
    
    def _ast_based_replace(self, text: str, replacements: Dict[str, str]) -> str:
        """
        基于AST的精确替换
        使用markdown库构建AST，精确定位和替换文本节点
        """
        try:
            import markdown  # type: ignore
            from markdown.extensions import codehilite, tables  # type: ignore
            from xml.etree import ElementTree as ET
            import re
            
            # 创建Markdown解析器
            md = markdown.Markdown(extensions=['codehilite', 'tables', 'fenced_code'])
            
            # 解析为HTML AST
            html = md.convert(text)
            
            # 使用自定义的AST替换算法
            return self._replace_text_nodes_in_markdown(text, replacements)
            
        except ImportError:
            # 如果没有markdown库，回退到中等复杂度处理
            return self._moderate_smart_replace(text, replacements)
        except Exception as e:
            logging.warning(f"AST处理失败: {e}, 回退到中等复杂度处理")
            return self._moderate_smart_replace(text, replacements)
    
    def _replace_text_nodes_in_markdown(self, text: str, replacements: Dict[str, str]) -> str:
        """
        在Markdown文本中精确替换文本节点，保持所有格式不变
        
        这个方法直接操作Markdown文本，而不是转换为HTML
        使用更精确的正则匹配来定位纯文本部分
        """
        import re
        
        # 先备份原始文本
        result = text
        
        # 定义Markdown结构的正则模式
        patterns = {
            'code_block': r'```[\s\S]*?```',  # 代码块
            'inline_code': r'`[^`]+`',        # 行内代码
            'link': r'\[([^\]]*)\]\([^\)]*\)', # 链接
            'image': r'!\[([^\]]*)\]\([^\)]*\)', # 图片
            'bold_italic': r'\*\*\*([^*]+)\*\*\*', # 粗斜体
            'bold': r'\*\*([^*]+)\*\*',        # 粗体
            'italic': r'\*([^*]+)\*',          # 斜体
            'strikethrough': r'~~([^~]+)~~',   # 删除线
        }
        
        # 收集所有需要保护的区域
        protected_regions = []
        
        for pattern_name, pattern in patterns.items():
            for match in re.finditer(pattern, result):
                protected_regions.append({
                    'start': match.start(),
                    'end': match.end(),
                    'content': match.group(),
                    'type': pattern_name
                })
        
        # 按位置排序
        protected_regions.sort(key=lambda x: x['start'])
        
        # 合并重叠的区域
        merged_regions = self._merge_overlapping_regions(protected_regions)
        
        # 执行替换，避开保护区域
        offset = 0
        for original, replacement in replacements.items():
            if not original.strip():
                continue
                
            # 在非保护区域中查找并替换
            new_result = ""
            last_pos = 0
            
            for match in re.finditer(re.escape(original), result[offset:]):
                match_start = match.start() + offset
                match_end = match.end() + offset
                
                # 检查是否在保护区域内
                in_protected = any(
                    region['start'] <= match_start < region['end'] or 
                    region['start'] < match_end <= region['end']
                    for region in merged_regions
                )
                
                if not in_protected:
                    # 安全替换
                    new_result = (result[:match_start] + 
                                replacement + 
                                result[match_end:])
                    result = new_result
                    # 更新偏移量
                    offset = match_start + len(replacement)
                    break
        
        return result
    
    def _merge_overlapping_regions(self, regions: List[Dict]) -> List[Dict]:
        """
        合并重叠的保护区域
        """
        if not regions:
            return []
        
        merged = [regions[0]]
        
        for current in regions[1:]:
            last = merged[-1]
            
            # 如果当前区域与最后一个区域重叠或相邻
            if current['start'] <= last['end']:
                # 合并区域
                merged[-1] = {
                    'start': last['start'],
                    'end': max(last['end'], current['end']),
                    'content': last['content'],  # 保持第一个区域的内容
                    'type': f"{last['type']}_merged"
                }
            else:
                merged.append(current)
        
        return merged
    

# 保持向后兼容的简化类
class MarkdownFormatPreserver:
    """
    向后兼容的Markdown格式保留器
    实际上是HybridMarkdownProcessor的简化接口
    """
    
    def __init__(self):
        self.processor = HybridMarkdownProcessor()
    
    def smart_sentence_replace(self, markdown_text: str, sentence_replacements: Dict[str, str]) -> str:
        """智能句子替换，保留Markdown格式"""
        return self.processor.smart_sentence_replace(markdown_text, sentence_replacements)