import re

# 计费规则（更新）：
# - 查AI率：每 500 英文词 = 1 积分，向上取整，最少 1 分
# - 降AI率：每 50 英文词 = 1 积分，向上取整，最少 1 分

_WORD_RE = re.compile(r"[A-Za-z]+(?:'[A-Za-z]+)?")


def count_english_words(text: str) -> int:
    if not text:
        return 0
    return len(_WORD_RE.findall(text))


def calc_detection_credits(text: str) -> int:
    words = count_english_words(text)
    blocks = (words + 500 - 1) // 500  # ceil(words / 500)
    return max(1, blocks)


def calc_humanize_credits(text: str) -> int:
    words = count_english_words(text)
    blocks = (words + 50 - 1) // 50    # ceil(words / 50)
    return max(1, blocks)

