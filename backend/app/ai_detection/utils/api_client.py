"""ZeroGPT API Client - 封装ZeroGPT API调用逻辑"""
import json
import asyncio
import aiohttp
from typing import Dict, Any, Optional
from fastapi import HTTPException
import logging

from app.ai_detection.config import (
    ZEROGPT_API_URL, 
    ZEROGPT_API_KEY, 
    DEFAULT_TIMEOUT,
    MAX_TEXT_LENGTH,
    CHUNK_SIZE
)

logger = logging.getLogger(__name__)

class ZeroGPTAPIError(Exception):
    """ZeroGPT API 相关错误"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict[str, Any]] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)

async def call_zerogpt_api(text: str) -> Dict[str, Any]:
    """
    调用ZeroGPT API进行AI检测
    
    Args:
        text: 要检测的文本
        
    Returns:
        API响应数据
        
    Raises:
        ZeroGPTAPIError: API调用失败时抛出
    """
    if not text or not text.strip():
        raise ZeroGPTAPIError("输入文本不能为空")
    
    if len(text) > MAX_TEXT_LENGTH:
        raise ZeroGPTAPIError(f"文本长度超过限制，最大允许 {MAX_TEXT_LENGTH} 个字符")
    
    payload = json.dumps({
        "input_text": text.strip()
    })
    
    headers = {
        'ApiKey': ZEROGPT_API_KEY,
        'Content-Type': 'application/json'
    }
    
    try:
        timeout = aiohttp.ClientTimeout(total=DEFAULT_TIMEOUT)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            logger.info(f"开始调用ZeroGPT API，文本长度: {len(text)} 字符")
            
            async with session.post(ZEROGPT_API_URL, headers=headers, data=payload) as response:
                response_text = await response.text()
                
                if response.status != 200:
                    logger.error(f"ZeroGPT API调用失败，状态码: {response.status}, 响应: {response_text}")
                    raise ZeroGPTAPIError(
                        f"API调用失败，HTTP状态码: {response.status}",
                        status_code=response.status,
                        response_data={"response_text": response_text}
                    )
                
                try:
                    response_data = json.loads(response_text)
                    logger.info("ZeroGPT API调用成功")
                    return response_data
                except json.JSONDecodeError as e:
                    logger.error(f"响应JSON解析失败: {e}, 原始响应: {response_text}")
                    raise ZeroGPTAPIError(
                        "API响应格式错误，无法解析JSON",
                        response_data={"response_text": response_text, "json_error": str(e)}
                    )
                    
    except asyncio.TimeoutError:
        logger.error(f"ZeroGPT API调用超时，超时时间: {DEFAULT_TIMEOUT}秒")
        raise ZeroGPTAPIError(f"API调用超时，请检查网络连接")
    except aiohttp.ClientError as e:
        logger.error(f"网络连接错误: {e}")
        raise ZeroGPTAPIError(f"网络连接失败: {str(e)}")
    except Exception as e:
        logger.error(f"ZeroGPT API调用发生未知错误: {e}")
        raise ZeroGPTAPIError(f"API调用失败: {str(e)}")

def parse_zerogpt_response(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    解析ZeroGPT API响应数据
    
    Args:
        response_data: API原始响应数据
        
    Returns:
        解析后的标准化数据
        
    Raises:
        ZeroGPTAPIError: 响应格式错误时抛出
    """
    try:
        # 检查响应结构
        if "data" not in response_data:
            raise ZeroGPTAPIError("API响应缺少'data'字段", response_data=response_data)
        
        data = response_data["data"]
        
        # 提取核心字段
        text_words = data.get("textWords", 0)
        ai_words = data.get("aiWords", 0)
        fake_percentage = data.get("fakePercentage", 0.0)
        highlighted = data.get("h", [])
        
        # 数据验证
        if not isinstance(text_words, (int, float)) or text_words < 0:
            text_words = 0
        if not isinstance(ai_words, (int, float)) or ai_words < 0:
            ai_words = 0
        if not isinstance(fake_percentage, (int, float)) or fake_percentage < 0:
            fake_percentage = 0.0
        if not isinstance(highlighted, list):
            highlighted = []
        
        # 确保百分比在合理范围内
        fake_percentage = min(max(fake_percentage, 0.0), 100.0)
        
        return {
            "total_words": int(text_words),
            "ai_words": int(ai_words),
            "fake_percentage": float(fake_percentage),
            "highlighted_sentences": highlighted
        }
        
    except Exception as e:
        logger.error(f"解析ZeroGPT响应失败: {e}, 原始数据: {response_data}")
        raise ZeroGPTAPIError(f"响应数据解析失败: {str(e)}", response_data=response_data)

async def detect_text_chunks(text: str) -> Dict[str, Any]:
    """
    对长文本进行分块检测并合并结果
    
    Args:
        text: 要检测的文本
        
    Returns:
        合并后的检测结果
    """
    if len(text) <= CHUNK_SIZE:
        # 文本不需要分块
        response = await call_zerogpt_api(text)
        return parse_zerogpt_response(response)
    
    # 分块处理
    chunks = []
    for i in range(0, len(text), CHUNK_SIZE):
        chunk = text[i:i + CHUNK_SIZE]
        # 尝试在句号处分割，避免句子被截断
        if i + CHUNK_SIZE < len(text) and '.' in chunk[-100:]:
            last_period = chunk.rfind('.', len(chunk) - 100)
            if last_period > len(chunk) // 2:  # 确保分割点不会太靠前
                chunk = chunk[:last_period + 1]
        chunks.append(chunk)
    
    logger.info(f"长文本分为 {len(chunks)} 块进行检测")
    
    # 并发检测所有块
    tasks = []
    for chunk in chunks:
        task = asyncio.create_task(call_zerogpt_api(chunk))
        tasks.append(task)
    
    try:
        responses = await asyncio.gather(*tasks, return_exceptions=True)
    except Exception as e:
        logger.error(f"批量检测失败: {e}")
        raise ZeroGPTAPIError(f"分块检测失败: {str(e)}")
    
    # 合并结果
    total_words = 0
    total_ai_words = 0
    all_highlighted = []
    successful_chunks = 0
    
    for i, response in enumerate(responses):
        if isinstance(response, Exception):
            logger.warning(f"第 {i+1} 块检测失败: {response}")
            continue
            
        try:
            parsed = parse_zerogpt_response(response)
            total_words += parsed["total_words"]
            total_ai_words += parsed["ai_words"]
            all_highlighted.extend(parsed["highlighted_sentences"])
            successful_chunks += 1
        except Exception as e:
            logger.warning(f"第 {i+1} 块结果解析失败: {e}")
            continue
    
    if successful_chunks == 0:
        raise ZeroGPTAPIError("所有文本块检测失败")
    
    # 计算综合AI百分比
    fake_percentage = (total_ai_words / total_words * 100) if total_words > 0 else 0.0
    
    logger.info(f"分块检测完成，成功检测 {successful_chunks}/{len(chunks)} 块")
    
    return {
        "total_words": total_words,
        "ai_words": total_ai_words,
        "fake_percentage": fake_percentage,
        "highlighted_sentences": all_highlighted
    } 