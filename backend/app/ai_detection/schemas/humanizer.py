"""AI Humanizer Schemas - 降重/人性化改写 相关Pydantic模型"""
from pydantic import BaseModel, Field
from typing import List, Optional
from app.ai_detection.schemas.detection import HighlightedSentence


class HumanizeRequest(BaseModel):
    """AI降重请求模型"""
    text: str = Field(..., description="需要进行人性化改写的完整文本", min_length=1, max_length=50000)
    language: Optional[str] = Field(None, description="文本语言（auto/english/chinese），默认auto")
    stream: Optional[bool] = Field(False, description="是否以流式返回改写后的文本")
    # 复用已存在的检测结果以节省成本：
    highlighted_sentences: Optional[List[HighlightedSentence]] = Field(
        None, description="可选：直接传入带位置的高亮句子，避免重复调用检测API"
    )
    highlighted_sentences_raw: Optional[List[str]] = Field(
        None, description="可选：传入ZeroGPT返回的原始高亮句子字符串列表，由后端重新定位坐标"
    )


class HumanizedSentence(BaseModel):
    """被改写的句子映射"""
    original: str = Field(..., description="原句")
    revised: str = Field(..., description="改写后的句子")
    start_index: int = Field(..., description="原句在清理后文本中的起始位置")
    end_index: int = Field(..., description="原句在清理后文本中的结束位置")


class HumanizeResponse(BaseModel):
    """AI降重响应模型"""
    status: str = Field(default="success", description="处理状态")
    original_text: str = Field(..., description="原始输入文本（清理前）")
    cleaned_text: str = Field(..., description="经过清理处理后的文本（用于位置对齐）")
    humanized_text: str = Field(..., description="人性化改写后的完整文本")
    replaced_count: int = Field(..., description="被识别并改写的句子数量")
    sentences: List[HumanizedSentence] = Field(default_factory=list, description="句子级改写映射列表")


class HumanizeBatchRequest(BaseModel):
    """批量AI降重请求"""
    texts: List[str] = Field(..., min_items=1, max_items=10, description="待处理文本列表")
    language: Optional[str] = Field(None, description="统一语言设置，可为空")


class HumanizeBatchItem(BaseModel):
    index: int = Field(..., description="文本索引")
    success: bool = Field(..., description="是否处理成功")
    error: Optional[str] = Field(None, description="错误信息（如失败）")
    response: Optional[HumanizeResponse] = Field(None, description="成功时的人性化改写结果")


class HumanizeBatchResponse(BaseModel):
    status: str = Field(default="success", description="批量处理状态")
    total: int = Field(..., description="总文本数")
    succeeded: int = Field(..., description="成功数")
    failed: int = Field(..., description="失败数")
    results: List[HumanizeBatchItem] = Field(..., description="逐条结果")


