"""AI Detection Schemas - 检测相关的Pydantic模型"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum

class RiskLevel(str, Enum):
    """AI风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"
    CRITICAL = "critical"

class DetectionRequest(BaseModel):
    """AI检测请求模型"""
    text: str = Field(..., description="要检测的文本内容", min_length=1, max_length=50000)
    
    class Config:
        json_schema_extra = {
            "example": {
                "text": "This is a sample text that I want to check for AI-generated content."
            }
        }

class HighlightedSentence(BaseModel):
    """高亮句子模型"""
    sentence: str = Field(..., description="被标记为AI生成的句子")
    start_index: int = Field(..., description="句子在原文中的开始位置")
    end_index: int = Field(..., description="句子在原文中的结束位置")

class DetectionResult(BaseModel):
    """AI检测结果模型"""
    fake_percentage: float = Field(..., description="AI生成内容的百分比", ge=0, le=100)
    total_words: int = Field(..., description="总词数")
    ai_words: int = Field(..., description="AI生成的词数")
    risk_level: RiskLevel = Field(..., description="风险等级")
    highlighted_sentences: List[HighlightedSentence] = Field([], description="被标记为AI生成的句子列表")
    highlighted_html: str = Field(..., description="带有高亮标签的HTML格式文本")

class DetectionResponse(BaseModel):
    """AI检测响应模型"""
    status: str = Field(default="success", description="检测状态")
    result: DetectionResult = Field(..., description="检测结果")
    message: Optional[str] = Field(None, description="额外信息或警告")
    processing_time: Optional[float] = Field(None, description="处理耗时（秒）")

class BatchDetectionRequest(BaseModel):
    """批量AI检测请求模型"""
    texts: List[str] = Field(..., description="要检测的文本列表", min_items=1, max_items=10)
    
    class Config:
        json_schema_extra = {
            "example": {
                "texts": [
                    "First text to check for AI content.",
                    "Second text to analyze for artificial generation."
                ]
            }
        }

class BatchDetectionResult(BaseModel):
    """批量检测结果中的单个结果"""
    index: int = Field(..., description="文本在请求列表中的索引")
    text_preview: str = Field(..., description="文本预览（前100个字符）")
    result: DetectionResult = Field(..., description="检测结果")
    error: Optional[str] = Field(None, description="如果检测失败，显示错误信息")

class BatchDetectionResponse(BaseModel):
    """批量AI检测响应模型"""
    status: str = Field(default="success", description="批量检测状态")
    total_texts: int = Field(..., description="总文本数量")
    successful_detections: int = Field(..., description="成功检测的文本数量")
    failed_detections: int = Field(..., description="检测失败的文本数量")
    results: List[BatchDetectionResult] = Field(..., description="每个文本的检测结果")
    processing_time: Optional[float] = Field(None, description="总处理耗时（秒）")

class DetectionStatistics(BaseModel):
    """检测统计信息模型"""
    average_ai_percentage: float = Field(..., description="平均AI百分比")
    risk_distribution: Dict[str, int] = Field(..., description="风险等级分布")
    total_words_analyzed: int = Field(..., description="总分析词数")
    total_ai_words: int = Field(..., description="总AI词数")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    status: str = Field(default="error", description="错误状态")
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误信息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情") 