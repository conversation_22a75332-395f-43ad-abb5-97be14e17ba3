"""AI Detection Core Logic - 核心AI检测逻辑"""
import time
import logging
from typing import Dict, Any, List
from fastapi import HTTPException, status

from app.ai_detection.schemas.detection import (
    DetectionResult, 
    DetectionResponse, 
    BatchDetectionResult, 
    BatchDetectionResponse,
    DetectionStatistics
)
from app.ai_detection.utils.api_client import (
    detect_text_chunks,
    ZeroGPTAPIError
)
from app.ai_detection.utils.text_processor import (
    clean_text,
    validate_text_input,
    extract_highlighted_sentences,
    calculate_risk_level,
    get_risk_level_description,
    create_text_preview,
    calculate_text_statistics,
    create_highlighted_html
)

logger = logging.getLogger(__name__)

async def detect_ai_content(text: str) -> DetectionResponse:
    """
    检测单个文本的AI生成内容
    
    Args:
        text: 要检测的文本
        
    Returns:
        检测结果响应
        
    Raises:
        HTTPException: 当检测失败时抛出
    """
    start_time = time.time()
    
    # 输入验证
    is_valid, error_message = validate_text_input(text)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"输入文本无效: {error_message}"
        )
    
    # 清理文本
    cleaned_text = clean_text(text)
    
    try:
        logger.info(f"开始AI检测，文本长度: {len(cleaned_text)} 字符")
        
        # 调用API进行检测
        api_result = await detect_text_chunks(cleaned_text)
        
        # 处理高亮句子
        highlighted_sentences = extract_highlighted_sentences(
            cleaned_text, 
            api_result.get("highlighted_sentences", [])
        )
        
        # 创建带有高亮标签的HTML
        highlighted_html = create_highlighted_html(cleaned_text, highlighted_sentences)
        
        # 计算风险等级
        fake_percentage = api_result["fake_percentage"]
        risk_level = calculate_risk_level(fake_percentage)
        
        # 构建检测结果
        detection_result = DetectionResult(
            fake_percentage=fake_percentage,
            total_words=api_result["total_words"],
            ai_words=api_result["ai_words"],
            risk_level=risk_level,
            highlighted_sentences=highlighted_sentences,
            highlighted_html=highlighted_html
        )
        
        processing_time = time.time() - start_time
        
        # 生成响应消息
        risk_description = get_risk_level_description(risk_level)
        message = f"检测完成 - {risk_description}"
        
        logger.info(f"AI检测完成，AI百分比: {fake_percentage}%, 风险等级: {risk_level.value}, 耗时: {processing_time:.2f}秒")
        
        return DetectionResponse(
            result=detection_result,
            message=message,
            processing_time=round(processing_time, 3)
        )
        
    except ZeroGPTAPIError as e:
        logger.error(f"ZeroGPT API错误: {e.message}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"AI检测服务暂时不可用: {e.message}"
        )
    except Exception as e:
        logger.error(f"AI检测过程中发生未知错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检测过程中发生错误: {str(e)}"
        )

async def detect_ai_content_batch(texts: List[str]) -> BatchDetectionResponse:
    """
    批量检测多个文本的AI生成内容
    
    Args:
        texts: 要检测的文本列表
        
    Returns:
        批量检测结果响应
    """
    start_time = time.time()
    
    if not texts or len(texts) == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文本列表不能为空"
        )
    
    if len(texts) > 15:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="单次批量检测最多支持10个文本"
        )
    
    results = []
    successful_count = 0
    failed_count = 0
    
    logger.info(f"开始批量AI检测，文本数量: {len(texts)}")
    
    for index, text in enumerate(texts):
        try:
            # 生成文本预览
            text_preview = create_text_preview(text, 100)
            
            # 验证文本
            is_valid, error_message = validate_text_input(text)
            if not is_valid:
                results.append(BatchDetectionResult(
                    index=index,
                    text_preview=text_preview,
                    result=DetectionResult(
                        fake_percentage=0.0,
                        total_words=0,
                        ai_words=0,
                        risk_level=calculate_risk_level(0.0),
                        highlighted_sentences=[]
                    ),
                    error=f"文本无效: {error_message}"
                ))
                failed_count += 1
                continue
            
            # 清理文本
            cleaned_text = clean_text(text)
            
            # 调用API进行检测
            api_result = await detect_text_chunks(cleaned_text)
            
            # 处理高亮句子
            highlighted_sentences = extract_highlighted_sentences(
                cleaned_text, 
                api_result.get("highlighted_sentences", [])
            )
            
            # 计算风险等级
            fake_percentage = api_result["fake_percentage"]
            risk_level = calculate_risk_level(fake_percentage)
            
            # 构建检测结果
            detection_result = DetectionResult(
                fake_percentage=fake_percentage,
                total_words=api_result["total_words"],
                ai_words=api_result["ai_words"],
                risk_level=risk_level,
                highlighted_sentences=highlighted_sentences
            )
            
            results.append(BatchDetectionResult(
                index=index,
                text_preview=text_preview,
                result=detection_result
            ))
            
            successful_count += 1
            logger.info(f"文本 {index + 1} 检测完成，AI百分比: {fake_percentage}%")
            
        except ZeroGPTAPIError as e:
            logger.error(f"文本 {index + 1} 检测失败 (API错误): {e.message}")
            results.append(BatchDetectionResult(
                index=index,
                text_preview=create_text_preview(text, 100),
                result=DetectionResult(
                    fake_percentage=0.0,
                    total_words=0,
                    ai_words=0,
                    risk_level=calculate_risk_level(0.0),
                    highlighted_sentences=[]
                ),
                error=f"API调用失败: {e.message}"
            ))
            failed_count += 1
            
        except Exception as e:
            logger.error(f"文本 {index + 1} 检测失败 (未知错误): {e}")
            results.append(BatchDetectionResult(
                index=index,
                text_preview=create_text_preview(text, 100),
                result=DetectionResult(
                    fake_percentage=0.0,
                    total_words=0,
                    ai_words=0,
                    risk_level=calculate_risk_level(0.0),
                    highlighted_sentences=[]
                ),
                error=f"检测失败: {str(e)}"
            ))
            failed_count += 1
    
    processing_time = time.time() - start_time
    
    logger.info(f"批量AI检测完成，成功: {successful_count}, 失败: {failed_count}, 总耗时: {processing_time:.2f}秒")
    
    return BatchDetectionResponse(
        total_texts=len(texts),
        successful_detections=successful_count,
        failed_detections=failed_count,
        results=results,
        processing_time=round(processing_time, 3)
    )

def calculate_batch_statistics(batch_results: List[BatchDetectionResult]) -> DetectionStatistics:
    """
    计算批量检测的统计信息
    
    Args:
        batch_results: 批量检测结果列表
        
    Returns:
        统计信息
    """
    successful_results = [r for r in batch_results if not r.error]
    
    if not successful_results:
        return DetectionStatistics(
            average_ai_percentage=0.0,
            risk_distribution={"low": 0, "medium": 0, "high": 0, "critical": 0},
            total_words_analyzed=0,
            total_ai_words=0
        )
    
    # 计算平均AI百分比
    total_percentage = sum(r.result.fake_percentage for r in successful_results)
    average_ai_percentage = total_percentage / len(successful_results)
    
    # 统计风险等级分布
    risk_distribution = {"low": 0, "medium": 0, "high": 0, "critical": 0}
    for result in successful_results:
        risk_distribution[result.result.risk_level.value] += 1
    
    # 计算总词数
    total_words_analyzed = sum(r.result.total_words for r in successful_results)
    total_ai_words = sum(r.result.ai_words for r in successful_results)
    
    return DetectionStatistics(
        average_ai_percentage=round(average_ai_percentage, 2),
        risk_distribution=risk_distribution,
        total_words_analyzed=total_words_analyzed,
        total_ai_words=total_ai_words
    )

async def get_detection_health_check() -> Dict[str, Any]:
    """
    检测服务健康检查
    
    Returns:
        健康检查结果
    """
    try:
        # 使用简短的测试文本进行API连通性测试
        test_text = "This is a simple test to check if the AI detection service is working properly."
        
        start_time = time.time()
        await detect_text_chunks(test_text)
        response_time = time.time() - start_time
        
        return {
            "status": "healthy",
            "service": "ZeroGPT AI Detection",
            "response_time": round(response_time, 3),
            "api_accessible": True,
            "timestamp": int(time.time())
        }
        
    except ZeroGPTAPIError as e:
        logger.error(f"健康检查失败 (API错误): {e.message}")
        return {
            "status": "unhealthy",
            "service": "ZeroGPT AI Detection",
            "error": f"API服务不可用: {e.message}",
            "api_accessible": False,
            "timestamp": int(time.time())
        }
        
    except Exception as e:
        logger.error(f"健康检查失败 (未知错误): {e}")
        return {
            "status": "unhealthy",
            "service": "ZeroGPT AI Detection",
            "error": f"服务异常: {str(e)}",
            "api_accessible": False,
            "timestamp": int(time.time())
        } 