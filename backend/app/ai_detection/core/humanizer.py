"""AI Humanizer Core - 降重核心逻辑
流程：
1) 调用现有 ZeroGPT 检测文本，提取被标记的AI句子及其位置
2) 将AI句子批量送入豆包模型，获得人性化改写
3) 根据文本格式选择不同的替换策略，保持格式不变
"""
import logging
from typing import List, Tuple, Optional
from app.ai_detection.utils.text_processor import (
    clean_text,
    extract_highlighted_sentences,
)
from app.ai_detection.utils.api_client import detect_text_chunks
from app.ai_detection.schemas.humanizer import (
    HumanizeResponse,
    HumanizedSentence
)
from app.ai_detection.schemas.detection import HighlightedSentence as HighlightedSentenceModel
from app.ai_detection.utils.humanizer_llm import humanize_sentences


def detect_text_format(text: str) -> str:
    """
    检测文本格式类型
    
    Returns:
        'markdown' 或 'text'
    """
    import re
    
    # Markdown格式特征检测
    markdown_patterns = [
        r'^#{1,6}\s+',      # 标题
        r'\*\*[^*]+\*\*',   # 加粗
        r'\*[^*]+\*',       # 斜体
        r'_[^_]+_',         # 下划线斜体
        r'`[^`]+`',         # 行内代码
        r'```',             # 代码块
        r'^\s*[-*+]\s+',    # 列表
        r'^\s*\d+\.\s+',    # 有序列表
        r'^\s*>\s+',        # 引用
        r'\[([^\]]+)\]\([^)]+\)',  # 链接
    ]
    
    markdown_score = 0
    for pattern in markdown_patterns:
        if re.search(pattern, text, re.MULTILINE):
            markdown_score += 1
    
    # 如果检测到2个或以上Markdown特征，认为是Markdown格式
    return 'markdown' if markdown_score >= 2 else 'text'


async def humanize_text_once(
    text: str,
    language: str | None = None,
    highlighted_sentences: list[HighlightedSentenceModel] | None = None,
    highlighted_sentences_raw: list[str] | None = None,
    format_hint: Optional[str] = None,
) -> HumanizeResponse:
    """对单段文本执行一次AI降重流程，支持格式保留。"""
    original_text = text
    
    # 检测文本格式
    detected_format = format_hint or detect_text_format(text)
    
    # 对于AI检测，始终使用清理后的纯文本
    cleaned = clean_text(text)

    # 1) 优先复用前端/上游传入的高亮结果，避免重复调用API
    if highlighted_sentences and len(highlighted_sentences) > 0:
        highlighted = highlighted_sentences
    else:
        if highlighted_sentences_raw is not None:
            raw_highlights = highlighted_sentences_raw
        else:
            # 若未传入，则调用检测API（可能产生成本）
            detection = await detect_text_chunks(cleaned)
            raw_highlights = detection.get("highlighted_sentences", [])
        # 2) 计算每个句子的精确位置
        highlighted = extract_highlighted_sentences(cleaned, raw_highlights)
    
    if not highlighted:
        return HumanizeResponse(
            original_text=original_text,
            cleaned_text=cleaned,
            humanized_text=original_text,
            replaced_count=0,
            sentences=[]
        )

    # 3) 送豆包改写，仅送原句内容
    target_sentences = [h.sentence for h in highlighted]
    revised = await humanize_sentences(target_sentences, language)

    # 4) 根据检测到的格式选择替换策略
    if detected_format == 'markdown':
        result_text = _replace_sentences_in_markdown(original_text, target_sentences, revised)
        replaced_count = len([r for r in revised if r and r.strip()])
    else:
        # 纯文本模式：在原文上按位置替换
        result_text, mappings = _replace_sentences_by_spans(
            original_text, [(h.start_index, h.end_index, r) for h, r in zip(highlighted, revised) if r]
        )
        replaced_count = len([m for m in mappings if m])

    # 5) 组装响应
    items: List[HumanizedSentence] = []
    for i, h in enumerate(highlighted):
        revised_text = revised[i] if i < len(revised) else h.sentence
        items.append(HumanizedSentence(
            original=h.sentence,
            revised=revised_text,
            start_index=h.start_index,
            end_index=h.end_index
        ))

    return HumanizeResponse(
        original_text=original_text,
        cleaned_text=cleaned,
        humanized_text=result_text,
        replaced_count=replaced_count,
        sentences=items
    )


def _replace_sentences_by_spans(text: str, spans: List[Tuple[int, int, str]]) -> tuple[str, List[Tuple[int, int, str]]]:
    """在给定文本上，按起止索引用新文本替换，避免索引失效：
    - 先按 start_index 升序排序
    - 从前往后构建新字符串，同时累计偏移
    返回：(新文本, 使用的映射列表)
    """
    if not spans:
        return text, []

    spans_sorted = sorted(spans, key=lambda s: s[0])
    parts: List[str] = []
    cursor = 0
    for start, end, repl in spans_sorted:
        start = max(0, min(start, len(text)))
        end = max(start, min(end, len(text)))
        if start > cursor:
            parts.append(text[cursor:start])
        parts.append(repl)
        cursor = end
    if cursor < len(text):
        parts.append(text[cursor:])
    return "".join(parts), spans_sorted


def _replace_sentences_in_markdown(markdown_text: str, original_sentences: List[str], revised_sentences: List[str]) -> str:
    """
    在Markdown文本中智能替换句子，保留格式
    使用混合模式处理器，根据复杂度自动选择最佳策略
    
    Args:
        markdown_text: 原始Markdown文本
        original_sentences: 需要替换的原句列表
        revised_sentences: 对应的新句子列表
    
    Returns:
        替换后的Markdown文本
    """
    try:
        from app.ai_detection.utils.markdown_processor import HybridMarkdownProcessor
        processor = HybridMarkdownProcessor()
        
        # 构建替换映射
        replacements = {}
        for orig, rev in zip(original_sentences, revised_sentences):
            if rev and rev.strip():  # 只处理有效的替换
                replacements[orig] = rev
        
        if not replacements:
            return markdown_text
        
        # 使用混合模式处理器进行智能替换
        result = processor.smart_sentence_replace(markdown_text, replacements)
        
        # 记录处理统计信息
        complexity = processor.detect_complexity(markdown_text)
        logging.info(f"Markdown处理完成 - 复杂度: {complexity.value}, 替换数量: {len(replacements)}")
        
        return result
        
    except ImportError as e:
        logging.warning(f"Markdown处理器导入失败: {e}, 使用简单替换")
        return _fallback_markdown_replace(markdown_text, original_sentences, revised_sentences)
    except Exception as e:
        logging.error(f"Markdown格式处理失败: {e}, 使用降级策略")
        return _fallback_markdown_replace(markdown_text, original_sentences, revised_sentences)


def _fallback_markdown_replace(markdown_text: str, original_sentences: List[str], revised_sentences: List[str]) -> str:
    """
    Markdown替换的降级策略
    当混合处理器不可用时使用
    """
    result = markdown_text
    for orig, rev in zip(original_sentences, revised_sentences):
        if rev and rev.strip():
            # 尝试直接替换
            if orig in result:
                result = result.replace(orig, rev, 1)
            else:
                # 尝试简单的模糊匹配
                import re
                try:
                    # 构建简单的灵活模式
                    escaped_orig = re.escape(orig)
                    flexible_pattern = escaped_orig.replace(r'\ ', r'\s+')
                    result = re.sub(flexible_pattern, rev, result, count=1, flags=re.DOTALL)
                except re.error:
                    # 正则失败，跳过这个替换
                    continue
    return result


