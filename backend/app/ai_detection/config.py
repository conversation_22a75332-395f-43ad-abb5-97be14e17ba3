"""AI Detection System Config"""
from app.core.config import settings

# ZeroGPT API配置
ZEROGPT_API_URL = "https://api.zerogpt.com/api/detect/detectText"
ZEROGPT_API_KEY = getattr(settings, 'ZEROGPT_API_KEY')

# 检测配置
DEFAULT_TIMEOUT = 30  # API调用超时时间（秒）
MAX_TEXT_LENGTH = 50000  # 最大文本长度限制（字符数）
CHUNK_SIZE = 10000  # 长文本分块大小（字符数）

# AI检测阈值配置
AI_PERCENTAGE_THRESHOLD = {
    "low": 25.0,      # 低风险阈值：25%以下为低AI风险
    "medium": 50.0,   # 中风险阈值：25%-50%为中等AI风险
    "high": 75.0      # 高风险阈值：50%-75%为高AI风险，75%以上为极高风险
} 