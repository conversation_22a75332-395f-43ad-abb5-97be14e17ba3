"""
组织权益分配系统的Pydantic模式定义
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class PackageAllocationRequest(BaseModel):
    """套餐分配请求"""
    organization_id: int = Field(..., description="组织ID")
    allocated_user_id: int = Field(..., description="分配给的用户ID")
    package_id: str = Field(..., description="套餐ID")


class PackageRevocationRequest(BaseModel):
    """套餐回收请求"""
    organization_id: int = Field(..., description="组织ID")
    allocated_user_id: int = Field(..., description="回收的用户ID")
    package_id: str = Field(..., description="套餐ID")


class UserInfo(BaseModel):
    """用户信息"""
    id: int
    username: str
    email: str


class OrderInfo(BaseModel):
    """订单信息"""
    id: int
    order_no: str
    amount: float
    credits: int
    package_id: Optional[str]
    package_quantity: Optional[int] = 1
    status: str
    payment_time: Optional[datetime]
    expires_at: Optional[datetime] = None
    remark: Optional[str]


class PackageAllocationResponse(BaseModel):
    """套餐分配记录响应"""
    id: int
    organization_id: int
    owner_user_id: int
    allocated_user_id: int
    source_order_id: int
    allocated_order_id: Optional[int]
    package_id: str
    status: str
    allocated_at: datetime
    revoked_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    # 关联信息
    allocated_user: Optional[UserInfo] = None
    owner_user: Optional[UserInfo] = None
    source_order: Optional[OrderInfo] = None
    allocated_order: Optional[OrderInfo] = None


class AvailablePackageResponse(BaseModel):
    """可分配套餐响应"""
    package_id: str
    purchased_count: int
    allocated_count: int
    available_count: int
    orders: List[OrderInfo]


class PackageAllocationListRequest(BaseModel):
    """套餐分配列表请求"""
    organization_id: int = Field(..., description="组织ID")
    status_filter: Optional[str] = Field(None, description="状态过滤：active/revoked")


class UserAllocatedPackagesRequest(BaseModel):
    """用户被分配套餐列表请求"""
    organization_id: int = Field(..., description="组织ID")


class PackageAllocationOperationResponse(BaseModel):
    """套餐分配操作响应"""
    success: bool
    message: str
    allocation: Optional[PackageAllocationResponse] = None
    allocated_order: Optional[OrderInfo] = None


class AvailablePackagesListResponse(BaseModel):
    """可分配套餐列表响应"""
    packages: List[AvailablePackageResponse]
    total_count: int


class PackageAllocationListResponse(BaseModel):
    """套餐分配列表响应"""
    allocations: List[PackageAllocationResponse]
    total_count: int


class UserAllocatedPackagesResponse(BaseModel):
    """用户被分配套餐响应"""
    allocations: List[PackageAllocationResponse]
    total_count: int
