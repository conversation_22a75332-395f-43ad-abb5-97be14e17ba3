"""
组织权益分配系统API路由
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.organization_benefits.services.package_allocation_service import PackageAllocationService
from app.organization_benefits.schemas import (
    PackageAllocationRequest,
    PackageRevocationRequest,
    PackageAllocationListRequest,
    UserAllocatedPackagesRequest,
    PackageAllocationOperationResponse,
    AvailablePackagesListResponse,
    PackageAllocationListResponse,
    UserAllocatedPackagesResponse,
    AvailablePackageResponse,
    PackageAllocationResponse
)

# 创建路由器
router = APIRouter(prefix="/organization-benefits", tags=["组织权益分配"])


@router.get("/available-packages/{organization_id}", response_model=AvailablePackagesListResponse)
async def get_available_packages(
    organization_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取可分配的套餐列表
    只有组织主账号可以访问
    """
    try:
        packages = await PackageAllocationService.get_available_packages_for_allocation(
            db, current_user, organization_id
        )
        
        return AvailablePackagesListResponse(
            packages=packages,
            total_count=len(packages)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取可分配套餐列表失败"
        )


@router.post("/allocate", response_model=PackageAllocationOperationResponse)
async def allocate_package(
    request: PackageAllocationRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    分配套餐给子账号
    只有组织主账号可以操作
    """
    try:
        result = await PackageAllocationService.allocate_package_to_user(
            db=db,
            owner_user=current_user,
            organization_id=request.organization_id,
            allocated_user_id=request.allocated_user_id,
            package_id=request.package_id
        )
        
        return PackageAllocationOperationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="分配套餐失败"
        )


@router.post("/revoke", response_model=PackageAllocationOperationResponse)
async def revoke_package(
    request: PackageRevocationRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    从子账号回收套餐
    只有组织主账号可以操作
    """
    try:
        result = await PackageAllocationService.revoke_package_from_user(
            db=db,
            owner_user=current_user,
            organization_id=request.organization_id,
            allocated_user_id=request.allocated_user_id,
            package_id=request.package_id
        )
        
        return PackageAllocationOperationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="回收套餐失败"
        )


@router.get("/allocations/{organization_id}", response_model=PackageAllocationListResponse)
async def get_organization_allocations(
    organization_id: int,
    status_filter: str = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取组织的套餐分配列表
    只有组织主账号可以访问
    """
    try:
        allocations = await PackageAllocationService.get_organization_allocations(
            db=db,
            user=current_user,
            organization_id=organization_id,
            status_filter=status_filter
        )
        
        return PackageAllocationListResponse(
            allocations=allocations,
            total_count=len(allocations)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取分配列表失败"
        )


@router.get("/my-allocations/{organization_id}", response_model=UserAllocatedPackagesResponse)
async def get_my_allocated_packages(
    organization_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户被分配的套餐列表
    组织成员可以查看自己被分配的套餐
    """
    try:
        allocations = await PackageAllocationService.get_user_allocated_packages(
            db=db,
            user=current_user,
            organization_id=organization_id
        )
        
        return UserAllocatedPackagesResponse(
            allocations=allocations,
            total_count=len(allocations)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户分配套餐失败"
        )
