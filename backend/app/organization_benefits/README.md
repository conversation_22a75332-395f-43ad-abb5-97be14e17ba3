# 组织权益分配系统

## 概述

组织权益分配系统是一个完整的企业级权益管理解决方案，支持组织内套餐权益的分配、回收和积分池共享功能。

## 核心功能

### 1. 套餐分配功能
- **主账号购买**：组织主账号可以购买多个套餐（按个数计算）
- **分配给子账号**：主账号可以将购买的套餐分配给组织内的子账号
- **使用权限控制**：子账号只能在当前组织身份下使用分配的套餐功能
- **套餐回收**：主账号可以从子账号收回套餐权益，重新分配给其他子账号

### 2. 积分共享功能
- **组织积分池**：组织购买的积分为全组织共享，所有成员都可以使用
- **统一管理**：积分由组织统一管理，不需要单独分配
- **智能扣费**：消费时自动从组织积分池中扣除，优先使用余额多的账户

### 3. 权限控制
- **主账号权限**：只有组织主账号可以进行套餐分配和回收操作
- **成员权限**：组织成员可以查看自己被分配的套餐和使用组织积分
- **身份验证**：确保用户在正确的组织身份下执行操作

## 技术架构

### 数据库设计

#### package_allocations 表
```sql
CREATE TABLE package_allocations (
    id SERIAL PRIMARY KEY,
    organization_id INTEGER NOT NULL REFERENCES organizations(id),
    owner_user_id INTEGER NOT NULL REFERENCES users(id),
    allocated_user_id INTEGER NOT NULL REFERENCES users(id),
    source_order_id INTEGER NOT NULL REFERENCES payment_orders(id),
    allocated_order_id INTEGER REFERENCES payment_orders(id),
    package_id VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' NOT NULL,
    allocated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### API 接口

#### 套餐分配相关
- `GET /api/organization-benefits/available-packages/{organization_id}` - 获取可分配套餐列表
- `POST /api/organization-benefits/allocate` - 分配套餐给子账号
- `POST /api/organization-benefits/revoke` - 从子账号回收套餐
- `GET /api/organization-benefits/allocations/{organization_id}` - 查看组织分配列表
- `GET /api/organization-benefits/my-allocations/{organization_id}` - 查看用户被分配的套餐

#### 积分共享相关
- 现有积分API自动支持组织积分池共享
- `GET /api/credit-payment/credit/balance` - 查看积分余额（组织身份下显示积分池总额）
- `POST /api/credit-payment/credit/consume` - 消费积分（自动从组织积分池扣除）

## 使用指南

### 1. 套餐分配流程

#### 主账号操作
1. 购买套餐（通过现有的积分付费系统）
2. 查看可分配的套餐列表
3. 选择目标子账号进行分配
4. 确认分配操作

#### 子账号使用
1. 切换到组织身份
2. 查看被分配的套餐
3. 正常使用套餐功能（如智能选校等）

### 2. 积分共享使用

#### 组织成员
1. 切换到组织身份
2. 查看组织积分池余额
3. 正常使用需要消费积分的功能
4. 系统自动从组织积分池扣除

### 3. 权限说明

#### 组织主账号权限
- ✅ 购买套餐和积分
- ✅ 分配套餐给子账号
- ✅ 回收子账号的套餐
- ✅ 查看所有分配记录
- ✅ 使用组织积分池

#### 组织成员权限
- ❌ 不能购买套餐和积分
- ❌ 不能分配或回收套餐
- ✅ 查看自己被分配的套餐
- ✅ 使用分配的套餐功能
- ✅ 使用组织积分池

## 测试指南

### 测试环境
使用 `test_organization_benefits.html` 进行功能测试：

1. 打开测试页面
2. 输入认证信息（Bearer Token、组织ID、用户ID）
3. 测试各项功能：
   - 套餐分配和回收
   - 分配状态查询
   - 积分共享
   - 权限控制

### 测试场景

#### 场景1：套餐分配
1. 主账号购买套餐
2. 分配给子账号
3. 子账号验证可以使用套餐功能
4. 主账号回收套餐
5. 子账号验证无法使用套餐功能

#### 场景2：积分共享
1. 组织购买积分
2. 不同成员消费积分
3. 验证从组织积分池扣除
4. 查看积分消费记录

#### 场景3：权限控制
1. 非主账号尝试分配套餐（应失败）
2. 非组织成员尝试使用组织积分（应失败）
3. 验证各种权限边界

## 注意事项

### 数据一致性
- 分配的套餐通过虚拟订单记录实现，确保与现有系统兼容
- 回收套餐时会标记相关订单为已取消状态
- 积分消费会在多个账户间分摊，保持事务一致性

### 安全考虑
- 所有操作都有严格的权限检查
- 支持多租户数据隔离
- 防止跨组织的权益访问

### 性能优化
- 使用数据库索引优化查询性能
- 积分消费时优先使用余额多的账户
- 支持批量操作减少数据库访问

## 故障排除

### 常见问题

1. **分配套餐失败**
   - 检查是否为组织主账号
   - 确认目标用户是组织成员
   - 验证套餐库存是否充足

2. **积分消费失败**
   - 检查组织积分池余额
   - 确认用户是组织成员
   - 验证用户身份是否正确

3. **权限不足错误**
   - 确认用户角色和权限
   - 检查组织身份切换是否正确
   - 验证组织成员状态

### 日志查看
系统会记录详细的操作日志，包括：
- 套餐分配和回收记录
- 积分消费详情
- 权限检查结果
- 错误信息和堆栈跟踪

## 扩展功能

### 未来规划
- 支持套餐使用期限设置
- 添加套餐使用统计报表
- 实现积分使用配额限制
- 支持批量套餐分配操作
