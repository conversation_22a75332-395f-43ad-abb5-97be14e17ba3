"""
套餐分配服务
实现组织内套餐权益的分配、回收、查询等核心业务逻辑
"""

from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sqlalchemy.orm import selectinload
from fastapi import HTTPException, status
from datetime import datetime, timezone

from app.models.user import User
from app.models.organization import OrganizationMember
from app.models.credit_payment import PaymentOrder
from app.models.package_allocation import PackageAllocation

from app.core.organization_permissions import OrganizationPermissionService


def utc_now():
    """返回UTC时区的当前时间（不带时区信息，适配数据库）"""
    return datetime.now(timezone.utc).replace(tzinfo=None)


class PackageAllocationService:
    """套餐分配服务类"""
    

    
    @staticmethod
    async def get_available_packages_for_allocation(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> List[Dict[str, Any]]:
        """
        获取可分配的套餐列表（主账号购买的套餐）
        
        Args:
            db: 数据库会话
            user: 用户对象（主账号）
            organization_id: 组织ID
            
        Returns:
            List[Dict]: 可分配的套餐列表
        """
        try:
            # 检查是否为组织主账号
            await OrganizationPermissionService.require_organization_owner(
                db, user, organization_id, "查看可分配的套餐"
            )
            
            # 查询主账号在该组织下购买的套餐订单
            query = (
                select(PaymentOrder)
                .where(
                    and_(
                        PaymentOrder.user_id == user.id,
                        PaymentOrder.organization_id == organization_id,
                        PaymentOrder.status == "paid",
                        PaymentOrder.package_id.isnot(None)  # 只查询套餐订单
                    )
                )
                .order_by(desc(PaymentOrder.payment_time))
            )
            
            result = await db.execute(query)
            orders = result.scalars().all()
            
            # 统计每个套餐的购买数量和已分配数量
            package_stats = {}
            for order in orders:
                package_id = order.package_id
                if package_id not in package_stats:
                    package_stats[package_id] = {
                        'package_id': package_id,
                        'purchased_count': 0,
                        'allocated_count': 0,
                        'available_count': 0,
                        'orders': []
                    }
                # 使用package_quantity字段计算购买数量，向后兼容
                quantity = getattr(order, 'package_quantity', 1) or 1
                package_stats[package_id]['purchased_count'] += quantity
                package_stats[package_id]['orders'].append(order.to_dict())
            
            # 查询已分配的套餐数量
            for package_id in package_stats.keys():
                allocation_query = select(func.count(PackageAllocation.id)).where(
                    and_(
                        PackageAllocation.organization_id == organization_id,
                        PackageAllocation.owner_user_id == user.id,
                        PackageAllocation.package_id == package_id,
                        PackageAllocation.status == "active"
                    )
                )
                allocation_result = await db.execute(allocation_query)
                allocated_count = allocation_result.scalar() or 0

                package_stats[package_id]['allocated_count'] = allocated_count

                # 🔥 新增：组织所有者默认占用一个账号
                # 可分配数量 = 购买数量 - 已分配数量 - 1（所有者占用）
                owner_reserved_count = 1  # 所有者默认占用1个账号
                available_count = (
                    package_stats[package_id]['purchased_count'] - allocated_count - owner_reserved_count
                )
                # 确保可分配数量不会小于0
                package_stats[package_id]['available_count'] = max(0, available_count)
                package_stats[package_id]['owner_reserved_count'] = owner_reserved_count
            
            return list(package_stats.values())
            
        except HTTPException:
            raise
        except Exception as e:
            print(f"获取可分配套餐列表失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取可分配套餐列表失败"
            )
    
    @staticmethod
    async def allocate_package_to_user(
        db: AsyncSession,
        owner_user: User,
        organization_id: int,
        allocated_user_id: int,
        package_id: str
    ) -> Dict[str, Any]:
        """
        分配套餐给子账号
        
        Args:
            db: 数据库会话
            owner_user: 主账号用户
            organization_id: 组织ID
            allocated_user_id: 分配给的用户ID
            package_id: 套餐ID
            
        Returns:
            Dict: 分配结果
        """
        try:
            # 检查是否为组织主账号
            await OrganizationPermissionService.require_organization_owner(
                db, owner_user, organization_id, "分配套餐"
            )
            
            # 检查目标用户是否为组织成员
            member_query = select(OrganizationMember).where(
                and_(
                    OrganizationMember.organization_id == organization_id,
                    OrganizationMember.user_id == allocated_user_id,
                    OrganizationMember.is_active == True
                )
            )
            member_result = await db.execute(member_query)
            member = member_result.scalars().first()
            
            if not member:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="目标用户不是组织成员"
                )
            
            # 检查是否已经分配过该套餐
            existing_query = select(PackageAllocation).where(
                and_(
                    PackageAllocation.organization_id == organization_id,
                    PackageAllocation.allocated_user_id == allocated_user_id,
                    PackageAllocation.package_id == package_id,
                    PackageAllocation.status == "active"
                )
            )
            existing_result = await db.execute(existing_query)
            existing_allocation = existing_result.scalars().first()
            
            if existing_allocation:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该用户已经分配了此套餐"
                )
            
            # 查找可用的源订单
            source_order_query = (
                select(PaymentOrder)
                .where(
                    and_(
                        PaymentOrder.user_id == owner_user.id,
                        PaymentOrder.organization_id == organization_id,
                        PaymentOrder.status == "paid",
                        PaymentOrder.package_id == package_id
                    )
                )
                .order_by(desc(PaymentOrder.payment_time))
            )
            source_order_result = await db.execute(source_order_query)
            source_orders = source_order_result.scalars().all()
            
            if not source_orders:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="没有可分配的套餐"
                )
            
            # 计算总购买套餐数量
            total_purchased = sum(getattr(order, 'package_quantity', 1) or 1 for order in source_orders)

            # 检查是否还有可分配的套餐数量
            allocated_count_query = select(func.count(PackageAllocation.id)).where(
                and_(
                    PackageAllocation.organization_id == organization_id,
                    PackageAllocation.owner_user_id == owner_user.id,
                    PackageAllocation.package_id == package_id,
                    PackageAllocation.status == "active"
                )
            )
            allocated_count_result = await db.execute(allocated_count_query)
            allocated_count = allocated_count_result.scalar() or 0

            # 🔥 新增：考虑组织所有者占用的账号数量
            owner_reserved_count = 1  # 所有者默认占用1个账号
            max_allocatable = total_purchased - owner_reserved_count  # 最大可分配数量

            if allocated_count >= max_allocatable:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"该套餐的可分配数量已用完（已购买{total_purchased}个，所有者占用{owner_reserved_count}个，已分配{allocated_count}个，可分配{max_allocatable}个）"
                )
            
            # 使用第一个源订单作为分配来源
            source_order = source_orders[0]

            # 获取子账号用户对象
            allocated_user_query = select(User).where(User.id == allocated_user_id)
            allocated_user_result = await db.execute(allocated_user_query)
            allocated_user = allocated_user_result.scalars().first()

            if not allocated_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分配目标用户不存在"
                )

            # 为子账号创建或获取积分账户（确保组织积分池完整性）
            from app.credit_payment.services.credit_service import CreditService

            # 临时设置子账号的组织身份，以便创建正确的积分账户
            allocated_user._current_identity_type = 'organization'
            allocated_user._current_organization_id = organization_id

            allocated_user_account = await CreditService.get_or_create_credit_account(allocated_user, db)
            if not allocated_user_account:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="无法为子账号创建积分账户"
                )

            print(f"💳 为子账号创建积分账户: 用户{allocated_user_id} -> 账户ID{allocated_user_account.id} (组织{organization_id})")

            # 为子账号创建虚拟订单记录
            allocated_order = PaymentOrder(
                order_no=f"ALLOC_{source_order.order_no}_{allocated_user_id}_{int(utc_now().timestamp())}",
                user_id=allocated_user_id,
                organization_id=organization_id,
                user_credit_account_id=allocated_user_account.id,  # 使用子账号的积分账户
                amount=source_order.amount,
                credits=source_order.credits,
                payment_method="allocation",  # 标记为分配方式
                package_id=package_id,
                package_quantity=1,  # 分配的套餐数量为1
                status="paid",
                payment_time=utc_now(),
                expires_at=source_order.expires_at,  # 使用源订单的过期时间
                remark=f"套餐分配：由用户{owner_user.id}分配"
            )
            
            db.add(allocated_order)
            await db.flush()  # 获取allocated_order的ID
            
            # 创建分配记录
            allocation = PackageAllocation(
                organization_id=organization_id,
                owner_user_id=owner_user.id,
                allocated_user_id=allocated_user_id,
                source_order_id=source_order.id,
                allocated_order_id=allocated_order.id,
                package_id=package_id,
                status="active"
            )
            
            db.add(allocation)
            await db.commit()
            
            return {
                "success": True,
                "message": "套餐分配成功",
                "allocation": allocation.to_dict(),
                "allocated_order": allocated_order.to_dict()
            }
            
        except HTTPException:
            await db.rollback()
            raise
        except Exception as e:
            await db.rollback()
            print(f"分配套餐失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="分配套餐失败"
            )

    @staticmethod
    async def revoke_package_from_user(
        db: AsyncSession,
        owner_user: User,
        organization_id: int,
        allocated_user_id: int,
        package_id: str
    ) -> Dict[str, Any]:
        """
        从子账号回收套餐

        Args:
            db: 数据库会话
            owner_user: 主账号用户
            organization_id: 组织ID
            allocated_user_id: 回收的用户ID
            package_id: 套餐ID

        Returns:
            Dict: 回收结果
        """
        try:
            # 检查是否为组织主账号
            await OrganizationPermissionService.require_organization_owner(
                db, owner_user, organization_id, "回收套餐"
            )

            # 查找活跃的分配记录
            allocation_query = select(PackageAllocation).where(
                and_(
                    PackageAllocation.organization_id == organization_id,
                    PackageAllocation.owner_user_id == owner_user.id,
                    PackageAllocation.allocated_user_id == allocated_user_id,
                    PackageAllocation.package_id == package_id,
                    PackageAllocation.status == "active"
                )
            )
            allocation_result = await db.execute(allocation_query)
            allocation = allocation_result.scalars().first()

            if not allocation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="未找到活跃的套餐分配记录"
                )

            # 更新分配记录状态
            allocation.status = "revoked"
            allocation.revoked_at = utc_now()
            allocation.updated_at = utc_now()

            # 更新分配的订单状态（标记为已取消）
            if allocation.allocated_order_id:
                allocated_order_query = select(PaymentOrder).where(
                    PaymentOrder.id == allocation.allocated_order_id
                )
                allocated_order_result = await db.execute(allocated_order_query)
                allocated_order = allocated_order_result.scalars().first()

                if allocated_order:
                    allocated_order.status = "cancelled"
                    allocated_order.remark = f"{allocated_order.remark or ''} - 套餐已被回收"

            await db.commit()

            return {
                "success": True,
                "message": "套餐回收成功",
                "allocation": allocation.to_dict()
            }

        except HTTPException:
            await db.rollback()
            raise
        except Exception as e:
            await db.rollback()
            print(f"回收套餐失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="回收套餐失败"
            )

    @staticmethod
    async def get_organization_allocations(
        db: AsyncSession,
        user: User,
        organization_id: int,
        status_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取组织的套餐分配列表

        Args:
            db: 数据库会话
            user: 用户对象
            organization_id: 组织ID
            status_filter: 状态过滤（active/revoked）

        Returns:
            List[Dict]: 分配记录列表
        """
        try:
            # 检查是否为组织主账号
            await OrganizationPermissionService.require_organization_owner(
                db, user, organization_id, "查看分配列表"
            )

            # 构建查询
            query = (
                select(PackageAllocation)
                .options(
                    selectinload(PackageAllocation.allocated_user),
                    selectinload(PackageAllocation.source_order),
                    selectinload(PackageAllocation.allocated_order)
                )
                .where(
                    and_(
                        PackageAllocation.organization_id == organization_id,
                        PackageAllocation.owner_user_id == user.id
                    )
                )
            )

            # 添加状态过滤
            if status_filter:
                query = query.where(PackageAllocation.status == status_filter)

            query = query.order_by(desc(PackageAllocation.allocated_at))

            result = await db.execute(query)
            allocations = result.scalars().all()

            # 转换为字典格式并添加用户信息
            allocation_list = []
            for allocation in allocations:
                allocation_dict = allocation.to_dict()

                # 添加用户信息
                if allocation.allocated_user:
                    allocation_dict['allocated_user'] = {
                        'id': allocation.allocated_user.id,
                        'username': allocation.allocated_user.username,
                        'email': allocation.allocated_user.email
                    }

                # 添加订单信息
                if allocation.source_order:
                    allocation_dict['source_order'] = allocation.source_order.to_dict()

                if allocation.allocated_order:
                    allocation_dict['allocated_order'] = allocation.allocated_order.to_dict()

                allocation_list.append(allocation_dict)

            return allocation_list

        except HTTPException:
            raise
        except Exception as e:
            print(f"获取分配列表失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取分配列表失败"
            )

    @staticmethod
    async def get_user_allocated_packages(
        db: AsyncSession,
        user: User,
        organization_id: int
    ) -> List[Dict[str, Any]]:
        """
        获取用户被分配的套餐列表

        Args:
            db: 数据库会话
            user: 用户对象
            organization_id: 组织ID

        Returns:
            List[Dict]: 用户被分配的套餐列表
        """
        try:
            # 检查用户是否为组织成员
            await OrganizationPermissionService.require_organization_member(
                db, user, organization_id, "查看分配的套餐"
            )

            # 查询用户被分配的套餐
            query = (
                select(PackageAllocation)
                .options(
                    selectinload(PackageAllocation.owner_user),
                    selectinload(PackageAllocation.source_order),
                    selectinload(PackageAllocation.allocated_order)
                )
                .where(
                    and_(
                        PackageAllocation.organization_id == organization_id,
                        PackageAllocation.allocated_user_id == user.id,
                        PackageAllocation.status == "active"
                    )
                )
                .order_by(desc(PackageAllocation.allocated_at))
            )

            result = await db.execute(query)
            allocations = result.scalars().all()

            # 转换为字典格式并添加分配者信息
            allocation_list = []
            for allocation in allocations:
                allocation_dict = allocation.to_dict()

                # 添加分配者信息
                if allocation.owner_user:
                    allocation_dict['owner_user'] = {
                        'id': allocation.owner_user.id,
                        'username': allocation.owner_user.username,
                        'email': allocation.owner_user.email
                    }

                # 添加订单信息
                if allocation.allocated_order:
                    allocation_dict['allocated_order'] = allocation.allocated_order.to_dict()

                allocation_list.append(allocation_dict)

            return allocation_list

        except Exception as e:
            print(f"获取用户分配套餐失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取用户分配套餐失败"
            )
