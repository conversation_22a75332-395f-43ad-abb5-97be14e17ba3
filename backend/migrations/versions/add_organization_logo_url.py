"""add logo_url to organizations

Revision ID: add_organization_logo_url
Revises: f66116de4cba
Create Date: 2025-08-18

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_organization_logo_url'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column('organizations', sa.Column('logo_url', sa.String(length=512), nullable=True))


def downgrade() -> None:
    op.drop_column('organizations', 'logo_url')


