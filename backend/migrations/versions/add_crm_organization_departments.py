"""Add CRM organization departments tables

Revision ID: add_crm_organization_departments
Revises: 
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_crm_organization_departments'
down_revision = 'add_organization_logo_url'
branch_labels = None
depends_on = None


def upgrade():
    """创建CRM组织管理所需的部门表和成员部门关联表"""
    
    # 1. 创建部门表 (crm_departments)
    op.create_table(
        'crm_departments',
        sa.Column('id', sa.Integer(), nullable=False, comment='部门ID，自增主键'),
        sa.Column('organization_id', sa.Integer(), nullable=False, comment='所属组织ID'),
        sa.Column('name', sa.String(length=100), nullable=False, comment='部门名称'),
        sa.Column('code', sa.String(length=50), nullable=False, comment='部门编码'),
        sa.Column('parent_id', sa.Integer(), nullable=True, comment='上级部门ID（支持树形结构）'),
        sa.<PERSON>umn('manager_id', sa.Integer(), nullable=True, comment='部门负责人ID（关联到组织成员）'),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true', comment='是否激活'),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), comment='更新时间'),
        
        # 主键
        sa.PrimaryKeyConstraint('id'),
        
        # 外键约束
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['parent_id'], ['crm_departments.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['manager_id'], ['organization_members.id'], ondelete='SET NULL'),
        
        # 唯一约束
        sa.UniqueConstraint('organization_id', 'code', name='uq_org_dept_code'),
        sa.UniqueConstraint('organization_id', 'name', name='uq_org_dept_name'),
        
        comment='组织部门表，支持树形层级结构'
    )
    
    # 创建部门表索引
    op.create_index('idx_crm_departments_org_id', 'crm_departments', ['organization_id'])
    op.create_index('idx_crm_departments_parent_id', 'crm_departments', ['parent_id'])
    op.create_index('idx_crm_departments_manager_id', 'crm_departments', ['manager_id'])
    op.create_index('idx_crm_departments_active', 'crm_departments', ['organization_id', 'is_active'])
    
    # 2. 创建成员部门关联表 (crm_member_departments)
    op.create_table(
        'crm_member_departments',
        sa.Column('id', sa.Integer(), nullable=False, comment='关联记录ID，自增主键'),
        sa.Column('organization_id', sa.Integer(), nullable=False, comment='所属组织ID'),
        sa.Column('member_id', sa.Integer(), nullable=False, comment='组织成员ID'),
        sa.Column('department_id', sa.Integer(), nullable=False, comment='部门ID'),
        sa.Column('position', sa.String(length=20), nullable=False, server_default='member', comment='在该部门的职位角色'),
        sa.Column('is_primary', sa.Boolean(), nullable=False, server_default='false', comment='是否为主要部门'),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), comment='更新时间'),
        
        # 主键
        sa.PrimaryKeyConstraint('id'),
        
        # 外键约束
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['member_id'], ['organization_members.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['department_id'], ['crm_departments.id'], ondelete='CASCADE'),
        
        # 唯一约束：确保同一成员在同一部门只有一条记录
        sa.UniqueConstraint('member_id', 'department_id', name='uq_member_department'),
        
        # 检查约束：职位角色枚举
        sa.CheckConstraint("position IN ('owner', 'manager', 'member')", name='chk_position'),
        
        comment='组织成员部门关联表，支持多部门分配'
    )
    
    # 创建成员部门关联表索引
    op.create_index('idx_crm_member_depts_org_id', 'crm_member_departments', ['organization_id'])
    op.create_index('idx_crm_member_depts_member_id', 'crm_member_departments', ['member_id'])
    op.create_index('idx_crm_member_depts_dept_id', 'crm_member_departments', ['department_id'])
    op.create_index('idx_crm_member_depts_primary', 'crm_member_departments', ['member_id', 'is_primary'])
    op.create_index('idx_crm_member_depts_position', 'crm_member_departments', ['department_id', 'position'])

    # 3. 创建更新时间触发器（如果数据库支持）
    # 为 crm_departments 表创建更新时间触发器
    op.execute("""
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    op.execute("""
        CREATE TRIGGER update_crm_departments_updated_at
        BEFORE UPDATE ON crm_departments
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    """)
    
    op.execute("""
        CREATE TRIGGER update_crm_member_departments_updated_at
        BEFORE UPDATE ON crm_member_departments
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    """)


def downgrade():
    """删除CRM组织管理相关表"""
    
    # 删除触发器
    op.execute("DROP TRIGGER IF EXISTS update_crm_member_departments_updated_at ON crm_member_departments;")
    op.execute("DROP TRIGGER IF EXISTS update_crm_departments_updated_at ON crm_departments;")
    op.execute("DROP FUNCTION IF EXISTS update_updated_at_column();")
    
    # 删除表（注意删除顺序，先删除有外键依赖的表）
    op.drop_table('crm_member_departments')
    op.drop_table('crm_departments')