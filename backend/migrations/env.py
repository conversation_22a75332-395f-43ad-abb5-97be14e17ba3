from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# 导入项目模型
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
from app.db.database import Base
from app.models import client
from app.ai_selection.db import models

# import all the models, so that Base has them registered for
# 'autogenerate' support
# from myapp import mymodel
from app.models.user import *
from app.models.client import *
from app.models.credit_payment import *
from app.ai_selection.db.models import *

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# 从应用程序配置中获取数据库连接信息
from app.core.config import settings
# 将异步URL转换为同步URL (postgresql+asyncpg:// -> postgresql://) 
# 注意到 settings.DATABASE_URL 使用的是 asyncpg 驱动，而 Alembic 需要使用同步的 psycopg2 驱动。
# 所以我们需要将 URL 从 postgresql+asyncpg://... 转换为 postgresql://...。
sqlalchemy_url = settings.DATABASE_URL.replace('postgresql+asyncpg://', 'postgresql://')
config.set_main_option('sqlalchemy.url', sqlalchemy_url)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
