--  windows先指定编码 $env:PGCLIENTENCODING="UTF8"
-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(64) UNIQUE NOT NULL,
    email VARCHAR(120) UNIQUE NOT NULL,
    nickname VA<PERSON><PERSON><PERSON>(64),
    password_hash VARCHAR(128),
    role VARCHAR(20) DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    openid VARCHAR(64) UNIQUE,
    unionid VARCHAR(64),
    wechat_nickname VARCHAR(64),
    avatar_url VARCHAR(255),
    login_type VARCHAR(20) DEFAULT 'password',
    wechat_refresh_token VARCHAR(255),
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建触发器函数，用于自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为 users 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_users_updated_at') THEN
        CREATE TRIGGER update_users_updated_at
        BEFORE UPDATE ON users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引（严格按照模型定义）
CREATE INDEX IF NOT EXISTS ix_users_id ON users(id);
CREATE INDEX IF NOT EXISTS ix_users_username ON users(username);
CREATE INDEX IF NOT EXISTS ix_users_email ON users(email);
CREATE INDEX IF NOT EXISTS ix_users_openid ON users(openid);
CREATE INDEX IF NOT EXISTS ix_users_unionid ON users(unionid);
CREATE UNIQUE INDEX IF NOT EXISTS uq_users_openid ON users(openid) WHERE openid IS NOT NULL;

-- 添加注释
COMMENT ON TABLE users IS '用户表，存储系统用户信息';
COMMENT ON COLUMN users.id IS '用户 ID，自增主键';
COMMENT ON COLUMN users.username IS '用户名，唯一，用于登录';
COMMENT ON COLUMN users.email IS '电子邮箱，唯一';
COMMENT ON COLUMN users.nickname IS '用户昵称，可选';
COMMENT ON COLUMN users.password_hash IS '密码哈希值，微信登录用户可为空';
COMMENT ON COLUMN users.role IS '用户角色，默认为普通用户(user)';
COMMENT ON COLUMN users.is_active IS '账户是否激活，默认为激活状态';
COMMENT ON COLUMN users.openid IS '微信OpenID，唯一标识';
COMMENT ON COLUMN users.unionid IS '微信UnionID，跨应用唯一标识';
COMMENT ON COLUMN users.wechat_nickname IS '微信昵称';
COMMENT ON COLUMN users.avatar_url IS '头像URL';
COMMENT ON COLUMN users.login_type IS '登录方式：password(密码登录)、wechat(微信登录)';
COMMENT ON COLUMN users.wechat_refresh_token IS '微信refresh_token，用于刷新access_token';
COMMENT ON COLUMN users.last_login IS '最后登录时间';
COMMENT ON COLUMN users.created_at IS '创建时间';
COMMENT ON COLUMN users.updated_at IS '更新时间';

-- 创建客户表（多租户支持）
CREATE TABLE IF NOT EXISTS clients (
    id SERIAL PRIMARY KEY,
    id_hashed VARCHAR(16) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(20),
    phone VARCHAR(50),
    email VARCHAR(120),
    location VARCHAR(100),
    address TEXT,
    id_card VARCHAR(50),
    passport VARCHAR(50),
    id_card_issuer VARCHAR(100),
    id_card_validity VARCHAR(100),
    passport_issue_place VARCHAR(100),
    passport_issue_date VARCHAR(50),
    passport_expiry VARCHAR(50),
    service_type VARCHAR(50) DEFAULT 'undergraduate',
    profile_type BOOLEAN DEFAULT TRUE NOT NULL,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    organization_id INTEGER REFERENCES organizations(id) ON DELETE SET NULL,
    is_archived BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 clients 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_clients_updated_at') THEN
        CREATE TRIGGER update_clients_updated_at
        BEFORE UPDATE ON clients
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引（严格按照模型定义）
CREATE INDEX IF NOT EXISTS idx_clients_id ON clients(id);
CREATE INDEX IF NOT EXISTS idx_clients_id_hashed ON clients(id_hashed);
CREATE INDEX IF NOT EXISTS idx_clients_name ON clients(name);
CREATE INDEX IF NOT EXISTS idx_clients_user_id ON clients(user_id);
CREATE INDEX IF NOT EXISTS idx_clients_organization_id ON clients(organization_id);
CREATE INDEX IF NOT EXISTS idx_clients_profile_type ON clients(profile_type);
CREATE INDEX IF NOT EXISTS idx_clients_user_org ON clients(user_id, organization_id);

-- 添加注释
COMMENT ON TABLE clients IS '客户表，支持多租户身份的客户信息管理';
COMMENT ON COLUMN clients.id IS '客户 ID，自增主键';
COMMENT ON COLUMN clients.id_hashed IS '客户哈希ID，用于前端展示和API调用';
COMMENT ON COLUMN clients.name IS '客户姓名';
COMMENT ON COLUMN clients.gender IS '性别：male(男)、female(女)、unknown(未知)';
COMMENT ON COLUMN clients.phone IS '电话号码';
COMMENT ON COLUMN clients.email IS '电子邮箱';
COMMENT ON COLUMN clients.location IS '所在城市';
COMMENT ON COLUMN clients.address IS '详细地址';
COMMENT ON COLUMN clients.id_card IS '身份证号码';
COMMENT ON COLUMN clients.passport IS '护照号码';
COMMENT ON COLUMN clients.id_card_issuer IS '身份证签发机构';
COMMENT ON COLUMN clients.id_card_validity IS '身份证有效期';
COMMENT ON COLUMN clients.passport_issue_place IS '护照签发地';
COMMENT ON COLUMN clients.passport_issue_date IS '护照签发日期';
COMMENT ON COLUMN clients.passport_expiry IS '护照过期日期';
COMMENT ON COLUMN clients.service_type IS '服务类型：undergraduate(本科)、master(硕士)等';
COMMENT ON COLUMN clients.profile_type IS '客户档案类型：true=完整建档，false=快速建档';
COMMENT ON COLUMN clients.user_id IS '关联的用户ID（顾问）';
COMMENT ON COLUMN clients.organization_id IS '关联的组织ID，NULL表示个人身份下的客户';
COMMENT ON COLUMN clients.is_archived IS '是否已归档（服务完成）';
COMMENT ON COLUMN clients.created_at IS '创建时间';
COMMENT ON COLUMN clients.updated_at IS '更新时间';

-- 创建教育经历表
CREATE TABLE IF NOT EXISTS education (
    id SERIAL PRIMARY KEY,
    school VARCHAR(200) NOT NULL,
    major VARCHAR(200),
    degree VARCHAR(50),
    gpa VARCHAR(20),
    gpa_scale VARCHAR(20),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 education 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_education_updated_at') THEN
        CREATE TRIGGER update_education_updated_at
        BEFORE UPDATE ON education
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_education_client_id ON education(client_id);

-- 添加注释
COMMENT ON TABLE education IS '教育经历表，存储客户的教育背景信息';
COMMENT ON COLUMN education.id IS '教育经历 ID，自增主键';
COMMENT ON COLUMN education.school IS '学校名称';
COMMENT ON COLUMN education.major IS '专业';
COMMENT ON COLUMN education.degree IS '学位：bachelor(本科)、master(硕士)、phd(博士)等';
COMMENT ON COLUMN education.gpa IS 'GPA成绩';
COMMENT ON COLUMN education.gpa_scale IS 'GPA制度：100(百分制)、4.0(四分制)、5.0(五分制)';
COMMENT ON COLUMN education.start_date IS '开始日期';
COMMENT ON COLUMN education.end_date IS '结束日期';
COMMENT ON COLUMN education.description IS '描述信息';
COMMENT ON COLUMN education.client_id IS '关联的客户ID';
COMMENT ON COLUMN education."order" IS '排序顺序';
COMMENT ON COLUMN education.created_at IS '创建时间';
COMMENT ON COLUMN education.updated_at IS '更新时间';

-- 创建学术经历表
CREATE TABLE IF NOT EXISTS academic (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(200),
    date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 academic 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_academic_updated_at') THEN
        CREATE TRIGGER update_academic_updated_at
        BEFORE UPDATE ON academic
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_academic_client_id ON academic(client_id);

-- 添加注释
COMMENT ON TABLE academic IS '学术经历表，存储客户的学术经历信息';
COMMENT ON COLUMN academic.id IS '学术经历 ID，自增主键';
COMMENT ON COLUMN academic.title IS '项目主题';
COMMENT ON COLUMN academic.type IS '项目类型：毕业设计、论文、科研项目、学科课程项目、大学生创业项目、其他等';
COMMENT ON COLUMN academic.date IS '研究日期';
COMMENT ON COLUMN academic.description IS '详细描述';
COMMENT ON COLUMN academic.client_id IS '关联的客户ID';
COMMENT ON COLUMN academic."order" IS '排序顺序';
COMMENT ON COLUMN academic.created_at IS '创建时间';
COMMENT ON COLUMN academic.updated_at IS '更新时间';

-- 创建工作经历表
CREATE TABLE IF NOT EXISTS work (
    id SERIAL PRIMARY KEY,
    company VARCHAR(200) NOT NULL,
    position VARCHAR(200),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 work 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_work_updated_at') THEN
        CREATE TRIGGER update_work_updated_at
        BEFORE UPDATE ON work
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_work_client_id ON work(client_id);

-- 添加注释
COMMENT ON TABLE work IS '工作经历表，存储客户的工作/实习经历信息';
COMMENT ON COLUMN work.id IS '工作经历 ID，自增主键';
COMMENT ON COLUMN work.company IS '公司/单位名称';
COMMENT ON COLUMN work.position IS '职位';
COMMENT ON COLUMN work.start_date IS '开始日期';
COMMENT ON COLUMN work.end_date IS '结束日期';
COMMENT ON COLUMN work.description IS '工作描述';
COMMENT ON COLUMN work.client_id IS '关联的客户ID';
COMMENT ON COLUMN work."order" IS '排序顺序';
COMMENT ON COLUMN work.created_at IS '创建时间';
COMMENT ON COLUMN work.updated_at IS '更新时间';

-- 创建课外活动表
CREATE TABLE IF NOT EXISTS activities (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    role VARCHAR(100),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 activities 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_activities_updated_at') THEN
        CREATE TRIGGER update_activities_updated_at
        BEFORE UPDATE ON activities
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_activities_client_id ON activities(client_id);

-- 添加注释
COMMENT ON TABLE activities IS '课外活动表，存储客户的课外活动信息';
COMMENT ON COLUMN activities.id IS '活动 ID，自增主键';
COMMENT ON COLUMN activities.name IS '活动名称';
COMMENT ON COLUMN activities.role IS '角色/职位';
COMMENT ON COLUMN activities.start_date IS '开始日期';
COMMENT ON COLUMN activities.end_date IS '结束日期';
COMMENT ON COLUMN activities.description IS '活动描述';
COMMENT ON COLUMN activities.client_id IS '关联的客户ID';
COMMENT ON COLUMN activities."order" IS '排序顺序';
COMMENT ON COLUMN activities.created_at IS '创建时间';
COMMENT ON COLUMN activities.updated_at IS '更新时间';

-- 创建奖项荣誉表
CREATE TABLE IF NOT EXISTS awards (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    level VARCHAR(100),
    date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 awards 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_awards_updated_at') THEN
        CREATE TRIGGER update_awards_updated_at
        BEFORE UPDATE ON awards
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_awards_client_id ON awards(client_id);

-- 添加注释
COMMENT ON TABLE awards IS '奖项荣誉表，存储客户获得的奖项和荣誉信息';
COMMENT ON COLUMN awards.id IS '奖项 ID，自增主键';
COMMENT ON COLUMN awards.name IS '奖项名称';
COMMENT ON COLUMN awards.level IS '奖项级别';
COMMENT ON COLUMN awards.date IS '获奖日期';
COMMENT ON COLUMN awards.description IS '奖项描述';
COMMENT ON COLUMN awards.client_id IS '关联的客户ID';
COMMENT ON COLUMN awards."order" IS '排序顺序';
COMMENT ON COLUMN awards.created_at IS '创建时间';
COMMENT ON COLUMN awards.updated_at IS '更新时间';

-- 创建技能表
CREATE TABLE IF NOT EXISTS skills (
    id SERIAL PRIMARY KEY,
    type VARCHAR(100) NOT NULL,
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 skills 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_skills_updated_at') THEN
        CREATE TRIGGER update_skills_updated_at
        BEFORE UPDATE ON skills
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_skills_client_id ON skills(client_id);

-- 添加注释
COMMENT ON TABLE skills IS '技能表，存储客户的技能信息';
COMMENT ON COLUMN skills.id IS '技能 ID，自增主键';
COMMENT ON COLUMN skills.type IS '技能类型，如"专业技能"、"综合技能"等';
COMMENT ON COLUMN skills.description IS '技能描述，具体的技能内容';
COMMENT ON COLUMN skills.client_id IS '关联的客户ID';
COMMENT ON COLUMN skills."order" IS '排序顺序';
COMMENT ON COLUMN skills.created_at IS '创建时间';
COMMENT ON COLUMN skills.updated_at IS '更新时间';

-- 创建语言成绩表
CREATE TABLE IF NOT EXISTS language_scores (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    score VARCHAR(50) NOT NULL,
    date VARCHAR(50),
    validity VARCHAR(50),
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 language_scores 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_language_scores_updated_at') THEN
        CREATE TRIGGER update_language_scores_updated_at
        BEFORE UPDATE ON language_scores
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_language_scores_client_id ON language_scores(client_id);

-- 添加注释
COMMENT ON TABLE language_scores IS '语言成绩表，存储客户的语言考试成绩信息';
COMMENT ON COLUMN language_scores.id IS '语言成绩 ID，自增主键';
COMMENT ON COLUMN language_scores.type IS '考试类型：toefl、ielts、gre、gmat等';
COMMENT ON COLUMN language_scores.score IS '分数';
COMMENT ON COLUMN language_scores.date IS '考试日期';
COMMENT ON COLUMN language_scores.validity IS '有效期';
COMMENT ON COLUMN language_scores.client_id IS '关联的客户ID';
COMMENT ON COLUMN language_scores."order" IS '排序顺序';
COMMENT ON COLUMN language_scores.created_at IS '创建时间';
COMMENT ON COLUMN language_scores.updated_at IS '更新时间';

-- 创建个人想法表
CREATE TABLE IF NOT EXISTS thoughts (
    id SERIAL PRIMARY KEY,
    target_major TEXT,
    personal_understanding TEXT,
    academic_match TEXT,
    work_match TEXT,
    future_plan TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 thoughts 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_thoughts_updated_at') THEN
        CREATE TRIGGER update_thoughts_updated_at
        BEFORE UPDATE ON thoughts
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_thoughts_client_id ON thoughts(client_id);

-- 添加注释
COMMENT ON TABLE thoughts IS '个人想法表，存储客户的个人想法信息';
COMMENT ON COLUMN thoughts.id IS '想法 ID，自增主键';
COMMENT ON COLUMN thoughts.target_major IS '目标专业申请动机';
COMMENT ON COLUMN thoughts.personal_understanding IS '专业个人解读';
COMMENT ON COLUMN thoughts.academic_match IS '学术经历匹配';
COMMENT ON COLUMN thoughts.work_match IS '工作经历匹配';
COMMENT ON COLUMN thoughts.future_plan IS '未来规划';
COMMENT ON COLUMN thoughts.client_id IS '关联的客户ID';
COMMENT ON COLUMN thoughts.created_at IS '创建时间';
COMMENT ON COLUMN thoughts.updated_at IS '更新时间';

-- 创建背景自定义模块表
CREATE TABLE IF NOT EXISTS background_custom_modules (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT,
    "order" INTEGER DEFAULT 0,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 background_custom_modules 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_background_custom_modules_updated_at') THEN
        CREATE TRIGGER update_background_custom_modules_updated_at
        BEFORE UPDATE ON background_custom_modules
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_background_custom_modules_client_id ON background_custom_modules(client_id);

-- 添加注释
COMMENT ON TABLE background_custom_modules IS '背景自定义模块表，存储客户的背景模块信息';
COMMENT ON COLUMN background_custom_modules.id IS '背景模块 ID，自增主键';
COMMENT ON COLUMN background_custom_modules.title IS '模块标题';
COMMENT ON COLUMN background_custom_modules.content IS '模块内容';
COMMENT ON COLUMN background_custom_modules."order" IS '排序顺序';
COMMENT ON COLUMN background_custom_modules.client_id IS '关联的客户ID';
COMMENT ON COLUMN background_custom_modules.created_at IS '创建时间';
COMMENT ON COLUMN background_custom_modules.updated_at IS '更新时间';

-- 创建想法自定义模块表
CREATE TABLE IF NOT EXISTS thought_custom_modules (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT,
    "order" INTEGER DEFAULT 0,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 thought_custom_modules 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_thought_custom_modules_updated_at') THEN
        CREATE TRIGGER update_thought_custom_modules_updated_at
        BEFORE UPDATE ON thought_custom_modules
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_thought_custom_modules_client_id ON thought_custom_modules(client_id);

-- 添加注释
COMMENT ON TABLE thought_custom_modules IS '想法自定义模块表，存储客户的想法模块信息';
COMMENT ON COLUMN thought_custom_modules.id IS '想法模块 ID，自增主键';
COMMENT ON COLUMN thought_custom_modules.title IS '模块标题';
COMMENT ON COLUMN thought_custom_modules.content IS '模块内容';
COMMENT ON COLUMN thought_custom_modules."order" IS '排序顺序';
COMMENT ON COLUMN thought_custom_modules.client_id IS '关联的客户ID';
COMMENT ON COLUMN thought_custom_modules.created_at IS '创建时间';
COMMENT ON COLUMN thought_custom_modules.updated_at IS '更新时间';


-- 注意：client_programs 表的创建被移动到 ai_selection_programs 表之后
-- 因为它需要引用 ai_selection_programs(id)

-- 创建AI写作-CV内容表
CREATE TABLE IF NOT EXISTS ai_writing_cv (
    id SERIAL PRIMARY KEY,
    version_name VARCHAR(200) NOT NULL DEFAULT '未命名版本',
    target_major VARCHAR(500),
    content_markdown TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 ai_writing_cv 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_ai_writing_cv_updated_at') THEN
        CREATE TRIGGER update_ai_writing_cv_updated_at
        BEFORE UPDATE ON ai_writing_cv
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引（严格按照模型定义）
CREATE INDEX IF NOT EXISTS idx_ai_writing_cv_id ON ai_writing_cv(id);
CREATE INDEX IF NOT EXISTS idx_ai_writing_cv_client_id ON ai_writing_cv(client_id);

-- 添加注释
COMMENT ON TABLE ai_writing_cv IS 'AI写作-CV内容表，存储客户的CV内容，支持多版本';
COMMENT ON COLUMN ai_writing_cv.id IS 'CV记录ID，自增主键';
COMMENT ON COLUMN ai_writing_cv.version_name IS 'CV版本名称';
COMMENT ON COLUMN ai_writing_cv.target_major IS '目标专业';
COMMENT ON COLUMN ai_writing_cv.content_markdown IS 'CV内容的Markdown格式';
COMMENT ON COLUMN ai_writing_cv.client_id IS '关联的客户ID';
COMMENT ON COLUMN ai_writing_cv.created_at IS '创建时间';
COMMENT ON COLUMN ai_writing_cv.updated_at IS '更新时间';

-- 创建AI写作-推荐信内容表
CREATE TABLE IF NOT EXISTS ai_writing_rl (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    content_markdown TEXT NOT NULL,
    version_name VARCHAR(200) NOT NULL,
    recommender_name VARCHAR(200) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 为 ai_writing_rl 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_ai_writing_rl_updated_at') THEN
        CREATE TRIGGER update_ai_writing_rl_updated_at
        BEFORE UPDATE ON ai_writing_rl
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ai_writing_rl_client_id ON ai_writing_rl(client_id);

-- 添加注释
COMMENT ON TABLE ai_writing_rl IS 'AI写作-推荐信内容表，存储客户的推荐信内容，支持多版本';
COMMENT ON COLUMN ai_writing_rl.id IS '推荐信记录ID，自增主键';
COMMENT ON COLUMN ai_writing_rl.client_id IS '关联的客户ID';
COMMENT ON COLUMN ai_writing_rl.content_markdown IS '推荐信内容的Markdown格式';
COMMENT ON COLUMN ai_writing_rl.version_name IS '推荐信版本名称';
COMMENT ON COLUMN ai_writing_rl.recommender_name IS '推荐人姓名';
COMMENT ON COLUMN ai_writing_rl.created_at IS '创建时间';
COMMENT ON COLUMN ai_writing_rl.updated_at IS '更新时间';

-- 创建AI写作-PS内容表
CREATE TABLE IF NOT EXISTS ai_writing_ps (
    id SERIAL PRIMARY KEY,
    version_name VARCHAR(200) NOT NULL DEFAULT '未命名版本',
    target_major VARCHAR(500),
    content_markdown TEXT,
    word_limit INTEGER,
    paragraph_setting INTEGER,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    cv_id INTEGER REFERENCES ai_writing_cv(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为已存在的表添加cv_id字段（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'ai_writing_ps' AND column_name = 'cv_id') THEN
        ALTER TABLE ai_writing_ps ADD COLUMN cv_id INTEGER REFERENCES ai_writing_cv(id) ON DELETE SET NULL;
    END IF;
END $$;

-- 为 ai_writing_ps 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_ai_writing_ps_updated_at') THEN
        CREATE TRIGGER update_ai_writing_ps_updated_at
        BEFORE UPDATE ON ai_writing_ps
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ai_writing_ps_client_id ON ai_writing_ps(client_id);
CREATE INDEX IF NOT EXISTS idx_ai_writing_ps_cv_id ON ai_writing_ps(cv_id);

-- 添加注释
COMMENT ON TABLE ai_writing_ps IS 'AI写作-PS内容表，存储客户的Personal Statement内容，支持多版本';
COMMENT ON COLUMN ai_writing_ps.id IS 'PS记录ID，自增主键';
COMMENT ON COLUMN ai_writing_ps.version_name IS 'PS版本名称';
COMMENT ON COLUMN ai_writing_ps.target_major IS '目标专业（院校-学位-专业）';
COMMENT ON COLUMN ai_writing_ps.content_markdown IS 'PS内容的Markdown格式';
COMMENT ON COLUMN ai_writing_ps.word_limit IS '字数限制';
COMMENT ON COLUMN ai_writing_ps.paragraph_setting IS '段落设置';
COMMENT ON COLUMN ai_writing_ps.client_id IS '关联的客户ID';
COMMENT ON COLUMN ai_writing_ps.cv_id IS '关联的CV版本ID，用于记录PS生成时使用的CV';
COMMENT ON COLUMN ai_writing_ps.created_at IS '创建时间';
COMMENT ON COLUMN ai_writing_ps.updated_at IS '更新时间';

-- 创建Token使用记录表
CREATE TABLE IF NOT EXISTS token_usage (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    service_type VARCHAR(50) NOT NULL,
    operation_type VARCHAR(100) NOT NULL,
    tokens_consumed INTEGER NOT NULL,
    request_id VARCHAR(100),
    model_name VARCHAR(50),
    prompt_length INTEGER,
    response_length INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_token_usage_user_id ON token_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_service_type ON token_usage(service_type);
CREATE INDEX IF NOT EXISTS idx_token_usage_request_id ON token_usage(request_id);
CREATE INDEX IF NOT EXISTS idx_token_usage_created_at ON token_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_token_usage_user_service ON token_usage(user_id, service_type);

-- 添加注释
COMMENT ON TABLE token_usage IS 'Token使用记录表，记录每次LLM调用的token消耗';
COMMENT ON COLUMN token_usage.id IS '记录ID，自增主键';
COMMENT ON COLUMN token_usage.user_id IS '用户ID，外键关联users表';
COMMENT ON COLUMN token_usage.service_type IS '服务类型：ai_selection, ai_writing等';
COMMENT ON COLUMN token_usage.operation_type IS '操作类型：user_profile, recommendation_reason等';
COMMENT ON COLUMN token_usage.tokens_consumed IS '本次消耗的token数量';
COMMENT ON COLUMN token_usage.request_id IS '请求ID，用于关联同一次推荐的多个LLM调用';
COMMENT ON COLUMN token_usage.model_name IS '使用的模型名称';
COMMENT ON COLUMN token_usage.prompt_length IS '提示词长度（字符数）';
COMMENT ON COLUMN token_usage.response_length IS '响应长度（字符数）';
COMMENT ON COLUMN token_usage.error_message IS '如果调用失败，记录错误信息';
COMMENT ON COLUMN token_usage.created_at IS '记录创建时间';

-- 创建用户Token使用汇总表
CREATE TABLE IF NOT EXISTS user_token_summary (
    user_id INTEGER PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_tokens_consumed INTEGER DEFAULT 0 NOT NULL,
    ai_selection_tokens INTEGER DEFAULT 0 NOT NULL,
    ai_writing_tokens INTEGER DEFAULT 0 NOT NULL,
    total_requests INTEGER DEFAULT 0 NOT NULL,
    last_usage_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 user_token_summary 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_token_summary_updated_at') THEN
        CREATE TRIGGER update_user_token_summary_updated_at
        BEFORE UPDATE ON user_token_summary
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 添加注释
COMMENT ON TABLE user_token_summary IS '用户Token使用汇总表，用于快速查询用户的总token消耗';
COMMENT ON COLUMN user_token_summary.user_id IS '用户ID，主键';
COMMENT ON COLUMN user_token_summary.total_tokens_consumed IS '总消耗token数量';
COMMENT ON COLUMN user_token_summary.ai_selection_tokens IS 'AI选校服务消耗的token数量';
COMMENT ON COLUMN user_token_summary.ai_writing_tokens IS 'AI写作服务消耗的token数量';
COMMENT ON COLUMN user_token_summary.total_requests IS '总请求次数';
COMMENT ON COLUMN user_token_summary.last_usage_at IS '最后使用时间';
COMMENT ON COLUMN user_token_summary.created_at IS '记录创建时间';
COMMENT ON COLUMN user_token_summary.updated_at IS '记录更新时间';

-- 创建用户积分账户表（多租户支持）
CREATE TABLE IF NOT EXISTS user_credit_accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
    credit_balance INTEGER DEFAULT 0 NOT NULL,
    total_credits_purchased INTEGER DEFAULT 0 NOT NULL,
    total_credits_consumed INTEGER DEFAULT 0 NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    last_recharge_at TIMESTAMP,
    last_consumption_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 确保用户在每个身份下只有一个积分账户
    CONSTRAINT uq_user_credit_identity UNIQUE (user_id, organization_id)
);

-- 为 user_credit_accounts 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_credit_accounts_updated_at') THEN
        CREATE TRIGGER update_user_credit_accounts_updated_at
        BEFORE UPDATE ON user_credit_accounts
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_credit_accounts_user_id ON user_credit_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_credit_accounts_organization_id ON user_credit_accounts(organization_id);
CREATE INDEX IF NOT EXISTS idx_user_credit_accounts_user_org ON user_credit_accounts(user_id, organization_id);

-- 添加注释
COMMENT ON TABLE user_credit_accounts IS '用户积分账户表，支持多租户身份的积分管理';
COMMENT ON COLUMN user_credit_accounts.id IS '积分账户ID，自增主键';
COMMENT ON COLUMN user_credit_accounts.user_id IS '用户ID，外键';
COMMENT ON COLUMN user_credit_accounts.organization_id IS '组织ID，NULL表示个人身份，非NULL表示组织身份';
COMMENT ON COLUMN user_credit_accounts.credit_balance IS '当前积分余额';
COMMENT ON COLUMN user_credit_accounts.total_credits_purchased IS '累计购买积分数';
COMMENT ON COLUMN user_credit_accounts.total_credits_consumed IS '累计消费积分数';
COMMENT ON COLUMN user_credit_accounts.is_active IS '账户是否激活';
COMMENT ON COLUMN user_credit_accounts.last_recharge_at IS '最后充值时间';
COMMENT ON COLUMN user_credit_accounts.last_consumption_at IS '最后消费时间';
COMMENT ON COLUMN user_credit_accounts.created_at IS '账户创建时间';
COMMENT ON COLUMN user_credit_accounts.updated_at IS '账户更新时间';

-- 创建支付订单表（多租户支持）
CREATE TABLE IF NOT EXISTS payment_orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(64) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
    user_credit_account_id INTEGER NOT NULL REFERENCES user_credit_accounts(id) ON DELETE CASCADE,
    amount DECIMAL(10, 2) NOT NULL,
    credits INTEGER NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    package_id VARCHAR(50),
    status VARCHAR(20) DEFAULT 'pending' NOT NULL,
    trade_no VARCHAR(100),
    payment_time TIMESTAMP,
    refund_amount DECIMAL(10, 2) DEFAULT 0 NOT NULL,
    refund_reason TEXT,
    refund_time TIMESTAMP,
    remark TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL
);


-- 迁移：为 payment_orders 增加充值类型与套餐数量列（幂等）
DO $$
BEGIN
    -- 新增 recharge_type（充值类型：package, credits）
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'payment_orders' AND column_name = 'recharge_type'
    ) THEN
        ALTER TABLE payment_orders
            ADD COLUMN recharge_type VARCHAR(20) NOT NULL DEFAULT 'package';
        COMMENT ON COLUMN payment_orders.recharge_type IS '充值类型：package, credits';
    END IF;
END $$;

-- 为 payment_orders 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_payment_orders_updated_at') THEN
        CREATE TRIGGER update_payment_orders_updated_at
        BEFORE UPDATE ON payment_orders
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_payment_orders_user_id ON payment_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_organization_id ON payment_orders(organization_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_order_no ON payment_orders(order_no);
CREATE INDEX IF NOT EXISTS idx_payment_orders_status ON payment_orders(status);
CREATE INDEX IF NOT EXISTS idx_payment_orders_created_at ON payment_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_orders_user_status ON payment_orders(user_id, status);
CREATE INDEX IF NOT EXISTS idx_payment_orders_user_org ON payment_orders(user_id, organization_id);

-- 添加注释
COMMENT ON TABLE payment_orders IS '支付订单表，支持多租户身份的充值订单管理';
COMMENT ON COLUMN payment_orders.id IS '订单ID，自增主键';
COMMENT ON COLUMN payment_orders.order_no IS '订单号，唯一';
COMMENT ON COLUMN payment_orders.user_id IS '用户ID';
COMMENT ON COLUMN payment_orders.organization_id IS '组织ID，NULL表示个人身份充值，非NULL表示组织身份充值';
COMMENT ON COLUMN payment_orders.user_credit_account_id IS '积分账户ID';
COMMENT ON COLUMN payment_orders.amount IS '支付金额（元）';
COMMENT ON COLUMN payment_orders.credits IS '购买的积分数量';
COMMENT ON COLUMN payment_orders.payment_method IS '支付方式：alipay, wechat';
COMMENT ON COLUMN payment_orders.package_id IS '套餐ID：personal_standard, personal_professional, business_flagship, enterprise_flagship';
COMMENT ON COLUMN payment_orders.status IS '订单状态：pending, paid, failed, cancelled, refunded, partial_refunded';
COMMENT ON COLUMN payment_orders.trade_no IS '第三方支付交易号';
COMMENT ON COLUMN payment_orders.payment_time IS '支付完成时间';
COMMENT ON COLUMN payment_orders.refund_amount IS '退款金额';
COMMENT ON COLUMN payment_orders.refund_reason IS '退款原因';
COMMENT ON COLUMN payment_orders.refund_time IS '退款时间';
COMMENT ON COLUMN payment_orders.remark IS '订单备注';
COMMENT ON COLUMN payment_orders.created_at IS '订单创建时间';
COMMENT ON COLUMN payment_orders.updated_at IS '订单更新时间';
COMMENT ON COLUMN payment_orders.expires_at IS '订单过期时间';

-- 创建积分交易记录表（多租户支持）
CREATE TABLE IF NOT EXISTS credit_transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
    user_credit_account_id INTEGER NOT NULL REFERENCES user_credit_accounts(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL,
    amount INTEGER NOT NULL,
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    order_id INTEGER REFERENCES payment_orders(id),
    service_type VARCHAR(50),
    operation_type VARCHAR(100),
    request_id VARCHAR(100),
    tokens_consumed INTEGER,
    conversion_rate INTEGER DEFAULT 100 NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_organization_id ON credit_transactions(organization_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_type ON credit_transactions(user_id, transaction_type);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_created_at ON credit_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_request_id ON credit_transactions(request_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_org ON credit_transactions(user_id, organization_id);

-- 添加注释
COMMENT ON TABLE credit_transactions IS '积分交易记录表，支持多租户身份的积分变动历史';
COMMENT ON COLUMN credit_transactions.id IS '交易记录ID，自增主键';
COMMENT ON COLUMN credit_transactions.user_id IS '用户ID';
COMMENT ON COLUMN credit_transactions.organization_id IS '组织ID，NULL表示个人身份交易，非NULL表示组织身份交易';
COMMENT ON COLUMN credit_transactions.user_credit_account_id IS '积分账户ID';
COMMENT ON COLUMN credit_transactions.transaction_type IS '交易类型：recharge, consumption, refund';
COMMENT ON COLUMN credit_transactions.amount IS '积分变动数量（正数为增加，负数为减少）';
COMMENT ON COLUMN credit_transactions.balance_before IS '交易前积分余额';
COMMENT ON COLUMN credit_transactions.balance_after IS '交易后积分余额';
COMMENT ON COLUMN credit_transactions.order_id IS '关联的支付订单ID（充值时）';
COMMENT ON COLUMN credit_transactions.service_type IS '服务类型（消费时）：ai_selection, ai_writing等';
COMMENT ON COLUMN credit_transactions.operation_type IS '操作类型（消费时）';
COMMENT ON COLUMN credit_transactions.request_id IS '请求ID（消费时）';
COMMENT ON COLUMN credit_transactions.tokens_consumed IS '消费的tokens数量';
COMMENT ON COLUMN credit_transactions.conversion_rate IS '换算比例（1积分=多少tokens）';
COMMENT ON COLUMN credit_transactions.description IS '交易描述';
COMMENT ON COLUMN credit_transactions.created_at IS '交易时间';

-- 创建AI选校项目表（严格按照 csv_to_postgres.py 中的 AISelectionProgram 模型定义）
CREATE TABLE IF NOT EXISTS ai_selection_programs (
    id SERIAL PRIMARY KEY,

    -- 学校信息
    school_name_cn VARCHAR(50) NOT NULL,
    school_name_en VARCHAR(100),
    school_region VARCHAR(10),
    school_labels VARCHAR(200),
    school_ranks VARCHAR(100),
    school_qs_rank VARCHAR(20),

    -- 项目基本信息
    program_code INTEGER,
    program_website VARCHAR(300),
    program_name_cn VARCHAR(100) NOT NULL,
    program_name_en VARCHAR(200),
    program_category VARCHAR(50),
    program_direction VARCHAR(50),
    faculty VARCHAR(50),
    enrollment_time VARCHAR(50),
    program_duration VARCHAR(50),
    program_tuition VARCHAR(100),

    -- 项目申请相关信息
    application_time TEXT,
    application_requirements TEXT,
    gpa_requirements TEXT,
    language_requirements TEXT,
    interview_type VARCHAR(50),
    interview_experience TEXT,

    -- 项目详情
    program_objectives TEXT,
    courses TEXT,
    consultant_analysis TEXT,

    -- 其他信息
    other_cost VARCHAR(100),
    degree VARCHAR(10),
    degree_evaluation TEXT,

    -- 向量嵌入（用于语义匹配）
    embedding JSONB,

    -- 时间戳字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 为 ai_selection_programs 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_ai_selection_programs_updated_at') THEN
    CREATE TRIGGER update_ai_selection_programs_updated_at
        BEFORE UPDATE ON ai_selection_programs
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引（严格按照 csv_to_postgres.py 中的索引定义）
CREATE INDEX IF NOT EXISTS idx_ai_selection_programs_school_name_cn ON ai_selection_programs(school_name_cn);
CREATE INDEX IF NOT EXISTS idx_ai_selection_programs_school_name_en ON ai_selection_programs(school_name_en);
CREATE INDEX IF NOT EXISTS idx_ai_selection_programs_program_name_cn ON ai_selection_programs(program_name_cn);
CREATE INDEX IF NOT EXISTS idx_ai_selection_programs_program_name_en ON ai_selection_programs(program_name_en);

-- 添加注释（严格按照 csv_to_postgres.py 中的注释）
COMMENT ON TABLE ai_selection_programs IS 'AI选校项目表，存储学校和专业项目信息';
COMMENT ON COLUMN ai_selection_programs.id IS 'ID，自增主键';

-- 学校信息注释
COMMENT ON COLUMN ai_selection_programs.school_name_cn IS '学校中文名';
COMMENT ON COLUMN ai_selection_programs.school_name_en IS '学校英文名';
COMMENT ON COLUMN ai_selection_programs.school_region IS '学校所在地区';
COMMENT ON COLUMN ai_selection_programs.school_labels IS '学校标签';
COMMENT ON COLUMN ai_selection_programs.school_ranks IS '学校排名信息';
COMMENT ON COLUMN ai_selection_programs.school_qs_rank IS '学校QS排名';

-- 项目基本信息注释
COMMENT ON COLUMN ai_selection_programs.program_code IS '项目代码';
COMMENT ON COLUMN ai_selection_programs.program_website IS '项目官网';
COMMENT ON COLUMN ai_selection_programs.program_name_cn IS '项目中文名';
COMMENT ON COLUMN ai_selection_programs.program_name_en IS '项目英文名';
COMMENT ON COLUMN ai_selection_programs.program_category IS '项目类别';
COMMENT ON COLUMN ai_selection_programs.program_direction IS '项目方向';
COMMENT ON COLUMN ai_selection_programs.faculty IS '所在学院';
COMMENT ON COLUMN ai_selection_programs.enrollment_time IS '入学时间';
COMMENT ON COLUMN ai_selection_programs.program_duration IS '项目时长';
COMMENT ON COLUMN ai_selection_programs.program_tuition IS '项目学费';

-- 项目申请相关信息注释
COMMENT ON COLUMN ai_selection_programs.application_time IS '申请时间';
COMMENT ON COLUMN ai_selection_programs.application_requirements IS '申请要求';
COMMENT ON COLUMN ai_selection_programs.gpa_requirements IS 'GPA要求';
COMMENT ON COLUMN ai_selection_programs.language_requirements IS '语言要求';
COMMENT ON COLUMN ai_selection_programs.interview_type IS '面试类型';
COMMENT ON COLUMN ai_selection_programs.interview_experience IS '面试经验';

-- 项目详情注释
COMMENT ON COLUMN ai_selection_programs.program_objectives IS '项目培养目标';
COMMENT ON COLUMN ai_selection_programs.courses IS '课程设置';
COMMENT ON COLUMN ai_selection_programs.consultant_analysis IS '顾问分析';

-- 其他信息注释
COMMENT ON COLUMN ai_selection_programs.other_cost IS '其他费用';
COMMENT ON COLUMN ai_selection_programs.degree IS '申请学位类型';
COMMENT ON COLUMN ai_selection_programs.degree_evaluation IS '留服认证';

-- 向量嵌入和时间戳注释
COMMENT ON COLUMN ai_selection_programs.embedding IS '项目描述的向量嵌入';
COMMENT ON COLUMN ai_selection_programs.created_at IS '创建时间(历史列，逐步迁移到 created_at)';
COMMENT ON COLUMN ai_selection_programs.updated_at IS '更新时间(历史列，逐步迁移到 updated_at)';

-- 迁移：为 ai_selection_programs 对齐触发器所需列名（created_at/updated_at）且不改变业务数据
DO $$
BEGIN
    -- 1) 新增并回填（若不存在）
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'ai_selection_programs' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE ai_selection_programs ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
    END IF;
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'ai_selection_programs' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE ai_selection_programs ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
    END IF;

    -- 回填历史列（如果存在）
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'ai_selection_programs' AND column_name = 'update_at'
    ) THEN
        UPDATE ai_selection_programs SET updated_at = update_at WHERE update_at IS NOT NULL;
    END IF;
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'ai_selection_programs' AND column_name = 'create_at'
    ) THEN
        UPDATE ai_selection_programs SET created_at = create_at WHERE create_at IS NOT NULL;
    END IF;

    -- 2) 原子删除旧列
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'ai_selection_programs' AND column_name = 'update_at'
    ) THEN
        ALTER TABLE ai_selection_programs DROP COLUMN update_at;
    END IF;
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'ai_selection_programs' AND column_name = 'create_at'
    ) THEN
        ALTER TABLE ai_selection_programs DROP COLUMN create_at;
    END IF;
END $$;

-- 创建客户定校书表（现在可以安全引用 ai_selection_programs）
CREATE TABLE IF NOT EXISTS client_programs (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    program_id INTEGER NOT NULL REFERENCES ai_selection_programs(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_client_programs_client_id ON client_programs(client_id);
CREATE INDEX IF NOT EXISTS idx_client_programs_program_id ON client_programs(program_id);

-- 添加注释
COMMENT ON TABLE client_programs IS '客户定校书表, 存储客户选择的学校项目信息';
COMMENT ON COLUMN client_programs.id IS '定校记录 ID, 自增主键';
COMMENT ON COLUMN client_programs.client_id IS '关联的客户ID';
COMMENT ON COLUMN client_programs.program_id IS '关联的项目ID';
COMMENT ON COLUMN client_programs.created_at IS '创建时间';

-- 创建AI选校案例表
CREATE TABLE IF NOT EXISTS ai_selection_cases (
    id SERIAL PRIMARY KEY,
    offer_school VARCHAR(200),
    offer_program VARCHAR(200),
    offer_program_id INTEGER REFERENCES ai_selection_programs(id),
    offer_program_code INTEGER,
    offer_region VARCHAR(100),
    offer_degree VARCHAR(50),
    offer_major_direction VARCHAR(200),
    student_name VARCHAR(100),
    undergraduate_school VARCHAR(200),
    undergraduate_school_tier VARCHAR(50),
    undergraduate_major VARCHAR(200),
    gpa REAL,
    language_score VARCHAR(200),
    key_experiences TEXT,
    embedding JSONB
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ai_selection_cases_offer_program_id ON ai_selection_cases(offer_program_id);
CREATE INDEX IF NOT EXISTS idx_ai_selection_cases_offer_school ON ai_selection_cases(offer_school);
CREATE INDEX IF NOT EXISTS idx_ai_selection_cases_undergraduate_school ON ai_selection_cases(undergraduate_school);

-- 添加注释
COMMENT ON TABLE ai_selection_cases IS 'AI选校案例表，存储申请成功案例信息';
COMMENT ON COLUMN ai_selection_cases.id IS 'ID，自增主键';
COMMENT ON COLUMN ai_selection_cases.offer_school IS '录取学校';
COMMENT ON COLUMN ai_selection_cases.offer_program IS '录取项目';
COMMENT ON COLUMN ai_selection_cases.offer_program_id IS '录取项目ID';
COMMENT ON COLUMN ai_selection_cases.offer_program_code IS '录取项目代码';
COMMENT ON COLUMN ai_selection_cases.offer_region IS '录取地区';
COMMENT ON COLUMN ai_selection_cases.offer_degree IS '录取学位';
COMMENT ON COLUMN ai_selection_cases.offer_major_direction IS '录取专业方向';
COMMENT ON COLUMN ai_selection_cases.student_name IS '学生姓名';
COMMENT ON COLUMN ai_selection_cases.undergraduate_school IS '本科学校';
COMMENT ON COLUMN ai_selection_cases.undergraduate_school_tier IS '本科学校层次';
COMMENT ON COLUMN ai_selection_cases.undergraduate_major IS '本科专业';
COMMENT ON COLUMN ai_selection_cases.gpa IS '绩点';
COMMENT ON COLUMN ai_selection_cases.language_score IS '语言成绩';
COMMENT ON COLUMN ai_selection_cases.key_experiences IS '关键经历描述';
COMMENT ON COLUMN ai_selection_cases.embedding IS '案例描述的向量嵌入';

-- 创建境内院校表
CREATE TABLE IF NOT EXISTS ai_selection_home_schools (
    id SERIAL PRIMARY KEY,
    school_name VARCHAR(200) NOT NULL,
    school_name_en VARCHAR(200),
    school_type VARCHAR(100),
    school_code VARCHAR(50),
    location VARCHAR(100),
    authority VARCHAR(200),
    ranking_ruanke INTEGER,
    school_tier VARCHAR(50),
    gpa_requirements JSON,
    remarks TEXT
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ai_selection_home_schools_school_name ON ai_selection_home_schools(school_name);
CREATE INDEX IF NOT EXISTS idx_ai_selection_home_schools_school_name_en ON ai_selection_home_schools(school_name_en);
CREATE INDEX IF NOT EXISTS idx_ai_selection_home_schools_school_code ON ai_selection_home_schools(school_code);
CREATE INDEX IF NOT EXISTS idx_ai_selection_home_schools_school_tier ON ai_selection_home_schools(school_tier);

-- 添加注释
COMMENT ON TABLE ai_selection_home_schools IS '境内院校表，存储境内院校信息数据';
COMMENT ON COLUMN ai_selection_home_schools.id IS 'ID，自增主键';
COMMENT ON COLUMN ai_selection_home_schools.school_name IS '学校名称';
COMMENT ON COLUMN ai_selection_home_schools.school_name_en IS '学校英文名称';
COMMENT ON COLUMN ai_selection_home_schools.school_type IS '学校类型';
COMMENT ON COLUMN ai_selection_home_schools.school_code IS '学校标识码';
COMMENT ON COLUMN ai_selection_home_schools.location IS '所在地';
COMMENT ON COLUMN ai_selection_home_schools.authority IS '主管部门';
COMMENT ON COLUMN ai_selection_home_schools.ranking_ruanke IS '软科排名';
COMMENT ON COLUMN ai_selection_home_schools.school_tier IS '学校层级(如985、211、双一流等)';
COMMENT ON COLUMN ai_selection_home_schools.gpa_requirements IS 'GPA要求，JSON格式存储各目标院校的GPA要求';
COMMENT ON COLUMN ai_selection_home_schools.remarks IS '备注';

-- 创建境外院校表
CREATE TABLE IF NOT EXISTS ai_selection_abroad_schools (
    id SERIAL PRIMARY KEY,
    school_name_cn VARCHAR(100) NOT NULL,
    school_name_en VARCHAR(100),
    school_code INTEGER,
    school_city VARCHAR(20),
    school_region VARCHAR(10),
    school_qs_rank VARCHAR(20),
    school_usnews_rank VARCHAR(20),
    school_the_rank VARCHAR(20),
    school_arwu_rank VARCHAR(20),
    school_ranks VARCHAR(100),
    school_labels VARCHAR(200),
    school_logo_url VARCHAR(100)
);

-- 创建索引（严格按照 csv_to_postgres.py 中的索引定义）
CREATE INDEX IF NOT EXISTS idx_ai_selection_abroad_schools_school_name_cn ON ai_selection_abroad_schools(school_name_cn);
CREATE INDEX IF NOT EXISTS idx_ai_selection_abroad_schools_school_name_en ON ai_selection_abroad_schools(school_name_en);
CREATE INDEX IF NOT EXISTS idx_ai_selection_abroad_schools_school_code ON ai_selection_abroad_schools(school_code);

-- 添加注释
COMMENT ON TABLE ai_selection_abroad_schools IS '境外院校表，存储境外院校信息数据';
COMMENT ON COLUMN ai_selection_abroad_schools.id IS 'ID，自增主键';
COMMENT ON COLUMN ai_selection_abroad_schools.school_name_cn IS '学校中文名';
COMMENT ON COLUMN ai_selection_abroad_schools.school_name_en IS '学校英文名';
COMMENT ON COLUMN ai_selection_abroad_schools.school_code IS '学校代码';
COMMENT ON COLUMN ai_selection_abroad_schools.school_city IS '学校所在城市';
COMMENT ON COLUMN ai_selection_abroad_schools.school_region IS '学校所在地区/国家';
COMMENT ON COLUMN ai_selection_abroad_schools.school_qs_rank IS '学校QS排名';
COMMENT ON COLUMN ai_selection_abroad_schools.school_usnews_rank IS '学校US News排名';
COMMENT ON COLUMN ai_selection_abroad_schools.school_the_rank IS '学校THE排名';
COMMENT ON COLUMN ai_selection_abroad_schools.school_arwu_rank IS '学校ARWU排名';
COMMENT ON COLUMN ai_selection_abroad_schools.school_ranks IS '学校综合排名信息';
COMMENT ON COLUMN ai_selection_abroad_schools.school_labels IS '学校标签';
COMMENT ON COLUMN ai_selection_abroad_schools.school_logo_url IS '学校logo链接';

-- 创建兑换码表
CREATE TABLE IF NOT EXISTS redeem_codes (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    credits INTEGER,
    package_id VARCHAR(50),
    description VARCHAR(200),
    max_uses INTEGER NOT NULL DEFAULT 1,
    used_count INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    expires_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- 为 redeem_codes 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_redeem_codes_updated_at') THEN
        CREATE TRIGGER update_redeem_codes_updated_at
        BEFORE UPDATE ON redeem_codes
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS ix_redeem_codes_id ON redeem_codes(id);
CREATE INDEX IF NOT EXISTS idx_redeem_codes_code ON redeem_codes(code);
CREATE INDEX IF NOT EXISTS idx_redeem_codes_active_expires ON redeem_codes(is_active, expires_at);
CREATE INDEX IF NOT EXISTS idx_redeem_codes_package_id ON redeem_codes(package_id);

-- 添加注释
COMMENT ON TABLE redeem_codes IS '兑换码表，存储积分兑换码信息';
COMMENT ON COLUMN redeem_codes.id IS '兑换码ID，自增主键';
COMMENT ON COLUMN redeem_codes.code IS '兑换码';
COMMENT ON COLUMN redeem_codes.credits IS '兑换码价值（积分数量）- 兼容旧版本';
COMMENT ON COLUMN redeem_codes.package_id IS '兑换的套餐ID';
COMMENT ON COLUMN redeem_codes.description IS '兑换码描述';
COMMENT ON COLUMN redeem_codes.max_uses IS '最大使用次数';
COMMENT ON COLUMN redeem_codes.used_count IS '已使用次数';
COMMENT ON COLUMN redeem_codes.is_active IS '是否激活';
COMMENT ON COLUMN redeem_codes.expires_at IS '过期时间';
COMMENT ON COLUMN redeem_codes.created_at IS '创建时间';
COMMENT ON COLUMN redeem_codes.updated_at IS '更新时间';
COMMENT ON COLUMN redeem_codes.created_by IS '创建者用户ID';

-- 创建兑换码使用记录表
CREATE TABLE IF NOT EXISTS redeem_code_redemptions (
    id SERIAL PRIMARY KEY,
    redeem_code_id INTEGER NOT NULL REFERENCES redeem_codes(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    credits_received INTEGER,
    package_order_id INTEGER REFERENCES payment_orders(id),
    transaction_id INTEGER REFERENCES credit_transactions(id),
    redeemed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS ix_redeem_code_redemptions_id ON redeem_code_redemptions(id);
CREATE INDEX IF NOT EXISTS ix_redeem_code_redemptions_redeem_code_id ON redeem_code_redemptions(redeem_code_id);
CREATE INDEX IF NOT EXISTS ix_redeem_code_redemptions_user_id ON redeem_code_redemptions(user_id);
CREATE INDEX IF NOT EXISTS idx_redeem_code_redemptions_user_code ON redeem_code_redemptions(user_id, redeem_code_id);
CREATE INDEX IF NOT EXISTS idx_redeem_code_redemptions_package_order ON redeem_code_redemptions(package_order_id);

-- 添加注释
COMMENT ON TABLE redeem_code_redemptions IS '兑换码使用记录表，记录用户兑换积分的历史';
COMMENT ON COLUMN redeem_code_redemptions.id IS '使用记录ID，自增主键';
COMMENT ON COLUMN redeem_code_redemptions.redeem_code_id IS '兑换码ID';
COMMENT ON COLUMN redeem_code_redemptions.user_id IS '使用者用户ID';
COMMENT ON COLUMN redeem_code_redemptions.credits_received IS '获得的积分数量';
COMMENT ON COLUMN redeem_code_redemptions.package_order_id IS '关联的套餐订单ID';
COMMENT ON COLUMN redeem_code_redemptions.transaction_id IS '关联的积分交易记录ID';
COMMENT ON COLUMN redeem_code_redemptions.redeemed_at IS '兑换时间';

-- 初始化兑换码数据
INSERT INTO redeem_codes (
    id,
    code,
    credits,
    package_id,
    description,
    max_uses,
    used_count,
    is_active,
    expires_at,
    created_at,
    updated_at,
    created_by
) VALUES
-- 现有的体验邀请码
(
    1,
    'JSAYQH',
    20,
    'trial_package',
    '体验邀请码',
    10,
    4,
    true,
    '2026-09-18 14:03:58',
    '2025-07-21 14:27:30',
    '2025-07-21 18:06:36',
    1
)
ON CONFLICT (id) DO NOTHING;

-- 重置兑换码序列（确保后续插入的ID从正确的值开始）
SELECT setval('redeem_codes_id_seq', (SELECT COALESCE(MAX(id), 1) FROM redeem_codes));

-- ==================== 套餐配置管理表 ====================

-- 创建套餐配置表
CREATE TABLE IF NOT EXISTS packages_config (
    id SERIAL PRIMARY KEY,
    package_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    amount DECIMAL(10, 2) NOT NULL,
    credits INTEGER NOT NULL,
    bonus INTEGER DEFAULT 0 NOT NULL,
    billing_cycle VARCHAR(20) NOT NULL,
    monthly_price DECIMAL(10, 2),
    min_users INTEGER DEFAULT 1 NOT NULL,
    max_users INTEGER DEFAULT 1 NOT NULL,
    validity_days INTEGER,
    is_trial BOOLEAN DEFAULT FALSE NOT NULL,
    contact_required BOOLEAN DEFAULT FALSE NOT NULL,
    contact_phone VARCHAR(20),
    features JSONB,
    bulk_discount JSONB,
    export_limit_excel INTEGER DEFAULT -1 NOT NULL,
    export_limit_pdf INTEGER DEFAULT -1 NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    sort_order INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 添加注释
COMMENT ON TABLE packages_config IS '套餐配置表，管理系统中所有套餐的配置信息';
COMMENT ON COLUMN packages_config.id IS '配置ID，自增主键';
COMMENT ON COLUMN packages_config.package_id IS '套餐ID，唯一标识';
COMMENT ON COLUMN packages_config.name IS '套餐名称';
COMMENT ON COLUMN packages_config.description IS '套餐描述';
COMMENT ON COLUMN packages_config.amount IS '套餐价格（元）';
COMMENT ON COLUMN packages_config.credits IS '套餐包含的积分数量';
COMMENT ON COLUMN packages_config.bonus IS '赠送积分数量';
COMMENT ON COLUMN packages_config.billing_cycle IS '计费周期：trial, yearly, monthly';
COMMENT ON COLUMN packages_config.monthly_price IS '月均价格（元）';
COMMENT ON COLUMN packages_config.min_users IS '最少用户数';
COMMENT ON COLUMN packages_config.max_users IS '最多用户数';
COMMENT ON COLUMN packages_config.validity_days IS '有效期天数（体验套餐使用）';
COMMENT ON COLUMN packages_config.is_trial IS '是否为体验套餐';
COMMENT ON COLUMN packages_config.contact_required IS '是否需要联系销售';
COMMENT ON COLUMN packages_config.contact_phone IS '联系电话';
COMMENT ON COLUMN packages_config.features IS '套餐功能特性列表';
COMMENT ON COLUMN packages_config.bulk_discount IS '批量折扣配置';
COMMENT ON COLUMN packages_config.export_limit_excel IS 'Excel导出次数限制（-1表示无限制，0表示不允许，正数表示具体次数）';
COMMENT ON COLUMN packages_config.export_limit_pdf IS 'PDF导出次数限制（-1表示无限制，0表示不允许，正数表示具体次数）';
COMMENT ON COLUMN packages_config.is_active IS '是否启用';
COMMENT ON COLUMN packages_config.sort_order IS '排序顺序';
COMMENT ON COLUMN packages_config.created_at IS '创建时间';
COMMENT ON COLUMN packages_config.updated_at IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_packages_config_package_id ON packages_config(package_id);
CREATE INDEX IF NOT EXISTS idx_packages_config_active_sort ON packages_config(is_active, sort_order);

-- 为 packages_config 表添加触发器
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_packages_config_updated_at') THEN
        CREATE TRIGGER update_packages_config_updated_at
        BEFORE UPDATE ON packages_config
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- ==================== 套餐配置数据迁移 ====================

-- 插入现有的套餐配置数据（从硬编码配置迁移到数据库）
INSERT INTO packages_config (
    package_id, name, description, amount, credits, bonus, billing_cycle, monthly_price,
    min_users, max_users, validity_days, is_trial, contact_required, contact_phone,
    features, bulk_discount, is_active, sort_order
) VALUES
-- 体验套餐
(
    'trial_package',
    '体验套餐',
    '29.9元体验套餐，包含20积分，有效期3天，适合新用户体验',
    29.90,
    20,
    0,
    'trial',
    29.90,
    1,
    1,
    3,
    true,
    false,
    null,
    '["不限次数选校方案匹配", "20积分", "数据方案导出", "定校书不限项目个数"]'::jsonb,
    null,
    true,
    1
),
-- 个人标准版
(
    'personal_standard',
    '个人标准版',
    '90元/人/月，按年付费，包含500积分/年，适合个人用户日常使用',
    1080.00,
    500,
    0,
    'yearly',
    90.00,
    1,
    1,
    null,
    false,
    false,
    null,
    '["基础AI功能", "标准客服支持", "数据导出"]'::jsonb,
    null,
    true,
    2
),
-- 个人专业版
(
    'personal_professional',
    '个人专业版',
    '140元/人/月，按年付费，包含1000积分/年，适合专业用户深度使用',
    1680.00,
    1000,
    0,
    'yearly',
    140.00,
    1,
    1,
    null,
    false,
    false,
    null,
    '["高级AI功能", "优先客服支持", "高级数据分析", "API访问"]'::jsonb,
    null,
    true,
    3
),
-- 商业旗舰版
(
    'business_flagship',
    '商业旗舰版',
    '160元/人/月，按年付费，包含1000积分/年，适合小型团队使用',
    1920.00,
    1000,
    0,
    'yearly',
    160.00,
    1,
    999,
    null,
    false,
    false,
    null,
    '["全部AI功能", "专属客服支持", "团队协作", "高级分析", "定制化服务"]'::jsonb,
    '{
        "threshold": 5,
        "discount_rate": 0.8,
        "discounted_amount": 1536.00,
        "discounted_monthly_price": 128.00
    }'::jsonb,
    true,
    4
),
-- 企业旗舰版
(
    'enterprise_flagship',
    '企业旗舰版',
    '企业级解决方案，需要电话咨询定价，提供定制化服务',
    0.00,
    0,
    0,
    'yearly',
    0.00,
    1,
    999999,
    null,
    false,
    true,
    '400-123-4567',
    '["企业级AI功能", "7×24专属支持", "私有化部署", "定制开发", "SLA保障", "数据安全认证"]'::jsonb,
    null,
    true,
    5
)
ON CONFLICT (package_id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    amount = EXCLUDED.amount,
    credits = EXCLUDED.credits,
    bonus = EXCLUDED.bonus,
    billing_cycle = EXCLUDED.billing_cycle,
    monthly_price = EXCLUDED.monthly_price,
    min_users = EXCLUDED.min_users,
    max_users = EXCLUDED.max_users,
    validity_days = EXCLUDED.validity_days,
    is_trial = EXCLUDED.is_trial,
    contact_required = EXCLUDED.contact_required,
    contact_phone = EXCLUDED.contact_phone,
    features = EXCLUDED.features,
    bulk_discount = EXCLUDED.bulk_discount,
    is_active = EXCLUDED.is_active,
    sort_order = EXCLUDED.sort_order,
    updated_at = CURRENT_TIMESTAMP;

-- ==================== 系统通知表 ====================

-- 创建系统通知表
CREATE TABLE IF NOT EXISTS system_notifications (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'general',
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    expire_at TIMESTAMP,
    action_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 为 system_notifications 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_system_notifications_updated_at') THEN
        CREATE TRIGGER update_system_notifications_updated_at
        BEFORE UPDATE ON system_notifications
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_notifications_user_id ON system_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_system_notifications_type ON system_notifications(type);
CREATE INDEX IF NOT EXISTS idx_system_notifications_priority ON system_notifications(priority);
CREATE INDEX IF NOT EXISTS idx_system_notifications_active ON system_notifications(is_active, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_notifications_expire ON system_notifications(expire_at) WHERE expire_at IS NOT NULL;

-- 添加注释
COMMENT ON TABLE system_notifications IS '系统通知表，存储所有通知信息';
COMMENT ON COLUMN system_notifications.id IS '通知ID，自增主键';
COMMENT ON COLUMN system_notifications.title IS '通知标题';
COMMENT ON COLUMN system_notifications.content IS '通知内容';
COMMENT ON COLUMN system_notifications.type IS '通知类型：maintenance(维护), feature(功能), security(安全), promotion(促销), general(一般)';
COMMENT ON COLUMN system_notifications.priority IS '优先级：high(重要), medium(一般), low(普通)';
COMMENT ON COLUMN system_notifications.user_id IS '目标用户ID，为空表示全员通知';
COMMENT ON COLUMN system_notifications.is_active IS '是否激活显示';
COMMENT ON COLUMN system_notifications.expire_at IS '过期时间，过期后自动隐藏';
COMMENT ON COLUMN system_notifications.action_data IS '扩展数据，JSON格式存储点击行为等信息';
COMMENT ON COLUMN system_notifications.created_at IS '创建时间';
COMMENT ON COLUMN system_notifications.updated_at IS '更新时间';

-- ==================== 系统通知视图和函数 ====================

-- 创建用户通知视图
CREATE OR REPLACE VIEW user_notifications_view AS
SELECT
    id,
    title,
    content,
    type,
    priority,
    user_id,
    is_active,
    expire_at,
    action_data,
    created_at,
    updated_at,
    -- 判断是否对指定用户可见
    CASE
        WHEN user_id IS NULL THEN TRUE  -- 全员通知
        ELSE FALSE                      -- 个人通知，需要在应用层过滤
    END as is_public
FROM system_notifications
WHERE is_active = TRUE
  AND (expire_at IS NULL OR expire_at > CURRENT_TIMESTAMP)
ORDER BY priority DESC, created_at DESC;

-- 添加视图注释
COMMENT ON VIEW user_notifications_view IS '用户通知视图，过滤掉已过期和未激活的通知';

-- 创建获取用户通知数量的函数
CREATE OR REPLACE FUNCTION get_user_notification_count(p_user_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    notification_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO notification_count
    FROM system_notifications
    WHERE is_active = TRUE
      AND (expire_at IS NULL OR expire_at > CURRENT_TIMESTAMP)
      AND (user_id IS NULL OR user_id = p_user_id);  -- 全员通知或个人通知

    RETURN COALESCE(notification_count, 0);
END;
$$ LANGUAGE plpgsql;

-- 添加函数注释
COMMENT ON FUNCTION get_user_notification_count(INTEGER) IS '获取指定用户的通知数量';

-- 创建获取用户通知列表的函数
CREATE OR REPLACE FUNCTION get_user_notifications(
    p_user_id INTEGER,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id INTEGER,
    title VARCHAR(200),
    content TEXT,
    type VARCHAR(50),
    priority VARCHAR(20),
    action_data JSONB,
    created_at TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        sn.id,
        sn.title,
        sn.content,
        sn.type,
        sn.priority,
        sn.action_data,
        sn.created_at
    FROM system_notifications sn
    WHERE sn.is_active = TRUE
      AND (sn.expire_at IS NULL OR sn.expire_at > CURRENT_TIMESTAMP)
      AND (sn.user_id IS NULL OR sn.user_id = p_user_id)
    ORDER BY
        CASE sn.priority
            WHEN 'high' THEN 3
            WHEN 'medium' THEN 2
            WHEN 'low' THEN 1
            ELSE 0
        END DESC,
        sn.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- 添加函数注释
COMMENT ON FUNCTION get_user_notifications(INTEGER, INTEGER, INTEGER) IS '获取指定用户的通知列表，支持分页';

-- ==================== 系统通知数据迁移 ====================

-- -- 插入示例通知数据
-- INSERT INTO system_notifications (
--     title, content, type, priority, user_id, is_active, action_data, created_at
-- ) VALUES
-- (
--     '系统维护升级通知',
--     '为了提升系统性能和用户体验，我们将于2025年7月31日19:00（UTC+8）进行系统维护升级。维护期间系统可能暂时不可用，预计维护时间半个小时。感谢您的理解与支持。',
--     'maintenance',
--     'high',
--     NULL,
--     TRUE,
--     '{"action_type": "none"}'::jsonb,
--     '2025-07-29 19:00:00'
-- )
-- ON CONFLICT (id) DO UPDATE SET
--     title = EXCLUDED.title,
--     content = EXCLUDED.content,
--     type = EXCLUDED.type,
--     priority = EXCLUDED.priority,
--     user_id = EXCLUDED.user_id,
--     is_active = EXCLUDED.is_active,
--     action_data = EXCLUDED.action_data,
--     updated_at = CURRENT_TIMESTAMP;

-- ===============================
-- 组织与账号管理系统数据库表
-- 创建日期: 2025-07-31
-- 描述: 包含组织、成员关系、邀请记录、用户登录会话等表
-- ===============================

-- 1. 创建组织表 (organizations)
CREATE TABLE IF NOT EXISTS organizations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 为 organizations 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_organizations_updated_at') THEN
        CREATE TRIGGER update_organizations_updated_at
        BEFORE UPDATE ON organizations
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建组织表索引
CREATE INDEX IF NOT EXISTS idx_organizations_id ON organizations(id);
CREATE INDEX IF NOT EXISTS idx_organizations_name ON organizations(name);
CREATE INDEX IF NOT EXISTS idx_organizations_owner_user_id ON organizations(owner_user_id);
CREATE INDEX IF NOT EXISTS idx_org_name_active ON organizations(name, is_active);
CREATE INDEX IF NOT EXISTS idx_org_owner_active ON organizations(owner_user_id, is_active);

-- 添加组织表注释
COMMENT ON TABLE organizations IS '组织表，存储组织基本信息';
COMMENT ON COLUMN organizations.id IS '组织ID，自增主键';
COMMENT ON COLUMN organizations.name IS '组织名称';
COMMENT ON COLUMN organizations.description IS '组织描述';
COMMENT ON COLUMN organizations.owner_user_id IS '创建者/主账号用户ID';
COMMENT ON COLUMN organizations.is_active IS '组织是否激活';
COMMENT ON COLUMN organizations.created_at IS '创建时间';
COMMENT ON COLUMN organizations.updated_at IS '更新时间';

-- 2. 创建组织成员关系表 (organization_members)
CREATE TABLE IF NOT EXISTS organization_members (
    id SERIAL PRIMARY KEY,
    organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL DEFAULT 'member',
    organization_username VARCHAR(64) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    joined_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 唯一约束
    CONSTRAINT uq_org_member UNIQUE (organization_id, user_id),
    CONSTRAINT uq_org_username UNIQUE (organization_id, organization_username)
);

-- 为 organization_members 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_organization_members_updated_at') THEN
        CREATE TRIGGER update_organization_members_updated_at
        BEFORE UPDATE ON organization_members
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建组织成员表索引
CREATE INDEX IF NOT EXISTS idx_organization_members_id ON organization_members(id);
CREATE INDEX IF NOT EXISTS idx_organization_members_organization_id ON organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_member_org_user ON organization_members(organization_id, user_id);
CREATE INDEX IF NOT EXISTS idx_member_user_active ON organization_members(user_id, is_active);

-- 添加组织成员表注释
COMMENT ON TABLE organization_members IS '组织成员关系表，存储用户与组织的成员关系';
COMMENT ON COLUMN organization_members.id IS '成员关系ID，自增主键';
COMMENT ON COLUMN organization_members.organization_id IS '组织ID';
COMMENT ON COLUMN organization_members.user_id IS '用户ID';
COMMENT ON COLUMN organization_members.role IS '角色：owner/member';
COMMENT ON COLUMN organization_members.organization_username IS '组织内用户名';
COMMENT ON COLUMN organization_members.is_active IS '成员是否激活';
COMMENT ON COLUMN organization_members.joined_at IS '加入时间';
COMMENT ON COLUMN organization_members.created_at IS '创建时间';
COMMENT ON COLUMN organization_members.updated_at IS '更新时间';

-- 3. 创建组织邀请码表 (organization_invitation_codes)
-- 新架构：每个组织在24小时内只能有一个有效的邀请码
CREATE TABLE IF NOT EXISTS organization_invitation_codes (
    id SERIAL PRIMARY KEY,
    organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    invited_by_user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    invitation_code VARCHAR(36) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 3.1 创建组织邀请码使用记录表 (organization_invitation_usages)
-- 记录用户使用邀请码加入组织的历史
CREATE TABLE IF NOT EXISTS organization_invitation_usages (
    id SERIAL PRIMARY KEY,
    invitation_code_id INTEGER NOT NULL REFERENCES organization_invitation_codes(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    organization_username VARCHAR(64) NOT NULL,
    joined_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 为 organization_invitation_codes 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_organization_invitation_codes_updated_at') THEN
        CREATE TRIGGER update_organization_invitation_codes_updated_at
        BEFORE UPDATE ON organization_invitation_codes
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建组织邀请码表索引
CREATE INDEX IF NOT EXISTS idx_organization_invitation_codes_id ON organization_invitation_codes(id);
CREATE INDEX IF NOT EXISTS idx_organization_invitation_codes_organization_id ON organization_invitation_codes(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_invitation_codes_invited_by_user_id ON organization_invitation_codes(invited_by_user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_organization_invitation_codes_invitation_code ON organization_invitation_codes(invitation_code);
CREATE INDEX IF NOT EXISTS idx_invitation_code_org_status ON organization_invitation_codes(organization_id, status);
CREATE INDEX IF NOT EXISTS idx_invitation_code_status_expires ON organization_invitation_codes(status, expires_at);
CREATE INDEX IF NOT EXISTS idx_invitation_code_expires ON organization_invitation_codes(expires_at);

-- 创建组织邀请码使用记录表索引
CREATE INDEX IF NOT EXISTS idx_organization_invitation_usages_id ON organization_invitation_usages(id);
CREATE INDEX IF NOT EXISTS idx_organization_invitation_usages_invitation_code_id ON organization_invitation_usages(invitation_code_id);
CREATE INDEX IF NOT EXISTS idx_organization_invitation_usages_user_id ON organization_invitation_usages(user_id);
CREATE INDEX IF NOT EXISTS idx_organization_invitation_usages_organization_id ON organization_invitation_usages(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_invitation_usages_joined_at ON organization_invitation_usages(joined_at);

-- 添加唯一约束确保用户不能重复使用同一个邀请码

-- 注意：此语句假设约束 'uq_invitation_usage_user' 尚不存在。
-- 如果约束已存在，执行此语句将会报错
-- 不支持：ADD CONSTRAINT IF NOT EXISTS uq_invitation_usage_user
ALTER TABLE organization_invitation_usages
ADD CONSTRAINT uq_invitation_usage_user
UNIQUE (invitation_code_id, user_id);

-- 添加组织邀请码表注释
COMMENT ON TABLE organization_invitation_codes IS '组织邀请码表，每个组织在24小时内只能有一个有效邀请码';
COMMENT ON COLUMN organization_invitation_codes.id IS '邀请码ID，自增主键';
COMMENT ON COLUMN organization_invitation_codes.organization_id IS '组织ID';
COMMENT ON COLUMN organization_invitation_codes.invited_by_user_id IS '邀请人用户ID';
COMMENT ON COLUMN organization_invitation_codes.invitation_code IS '邀请码，UUID格式';
COMMENT ON COLUMN organization_invitation_codes.status IS '邀请码状态：active(有效)/expired(已过期)/revoked(已撤销)';
COMMENT ON COLUMN organization_invitation_codes.expires_at IS '过期时间，每个邀请码有效期为24小时';
COMMENT ON COLUMN organization_invitation_codes.created_at IS '创建时间';
COMMENT ON COLUMN organization_invitation_codes.updated_at IS '更新时间';

-- 添加组织邀请码使用记录表注释
COMMENT ON TABLE organization_invitation_usages IS '邀请码使用记录表，记录用户使用邀请码加入组织的历史';
COMMENT ON COLUMN organization_invitation_usages.id IS '使用记录ID，自增主键';
COMMENT ON COLUMN organization_invitation_usages.invitation_code_id IS '邀请码ID';
COMMENT ON COLUMN organization_invitation_usages.user_id IS '用户ID';
COMMENT ON COLUMN organization_invitation_usages.organization_id IS '组织ID';
COMMENT ON COLUMN organization_invitation_usages.organization_username IS '用户在组织中的用户名';
COMMENT ON COLUMN organization_invitation_usages.joined_at IS '用户加入组织的时间';
COMMENT ON COLUMN organization_invitation_usages.created_at IS '创建时间';

-- 4. 创建用户登录会话表 (user_login_sessions)
CREATE TABLE IF NOT EXISTS user_login_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    current_identity_type VARCHAR(20) NOT NULL DEFAULT 'personal',
    current_organization_id INTEGER REFERENCES organizations(id) ON DELETE SET NULL,
    last_identity_type VARCHAR(20),
    last_organization_id INTEGER REFERENCES organizations(id) ON DELETE SET NULL,
    session_token VARCHAR(36) UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 为 user_login_sessions 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_login_sessions_updated_at') THEN
        CREATE TRIGGER update_user_login_sessions_updated_at
        BEFORE UPDATE ON user_login_sessions
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建用户登录会话表索引
CREATE INDEX IF NOT EXISTS idx_user_login_sessions_id ON user_login_sessions(id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_login_sessions_user_id ON user_login_sessions(user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_login_sessions_session_token ON user_login_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_session_user_token ON user_login_sessions(user_id, session_token);
CREATE INDEX IF NOT EXISTS idx_session_current_org ON user_login_sessions(current_organization_id);

-- 添加用户登录会话表注释
COMMENT ON TABLE user_login_sessions IS '用户登录会话表，记录用户当前登录身份和偏好';
COMMENT ON COLUMN user_login_sessions.id IS '会话记录ID，自增主键';
COMMENT ON COLUMN user_login_sessions.user_id IS '用户ID，唯一';
COMMENT ON COLUMN user_login_sessions.current_identity_type IS '当前身份类型：personal/organization';
COMMENT ON COLUMN user_login_sessions.current_organization_id IS '当前组织ID，个人身份时为空';
COMMENT ON COLUMN user_login_sessions.last_identity_type IS '上次身份类型';
COMMENT ON COLUMN user_login_sessions.last_organization_id IS '上次组织ID';
COMMENT ON COLUMN user_login_sessions.session_token IS '会话令牌，用于标识唯一会话';
COMMENT ON COLUMN user_login_sessions.created_at IS '创建时间';
COMMENT ON COLUMN user_login_sessions.updated_at IS '更新时间';

-- ==================== 导出限制字段迁移 ====================

-- 为现有的 packages_config 表添加导出限制字段（如果不存在）
DO $$
BEGIN
    -- 添加 Excel 导出限制字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'packages_config' AND column_name = 'export_limit_excel') THEN
        ALTER TABLE packages_config ADD COLUMN export_limit_excel INTEGER DEFAULT -1 NOT NULL;
        COMMENT ON COLUMN packages_config.export_limit_excel IS 'Excel导出次数限制（-1表示无限制，0表示不允许，正数表示具体次数）';
    END IF;

    -- 添加 PDF 导出限制字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'packages_config' AND column_name = 'export_limit_pdf') THEN
        ALTER TABLE packages_config ADD COLUMN export_limit_pdf INTEGER DEFAULT -1 NOT NULL;
        COMMENT ON COLUMN packages_config.export_limit_pdf IS 'PDF导出次数限制（-1表示无限制，0表示不允许，正数表示具体次数）';
    END IF;
END $$;

-- 更新现有套餐的导出限制配置（基于原有硬编码配置）
UPDATE packages_config SET
    export_limit_excel = -1,  -- 暂时全部设置为无限次
    export_limit_pdf = -1     -- 为后期区分excel导出无限和pdf导出计费做铺垫
WHERE export_limit_excel IS NULL OR export_limit_pdf IS NULL;

-- ==================== 多租户积分系统迁移 ====================

-- 为现有表添加多租户支持字段（如果不存在）
DO $$
BEGIN
    -- 为 user_credit_accounts 表添加 organization_id 字段（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'user_credit_accounts' AND column_name = 'organization_id') THEN
        ALTER TABLE user_credit_accounts ADD COLUMN organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE;

        -- 移除原有的 UNIQUE 约束（如果存在）
        ALTER TABLE user_credit_accounts DROP CONSTRAINT IF EXISTS user_credit_accounts_user_id_key;

        -- 添加新的复合唯一约束
        ALTER TABLE user_credit_accounts ADD CONSTRAINT uq_user_credit_identity UNIQUE (user_id, organization_id);

        -- 添加索引
        CREATE INDEX IF NOT EXISTS idx_user_credit_accounts_organization_id ON user_credit_accounts(organization_id);
        CREATE INDEX IF NOT EXISTS idx_user_credit_accounts_user_org ON user_credit_accounts(user_id, organization_id);

        -- 更新注释
        COMMENT ON COLUMN user_credit_accounts.organization_id IS '组织ID，NULL表示个人身份，非NULL表示组织身份';
        COMMENT ON TABLE user_credit_accounts IS '用户积分账户表，支持多租户身份的积分管理';
    END IF;

    -- 为 payment_orders 表添加 organization_id 字段（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'payment_orders' AND column_name = 'organization_id') THEN
        ALTER TABLE payment_orders ADD COLUMN organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE;

        -- 添加索引
        CREATE INDEX IF NOT EXISTS idx_payment_orders_organization_id ON payment_orders(organization_id);
        CREATE INDEX IF NOT EXISTS idx_payment_orders_user_org ON payment_orders(user_id, organization_id);

        -- 更新注释
        COMMENT ON COLUMN payment_orders.organization_id IS '组织ID，NULL表示个人身份充值，非NULL表示组织身份充值';
        COMMENT ON TABLE payment_orders IS '支付订单表，支持多租户身份的充值订单管理';
    END IF;

    -- 为 credit_transactions 表添加 organization_id 字段（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'credit_transactions' AND column_name = 'organization_id') THEN
        ALTER TABLE credit_transactions ADD COLUMN organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE;

        -- 添加索引
        CREATE INDEX IF NOT EXISTS idx_credit_transactions_organization_id ON credit_transactions(organization_id);
        CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_org ON credit_transactions(user_id, organization_id);

        -- 更新注释
        COMMENT ON COLUMN credit_transactions.organization_id IS '组织ID，NULL表示个人身份交易，非NULL表示组织身份交易';
        COMMENT ON TABLE credit_transactions IS '积分交易记录表，支持多租户身份的积分变动历史';
    END IF;

    -- 为 clients 表添加 organization_id 字段（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'clients' AND column_name = 'organization_id') THEN
        ALTER TABLE clients ADD COLUMN organization_id INTEGER REFERENCES organizations(id) ON DELETE SET NULL;

        -- 添加索引
        CREATE INDEX IF NOT EXISTS idx_clients_organization_id ON clients(organization_id);
        CREATE INDEX IF NOT EXISTS idx_clients_user_org ON clients(user_id, organization_id);

        -- 更新注释
        COMMENT ON COLUMN clients.organization_id IS '关联的组织ID，NULL表示个人身份下的客户';
        COMMENT ON TABLE clients IS '客户表，支持多租户身份的客户信息管理';
    END IF;

    -- 创建套餐分配表（组织权益分配系统）
    CREATE TABLE IF NOT EXISTS package_allocations (
        id SERIAL PRIMARY KEY,
        organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
        owner_user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        allocated_user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        source_order_id INTEGER NOT NULL REFERENCES payment_orders(id) ON DELETE CASCADE,
        allocated_order_id INTEGER REFERENCES payment_orders(id) ON DELETE CASCADE,
        package_id VARCHAR(50) NOT NULL,
        status VARCHAR(20) DEFAULT 'active' NOT NULL,
        allocated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        revoked_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- 添加索引以优化查询性能
    CREATE INDEX IF NOT EXISTS idx_package_allocations_organization_id ON package_allocations(organization_id);
    CREATE INDEX IF NOT EXISTS idx_package_allocations_owner_user ON package_allocations(owner_user_id);
    CREATE INDEX IF NOT EXISTS idx_package_allocations_allocated_user ON package_allocations(allocated_user_id);
    CREATE INDEX IF NOT EXISTS idx_package_allocations_source_order ON package_allocations(source_order_id);
    CREATE INDEX IF NOT EXISTS idx_package_allocations_allocated_order ON package_allocations(allocated_order_id);
    CREATE INDEX IF NOT EXISTS idx_package_allocations_status ON package_allocations(status);
    CREATE INDEX IF NOT EXISTS idx_package_allocations_org_user_status ON package_allocations(organization_id, allocated_user_id, status);

    -- 添加唯一约束，防止重复分配
    CREATE UNIQUE INDEX IF NOT EXISTS idx_package_allocations_unique_active
    ON package_allocations(organization_id, allocated_user_id, package_id)
    WHERE status = 'active';

    -- 添加注释
    COMMENT ON TABLE package_allocations IS '套餐分配表，记录组织内套餐权益分配关系';
    COMMENT ON COLUMN package_allocations.id IS '分配记录ID，自增主键';
    COMMENT ON COLUMN package_allocations.organization_id IS '组织ID';
    COMMENT ON COLUMN package_allocations.owner_user_id IS '主账号用户ID（套餐购买者）';
    COMMENT ON COLUMN package_allocations.allocated_user_id IS '分配给的用户ID（子账号）';
    COMMENT ON COLUMN package_allocations.source_order_id IS '源订单ID（主账号购买的订单）';
    COMMENT ON COLUMN package_allocations.allocated_order_id IS '分配订单ID（为子账号创建的虚拟订单）';
    COMMENT ON COLUMN package_allocations.package_id IS '套餐ID';
    COMMENT ON COLUMN package_allocations.status IS '分配状态：active/revoked';
    COMMENT ON COLUMN package_allocations.allocated_at IS '分配时间';
    COMMENT ON COLUMN package_allocations.revoked_at IS '回收时间';
    COMMENT ON COLUMN package_allocations.created_at IS '创建时间';
    COMMENT ON COLUMN package_allocations.updated_at IS '更新时间';

    -- 为 payment_orders 表添加 package_quantity 字段（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'payment_orders' AND column_name = 'package_quantity') THEN
        -- 先添加字段，允许NULL值
        ALTER TABLE payment_orders ADD COLUMN package_quantity INTEGER;

        -- 为所有已有记录设置默认值1
        UPDATE payment_orders SET package_quantity = 1 WHERE package_quantity IS NULL;

        -- 设置默认值和非空约束
        ALTER TABLE payment_orders ALTER COLUMN package_quantity SET DEFAULT 1;
        ALTER TABLE payment_orders ALTER COLUMN package_quantity SET NOT NULL;

        -- 更新注释
        COMMENT ON COLUMN payment_orders.package_quantity IS '套餐购买数量，默认为1，支持批量购买';
    END IF;

END $$;

-- ==================== 用户邀请码扩展 ====================

-- 为用户表添加邀请码字段（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'users' AND column_name = 'invitation_code') THEN
        ALTER TABLE users ADD COLUMN invitation_code VARCHAR(6) UNIQUE;
    END IF;
END $$;

-- 创建邀请码索引（如果不存在）
CREATE INDEX IF NOT EXISTS ix_users_invitation_code ON users(invitation_code);

-- 添加邀请码字段注释
COMMENT ON COLUMN users.invitation_code IS '用户邀请码，6位数字字母组合，用于邀请功能（可扩展为个人邀请或组织邀请）';

-- 创建邀请码生成函数
CREATE OR REPLACE FUNCTION generate_invitation_code() RETURNS VARCHAR(6) AS $$
DECLARE
    chars TEXT := 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; -- 去掉容易混淆的字符I、O、0、1
    result VARCHAR(6) := '';
    i INTEGER;
BEGIN
    FOR i IN 1..6 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 为现有用户生成邀请码的函数
CREATE OR REPLACE FUNCTION ensure_user_invitation_codes() RETURNS INTEGER AS $$
DECLARE
    user_record RECORD;
    new_code VARCHAR(6);
    updated_count INTEGER := 0;
    max_attempts INTEGER := 10;
    attempt INTEGER;
BEGIN
    -- 遍历所有没有邀请码的用户
    FOR user_record IN
        SELECT id FROM users WHERE invitation_code IS NULL
    LOOP
        attempt := 0;
        LOOP
            -- 生成新的邀请码
            new_code := generate_invitation_code();

            -- 尝试更新用户邀请码
            BEGIN
                UPDATE users
                SET invitation_code = new_code
                WHERE id = user_record.id AND invitation_code IS NULL;

                IF FOUND THEN
                    updated_count := updated_count + 1;
                    EXIT; -- 成功更新，退出循环
                END IF;

            EXCEPTION WHEN unique_violation THEN
                -- 邀请码重复，重新生成
                attempt := attempt + 1;
                IF attempt >= max_attempts THEN
                    RAISE EXCEPTION '为用户 % 生成唯一邀请码失败，已尝试 % 次', user_record.id, max_attempts;
                END IF;
            END;
        END LOOP;
    END LOOP;

    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- 执行为现有用户生成邀请码
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    SELECT ensure_user_invitation_codes() INTO updated_count;
    RAISE NOTICE '已为 % 个用户生成邀请码', updated_count;
END $$;

-- 创建个人邀请记录表
CREATE TABLE IF NOT EXISTS personal_invitations (
    id SERIAL PRIMARY KEY,
    inviter_user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    invitee_user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    invitation_code VARCHAR(6) NOT NULL,
    inviter_rewarded BOOLEAN DEFAULT FALSE NOT NULL,
    invitee_rewarded BOOLEAN DEFAULT FALSE NOT NULL,
    reward_package_id VARCHAR(50) DEFAULT 'trial_package' NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    inviter_rewarded_at TIMESTAMP,
    invitee_rewarded_at TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_personal_invitation_inviter ON personal_invitations(inviter_user_id);
CREATE INDEX IF NOT EXISTS idx_personal_invitation_invitee ON personal_invitations(invitee_user_id);
CREATE INDEX IF NOT EXISTS idx_personal_invitation_code ON personal_invitations(invitation_code);
CREATE INDEX IF NOT EXISTS idx_personal_invitation_created ON personal_invitations(created_at);

-- 添加注释
COMMENT ON TABLE personal_invitations IS '个人邀请记录表，记录用户之间的邀请关系和奖励状态';
COMMENT ON COLUMN personal_invitations.id IS '邀请记录ID，自增主键';
COMMENT ON COLUMN personal_invitations.inviter_user_id IS '邀请人用户ID';
COMMENT ON COLUMN personal_invitations.invitee_user_id IS '被邀请人用户ID';
COMMENT ON COLUMN personal_invitations.invitation_code IS '使用的邀请码';
COMMENT ON COLUMN personal_invitations.inviter_rewarded IS '邀请人是否已获得奖励';
COMMENT ON COLUMN personal_invitations.invitee_rewarded IS '被邀请人是否已获得奖励';
COMMENT ON COLUMN personal_invitations.reward_package_id IS '奖励套餐ID';
COMMENT ON COLUMN personal_invitations.created_at IS '邀请时间';
COMMENT ON COLUMN personal_invitations.inviter_rewarded_at IS '邀请人获得奖励时间';
COMMENT ON COLUMN personal_invitations.invitee_rewarded_at IS '被邀请人获得奖励时间';

-- ==================== CRM系统数据库表 ====================
-- 创建日期: 2025-08-13
-- 描述: CRM客户关系管理系统表，包含市场资源和有效客户管理

-- 创建CRM客户表
CREATE TABLE IF NOT EXISTS crm_clients (
    id BIGSERIAL PRIMARY KEY,
    organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

    -- 基础信息
    add_date TIMESTAMP NOT NULL,
    customer_name VARCHAR(100),
    wechat_name VARCHAR(100) NOT NULL,
    wechat_id VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    channel_source VARCHAR(50) NOT NULL,

    -- 业务信息
    study_project VARCHAR(100) NOT NULL,
    customer_background TEXT,
    market_supplement TEXT,

    -- 分配/有效性
    assignment_status VARCHAR(20) NOT NULL DEFAULT 'unassigned',
    is_valid BOOLEAN NOT NULL DEFAULT TRUE,

    -- 有效客户：意向等级 + 五维反馈
    intent_level VARCHAR(10),
    feedback_budget TEXT,
    feedback_motivation TEXT,
    feedback_urgency TEXT,
    feedback_comparison TEXT,
    feedback_parents TEXT,

    -- 当前就读与成绩
    current_school VARCHAR(200),
    current_major VARCHAR(200),
    current_grade VARCHAR(50),
    gpa_value VARCHAR(10),
    gpa_scale VARCHAR(10),

    -- 语言与入学/意向
    language_scores JSONB,
    intended_enrollment_date VARCHAR(32),
    intent_regions TEXT[] NOT NULL DEFAULT '{}',
    intent_schools TEXT[] NOT NULL DEFAULT '{}',
    intent_majors TEXT[] NOT NULL DEFAULT '{}',

    -- 生命周期与转签引用
    lifecycle_status VARCHAR(20) NOT NULL DEFAULT 'lead',
    converted_client_id INTEGER REFERENCES clients(id) ON DELETE SET NULL,

    -- 审计字段
    created_by_user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    updated_by_user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    CONSTRAINT chk_assignment_status CHECK (assignment_status IN ('unassigned', 'assigned')),
    CONSTRAINT chk_intent_level CHECK (intent_level IN ('high', 'medium', 'low')),
    CONSTRAINT chk_lifecycle_status CHECK (lifecycle_status IN ('lead', 'valid', 'signed', 'archived'))
);

-- 为 crm_clients 表添加触发器（仅在不存在时创建）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_crm_clients_updated_at') THEN
        CREATE TRIGGER update_crm_clients_updated_at
        BEFORE UPDATE ON crm_clients
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_crm_clients_id ON crm_clients(id);
CREATE INDEX IF NOT EXISTS idx_crm_clients_organization_id ON crm_clients(organization_id);
CREATE INDEX IF NOT EXISTS idx_crm_clients_customer_name ON crm_clients(customer_name);
CREATE INDEX IF NOT EXISTS idx_crm_clients_wechat_name ON crm_clients(wechat_name);
CREATE INDEX IF NOT EXISTS idx_crm_clients_wechat_id ON crm_clients(wechat_id);
CREATE INDEX IF NOT EXISTS idx_crm_clients_phone ON crm_clients(phone);
CREATE INDEX IF NOT EXISTS idx_crm_clients_assignment_status ON crm_clients(assignment_status);
CREATE INDEX IF NOT EXISTS idx_crm_clients_is_valid ON crm_clients(is_valid);
CREATE INDEX IF NOT EXISTS idx_crm_clients_intent_level ON crm_clients(intent_level);
CREATE INDEX IF NOT EXISTS idx_crm_clients_lifecycle_status ON crm_clients(lifecycle_status);
CREATE INDEX IF NOT EXISTS idx_crm_clients_converted_client_id ON crm_clients(converted_client_id);
CREATE INDEX IF NOT EXISTS idx_crm_clients_created_by_user_id ON crm_clients(created_by_user_id);
CREATE INDEX IF NOT EXISTS idx_crm_clients_updated_by_user_id ON crm_clients(updated_by_user_id);
CREATE INDEX IF NOT EXISTS idx_crm_clients_created_at ON crm_clients(created_at);
CREATE INDEX IF NOT EXISTS idx_crm_clients_updated_at ON crm_clients(updated_at);
CREATE INDEX IF NOT EXISTS idx_crm_clients_org_valid ON crm_clients(organization_id, is_valid);
CREATE INDEX IF NOT EXISTS idx_crm_clients_org_status ON crm_clients(organization_id, assignment_status);

-- 添加注释
COMMENT ON TABLE crm_clients IS 'CRM客户表，包含市场资源和有效客户的完整生命周期管理';
COMMENT ON COLUMN crm_clients.id IS '客户ID，自增主键';
COMMENT ON COLUMN crm_clients.organization_id IS '所属组织ID';
COMMENT ON COLUMN crm_clients.add_date IS '添加日期';
COMMENT ON COLUMN crm_clients.customer_name IS '客户姓名（可空）';
COMMENT ON COLUMN crm_clients.wechat_name IS '微信名称（必填）';
COMMENT ON COLUMN crm_clients.wechat_id IS '微信账号（必填）';
COMMENT ON COLUMN crm_clients.phone IS '电话号码';
COMMENT ON COLUMN crm_clients.channel_source IS '渠道来源（必填）';
COMMENT ON COLUMN crm_clients.study_project IS '咨询项目（必填）';
COMMENT ON COLUMN crm_clients.customer_background IS '客户背景';
COMMENT ON COLUMN crm_clients.market_supplement IS '市场补充';
COMMENT ON COLUMN crm_clients.assignment_status IS '分配状态：unassigned(未分配)、assigned(已分配)';
COMMENT ON COLUMN crm_clients.is_valid IS '是否有效客户';
COMMENT ON COLUMN crm_clients.intent_level IS '意向等级：high(高意向)、medium(中意向)、low(低意向)';
COMMENT ON COLUMN crm_clients.feedback_budget IS '预算反馈';
COMMENT ON COLUMN crm_clients.feedback_motivation IS '动机反馈';
COMMENT ON COLUMN crm_clients.feedback_urgency IS '紧迫性反馈';
COMMENT ON COLUMN crm_clients.feedback_comparison IS '对比反馈';
COMMENT ON COLUMN crm_clients.feedback_parents IS '父母/决策人反馈';
COMMENT ON COLUMN crm_clients.current_school IS '当前院校';
COMMENT ON COLUMN crm_clients.current_major IS '当前专业';
COMMENT ON COLUMN crm_clients.current_grade IS '当前年级';
COMMENT ON COLUMN crm_clients.gpa_value IS 'GPA数值';
COMMENT ON COLUMN crm_clients.gpa_scale IS 'GPA满分';
COMMENT ON COLUMN crm_clients.language_scores IS '语言成绩JSON格式存储';
COMMENT ON COLUMN crm_clients.intended_enrollment_date IS '预期入学时间';
COMMENT ON COLUMN crm_clients.intent_regions IS '意向地区数组';
COMMENT ON COLUMN crm_clients.intent_schools IS '意向院校数组';
COMMENT ON COLUMN crm_clients.intent_majors IS '意向专业数组';
COMMENT ON COLUMN crm_clients.lifecycle_status IS '生命周期状态：lead(潜在客户)、valid(有效客户)、signed(签约客户)、archived(已归档)';
COMMENT ON COLUMN crm_clients.converted_client_id IS '转签后的正式客户ID';
COMMENT ON COLUMN crm_clients.created_by_user_id IS '创建者用户ID';
COMMENT ON COLUMN crm_clients.updated_by_user_id IS '更新者用户ID';
COMMENT ON COLUMN crm_clients.created_at IS '创建时间';
COMMENT ON COLUMN crm_clients.updated_at IS '更新时间';

-- 创建CRM客户人员分配表
CREATE TABLE IF NOT EXISTS crm_client_staff (
    id BIGSERIAL PRIMARY KEY,
    crm_client_id BIGINT NOT NULL REFERENCES crm_clients(id) ON DELETE CASCADE,
    organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    CONSTRAINT chk_staff_role CHECK (role IN ('market', 'sales', 'document_writer', 'submission'))
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_crm_client_staff_id ON crm_client_staff(id);
CREATE INDEX IF NOT EXISTS idx_crm_client_staff_crm_client_id ON crm_client_staff(crm_client_id);
CREATE INDEX IF NOT EXISTS idx_crm_client_staff_organization_id ON crm_client_staff(organization_id);
CREATE INDEX IF NOT EXISTS idx_crm_client_staff_user_id ON crm_client_staff(user_id);
CREATE INDEX IF NOT EXISTS idx_crm_client_staff_role ON crm_client_staff(role);
CREATE INDEX IF NOT EXISTS idx_crm_client_staff_client_user ON crm_client_staff(crm_client_id, user_id);
CREATE INDEX IF NOT EXISTS idx_crm_client_staff_org_user ON crm_client_staff(organization_id, user_id);

-- 添加注释
COMMENT ON TABLE crm_client_staff IS 'CRM客户人员分配表，记录客户与工作人员的分配关系';
COMMENT ON COLUMN crm_client_staff.id IS '分配记录ID，自增主键';
COMMENT ON COLUMN crm_client_staff.crm_client_id IS '关联的CRM客户ID';
COMMENT ON COLUMN crm_client_staff.organization_id IS '组织ID';
COMMENT ON COLUMN crm_client_staff.user_id IS '用户ID';
COMMENT ON COLUMN crm_client_staff.role IS '角色：market(市场)、sales(销售)、document_writer(文案)、submission(递交)';
COMMENT ON COLUMN crm_client_staff.created_at IS '创建时间';

-- 输出完成信息
SELECT 'Database tables created successfully! Organization management, multi-tenant credit system, personal invitation and CRM tables added.' AS status;
