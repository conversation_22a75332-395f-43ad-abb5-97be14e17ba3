version: '3.8'

services:
  # 前端服务 - Vue 3 + Vite 开发服务器
  frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile.dev
    container_name: tunshuedu-frontend-dev
    volumes:
      # 挂载源代码目录，支持热重载
      - ./frontend:/app
      # 排除node_modules，使用容器内的版本
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NODE_OPTIONS=--openssl-legacy-provider
      - VITE_API_URL=
      - VITE_BACKEND_URL=http://backend:8000
    restart: unless-stopped
    networks:
      - tunshuedu-network

  # 后端服务 - FastAPI + Uvicorn 开发服务器
  backend:
    build:
      context: .
      dockerfile: docker/backend/Dockerfile.dev
    container_name: tunshuedu-backend-dev
    volumes:
      # 挂载源代码目录，支持热重载
      - ./backend:/app
    ports:
      - "8000:8000"
    environment:
      # 数据库配置 - 阿里云PostgreSQL
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      
      # 应用配置
      - ENVIRONMENT=development
      - SECRET_KEY=${SECRET_KEY}
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - REFRESH_TOKEN_EXPIRE_DAYS=7
      
      # AI API配置
      - SILICONE_FLOW_API_KEY=${SILICONE_FLOW_API_KEY}
      - ALIBABACLOUD_API_KEY_ai_selection=${ALIBABACLOUD_API_KEY_ai_selection}
      - ALIBABACLOUD_API_KEY_bg_extraction=${ALIBABACLOUD_API_KEY_bg_extraction}
      - ALIBABACLOUD_API_KEY_ai_augmentation=${ALIBABACLOUD_API_KEY_ai_augmentation}
      - ALIBABACLOUD_API_KEY_ai_writing=${ALIBABACLOUD_API_KEY_ai_writing}
      - DOUBAO_API_KEY=${DOUBAO_API_KEY}
      - ZEROGPT_API_KEY=${ZEROGPT_API_KEY}
      
      # 微信登录配置
      - WECHAT_APP_ID=${WECHAT_APP_ID}
      - WECHAT_APP_SECRET=${WECHAT_APP_SECRET}
      - WECHAT_REDIRECT_URI=${WECHAT_REDIRECT_URI}
      
      # 前端URL
      - FRONTEND_URL=http://localhost
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://redis:6379/0
    restart: unless-stopped
    networks:
      - tunshuedu-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - frontend
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: tunshuedu-nginx-dev
    ports:
      - "80:80"
    volumes:
      - ./docker/nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    networks:
      - tunshuedu-network
    depends_on:
      - frontend
      - backend


  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: tunshuedu-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - tunshuedu-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3

# 网络配置
networks:
  tunshuedu-network:
    driver: bridge
    name: tunshuedu-dev-network

# 数据卷
volumes:
  node_modules:
    name: tunshuedu-node-modules-dev
  redis_data:
    name: tunshuedu-redis-data-dev 