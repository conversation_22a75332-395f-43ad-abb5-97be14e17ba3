# Git相关
.git
.gitignore
.gitattributes

# 文档
README.md
*.md

# 环境文件
.env
.env.*
!.env.example

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
.cursor/

# 操作系统
.DS_Store
Thumbs.db

# 前端相关
frontend/node_modules/
frontend/dist/
frontend/.nuxt/
frontend/.output/
frontend/.vite/

# 后端相关
backend/venv/
backend/.venv/
backend/__pycache__/
backend/*.pyc
backend/*.pyo
backend/*.pyd
backend/.Python
backend/pip-log.txt
backend/pip-delete-this-directory.txt
backend/.coverage
backend/.pytest_cache/

# 数据文件
*.db
*.sqlite
*.sqlite3
dump.rdb

# 日志文件
*.log
logs/

# 临时文件
tmp/
temp/
*.tmp

# 密钥和证书
keys/
*.key
*.pem
*.crt

# 测试文件
test_*.py
*_test.py

# 开发工具
.augment/
structure.md
.cursorrules
.cursorignore
.prettierrc
tsconfig*.json
backend/migrations/__pycache__/

# Docker相关（避免递归复制）
docker/
Dockerfile*
docker-compose*.yml 