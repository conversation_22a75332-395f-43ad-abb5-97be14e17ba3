# =================================
# 囤鼠教育 Docker 环境配置示例
# =================================
# 复制此文件为 .env 并填写实际配置值

# =================================
# 数据库配置 - 阿里云 PostgreSQL 17.4
# =================================
POSTGRES_HOST=your-alicloud-postgres-host.com
POSTGRES_PORT=5432
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=tunshuedu_db

# =================================
# 应用安全配置
# =================================
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =================================
# AI API 配置
# =================================
SILICONE_FLOW_API_KEY=your-silicone-flow-api-key
ALIBABACLOUD_API_KEY_ai_selection=your-alibaba-ai-selection-key
ALIBABACLOUD_API_KEY_bg_extraction=your-alibaba-bg-extraction-key
ALIBABACLOUD_API_KEY_ai_augmentation=your-alibaba-ai-augmentation-key
ALIBABACLOUD_API_KEY_ai_writing=your-alibaba-ai-writing-key

# =================================
# AI检测API配置
# =================================
ZEROGPT_API_KEY=your-zerogpt-api-key

# =================================
# 微信登录配置
# =================================
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret
WECHAT_REDIRECT_URI=https://tunshuedu.com/auth/wechat/callback

# =================================
# 前端配置
# =================================
# 开发环境：留空使用nginx代理
# 生产环境：使用实际域名
VITE_API_URL=

# =================================
# 部署配置（生产环境）
# =================================
DOMAIN_NAME=tunshuedu.com
SSL_CERT_PATH=./ssl/certificate.crt
SSL_KEY_PATH=./ssl/private.key 